import{n as I}from"./index-f7d9b065.js";const i=()=>{const o=I(t=>t.payload.unselectedRows||[]);return{createFCRows:t=>t==null?void 0:t.flatMap(e=>e.Toplantdata.map(n=>({...n,FinanceCostingId:e.FinanceCostingId,MassSchedulingId:e.MassSchedulingId,RequestType:e.RequestType,RequestId:e.RequestId,Requester:e.Requester,CreatedOn:e.CreatedOn,Material:e.Material,MatlType:e.MatlType,IntlPoPrice:e.IntlPoPrice,PryVendor:e.<PERSON>,FlagForBOM:e.FlagForBOM,VolInEA:e.VolInEA,VolInCA:e.VolInCA,VolInCAR:e.VolInCAR,NoOfUnitForCA:e.NoOfUnitForCA,NoOfUnitForCT:e.NoOfUnitForCT,Torequestheaderdata:e.<PERSON>,Tomaterialerrordata:e.<PERSON>,id:n==null?void 0:n.FinancePlantId}))),createFCPayload:()=>{const t=o,a=new Map;return t.forEach(n=>{const{FinancePlantId:s,Material:d,IsDeleted:P,Plant:c,FPurStatus:l,FStdPrice:u,id:T,...F}=n,r={FinancePlantId:s,Material:d,IsDeleted:!0,Plant:c,FPurStatus:l,FStdPrice:u};a.has(n.FinanceCostingId)?a.get(n.FinanceCostingId).Toplantdata.push(r):a.set(n.FinanceCostingId,{...F,Material:n==null?void 0:n.Material,Toplantdata:[r]})}),Array.from(a.values())}}};export{i as u};
