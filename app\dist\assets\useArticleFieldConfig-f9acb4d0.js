import{s as ae,aP as re,n as P,r as B,u as ne,cZ as le,bf as De,bI as C,aT as g,C as v,aJ as w,dB as Q,dC as Y,dD as $,dE as j,dF as k,dG as H,dH as V,dI as K,dJ as x,dV as J,dL as X,dc as Z,dd as Ee,aD as oe}from"./index-f7d9b065.js";const z=_=>{let N={},s=_==null?void 0:_.sort((n,i)=>n.MDG_MAT_VIEW_SEQUENCE-i.MDG_MAT_VIEW_SEQUENCE);const L=Z(s,"MDG_MAT_VIEW_NAME");let F=[];Object.entries(L).forEach(([n,i])=>{let M=Z(i,"MDG_MAT_CARD_NAME"),l=[];Object.entries(M).forEach(([G,c])=>{c.sort((r,m)=>r.MDG_MAT_SEQUENCE_NO-m.MDG_MAT_SEQUENCE_NO);let b=c.map(r=>({fieldName:r.MDG_MAT_UI_FIELD_NAME,sequenceNo:r.MDG_MAT_SEQUENCE_NO,fieldType:r.MDG_MAT_FIELD_TYPE,maxLength:r.MDG_MAT_MAX_LENGTH,dataType:r.MDG_MAT_DATA_TYPE,viewName:r.MDG_MAT_VIEW_NAME,cardName:r.MDG_MAT_CARD_NAME,cardSeq:r.MDG_MAT_CARD_SEQUENCE,value:r.MDG_MAT_DEFAULT_VALUE,visibility:r.MDG_MAT_VISIBILITY,jsonName:r.MDG_MAT_JSON_FIELD_NAME,fieldPriority:r.MDG_MAT_MATERIAL_FLD_PRT,fieldTooltip:r==null?void 0:r.MDG_MAT_TOOLTIP_MESSAGE}));l.push({cardName:G,cardSeq:c[0].MDG_MAT_CARD_SEQUENCE,cardDetails:b})}),l.sort((G,c)=>G.cardSeq-c.cardSeq),F.push({viewName:n,cards:l})});let p=Ee(F),y={};return p.forEach(n=>{let i={};n.cards.forEach(M=>{i[M.cardName]=M.cardDetails,n.viewName!=="Request Header"&&M.cardDetails.forEach(l=>{l.visibility===oe.MANDATORY&&(N[l.viewName]||(N[l.viewName]=[]),N[l.viewName].push({jsonName:l==null?void 0:l.jsonName,fieldName:l==null?void 0:l.fieldName}))})}),y[n.viewName]=i}),{transformedData:y,mandatoryFields:N}},Te=()=>{const _=ae(),{customError:N}=re(),s=P(t=>{var E;return(E=t.payload)==null?void 0:E.payloadData}),L=P(t=>t.applicationConfig),{userData:F,taskData:p}=P(t=>t.userManagement),[y,n]=B.useState(!0),[i,M]=B.useState(null);ne();const l=window.location.href.includes("DisplayMaterialSAPView"),G=le(De.CURRENT_TASK);let c=null;c=typeof G=="string"?JSON.parse(G):G;let b=c==null?void 0:c.ATTRIBUTE_5;const r=async(t,E)=>{if(s!=null&&s.RequestType||l){const T={decisionTableId:null,decisionTableName:"MDG_ARTICLE_FIELD_CONFIG",version:"v3",rulePolicy:null,validityDate:null,conditions:[{"MDG_CONDITIONS.MDG_MAT_SCENARIO":(s==null?void 0:s.RequestType)||"Display","MDG_CONDITIONS.MDG_MAT_MATERIAL_TYPE":l?t.split("-")[0]:t||"VERP","MDG_CONDITIONS.MDG_MAT_REGION":(s==null?void 0:s.Region)||"US","MDG_CONDITIONS.MDG_MAT_GROUP_ROLE":l?"Z_MAT_REQ_DISPLAY":p.ATTRIBUTE_5?p.ATTRIBUTE_5:b||"Merchandising Admin","MDG_CONDITIONS.MDG_MAT_TASK_NAME":p.taskDesc??"Initiate"}],systemFilters:null,systemOrders:null,filterString:null},I=e=>{var O,f,S,U;if(e.statusCode===w.STATUS_200){if(Array.isArray((O=e==null?void 0:e.data)==null?void 0:O.result)&&((f=e==null?void 0:e.data)!=null&&f.result.every(R=>Object.keys(R).length!==0))){let R=(U=(S=e==null?void 0:e.data)==null?void 0:S.result[0])==null?void 0:U.MDG_MAT_MATERIAL_FIELD_CONFIG;const{transformedData:o,mandatoryFields:u}=z(R),W=Object.keys(o).map(a=>(a==="Basic Data"?_(Q(o["Basic Data"])):a==="Sales"?_(Y(o.Sales)):a==="Purchasing"?_($(o.Purchasing)):a==="MRP"?_(j(o.MRP)):a==="Accounting"&&_(k(o.Accounting)),a!=="Request Header"&&_(H({tab:a,data:o[a]})),{[a]:o[a]}));_(V({[(s==null?void 0:s.Region)||E||"US"]:{[t]:{allfields:K(W),mandatoryFields:u}}}));let h=[...new Set(R.filter(a=>a.MDG_MAT_VISIBILITY!=="Hidden").sort((a,D)=>a.MDG_MAT_VIEW_SEQUENCE-D.MDG_MAT_VIEW_SEQUENCE).map(a=>a.MDG_MAT_VIEW_NAME))];h=[...h.filter(a=>!x.includes(a))],_(J({matType:t,views:h}))}else _(V({[(s==null?void 0:s.Region)||E||"US"]:{[t]:{}}}));n(!1)}},A=e=>{N(e),M(e),n(!1)},d=L.environment==="localhost"?`/${C}${g.INVOKE_RULES.LOCAL}`:`/${C}${g.INVOKE_RULES.PROD}`;v(d,"post",I,A,T)}},m=(t,E,T)=>{n(!0);try{r(t,E,T)}catch(I){M(I),n(!1)}},ee=async(t,E)=>{if(s!=null&&s.RequestType){const T={decisionTableId:null,decisionTableName:"MDG_MAT_EXTEND_TEMPLATE_DT",version:"v2",rulePolicy:null,validityDate:null,conditions:[{"MDG_CONDITIONS.MDG_MAT_SCENARIO":"Extend","MDG_CONDITIONS.MDG_MAT_REGION":"US","MDG_CONDITIONS.MDG_MAT_GROUP_ROLE":"CA-MDG-MD-TEAM-US"}],systemFilters:null,systemOrders:null,filterString:null},I=e=>{var O,f,S,U,R;if(e.statusCode===w.STATUS_200){if(Array.isArray((O=e==null?void 0:e.data)==null?void 0:O.result)&&((f=e==null?void 0:e.data)!=null&&f.result.every(o=>Object.keys(o).length!==0))){let o=(U=(S=e==null?void 0:e.data)==null?void 0:S.result[0])==null?void 0:U.MDG_MAT_EXTEND_TEMPLATE_DT;const{transformedData:u,mandatoryFields:q}=z(o),h=Object.keys(u).map(D=>(D==="Basic Data"?_(Q(u["Basic Data"])):D==="Sales"?_(Y(u.Sales)):D==="Purchasing"?_($(u.Purchasing)):D==="MRP"?_(j(u.MRP)):D==="Accounting"&&_(k(u.Accounting)),D!=="Request Header"&&_(H({tab:D,data:u[D]})),{[D]:u[D]}));_(V({[(s==null?void 0:s.Region)||E||"US"]:{[t]:{allfields:K(h),mandatoryFields:q}}}));let a=[...new Set((R=o==null?void 0:o.sort((D,te)=>D.MDG_MAT_VIEW_SEQUENCE-te.MDG_MAT_VIEW_SEQUENCE))==null?void 0:R.map(D=>D.MDG_MAT_VIEW_NAME))];a=a.filter(D=>!x.includes(D)),_(J({matType:t,views:a}))}else _(V({[(s==null?void 0:s.Region)||E||"US"]:{[t]:{}}}));n(!1)}},A=e=>{N(e),M(e),n(!1)},d=L.environment==="localhost"?`/${C}${g.INVOKE_RULES.LOCAL}`:`/${C}${g.INVOKE_RULES.PROD}`;v(d,"post",I,A,T)}},se=(t,E)=>{n(!0);try{ee(t,E)}catch(T){M(T),n(!1)}},_e=t=>{let E={decisionTableId:null,decisionTableName:"MDG_MAT_REG_SORG_PLNT_CNTRY_STORE_WH_MAPPING",version:"v1",rulePolicy:null,validityDate:null,conditions:[{"MDG_CONDITIONS.MDG_MAT_REGION":"US"}],systemFilters:null,systemOrders:null,filterString:null};const T=A=>{var d,e;if(A.statusCode===w.STATUS_200){let O=(e=(d=A==null?void 0:A.data)==null?void 0:d.result[0])==null?void 0:e.MDG_MAT_REG_SORG_PLNT_CNTRY_STORE_WH_MAPPING;const f=Array.from(new Map(O.map(S=>[S.MDG_MAT_SALES_ORG,{code:S.MDG_MAT_SALES_ORG,desc:S.MDG_MAT_SALES_ORG_DESC}])).values());_(X({keyName:"uniqueSalesOrgList",data:f})),_(X({keyName:"salesOrgData",data:O}))}},I=A=>{N(A)};L.environment==="localhost"?v(`/${C}${g.INVOKE_RULES.LOCAL}`,"post",T,I,E):v(`/${C}${g.INVOKE_RULES.PROD}`,"post",T,I,E)};return{loading:y,error:i,fetchMaterialFieldConfig:m,fetchMaterialFieldConfigExtend:se,fetchOrgData:t=>{n(!0);try{_e(t)}catch(E){M(E),n(!1)}}}};export{Te as u};
