import{cs as T,dy as ce,d as i,aa as de,a4 as se,B as C,an as b,g as le,r as d,n as ue,aP as me,c as r,j as e,O as s,a8 as M,aZ as w,$ as Y,aE as F,a6 as pe,a$ as he,ah as Ie,ag as Te,aF as ge,aG as Ee,C as _e,dm as B,cq as v,dz as fe,aT as Ne}from"./index-f7d9b065.js";import{a as Se,d as we}from"./SlideshowOutlined-37fd5794.js";import{d as Re}from"./CloudUpload-0ba6431e.js";import{S as Ve}from"./SingleSelectDropdown-aee403d4.js";import{D as J}from"./DatePicker-78c32993.js";import"./_baseDelay-04dfb554.js";const{VITE_CLIENT_ID:De}={VITE_ENV:"dev",VITE_CLIENT_ID:"************-45noron6i0dj5j5l5ljin32fa635ts1m.apps.googleusercontent.com",VITE_DESTINATION_ADMIN:"cw-mdg-admin-dest",VITE_DESTINATION_SERVICE_REQUEST:"cw-scp-serv-req-oauth2-dev",VITE_DESTINATION_PO:"cw-scp-purch-order-oauth2-dev",VITE_DESTINATION_INVOICE:"cw-scp-invoice-oauth2-dev",VITE_DESTINATION_DOCUMENT_MANAGEMENT:"cw-mdg-documentmanagement-dest",VITE_DESTINATION_RETURNS:"cw-scp-returns-oauth2-dev",VITE_DESTINATION_MANAGE_ACCOUNT:"cw-scp-manage-acct-oauth2-dev",VITE_DESTINATION_NOTIFICATION:"cw-scp-notification-oauth2-dev",VITE_DESTINATION_BOM:"cw-mdg-billofmaterial-dest",VITE_DESTINATION_PR:"cw-scp-pr-oauth2-dev",VITE_DESTINATION_IWA:"cw-mdg-iwm-dev",VITE_DESTINATION_IWA_NPI:"cw-mdg-iwa-oauth2-dest",VITE_DESTINATION_SERVICE_ENTRY_SHEET:"cw-scp-ses-oauth2-dev",VITE_DESTINATION_PLANNING_MANAGEMENT:"cw-scp-pfm-oauth2-dev",VITE_DESTINATION_MATERIAL_MGMT:"cw-mdg-materialmanagement-dest",VITE_DESTINATION_ARTICLE_MGMT:"cw-mdg-articlemanagement-dest",VITE_DESTINATION_AI:"cw-mdg-artificialintelligence-dest",VITE_DESTINATION_WEBSOCKET:"cw-mdg-notification-dest",VITE_DESTINATION_COST_CENTER:"cw-mdg-costcenter-dest",VITE_DESTINATION_PROFIT_CENTER:"cw-mdg-profitcenter-dest",VITE_DESTINATION_BANK_KEY:"cw-mdg-bankkey-dest",VITE_DESTINATION_GENERAL_LEDGER:"cw-mdg-generalledger-dest",VITE_DESTINATION_DASHBOARD:"cw-mdg-dashboard-dest",VITE_DESTINATION_SLA_MGMT:"cw-mdg-slamanagement-dest",VITE_DESTINATION_IDM:"cw-caf-idm-services",VITE_DESTINATION_ITM_JAVA_SERVICES:"ITMJavaServices",VITE_DESTINATION_IWA_NEW:"IWAApi",VITE_CW_MDG_COSTCENTER_MASS_DEST:"cw-mdg-costcenter-dest",VITE_CW_MDG_GENERALLEDGER_MASS_DEST:"cw-mdg-generalledger-dest",VITE_CW_MDG_PROFITCENTER_MASS_DEST:"cw-mdg-profitcenter-dest",VITE_DESTINATION_INTERNAL_ORDER:"cw-mdg-internalorder-dest",VITE_URL_ITM_JAVA_SERVICES:"https://cw-mdg-iwm-dev.cfapps.eu10-004.hana.ondemand.com",VITE_BASE_URL_ITM_JAVA_SERVICES:"https://cherryworkproducts-itm-java-dev.cfapps.eu10-004.hana.ondemand.com",VITE_BASE_URL_MESSAGING_SERVICES:"https://messaging.cherryworkproducts.com",VITE_BASE_URL_CRUD_SERVICES:"https://crudservicesdev.cherryworkproducts.com",VITE_BASE_URL_IWASCP_SERVICES:"https://cw-scp-authentication.cfapps.eu10-004.hana.ondemand.com",VITE_URL_MATERIAL_MGMT:"https://incture-cherrywork-dev-cw-mdg-dev-cw-mdg-materialmanagement.cfapps.eu10-004.hana.ondemand.com",VITE_URL_ARTICLE_MGMT:"https://incture-cherrywork-dev-cw-mdg-dev-cw-mdg-articlemanagement.cfapps.eu10-004.hana.ondemand.com",VITE_URL_AI:"https://incture-cherrywork-dev-cw-mdg-dev-cw-mdg-artificialintelligence.cfapps.eu10-004.hana.ondemand.com",VITE_URL_DOCUMENT_MANAGEMENT:"https://incture-cherrywork-dev-cw-mdg-dev-cw-mdg-documentmanagement.cfapps.eu10-004.hana.ondemand.com",VITE_URL_WEBSOCKET:"https://incture-cherrywork-dev-cw-mdg-dev-cw-mdg-notification.cfapps.eu10-004.hana.ondemand.com",VITE_URL_COST_CENTER:"https://incture-cherrywork-dev-cw-mdg-dev-cw-mdg-costcenter.cfapps.eu10-004.hana.ondemand.com",VITE_URL_PROFIT_CENTER:"https://incture-cherrywork-dev-cw-mdg-dev-cw-mdg-profitcenter.cfapps.eu10-004.hana.ondemand.com",VITE_URL_BANK_KEY:"https://incture-cherrywork-dev-cw-mdg-dev-cw-mdg-bankkey.cfapps.eu10-004.hana.ondemand.com",VITE_URL_GENERAL_LEDGER:"https://incture-cherrywork-dev-cw-mdg-dev-cw-mdg-generalledger.cfapps.eu10-004.hana.ondemand.com",VITE_URL_ADMIN:"https://incture-cherrywork-dev-cw-mdg-dev-cw-mdg-admin.cfapps.eu10-004.hana.ondemand.com",VITE_URL_IWA_NPI:"https://cw-mdg-authentication-dev.cfapps.eu10-004.hana.ondemand.com",VITE_URL_DASHBOARD:"https://incture-cherrywork-dev-cw-mdg-dev-cw-mdg-dashboard.cfapps.eu10-004.hana.ondemand.com",VITE_URL_SLA_MGMT:"https://cw-mdg-slamanagement-dev.cfapps.eu10-004.hana.ondemand.com",VITE_URL_IDM:"https://cw-caf-idm-services.cfapps.eu10-004.hana.ondemand.com",VITE_URL_AUTH_TOKEN:"https://cw-mdg-materialmanagement-dev.cfapps.eu10-004.hana.ondemand.com/authenticate/token",VITE_URL_AUTH_TOKEN_CAF:"https://incture-cherrywork-dev-cw-mdg-dev-cw-mdg-materialmanagement.cfapps.eu10-004.hana.ondemand.com/authenticate/tokenCaf",VITE_URL_PROFIT_CENTER_MASS_CAF:"https://incture-cherrywork-dev-cw-mdg-dev-cw-mdg-profitcenter.cfapps.eu10-004.hana.ondemand.com",VITE_URL_COST_CENTER_MASS_CAF:"https://incture-cherrywork-dev-cw-mdg-dev-cw-mdg-costcenter.cfapps.eu10-004.hana.ondemand.com",VITE_URL_GENERAL_LEDGER_MASS_CAF:"https://incture-cherrywork-dev-cw-mdg-dev-cw-mdg-generalledger.cfapps.eu10-004.hana.ondemand.com",VITE_URL_INTERNAL_ORDER:"https://incture-cherrywork-dev-cw-mdg-dev-cw-mdg-internalorder.cfapps.eu10-004.hana.ondemand.com",VITE_URL_BOM:"https://incture-cherrywork-dev-cw-mdg-dev-cw-mdg-billofmaterial.cfapps.eu10-004.hana.ondemand.com",VITE_URL_IWA_NEW:"https://incture-cherrywork-dev-cw-caf-dev-cw-caf-iwa-services.cfapps.eu10-004.hana.ondemand.com",VITE_APP_TOKEN:"********************************************************************************************************************************************************************************************************************************************************************************.***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.JrvD-cBxLEbrxDkFZeGTVNu84j_qVu6DnlzP2E6Ch-WMSQJfdx4qBzVks4xZR2yBILpenhMUsU_Q8KjyNW8awvvf-pFTtINl2QuHmrTyTAHq0pZBFqHZJ2ACLqnDYbL9ozPmiu4799ANcLfkozycal8IX9pKLhdM0FS5cNK_kLFEtBlPERkd3Q_tZfPtdumL2L8Muf9sUggdl5yJBBBS4ZwFkS08exgq_RBdlA9LML2ug-tR9rW_pHdUtk4M1K1kCtGt2zlIxXQjO-hEf_VxIH3jbf9tzUCULn2B4VIS12vYFpN_dRrNEiYljj8uvHDvHaNoa3w4puKU_NkTQuBJPw",BASE_URL:"/",MODE:"production",DEV:!1,PROD:!0,SSR:!1},ye=De,Me="https://www.googleapis.com/auth/youtube.upload",ve=T(ce)(({theme:o})=>({padding:o.spacing(3),marginBottom:o.spacing(2),borderRadius:o.spacing(2),boxShadow:"0 2px 12px rgba(0,0,0,0.08)"})),Ae=T(i)(({theme:o})=>({fontSize:"18px",fontWeight:600,color:o.palette.text.primary,marginBottom:o.spacing(2),borderBottom:`2px solid ${o.palette.primary.main}`,paddingBottom:o.spacing(1)})),A=T(de)(({theme:o})=>({"& .MuiOutlinedInput-root":{borderRadius:o.spacing(1)}})),Ce=T(se)(({theme:o})=>({borderRadius:o.spacing(1),backgroundColor:o.palette.background.paper})),be=T(C)(({theme:o})=>({border:`2px dashed ${o.palette.divider}`,borderRadius:o.spacing(2),padding:o.spacing(3),textAlign:"center",cursor:"pointer",transition:"all 0.3s ease","&:hover":{borderColor:o.palette.primary.main,backgroundColor:o.palette.action.hover}}));T(b)(({theme:o})=>({borderRadius:o.spacing(3),padding:o.spacing(1,3),textTransform:"none",fontWeight:600}));const Je=()=>{const o=le(),R=new Date,O=new Date;O.setDate(R.getDate()+7);const[a,k]=d.useState({title:"",description:"",category:"",startDate:R,endDate:O,module:"",files:null,link:""}),[G,_]=d.useState(!1),[Z,L]=d.useState(!1),[X,u]=d.useState(!1),[f,U]=d.useState(!1),[Oe,g]=d.useState(!1),[E,W]=d.useState([]),[V,D]=d.useState(!1),[j,N]=d.useState(!1),[P,ke]=d.useState(""),y=ue(t=>t.userManagement.userData),[z,Q]=d.useState(null),{customError:S,warn:Le}=me();d.useEffect(()=>{(()=>{if(!window.google){const n=document.createElement("script");n.src="https://accounts.google.com/gsi/client",n.async=!0,n.defer=!0,document.head.appendChild(n)}})()},[]);const H=async()=>new Promise((t,n)=>{window.google?window.google.accounts.oauth2.initTokenClient({client_id:ye,scope:Me,callback:c=>{c.error?(S("Authentication error:",c.error),n(c.error)):(Q(c.access_token),t(c.access_token))}}).requestAccessToken():n("Google API not loaded")}),$=async()=>{try{return z||await H()}catch(t){throw S("Failed to get authentication token:",t),t}},m=(t,n)=>{k(c=>({...c,[t]:n})),E.includes(t)&&W(c=>c.filter(l=>l!==t))},K=t=>{k(n=>({...n,files:t.target.files}))},q=()=>{const t=[];return["category","module","title","description"].forEach(c=>{(!a[c]||a[c].trim()==="")&&t.push(c)}),a.startDate>=a.endDate&&t.push("dateRange"),W(t),t.length===0},ee=async(t=!1)=>{const n=new FormData;a.files&&[...a.files].forEach(l=>n.append("files",l));const c={broadcastCategory:a.category,broadcastTitle:a.title,startDate:v(a.startDate).format("YYYY-MM-DD HH:mm:ss.000"),endDate:v(a.endDate).format("YYYY-MM-DD HH:mm:ss.000"),description:a.description,module:a.module,createdBy:(y==null?void 0:y.displayName)||"",createdDate:v(R).format("YYYY-MM-DD HH:mm:ss.000"),externalUrl:a.link,...t&&{status:"Draft"}};return n.append("broadcastDetails",JSON.stringify(c)),n},te=async(t,n)=>{var h,I;const c=fe(B);N(!0);const l=new FormData;l.append("file",t),n&&l.append("accessToken",n);const p=await fetch(`${c}${(I=(h=Ne)==null?void 0:h.API)==null?void 0:I.UPLOAD_VIDEO}`,{method:"POST",body:l});if(!p.ok){const ie=await p.text();throw new Error(`Upload failed: ${p.status} ${p.statusText} - ${ie}`)}return await p.json()},ae=async(t=!1)=>{if(!q()){g(!0),u(!0);return}try{let n=null;if(a.category==="Videos"&&!t&&a.files&&a.files.length>0){D(!0);try{n=await $()}catch{D(!1),g(!0),u(!0);return}D(!1);const h=[...a.files].map(I=>te(I,n));try{const I=await Promise.all(h)}catch{g(!0),u(!0);return}}else N(!0);const c=await ee(t),l=h=>{_(!1),L(!0),N(!1),o("/configCockpit/broadcastConfigurations")},p=h=>{S("Error creating broadcast:",h),g(!0),u(!0),N(!1)};_e(`/${B}/broadcastManagement/uploadFiles`,"postformdata",l,p,c)}catch(n){S("Error in submission process:",n),g(!0),u(!0)}},x=()=>{_(!0)},re=()=>{ae(!f)},oe=()=>a.category==="Videos"?".mp4":".jpeg,.jpg,.png",ne=()=>a.category==="Videos"?"Only MP4 format supported":"Only PNG, JPEG, JPG formats supported";return r(C,{sx:{padding:2,paddingBottom:10},children:[e(s,{container:!0,sx:{borderRadius:2,marginBottom:2},children:e(s,{container:!0,children:e(s,{item:!0,md:7,style:{padding:"16px",paddingLeft:""},children:r(w,{direction:"row",children:[e(pe,{onClick:()=>o("/configCockpit/broadcastConfigurations"),color:"primary","aria-label":"upload picture",component:"label",children:e(he,{sx:{fontSize:"25px",color:"#000000"}})}),r(C,{children:[e(i,{variant:"h5",paddingTop:"0.3rem",fontSize:"20px",children:e("strong",{children:"New Broadcast"})}),e(i,{variant:"body2",color:"#777",fontSize:"12px",children:"This view displays the details of the New Broadcast and allows user to create new one"})]})]})})})}),r(ve,{children:[e(Ae,{children:"Broadcast Configuration"}),r(s,{container:!0,spacing:3,children:[r(s,{item:!0,xs:12,md:6,lg:3,children:[r(i,{variant:"subtitle2",gutterBottom:!0,children:["Broadcast Category ",e("span",{style:{color:"red"},children:"*"})]}),e(Y,{fullWidth:!0,size:"small",children:r(Ce,{value:a.category,onChange:t=>m("category",t.target.value),displayEmpty:!0,error:E.includes("category"),renderValue:t=>t||e(i,{color:"text.secondary",children:"Select Category"}),children:[e(M,{value:"",children:e(i,{color:"text.secondary",children:"Select Category"})}),e(M,{value:"Announcements",children:r(w,{direction:"row",spacing:1,alignItems:"center",children:[e(Se,{color:"primary"}),e(i,{children:"Announcements"})]})}),e(M,{value:"Videos",children:r(w,{direction:"row",spacing:1,alignItems:"center",children:[e(we,{color:"primary"}),e(i,{children:"Videos"})]})})]})})]}),r(s,{item:!0,xs:12,md:6,lg:3,children:[r(i,{variant:"subtitle2",gutterBottom:!0,children:["Module ",e("span",{style:{color:"red"},children:"*"})]}),e(Y,{fullWidth:!0,size:"small",children:e(Ve,{options:[{code:"Material",desc:""},{code:"Article",desc:""},{code:"Cost Center",desc:""}],value:a.module,onChange:t=>m("module",t==null?void 0:t.code),placeholder:"Select Module",disabled:!1,minWidth:"100%",error:E.includes("module")})})]}),r(s,{item:!0,xs:12,md:6,lg:3,children:[r(i,{variant:"subtitle2",gutterBottom:!0,children:["Start Date ",e("span",{style:{color:"red"},children:"*"})]}),e(J,{size:"sm",placeholder:"Select Start Date",value:a.startDate,onChange:t=>m("startDate",t),format:"dd MMM yyyy",style:{width:"100%",height:"40px"}})]}),r(s,{item:!0,xs:12,md:6,lg:3,children:[r(i,{variant:"subtitle2",gutterBottom:!0,children:["End Date ",e("span",{style:{color:"red"},children:"*"})]}),e(J,{size:"sm",placeholder:"Select End Date",value:a.endDate,onChange:t=>m("endDate",t),format:"dd MMM yyyy",style:{width:"100%",height:"40px"}})]}),r(s,{item:!0,xs:12,children:[r(i,{variant:"subtitle2",gutterBottom:!0,children:["Broadcast Title ",e("span",{style:{color:"red"},children:"*"}),r(i,{component:"span",variant:"caption",color:"text.secondary",children:[" ","(Max 100 characters)"]})]}),e(A,{fullWidth:!0,placeholder:"Enter broadcast title",value:a.title,onChange:t=>m("title",t.target.value),error:E.includes("title"),inputProps:{maxLength:100},helperText:`${a.title.length}/100`})]}),r(s,{item:!0,xs:12,children:[r(i,{variant:"subtitle2",gutterBottom:!0,children:["Broadcast Description ",e("span",{style:{color:"red"},children:"*"}),r(i,{component:"span",variant:"caption",color:"text.secondary",children:[" ","(Max 300 characters)"]})]}),e(A,{fullWidth:!0,multiline:!0,rows:4,placeholder:"Enter broadcast description",value:a.description,onChange:t=>m("description",t.target.value),error:E.includes("description"),inputProps:{maxLength:300},helperText:`${a.description.length}/300`})]}),r(s,{item:!0,xs:12,md:8,children:[e(i,{variant:"subtitle2",gutterBottom:!0,children:"Upload Document"}),r(be,{children:[e("input",{accept:oe(),style:{display:"none"},id:"file-upload",multiple:!0,type:"file",onChange:K}),e("label",{htmlFor:"file-upload",children:r(w,{spacing:1,alignItems:"center",children:[e(Re,{color:"primary",sx:{fontSize:40}}),e(i,{variant:"body2",children:"Click to upload files"}),e(i,{variant:"caption",color:"text.secondary",children:ne()}),a.files&&r(i,{variant:"caption",color:"primary",children:[a.files.length," file(s) selected"]})]})})]})]}),r(s,{item:!0,xs:12,md:4,children:[e(i,{variant:"subtitle2",gutterBottom:!0,children:"External URL"}),e(A,{fullWidth:!0,placeholder:"Enter URL (optional)",value:a.link,onChange:t=>m("link",t.target.value),type:"url"})]})]})]}),e(Te,{sx:{position:"fixed",bottom:0,left:0,right:0,zIndex:5},elevation:2,children:r(Ie,{showLabels:!0,className:"container_BottomNav",sx:{display:"flex",justifyContent:"flex-end"},children:[e(b,{size:"small",variant:"outlined",onClick:()=>{U(!1),x()},className:"btn-mr",sx:{marginRight:1},disabled:V,children:"Save As Draft"}),e(b,{size:"small",variant:"contained",onClick:()=>{U(!0),x()},disabled:V,children:V?"Authenticating...":"Publish"})]})}),e(F,{dialogState:G,closeReusableDialog:()=>_(!1),dialogTitle:"Confirm Broadcast Creation",dialogMessage:`Are you sure you want to ${f?"publish":"save as draft"} this broadcast?${a.category==="Videos"&&f?" You will need to authenticate with Google for video upload.":""}`,handleDialogConfirm:re,handleDialogReject:()=>_(!1),showCancelButton:!0,dialogCancelText:"Cancel",dialogOkText:f?"Publish":"Save Draft",dialogSeverity:"success"}),e(ge,{openSnackBar:Z,alertMsg:"Broadcast created successfully!",handleSnackBarClose:()=>{L(!1),o("/configCockpit/broadcastManagement")}}),e(F,{dialogState:X,closeReusableDialog:()=>u(!1),dialogTitle:"Validation Error",dialogMessage:"Please fill in all required fields correctly.",handleDialogConfirm:()=>u(!1),dialogOkText:"OK",dialogSeverity:"error"}),e(Ee,{blurLoading:j,loaderMessage:P})]})};export{Je as default};
