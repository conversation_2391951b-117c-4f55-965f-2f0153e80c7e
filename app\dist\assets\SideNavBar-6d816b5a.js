import{r as S,u as W,a as $,b as j,j as t,T as G,c as y,R as P,d as A,L as T,e as E,D as L,B as z,f as H,g as F,h as R,A as _,i as q,k as K,F as Q}from"./index-f7d9b065.js";function U({navState:e,handleDrawer:x,drawerData:h,entitesAndActivities:N,handleMoreDrawer:a,mode:m,onClickNavDrawerItem:u}){$();const r=i=>{u(i),x("close"),a("close")};return t(L,{sx:{"& .MuiDrawer-root":{position:"absolute"},"& .MuiPaper-root":{position:"absolute",background:m==="dark"?"#170E5E":"#ffffff"},zIndex:1e3,left:"90px",top:"64px"},anchor:"left",open:e,onClose:x,children:t(z,{sx:{minWidth:"250px"},role:"presentation",color:"#170E5E",children:t(H,{children:h==null?void 0:h.subModule.map(i=>{var c;if(i.isAccessible&&i.isSideOption&&((c=N[h.module.iwaName])!=null&&c.includes(i.iwaName)))return t(J,{onClickNavigateTo:r,option:i,handleDrawer:x,mode:m},i.iwaName)})})})})}function V({moreNavState:e,handleMoreDrawer:x,entitesAndActivities:h,handleDrawer:N,sideNavList:a,setDrawerData:m,updateSideNav:u,mode:r,onClickMoreNavDrawerItem:i}){var l,D;const{t:c}=$(),v=F(),k=W(),n=j(),f=s=>{i(s),v(s),N("close")};return t(L,{sx:{"& .MuiDrawer-root":{position:"absolute"},"& .MuiPaper-root":{position:"absolute",background:r==="dark"?"#170E5E":"#ffffff"},zIndex:1e3,left:"90px",top:"64px"},anchor:"left",open:e,onClose:()=>x("close"),children:t(z,{sx:{minWidth:"250px"},role:"presentation",color:"#170E5E",children:t(H,{children:(D=(l=a==null?void 0:a.data)==null?void 0:l.slice(a.configuration.moreOptions,a.data.length))==null?void 0:D.map(s=>{var I,C,o;if(s.isAccessible&&s.isSideOption&&h[s.iwaName])return t(T,{disablePadding:!0,children:y(E,{className:"sideNavButton",sx:{borderBottom:"1px solid #efefef","&:hover":{backgroundColor:n.palette.primary.light},...Y(n,(I=k.pathname)==null?void 0:I.includes(`/${s.routePath}`))},onClick:()=>{s.childItems.filter(d=>d.isAccessible&&d.isSideOption).length?(N("open"),m({module:s,subModule:s.childItems})):f(s.routePath),u(s),x("close")},children:[t(P,{isSelected:(C=k.pathname)==null?void 0:C.includes(`/${s.routePath}`),mode:r,iconName:s.icon,iconSize:"20px !important"}),t(R,{primaryTypographyProps:{fontSize:"12px !important"},className:"sideNavText",sx:{fontSize:"0.1rem",color:(o=k.pathname)!=null&&o.includes(`/${s.routePath}`)?"#3730c7":r==="dark"?"#ffffff":"#000000",display:"block",textAlign:"left",autofocus:"false",textDecoration:"none",paddingLeft:"1rem !important",width:"max-content"},children:c(s.displayName)})]},s.id)},s.iwaName)})})})})}const Y=(e,x)=>({backgroundColor:x?e.palette.primary.light:"transparent",borderRadius:"4px"}),J=({option:e,popupState:x,onClickNavigateTo:h,handleDrawer:N,mode:a})=>{var k,n,f;const{t:m}=$(),u=W();F();const r=j(),i=e.childItems.filter(l=>{var D;return l.isAccessible&&l.isSideOption&&((D=u==null?void 0:u.pathname)==null?void 0:D.includes(`${l.routePath}`))}).length!==0,[c,v]=S.useState(i);if(e.isAccessible&&e.isSideOption)return e.childItems.filter(l=>l.isAccessible&&l.isSideOption).length===0?t(T,{disablePadding:!0,sx:{...Y(r,(k=u.pathname)==null?void 0:k.includes(`${e.routePath}`))},children:t(E,{className:"sideNavButton",sx:{width:"100%",display:"flex",flexDirection:"column",alignItems:"flex-start",padding:"0",borderBottom:"1px solid #efefef","&:hover .descriptionText":{opacity:1,maxHeight:"100px",transform:"translateY(0px)"}},onClick:()=>{h(e.routePath)},children:y(z,{display:"flex",flexDirection:"column",sx:{maxWidth:"250px",width:"100%",padding:"8px 16px",display:"flex",flexDirection:"column",alignItems:"flex-start"},children:[y(z,{display:"flex",alignItems:"center",children:[t(P,{isSelected:(n=u.pathname)==null?void 0:n.includes(`${e.routePath}`),iconName:e.icon,iconSize:"20px !important",mode:a}),t(R,{primaryTypographyProps:{fontSize:"12px !important"},className:"sideNavText",sx:{color:(f=u.pathname)!=null&&f.includes(`${e.routePath}`)?r.palette.primary.main:r.palette.text.secondary,paddingLeft:"1rem !important",textAlign:"left",width:"80%"},children:m(e.displayName)})]}),t(A,{className:"descriptionText",fontSize:"10px",color:"text.secondary",sx:{paddingLeft:"2.2rem",opacity:0,maxHeight:0,transform:"translateY(-5px)",transition:"opacity 0.3s ease, transform 0.3s ease, max-height 0.3s ease",overflow:"hidden",whiteSpace:"normal",wordBreak:"break-word",width:"80%",maxWidth:"100%",overflowWrap:"break-word"},children:m(e==null?void 0:e.description)})]})},e.id)}):t(T,{disablePadding:!0,children:y(K,{elevation:0,expanded:c,onClick:()=>v(!c),sx:{border:0,"&::before":{backgroundColor:"none !important"},position:"relative !important",width:"100%",margin:"0px !important"},children:[y(_,{sx:{backgroundColor:i?`${r.palette.primary.light} !important`:"",borderBottom:"1px solid #efefef",borderRadius:"4px","&:hover":{backgroundColor:r.palette.primary.light,paddingBottom:"30px !important"},margin:"0px 0px 0px 0px !important",paddingBottom:"4px !important",paddingTop:"4px !important"},expandIcon:t(P,{mode:a,isSelected:i,iconName:"ExpandMore"}),children:[t(P,{isSelected:i,iconName:e.icon,iconSize:"20px !important",mode:a}),t(R,{primaryTypographyProps:{fontSize:"12px !important"},className:"sideNavText",sx:{color:i?r.palette.primary.main:r.palette.text.secondary,textAlign:"left",textDecoration:"none",paddingLeft:"1rem !important",margin:"0px !important"},primary:y(z,{children:[t(A,{fontSize:"12px",fontWeight:500,children:e.displayName}),(e==null?void 0:e.description)&&t(A,{className:"descriptionText",fontSize:"10px",color:"text.secondary",sx:{position:"absolute",opacity:0,maxHeight:0,transform:"translateY(-5px)",transition:"opacity 0.3s ease, transform 0.3s ease, max-height 0.3s ease",overflow:"hidden",whiteSpace:"normal",wordBreak:"break-word",maxWidth:"80%",".MuiAccordionSummary-root:hover &":{opacity:1,maxHeight:"100px",maxWidth:"60%",transform:"translateY(0)",paddingTop:"4px !important"}},children:m(e.description)})]})})]}),y(q,{sx:{padding:"0px !important"},children:[t(H,{disablePadding:!0,children:e==null?void 0:e.childItems.map(l=>t("div",{children:t(J,{onClickNavigateTo:h,option:l,mode:a,handleDrawer:N})},l))}),t(A,{className:"descriptionText",fontSize:"10px",color:"text.secondary",sx:{paddingLeft:"2.2rem",opacity:0,maxHeight:0,transform:"translateY(-5px)",transition:"opacity 0.3s ease, transform 0.3s ease, max-height 0.3s ease",overflow:"hidden",whiteSpace:"normal",wordBreak:"break-word",width:"80%",maxWidth:"100%",overflowWrap:"break-word"},children:m(e==null?void 0:e.description)})]})]})})},X=S.memo(({option:e,handleDrawer:x,updateSideNav:h,setDrawerData:N,handleMoreDrawer:a,mode:m,onClickNavigateNavListItem:u})=>{const r=W(),{t:i}=$(),c=j(),v=f=>{u(f),x("close"),a("close")},n=(f=>{var l;return f==="/"?r.pathname===f:(l=r.pathname)==null?void 0:l.includes(f)})(e.routePath);if(e.isAccessible&&e.isSideOption)return e.childItems.filter(f=>f.isAccessible&&f.isSideOption).length!==0?t(E,{className:`sideNavButton_${e.componentName}`,onClick:()=>{x(),a("close"),h(e),N({module:e,subModule:e.childItems})},sx:{padding:"0.1px 0 !important",marginBottom:"3px !important",borderRadius:n?"8px !important":"",backgroundColor:n?`${c.palette.primary.light} !important`:""},children:t(G,{title:i(e==null?void 0:e.description),arrow:!0,placement:"right",enterDelay:200,leaveDelay:200,disableInteractive:!0,PopperProps:{modifiers:[{name:"preventOverflow",options:{altBoundary:!0,rootBoundary:"viewport"}},{name:"offset",options:{offset:[0,8]}}]},componentsProps:{},children:y(T,{disablePadding:!0,sx:{color:"#000000",display:"block",justifyContent:"center !important",textAlign:"center",textDecoration:"none",padding:"0px"},className:"sideNavItem",children:[t(P,{isSelected:n,iconName:e.icon,mode:m}),t(A,{display:"block",className:"sideNavText",sx:{fontSize:"11px !important",justifyContent:"center !important",flexGrow:1,textAlign:"center !important",textDecoration:"none",color:n?c.palette.primary.main:c.palette.text.secondary,fontWeight:n?"700 !important":"500 !important",borderRadius:n?"8px !important":"0px !important"},children:i(e.displayName)})]})})}):t(E,{className:`sideNavButton_${e.componentName}`,selected:n,onClick:()=>{v(e.routePath)},sx:{padding:"0",justifyContent:"center !important",marginBottom:"3px !important",...Y(c,n)},children:t(G,{title:i(e==null?void 0:e.description),arrow:!0,placement:"right",componentsProps:{},children:y(T,{disablePadding:!0,sx:{color:"#000000",display:"block",justifyContent:"center !important",textAlign:"center",autofocus:"false",textDecoration:"none",padding:"0px"},className:"sideNavItem",children:[t(P,{isSelected:n,iconName:e.icon,mode:m}),t(A,{display:"block",className:"sideNavText",sx:{fontSize:"11px !important",justifyContent:"center !important",flexGrow:1,textAlign:"center !important",textDecoration:"none",color:n?c.palette.primary.main:c.palette.text.secondary,fontWeight:n?"700":"500",borderRadius:n?"8px":"0px"},children:i(e.displayName)})]})})},e.id)});function Z(e){const{onClickNavigateNavListItem:x,onClickNavDrawerItem:h,onClickMoreNavDrawerItem:N}=e,[a,m]=S.useState([]),u=W();j(),S.useEffect(()=>{e!=null&&e.sideNavOptions&&m(e==null?void 0:e.sideNavOptions)},[e==null?void 0:e.sideNavOptions]);const r=o=>{var O,M,B;if(!o&&!u.pathname)return;let d=o?[o]:(O=a==null?void 0:a.data)==null?void 0:O.filter(p=>{var b;return p.routePath!=="/"?(b=u.pathname)==null?void 0:b.includes(p.routePath):!1});if(d!=null&&d.length&&((M=d==null?void 0:d[0])==null?void 0:M.id)>a.configuration.moreOptions){const p=[];let b=1;(B=a==null?void 0:a.data)==null||B.forEach(g=>{var w;g.id!==((w=d==null?void 0:d[0])==null?void 0:w.id)&&(g.id===a.configuration.moreOptions?(p.push({...d==null?void 0:d[0],id:b}),b++,p.push({...g,id:b})):p.push({...g,id:b}),b++)}),p.sort((g,w)=>g.id-w.id),m(g=>JSON.stringify(g.data)!==JSON.stringify(p)?{...g,data:p}:g)}};S.useEffect(()=>{r()},[]);const i=({handleDrawer:o,setDrawerData:d,entitesAndActivities:O,handleMoreDrawer:M,mode:B})=>{var p,b,g;return t(Q,{children:y(H,{className:"drawerList",children:[a&&((p=a==null?void 0:a.data)==null?void 0:p.slice(0,a.configuration.moreOptions).map(w=>{if(w.isAccessible&&w.isSideOption&&O[w.iwaName])return t(X,{handleDrawer:o,option:w,updateSideNav:r,setDrawerData:d,handleMoreDrawer:M,mode:B,onClickNavigateNavListItem:x},w.iwaName)})),((b=a==null?void 0:a.data)==null?void 0:b.length)>((g=a==null?void 0:a.configuration)==null?void 0:g.moreOptions)&&t(E,{className:"sideNavButton",onClick:()=>{o("close"),M("open")},sx:{padding:"0 !important"},children:y(T,{disablePadding:!0,sx:{color:"#000000",display:"block",textAlign:"center",autofocus:"false",textDecoration:"none"},className:"sideNavItem",children:[t(P,{mode:B,iconName:"MoreHoriz"}),t(A,{className:"clsasaassd",display:"block",sx:{fontSize:11,justifyContent:"center",flexGrow:1,textAlign:"center !important",textDecoration:"none",color:window.location.pathname==="/supplier/more"||B==="dark"?"#ffffff":"#000000",fontWeight:window.location.pathname==="/supplier/more"?"700":"500"},children:"More"})]})})]})})},[c,v]=S.useState(!1),[k,n]=S.useState(!1),[f,l]=S.useState({module:{},subModule:[]}),[D,s]=S.useState({module:{},subModule:[]}),I=o=>{v(o==="open"?!0:o==="close"?!1:!c)},C=o=>{n(o==="open"?!0:o==="close"?!1:!c)};return y("div",{className:"sideNavbar",children:[t(L,{className:"drawerBase",variant:"permanent",sx:{"& .MuiPaper-root":{background:o=>o.palette.background.default}},children:(e==null?void 0:e.entitesAndActivities)&&t(i,{setDrawerData:l,handleDrawer:I,entitesAndActivities:e==null?void 0:e.entitesAndActivities,setMoreDrawerData:s,handleMoreDrawer:C,mode:e==null?void 0:e.mode})}),(e==null?void 0:e.entitesAndActivities)&&t(U,{navState:c,handleDrawer:I,drawerData:f,entitesAndActivities:e==null?void 0:e.entitesAndActivities,handleMoreDrawer:C,mode:e==null?void 0:e.mode,onClickNavDrawerItem:h}),(e==null?void 0:e.entitesAndActivities)&&t(V,{moreNavState:k,handleMoreDrawer:C,entitesAndActivities:e==null?void 0:e.entitesAndActivities,handleDrawer:I,sideNavList:a,setDrawerData:l,updateSideNav:r,mode:e==null?void 0:e.mode,onClickMoreNavDrawerItem:N})]})}const te=S.memo(Z);export{te as default};
