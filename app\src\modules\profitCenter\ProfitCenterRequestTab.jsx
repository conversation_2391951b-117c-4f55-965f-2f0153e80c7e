import React, { useEffect, useState } from "react";
import { useSelector, useDispatch } from "react-redux";
import { useLocation, useNavigate } from "react-router-dom";
import RequestHeaderPC from "./RequestHeaderPC";
import ReusableBackDrop from "../../components/Common/ReusableBackDrop";
import ArrowCircleLeftOutlined from "@mui/icons-material/ArrowCircleLeftOutlined";
import RequestDetailsPC from "./RequestDetailsPC";
import { setActiveStep } from "@app/redux/stepperSlice";
import RequestDetailsChangePC from "./RequestDetailsChangePC";
import { setRequestHeader } from "@app/requestDataSlice";
import {
  API_CODE,
  ARTIFACTNAMES,
  DESTINATION_FIN,
  DIALOUGE_BOX_MESSAGES,
  FAILURE_DIALOG_MESSAGE,
  REQUEST_STATUS,
  REQUEST_TYPE,
  SUCCESS_DIALOG_MESSAGE,
} from "@constant/enum";
import {
  button_Outlined,
  button_Primary,
} from "../../components/Common/commonStyles.jsx";
import {
  destination_IDM,
  destination_ProfitCenter_Mass,
} from "../../destinationVariables";
import { doAjax } from "../../components/Common/fetchService";
import {
  transformApiResponseToReduxPayloadPc,
  fetchRegionBasedOnCountry,
  getProfitCenterGrp,
  changePayloadForPC,
  createPayloadForPC,
} from "../../functions";

import {
  Step,
  StepButton,
  Stepper,
  IconButton,
  DialogContent,
  DialogActions,
  Button,
} from "@mui/material";
import { Box, Grid, Typography } from "@mui/material";
import PermIdentityOutlinedIcon from "@mui/icons-material/PermIdentityOutlined";
import { APP_END_POINTS } from "@constant/appEndPoints";
import {
  resetPayloadData,
  setPCPayload,
  setRequestHeaderPayloadData,
  resetValidationStatus,
  resetProfitCenterStatePc,
} from "@app/profitCenterTabsSlice";
import { setDropDown } from "@app/dropDownDataSlice";
import AttachmentsCommentsTab from "../../components/RequestBench/RequestPages/AttachmentsCommentsTab";
import {
  setDropDown as setDropDownAction,
  setOdataApiCall,
} from "@profitCenter/slice/profitCenterDropdownSlice";
import { idGenerator } from "../../functions";
import CustomDialog from "@components/Common/ui/CustomDialog";
import { WarningOutlined } from "@mui/icons-material";
import { colors } from "@constant/colors";
import SummarizeOutlinedIcon from "@mui/icons-material/SummarizeOutlined";
import TrackChangesTwoToneIcon from "@mui/icons-material/TrackChangesTwoTone";
import FileUploadOutlinedIcon from "@mui/icons-material/FileUploadOutlined";
import PreviewPage from "../../components/RequestBench/PreviewPage";
import { LOCAL_STORAGE_KEYS, MODULE_MAP } from "../../constant/enum";
import useLang from "@hooks/useLang";
import FeedOutlinedIcon from "@mui/icons-material/FeedOutlined";
import useDropdownFMDData from "../modulesHooks/useDropdownFMDData.js";
import {
  setLocalStorage,
  clearLocalStorageItem,
  appendPrefixByJavaKey,
} from "@helper/helper.js";
import useProfitCenterChangeFieldConfig from "@hooks/useProfitCenterChangeFieldConfig";
import { END_POINTS } from "@constant/apiEndPoints";
import ExcelOperations from "@components/Common/ExcelOperationsCard";
import ChangeLogGL from "@components/Changelog/ChangeLogGL";
import {
  clearCreateChangeLogDataGL,
  setCreatePayloadCopyForChangeLog,
} from "@app/changeLogReducer";
import { clearChangeLogData } from "@app/payloadSlice";
import {
  updateCurrentCount,
  updateNextButtonStatus,
  updateTotalCount,
} from "@app/paginationSlice";
import ErrorReportDialog from "@components/Common/ErrorReportDialog";
import SuccessDialog from "@components/Common/SubmitDialog";
import BottomNavGlobal from "@components/RequestBench/RequestPages/BottomNavGlobal";

const steps = [
  "Request Header",
  "Profit Center List",
  "Attachments & Comments",
  "Preview",
];

const ProfitCenterRequestTab = () => {
  const { t } = useLang();
  const tabValue = useSelector((state) => state.CommonStepper.activeStep);
  const { getChangeTemplate } = useProfitCenterChangeFieldConfig(MODULE_MAP.PC);
const createChangeLogData = useSelector(
    (state) => state.changeLog.createChangeLogDataGL
  );
  let task = useSelector((state) => state?.userManagement.taskData);
const filteredButtons = useSelector((state) => state.payload.filteredButtons);

  const requestHeaderData = useSelector(
    (state) => state.profitCenter.payload.requestHeaderData
  );
  const requestIdHeader = useSelector(
    (state) => state.request.requestHeader?.requestId
  );
  const applicationConfig = useSelector((state) => state.applicationConfig);
  const requestHeaderSlice = useSelector(
    (state) => state.request.requestHeader
  );
  const requestType = useSelector(
    (state) => state.request.requestHeader.requestType
  );

  const reduxPayload = useSelector((state) => state.profitCenter.payload);
  const templateName = reduxPayload?.requestHeaderData?.TemplateName;

  const dynamicData = useSelector((state) => state.payload.dynamicKeyValues);
  const { fetchedProfitCenterData, fetchReqBenchData } = useSelector(
    (state) => state.profitCenter
  );

  const changeChangeLogData = useSelector(
      (state) => state.changeLog.createChangeLogDataGL
    );

  const dispatch = useDispatch();
  const [isSecondTabEnabled, setIsSecondTabEnabled] = useState(false);
  const [addHardCodeData, setAddHardCodeData] = useState(false);
  const [isAttachmentTabEnabled, setIsAttachmentTabEnabled] = useState(false);
  const [downloadClicked, setDownloadClicked] = useState(false);
  const [apiResponses, setApiResponses] = useState([]);
  const [completed, setCompleted] = useState([false, false, false]);
  const navigate = useNavigate();
  const [blurLoading, setBlurLoading] = useState(false);
  const [loaderMessage, setLoaderMessage] = useState("");
  const [isDialogVisible, setisDialogVisible] = useState(false);
  const [enableDocumentUpload, setEnableDocumentUpload] = useState(false);
  const [attachmentsData, setAttachmentsData] = useState([]);
  const [pcNumber, setPcNumber] = useState("");
  const [isChangeLogopen, setisChangeLogopen] = useState(false);
  const [messageDialogMessage, setMessageDialogMessage] = useState();
  const [dialogOpen, setDialogOpen] = useState(false);
  const [counter, setCounter] = useState(5);

  const [successDialogOpen, setSuccessDialogOpen] = useState(false);
  const [dialogData, setDialogData] = useState({
      title: "",
      message: "",
      subText: "",
      buttonText: "",
      redirectTo: "",
    });

  const isProfitCenterApiCalled = useSelector(
    (state) => state.profitCenterDropdownData?.isOdataApiCalled
  );
  const { fetchAllDropdownFMD } = useDropdownFMDData(
    destination_ProfitCenter_Mass,
    setDropDown
  );
  const handleTabChange = (index) => {
    dispatch(setActiveStep(index));
  };
  const templateFullData = useSelector(
    (state) => state?.payload?.changeFieldSelectiondata || []
  );
  const location = useLocation();
  const module = location?.state?.moduleName;

  const rowData = location.state;

  const queryParams = new URLSearchParams(location.search);
  const reqBench = queryParams.get("reqBench");

  const requestId = queryParams.get("RequestId");
  const RequestId = queryParams.get("RequestId");
  const RequestType = queryParams.get("RequestType");

  

  const isChildRequest =
    rowData?.childRequestIds !== "Not Available" &&
    typeof task === "object" &&
    task !== null &&
    Object.keys(task).length === 0 &&
    reqBench === "true";

  const fieldDisable = Object.keys(task).length !== 0;

  const handleDownload = () => {
    setDownloadClicked(true);
  };

  const openChangeLog = () => {
    setisChangeLogopen(true);
  };

useEffect(() => {
  let timer;
  if (successDialogOpen && counter > 0) {
    timer = setInterval(() => {
      setCounter((prev) => prev - 1);
    }, 1000);
  }

  if (counter === 0) {
    clearInterval(timer);
    navigate(APP_END_POINTS?.REQUEST_BENCH); 
  }

  return () => clearInterval(timer);
}, [successDialogOpen, counter, navigate]);

  const exportExcel = () => {
    const payloadData = {
      dtName:
        RequestType === REQUEST_TYPE?.CREATE ||
        RequestType === REQUEST_TYPE?.CREATE_WITH_UPLOAD ||
        requestType === REQUEST_TYPE?.CREATE ||
        requestType === REQUEST_TYPE?.CREATE_WITH_UPLOAD
          ? "MDG_PC_FIELD_CONFIG"
          : "MDG_CHANGE_TEMPLATE_DT",
      version:
        RequestType === REQUEST_TYPE?.CREATE ||
        RequestType === REQUEST_TYPE?.CREATE_WITH_UPLOAD ||
        requestType === REQUEST_TYPE?.CREATE ||
        requestType === REQUEST_TYPE?.CREATE_WITH_UPLOAD
          ? "v2"
          : "v6",
      requestId: RequestId,
      scenario: requestType || RequestType || REQUEST_TYPE?.CREATE_WITH_UPLOAD,
      isChild:
        (task && Object.keys(task).length !== 0) || rowData.isChildRequest
          ? true
          : false,
    };

    const hSuccess = (response) => {
      const href = URL.createObjectURL(response);
      const link = document.createElement("a");

      link.href = href;
      link.setAttribute(
        "download",
        `${
          payloadData?.scenario ? payloadData?.scenario : "Mass_Create"
        }_Data Export.xlsx`
      );
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(href);

      setBlurLoading(false);
      setLoaderMessage("");
      setMessageDialogMessage(
        `${
          payloadData?.scenario ? payloadData?.scenario : "Mass_Create"
        }_Data Export.xlsx has been exported successfully.`
      );
    };

    const hError = (error) => {
      console.error("Attachment fetch error:", error);
    };

    doAjax(
      `/${destination_ProfitCenter_Mass}/excel/exportPCExcel`,
      "postandgetblob",
      hSuccess,
      hError,
      payloadData
    );
  };

  const handleClosemodalData = (data) => {
    setisChangeLogopen(data);
  };

  const handleUploadPC = (file) => {
    let url = "";
    url = "getAllProfitCenterFromExcel";
    setLoaderMessage("Initiating Excel Upload");
    setBlurLoading(true);
    const formData = new FormData();
    [...file].forEach((item) => formData.append("files", item));
    formData.append(
      "dtName",
      RequestType === REQUEST_TYPE.CREATE_WITH_UPLOAD ||
        RequestType === REQUEST_TYPE.EXTEND_WITH_UPLOAD
        ? "MDG_PC_FIELD_CONFIG"
        : "MDG_CHANGE_TEMPLATE_DT"
    );
    formData.append(
      "version",
      RequestType === REQUEST_TYPE.CREATE_WITH_UPLOAD ||
        RequestType === REQUEST_TYPE.EXTEND_WITH_UPLOAD
        ? "v2"
        : "v6"
    );
    formData.append("requestId", requestId ? requestId : "");
    formData.append("IsSunoco", "false");
    formData.append("screenName", RequestType ? RequestType : "");

    const hSuccess = (data) => {
      if (data.statusCode === 200) {
        setEnableDocumentUpload(false);
        setBlurLoading(false);
        setLoaderMessage("");
        navigate(APP_END_POINTS?.REQUEST_BENCH);
      } else {
        setEnableDocumentUpload(false);
        setBlurLoading(false);
        setLoaderMessage("");
        navigate(APP_END_POINTS?.REQUEST_BENCH);
      }
    };
    const hError = (error) => {
      setBlurLoading(false);
      setLoaderMessage("");
      navigate(APP_END_POINTS?.REQUEST_BENCH);
    };

    doAjax(
      `/${destination_ProfitCenter_Mass}/massAction/${url}`,
      "postformdata",
      hSuccess,
      hError,
      formData
    );
  };

  useEffect(() => {
    if (!isProfitCenterApiCalled) {
      fetchAllDropdownFMD("profitCenter");
      setLocalStorage(LOCAL_STORAGE_KEYS.MODULE, MODULE_MAP.PC);
      dispatch(setOdataApiCall(true));
    }
    setLocalStorage(LOCAL_STORAGE_KEYS.MODULE, MODULE_MAP.PC);
    getAttachmentsIDM();
    setPcNumber(idGenerator("PC"));
    return () => {
      clearLocalStorageItem(LOCAL_STORAGE_KEYS.MODULE);
    };
  }, []);

  const getAttachmentsIDM = () => {
    const payload = {
      decisionTableId: null,
      decisionTableName: "MDG_ATTACHMENTS_LIST_DT",
      version: "v1",
      rulePolicy: null,
      validityDate: null,
      conditions: [
        {
          "MDG_CONDITIONS.MDG_ATTACHMENTS_OBJECT_TYPE": "ET Profit Center",
          "MDG_CONDITIONS.MDG_ATTACHMENTS_SCENARIO": REQUEST_TYPE.CREATE,
          "MDG_CONDITIONS.MDG_ATTACHMENTS_REG_COMP_CODE": 1, // ensure backend expects number not string
        },
      ],
      systemFilters: null,
      systemOrders: null,
      filterString: null,
    };

    const hSuccess = (data) => {
      if (data?.statusCode === 200) {
        const attachmentList =
          data?.data?.result?.[0]?.MDG_ATTACHMENTS_ACTION_TYPE ?? [];

        // Optional: If you need to transform for display
        const templateData = attachmentList.map((element, index) => ({
          id: index,
          attachmentName: element?.MDG_ATTACHMENTS_NAME,
          changeEntryFields: element?.MDG_ATTACH_CHNG_ENT_FIELDS,
        }));
        setAttachmentsData(attachmentList);
      } else {
        console.warn("Unexpected statusCode:", data?.statusCode);
      }
    };

    const hError = (error) => {
      console.error("Attachment fetch error:", error);
    };

    const endpoint =
      applicationConfig.environment === "localhost"
        ? END_POINTS.INVOKE_RULES.LOCAL
        : END_POINTS.INVOKE_RULES.PROD;

    doAjax(`/${destination_IDM}${endpoint}`, "post", hSuccess, hError, payload);
  };

  const getCompanyCode = (controllingAreaCode = "", rolePrefix = "") => {
    const hSuccess = (data) => {
      dispatch(
        setDropDownAction({
          keyName: "CompCode",
          data: data?.body || [],
        })
      );
      // dispatch(setDropDown({ keyName: "CompCode", data: data.body }));
    };

    const hError = (error) => {
      console.log(error);
    };

    doAjax(
      `/${destination_ProfitCenter_Mass}/data/getCompCodeBasedOnControllingArea?controllingArea=${controllingAreaCode}&rolePrefix=${rolePrefix}`,
      "get",
      hSuccess,
      hError
    );
  };

  const getDisplayDataPC = (requestId) => {
    setBlurLoading(true)
    const isChildPresent = rowData?.childRequestIds !== "Not Available";

    const payload = {
      sort: "id,asc",
      parentId: !isChildPresent ? requestId : "",
      massCreationId:
        isChildPresent &&
        (RequestType === REQUEST_TYPE.CREATE ||
          RequestType === REQUEST_TYPE.CREATE_WITH_UPLOAD)
          ? requestId
          : "",
      massChangeId:
        isChildPresent &&
        (RequestType === REQUEST_TYPE.CHANGE ||
          RequestType === REQUEST_TYPE.CHANGE_WITH_UPLOAD)
          ? requestId
          : "",
      page: 0,
      size: 10,
    };

    const hSuccess = (response) => {
      setBlurLoading(false)

      if (response?.body?.status === "Processing") {
        setDialogData({
            // title: SUCCESS_DIALOG_MESSAGE.TITLE,
            message: "Request is in Process !!",
            subText: SUCCESS_DIALOG_MESSAGE.SUBTEXT,
            buttonText: SUCCESS_DIALOG_MESSAGE.BUTTONTEXT,
            redirectTo: SUCCESS_DIALOG_MESSAGE.REDIRECT,
          });
          setCounter(5);
          setSuccessDialogOpen(true);
    
    return; // stop here so you don't try to read response.body etc.
  }
      const apiResponse = response?.body || [];
      
      dispatch(updateTotalCount(response?.totalElements));

      if (
        response?.totalPages === 1 ||
        response?.currentPage + 1 === response?.totalPages
      ) {
        dispatch(updateCurrentCount(response?.totalElements));
        dispatch(updateNextButtonStatus(true));
      } else {
        dispatch(
          updateCurrentCount((response?.currentPage + 1) * response?.pageSize)
        );
      }
      //new added For header Data Constant
      let requestHeaderData = response?.body[0]?.Torequestheaderdata;
      let TotalIntermediateTasks = response?.body[0]?.TotalIntermediateTasks;
      let displayData={
          RequestId: requestHeaderData.RequestId,
          RequestPrefix: requestHeaderData.RequestPrefix,
          ReqCreatedBy: requestHeaderData.ReqCreatedBy,
          ReqCreatedOn: requestHeaderData.ReqCreatedOn,
          ReqUpdatedOn: requestHeaderData.ReqUpdatedOn,
          RequestType: requestHeaderData.RequestType,
          RequestDesc: requestHeaderData.RequestDesc,
          RequestStatus: requestHeaderData.RequestStatus,
          RequestPriority: requestHeaderData.RequestPriority,
          FieldName: requestHeaderData.FieldName,
          TemplateName: requestHeaderData.TemplateName,
          Division: requestHeaderData.Division,
          region: requestHeaderData.region,
          leadingCat: requestHeaderData.leadingCat,
          firstProd: requestHeaderData.firstProd,
          launchDate: requestHeaderData.launchDate,
          isBifurcated: requestHeaderData.isBifurcated,
          screenName: requestHeaderData.screenName,
          TotalIntermediateTasks: TotalIntermediateTasks,
          childRequestId: apiResponse?.[0]?.ToChildHeaderdata?.RequestId
      }
      dispatch(setRequestHeaderPayloadData(displayData));
      
      setApiResponses(apiResponse);
      const reqType = requestHeaderData?.RequestType;
      if (
        reqType === REQUEST_TYPE.CHANGE ||
        reqType === REQUEST_TYPE.CHANGE_WITH_UPLOAD
      ) {
        getChangeTemplate();
      }
      const transformedPayload =
        transformApiResponseToReduxPayloadPc(apiResponse);

      apiResponse.forEach((item) => {
        if (item.ProfitCenterID) {
          fetchRegionBasedOnCountry(
            item.Country,
            dispatch,
            item.ProfitCenterID
          );
          getProfitCenterGrp(item.COArea, dispatch, item.ProfitCenterID);
        }
        if (apiResponse?.[0]?.COArea) {
          getCompanyCode(apiResponse?.[0]?.COArea, "ETP");
        }

        if (item?.Torequestheaderdata?.TemplateName) {
          let selectedTemplate = item?.Torequestheaderdata?.TemplateName;
          const filteredAndSortedFields = templateFullData
            .filter(
              (item) =>
                item?.MDG_CHANGE_TEMPLATE_NAME === selectedTemplate &&
                item?.MDG_MAT_CHANGE_TYPE === "Item" &&
                item?.MDG_MAT_FIELD_VISIBILITY !== "Hidden" &&
                item?.MDG_MAT_FIELD_VISIBILITY !== "Display"
            )
            .sort((a, b) => {
              // Convert to number for proper sorting, handle nulls/fallbacks
              const seqA = Number(a?.MDG_MAT_FIELD_SEQUENCE) || 0;
              const seqB = Number(b?.MDG_MAT_FIELD_SEQUENCE) || 0;
              return seqA - seqB;
            });
          const uniqueFieldNames = [
            ...new Set(
              filteredAndSortedFields
                .map((item) => item?.MDG_MAT_FIELD_NAME)
                .filter(Boolean)
            ),
          ].map((field) => ({ code: field }));
          dispatch(
            setDropDown({
              keyName: "FieldName",
              data: uniqueFieldNames || [],
              // keyName2: uniqueId,
            })
          );
        }
      });
      dispatch(setPCPayload(transformedPayload?.payload));
      dispatch(setCreatePayloadCopyForChangeLog(transformedPayload?.payload));
    };

    const hError = (error) => {
      console.error("Error fetching PC Create data:", error);
    };

    doAjax(
      `/${destination_ProfitCenter_Mass}/data/displayMassProfitCenterDto`,
      "post",
      hSuccess,
      hError,
      payload
    );
  };

  useEffect(() => {
    if (isSecondTabEnabled) {
      setCompleted((prev) => {
        const updated = [...prev];
        updated[0] = true; // ✅ mark step 1 as completed
        return updated;
      });
    }
  }, [isSecondTabEnabled]);

  useEffect(() => {
    if (isAttachmentTabEnabled) {
      setCompleted((prev) => {
        const updated = [...prev];
        updated[1] = true; // ✅ mark step 2 as completed
        return updated;
      });
    }
  }, [isAttachmentTabEnabled]);

  useEffect(() => {
    const loadData = async () => {
      if (RequestId) {
        await getDisplayDataPC(RequestId);
        if (
          ((RequestType === REQUEST_TYPE.CHANGE_WITH_UPLOAD &&
            !rowData?.length) ||
            RequestType === REQUEST_TYPE.CREATE_WITH_UPLOAD ||
            RequestType === REQUEST_TYPE.EXTEND_WITH_UPLOAD) &&
          (rowData?.reqStatus === REQUEST_STATUS.DRAFT ||
            rowData?.reqStatus === REQUEST_STATUS.UPLOAD_FAILED)
        ) {
          dispatch(setActiveStep(0));
          setIsSecondTabEnabled(false);
          setIsAttachmentTabEnabled(false);
        } else {
          dispatch(setActiveStep(1));
          setIsSecondTabEnabled(true);
          setIsAttachmentTabEnabled(true);
        }
      } else {
        dispatch(setActiveStep(0));
      }
    };

    loadData();
    return () => {
      dispatch(clearChangeLogData());
      dispatch(resetPayloadData());
      dispatch(setRequestHeader({}));
      dispatch(resetValidationStatus());
      dispatch(resetProfitCenterStatePc());
      dispatch(clearCreateChangeLogDataGL());
      dispatch(setDropDown({ keyName: "FieldName", data: [] }));
    };
  }, [requestId, dispatch]);

  const handleYes = () => {
    if (requestId && !reqBench) {
      navigate(APP_END_POINTS?.MY_TASK);
    } else if (reqBench) {
      navigate(APP_END_POINTS?.REQUEST_BENCH);
    } else if (!requestId && !reqBench) {
      navigate(APP_END_POINTS?.MASTER_DATA_PC);
    }
  };

  const handleCancel = () => {
    setisDialogVisible(false);
  };

  const pcPayload =
    RequestType === REQUEST_TYPE?.CREATE ||
    RequestType === REQUEST_TYPE?.CREATE_WITH_UPLOAD ||
    requestType === REQUEST_TYPE?.CREATE ||
    requestType === REQUEST_TYPE?.CREATE_WITH_UPLOAD
      ? createPayloadForPC(
          reduxPayload,
          requestHeaderSlice,
          requestId,
          task,
          dynamicData
        )
      : changePayloadForPC(
          requestHeaderData,
              requestHeaderSlice,
              task,
              fetchReqBenchData,
              fetchedProfitCenterData,
              changeChangeLogData,
              
        );
  
  const payloadForPreviewDownloadExcel = {
    profitCenterDetails: pcPayload,
    dtName:
      RequestType === REQUEST_TYPE?.CREATE ||
      RequestType === REQUEST_TYPE?.CREATE_WITH_UPLOAD ||
      requestType === REQUEST_TYPE?.CREATE ||
      requestType === REQUEST_TYPE?.CREATE_WITH_UPLOAD
        ? "MDG_PC_FIELD_CONFIG"
        : "MDG_CHANGE_TEMPLATE_DT",
    version:
      RequestType === REQUEST_TYPE?.CREATE ||
      RequestType === REQUEST_TYPE?.CREATE_WITH_UPLOAD ||
      requestType === REQUEST_TYPE?.CREATE ||
      requestType === REQUEST_TYPE?.CREATE_WITH_UPLOAD
        ? "v2"
        : "v6",
    requestId: requestId || "",
    scenario: RequestType || requestType,
    templateName: requestHeaderData?.TemplateName||"",
    region: "US",
  };

  const handleButtonClick = async (type, remarks) => {
      
        
        setBlurLoading(true);
        let apiEndpoint =
          END_POINTS?.MASTER_BUTTON_APIS?.[MODULE_MAP?.PC]?.[
          requestHeaderData?.RequestType
          ]?.[type];

      const finalPayload=
        RequestType === REQUEST_TYPE?.CREATE ||
    RequestType === REQUEST_TYPE?.CREATE_WITH_UPLOAD ||
    requestType === REQUEST_TYPE?.CREATE ||
    requestType === REQUEST_TYPE?.CREATE_WITH_UPLOAD?
         createPayloadForPC(
        reduxPayload,
        requestHeaderSlice,
        requestId,
        task,
        dynamicData,
        createChangeLogData,
        remarks,
        // NOTE:use for filtered button
        // userInput,
        // selectedLevel
      ):changePayloadForPC(
              requestHeaderData,
              requestHeaderSlice,
              task,
              fetchReqBenchData,
              fetchedProfitCenterData,
              changeChangeLogData,
              remarks
      )
        const hSuccess = (data) => {
          setBlurLoading(false);
          if (data?.statusCode === API_CODE.STATUS_200 || data?.statusCode === API_CODE.STATUS_201) {
          setDialogData({
            title: SUCCESS_DIALOG_MESSAGE.TITLE,
            message: data.message,
            subText: SUCCESS_DIALOG_MESSAGE.SUBTEXT,
            buttonText: SUCCESS_DIALOG_MESSAGE.BUTTONTEXT,
            redirectTo: SUCCESS_DIALOG_MESSAGE.REDIRECT,
          });
          setSuccessDialogOpen(true);
        } else if (data?.statusCode === API_CODE.STATUS_500 || data?.statusCode === API_CODE.STATUS_501) {
          setDialogData({
            title: FAILURE_DIALOG_MESSAGE.TITLE,
            message: data.message,
            subText: FAILURE_DIALOG_MESSAGE.SUBTEXT,
            buttonText: FAILURE_DIALOG_MESSAGE.BUTTONTEXT,
            redirectTo: FAILURE_DIALOG_MESSAGE.REDIRECT,
          });
          setSuccessDialogOpen(true);
        } else {
          // fallback - maybe show a snackbar or console log
          setSnackbarOpen(true);
          setAlertMsg("Unexpected response received.");
        }
        };
    
        const hError = (error) => {
          setBlurLoading(false);
          showSnackbar(error?.error, "error");
        };
    
        doAjax(apiEndpoint?.URL, "POST", hSuccess, hError, finalPayload);
      };

     
  return (
    <div>
      <Box sx={{ padding: 2 }}>
        <Grid
          sx={{
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
          }}
        >
          {requestId || requestIdHeader ? (
            <Box>
              <Typography
                variant="h6"
                sx={{
                  mb: 1,
                  textAlign: "left",
                  display: "flex",
                  alignItems: "center",
                  gap: 1,
                }}
              >
                <PermIdentityOutlinedIcon sx={{ fontSize: "1.5rem" }} />
                {t("Request Header ID:")}{" "}
                <span>
                  {requestIdHeader
                    ? requestHeaderSlice?.requestId
                    : `${requestId}`}
                </span>
              </Typography>

              {templateName && (
                <Typography
                  variant="h6"
                  sx={{
                    mb: 1,
                    textAlign: "left",
                    display: "flex",
                    alignItems: "center",
                    gap: 1,
                  }}
                >
                  <FeedOutlinedIcon sx={{ fontSize: "1.5rem" }} />
                  Template Name: <span>{templateName}</span>
                </Typography>
              )}
            </Box>
          ) : (
            <div style={{ flex: 1 }} />
          )}

          {isChangeLogopen && (
            <ChangeLogGL
              module={MODULE_MAP.PC}
              open={true}
              closeModal={handleClosemodalData}
              requestId={requestHeaderData?.RequestId || requestId}
              requestType={"create"}
            />
          )}
          {tabValue === 1 && (
            <Box
              sx={{ display: "flex", justifyContent: "flex-end", gap: "1rem" }}
            >
              <Button
                variant="outlined"
                size="small"
                title="Error Report"
                disabled={!RequestId}
                onClick={() => setDialogOpen(true)}
                color="primary"
              >
                <SummarizeOutlinedIcon sx={{ padding: "2px" }} />
              </Button>

              <Button
                variant="outlined"
                disabled={!RequestId}
                size="small"
                onClick={openChangeLog}
                title="Change Log"
              >
                <TrackChangesTwoToneIcon sx={{ padding: "2px" }} />
              </Button>

              <Button
                variant="outlined"
                disabled={!RequestId}
                size="small"
                onClick={exportExcel}
                title="Export Excel"
              >
                <FileUploadOutlinedIcon sx={{ padding: "2px" }} />
              </Button>
            </Box>
          )}
        </Grid>
        <IconButton
          onClick={() => {
            if (reqBench === "true") {
              navigate(APP_END_POINTS?.REQUEST_BENCH);
              return;
            }
            setisDialogVisible(true);
          }}
          color="primary"
          aria-label="upload picture"
          component="label"
          sx={{ left: "-10px" }}
          title="Back"
        >
          <ArrowCircleLeftOutlined
            sx={{ fontSize: "25px", color: "#000000" }}
          />
        </IconButton>

        <Stepper
          nonLinear
          activeStep={tabValue}
          sx={{
            display: "flex",
            alignItems: "center",
            justifyContent: "center",
            margin: "-35px 14% 10px",
            marginTop: "-35px",
          }}
        >
          {steps.map((label, index) => (
            <Step key={label} >
              <StepButton
                color="inherit"
                disabled={
                  index === 1 && !isSecondTabEnabled
                  //  ||
                  // (index === 2 && !isAttachmentTabEnabled)
                  // (index === 3 && !isAttachmentTabEnabled)
                }
                onClick={() => handleTabChange(index)}
                sx={{ fontSize: "50px", fontWeight: "bold" }}
              >
                <span style={{ fontSize: "15px", fontWeight: "bold" }}>
                  {label}
                </span>
              </StepButton>
            </Step>
          ))}
        </Stepper>

        {tabValue === 0 && (
          <>
            <RequestHeaderPC
              apiResponse={apiResponses}
              reqBench={reqBench}
              downloadClicked={downloadClicked}
              setDownloadClicked={setDownloadClicked}
              setIsSecondTabEnabled={setIsSecondTabEnabled}
              setIsAttachmentTabEnabled={setIsAttachmentTabEnabled}
            />
            {(RequestType === REQUEST_TYPE.CHANGE_WITH_UPLOAD ||
              RequestType === REQUEST_TYPE.CREATE_WITH_UPLOAD ||
              RequestType === REQUEST_TYPE.EXTEND_WITH_UPLOAD) &&
              ((rowData?.reqStatus == REQUEST_STATUS.DRAFT &&
                !rowData?.material?.length) ||
                rowData?.reqStatus == REQUEST_STATUS.UPLOAD_FAILED) && (
                <ExcelOperations
                  handleDownload={handleDownload}
                  setEnableDocumentUpload={setEnableDocumentUpload}
                  enableDocumentUpload={enableDocumentUpload}
                  handleUploadMaterial={handleUploadPC}
                />
              )}
          </>
        )}
        {tabValue === 1 &&
          requestHeaderData.RequestType &&
          (requestHeaderData.RequestType === "Change" ||
          requestHeaderData.RequestType === "Change with Upload" ? (
            <RequestDetailsChangePC
              reqBench={reqBench}
              requestId={requestId}
              apiResponses={apiResponses}
              setIsAttachmentTabEnabled={setIsAttachmentTabEnabled}
              setCompleted={setCompleted}
              downloadClicked={downloadClicked}
              setDownloadClicked={setDownloadClicked}
              template={templateName}
              module={MODULE_MAP?.PC}
              isDisabled={isChildRequest}
              fieldDisable={fieldDisable}
            />
          ) : (
            <RequestDetailsPC
              reqBench={reqBench}
              apiResponses={apiResponses}
              setIsAttachmentTabEnabled={setIsAttachmentTabEnabled} // ✅ pass setter
              setCompleted={setCompleted}
              module={MODULE_MAP?.PC}
              isDisabled={isChildRequest}
              fieldDisable={fieldDisable}
            />
          ))}
        {tabValue === 2 && (
          <AttachmentsCommentsTab
            requestStatus={
              rowData?.reqStatus
                ? rowData?.reqStatus
                : REQUEST_STATUS.ENABLE_FOR_FIRST_TIME
            }
            attachmentsData={attachmentsData}
            requestIdHeader={requestIdHeader ? requestIdHeader : requestId}
            pcNumber={pcNumber}
            module={MODULE_MAP?.PC}
            artifactName={ARTIFACTNAMES.PC}
          />
        )}
        {tabValue === 3 && (
          <Box
            sx={{
              width: "100%",
              overflow: "auto",
            }}
          >
            <PreviewPage
              module={MODULE_MAP?.PC}
              payloadForPreviewDownloadExcel={payloadForPreviewDownloadExcel}
            />
          </Box>
        )}
      </Box>

      <SuccessDialog
              open={successDialogOpen}
              onClose={() => setSuccessDialogOpen(false)}
              title={dialogData.title}
              message={dialogData.message}
              subText={dialogData.subText}
              buttonText={dialogData.buttonText}
              redirectTo={dialogData.redirectTo}
            />
      <ReusableBackDrop
        blurLoading={blurLoading}
        loaderMessage={loaderMessage}
      />
      <ErrorReportDialog
        dialogState={dialogOpen}
        closeReusableDialog={() => setDialogOpen(false)}
        module={MODULE_MAP?.PC}
        isHierarchyCheck={false}
      />
      {isDialogVisible && (
        <CustomDialog
          isOpen={isDialogVisible}
          titleIcon={
            <WarningOutlined
              size="small"
              sx={{ color: colors?.secondary?.amber, fontSize: "20px" }}
            />
          }
          Title={"Warning"}
          handleClose={handleCancel}
        >
          <DialogContent sx={{ mt: 2 }}>
            {DIALOUGE_BOX_MESSAGES.LEAVE_PAGE_MESSAGE}
          </DialogContent>
          <DialogActions>
            <Button
              variant="outlined"
              size="small"
              sx={{ ...button_Outlined }}
              onClick={handleCancel}
            >
              {t("No")}
            </Button>
            <Button
              variant="contained"
              size="small"
              sx={{ ...button_Primary }}
              onClick={handleYes}
          >
              {t("Yes")}
            </Button>
          </DialogActions>
        </CustomDialog>
      )}
      {tabValue !== 0 && tabValue !== 1 && (
  <BottomNavGlobal
    handleSaveAsDraft={handleButtonClick}
    handleSubmitForReview={handleButtonClick}
    // NOTE:use for button display
    // handleSubmitForApprove={handleButtonClick}
    // handleSendBack={handleButtonClick}
    // handleCorrection={handleButtonClick}
    // handleRejectAndCancel={handleButtonClick}
    // handleValidateAndSyndicate={handleButtonClick}
    
    // validateEnabled={validateEnabled}
    filteredButtons={filteredButtons}
    moduleName={module}
    activeTab={tabValue}
   // NOTE:use for button display
    // showWfLevels={showWfLevels}
    // selectedLevel={selectedLevel}
    // workFlowLevels={wfLevels}
    // setSelectedLevel={setSelectedLevel}
  />
)}

        
    </div>
  );
};

export default ProfitCenterRequestTab;
