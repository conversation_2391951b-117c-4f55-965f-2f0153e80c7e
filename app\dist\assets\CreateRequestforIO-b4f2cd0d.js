import{s as rt,n as k,o as Zt,r as n,cv as br,aO as te,a as Rt,u as dt,g as Ot,cw as Ct,cx as Ee,cy as _r,cz as Bt,cA as Dr,cB as Nr,cC as Gt,cD as xr,aX as Te,j as r,aZ as Kt,c as O,b1 as vr,d as We,B as oe,O as he,aD as Ht,c5 as yt,an as me,aH as kr,bK as at,C as ve,bj as R,aT as je,ae as ce,aJ as Jt,aY as Ue,cE as Lr,cF as lt,cG as et,aP as It,cH as wr,cI as ge,cJ as mt,cK as Ge,cL as He,a9 as Mr,aa as Ut,a6 as tt,cM as Fr,cN as qr,T as it,cO as Pr,cP as Wt,cQ as jt,ch as Br,b6 as Gr,b5 as Hr,ag as Ur,ai as Wr,aj as jr,al as Xt,$ as $r,bO as Vr,bP as Yr,bQ as $t,bG as Vt,F as Et,am as er,aG as zr,cR as Yt,cS as Je,cT as Xe,cU as zt,cV as Qr,cW as Zr,cX as Kr,aK as Tt,cY as Jr,cZ as Xr,bf as tr,c_ as es,c$ as ts,d0 as rs,d1 as ss,d2 as os,d3 as ns,d4 as is,d5 as as,d6 as ls,d7 as ds,br as cs,ad as us,d8 as ps,Z as fs,d9 as gs}from"./index-f7d9b065.js";import{d as hs}from"./PermIdentityOutlined-0746a749.js";import{F as ms,s as Ts}from"./FilterFieldGlobal-f8e8f75f.js";import{u as rr}from"./useDownloadExcel-cd596a31.js";import{D as ys,B as Es,A as Rs,P as Os}from"./PreviewPage-634057fa.js";import{F as Qt,C as Cs}from"./ChangeLogGlobal-ef1f8800.js";import{S as xe}from"./SingleSelectDropdown-aee403d4.js";import{u as Is}from"./useInternalOrderFieldConfig-01532769.js";import{G as Ss}from"./GenericTabsGlobal-613ace00.js";import{u as As,E as bs}from"./ErrorReportDialog-cb66d1ed.js";import{u as _s}from"./useDynamicWorkflowDT-955b7628.js";import{E as Ds}from"./ExcelOperationsCard-49e9ffd2.js";import{d as Ns}from"./FileUploadOutlined-4a68a28a.js";import{d as xs}from"./TrackChangesTwoTone-7a2ab513.js";import"./AdapterDayjs-2a9281df.js";import"./advancedFormat-4f6292d4.js";import"./customParseFormat-1bc1aa07.js";import"./isBetween-c1c6beb9.js";import"./DatePicker-a8e9bd4a.js";import"./useMobilePicker-9b56b5b6.js";import"./CSSTransition-cd337b47.js";import"./AttachFile-8d552da8.js";import"./UtilDoc-6f590135.js";import"./FileDownloadOutlined-59854a55.js";import"./VisibilityOutlined-b6cd6d28.js";import"./DeleteOutlined-e668453f.js";import"./CloudUpload-0ba6431e.js";import"./utilityImages-067c3dc2.js";import"./Delete-5278579a.js";import"./ReusablePromptBox-e1871d49.js";import"./featureConfig-652a9f8d.js";import"./Description-ab582559.js";import"./DataObject-52409c14.js";import"./Download-52c4427b.js";import"./useFinanceCostingRows-ffbb569f.js";import"./CheckCircleOutline-e186af3e.js";import"./createChangeLogTemplate-fc8912a0.js";import"./lz-string-0665f106.js";import"./ErrorHistory-ef441d1f.js";import"./CloudDownload-9a7605e9.js";import"./AttachmentUploadDialog-43cc9099.js";const sr=({requestId:A,reqBench:d})=>{const i=rt(),L=k(h=>{var c,a;return(a=(c=h.internalOrder.IOpayloadData)==null?void 0:c.requestHeaderData)==null?void 0:a.RequestType}),{getDtCall:q,dtData:y}=Zt(),g=()=>{const c={decisionTableId:null,decisionTableName:"MDG_FMD_REQUEST_HEADER_CONFIG",version:"v2",conditions:[{"MDG_CONDITIONS.MDG_MAT_SCENARIO":L||"Create","MDG_CONDITIONS.MDG_MAT_MODULE_NAME":te.IO}]};q(c)};n.useEffect(()=>{var h,c;if(y){const p={"Header Data":(((c=(h=y==null?void 0:y.result)==null?void 0:h[0])==null?void 0:c.MDG_MAT_REQUEST_HEADER_CONFIG)??[]).sort((w,re)=>w.MDG_MAT_SEQUENCE_NO-re.MDG_MAT_SEQUENCE_NO).map(w=>({fieldName:w.MDG_MAT_UI_FIELD_NAME,sequenceNo:w.MDG_MAT_SEQUENCE_NO,fieldType:w.MDG_MAT_FIELD_TYPE,maxLength:w.MDG_MAT_MAX_LENGTH,value:w.MDG_MAT_DEFAULT_VALUE,visibility:w.MDG_MAT_VISIBILITY,jsonName:w.MDG_MAT_JSON_FIELD_NAME}))};i(br(p))}},[y]),n.useEffect(()=>{g()},[A,d,L])},vs=({setIsSecondTabEnabled:A,setIsAttachmentTabEnabled:d,setDownloadClicked:i,downloadClicked:L})=>{var Le,Ve,ye;const{t:q}=Rt(),{handleDownload:y,handleEmailDownload:g}=rr((Le=yt)==null?void 0:Le.IO),[h,c]=n.useState("systemGenerated"),[a,C]=n.useState(!1),[p,w]=n.useState(""),[re,Z]=n.useState(!1),u=rt(),P=dt(),m=new URLSearchParams(P.search),se=m.get("RequestId"),Y=m.get("reqBench"),z=Ot(),W=k(o=>o.internalOrder.requestHeaderDTIO),f=k(o=>o.internalOrder.IOpayloadData),b=k(o=>o.request.requestHeader),B=k(o=>o.internalOrder.savedReqData),F=k(o=>o.userManagement.userData),{showSnackbar:K}=Ct();sr({requestId:se,reqBench:Y}),u(Ee({keyName:"RequestPriority",data:_r})),u(Ee({keyName:(Ve=Bt)==null?void 0:Ve.REGION,data:Dr})),u(Ee({keyName:(ye=Bt)==null?void 0:ye.REQUEST_TYPE,data:Nr})),n.useEffect(()=>{var o,_;!se&&!Y&&(F!=null&&F.user_id)&&((o=W==null?void 0:W["Header Data"])==null?void 0:o.length)>0&&((_=f==null?void 0:f.requestHeaderData)!=null&&_.ReqCreatedBy||u(Gt({keyName:"ReqCreatedBy",data:F==null?void 0:F.user_id})),u(Gt({keyName:"RequestStatus",data:"DRAFT"})),u(xr({keyName:"RequestStatus",data:"DRAFT"})))},[se,Y,F==null?void 0:F.user_id,W]),n.useEffect(()=>{var o,_,$;if(L){if(((o=f==null?void 0:f.requestHeaderData)==null?void 0:o.RequestType)===Te.CREATE_WITH_UPLOAD){C(!0);return}if(((_=f==null?void 0:f.requestHeaderData)==null?void 0:_.RequestType)===(($=Te)==null?void 0:$.CHANGE_WITH_UPLOAD))return}},[L]);const ke=()=>{const o=W[Object.keys(W)[0]];return o!=null&&o.length?o.every(_=>{var $,ee;return _.visibility!==(($=Ht)==null?void 0:$.MANDATORY)||!!((ee=f==null?void 0:f.requestHeaderData)!=null&&ee[_.jsonName])}):!1},G=()=>{var o,_;h==="systemGenerated"&&(y(w,Z,f,(o=te)==null?void 0:o.IO),ue()),h==="mailGenerated"&&(g(w,Z,f,(_=te)==null?void 0:_.IO),ue())},Re=o=>{var _;c((_=o==null?void 0:o.target)==null?void 0:_.value)},ue=()=>{var o;i(!1),C(!1),c("systemGenerated"),se||z((o=at)==null?void 0:o.REQUEST_BENCH)},$e=async()=>{const o=(f==null?void 0:f.requestHeaderData)||{},_=new Date().toISOString(),$={RequestId:"",ReqCreatedBy:(F==null?void 0:F.user_id)||"",ReqCreatedOn:o!=null&&o.ReqCreatedOn?new Date(o.ReqCreatedOn).toISOString():_,ReqUpdatedOn:_,RequestType:(o==null?void 0:o.RequestType)||"",RequestPriority:(o==null?void 0:o.RequestPriority)||"Medium",RequestDesc:(o==null?void 0:o.RequestDesc)||"",RequestStatus:(o==null?void 0:o.RequestStatus)||"DRAFT",TemplateName:"",FieldName:"",Region:(o==null?void 0:o.Region)||"US",IsBifurcated:(o==null?void 0:o.IsBifurcated)??!1},ee=T=>{var Oe,ne,we,le;if((T==null?void 0:T.status)===!0||(T==null?void 0:T.statusCode)===Jt.STATUS_200||(T==null?void 0:T.status)==="success"){if(K(`${q(Ue.REQUEST_HEADER_CREATED)} ${((Oe=T==null?void 0:T.body)==null?void 0:Oe.RequestId)||(T==null?void 0:T.RequestId)||""}`,"success"),u(Lr(T.body||T)),u(lt({keyName:"requestHeaderData",data:{...T.body||T,RequestId:((ne=T==null?void 0:T.body)==null?void 0:ne.RequestId)||(T==null?void 0:T.RequestId)}})),d&&d(!0),A&&A(!0),((we=f==null?void 0:f.requestHeaderData)==null?void 0:we.RequestType)===Te.CREATE_WITH_UPLOAD){C(!0);return}if((f==null?void 0:f.RequestType)===((le=Te)==null?void 0:le.CHANGE_WITH_UPLOAD))return;u(et(1))}else K(ce.ERROR_REQUEST_HEADER,"error")},Ye=()=>{K(ce.ERROR_REQUEST_HEADER,"error")};try{ve(`/${R}${je.MASS_ACTION.CREATE_IO_REQUEST}`,"post",ee,Ye,$)}catch{K(ce.ERROR_REQUEST_HEADER,"error")}};return r("div",{children:r(Kt,{spacing:2,children:Object.entries(W).map(([o,_])=>O(he,{item:!0,md:12,sx:{backgroundColor:"white",borderRadius:"8px",border:"1px solid #E0E0E0",mt:.25,boxShadow:"0px 2px 14px 0px rgba(48, 38, 185, 0.10)",...vr},children:[r(We,{sx:{fontSize:"12px",fontWeight:"700",paddingBottom:"10px"},children:q(o)}),r(oe,{children:r(he,{container:!0,spacing:1,children:Array.isArray(_)&&_.filter($=>$.visibility!==Ht.HIDDEN).sort(($,ee)=>$.sequenceNo-ee.sequenceNo).map($=>r(ms,{isHeader:!0,field:$,dropDownData:{},module:yt.IO,disabled:se||(b==null?void 0:b.requestId)||(B==null?void 0:B.RequestId),requestHeader:!0},$.jsonName))})}),!se&&!(b!=null&&b.requestId)&&!(B!=null&&B.RequestId)&&r(oe,{sx:{display:"flex",justifyContent:"flex-end",marginTop:"20px"},children:r(me,{variant:"contained",color:"primary",onClick:$e,disabled:!ke(),children:q("Save Request Header")})}),r(kr,{}),r(ys,{onDownloadTypeChange:G,open:a,downloadType:h,handleDownloadTypeChange:Re,onClose:ue})]},o))})})},ks=()=>{const{fieldConfigByOrderType:A}=k(g=>g.internalOrder),{IOpayloadData:d}=k(g=>g.internalOrder),{showSnackbar:i}=Ct(),L=g=>{const h=["orderType","controllingArea","description"],c={orderType:"Order Type",controllingArea:"Controlling Area",description:"Description"},a=[];return h.forEach(C=>{let p=g[C];typeof p=="object"&&p!==null&&p.code!==void 0&&(p=p.code),(p==null||typeof p=="string"&&p.trim()==="")&&a.push(c[C]||C)}),{missingFields:a.length>0?a:null,isValid:a.length===0,viewType:null,failedView:a.length>0?"table":null}};return{checkValidation:g=>{var m,se;const h=typeof g.orderType=="object"?(m=g.orderType)==null?void 0:m.code:g.orderType,c=A[h];if(!c||!c.mandatoryFields)return L(g);const a={};let C=[],p=null;const w=["orderType","controllingArea","description"],re={orderType:"Order Type",controllingArea:"Controlling Area",description:"Description"},Z=[];w.forEach(Y=>{let z=g[Y];typeof z=="object"&&z!==null&&z.code!==void 0&&(z=z.code),(z==null||typeof z=="string"&&z.trim()==="")&&Z.push(re[Y]||Y)}),Z.length>0&&(a["Internal Order List"]=Z,C.push(...Z),p="Internal Order List");const u=(se=d==null?void 0:d.rowsBodyData)==null?void 0:se[g.id],P=(u==null?void 0:u.payload)||{};return Object.entries(c.mandatoryFields).forEach(([Y,z])=>{const W=[];z.forEach(f=>{const b=P[f.jsonName];(!b||typeof b=="string"&&b.trim()==="")&&W.push(f.fieldName)}),W.length>0&&(a[Y]=W,C.push(...W),p||(p=Y))}),{missingFields:C.length>0?a:null,isValid:C.length===0,viewType:p,failedView:p}},checkBasicValidation:L,checkForDuplicateRows:g=>{const h=[];for(let c=0;c<g.length;c++)for(let a=c+1;a<g.length;a++){const C=g[c],p=g[a];C.orderType===p.orderType&&C.controllingArea===p.controllingArea&&C.compCode===p.compCode&&C.internalOrder===p.internalOrder&&C.internalOrder&&C.internalOrder.trim()!==""&&h.push({row1:c+1,row2:a+1})}if(h.length>0){const c=h.map(a=>`Rows ${a.row1} and ${a.row2} have identical key fields`);return i(ce.IO_DUPLICATE_ROWS_ERROR(c),"error"),!1}return!0}}},Ls=A=>{var vt,kt,Lt,wt,Mt,Ft,qt;const{t:d}=Rt(),i=rt(),L=Ot(),q=dt(),y=new URLSearchParams(q.search),g=y.get("RequestId"),h=y.get("reqBench"),c=(q==null?void 0:q.state)||{},{fetchInternalOrderFieldConfig:a,fieldConfigByOrderType:C}=Is(),{getButtonsDisplayGlobal:p,showWfLevels:w}=As(),{checkValidation:re,checkForDuplicateRows:Z}=ks(),[u,P]=n.useState([]),[m,se]=n.useState(!1),[Y,z]=n.useState(0),[W,f]=n.useState(0),[b,B]=n.useState(!1),[F,K]=n.useState(!1),[ke,G]=n.useState(""),[Re,ue]=n.useState(""),[$e,Le]=n.useState([]),[Ve,ye]=n.useState(null),[o,_]=n.useState(null),[$,ee]=n.useState([]),[Ye,T]=n.useState(""),[Oe,ne]=n.useState([]),[we,le]=n.useState(!1),[st,Me]=n.useState([]),[ct,ot]=n.useState({}),[ze,ut]=n.useState({open:!1,message:"",title:""}),Qe=k(e=>e.internalOrder.isOpenDialog),[pe,Ze]=n.useState("yes"),[Ce,Ie]=n.useState(""),[Se,Ae]=n.useState(""),[N,j]=n.useState(""),[de,Fe]=n.useState(!1),[be,pt]=n.useState(""),{showSnackbar:ie}=Ct(),{customError:St,log:qs}=It(),{getDynamicWorkflowDT:or}=_s(),ae=k(e=>e.internalOrder.activeRowId),Ke=k(e=>e.internalOrder.selectedOrderType),I=k(e=>e.internalOrder.dropDownDataIO),D=k(e=>e.internalOrder.IOpayloadData),S=k(e=>e.userManagement.taskData),At=k(e=>e.payload.filteredButtons),ft=k(e=>e.internalOrder.savedReqData),bt=k(e=>e.internalOrder),nr=k(e=>e.internalOrder.requestHeaderDTIO),_t=k(e=>e.internalOrder.validatedRows),ir=k(e=>e.changeLog.createChangeLogDataIO),ar=k(e=>e.internalOrder.validatedRowsStatus),Dt=(ft==null?void 0:ft.RequestId)||((vt=D==null?void 0:D.requestHeaderData)==null?void 0:vt.RequestId)||"",Q=(D==null?void 0:D.requestHeaderData)||"",gt=(D==null?void 0:D.childRequestHeaderData)||"",nt=!wr.includes(A==null?void 0:A.requestStatus)||g&&!h,lr={taskId:(S==null?void 0:S.taskId)||null,processInstanceId:(S==null?void 0:S.processInstanceId)||null,assignee:(S==null?void 0:S.assignee)||null,buttonConfig:At,requestType:(kt=Te)==null?void 0:kt.CREATE,moduleType:(Lt=te)==null?void 0:Lt.IO,userContext:{userId:S==null?void 0:S.assignee,timestamp:new Date().toISOString()},...S},_e=(((wt=C[Ke])==null?void 0:wt.allTabsData)||[]).filter(e=>e.tab.toLowerCase()!=="header"&&e.tab.toLowerCase()!=="request header");n.useEffect(()=>{var e,t;(g&&!h||h&&(c==null?void 0:c.reqStatus)!==((e=ge)==null?void 0:e.DRAFT)||h&&(c==null?void 0:c.reqStatus)===((t=ge)==null?void 0:t.DRAFT)&&(c==null?void 0:c.objectNumbers)!=="Not Available")&&i(mt(!1))},[]),n.useEffect(()=>{S!=null&&S.ATTRIBUTE_1&&p("Internal Order","MDG_DYN_BTN_DT","v3")},[S]),n.useEffect(()=>{const e=async()=>{var t;try{const s=await or(Q==null?void 0:Q.RequestType,Q==null?void 0:Q.Region,"",gt==null?void 0:gt.InternalOrderGroup,S==null?void 0:S.ATTRIBUTE_3,"v1","MDG_INTORD_DYNAMIC_WORKFLOW_DT",(t=te)==null?void 0:t.IO);Le(s)}catch(s){St(s)}};Q!=null&&Q.RequestType&&(Q!=null&&Q.Region)&&(S!=null&&S.ATTRIBUTE_3)&&e()},[Q,S==null?void 0:S.ATTRIBUTE_3]),n.useEffect(()=>{var s;const e=(D==null?void 0:D.rowsBodyData)||{},t=Object.keys(e).map(l=>{var E,x,M,v,H,V;return{id:l,included:((E=e[l])==null?void 0:E.included)??!0,internalOrder:((x=e[l])==null?void 0:x.internalOrder)??"",controllingArea:((M=e[l])==null?void 0:M.controllingArea)??"",orderType:((v=e[l])==null?void 0:v.orderType)??"",description:((H=e[l])==null?void 0:H.description)??"",compCode:((V=e[l])==null?void 0:V.compCode)??""}});if(t.length>0&&(P(t),!ae&&t.length>0)){const l=t[0],E=((s=l.orderType)==null?void 0:s.code)||l.orderType;i(Ge(l.id)),E&&i(He(E))}},[D==null?void 0:D.rowsBodyData,ae,i]),n.useEffect(()=>{var e;if(u.length>0&&!ae){const t=u[0],s=((e=t.orderType)==null?void 0:e.code)||t.orderType;if(i(Ge(t.id)),s){i(He(s));try{a(s)}catch(l){St("Failed to fetch field configuration for first row:",l)}}}},[u,ae,i]),n.useEffect(()=>{Ke&&a(Ke)},[Ke]);const De=async(e,t)=>{var M,v,H,V,J;if(e==="VALIDATE"){Nt();return}K(!0);const s=(J=(V=(H=(M=je)==null?void 0:M.MASTER_BUTTON_APIS)==null?void 0:H[(v=te)==null?void 0:v.IO])==null?void 0:V[Q==null?void 0:Q.RequestType])==null?void 0:J[e],l=Yt(bt,Q,Dt,S,Re,t),E=U=>{var X;K(!1),ie((U==null?void 0:U.message)||((X=Ue)==null?void 0:X.DEFAULT),"success"),s!=null&&s.NAVIGATE_TO&&L(s==null?void 0:s.NAVIGATE_TO)},x=U=>{K(!1),ie((U==null?void 0:U.error)||ce.IO_PROCESSING_ERROR,"error")};ve(s==null?void 0:s.URL,"POST",E,x,l)},Nt=async()=>{if(!Z(u))return;let e=!0,t=null;for(let x=0;x<u.length;x++){const M=u[x],{missingFields:v,isValid:H,viewType:V,failedView:J}=re(M);!H&&v?(e=!1,t||(t={row:M,missingFields:v,viewType:V,failedView:J}),i(Je({rowId:M.id,status:"error"})),i(Xe({rowId:M.id,isValid:!1}))):(i(Je({rowId:M.id,status:"success"})),i(Xe({rowId:M.id,isValid:!0})))}if(!e&&t){if(i(Ge(t.row.id)),ye(t.missingFields),_(t.failedView),typeof t.missingFields=="object"&&!Array.isArray(t.missingFields)){const x=Object.values(t.missingFields).flat();if(ee(x),t.viewType){const H=_e.findIndex(V=>V.tab===t.viewType);H!==-1&&(f(H),T(t.viewType),ne([t.viewType]))}const M=[];let v=[];Object.entries(t.missingFields).forEach(([H,V])=>{v.push(...V),V.forEach(J=>{var Ne;const U=typeof t.row.orderType=="object"?(Ne=t.row.orderType)==null?void 0:Ne.code:t.row.orderType,X=C[U];X&&X.mandatoryFields&&Object.values(X.mandatoryFields).forEach(Pe=>{const fe=Pe.find(Be=>Be.fieldName===J);fe&&M.push(fe.jsonName)})})}),ot(H=>({...H,[t.row.id]:M})),Me(v),le(!0)}else ee([]),ne([]),Me(t.missingFields),le(!0);return}ye(null),_(null),ee([]),ne([]),K(!0);const s=Yt(bt,nr,Dt,S,lr,ir),l=x=>{K(!1),ie(Ue.IO_VALIDATION_INITIATED,"success"),L("/requestbench")},E=x=>{K(!1),ie(ce.IO_VALIDATION_ERROR,"error")};ve(`/${R}${je.MASS_ACTION.VALIDATE_MASS_INTERNAL_ORDER}`,"POST",l,E,s)},dr=e=>{const t=_t[e.id],s=ar[e.id];return t===!0?"success":s==="error"?"error":"default"},cr=()=>u.length===0?!0:u.every(e=>_t[e.id]===!0),ur=e=>!u.some(s=>s.id!==e.id&&s.orderType===e.orderType&&s.controllingArea===e.controllingArea&&s.compCode===e.compCode&&s.internalOrder===e.internalOrder&&e.internalOrder&&e.internalOrder.trim()!==""),pr=async e=>{var v;const{missingFields:t,isValid:s,viewType:l,failedView:E}=re(e);if(!s&&t){if(ye(t),_(E),typeof t=="object"&&!Array.isArray(t)){const H=Object.values(t).flat();if(ee(H),l){const U=_e.findIndex(X=>X.tab===l);U!==-1&&(f(U),T(l),ne([l]))}const V=[];let J=[];Object.entries(t).forEach(([U,X])=>{J.push(...X),X.forEach(Ne=>{var Be;const Pe=typeof e.orderType=="object"?(Be=e.orderType)==null?void 0:Be.code:e.orderType,fe=C[Pe];fe&&fe.mandatoryFields&&Object.values(fe.mandatoryFields).forEach(Sr=>{const Pt=Sr.find(Ar=>Ar.fieldName===Ne);Pt&&V.push(Pt.jsonName)})})}),ot(U=>({...U,[e.id]:V})),Me(J),le(!0)}else ee([]),ne([]),Me(t),le(!0);i(Je({rowId:e.id,status:"error"})),i(Xe({rowId:e.id,isValid:!1}));return}if(ye(null),_(null),ee([]),ne([]),!ur(e)){ie(ce.IO_DUPLICATE_ERROR,"error"),i(Je({rowId:e.id,status:"error"})),i(Xe({rowId:e.id,isValid:!1}));return}i(Zr({rowId:e.id,data:JSON.parse(JSON.stringify(e))}));const M=((v=D.rowsBodyData[e.id])==null?void 0:v.payload)||{};i(Kr({rowId:e.id,data:JSON.parse(JSON.stringify(M))})),ie(Ue.IO_VALIDATION_SUCCESSFUL,"success"),i(Je({rowId:e.id,status:"success"})),i(Xe({rowId:e.id,isValid:!0}))},xt=(e,t)=>{Fe(!0);const s=`/${R}${je.DISPLAY_INTERNAL_ORDER.GET_IO}`;ve(s,"post",E=>{Fe(!1),i(Ee({keyName:"internalOrderOptions",data:(E==null?void 0:E.body)||[]}))},()=>{Fe(!1)},{orderType:e,compCode:t,top:"10",skip:""})},fr=()=>{i(mt(!0))},ht=()=>{i(mt(!1)),Ze("yes"),Ie(""),Ae(""),j(""),i(Ee({keyName:"internalOrderOptions",data:[]}))},gr=async(e,t,s)=>{K(!0);const l=`/${R}${je.DISPLAY_INTERNAL_ORDER.DISPLAY_MASS_DTO}`;ve(l,"POST",v=>{var Ne,Pe;K(!1);const H=((Ne=v==null?void 0:v.body)==null?void 0:Ne[0])||{};let V={...D.rowsBodyData},J=[];const U=Tt();if(!U)return;const X={id:U,included:!0,internalOrder:"",controllingArea:H.CoArea||"",orderType:H.orderType||e,description:"",compCode:H.CompCode||"",validated:"default"};if(((Pe=D==null?void 0:D.rowsHeaderData)==null?void 0:Pe.length)===0)J.push(X);else{J=[...D.rowsHeaderData];const fe=D.rowsHeaderData.findIndex(Be=>Be.id===U);fe!==-1?J[fe]=X:J.push(X)}V[U]={...X,payload:{...H}},i(lt({keyName:"rowsHeaderData",data:J})),i(lt({keyName:"rowsBodyData",data:V})),P(J),Ie(""),Ae(""),j(""),ie(Ue.IO_ROW_ADDED_WITH_REFERENCE,"success")},v=>{K(!1),ie((v==null?void 0:v.error)||ce.IO_REFERENCE_ERROR||"Error adding row with reference","error")},{internalOrder:s})},hr=()=>{const e=Tt(),t={id:e,included:!0,internalOrder:"",controllingArea:"",orderType:"",description:"",compCode:""};P([...u,t]),Object.entries(t).forEach(([s,l])=>{s!=="id"&&i(zt({uniqueId:e,keyName:s,data:l}))})},mr=e=>{const t={...D.rowsBodyData},s=t[e]||{};let l=[];const E=Tt();t[E]={...s,description:"",internalOrder:"",id:E},i(lt({keyName:"rowsBodyData",data:t})),P(l),Ie(""),Ae(""),j(""),ie(Ue.IO_ROW_ADDED_WITH_REFERENCE,"success")},Tr=async()=>{pe==="no"?hr():pe==="yes"&&(be?mr(be):await gr(Ce,Se,N)),ht()},qe=({id:e,field:t,value:s})=>{const l=t==="description"?s.toUpperCase():s,E=u.map(x=>x.id===e?{...x,[t]:l}:x);if(P(E),i(zt({uniqueId:e,keyName:t,data:l})),t==="orderType"){const x=typeof s=="object"?s.code:s;i(Ge(e)),i(He(x)),f(0)}},yr=e=>{var l;const t=e.row,s=typeof t.orderType=="object"?(l=t.orderType)==null?void 0:l.code:t.orderType;i(Ge(t.id)),f(0),i(s?He(s):He(""))},Er=e=>{const t=u.filter(s=>s.id!==e);P(t),i(Qr({rowId:e})),ae===e&&(i(Ge(null)),i(He("")))},Rr=()=>{se(!m)},Or=()=>{B(!b)},Cr=e=>{z(e)},Ir=[{field:"included",headerName:d("Included"),flex:.5,align:"center",headerAlign:"center",editable:!1,renderCell:e=>r(Mr,{checked:e.row.included,onChange:t=>qe({id:e.row.id,field:"included",value:t.target.checked})})},{field:"lineNumber",headerName:d("Line Number"),flex:.6,align:"center",headerAlign:"center",editable:!1,renderCell:e=>{const s=((u==null?void 0:u.findIndex(l=>{var E;return(l==null?void 0:l.id)===((E=e==null?void 0:e.row)==null?void 0:E.id)}))+1)*10;return r("div",{children:s})}},{field:"internalOrder",headerName:d("Internal Order"),flex:.5,align:"center",headerAlign:"center",editable:!1,renderCell:e=>r(Ut,{value:e.row.internalOrder||"",onChange:t=>qe({id:e.row.id,field:"internalOrder",value:t}),size:"small",fullWidth:!0,variant:"outlined",disabled:"true",placeholder:d("Enter Internal Order")})},{field:"controllingArea",headerName:d("Controlling Area"),renderHeader:()=>O("span",{children:[d("Controlling Area"),r("span",{style:{color:"red"},children:"*"})]}),flex:.7,align:"center",editable:!1,headerAlign:"center",renderCell:e=>r(xe,{options:(I==null?void 0:I.controllingArea)||[],value:e.row.controllingArea||"",onChange:t=>qe({id:e.row.id,field:"controllingArea",value:t}),placeholder:d("SELECT CONTROLLING AREA"),minWidth:"90%",listWidth:235,disabled:nt})},{field:"orderType",headerName:d("Order Type"),renderHeader:()=>O("span",{children:[d("Order Type"),r("span",{style:{color:"red"},children:"*"})]}),flex:.7,editable:!1,align:"center",headerAlign:"center",renderCell:e=>r(xe,{options:(I==null?void 0:I.orderType)||[],value:e.row.orderType||"",onChange:t=>qe({id:e.row.id,field:"orderType",value:t}),placeholder:d("Select Order Type"),minWidth:"90%",listWidth:235,disabled:nt})},{field:"description",headerName:d("Description"),renderHeader:()=>O("span",{children:[d("Description"),r("span",{style:{color:"red"},children:"*"})]}),flex:.7,editable:!1,align:"center",headerAlign:"center",renderCell:e=>r(Ut,{value:e.row.description||"",onChange:t=>qe({id:e.row.id,field:"description",value:t.target.value}),size:"small",fullWidth:!0,variant:"outlined",placeholder:d("Enter Description"),disabled:nt})},{field:"compCode",headerName:d("Comp Code"),renderHeader:()=>O("span",{children:[d("Comp Code"),r("span",{style:{color:"red"},children:"*"})]}),flex:.7,editable:!1,align:"center",headerAlign:"center",renderCell:e=>r(xe,{options:(I==null?void 0:I.compCode)||[],value:e.row.compCode||"",onChange:t=>qe({id:e.row.id,field:"compCode",value:t}),placeholder:d("SELECT COMPANY CODE"),minWidth:"90%",listWidth:235,disabled:nt})},{field:"actions",headerName:d("Actions"),flex:.5,align:"center",headerAlign:"center",renderCell:e=>{let t=dr(e==null?void 0:e.row);const s=async l=>{l.stopPropagation(),await pr(e.row)};return O(Kt,{direction:"row",alignItems:"center",sx:{marginLeft:"0.5rem",marginRight:"0.5rem"},spacing:.5,children:[r(it,{title:t==="success"?"Validated Successfully":d(t==="error"?"Validation Failed":"Click to Validate"),children:r(tt,{onClick:s,color:t==="success"?"success":t==="error"?"error":"default",children:t==="error"?r(Fr,{}):r(qr,{})})}),r(it,{title:d("Delete Row"),children:r(tt,{onClick:()=>Er(e.row.id),color:"error",children:r(Pr,{})})})]})}}];return O("div",{children:[r("div",{style:{padding:"0",width:"100%",margin:"0",marginTop:"20px"},children:O(oe,{sx:{position:m?"fixed":"relative",top:m?0:"auto",left:m?0:"auto",right:m?0:"auto",bottom:m?0:"auto",width:m?"100vw":"100%",height:m?"100vh":"auto",zIndex:m?1004:void 0,backgroundColor:m?"white":"transparent",padding:m?"20px":"0",display:"flex",flexDirection:"column",boxShadow:m?"0px 0px 15px rgba(0, 0, 0, 0.2)":"none",transition:"all 0.3s ease",borderRadius:"8px",border:"1px solid #e0e0e0"},children:[O(oe,{sx:{display:"flex",justifyContent:"space-between",alignItems:"center",padding:"8px 16px",backgroundColor:"#f5f5f5",borderRadius:"8px 8px 0 0"},children:[r(We,{variant:"h6",children:d("Internal Order List")}),O(oe,{sx:{display:"flex",alignItems:"center",gap:1},children:[O(me,{variant:"contained",color:"primary",size:"small",onClick:fr,disabled:!cr(),children:["+ ",d("Add")]}),r(it,{title:d(m?"Exit Zoom":"Zoom In"),sx:{zIndex:"1009"},children:r(tt,{onClick:Rr,color:"primary",sx:{backgroundColor:"rgba(0, 0, 0, 0.05)","&:hover":{backgroundColor:"rgba(0, 0, 0, 0.1)"}},children:m?r(Wt,{}):r(jt,{})})})]})]}),r("div",{style:{width:"100%",height:"100%",overflowX:"auto"},children:r("div",{style:{height:"100%"},children:r(Br,{rows:u,columns:Ir,pageSize:50,autoHeight:!1,page:Y,rowsPerPageOptions:[50],onPageChange:e=>Cr(e),onRowClick:yr,pagination:!0,disableSelectionOnClick:!0,getRowClassName:e=>e.id===ae?"selected-row":"",style:{border:"1px solid #ccc",borderRadius:"8px",width:"100%",height:m?"calc(100vh - 150px)":`${Math.min(u.length*50+130,300)}px`,overflow:"auto"},sx:{"& .MuiDataGrid-cell":{padding:"8px"},"& .MuiDataGrid-columnHeaders":{backgroundColor:"#f5f5f5",fontWeight:"bold"},"& .selected-row":{backgroundColor:"rgb(234 233 255)"}}})})})]})}),Ke&&_e.length>0&&O(oe,{sx:{position:b?"fixed":"relative",top:b?0:"auto",left:b?0:"auto",right:b?0:"auto",bottom:b?0:"auto",width:b?"100vw":"100%",height:b?"100vh":"auto",zIndex:b?1300:"auto",backgroundColor:b?"#fff":"transparent",padding:b?"24px":"0",boxSizing:"border-box",overflow:"auto",margin:b?0:"20px 0 0 0",display:"flex",flexDirection:"column",boxShadow:b?"0px 0px 15px rgba(0, 0, 0, 0.2)":"none",borderRadius:"8px",border:"1px solid #e0e0e0"},children:[O(oe,{sx:{display:"flex",justifyContent:"space-between",alignItems:"center",padding:"8px 16px",backgroundColor:"#f5f5f5",borderRadius:"8px 8px 0 0"},children:[r(We,{variant:"h6",children:d("View Details")}),r(it,{title:d(b?"Exit Zoom":"Zoom In"),children:r(tt,{onClick:Or,color:"primary",sx:{backgroundColor:"rgba(0, 0, 0, 0.05)","&:hover":{backgroundColor:"rgba(0, 0, 0, 0.1)"}},children:b?r(Wt,{}):r(jt,{})})})]}),r(Hr,{value:W,onChange:(e,t)=>f(t),variant:"scrollable",scrollButtons:"auto",sx:{borderBottom:1,borderColor:"divider","& .MuiTabs-indicator":{backgroundColor:"#3026B9",height:"3px"}},children:_e.map((e,t)=>r(Gr,{label:e.tab},t))}),r(Ur,{elevation:2,sx:{p:3,borderRadius:4,marginTop:2},children:_e[W]&&ae&&r(Ss,{disabled:!1,basicDataTabDetails:_e[W].data,dropDownData:I,activeViewTab:_e[W].tab,uniqueId:ae,selectedRow:(()=>{var M;const e=u.find(v=>v.id===ae)||{},t=((M=D==null?void 0:D.rowsBodyData)==null?void 0:M[ae])||{},s=t.payload||{},{payload:l,...E}=t;return{...E,...e,...s}})(),module:yt.IO,fieldErrors:ct[ae]||[],missingValidationCards:Oe})})]}),r(Es,{handleSaveAsDraft:De,handleSubmitForReview:De,handleSubmitForApprove:De,handleSendBack:De,handleCorrection:De,handleRejectAndCancel:De,handleValidateAndSyndicate:De,validateAllRows:Nt,isSaveAsDraftEnabled:!0,validateEnabled:!0,filteredButtons:At,moduleName:te.IO,showWfLevels:w,selectedLevel:Re,setSelectedLevel:ue,workFlowLevels:$e}),Qe&&O(Wr,{fullWidth:!0,open:Qe,maxWidth:"lg",onClose:ht,sx:{"&::webkit-scrollbar":{width:"1px"}},children:[r(jr,{sx:{justifyContent:"space-between",alignItems:"center",height:"max-content",padding:".5rem",paddingLeft:"1rem",background:e=>e.palette.primary.light,display:"flex"},children:r(We,{variant:"h6",children:"Add New"})}),O(Xt,{sx:{padding:".5rem 1rem",alignItems:"center",justifyContent:"center",margin:"0px 25px"},children:[O($r,{component:"fieldset",sx:{paddingBottom:"2%"},children:[r(Vr,{component:"legend",sx:{padding:"15px 0px",fontWeight:"600",fontSize:"15px"},children:"How would you like to proceed?"}),O(Yr,{row:!0,"aria-label":"internal-order-reference",name:"internal-order-reference",value:pe,onChange:e=>Ze(e.target.value),children:[r(Vt,{value:"yes",control:r($t,{}),label:"With Reference"}),r(Vt,{value:"no",control:r($t,{}),label:"Without Reference"})]})]}),r(he,{container:!0,spacing:2,children:r(he,{item:!0,xs:12,children:O(he,{container:!0,spacing:2,children:[r(he,{item:!0,xs:3,children:r(xe,{options:(I==null?void 0:I.orderType)||[],value:((Mt=I==null?void 0:I.orderType)==null?void 0:Mt.find(e=>e.code===Ce))||null,onChange:e=>{Ie((e==null?void 0:e.code)||""),j(""),i(Ee({keyName:"internalOrderOptions",data:[]})),e!=null&&e.code&&Se&&xt(e==null?void 0:e.code,Se)},placeholder:"SELECT ORDER TYPE",minWidth:"90%",listWidth:235,disabled:pe==="no"})}),r(he,{item:!0,xs:3,children:r(xe,{options:(I==null?void 0:I.compCode)||[],value:((Ft=I==null?void 0:I.compCode)==null?void 0:Ft.find(e=>e.code===Se))||null,onChange:e=>{Ae((e==null?void 0:e.code)||""),j(""),i(Ee({keyName:"internalOrderOptions",data:[]})),Ce&&(e!=null&&e.code)&&xt(Ce,e==null?void 0:e.code)},placeholder:"SELECT COMPANY CODE",minWidth:"90%",listWidth:235,disabled:pe==="no"})}),r(he,{item:!0,xs:3,children:r(xe,{options:(I==null?void 0:I.internalOrderOptions)||[],value:((qt=I==null?void 0:I.internalOrderOptions)==null?void 0:qt.find(e=>e.code===N))||null,onChange:e=>{j((e==null?void 0:e.code)||"")},isLoading:de,placeholder:"SELECT INTERNAL ORDER",disabled:pe==="no",minWidth:"90%",listWidth:235})})]})})}),u.length>0&&O(Et,{children:[r(oe,{sx:{textAlign:"center",my:2},children:r(We,{variant:"body2",sx:{color:"#666",fontWeight:"bold"},children:"OR"})}),r(xe,{options:u.map((e,t)=>{const s=(t+1)*10;return{code:s.toString(),desc:`${s}`}}),value:be?(()=>{var t,s;const e=u.find(l=>l.id===be);if(e){const E=(u.findIndex(M=>M.id===be)+1)*10,x=typeof(e==null?void 0:e.orderType)=="object"?((t=e==null?void 0:e.orderType)==null?void 0:t.desc)||((s=e==null?void 0:e.orderType)==null?void 0:s.code)||"":(e==null?void 0:e.orderType)||"";return{code:E.toString(),desc:`${E}${x?` (${x})`:""}`}}return null})():null,onChange:e=>{const s=parseInt((e==null?void 0:e.code)||"0")/10-1,l=u[s];pt((l==null?void 0:l.id)||"")},placeholder:"SELECT INTERNAL ORDER LINE NUMBER",minWidth:"90%",listWidth:450})]})]}),O(er,{sx:{display:"flex",justifyContent:"end"},children:[r(me,{sx:{width:"max-content",textTransform:"capitalize"},onClick:ht,variant:"outlined",children:"Cancel"}),r(me,{className:"button_primary--normal",type:"save",onClick:Tr,variant:"contained",children:"Proceed"})]})]}),F&&r(zr,{blurLoading:F,loaderMessage:ke}),r(Qt,{open:we,onClose:()=>le(!1),missingFields:st,t:d}),r(Qt,{open:ze.open,onClose:()=>ut({open:!1,message:"",title:""}),customMessage:ze.message,title:ze.title,t:d})]})},ws=()=>{const{customError:A}=It();return{fetchDataAndDispatch:(i,L,q="get",y={},g=!1)=>{const h=a=>{g||Jr.dispatch(Ee({keyName:L,data:a.body}))},c=a=>{A(a)};ve(i,q.toLowerCase(),h,c,y)}}},Ms=()=>{const{fetchDataAndDispatch:A}=ws();return{fetchAllDropdownIOData:()=>{[{url:`/${R}/api/v1/lookup/scales`,keyName:"Scale"},{url:`/${R}/api/v1/lookup/work-end-dates`,keyName:"DateWorkEnds"},{url:`/${R}/api/v1/lookup/work-start-dates`,keyName:"DateWorkBegins"},{url:`/${R}/api/v1/lookup/gl-accounts`,keyName:"GlAccount"},{url:`/${R}/api/v1/lookup/cost-centers`,keyName:"Costcenter"},{url:`/${R}/api/v1/lookup/overhead-keys`,keyName:"OverheadKey"},{url:`/${R}/api/v1/lookup/costing-sheets`,keyName:"CstgSheet"},{url:`/${R}/api/v1/lookup/currencies`,keyName:"Currency"},{url:`/${R}/api/v1/lookup/locations`,keyName:"LocationPlant"},{url:`/${R}/api/v1/lookup/sales-orders`,keyName:"SalesOrd"},{url:`/${R}/api/v1/lookup/requesting-orders`,keyName:"RequestOrder"},{url:`/${R}/api/v1/lookup/requesting-company-codes`,keyName:"RequCompCode"},{url:`/${R}/api/v1/lookup/requesting-cost-centers`,keyName:"RequestCctr"},{url:`/${R}/api/v1/lookup/wbs-elements`,keyName:"WbsElement"},{url:`/${R}/api/v1/lookup/responsible-cost-centers`,keyName:"Respcctr"},{url:`/${R}/api/v1/lookup/profit-centers`,keyName:"ProfitCtr"},{url:`/${R}/api/v1/lookup/object-classes`,keyName:"Objectclass"},{url:`/${R}/api/v1/lookup/functional-areas`,keyName:"FuncArea"},{url:`/${R}/api/v1/lookup/plants`,keyName:"Plant"},{url:`/${R}/api/v1/lookup/business-areas`,keyName:"BusArea"},{url:`/${R}/api/v1/lookup/responsible-users`,keyName:"ResponsibleUser"},{url:`/${R}/api/v1/lookup/controlling-areas`,keyName:"controllingArea"},{url:`/${R}/api/v1/lookup/order-types`,keyName:"orderType"},{url:`/${R}/api/v1/lookup/company-codes`,keyName:"compCode"}].forEach(({url:L,keyName:q})=>{A(L,q)})}}},Fs=()=>{const[A,d]=n.useState(!1),i=dt(),[L,q]=n.useState(null),y=rt(),{customError:g}=It();return{getDisplayInternalOrderData:n.useCallback((c,a,C,p)=>{d(!0),q(null);const w=i.state,re=Xr(tr.CURRENT_TASK,!0,{});a||p!=null&&p.ATTRIBUTE_2||(re==null||re.ATTRIBUTE_2);const Z=(w==null?void 0:w.childRequestIds)!=="Not Available",u=C?{childRequestId:Z?c:"",parentRequestId:Z?"":c,page:"0",size:"10"}:{childRequestId:Z?c:"",parentRequestId:Z?"":c,page:"0",size:"10"};ve(`/${R}${je.DISPLAY_INTERNAL_ORDER.DISPLAY_DTO}`,"post",P=>{if(d(!1),(P==null?void 0:P.statusCode)!==Jt.STATUS_200){q((P==null?void 0:P.message)||ce.ERROR_GET_DISPLAY_DATA);return}const{payload:m}=es(P);y(ts({requestHeaderData:m.requestHeaderData,rowsBodyData:m.rowsBodyData,childRequestHeaderData:m.childRequestHeaderData,changeLogData:m.changeLogData,ToInternalOrderErrorData:m.ToInternalOrderErrorData})),y(rs(m.rowsBodyData))},P=>{d(!1),q(P),g(ce.ERROR_FETCHING_DATA)},u)},[y,g]),loading:A,error:L,clearError:()=>q(null)}},bo=()=>{var Qe,pe,Ze,Ce,Ie,Se,Ae;const{t:A}=Rt(),d=Ot(),i=dt(),L=rt(),y=new URLSearchParams(i.search.split("?")[1]).get("RequestId"),g=new URLSearchParams(i.search),h=g.get("RequestType"),c=g.get("reqBench"),a=i.state,{getDtCall:C,dtData:p}=Zt(),[w,re]=n.useState(!1),[Z,u]=n.useState([!1]),[P,m]=n.useState(!1),[se,Y]=n.useState(!1),{fetchAllDropdownIOData:z}=Ms(),{getDisplayInternalOrderData:W,loading:f,error:b}=Fs(),B=k(N=>N.internalOrder.savedReqData),F=k(N=>N.internalOrder.tabValue),[K,ke]=n.useState([]);n.useState(!1),n.useState(null);const G=k(N=>N.internalOrder.IOpayloadData),[Re,ue]=n.useState(!1),[$e,Le]=n.useState(!1),{handleUploadMaterial:Ve}=rr((Qe=te)==null?void 0:Qe.IO),[ye,o]=n.useState(""),[_,$]=n.useState(!1),[ee,Ye]=n.useState(!1),[T,Oe]=n.useState(!1),ne=[A("Request Header"),A("Internal Order List"),A("Attachments & Remarks"),A("Preview")];sr({requestId:y,reqBench:c});const we=N=>{L(et(N))},le=()=>{var N,j,de;y&&!c?d((N=at)==null?void 0:N.MY_TASK):c?d((j=at)==null?void 0:j.REQUEST_BENCH):!y&&!c&&d((de=at)==null?void 0:de.INTERNAL_ORDER)},st=()=>{re(!1)},Me=()=>{Ye(!0)},ct=()=>{ue(!0)},ot=N=>{var j;Ve(N,o,$,G,(j=te)==null?void 0:j.IO,h,y,a)},ze=()=>{let N={decisionTableId:null,decisionTableName:"MDG_ATTACHMENTS_LIST_DT",version:"v1",rulePolicy:null,validityDate:null,conditions:[{"MDG_CONDITIONS.MDG_ATTACHMENTS_OBJECT_TYPE":te.IO,"MDG_CONDITIONS.MDG_ATTACHMENTS_SCENARIO":"Create","MDG_CONDITIONS.MDG_ATTACHMENTS_REG_COMP_CODE":1}],systemFilters:null,systemOrders:null,filterString:null};C(N)},ut=()=>{console.log("here download works")};return n.useEffect(()=>{var N;if(p){const de=((N=p==null?void 0:p.result[0])==null?void 0:N.MDG_ATTACHMENTS_ACTION_TYPE)||[];de.length===0?ke([{MDG_ATTACHMENTS_NAME:"Document",MDG_ATTACH_CHNG_ENT_FIELDS:""}]):ke(de)}},[p]),n.useEffect(()=>{ze(),Ts(tr.MODULE,te.IO)},[y,c]),n.useEffect(()=>{z()},[]),n.useEffect(()=>{(async()=>{var j;if(y){await W(y,null,c,null);const de=(j=G==null?void 0:G.requestHeaderData)==null?void 0:j.RequestStatus,Fe=de===ge.DRAFT||de===ge.UPLOAD_FAILED,be=h===Te.CHANGE_WITH_UPLOAD,pt=h===Te.CREATE_WITH_UPLOAD,ie=!(G!=null&&G.rowsBodyData)||Object.keys((G==null?void 0:G.rowsBodyData)||{}).length===0;if((be&&ie||pt)&&Fe){L(et(0)),m(!1),Y(!1);return}L(et(1)),m(!0),Y(!0)}else L(et(0))})()},[y]),n.useEffect(()=>()=>{L(ss())},[]),O(Et,{children:[O(oe,{sx:{padding:2},children:[O(he,{sx:{display:"flex",justifyContent:"space-between",alignItems:"center"},children:[O(We,{variant:"h6",sx:{display:"flex",alignItems:"center",gap:1},children:[r(hs,{sx:{fontSize:"1.5rem"}}),A("Request Header ID"),": ",r("span",{children:(B==null?void 0:B.RequestId)||((pe=G==null?void 0:G.requestHeaderData)==null?void 0:pe.RequestId)})]}),F===1&&O(oe,{sx:{display:"flex",justifyContent:"flex-end",gap:"1rem"},children:[r(me,{variant:"outlined",size:"small",title:"Download Error Report",disabled:!y,onClick:()=>Oe(!0),color:"primary",children:r(ps,{sx:{padding:"2px"}})}),r(me,{variant:"outlined",disabled:!1,size:"small",onClick:Me,title:"Change Log",children:r(xs,{sx:{padding:"2px"}})}),r(me,{variant:"outlined",disabled:!y,size:"small",onClick:ut,title:"Export Excel",children:r(Ns,{sx:{padding:"2px"}})})]})]}),r(tt,{onClick:()=>re(!0),color:"primary",title:A("Back"),sx:{left:"-10px"},children:r(os,{sx:{fontSize:"25px",color:"#000000"}})}),r(as,{nonLinear:!0,activeStep:F,sx:{justifyContent:"center",margin:"25px 14%",marginTop:"-35px"},children:ne.map((N,j)=>r(ns,{completed:Z[j],children:r(is,{onClick:()=>we(j),children:r("span",{style:{fontSize:"15px",fontWeight:"bold"},children:N})})},N))}),ee&&r(Cs,{open:!0,closeModal:()=>Ye(!1),requestId:(B==null?void 0:B.RequestId)||y,requestType:h||((Ze=G==null?void 0:G.requestHeaderData)==null?void 0:Ze.RequestType),module:(Ce=te)==null?void 0:Ce.IO}),r(bs,{dialogState:T,closeReusableDialog:()=>Oe(!1),module:(Ie=te)==null?void 0:Ie.BK}),O(oe,{sx:{padding:"20px",borderRadius:"8px"},children:[F===0&&O(Et,{children:[r(vs,{setIsSecondTabEnabled:m,setIsAttachmentTabEnabled:Y,downloadClicked:Re,setDownloadClicked:ue}),(h===Te.CHANGE_WITH_UPLOAD||h===Te.CREATE_WITH_UPLOAD)&&((a==null?void 0:a.reqStatus)==ge.DRAFT&&!(a!=null&&a.objectNumbers)!=="Not Available"||(a==null?void 0:a.reqStatus)==ge.UPLOAD_FAILED)&&r(Ds,{handleDownload:ct,setEnableDocumentUpload:Le,enableDocumentUpload:$e,handleUploadMaterial:ot})]}),F===1&&r(Ls,{requestStatus:a!=null&&a.reqStatus?a==null?void 0:a.reqStatus:ge.ENABLE_FOR_FIRST_TIME,setIsAttachmentTabEnabled:Y,downloadClicked:Re,setDownloadClicked:ue}),F===2&&r(Rs,{requestStatus:ge.ENABLE_FOR_FIRST_TIME,attachmentsData:K,requestIdHeader:B==null?void 0:B.RequestId,pcNumber:B==null?void 0:B.RequestId,module:(Se=te)==null?void 0:Se.IO,artifactName:ls.IO}),F===3&&r(oe,{sx:{width:"100%",overflow:"auto"},children:r(Os,{requestStatus:ge.ENABLE_FOR_FIRST_TIME,module:(Ae=te)==null?void 0:Ae.IO,payloadData:G,payloadForDownloadExcel:G})})]})]}),w&&O(ds,{isOpen:w,titleIcon:r(gs,{sx:{color:fs.secondary.amber,fontSize:"20px"}}),Title:A("Warning"),handleClose:st,children:[r(Xt,{sx:{mt:2},children:A("Are you sure you want to leave this page?")}),O(er,{children:[r(me,{variant:"outlined",size:"small",sx:cs,onClick:st,children:A("No")}),r(me,{variant:"contained",size:"small",sx:us,onClick:le,children:A("Yes")})]})]})]})};export{bo as default};
