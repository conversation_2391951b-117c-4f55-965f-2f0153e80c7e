import{aD as I,eq as Ne,er as ge,t as q,j as d,dO as xe,dQ as Se,s as ke,u as Ie,n as C,el as be,a as Ee,r as $,en as De,es as Te,em as M,aZ as Re,c as D,Z as N,d as H,eo as Ae,T as ye,b3 as ve,cz as P,aa as we,F as Le,O as Ce,dX as Oe}from"./index-f7d9b065.js";import{u as Me}from"./useChangeLogUpdate-1ba6b2dd.js";import{S as Pe}from"./AdapterDayjs-2a9281df.js";function qe({details:e,newValue:m,materialID:b,storedRows:T,dispatch:R}){if((e==null?void 0:e.visibility)===I.MANDATORY&&!m){R(Ne(!0));const g=T.map(y=>y.id===b?{...y,validated:!1}:y);R(ge(g))}}const he=2,ue=q.createContext({}),Ye=q.forwardRef((e,m)=>{const b=q.useContext(ue);return d("div",{ref:m,...e,...b})}),_e=q.forwardRef(function(m,b){const{children:T,...R}=m,g=[];T.forEach(x=>{g.push(x)});const y=g.length,A=Se.HEIGHT,Y=()=>y>8?8*A:g.length*A;return d("div",{ref:b,children:d(ue.Provider,{value:R,children:d(xe,{itemData:g,height:Y()+2*he,width:"100%",outerElementType:Ye,innerElementType:"ul",itemSize:A,overscanCount:5,itemCount:y,children:({data:x,index:h,style:a})=>{const _=x[h],l={...a,top:a.top+he};return d("li",{style:{...l,listStyle:"none"},children:_})}})})})});function We(e){var U,W,z,j,B,F,Q,G,Z,X,J,K,V,s,r,o,p,ee,te,ie,ae,le,de,ce,ne,me;const m=ke(),{updateChangeLog:b}=Me(),T=Ie(),g=new URLSearchParams(T.search).get("RequestId"),y=C(i=>i.payload.payloadData),A=be.some(i=>T.pathname.includes(i)),Y=C(i=>i.request.materialRows),{t:x}=Ee(),h=C(i=>i.payload||{}),a=C(i=>{var t;return((t=i.materialDropDownData)==null?void 0:t.dropDown)||{}}),_=C(i=>{var t;return((t=i.payload)==null?void 0:t.errorFields)||[]}),[l,E]=$.useState(null),n=((j=(z=(W=(U=h==null?void 0:h[e==null?void 0:e.materialID])==null?void 0:U.payloadData)==null?void 0:W[e==null?void 0:e.viewName])==null?void 0:z[e==null?void 0:e.plantData])==null?void 0:j[e==null?void 0:e.keyName])||((B=h==null?void 0:h.payloadData)==null?void 0:B[e==null?void 0:e.keyName])||((Q=(F=h==null?void 0:h.payloadData)==null?void 0:F.data)==null?void 0:Q[e==null?void 0:e.keyName])||(((G=e==null?void 0:e.details)==null?void 0:G.fieldPriority)==="ApplyDef"||e!=null&&e.isRequestHeader?(Z=e==null?void 0:e.details)==null?void 0:Z.value:null);$.useEffect(()=>{var i,t,u;(((i=e==null?void 0:e.details)==null?void 0:i.visibility)===I.MANDATORY||((t=e==null?void 0:e.details)==null?void 0:t.visibility)==="Required")&&m(De((e==null?void 0:e.keyName)||"")),((u=e==null?void 0:e.details)==null?void 0:u.visibility)===I.DISPLAY&&m(Te(e==null?void 0:e.keyName))},[m,(X=e==null?void 0:e.details)==null?void 0:X.visibility,e==null?void 0:e.keyName]),$.useEffect(()=>{var i,t,u,S,v,w;if(n!=null&&n!=="")if(n!=null&&n.code)E(n),M({materialID:(e==null?void 0:e.materialID)||"",keyName:(e==null?void 0:e.keyName)||"",data:n,viewID:e==null?void 0:e.viewName,itemID:e==null?void 0:e.plantData});else if(a!=null&&a[e==null?void 0:e.keyName]||(i=a==null?void 0:a[e==null?void 0:e.keyName])!=null&&i[e==null?void 0:e.plantData]){if(!Array.isArray(a==null?void 0:a[e==null?void 0:e.keyName])&&!Array.isArray((t=a==null?void 0:a[e==null?void 0:e.keyName])==null?void 0:t[e==null?void 0:e.plantData])){E(null);return}const c=(u=a[e==null?void 0:e.keyName])!=null&&u.length?(S=a[e==null?void 0:e.keyName])==null?void 0:S.find(f=>{var k,L;return((k=f==null?void 0:f.code)==null?void 0:k.trim())===((L=n==null?void 0:n.toString())==null?void 0:L.trim())}):(w=(v=a[e==null?void 0:e.keyName])==null?void 0:v[e==null?void 0:e.plantData])==null?void 0:w.find(f=>{var k,L;return((k=f==null?void 0:f.code)==null?void 0:k.trim())===((L=n==null?void 0:n.toString())==null?void 0:L.trim())});c?(E({code:c==null?void 0:c.code,desc:c==null?void 0:c.desc}),m(M({materialID:(e==null?void 0:e.materialID)||"",keyName:(e==null?void 0:e.keyName)||"",data:{code:c==null?void 0:c.code,desc:c==null?void 0:c.desc},viewID:e==null?void 0:e.viewName,itemID:e==null?void 0:e.plantData}))):(E(null),m(M({materialID:(e==null?void 0:e.materialID)||"",keyName:(e==null?void 0:e.keyName)||"",data:null,viewID:e==null?void 0:e.viewName,itemID:e==null?void 0:e.plantData})))}else E(null);else E(null)},[n]);const fe=(i,t)=>{var u,S;E(t),m(M({materialID:(e==null?void 0:e.materialID)||"",keyName:(e==null?void 0:e.keyName)||"",data:t??null,viewID:e==null?void 0:e.viewName,itemID:e==null?void 0:e.plantData})),g&&!Oe.includes(y==null?void 0:y.RequestStatus)&&b({materialID:e==null?void 0:e.selectedMaterialNumber,viewName:e==null?void 0:e.viewName,plantData:e==null?void 0:e.plantData,fieldName:(u=e==null?void 0:e.details)==null?void 0:u.fieldName,jsonName:(S=e==null?void 0:e.details)==null?void 0:S.jsonName,currentValue:`${t==null?void 0:t.code}-${(t==null?void 0:t.desc)??""}`,requestId:y==null?void 0:y.RequestId,childRequestId:g}),qe({details:e==null?void 0:e.details,newValue:t,materialID:e==null?void 0:e.materialID,storedRows:Y,dispatch:m})},O=(J=e==null?void 0:e.details)==null?void 0:J.jsonName;return d(Ce,{item:!0,md:2,sx:{marginBottom:"12px !important"},children:((K=e==null?void 0:e.details)==null?void 0:K.visibility)==="Hidden"?null:d(Re,{children:A?D("div",{style:{padding:"16px",backgroundColor:N.primary.white,borderRadius:"8px",boxShadow:"0 2px 8px rgba(0, 0, 0, 0.1)",transition:"all 0.3s ease"},children:[D(H,{variant:"body1",style:{whiteSpace:"normal",wordWrap:"break-word",width:"100%",fontWeight:600,fontSize:"12px",color:N.secondary.grey,marginBottom:"4px",lineHeight:"1.3"},title:x((V=e==null?void 0:e.details)==null?void 0:V.fieldName),children:[x((s=e==null?void 0:e.details)==null?void 0:s.fieldName)||"Field Name",(((r=e==null?void 0:e.details)==null?void 0:r.visibility)===I.REQUIRED||((o=e==null?void 0:e.details)==null?void 0:o.visibility)===Ae.MANDATORY)&&d("span",{style:{color:N.error.darkRed,marginLeft:"5px",fontSize:"1.1rem"},children:"*"})]}),d("div",{style:{fontSize:"0.8rem",color:N.black.dark,marginTop:"4px",overflow:"hidden",textOverflow:"ellipsis",whiteSpace:"nowrap",width:"100%",minHeight:"25px",cursor:"pointer",lineHeight:"1.2"},children:l!=null&&l.code||l!=null&&l.desc?d(ye,{title:l!=null&&l.code?`${l==null?void 0:l.code} - ${(l==null?void 0:l.desc)||""}`:"--",arrow:!0,children:D("span",{children:[d("strong",{style:{fontWeight:600,color:N.black.dark,marginRight:"6px",letterSpacing:"0.5px",wordSpacing:"1px"},children:l==null?void 0:l.code}),(l==null?void 0:l.desc)&&D("span",{style:{fontWeight:500,color:N.black.dark,letterSpacing:"0.5px",wordSpacing:"1px"},children:["- ",l==null?void 0:l.desc]})]})}):d(Pe,{fallback:"--"})})]}):D(Le,{children:[D(H,{variant:"body2",sx:{whiteSpace:"nowrap",overflow:`${e.width?"":"hidden"}`,textOverflow:"ellipsis",maxWidth:"100%"},title:x((p=e==null?void 0:e.details)==null?void 0:p.fieldName),children:[x((ee=e==null?void 0:e.details)==null?void 0:ee.fieldName)||"Field Name",(((te=e==null?void 0:e.details)==null?void 0:te.visibility)==="Required"||((ie=e==null?void 0:e.details)==null?void 0:ie.visibility)===I.MANDATORY)&&d("span",{style:{color:N.error.dark},children:"*"})]}),d(ye,{title:((ae=e.details)==null?void 0:ae.fieldTooltip)||"",arrow:!0,placement:"top",children:d(ve,{sx:{height:"31px","& .MuiAutocomplete-listbox":{padding:0,"& .MuiAutocomplete-option":{paddingLeft:"16px",paddingTop:"4px",paddingBottom:"4px",justifyContent:"flex-start"}},"& .MuiAutocomplete-option":{display:"flex",alignItems:"center",minHeight:"36px"},"& .MuiInputBase-root.Mui-disabled":{"& > input":{WebkitTextFillColor:N.black.dark,color:N.black.dark},backgroundColor:N.hover.light},width:`${e.width?e.width:"auto"}`},fullWidth:!0,disabled:(e==null?void 0:e.disabled)||((le=e.details)==null?void 0:le.visibility)===I.DISPLAY,size:"small",value:l,onChange:fe,options:(de=a==null?void 0:a[e==null?void 0:e.keyName])!=null&&de.length?a==null?void 0:a[e==null?void 0:e.keyName]:((ce=a==null?void 0:a[e==null?void 0:e.keyName])==null?void 0:ce[e==null?void 0:e.plantData])||[],required:((ne=e==null?void 0:e.details)==null?void 0:ne.visibility)===I.MANDATORY||((me=e==null?void 0:e.details)==null?void 0:me.visibility)==="Required",ListboxComponent:_e,getOptionLabel:i=>i!=null&&i.desc?`${(i==null?void 0:i.code)||""} - ${(i==null?void 0:i.desc)||""}`:`${(i==null?void 0:i.code)||""}`,renderOption:(i,t)=>d(H,{...i,component:"li",style:{fontSize:12,padding:"8px 16px",width:"100%",cursor:"pointer",display:"flex",alignItems:"start"},title:`${O===P.REQUEST_TYPE?"":t==null?void 0:t.code}${t!=null&&t.desc&&O!==P.REQUEST_TYPE?` - ${t==null?void 0:t.desc}`:`${t==null?void 0:t.desc}`}`,children:D("span",{style:{whiteSpace:"nowrap",overflow:"hidden",textOverflow:"ellipsis"},children:[d("strong",{children:t==null?void 0:t.code}),t!=null&&t.desc&&O!==P.REQUEST_TYPE?` - ${t==null?void 0:t.desc}`:""]})}),renderInput:i=>{var t,u,S,v,w,c,f,k;return d(we,{...i,variant:"outlined",placeholder:e!=null&&e.disabled||((t=e.details)==null?void 0:t.visibility)===I.DISPLAY?"":x(`SELECT ${((S=(u=e==null?void 0:e.details)==null?void 0:u.fieldName)==null?void 0:S.toUpperCase())||""}`),error:_.includes((e==null?void 0:e.keyName)||"")||((w=e.missingFields)==null?void 0:w.includes(((v=e==null?void 0:e.details)==null?void 0:v.fieldName)||"")),InputProps:{...i.InputProps},inputProps:{...i.inputProps,value:O===P.REQUEST_TYPE?(f=(c=i==null?void 0:i.inputProps)==null?void 0:c.value)==null?void 0:f.split(" - ")[0]:(k=i==null?void 0:i.inputProps)==null?void 0:k.value}})}})})]})})})}export{We as A,qe as h};
