import{j as o,c as u,Z as h,aP as vs,n as re,o as Je,s as xs,g as Ps,r as i,P as Ss,t as he,a as $s,v as Ls,w as qe,x as G,y as Rs,z as A,C as $,E as bs,G as ws,H as ks,I as Bs,J as Ke,K as Us,N as Ys,S as $t,O as p,Q as Os,d as z,U as zs,V as Hs,W as Ws,i as Vs,X as B,Y as y,$ as te,a0 as Xs,a1 as Js,a2 as qs,a3 as Ks,a4 as Qs,a5 as Zs,a6 as Lt,a7 as Rt,a8 as Qe,a9 as Fs,F as _e,aa as Ze,ab as bt,ac as js,ad as ms,ae as el,af as tl,ag as Fe,ah as al,ai as sl,aj as ll,ak as nl,al as ol,am as il,an as ce,ao as rl,ap as cl,aq as wt,ar as kt,as as Bt,at as ae,k as dl,A as ul,aQ as D,av as Ml,aw as fl,ax as pl,ay as gl,az as Dl,aA as hl,aB as _l,aC as Cl,aD as Al,aE as El,aF as Nl,aG as yl,aH as Il,aI as Tl,aJ as Ce,aK as je,aL as Ut,aM as Yt,T as Ot,B as Ae,aN as Gl}from"./index-f7d9b065.js";import{u as vl,s as xl}from"./useMaterialFetchDropdownAndDispatch-2f5f3f49.js";import{A as Pl}from"./AttachmentUploadDialog-43cc9099.js";import{R as Sl}from"./ReusablePresetFilter-36c3fb2e.js";import{u as $l}from"./useArticleFieldConfig-f9acb4d0.js";import{L as K}from"./LargeDropdown-b2630df6.js";import{S as Ll}from"./SingleSelectDropdown-aee403d4.js";import{E as Rl}from"./ExportExcelSearch-8c17fa97.js";import"./CloudUpload-0ba6431e.js";import"./Delete-5278579a.js";import"./utilityImages-067c3dc2.js";import"./FeedOutlined-41109ec9.js";import"./FilterChangeDropdown-22e24089.js";import"./useMaterialFieldConfig-3bde8f21.js";const bl=({percentage:T,id:U})=>{const Y=d=>{var C,x,R,b,g;return d===100?(C=h)==null?void 0:C.progressColors.complete:d>=75?(x=h)==null?void 0:x.progressColors.high:d>=50?(R=h)==null?void 0:R.progressColors.medium:d>=25?(b=h)==null?void 0:b.progressColors.low:(g=h)==null?void 0:g.progressColors.minimal},de=Math.round(T/100*16),L=30,j=30,v=20,H=15,W=2*Math.PI/16,Ne=d=>{const C=d*W-Math.PI/2,x=(d+1)*W-Math.PI/2,R=L+H*Math.cos(C),b=j+H*Math.sin(C),g=L+v*Math.cos(C),me=j+v*Math.sin(C),ye=L+v*Math.cos(x),et=j+v*Math.sin(x),Ie=L+H*Math.cos(x),ue=j+H*Math.sin(x);return`M ${R} ${b} L ${g} ${me} A ${v} ${v} 0 0 1 ${ye} ${et} L ${Ie} ${ue} A ${H} ${H} 0 0 0 ${R} ${b} Z`},Q=Y(T);return o("div",{style:{display:"flex",alignItems:"center",justifyContent:"center",width:"100%",height:"100%",padding:"8px"},children:u("div",{style:{position:"relative",width:"60px",height:"60px",display:"flex",alignItems:"center",justifyContent:"center"},children:[u("svg",{width:"60",height:"60",viewBox:"0 0 60 60",style:{position:"absolute",top:0,left:0},children:[o("defs",{children:u("linearGradient",{id:`segmentGradient-${U}`,x1:"0%",y1:"0%",x2:"100%",y2:"100%",children:[o("stop",{offset:"0%",stopColor:Q.start}),o("stop",{offset:"100%",stopColor:Q.end})]})}),Array.from({length:16},(d,C)=>{var x,R,b;return o("path",{d:Ne(C),fill:C<de?`url(#segmentGradient-${U})`:(x=h)==null?void 0:x.progressColors.inactive.fill,stroke:(R=h)==null?void 0:R.progressColors.inactive.stroke,strokeWidth:"0.5",style:{transition:`fill 0.6s cubic-bezier(0.4, 0, 0.2, 1) ${C*.05}s`,filter:C<de?`drop-shadow(0 1px 2px ${(b=h)==null?void 0:b.progressColors.shadow.dropShadow})`:"none"}},C)})]}),o("div",{style:{position:"absolute",top:"50%",left:"50%",transform:"translate(-50%, -50%)",display:"flex",flexDirection:"column",alignItems:"center",justifyContent:"center",zIndex:1},children:u("div",{style:{fontSize:"12px",fontWeight:"700",color:Q.end,letterSpacing:"-0.025em"},children:[T,"%"]})}),o("div",{style:{position:"absolute",top:"50%",left:"50%",transform:"translate(-50%, -50%)",width:"32px",height:"32px",borderRadius:"50%",zIndex:0}})]})})},wl=ae(dl,{target:"e16sjfve5"})(({theme:T})=>({marginTop:"0px !important",border:`1px solid ${h.primary.border}`,borderRadius:"8px",boxShadow:"0 2px 4px rgba(0,0,0,0.05)","&:not(:last-child)":{borderBottom:0},"&:before":{display:"none"}}),""),kl=ae(ul,{target:"e16sjfve4"})(({theme:T})=>({minHeight:"2rem !important",margin:"0px !important",backgroundColor:h.primary.ultraLight,borderRadius:"8px 8px 0 0",transition:"all 0.2s ease-in-out","&:hover":{backgroundColor:`${h.primary.light}20`}}),""),Bl=ae(p,{target:"e16sjfve3"})({name:"seull4",styles:"padding:0.75rem;gap:0.5rem"}),Ul=ae(p,{target:"e16sjfve2"})({name:"1ikq1ll",styles:"display:flex;justify-content:flex-end;padding-right:0.75rem;padding-bottom:0.75rem;padding-top:0rem;gap:0.5rem"}),zt=ae(ce,{target:"e16sjfve1"})({name:"1x9mjbh",styles:"border-radius:4px;padding:4px 12px;text-transform:none;font-size:0.875rem"}),I=ae(z,{target:"e16sjfve0"})({fontSize:"0.75rem",color:h.primary.dark,marginBottom:"0.25rem",fontWeight:500},""),Dn=()=>{var xt,Pt,St;const{customError:T}=vs(),U=re(t=>t.materialDropDownData.dropDown),Y=re(t=>t.request.salesOrgDTData),{fetchOrgData:Ee}=$l(),{getDtCall:de,dtData:L}=Je(),{getDtCall:j,dtData:v}=Je(),{getDtCall:H,dtData:W}=Je(),{fetchDataAndDispatch:Ne}=vl();let Q=re(t=>{var s;return(s=t.userManagement.entitiesAndActivities)==null?void 0:s.Article});const d=xs(),C=Ps(),x=48,R=8,b={PaperProps:{style:{maxHeight:x*4.5+R,width:250}}},g=re(t=>t.appSettings),[me,ye]=i.useState(!0),[et,Ie]=i.useState(!1),[ue,w]=i.useState(!1),[Yl,Te]=i.useState(!1),[tt,at]=i.useState(!1),[m,se]=i.useState({}),[Ge,ve]=i.useState([]),[xe,st]=i.useState([]),[Ol,Ht]=i.useState(""),[lt,Wt]=i.useState(null),[Vt,Xt]=i.useState([]),[Pe,Se]=i.useState([]),[E,nt]=i.useState(""),[$e,ot]=i.useState([]),[P,Le]=i.useState([]),[Re,be]=i.useState([]),[Jt,zl]=i.useState(!1),[N,we]=i.useState([]),[ke,ee]=i.useState([]),[it,k]=i.useState(!1),[qt,Kt]=i.useState(null),[S,Be]=i.useState([]),[Qt,Ue]=i.useState([...S]),[V,Zt]=i.useState((xt=Ss)==null?void 0:xt.TOP_SKIP);he.useState(""),i.useState(!1),i.useState("");const[Hl,Ft]=i.useState(!1),[Wl,jt]=i.useState(!0),[mt,ea]=i.useState([]);i.useState([]);const[ta,rt]=i.useState([]),[Vl,le]=i.useState(!0),[Xl,aa]=i.useState([]),[Jl,sa]=i.useState([]),[Me,Ye]=i.useState([]),[ql,la]=i.useState([]),[Kl,na]=i.useState({}),[oa,ia]=i.useState([]),[ra,ca]=i.useState([]),[ne,da]=i.useState([]),[ua,Ma]=i.useState([]),[Oe,fa]=i.useState([]),[pa,ga]=i.useState("success"),[Da,ct]=i.useState(!1),[ha,ze]=i.useState(!1),[dt,ut]=i.useState(""),[Ql,_a]=i.useState(!1),[Ca,Mt]=i.useState(!1),fe=he.useRef(null),[Aa,Ea]=i.useState(0),[Na,ft]=i.useState(!1),pe=he.useRef(null),[ya,Ia]=i.useState(0),Ta=["Create Multiple","Upload Template","Download Template"],Ga=["Change Multiple","Upload Template","Download Template"],[va,Zl]=i.useState(""),[xa,He]=i.useState(!1),[Pa,We]=i.useState(!1),{t:M}=$s(),[Ve,Sa]=i.useState(),$a=t=>{He(!1)},La=()=>{ct(!0)},Ra=()=>{ct(!1)},e=re(t=>t.commonFilter.ArticleMaster),ba=()=>{hs(!1)},wa=(t,s)=>{s!==0&&(Ea(s),Mt(!1),s===1?Fa():s===2&&Xa())},ka=t=>{fe.current&&fe.current.contains(t.target)||Mt(!1)},Ba=t=>{const s=new FormData;[...t].forEach(a=>s.append("files",a)),s.append("dtName","MDG_MAT_FIELD_CONFIG"),s.append("version","v1");var n=`/${D}/massAction/getAllMaterialsFromExcel`;$(n,"postformdata",a=>{var f;d(Tl((f=a==null?void 0:a.body)==null?void 0:f.tableData)),C("/masterDataCockpit/materialMaster/massMaterialTable",{state:dt}),a.statusCode===200&&(X("Create"),O(`${t.name} has been Uploaded Succesfully`),J("success"),jt(!1),_a(!0),Ft(!0),C("/masterDataCockpit/materialMaster/massMaterialTable",{state:dt})),ba()},a=>{T(a)},s)},Ua=(t,s)=>{s!==0&&(Ia(s),ft(!1),s===1?ja():s===2&&Va())},Ya=t=>{pe.current&&pe.current.contains(t.target)||ft(!1)},pt=(t="")=>{var r;k(!0);let s={materialNo:t,salesOrg:((r=Y==null?void 0:Y.uniqueSalesOrgList)==null?void 0:r.map(a=>a.code).join("$^$"))||"",top:200,skip:0};const n=a=>{k(!1),Xt(a.body)},l=a=>{T(a)};$(`/${D}/data/getSearchParamsMaterialNo`,"post",n,l,s)},Oa=t=>{const s=t.target.value;if(Ht(s),lt&&clearTimeout(lt),s.length>=4){const n=setTimeout(()=>{pt(s)},500);Wt(n)}},za=t=>{if(t.target.value!==null){var s=t.target.value;let n={...e,description:s};d(G({module:"ArticleMaster",filterData:n}))}},Ha=(t,s)=>{if(t.target.value!==null){var n=t.target.value;let l={...e,createdBy:n};d(G({module:"ArticleMaster",filterData:l}))}},Wa=(t,s)=>{if(t.target.value!==null){var n=t.target.value;let l={...e,oldMaterialNumber:n};d(G({module:"ArticleMaster",filterData:l}))}},Va=()=>{var s={materialNos:va.map(r=>r.materialNumber??""),dtName:"MDG_MAT_FIELD_CONFIG",version:"v2"};let n=r=>{ye(!1);const a=URL.createObjectURL(r),f=document.createElement("a");f.href=a,f.setAttribute("download","Material_Mass Change.xls"),document.body.appendChild(f),f.click(),document.body.removeChild(f),URL.revokeObjectURL(a),q(),X("Success"),O("Material_Mass Change.xls has been downloaded successfully"),J("success")},l=r=>{r.message&&(q(),X("Error"),O(`${r.message}`),J("danger"))};$(`/${D}/excel/downloadExcelWithData`,"postandgetblob",n,l,s)},Xa=async()=>{const t=new URLSearchParams({dtName:"MDG_MAT_FIELD_CONFIG",version:"v2"});let s=l=>{const r=URL.createObjectURL(l),a=document.createElement("a");a.href=r,a.setAttribute("download",`${name}`),document.body.appendChild(a),a.click(),document.body.removeChild(a),URL.revokeObjectURL(r),q(),X("Success"),O("Material_Mass Create.xls has been downloaded successfully"),J("success")},n=l=>{l.message&&(q(),X("Error"),O(`${l.message}`),J("danger"))};$(`/${D}/excel/downloadExcel?${t.toString()}`,"getblobfile",s,n)};let gt={"Basic Material":`/${D}/data/getBasicMatl`,"Product Hierarchy":`/${D}/data/getProdHier`,"Purchasing Group":`/${D}/data/getPurGroup`,"Lab/Office":`/${D}/data/getDsnOffice`,"Transportation Group":`/${D}/data/getTransGrp`,"Material Group 5":`/${D}/data/getMatlGrp5`,"Profit Center":`/${D}/data/getProfitCenterBasedOnPlant`,"MRP Controller":`/${D}/data/getMRPController`,"Warehouse No.":`/${D}/data/getWareHouseNo`,"MRP Profile":`/${D}/data/getMRPProfile`};const Dt=(t,s)=>{let n={plant:(P==null?void 0:P.map(a=>a.code).join("$^$"))||""};Te(!0);const l=a=>{Te(!1);const f=a.body;da(_=>({..._,[s]:f}))},r=a=>{Te(!1)};s==="Profit Center"?$(t,"post",l,r,n):$(t,"get",l,r)},Ja=t=>{var s,n;((s=m[t])==null?void 0:s.length)===((n=ne[t])==null?void 0:n.length)?se(l=>({...l,[t]:[]})):se(l=>({...l,[t]:ne[t]??[]}))},qa=t=>{const s=t.target.value;Ye(s),la([]),s.forEach(async n=>{const l=gt[n];Dt(l,n)})},Ka=()=>{k(!0);const t=n=>{k(!1),ia(n.body)},s=n=>{k(!1)};$(`/${D}/data/getMatlType`,"get",t,s)},Qa=()=>{k(!0);const t=n=>{ca(n.body),k(!1)},s=n=>{k(!1),T(n)};$(`/${D}/data/getMatlGroup`,"get",t,s)},Za=t=>{k(!0);let s={salesOrg:t?t.map(r=>r==null?void 0:r.code).join("$^$"):""};const n=r=>{k(!1),rt(r.body)},l=r=>{T(r)};$(`/${D}/data/getDistrChan`,"post",n,l,s)},Fa=()=>{ze(!0),ut("Create")},ja=()=>{ze(!0),ut("Change")};i.useEffect(()=>{tt&&(oe(),at(!1))},[tt]),i.useEffect(()=>{[{url:`/${D}/data/getCSalStatus`,keyName:"CSalStatus"}].forEach(({url:s,keyName:n})=>{Ne(s,n)}),d(Ls())},[]);const ma=()=>{d(Ml()),d(fl()),d(pl()),d(gl()),d(Dl()),d(hl([])),d(_l({})),d(Cl({}))};i.useEffect(()=>(Qa(),Ka(),ma(),Dt([gt]),()=>{d(qe({module:"ArticleMaster",days:7}))}),[]),i.useEffect(()=>Ue([...S]),[S]),i.useEffect(()=>{var t=xe.map(n=>n==null?void 0:n.code).join("$^$");let s={...e,type:t};d(G({module:"ArticleMaster",filterData:s}))},[xe]),i.useEffect(()=>{var t=ke.map(n=>n==null?void 0:n.code).join("$^$");let s={...e,distributionChannel:t};d(G({module:"ArticleMaster",filterData:s}))},[ke]),i.useEffect(()=>{var t=Ge.map(n=>n==null?void 0:n.code).join("$^$");let s={...e,PurStatus:t};d(G({module:"ArticleMaster",filterData:s}))},[Ge]),i.useEffect(()=>{var t=Pe.map(n=>n==null?void 0:n.code).join("$^$");let s={...e,number:t};d(G({module:"ArticleMaster",filterData:s}))},[Pe]),i.useEffect(()=>{var t=Re.map(n=>n==null?void 0:n.code).join("$^$");let s={...e,division:t};d(G({module:"ArticleMaster",filterData:s}))},[Re]),i.useEffect(()=>{Object.keys(m).forEach(t=>{var l;const s=(l=m[t])==null?void 0:l.map(r=>r==null?void 0:r.code).join("$^$");let n={...e,[t]:s};d(G({module:"ArticleMaster",filterData:n}))})},[m]),i.useEffect(()=>{var t=N.map(n=>n==null?void 0:n.code).join("$^$");let s={...e,salesOrg:t};d(G({module:"ArticleMaster",filterData:s})),es(),pt()},[N]);const es=()=>{const t=Rs(N,Y);ea(t)};i.useEffect(()=>{var t=$e.map(n=>n==null?void 0:n.code).join("$^$");let s={...e,group:t};d(G({module:"ArticleMaster",filterData:s}))},[$e]),i.useEffect(()=>{var t=P.map(n=>n==null?void 0:n.code).join("$^$");let s={...e,plant:t};d(G({module:"ArticleMaster",filterData:s}))},[P]);const ts=t=>{if(!t){De(Ct),Z(0),Ue([...S]);return}const s=S.filter(n=>{var a;let l=!1,r=Object.keys(n);for(let f=0;f<r.length&&(l=n[r[f]]?(n==null?void 0:n[r==null?void 0:r[f]])&&((a=n==null?void 0:n[r==null?void 0:r[f]].toString().toLowerCase())==null?void 0:a.indexOf(t==null?void 0:t.toLowerCase()))!=-1:!1,!l);f++);return l});Ue([...s]),De(s==null?void 0:s.length)},ht=new Date,ge=new Date;ge.setDate(ge.getDate()-7),i.useState([ge,ht]),i.useState([ge,ht]);const as=t=>{var s=t;d(G({module:"ArticleMaster",filterData:{...e,createdOn:s}}))},oe=()=>{Z(0),w(!0),We(!1);let t={fromDate:A(e==null?void 0:e.createdOn[0]).format("YYYYMMDD")??"",toDate:A(e==null?void 0:e.createdOn[1]).format("YYYYMMDD")??"",createdBy:(e==null?void 0:e.createdBy)??"",materialDesc:(e==null?void 0:e.description)??"",plant:(e==null?void 0:e.plant)??"",materialGroup:(e==null?void 0:e.group)??"",materialType:(e==null?void 0:e.type)??"",changedBy:(e==null?void 0:e.changedBy)??"",taskId:(e==null?void 0:e.taskId)??"",status:(e==null?void 0:e.status)??"",salesOrg:(e==null?void 0:e.salesOrg)??"",division:(e==null?void 0:e.division)??"",distributionChannel:(e==null?void 0:e.distributionChannel)??"",storageLocation:(e==null?void 0:e.storageLocation)??"",ProdHier:(e==null?void 0:e["Product Hierarchy"])??"",BasicMatl:(e==null?void 0:e["Basic Material"])??"",ProfitCtr:(e==null?void 0:e["Profit Center"])??"",PurGroup:(e==null?void 0:e["Purchasing Group"])??"",MatlGrp5:(e==null?void 0:e["Material Group 5"])??"",MrpCtrler:(e==null?void 0:e["MRP Controller"])??"",warehouseNo:(e==null?void 0:e["Warehouse No"])??"",Mrpprofile:(e==null?void 0:e["MRP Profile"])??"",oldMaterialNo:(e==null?void 0:e.oldMaterialNumber)??"",number:(e==null?void 0:e.number)??"",PurStatus:(e==null?void 0:e.PurStatus)??"",top:V,skip:0,labOffice:(e==null?void 0:e["Lab/Office"])??"",transportationGroup:(e==null?void 0:e["Transportation Group"])??"",batchManagement:(e==null?void 0:e.batchManagement)??""};const s=l=>{var _;if((l==null?void 0:l.statusCode)===Ce.STATUS_200){var r=[];for(let c=0;c<((_=l==null?void 0:l.body)==null?void 0:_.length);c++){var a=l==null?void 0:l.body[c],f={id:je(),Number:a.Number,materialType:a.Materialtype!==""?`${a.Materialtype} - ${a.MaterialTypeDesc}`:"Not Available",materialDesc:a.MaterialDescrption!==""?`${a.MaterialDescrption}`:"Not Available",materialGroup:a.MaterialGroup!==""?`${a.MaterialGroup} - ${a.materialGroupDesc}`:"-",XplantMatStatus:a.XplantMatStatus!==""?`${a.XplantMatStatus} ${a.XplantMatStatusDesc?"-"+a.XplantMatStatusDesc:""}`:"-",Plant:a.Plant.length>0?`${a.Plant}`:"-",WarehouseNo:a.WarehouseNo.length>0?`${a.WarehouseNo}`:"-",createdOn:A(a.CreatedOn).format(g==null?void 0:g.dateFormat),changedOn:A(a.LastChange).format(g==null?void 0:g.dateFormat),changedBy:a.ChangedBy,createdBy:a.CreatedBy,Division:a.Division!==""?`${a.Division} ${a.DivisionDesc?"-"+a.DivisionDesc:""}`:"Not Available",StorageLocation:a.StorageLocation.length>0?`${a.StorageLocation} `:"-",oldMaterialNumber:a.OldMaterialNumber!==""?`${a.OldMaterialNumber} - ${a.OldMaterialNumberName}`:"Not Available",labOffice:a.LabOffice!==""?`${a.LabOffice} - ${a.LabOfficeName}`:"Not Available",transportationGroup:a.TrnsportGroup!==""?`${a.TrnsportGroup} - ${a.TrnsportGroupName}`:"Not Available",SalesOrg:a.SalesOrg.length>0?`${a.SalesOrg}`:"-",DistChnl:a.DistChnl.length>0?`${a.DistChnl}`:"-",indSector:a.Industrysector!==""?a.Industrysector:"-",PrimaryVendor:a.PryVendor!==""?a.PryVendor:"-"};r.push(f)}r.sort((c,F)=>A(c.createdOn,"DD MMM YYYY HH:mm")-A(F.createdOn,"DD MMM YYYY HH:mm")),Be(r.reverse()),w(!1),De(l.count),At(l.count),d(Ut({module:"MaterialMgmt"}))}else(l==null?void 0:l.statusCode)===Ce.STATUS_414&&(Yt(l==null?void 0:l.message,"error"),w(!1))},n=l=>{T(l)};$(`/${D}/data/getMaterialBasedOnAdditionalParams`,"post",s,n,t)},ss=()=>{w(!0);let t={fromDate:A(e==null?void 0:e.createdOn[0]).format("YYYYMMDD")??"",toDate:A(e==null?void 0:e.createdOn[1]).format("YYYYMMDD")??"",createdBy:(e==null?void 0:e.createdBy)??"",materialDesc:(e==null?void 0:e.description)??"",plant:(e==null?void 0:e.plant)??"",materialGroup:(e==null?void 0:e.group)??"",materialType:(e==null?void 0:e.type)??"",changedBy:(e==null?void 0:e.changedBy)??"",taskId:(e==null?void 0:e.taskId)??"",status:(e==null?void 0:e.status)??"",salesOrg:(e==null?void 0:e.salesOrg)??"",division:(e==null?void 0:e.division)??"",distributionChannel:(e==null?void 0:e.distributionChannel)??"",storageLocation:(e==null?void 0:e.storageLocation)??"",ProdHier:(e==null?void 0:e["Product Hierarchy"])??"",BasicMatl:(e==null?void 0:e["Basic Material"])??"",ProfitCtr:(e==null?void 0:e["Profit Center"])??"",PurGroup:(e==null?void 0:e["Purchasing Group"])??"",MatlGrp5:(e==null?void 0:e["Material Group 5"])??"",MrpCtrler:(e==null?void 0:e["MRP Controller"])??"",warehouseNo:(e==null?void 0:e["Warehouse No"])??"",Mrpprofile:(e==null?void 0:e["MRP Profile"])??"",oldMaterialNo:(e==null?void 0:e.oldMaterialNumber)??"",number:(e==null?void 0:e.number)??"",PurStatus:(e==null?void 0:e.PurStatus)??"",top:V,skip:V*ie,fetchCount:!1,labOffice:(e==null?void 0:e["Lab/Office"])??"",transportationGroup:(e==null?void 0:e["Transportation Group"])??"",batchManagement:(e==null?void 0:e.batchManagement)??""};const s=l=>{var _;w(!1);var r=[];for(let c=0;c<((_=l==null?void 0:l.body)==null?void 0:_.length);c++){var a=l==null?void 0:l.body[c],f={id:je(),Number:a.Number,materialType:a.Materialtype!==""?`${a.Materialtype} - ${a.MaterialTypeDesc}`:"Not Available",materialDesc:a.MaterialDescrption!==""?`${a.MaterialDescrption}`:"Not Available",materialGroup:a.MaterialGroup!==""?`${a.MaterialGroup} - ${a.materialGroupDesc}`:"-",XplantMatStatus:a.XplantMatStatus!==""?`${a.XplantMatStatus} ${a.XplantMatStatusDesc?"-"+a.XplantMatStatusDesc:""}`:"-",Plant:a.Plant.length>0?`${a.Plant}`:"Not Available",WarehouseNo:a.WarehouseNo.length>0?`${a.WarehouseNo}`:"-",createdOn:A(a.CreatedOn).format(g==null?void 0:g.dateFormat),changedOn:A(a.LastChange).format(g==null?void 0:g.dateFormat),changedBy:a.ChangedBy,createdBy:a.CreatedBy,division:a.Division!==""?`${a.Division}- ${a.DivisionDesc} `:"Not Available",storageLocation:a.StorageLocation.length>0?`${a.StorageLocation} `:"-",oldMaterialNumber:a.OldMaterialNumber!==""?`${a.OldMaterialNumber} - ${a.OldMaterialNumberName}`:"Not Available",labOffice:a.LabOffice!==""?`${a.LabOffice} - ${a.LabOfficeName}`:"Not Available",transportationGroup:a.TrnsportGroup!==""?`${a.TrnsportGroup} - ${a.TrnsportGroupName}`:"Not Available",SalesOrg:a.SalesOrg.length>0?`${a.SalesOrg}`:"-",DistChnl:a.DistChnl.length>0?`${a.DistChnl}`:"-",indSector:a.Industrysector!==""?a.Industrysector:"-",PrimaryVendor:a.PryVendor!==""?a.PryVendor:"-"};r.push(f)}r.sort((c,F)=>A(c.createdOn,"DD MMM YYYY HH:mm")-A(F.createdOn,"DD MMM YYYY HH:mm")),Be(c=>[...c,...r]),w(!1)},n=l=>{w(!1),T(l)};$(`/${D}/data/getMaterialBasedOnAdditionalParams`,"post",s,n,t)};i.useState(null);const[_t,De]=i.useState(0),[Ct,At]=i.useState(0),[Fl,ls]=i.useState(!1),[ns,os]=i.useState(!1),[jl,is]=i.useState(!1),[ml,rs]=i.useState(!1),[cs,Et]=i.useState(!1),[ds,X]=i.useState(""),[Nt,O]=i.useState(""),[us,J]=i.useState(""),Xe=()=>{os(!1)},q=()=>{Et(!0)},yt=()=>{Et(!1),rs(!1),ls(!1),is(!1)};i.useState(null);const[It,Ms]=i.useState(!1),Tt=()=>{Le([]),Ye([]),Se([]),nt(""),ve([]),ee([]),ot([]),we([]),se({}),st([]),be([]),d(qe({module:"ArticleMaster"})),na(t=>{const s={...t};return Object.keys(s).forEach(n=>{s[n]={code:"",desc:""}}),s}),at(!0)},fs=t=>{const s=t.map(c=>S.find(F=>F.id===c));var n=s.map(c=>c.company),l=new Set(n),r=s.map(c=>c.vendor),a=new Set(r),f=s.map(c=>c.paymentTerm),_=new Set(f);s.length>0?l.size===1?a.size===1?_.size!==1?(le(!0),X("Error"),O("Invoice cannot be generated for vendors with different payment terms"),J("danger"),q()):le(!1):(le(!0),X("Error"),O("Invoice cannot be generated for multiple suppliers"),J("danger"),q()):(le(!0),X("Error"),O("Invoice cannot be generated for multiple companies"),J("danger"),q()):le(!0),aa(t),sa(s)},[ie,Z]=i.useState(0),ps=t=>{const s=t.target.value;Zt(s),Z(0)},gs=(t,s)=>{Z(isNaN(s)?0:s)};i.useEffect(()=>{Pa||ie!=0&&ie*V>=(S==null?void 0:S.length)&&ss()},[ie,V]);function Ds(){oe()}const[en,hs]=i.useState(!1),_s=(t,s)=>({field:t,headerName:M(s),editable:!1,flex:1,renderCell:n=>{const l=n.value?n.value.split(",").map(f=>f.trim()):[],r=l.length-1;if(l.length===0)return"-";const a=f=>{const[_,...c]=f.split("-");return u(_e,{children:[o("strong",{children:_}),c.length?` - ${c.join("-")}`:""]})};return u(Ae,{sx:{display:"flex",alignItems:"center",width:"100%",minWidth:0},children:[o(Ot,{title:l[0],placement:"top",arrow:!0,children:o(z,{variant:"body2",sx:{overflow:"hidden",textOverflow:"ellipsis",whiteSpace:"nowrap",flex:1,minWidth:0},children:a(l[0])})}),r>0&&o(Ae,{sx:{display:"flex",alignItems:"center",ml:1,flexShrink:0},children:o(Ot,{arrow:!0,placement:"right",title:u(Ae,{sx:{p:1,maxHeight:200,overflowY:"auto"},children:[u(z,{variant:"subtitle2",sx:{fontWeight:600,mb:1},children:["Additional ",s,"s (",r,")"]}),l.slice(1).map((f,_)=>o(z,{variant:"body2",sx:{mb:.5},children:a(f)},_))]}),children:u(Ae,{sx:{display:"flex",alignItems:"center",cursor:"pointer"},children:[o(Gl,{sx:{fontSize:"1rem",color:"primary.main","&:hover":{color:"primary.dark"}}}),u(z,{variant:"caption",sx:{ml:.5,color:"primary.main",fontSize:"11px"},children:["+",r]})]})})})]})}}),Cs=()=>({field:"dataValidation",headerName:M("Data Validation"),editable:!1,flex:1,renderCell:t=>{const s=t.value;let n;return s!=null?n=s:n=[100,75,50,25][t.api.getRowIndexRelativeToVisibleRows(t.id)%4],o(bl,{percentage:n,id:t.id})}}),As=(t,s)=>({field:t,headerName:M(s),editable:!1,flex:1,renderCell:n=>{var a;const[l,...r]=((a=n.value)==null?void 0:a.split(" - "))||[];return u("span",{style:{flex:1,wordBreak:"break-word",whiteSpace:"normal"},children:[o("strong",{children:l})," ",r.length?`- ${r.join(" - ")}`:""]})}}),Gt=t=>{let s={decisionTableId:null,decisionTableName:Ke.MDG_MAT_SEARCHSCREEN_COLUMN,version:"v2",conditions:[{"MDG_CONDITIONS.MDG_MAT_REGION":(t==null?void 0:t.toUpperCase())||"US","MDG_CONDITIONS.MDG_MODULE":"Article","MDG_CONDITIONS.MDG_MAT_SEARCHTYPE":"Master Data","MDG_CONDITIONS.MDG_MAT_VIEW_TYPE":"NA"}]};j(s)},Es=()=>{let t={decisionTableId:null,decisionTableName:Ke.MDG_MAT_SEARCHSCREEN_PARAMETER,version:"v2",conditions:[{"MDG_CONDITIONS.MDG_MAT_REGION":"US","MDG_CONDITIONS.MDG_MODULE":"Article","MDG_CONDITIONS.MDG_MAT_SEARCHTYPE":"Master Data","MDG_CONDITIONS.MDG_MAT_VIEW_TYPE":"NA"}]};H(t)},Ns=t=>{const s=[];let n=(t==null?void 0:t.sort((l,r)=>l.MDG_MAT_SEQUENCE_NO-r.MDG_MAT_SEQUENCE_NO))||[];return n&&(n==null||n.forEach(l=>{if((l==null?void 0:l.MDG_MAT_VISIBILITY)===Al.DISPLAY&&l!=null&&l.MDG_MAT_UI_FIELD_NAME){const r=l.MDG_MAT_JSON_FIELD_NAME,a=l.MDG_MAT_UI_FIELD_NAME;r==="DataValidation"?s.push(Cs()):l.MDG_MAT_FIELD_TYPE==="Multiple"?s.push(_s(r,a)):l.MDG_MAT_FIELD_TYPE==="Single"&&s.push(As(r,a))}})),s};i.useEffect(()=>{var t,s,n,l;if(v){const r=Ns((s=(t=v==null?void 0:v.result)==null?void 0:t[0])==null?void 0:s.MDG_MAT_SEARCHSCREEN_COLUMN_ACTION_TYPE);Ma(r)}if(W){const r=(l=(n=W==null?void 0:W.result)==null?void 0:n[0])==null?void 0:l.MDG_MAT_SEARCHSCREEN_PARAMETER_ACTION_TYPE,a=r==null?void 0:r.filter(f=>f.MDG_MAT_FILTER_TYPE==="Additional").map(f=>({title:M(f.MDG_MAT_UI_FIELD_NAME)}));fa(r),Sa(a)}},[v,W]),i.useEffect(()=>{oe()},[V]),i.useEffect(()=>{var t;if(E){Ee(E),vt(E);const s=(t=E==null?void 0:E.code)==null?void 0:t.toUpperCase();(s==="US"||s==="EUR")&&Gt(s)}},[E]),i.useEffect(()=>(Gt("US"),Es(),d(qe({module:"DuplicateDesc"})),d(bs()),d(ws()),d(ks({})),()=>{d(Bs())}),[]);let ys=i.useRef(null);const vt=t=>{let s={decisionTableId:null,decisionTableName:Ke.MDG_MAT_REGION_DIVISION_MAPPING,version:"v1",conditions:[{"MDG_CONDITIONS.MDG_MAT_REGION":(t==null?void 0:t.code)||""}]};de(s)};i.useEffect(()=>{var t,s;if(L){const n=Us((s=(t=L==null?void 0:L.result)==null?void 0:t[0])==null?void 0:s.MDG_MAT_REGION_DIVISION_MAPPING);d(xl({keyName:"Division",data:n}))}},[L]),i.useEffect(()=>{E&&(Ee(E),vt(E))},[E]);const Is=()=>{We(!0),Gs()},Ts=()=>{We(!0),Z(0)},Gs=()=>{Z(0),w(!0);let t={fromDate:A(e==null?void 0:e.createdOn[0]).format("YYYYMMDD")??"",toDate:A(e==null?void 0:e.createdOn[1]).format("YYYYMMDD")??"",createdBy:(e==null?void 0:e.createdBy)??"",materialDesc:(e==null?void 0:e.description)??"",plant:(e==null?void 0:e.plant)??"",materialGroup:(e==null?void 0:e.group)??"",materialType:(e==null?void 0:e.type)??"",changedBy:(e==null?void 0:e.changedBy)??"",taskId:(e==null?void 0:e.taskId)??"",status:(e==null?void 0:e.status)??"",salesOrg:(e==null?void 0:e.salesOrg)??"",division:(e==null?void 0:e.division)??"",distributionChannel:(e==null?void 0:e.distributionChannel)??"",storageLocation:(e==null?void 0:e.storageLocation)??"",ProdHier:(e==null?void 0:e["Product Hierarchy"])??"",BasicMatl:(e==null?void 0:e["Basic Material"])??"",ProfitCtr:(e==null?void 0:e["Profit Center"])??"",PurGroup:(e==null?void 0:e["Purchasing Group"])??"",MatlGrp5:(e==null?void 0:e["Material Group 5"])??"",MrpCtrler:(e==null?void 0:e["MRP Controller"])??"",warehouseNo:(e==null?void 0:e["Warehouse No"])??"",Mrpprofile:(e==null?void 0:e["MRP Profile"])??"",oldMaterialNo:(e==null?void 0:e.oldMaterialNumber)??"",number:(e==null?void 0:e.number)??"",PurStatus:(e==null?void 0:e.PurStatus)??"",top:Ct,skip:0,labOffice:(e==null?void 0:e["Lab/Office"])??"",transportationGroup:(e==null?void 0:e["Transportation Group"])??"",batchManagement:(e==null?void 0:e.batchManagement)??""};const s=l=>{var _;if((l==null?void 0:l.statusCode)===Ce.STATUS_200){var r=[];for(let c=0;c<((_=l==null?void 0:l.body)==null?void 0:_.length);c++){var a=l==null?void 0:l.body[c],f={id:je(),Number:a.Number,materialType:a.Materialtype!==""?`${a.Materialtype} - ${a.MaterialTypeDesc}`:"Not Available",materialDesc:a.MaterialDescrption!==""?`${a.MaterialDescrption}`:"Not Available",materialGroup:a.MaterialGroup!==""?`${a.MaterialGroup} - ${a.materialGroupDesc}`:"-",XplantMatStatus:a.XplantMatStatus!==""?`${a.XplantMatStatus} ${a.XplantMatStatusDesc?"-"+a.XplantMatStatusDesc:""}`:"-",Plant:a.Plant.length>0?`${a.Plant}`:"-",WarehouseNo:a.WarehouseNo.length>0?`${a.WarehouseNo}`:"-",createdOn:A(a.CreatedOn).format(g==null?void 0:g.dateFormat),changedOn:A(a.LastChange).format(g==null?void 0:g.dateFormat),changedBy:a.ChangedBy,createdBy:a.CreatedBy,division:a.Division!==""?`${a.Division}- ${a.DivisionDesc} `:"Not Available",StorageLocation:a.StorageLocation.length>0?`${a.StorageLocation} `:"-",oldMaterialNumber:a.OldMaterialNumber!==""?`${a.OldMaterialNumber} - ${a.OldMaterialNumberName}`:"Not Available",labOffice:a.LabOffice!==""?`${a.LabOffice} - ${a.LabOfficeName}`:"Not Available",transportationGroup:a.TrnsportGroup!==""?`${a.TrnsportGroup} - ${a.TrnsportGroupName}`:"Not Available",SalesOrg:a.SalesOrg.length>0?`${a.SalesOrg}`:"-",DistChnl:a.DistChnl.length>0?`${a.DistChnl}`:"-",indSector:a.Industrysector!==""?a.Industrysector:"-",PrimaryVendor:a.PryVendor!==""?a.PryVendor:"-"};r.push(f)}r.sort((c,F)=>A(c.createdOn,"DD MMM YYYY HH:mm")-A(F.createdOn,"DD MMM YYYY HH:mm")),Be(r.reverse()),w(!1),Z(Math.floor((r==null?void 0:r.length)/V)),De(l.count),At(l.count),d(Ut({module:"MaterialMgmt"}))}else(l==null?void 0:l.statusCode)===Ce.STATUS_414&&(Yt(l==null?void 0:l.message,"error"),w(!1))},n=l=>{T(l)};$(`/${D}/data/getMaterialBasedOnAdditionalParams`,"post",s,n,t)};return u("div",{ref:ys,children:[o(El,{dialogState:cs,openReusableDialog:q,closeReusableDialog:yt,dialogTitle:ds,dialogMessage:Nt,handleDialogConfirm:yt,dialogOkText:"OK",dialogSeverity:us}),o(Nl,{openSnackBar:Da,alertMsg:Nt,alertType:pa,handleSnackBarClose:Ra}),o("div",{style:{...Ys,backgroundColor:"#FAFCFF"},children:u($t,{spacing:1,children:[o(p,{container:!0,mt:0,sx:Os,children:u(p,{item:!0,md:5,children:[o(z,{variant:"h3",children:o("strong",{children:M("Article Management")})}),o(z,{variant:"body2",color:"#777",children:M("This view displays the list of Articles")})]})}),o(p,{container:!0,sx:zs,children:o(p,{item:!0,md:12,children:u(wl,{defaultExpanded:!1,children:[u(kl,{expandIcon:o(Hs,{sx:{fontSize:"1.25rem",color:h.primary.main}}),"aria-controls":"panel1a-content",id:"panel1a-header",className:"filterArticle",children:[o(Ws,{sx:{fontSize:"1.25rem",marginRight:1,color:h.primary.main}}),o(z,{sx:{fontSize:"0.875rem",fontWeight:600,color:h.primary.dark},children:M("Filter Article")})]}),u(Vs,{sx:{padding:"1rem 1rem 0.5rem"},children:[u(Bl,{container:!0,children:[u(p,{container:!0,rowSpacing:1,spacing:2,alignItems:"center",sx:{padding:"0rem 1rem 0.5rem"},children:[Oe==null?void 0:Oe.filter(t=>t.MDG_MAT_VISIBILITY!=="Hidden").sort((t,s)=>t.MDG_MAT_SEQUENCE_NO-s.MDG_MAT_SEQUENCE_NO).map((t,s)=>{var n,l,r,a,f,_;return u(he.Fragment,{children:[(t==null?void 0:t.MDG_MAT_JSON_FIELD_NAME)===B.REGION&&u(p,{item:!0,md:2,children:[u(I,{sx:y,children:[M(t==null?void 0:t.MDG_MAT_UI_FIELD_NAME)," ",o("span",{style:{color:(l=(n=h)==null?void 0:n.error)==null?void 0:l.dark},children:"*"})]}),o(te,{size:"small",fullWidth:!0,children:o(Ll,{options:[{code:"US",desc:"USA"},{code:"EUR",desc:"Europe"}],value:E,onChange:c=>{nt(c),we([]),ee([]),Le([]),Se([])},placeholder:M(t==null?void 0:t.MDG_MAT_UI_FIELD_NAME),disabled:!1,minWidth:"90%",listWidth:210})})]}),(t==null?void 0:t.MDG_MAT_JSON_FIELD_NAME)===B.SALESORG&&u(p,{item:!0,md:2,children:[u(I,{sx:y,children:[M(t==null?void 0:t.MDG_MAT_UI_FIELD_NAME),o("span",{style:{color:(a=(r=h)==null?void 0:r.error)==null?void 0:a.dark},children:"*"})]}),o(K,{matGroup:E?Y==null?void 0:Y.uniqueSalesOrgList:[],selectedMaterialGroup:N,setSelectedMaterialGroup:c=>{we(c),ee([]),c.length===0?(rt([]),ee([])):Za(c)},placeholder:M(t==null?void 0:t.MDG_MAT_UI_FIELD_NAME)})]}),(t==null?void 0:t.MDG_MAT_JSON_FIELD_NAME)===B.PLANT&&u(p,{item:!0,md:2,children:[u(I,{sx:y,children:[M(t==null?void 0:t.MDG_MAT_UI_FIELD_NAME),o("span",{style:{color:(_=(f=h)==null?void 0:f.error)==null?void 0:_.dark},children:"*"})]}),o(K,{matGroup:N!=null&&N.length?mt:[],selectedMaterialGroup:P,setSelectedMaterialGroup:Le,placeholder:M(t==null?void 0:t.MDG_MAT_UI_FIELD_NAME)})]}),(t==null?void 0:t.MDG_MAT_JSON_FIELD_NAME)===B.NUMBER&&u(p,{item:!0,md:2,children:[o(I,{sx:y,children:M(t==null?void 0:t.MDG_MAT_UI_FIELD_NAME)}),o(te,{size:"small",fullWidth:!0,children:o(Xs,{matGroup:Vt,selectedMaterialGroup:Pe,setSelectedMaterialGroup:Se,isDropDownLoading:it,placeholder:M(t==null?void 0:t.MDG_MAT_UI_FIELD_NAME),onInputChange:Oa,minCharacters:4})})]}),(t==null?void 0:t.MDG_MAT_JSON_FIELD_NAME)===B.MATERIALTYPE&&u(p,{item:!0,md:2,children:[o(I,{sx:y,children:M(t==null?void 0:t.MDG_MAT_UI_FIELD_NAME)}),o(K,{matGroup:oa,selectedMaterialGroup:xe,setSelectedMaterialGroup:st,placeholder:M(t==null?void 0:t.MDG_MAT_UI_FIELD_NAME)})]}),(t==null?void 0:t.MDG_MAT_JSON_FIELD_NAME)===B.MATERIALGROUP&&u(p,{item:!0,md:2,children:[o(I,{sx:y,children:M(t==null?void 0:t.MDG_MAT_UI_FIELD_NAME)}),o(K,{matGroup:ra,selectedMaterialGroup:$e,setSelectedMaterialGroup:ot,placeholder:M(t==null?void 0:t.MDG_MAT_UI_FIELD_NAME)})]}),(t==null?void 0:t.MDG_MAT_JSON_FIELD_NAME)===B.DISTRIBUTIONCHANNEL&&u(p,{item:!0,md:2,children:[o(I,{sx:y,children:M(t==null?void 0:t.MDG_MAT_UI_FIELD_NAME)}),o(K,{matGroup:N!=null&&N.length?ta:[],selectedMaterialGroup:ke,setSelectedMaterialGroup:c=>{if(!c||c.length===0){ee([]);return}ee(c)},isDropDownLoading:it,placeholder:M(t==null?void 0:t.MDG_MAT_UI_FIELD_NAME)})]}),(t==null?void 0:t.MDG_MAT_JSON_FIELD_NAME)===B.DIVISION&&u(p,{item:!0,md:2,children:[o(I,{sx:y,children:M(t==null?void 0:t.MDG_MAT_UI_FIELD_NAME)}),o(K,{matGroup:E?U==null?void 0:U.Division:[],selectedMaterialGroup:Re,setSelectedMaterialGroup:c=>{if(!c||c.length===0){be([]);return}be(c)},placeholder:M(t==null?void 0:t.MDG_MAT_UI_FIELD_NAME)})]}),(t==null?void 0:t.MDG_MAT_JSON_FIELD_NAME)===B.PURSTATUS&&u(p,{item:!0,md:2,children:[o(I,{sx:y,children:M(t==null?void 0:t.MDG_MAT_UI_FIELD_NAME)}),o(K,{matGroup:(U==null?void 0:U.CSalStatus)??[],selectedMaterialGroup:Ge,setSelectedMaterialGroup:c=>{if(!c||c.length===0){ve([]);return}ve(c)},placeholder:M(t==null?void 0:t.MDG_MAT_UI_FIELD_NAME)})]}),(t==null?void 0:t.MDG_MAT_JSON_FIELD_NAME)===B.CREATEDON&&u(p,{item:!0,md:2,children:[o(I,{sx:y,children:M(t==null?void 0:t.MDG_MAT_UI_FIELD_NAME)}),o(te,{fullWidth:!0,sx:{padding:0},children:o(Js,{dateAdapter:qs,children:o(Ks,{handleDate:as,date:e==null?void 0:e.createdOn})})})]})]},s)}),u(p,{item:!0,md:2,children:[o(I,{sx:y,children:M("Add New Filters")}),o(te,{sx:{width:"100%"},children:o(Qs,{sx:{font_Small:y,fontSize:"12px",width:"100%"},size:"small",multiple:!0,limitTags:2,value:Me,onChange:qa,renderValue:t=>t.join(", "),MenuProps:{MenuProps:b},endAdornment:Me.length>0&&o(Zs,{position:"end",sx:{marginRight:"10px"},children:o(Lt,{size:"small",onClick:()=>Ye([]),"aria-label":"Clear selections",children:o(Rt,{})})}),children:Ve==null?void 0:Ve.map(t=>u(Qe,{value:t.title,children:[o(Fs,{checked:Me.indexOf(t.title)>-1}),t.title]},t.title))})})]})]}),o(p,{container:!0,sx:{flexDirection:"row",padding:"0rem 1rem 0.5rem"},gap:1,children:Me.map((t,s)=>{var n;return t==="Old Article Number"?o(_e,{children:u(p,{item:!0,md:2,children:[o(I,{sx:y,children:M(t)}),o(te,{size:"small",fullWidth:!0,children:o(Ze,{sx:{fontSize:"12px !important"},fullWidth:!0,size:"small",value:e==null?void 0:e.oldMaterialNumber,onChange:Wa,placeholder:M("ENTER OLD ARTICLE NUMBER")})})]},s)}):t==="Article Description"?o(_e,{children:u(p,{item:!0,md:2,children:[o(I,{sx:y,children:M(t)}),o(te,{size:"small",fullWidth:!0,children:o(Ze,{sx:{fontSize:"12px !important"},fullWidth:!0,size:"small",value:e==null?void 0:e.materialDescription,onChange:za,placeholder:M("ENTER ARTICLE DESCRIPTION")})})]},s)}):t==="Created By"?o(_e,{children:u(p,{item:!0,md:2,children:[o(I,{sx:y,children:M("Created By")}),o(Ze,{sx:{fontSize:"12px !important"},fullWidth:!0,size:"small",value:e==null?void 0:e.createdBy,onChange:Ha,placeholder:M("ENTER CREATED BY")})]},s)}):u(p,{item:!0,md:2,children:[o(I,{sx:{fontSize:"12px"},children:M(t)}),o(K,{matGroup:(ne==null?void 0:ne[t])??[],selectedMaterialGroup:((n=m[t])==null?void 0:n.length)>0?m[t]:[],setSelectedMaterialGroup:l=>{var r;if(!l||l.length===0){se(a=>({...a,[t]:[]}));return}l.length>0&&((r=l[l.length-1])==null?void 0:r.code)==="Select All"?Ja(t):se(a=>({...a,[t]:l}))}})]},s)})})]}),u(Ul,{children:[o(zt,{variant:"outlined",size:"small",startIcon:o(Rt,{sx:{fontSize:"1rem"}}),onClick:()=>{Tt()},disabled:It,sx:{borderColor:h.primary.main,color:h.primary.main},children:M("Clear")}),o(p,{sx:{...bt},children:o(Sl,{moduleName:"ArticleMaster",handleSearch:oe,disabled:E===""||!(N!=null&&N.length)||!(P!=null&&P.length),onPresetActiveChange:t=>Ms(t),onClearPreset:Tt})}),o(zt,{variant:"contained",size:"small",startIcon:o(js,{sx:{fontSize:"1rem"}}),sx:{...ms,...bt},disabled:It,onClick:()=>{const t=[];if(E==""&&t.push("Region"),N!=null&&N.length||t.push("SalesOrg"),P!=null&&P.length||t.push("Plant"),t.length>0){O(el.MANDATORY_FILTER_MD(t.join(", "))),ga("error"),La();return}oe()},children:M("Search")})]})]})]})})}),o(p,{item:!0,sx:{position:"relative"},children:o($t,{children:o(tl,{isLoading:ue,paginationLoading:ue,module:"ArticleMgmt",width:"100%",title:M("List of Articles")+" ("+_t+")",rows:Qt??[],columns:ua??[],showSearch:!0,showRefresh:!0,showSelectedCount:!0,showExport:!0,onSearch:t=>ts(t),onRefresh:Ds,pageSize:V,page:ie,onPageSizeChange:ps,rowCount:_t??(S==null?void 0:S.length)??0,onPageChange:gs,getRowIdValue:"id",hideFooter:!0,disableSelectionOnClick:!0,status_onRowSingleClick:!0,tempheight:"calc(100vh - 320px)",onRowsSelectionHandler:fs,callback_onRowSingleClick:t=>{var n,l;const s=t.row.Number;(l=(n=t==null?void 0:t.row)==null?void 0:n.materialType)==null||l.split(" - ")[0],Ie(!0),C(`/masterDataCockpit/articleMaster/DisplayArticleSAPView/${s}`,{state:t.row})},showCustomNavigation:!0,stopPropagation_Column:"action",status_onRowDoubleClick:!0,showFirstPageoptions:!0,showSelectAllOptions:!0,onSelectAllOptions:Is,onSelectFirstPageOptions:Ts})})}),(Q==null?void 0:Q.length)>0&&o(Fe,{sx:{position:"fixed",bottom:0,left:0,right:0},elevation:2,children:u(al,{className:"container_BottomNav",showLabels:!0,sx:{display:"flex",justifyContent:"flex-end",gap:1},value:qt,onChange:t=>{Kt(t)},children:[u(sl,{open:ns,onClose:Xe,sx:{"&::webkit-scrollbar":{width:"1px"}},children:[u(ll,{children:[o(z,{variant:"h6",children:"Inputs"}),o(Lt,{onClick:Xe,children:o(nl,{})})]}),o(ol,{dividers:!0,children:o(p,{container:!0,spacing:1})}),u(il,{children:[o(ce,{onClick:Xe,children:"Cancel"}),o(ce,{variant:"contained",children:"Proceed"})]})]}),o(ce,{size:"small",variant:"contained",onClick:()=>{C("/requestBench/createArticle"),d(rl({})),d(cl({}))},className:"createRequestButtonArticle",children:M("Create Request")}),o(ce,{size:"small",variant:"contained",onClick:()=>{He(!0)},className:"sapdataAM",children:M("SAP Data Export")}),o(wt,{sx:{zIndex:1},open:Ca,anchorEl:fe.current,placement:"top-end",children:o(Fe,{style:{width:(Pt=fe.current)==null?void 0:Pt.clientWidth},children:o(kt,{onClickAway:ka,children:o(Bt,{id:"split-button-menu",autoFocusItem:!0,children:Ta.slice(1).map((t,s)=>o(Qe,{selected:s===Aa-1,onClick:()=>wa(t,s+1),children:t},t))})})})}),o(wt,{sx:{zIndex:1},open:Na,anchorEl:pe.current,placement:"top-end",children:o(Fe,{style:{width:(St=pe.current)==null?void 0:St.clientWidth},children:o(kt,{onClickAway:Ya,children:o(Bt,{id:"split-button-menu",autoFocusItem:!0,children:Ga.slice(1).map((t,s)=>o(Qe,{selected:s===ya-1,onClick:()=>Ua(t,s+1),children:t},t))})})})}),ha&&o(Pl,{artifactId:"",artifactName:"",setOpen:ze,handleUpload:Ba})]})})]})}),o(Rl,{openSearch:xa,setOpenSearch:He,onSearchComplete:$a}),o(yl,{blurLoading:Jt}),o(Il,{})]})};export{Dn as default};
