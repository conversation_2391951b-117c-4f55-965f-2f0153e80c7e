import{c9 as fc,ca as Nc,cb as Cc,aP as Os,n as Z,s as as,u as rl,aX as c,C as qe,bI as Bs,aT as Ot,dG as _c,xi as Oc,r as i,g as jl,a as Nl,o as eo,cz as vr,ep as Gr,xj as Ic,fw as Pt,f1 as ml,j as e,c as f,O as ve,b1 as cl,d as st,B as Be,an as nt,bD as Oi,aH as Jl,ai as Ws,aj as Hs,al as es,b3 as ul,aa as Sn,am as ts,aG as hl,aF as Cl,aZ as Fs,aD as ys,bK as ns,aQ as C,xk as bc,xl as Rc,xm as Mc,g3 as Qs,aY as xc,ae as el,ap as to,xn as fl,aC as Dc,wV as Lc,H as no,wW as yc,wX as vc,aJ as wt,dT as Ur,J as so,xo as kr,be as S,xp as $r,xq as lo,xr as bl,Z as We,xs as Ii,d7 as Xl,xt as Gc,fN as Uc,aO as Ds,cZ as vl,bf as Ys,cw as bi,ah as kc,cI as cs,F as Ut,cH as Ls,fR as wn,fY as Zs,T as bn,f_ as io,ad as zl,ag as Yl,aE as Kl,ak as Dl,a6 as cn,$ as ro,ez as $c,fS as Pl,au as Yt,g1 as Ei,aK as Sl,g2 as Rl,fx as Pr,g4 as xl,bE as Ri,dy as Pc,bs as pc,bG as Ai,a9 as al,a5 as pr,xz as Mi,em as pn,ch as Ll,ek as Xs,xB as Ml,k as oo,V as co,A as ao,i as uo,b9 as ho,g5 as xi,xu as go,xv as To,d0 as Eo,ge as Tl,M as si,xw as Wc,yt as Hc,xx as qc,xy as Bc,xA as ps,bi as Wr,eb as _s,yu as Hr,xG as Fc,xH as Vc,b5 as Ao,b6 as Vl,d5 as So,d3 as Si,d4 as mi,xE as xs,bm as pl,bn as Wl,bo as ll,bp as je,bq as Hl,bl as mo,xJ as li,er as Pn,bO as wc,bP as jc,bQ as qr,br as Di,qT as wl,yv as ii,xD as fo,xL as ri,xM as ql,xN as Jc,aU as Cs,xO as No,xP as Xc,xQ as Co,xR as Al,xS as El,xT as Br,dJ as Li,xC as fi,bh as Ks,fy as zc,xF as _o,xI as Yc,xU as Kc,xV as Qc,xW as Zc,ao as Oo,e3 as Fr,yf as ea,y8 as ta,wS as Ni,da as Vr,a$ as Ci,yg as wr,aA as Bl,aB as Fl,wY as Io,yh as na,yi as oi,cq as sa,a1 as la,a2 as ia,af as ra,yj as oa,eL as ca,y9 as aa,ya as da,yk as ua,yl as ha,gp as ga,y4 as Ta,wP as ci,wQ as Ea,wR as jr,r0 as Aa,wT as Sa,xX as ai,xY as ma,xZ as fa,I as Na,x_ as Ca,x$ as _a,wZ as Oa,w_ as Ia,y0 as ba,y1 as Ra,bd as di,K as Ma,aW as xa,bc as Da,cA as La,y2 as ya,y3 as va,d8 as Ga,d6 as Ua,d9 as ka}from"./index-f7d9b065.js";import{s as zs,u as $a}from"./useMaterialFetchDropdownAndDispatch-2f5f3f49.js";import{F as il}from"./FilterField-ed1f5dc1.js";import{u as Pa}from"./useProfitcenterRequestHeaderConfig-c3b21e37.js";import{u as bo,a as pa}from"./useChangeMaterialRows-cefb1340.js";import{R as Wa,T as Jr,i as Ha,u as yi,a as Ro,b as Mo,c as xo,d as _i,S as qa,o as ui,e as Do,E as Ba,f as Xr,G as Fa,j as Va,k as zr,l as wa,m as ja,C as Ja,g as Xa,h as za}from"./RequestDetailsForFC-231ddbc2.js";import{D as Ya,C as Yr,e as Lo,d as yo,T as hi,M as Ka,f as Qa,a as Za,b as qs,c as ed,A as td,P as nd}from"./PreviewPage-634057fa.js";import{u as vi}from"./useArticleFieldConfig-f9acb4d0.js";import{d as dl}from"./DeleteOutlineOutlined-8fd07dc7.js";import{d as yl}from"./Description-ab582559.js";import{d as sd}from"./TaskAlt-0afc1812.js";import{A as vo}from"./AdditionalData-c6566943.js";import{S as Qt}from"./SingleSelectDropdown-aee403d4.js";import{d as Kr,a as Qr}from"./CloseFullscreen-2870eb3e.js";import{u as Go}from"./useDynamicWorkflowDT-955b7628.js";import{u as Uo}from"./useCustomDtCall-0fd16760.js";import{d as ld}from"./LibraryAdd-286802b4.js";import{a as ko,d as id}from"./FileUploadOutlined-4a68a28a.js";import{d as rd}from"./Remove-82c67208.js";import{d as od}from"./Edit-51c94b76.js";import{G as gi}from"./GenericViewGeneral-e6209433.js";import{d as $o}from"./Delete-5278579a.js";import{u as Po}from"./useFinanceCostingRows-ffbb569f.js";import{d as cd}from"./PermIdentityOutlined-0746a749.js";import{d as ad}from"./FeedOutlined-41109ec9.js";import{D as dd}from"./DatePicker-a8e9bd4a.js";import{d as Zr}from"./TrackChangesTwoTone-7a2ab513.js";import{E as ud}from"./ExcelOperationsCard-49e9ffd2.js";import"./useChangeLogUpdate-1ba6b2dd.js";import"./AdapterDayjs-2a9281df.js";import"./advancedFormat-4f6292d4.js";import"./customParseFormat-1bc1aa07.js";import"./isBetween-c1c6beb9.js";import"./AutoCompleteType-13f5746b.js";import"./useMobilePicker-9b56b5b6.js";import"./CSSTransition-cd337b47.js";import"./useChangeMaterialRowsRequestor-fc0d44be.js";import"./FilterChangeDropdown-22e24089.js";import"./createChangeLogTemplate-fc8912a0.js";import"./AttachFile-8d552da8.js";import"./UtilDoc-6f590135.js";import"./FileDownloadOutlined-59854a55.js";import"./VisibilityOutlined-b6cd6d28.js";import"./DeleteOutlined-e668453f.js";import"./CloudUpload-0ba6431e.js";import"./utilityImages-067c3dc2.js";import"./ReusablePromptBox-e1871d49.js";import"./featureConfig-652a9f8d.js";import"./DataObject-52409c14.js";import"./Download-52c4427b.js";import"./CheckCircleOutline-e186af3e.js";import"./DeleteOutline-584dc929.js";import"./CloudDownload-9a7605e9.js";import"./AttachmentUploadDialog-43cc9099.js";var Gi={},hd=Nc;Object.defineProperty(Gi,"__esModule",{value:!0});var po=Gi.default=void 0,gd=hd(fc()),Td=Cc;po=Gi.default=(0,gd.default)((0,Td.jsx)("path",{d:"m19.07 4.93-1.41 1.41C19.1 7.79 20 9.79 20 12c0 4.42-3.58 8-8 8s-8-3.58-8-8c0-4.08 3.05-7.44 7-7.93v2.02C8.16 6.57 6 9.03 6 12c0 3.31 2.69 6 6 6s6-2.69 6-6c0-1.66-.67-3.16-1.76-4.24l-1.41 1.41C15.55 9.9 16 10.9 16 12c0 2.21-1.79 4-4 4s-4-1.79-4-4c0-1.86 1.28-3.41 3-3.86v2.14c-.6.35-1 .98-1 1.72 0 1.1.9 2 2 2s2-.9 2-2c0-.74-.4-1.38-1-1.72V2h-1C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10c0-2.76-1.12-5.26-2.93-7.07"}),"TrackChanges");const Wo=()=>{const{customError:n}=Os(),N=Z(Se=>Se.payload.payloadData),ae=Z(Se=>Se.applicationConfig),I=Z(Se=>{var oe;return(oe=Se.userManagement)==null?void 0:oe.taskData}),M=as(),x=rl(),D=new URLSearchParams(x.search).get("RequestType");return{getRequestHeaderTemplate:()=>{var R,_,j;let Se={decisionTableId:null,decisionTableName:"MDG_MAT_REQUEST_HEADER_CONFIG",version:"v2",rulePolicy:null,validityDate:null,conditions:[{"MDG_CONDITIONS.MDG_MAT_SCENARIO":(I==null?void 0:I.ATTRIBUTE_2)===((R=c)==null?void 0:R.FINANCE_COSTING)?(_=c)==null?void 0:_.FINANCE_COSTING:D||(N==null?void 0:N.RequestType)||((j=c)==null?void 0:j.CREATE),"MDG_CONDITIONS.MDG_MAT_REGION":(N==null?void 0:N.Region)||"US","MDG_CONDITIONS.MDG_MODULE":"Article"}],systemFilters:null,systemOrders:null,filterString:null};const oe=u=>{var ie,W;if(u.statusCode===200){const K={"Header Data":((W=(ie=u==null?void 0:u.data)==null?void 0:ie.result[0])==null?void 0:W.MDG_MAT_REQUEST_HEADER_CONFIG).sort((Le,Fe)=>Le.MDG_MAT_SEQUENCE_NO-Fe.MDG_MAT_SEQUENCE_NO).map(Le=>({fieldName:Le.MDG_MAT_UI_FIELD_NAME,sequenceNo:Le.MDG_MAT_SEQUENCE_NO,fieldType:Le.MDG_MAT_FIELD_TYPE,maxLength:Le.MDG_MAT_MAX_LENGTH,value:Le.MDG_MAT_DEFAULT_VALUE,visibility:Le.MDG_MAT_VISIBILITY,jsonName:Le.MDG_MAT_JSON_FIELD_NAME}))};M(_c({tab:"Request Header",data:K})),M(Oc(K))}},z=u=>{n(u)};ae.environment==="localhost"?qe(`/${Bs}${Ot.INVOKE_RULES.LOCAL}`,"post",oe,z,Se):qe(`/${Bs}${Ot.INVOKE_RULES.PROD}`,"post",oe,z,Se)}}},Ed=({setIsSecondTabEnabled:n,setIsAttachmentTabEnabled:N,requestStatus:ae,downloadClicked:I,setDownloadClicked:M})=>{var an,de,vt,jn,Jn,Tn,At,mn;const[x,ue]=i.useState({}),[D,E]=i.useState(!1),[Se,oe]=i.useState(!1),[z,R]=i.useState("success"),[_,j]=i.useState(!1),[u,ie]=i.useState([]),[W,V]=i.useState(),[a,K]=i.useState({}),[Le,Fe]=i.useState(!1),[ze,ge]=i.useState("systemGenerated"),[Te,ee]=i.useState(""),[re,H]=i.useState(""),[fe,Q]=i.useState([]),[Re,he]=i.useState(!1),te=as(),Mt=jl(),T=Z(P=>P.payload.payloadData),J=Z(P=>P.tabsData.requestHeaderData),p=Z(P=>P.tabsData.changeFieldsDT);let Ce=Z(P=>P.userManagement.roles);const L=Z(P=>P.payload.payloadData),q=Z(P=>P.userManagement.userData),b=Z(P=>{var A,Ae;return(Ae=(A=P.userManagement)==null?void 0:A.entitiesAndActivities)==null?void 0:Ae.Material}),se=Z(P=>P.request.requestHeader),O=Z(P=>P.request.salesOrgDTData),St=rl(),ye=new URLSearchParams(St.search),Oe=ye.get("reqBench"),ht=ye.get("RequestId"),{t:It}=Nl(),{getRequestHeaderTemplate:bt}=Wo(),{getChangeTemplate:dt}=bo();Pa();const{fetchOrgData:tn}=vi(),{getDtCall:Ct}=eo(),{customError:mt}=Os(),Jt=[{code:"Create",desc:"Create New Article in Application"},{code:"Change",desc:"Modify Existing Article in Application"},{code:"Extend",desc:"Extend Existing Article in Application"},{code:"Create with Upload",desc:"Create New Article with Excel Upload"},{code:"Change with Upload",desc:"Modify Existing Article with Excel Upload"},{code:"Extend with Upload",desc:"Extend Existing Article with Excel Upload"}].filter(P=>b==null?void 0:b.includes(P.code)),Rt=[{code:"Oncology",desc:""},{code:"Anesthesia/Pain Management",desc:""},{code:"Cardiovascular",desc:""}],Tt=[{code:(an=Pt)==null?void 0:an.LOGISTIC,desc:""},{code:(de=Pt)==null?void 0:de.MRP,desc:""},{code:(vt=Pt)==null?void 0:vt.WARE_VIEW_2,desc:""},{code:(jn=Pt)==null?void 0:jn.ITEM_CAT,desc:""},{code:(Jn=Pt)==null?void 0:Jn.SET_DNU,desc:""},{code:(Tn=Pt)==null?void 0:Tn.UPD_DESC,desc:""},{code:(At=Pt)==null?void 0:At.CHG_STAT,desc:""}],Ie=[{code:"High",desc:""},{code:"Medium",desc:""},{code:"Low",desc:""}];te(zs({keyName:(mn=vr)==null?void 0:mn.REQUEST_TYPE,data:Jt})),te(zs({keyName:"LeadingCat",data:Rt})),te(zs({keyName:"RequestPriority",data:Ie})),te(zs({keyName:"TemplateName",data:Tt})),!ht&&!Oe&&(te(Gr({keyName:"ReqCreatedBy",data:q==null?void 0:q.user_id})),te(Gr({keyName:"RequestStatus",data:"DRAFT"})));const pe="Basic Data",[Y,ne]=i.useState([pe]);i.useState(""),i.useState("");const[Ge,Xe]=i.useState(!0);i.useEffect(()=>{te(Ic(Y))},[te,Y]);const Et=()=>{var A,Ae;let P=!0;return L&&((A=J[Object.keys(J)])!=null&&A.length)?(Ae=J[Object.keys(J)[0]])==null||Ae.forEach(Me=>{var le;!L[Me.jsonName]&&Me.visibility===((le=ys)==null?void 0:le.MANDATORY)&&(P=!1)}):P=!1,P},y=()=>{j(!0)},ke=()=>{j(!1)},yt=()=>{var P;M(!1),Fe(!1),ge("systemGenerated"),ht||Mt((P=ns)==null?void 0:P.REQUEST_BENCH)},Dn=P=>{var A;ge((A=P==null?void 0:P.target)==null?void 0:A.value)},Zt=()=>{ze==="systemGenerated"&&(nn(),yt()),ze==="mailGenerated"&&(Wt(),yt())},nn=()=>{ee("Please wait 1-2 minutes while real-time data are downloaded into the template. Thank you for your patience."),H(!0);let P={region:T==null?void 0:T.Region,scenario:T==null?void 0:T.RequestType,matlType:"ALL",dtName:"MDG_MAT_MASS_CREATE_EXTEND_FIELD_CONFIG",version:"v1",rolePrefix:"",requestId:se!=null&&se.requestId?se==null?void 0:se.requestId:T!=null&&T.RequestId?T==null?void 0:T.RequestId:""};const A=le=>{if((le==null?void 0:le.size)==0){H(!1),ee(""),oe(!0),V("No data found for the selected criteria."),R("danger"),y();return}const Ve=URL.createObjectURL(le),$e=document.createElement("a");$e.href=Ve,$e.setAttribute("download",`${(T==null?void 0:T.RequestType)===c.EXTEND_WITH_UPLOAD?"Mass_Extend.xlsx":"Mass_Create.xlsx"}`),document.body.appendChild($e),$e.click(),document.body.removeChild($e),URL.revokeObjectURL(Ve),H(!1),ee(""),oe(!0),V(`${T!=null&&T.TemplateName?`${T.TemplateName}_Mass Change`:(T==null?void 0:T.RequestType)===c.EXTEND_WITH_UPLOAD?"Mass_Extend":"Mass_Create"}.xlsx has been downloaded successfully.`),R("success"),y(),setTimeout(()=>{Mt("/requestBench")},2600)},Ae=()=>{H(!1)},Me=`/${C}${(T==null?void 0:T.RequestType)===c.EXTEND_WITH_UPLOAD?Ot.EXCEL.DOWNLOAD_EXCEL_FOR_EXTEND:Ot.EXCEL.DOWNLOAD_EXCEL}`;qe(Me,"postandgetblob",A,Ae,P)},Wt=()=>{H(!0);let P={region:T==null?void 0:T.Region,scenario:T==null?void 0:T.RequestType,matlType:"ALL",dtName:"MDG_MAT_MASS_CREATE_EXTEND_FIELD_CONFIG",version:"v1",rolePrefix:"",requestId:se!=null&&se.requestId?se==null?void 0:se.requestId:T!=null&&T.RequestId?T==null?void 0:T.RequestId:""};const A=()=>{var le;H(!1),ee(""),oe(!0),V((le=xc)==null?void 0:le.DOWNLOAD_MAIL_INITIATED),R("success"),y(),setTimeout(()=>{var Ve;Mt((Ve=ns)==null?void 0:Ve.REQUEST_BENCH)},2600)},Ae=()=>{var le;H(!1),oe(!0),V((le=el)==null?void 0:le.ERR_DOWNLOADING_EXCEL),R("danger"),y(),setTimeout(()=>{var Ve;Mt((Ve=ns)==null?void 0:Ve.REQUEST_BENCH)},2600)},Me=`/${C}${(T==null?void 0:T.RequestType)===c.EXTEND_WITH_UPLOAD?Ot.EXCEL.DOWNLOAD_EXCEL_FOR_EXTEND_MAIL:Ot.EXCEL.DOWNLOAD_EXCEL_MAIL}`;qe(Me,"post",A,Ae,P)},lt=()=>E(!1),kt=P=>{if(u.includes("Distribution Channel")){const A=Me=>Q(Me==null?void 0:Me.body),Ae=Me=>mt(Me);qe(`/${C}/data/getDistrChan?salesOrg=${P.code}`,"get",A,Ae)}},Bt={orgData:["Plant","Sales Organization","Distribution Channel"].map(P=>({info:a[P]||{code:"",desc:""},desc:P})),selectedViews:{selectedSections:Y}},Ln=(P,A)=>{K(Ae=>({...Ae,[P]:A})),P==="Sales Organization"&&kt(A)},xt=`/Date(${Date.now()})/`,[rn,Wn]=i.useState(!1),yn=()=>{var Ve;Wn(!0);let P=bc(L==null?void 0:L.Region,Ce);te(Rc({...q,role:P})),he(!1);const A=new Date(L==null?void 0:L.ReqCreatedOn).getTime(),Ae={RequestId:se!=null&&se.requestId?se==null?void 0:se.requestId:"",Region:(L==null?void 0:L.Region)||"",MatlType:(L==null?void 0:L.MatlType)||"",ReqCreatedBy:(q==null?void 0:q.user_id)||"",ReqCreatedOn:A?`/Date(${A})/`:xt,ReqUpdatedOn:A?`/Date(${A})/`:xt,RequestType:(L==null?void 0:L.RequestType)||"",RequestDesc:(L==null?void 0:L.RequestDesc)||"",Division:(L==null?void 0:L.Division)||"",RequestStatus:"DRAFT",RequestPriority:(L==null?void 0:L.RequestPriority)||"",LeadingCat:(L==null?void 0:L.LeadingCat)||"",FieldName:((Ve=L==null?void 0:L.FieldName)==null?void 0:Ve.join("$^$"))||"",TemplateName:(L==null?void 0:L.TemplateName)||"",Json301:(L==null?void 0:L.Json301)||""},Me=$e=>{var Ft,_e,fn;if(Wn(!1),oe(!0),V($e==null?void 0:$e.message),R("success"),y(),te(to($e.body)),te(ml({keyName:vr.REQUEST_ID,data:(Ft=$e==null?void 0:$e.body)==null?void 0:Ft.requestId})),N(!0),Xe(!1),te(fl({})),te(Dc({})),(T==null?void 0:T.RequestType)===c.CREATE_WITH_UPLOAD||(T==null?void 0:T.RequestType)===c.EXTEND_WITH_UPLOAD){Fe(!0);return}if((T==null?void 0:T.RequestType)===((_e=c)==null?void 0:_e.CHANGE_WITH_UPLOAD)){he(!0);return}if((T==null?void 0:T.RequestType)===((fn=c)==null?void 0:fn.CHANGE)){const be=Lc(p==null?void 0:p["Config Data"],T==null?void 0:T.FieldName,["Material","Plant","Sales Org","Distribution Channel","Warehouse","MRP Controller"]);te(no({...p,"Config Data":be}));const jt=yc(p==null?void 0:p[T==null?void 0:T.TemplateName],T==null?void 0:T.FieldName);te(vc([...jt]))}setTimeout(()=>{te(Qs(1)),n(!0)},2500)},le=()=>{Wn(!1),oe(!0),R("error"),V("Error occured while saving Request Header"),y()};qe(`/${C}/alter/createRequestHeader`,"post",Me,le,Ae)};i.useEffect(()=>{var P;if(I){if((T==null?void 0:T.RequestType)===c.CREATE_WITH_UPLOAD||(T==null?void 0:T.RequestType)===c.EXTEND_WITH_UPLOAD){Fe(!0);return}if((T==null?void 0:T.RequestType)===((P=c)==null?void 0:P.CHANGE_WITH_UPLOAD)){he(!0);return}}},[I]);function Xt(P){return P.every(A=>A.info.code&&A.info.desc)}const we=()=>{if(!Xt(Bt.orgData))oe(!0),R("error"),V("Please choose all mandatory fields"),y();else{const A={label:"Attachments & Comments",value:"attachments&comments"},Me=[{label:"General Information",value:"generalInformation"},...Y,A];Bt.selectedViews=Me,te(Mc(Bt)),te(Qs(1)),n(!0)}};i.useEffect(()=>{bt()},[T==null?void 0:T.RequestType]);const on=(P="")=>{var le,Ve,$e,Ft;const A={materialNo:P??"",top:500,skip:0,salesOrg:((Ve=(le=O==null?void 0:O.uniqueSalesOrgList)==null?void 0:le.map(_e=>_e.code))==null?void 0:Ve.join("$^$"))||""},Ae=_e=>{(_e==null?void 0:_e.statusCode)===wt.STATUS_200&&(te(zs({keyName:Ur.RETURN_MAT_NUMBER,data:_e==null?void 0:_e.body})),te(zs({keyName:Ur.PARENT_MAT_NUMBER,data:_e==null?void 0:_e.body})))},Me=_e=>{mt(_e)};qe(`/${C}${(Ft=($e=Ot)==null?void 0:$e.DATA)==null?void 0:Ft.GET_SEARCH_PARAMS_MATERIAL_NO}`,"post",Ae,Me,A)};i.useEffect(()=>{O!=null&&O.uniqueSalesOrgList&&on()},[]);const Kn=P=>{let A={decisionTableId:null,decisionTableName:so.MDG_MAT_REGION_DIVISION_MAPPING,version:"v1",conditions:[{"MDG_CONDITIONS.MDG_MAT_REGION":P||""}]};Ct(A)};return i.useEffect(()=>{T!=null&&T.Region&&(tn(),Kn(T==null?void 0:T.Region))},[T==null?void 0:T.Region]),i.useEffect(()=>{T!=null&&T.TemplateName&&(((T==null?void 0:T.TemplateName)===Pt.MRP||(T==null?void 0:T.TemplateName)===Pt.WARE_VIEW_2)&&te(ml({keyName:"FieldName",data:void 0})),dt())},[T==null?void 0:T.TemplateName]),e("div",{children:f(Fs,{spacing:2,children:[Object.entries(J).map(([P,A])=>f(ve,{item:!0,md:12,sx:{backgroundColor:"white",maxHeight:"max-content",height:"max-content",borderRadius:"8px",border:"1px solid #E0E0E0",mt:.25,boxShadow:"0px 2px 14px 0px rgba(48, 38, 185, 0.10)",...cl},children:[e(st,{sx:{fontSize:"12px",fontWeight:"700",paddingBottom:"10px"},children:It(P)}),e(Be,{children:e(ve,{container:!0,spacing:1,children:A.filter(Ae=>Ae.visibility!=="Hidden").sort((Ae,Me)=>Ae.sequenceNo-Me.sequenceNo).map(Ae=>e(il,{isHeader:!0,field:Ae,dropDownData:x,disabled:ht||(se==null?void 0:se.requestId),requestHeader:!0},Ae.id))})}),!ht&&!(se!=null&&se.requestId)&&e(Be,{sx:{display:"flex",justifyContent:"flex-end",marginTop:"20px"},children:e(nt,{variant:"contained",color:"primary",disabled:rn||!Et(),onClick:yn,startIcon:rn?e(Oi,{size:20,color:"inherit"}):null,children:It("Save Request Header")})}),e(Jl,{})]},P)),f(Ws,{open:D,onClose:lt,children:[e(Hs,{sx:{backgroundColor:"#EAE9FF"},children:"Select Org Data"}),e(es,{children:e(ve,{container:!0,columnSpacing:1,children:u.map((P,A)=>f(i.Fragment,{children:[e(ve,{item:!0,md:4,children:f(st,{children:[P,e("span",{style:{color:"red"},children:"*"})]})}),e(ve,{item:!0,md:8,children:e(ul,{options:P==="Distribution Channel"?fe:x[P]||[],size:"small",getOptionLabel:Ae=>`${Ae.code} - ${Ae.desc}`,renderOption:(Ae,Me)=>e("li",{...Ae,children:e(st,{children:`${Me.code} - ${Me.desc}`})}),onChange:(Ae,Me)=>Ln(P,Me),renderInput:Ae=>e(Sn,{...Ae,placeholder:`Select ${P}`})})})]},A))})}),f(ts,{children:[e(nt,{onClick:lt,variant:"outlined",children:It("Cancel")}),e(nt,{variant:"contained",onClick:()=>{we()},children:It("Proceed")})]})]}),Re&&e(Wa,{downloadClicked:I,setDownloadClicked:M}),e(Ya,{onDownloadTypeChange:Zt,open:Le,downloadType:ze,handleDownloadTypeChange:Dn,onClose:yt}),e(hl,{blurLoading:re,loaderMessage:Te}),Se&&e(Cl,{openSnackBar:_,alertMsg:W,alertType:z,handleSnackBarClose:ke})]})})},Ad=(n,N,ae,I,M)=>{let x=Z(D=>D.userManagement.taskData);return{checkValidation:(D,E,Se,oe,z)=>{var Fe,ze,ge,Te,ee,re,H,fe,Q,Re,he,te,Mt,T,J,p,Ce,L;const R=(Fe=n==null?void 0:n[D])==null?void 0:Fe.payloadData,_=(ze=n==null?void 0:n[D])==null?void 0:ze.headerData;(ge=n==null?void 0:n[D])==null||ge.ManufacturerID;const j=(Te=n==null?void 0:n.payloadData)==null?void 0:Te.Region,u=(x==null?void 0:x.taskDesc)!=="Generic Article Activation Task"||!x||Object.keys(x).length===0;if(!(_!=null&&_.materialNumber)&&!u)return{missingFields:["Article number"],isValid:!1};if(!(_!=null&&_.materialType))return{missingFields:["Article Type"],isValid:!1};if(!(_!=null&&_.articleCategory))return{missingFields:["Article Category"],isValid:!1};if(!((ee=_==null?void 0:_.apparelMcat)!=null&&ee.code))return{missingFields:["Merchandising Category"],isValid:!1};if(!(_!=null&&_.globalMaterialDescription)||!R)return{missingFields:["Article Description"],isValid:!1};if((_.articleCategory.code=="11"||_.articleCategory=="11")&&!(Object.keys((_==null?void 0:_.articleComponents)??{}).length>0)||!R)return{missingFields:["Components"],isValid:!1};const ie=kr(R[S.BASIC_DATA]),W=kr(R[S.SUPPLIER_FORM]);ie.Material=_==null?void 0:_.materialNumber,ie.MatlDesc=_==null?void 0:_.globalMaterialDescription;const V=N==null?void 0:N.find(q=>{var b;return(q==null?void 0:q[j])&&(q==null?void 0:q[j][(b=_==null?void 0:_.materialType)==null?void 0:b.code])}),a=V&&V[j]&&((H=V[j][(re=_==null?void 0:_.materialType)==null?void 0:re.code])==null?void 0:H.mandatoryFields),K=a==null?void 0:a[S.BASIC_DATA];if((K==null?void 0:K.length)>0){for(const q of K)if(!ie[q==null?void 0:q.jsonName])return{missingFields:$r(K,ie),viewType:S.BASIC_DATA,isValid:!1,plant:[S.BASIC_DATA]}}const Le=a==null?void 0:a[S.SUPPLIER_FORM];if(ae.includes(S.SUPPLIER_FORM)&&(Le==null?void 0:Le.length)>0){for(const q of Le)if(!W[q==null?void 0:q.jsonName])return{missingFields:$r(Le,W),viewType:S.SUPPLIER_FORM,isValid:!1,plant:[S.SUPPLIER_FORM]}}for(const q of ae){const b=lo(E),{displayCombinations:se}=(b==null?void 0:b[q])||{};if(se&&se[0]&&(se==null?void 0:se.length)>0){const O=a==null?void 0:a[q];if(O){let St={};for(const ye of se){const Oe=(fe=R[q])==null?void 0:fe[ye];if(Oe){const ht=bl(O,Oe);((Q=Object.keys(ht))==null?void 0:Q.length)>0&&(St[ye]=Object.keys(ht))}else St[ye]=O.map(ht=>ht.fieldName);if(((Re=Object.keys(St))==null?void 0:Re.length)>0)return{missingFields:St,viewType:q,isValid:!1,plant:Object.keys(St)}}}}}if(ae.includes(S.SALES)){const q=a==null?void 0:a[S.SALES_GENERAL];let b={};if(q&&R[S.SALES_GENERAL]){const se=bl(q,(he=R[S.SALES_GENERAL])==null?void 0:he[S.SALES_GENERAL]);Object.keys(se).length>0&&(b[S.SALES_GENERAL]=Object.keys(se))}else q&&(b[S.SALES_GENERAL]=q.map(se=>se.fieldName));if(((te=Object.keys(b))==null?void 0:te.length)>0)return{missingFields:b,viewType:S.SALES,isValid:!1,plant:[S.SALES_GENERAL]}}if(ae.includes(S.SUPPLIER_FORM)){const q=a==null?void 0:a[S.SUPPLIER_FORM];let b={};if(q&&R[S.SUPPLIER_FORM]){const se=bl(q,(Mt=R[S.SUPPLIER_FORM])==null?void 0:Mt.basic);Object.keys(se).length>0&&(b[S.SUPPLIER_FORM]=Object.keys(se))}else q&&(b[S.SUPPLIER_FORM]=q.map(se=>se.fieldName));if(((T=Object.keys(b))==null?void 0:T.length)>0)return{missingFields:b,viewType:S.SUPPLIER_FORM,isValid:!1,plant:[S.SUPPLIER_FORM]}}if(ae.includes(S.PURCHASING)){const q=a==null?void 0:a[S.PURCHASING_GENERAL];let b={};if(q&&R[S.PURCHASING_GENERAL]){const se=bl(q,(J=R[S.PURCHASING_GENERAL])==null?void 0:J[S.PURCHASING_GENERAL]);Object.keys(se).length>0&&(b[S.PURCHASING_GENERAL]=Object.keys(se))}else q&&(b[S.PURCHASING_GENERAL]=q.map(se=>se.fieldName));if(((p=Object.keys(b))==null?void 0:p.length)>0)return{missingFields:b,viewType:S.PURCHASING,isValid:!1,plant:[S.PURCHASING_GENERAL]}}if(ae.includes(S.STORAGE)){const q=a==null?void 0:a[S.STORAGE_GENERAL];let b={};if(q&&R[S.STORAGE_GENERAL]){const se=bl(q,(Ce=R[S.STORAGE_GENERAL])==null?void 0:Ce[S.STORAGE_GENERAL]);Object.keys(se).length>0&&(b[S.STORAGE_GENERAL]=Object.keys(se))}else q&&(b[S.STORAGE_GENERAL]=q.map(se=>se.fieldName));if(((L=Object.keys(b))==null?void 0:L.length)>0)return{missingFields:b,viewType:S.STORAGE,isValid:!1,plant:[S.STORAGE_GENERAL]}}return{missingFields:null,isValid:!0}}}},Ho=({open:n,onClose:N,title:ae,lengthOfOrgRow:I,selectedMaterialPayload:M,materialID:x,orgRows:ue})=>{var R,_;const[D,E]=i.useState({}),Se=as(),oe=()=>{const j=[];return ue&&ue.length>0&&(ue==null||ue.forEach((u,ie)=>{var W,V,a,K,Le,Fe,ze,ge,Te,ee,re,H,fe,Q;if(ie!==(I==null?void 0:I.copyFor)){const Re=(V=(W=u.plant)==null?void 0:W.value)==null?void 0:V.code,he=((K=(a=u.plant)==null?void 0:a.value)==null?void 0:K.desc)||Re,te=(Le=u.salesOrg)==null?void 0:Le.code,Mt=((Fe=u.salesOrg)==null?void 0:Fe.desc)||te,T=(ge=(ze=u.dc)==null?void 0:ze.value)==null?void 0:ge.code,J=((ee=(Te=u.dc)==null?void 0:Te.value)==null?void 0:ee.desc)||T,p=(H=(re=u.warehouse)==null?void 0:re.value)==null?void 0:H.code,Ce=((Q=(fe=u.warehouse)==null?void 0:fe.value)==null?void 0:Q.desc)||p;if(Re){let L=`Plant: ${he||"N/A"}`;te&&(L+=` | SalesOrg: ${Mt||"N/A"}`),T&&(L+=` | DC: ${J||"N/A"}`),p&&(L+=` | Warehouse: ${Ce||"N/A"}`);let q=Re;te&&(q+=`-${te}`),T&&(q+=`-${T}`),p&&(q+=`-${p}`),j==null||j.push({code:q,desc:L,index:ie,plant:Re,salesOrg:te,dc:T,warehouse:p})}}})),j},z=()=>{var K,Le,Fe,ze,ge,Te,ee,re;if(!D.code)return;const j=ue[I.copyFor],u=(Le=(K=j==null?void 0:j.plant)==null?void 0:K.value)==null?void 0:Le.code,ie=(Fe=j==null?void 0:j.salesOrg)==null?void 0:Fe.code,W=(ge=(ze=j==null?void 0:j.dc)==null?void 0:ze.value)==null?void 0:ge.code,V=(ee=(Te=j==null?void 0:j.warehouse)==null?void 0:Te.value)==null?void 0:ee.code;if(!u)return;const a=JSON.parse(JSON.stringify(M));(re=Object.keys(a))==null||re.forEach(H=>{const fe=a[H];if(!(H===S.BASIC_DATA||H===S.SALES_GENERAL||H===S.PURCHASING_GENERAL||H===S.TAX_DATA)&&typeof fe=="object"){const Q=Object.keys(fe);if(H===S.WAREHOUSE){const Re=Q==null?void 0:Q.find(te=>te.includes(D.warehouse)),he=Q==null?void 0:Q.find(te=>te.includes(V));if(Re&&he&&he!==Re){const te=JSON.parse(JSON.stringify(fe[Re]));delete te.WarehouseId,a[H][he]={...JSON.parse(JSON.stringify(a[H][he]||{})),...te}}}else if(H===S.SALES){const Re=`${D.salesOrg}-${D.dc}`,he=`${ie}-${W}`,te=Q==null?void 0:Q.find(T=>T===Re),Mt=Q==null?void 0:Q.find(T=>T===he);if(te&&Mt&&Mt!==te){const T=JSON.parse(JSON.stringify(fe[te]));delete T.SalesId,a[H][Mt]={...JSON.parse(JSON.stringify(a[H][Mt]||{})),...T}}}else{const Re=Q==null?void 0:Q.find(te=>te.includes(D.plant)),he=Q==null?void 0:Q.find(te=>te.includes(u));if(Re&&he&&he!==Re){const te=JSON.parse(JSON.stringify(fe[Re]));te&&(delete te.SalesId,delete te.PlantId,delete te.StorageLocationId,delete te.AccountingId,he&&(a[H][he]={...JSON.parse(JSON.stringify(a[H][he]||{})),...te}))}}}}),Se(Gc({materialID:x,data:a})),N()};return f(Xl,{isOpen:n,titleIcon:e(yl,{size:"small",sx:{color:(_=(R=We)==null?void 0:R.primary)==null?void 0:_.dark,fontSize:"20px"}}),Title:ae,handleClose:()=>N(),children:[f(es,{sx:{mt:2},children:[e(st,{sx:{mb:2},children:Ii.COPY_ORG_DATA_VALUES}),e(Qt,{options:oe(),placeholder:"SELECT SOURCE ORGANIZATION",onChange:j=>E(j),value:D})]}),e(ts,{children:e(nt,{variant:"contained",size:"small",onClick:()=>z(),children:"Ok"})})]})},Sd=(n,N,ae,I,M)=>{const{customError:x}=Os(),[ue,D]=i.useState([]),[E,Se]=i.useState([]),[oe,z]=i.useState(!1),R=new URLSearchParams(location.search),_=Uc[M]||(()=>({})),j=Z(_),u=Z(W=>{var V,a;return(a=(V=W==null?void 0:W.userManagement)==null?void 0:V.taskData)==null?void 0:a.ATTRIBUTE_2}),ie=R.get("RequestType");return i.useEffect(()=>{let W={decisionTableId:null,decisionTableName:"MDG_MAT_DYN_BUTTON_CONFIG",version:"v4",rulePolicy:null,validityDate:null,conditions:[{"MDG_CONDITIONS.MDG_MAT_DYN_BTN_MODULE_NAME":M===Ds.BOM?"BOM":"Article","MDG_CONDITIONS.MDG_MAT_DYN_BTN_REQUEST_TYPE":ie||u||(j==null?void 0:j.RequestType)}],systemFilters:null,systemOrders:null,filterString:null};const V=Le=>{var Fe,ze;Le.statusCode===200&&D((ze=(Fe=Le==null?void 0:Le.data)==null?void 0:Fe.result[0])==null?void 0:ze.MDG_MAT_DYN_BUTTON_CONFIG)},a=Le=>{x(Le)},K=N.environment==="localhost"?`/${ae}/rest/v1/invoke-rules`:`/${ae}/v1/invoke-rules`;qe(K,"post",V,a,W)},[n]),i.useEffect(()=>{const W=vl(Ys.CURRENT_TASK,!0,{}),V=(W==null?void 0:W.taskDesc)||(n==null?void 0:n.taskDesc),K=ue.filter(Le=>Le.MDG_MAT_DYN_BTN_TASK_NAME===V).sort((Le,Fe)=>{const ze=Yr[Le.MDG_MAT_DYN_BTN_ACTION_TYPE]??999,ge=Yr[Fe.MDG_MAT_DYN_BTN_ACTION_TYPE]??999;return ze-ge});Se(K),(K.find(Le=>Le.MDG_MAT_DYN_BTN_BUTTON_NAME===I.SEND_BACK)||K.find(Le=>Le.MDG_MAT_DYN_BTN_BUTTON_NAME===I.CORRECTION))&&z(!0)},[ue]),{filteredButtons:E,showWfLevels:oe}},qo=n=>{var is,sn,En,Ye,it,pt,$t,Gn,zt,Un,zn,Qn,hs,gs,Cn,ms,Zn,kn,fs,bs,Rs,Vs,Fn,ut,_n,vs;const N=Z($=>$.payload),ae=Z($=>$.payload.dynamicKeyValues),I=Z($=>$.payload.changeFieldRows),M=Z($=>$.payload.selectedRows),x=(is=N==null?void 0:N.payloadData)!=null&&is.data?(En=(sn=N==null?void 0:N.payloadData)==null?void 0:sn.data)==null?void 0:En.RequestType:(Ye=N==null?void 0:N.payloadData)==null?void 0:Ye.RequestType,[ue,D]=i.useState(!1),[E,Se]=i.useState("success"),[oe,z]=i.useState(!1),[R,_]=i.useState(""),[j,u]=i.useState(""),[ie,W]=i.useState(!1),[V,a]=i.useState(""),[K,Le]=i.useState(!1),[Fe,ze]=i.useState(""),[ge,Te]=i.useState(""),[ee,re]=i.useState(!1),[H,fe]=i.useState([]),[Q,Re]=i.useState([]),[he,te]=i.useState(!1),[Mt,T]=i.useState(!1),{t:J}=Nl(),[p,Ce]=i.useState(!1),[L,q]=i.useState(!1),[b,se]=i.useState(""),[O,St]=i.useState(!1),[ye,Oe]=i.useState(!1),[ht,It]=i.useState(""),[bt,dt]=i.useState(""),[tn,Ct]=i.useState(!1),[mt,gn]=i.useState({}),[Jt,Rt]=i.useState(""),[Tt,Ie]=i.useState("");let pe=Z($=>$.userManagement.userData),Y=Z($=>$.userManagement.taskData);const ne=jl(),Ge=as(),Xe=rl(),Et=Xe.state,y=Z($=>$.payload.payloadData),ke=Z($=>$.payload.requestorPayload),yt=Z($=>$.tabsData.changeFieldsDT),Dn=(y==null?void 0:y.TemplateName)||"",{changePayloadForTemplate:Zt}=Lo(Dn),nn=new URLSearchParams(Xe.search.split("?")[1]),Wt=nn.get("RequestId"),lt=nn.get("reqBench"),kt=vl(Ys.CURRENT_TASK),Bt=typeof kt=="string"?JSON.parse(kt):kt,Ln=!(Y!=null&&Y.taskId||Bt!=null&&Bt.ATTRIBUTE_5)&&!lt,[xt,rn]=i.useState(!1),{customError:Wn}=Os(),{createFCPayload:yn}=Po(),{createPayloadFromReduxState:Xt}=yo({initialReqScreen:Ln,isReqBench:lt,remarks:Fe,userInput:b,selectedLevel:ge}),[we,on]=i.useState(!1),[Kn,an]=i.useState(!1),[de,vt]=i.useState(!1),[jn,Jn]=i.useState(""),Tn=((it=N==null?void 0:N.payloadData)==null?void 0:it.RequestType)===c.CREATE_WITH_UPLOAD||((pt=N==null?void 0:N.payloadData)==null?void 0:pt.RequestType)===c.EXTEND_WITH_UPLOAD||(($t=N==null?void 0:N.payloadData)==null?void 0:$t.RequestType)===c.CHANGE_WITH_UPLOAD,{showSnackbar:At}=bi(),mn=Z($=>$.request.tabValue),P=200,A=i.useRef(),Ae=()=>{z(!0)},Me=()=>{z(!1)},le=()=>{T(!0)},Ve=()=>{T(!1)},$e=()=>{var $;Tt===wn.SAVE?(Ve(),jt()):Tt===(($=wn)==null?void 0:$.VALIDATE)&&(Ve(),_t())},Ft=()=>{Le(!0)},_e=()=>{ze(""),Le(!1)},fn=($,Ke)=>{const Qe=$.target.value;if(on(Qe.length>=P),Qe.length>0&&Qe[0]===" ")ze(Qe.trimStart()),Ge(Pl({keyName:"Comments",data:Qe.trimStart()}));else{let Ze=Qe;ze(Ze),Ge(Pl({keyName:"Comments",data:Ze}))}},be=$=>{Te($.target.value),Ge(Pl({keyName:"Level",data:$.target.value}))},jt=()=>{var G,et,He,rt,Kt,An,gt,ft,Vn,Ts,Es,Gs,l;_e(),W(!0);var $;((G=N==null?void 0:N.payloadData)==null?void 0:G.RequestType)===c.CREATE||((et=N==null?void 0:N.payloadData)==null?void 0:et.RequestType)===c.CREATE_WITH_UPLOAD?Tt===wn.SAVE?$=`/${Yt}/massAction/createMaterialSaveAsDraft`:$=(pe==null?void 0:pe.role)==="Approver"?`/${Yt}/massAction/createBasicMaterialsApproved`:`/${Yt}/massAction/createMaterialSubmitForReview`:((He=N==null?void 0:N.payloadData)==null?void 0:He.RequestType)===c.EXTEND_WITH_UPLOAD?Tt===wn.SAVE?$=`/${Yt}${Ot.MASS_ACTION.EXTEND_MATERIAL_SAVE_AS_DRAFT}`:$=`/${Yt}${Ot.MASS_ACTION.EXTEND_MATERIAL_DIRECT_APPROVED}`:(x===c.CHANGE||x===c.CHANGE_WITH_UPLOAD)&&(Tt===wn.SAVE?$=`/${Yt}/massAction/changeMaterialSaveAsDraft`:$=(pe==null?void 0:pe.role)==="Approver"?`/${Yt}/massAction/changeBasicMaterialsApproved`:`/${Yt}/massAction/changeMaterialSubmitForReview`);const Ke=o=>{if(o.statusCode>=wt.STATUS_200&&o.statusCode<wt.STATUS_300){W(!1);let m;(pe==null?void 0:pe.role)==="Approver"?m=`Article Syndicated successfully in SAP with Article ID : ${o==null?void 0:o.body.join(", ")}`:Tt===wn.SAVE?m=o==null?void 0:o.message:m=`Request Submitted for Approval with Request ID ${o==null?void 0:o.body}`,At(m,"success"),Ae(),ne("/masterDataCockpit/materialMaster/material")}else W(!1),At(o==null?void 0:o.message,"error");Ie("")},Qe=o=>{At(o==null?void 0:o.message,"error"),W(!1),Ie("")};var Ze;Ze=((rt=N==null?void 0:N.payloadData)==null?void 0:rt.RequestType)===c.CREATE||((Kt=N==null?void 0:N.payloadData)==null?void 0:Kt.RequestType)===c.CREATE_WITH_UPLOAD||((An=N==null?void 0:N.payloadData)==null?void 0:An.RequestType)===c.EXTEND_WITH_UPLOAD?Xt(N):((gt=N==null?void 0:N.payloadData)==null?void 0:gt.RequestType)===c.CHANGE?Zt(!!lt):(Vn=(ft=N==null?void 0:N.payloadData)==null?void 0:ft.data)!=null&&Vn.RequestType||((Ts=N==null?void 0:N.payloadData)==null?void 0:Ts.RequestType)===c.CHANGE_WITH_UPLOAD?Zt(!0):(Gs=(Es=N==null?void 0:N.payloadData)==null?void 0:Es.data)!=null&&Gs.RequestType||((l=N==null?void 0:N.payloadData)==null?void 0:l.RequestType)===c.CHANGE?Xt(N):[],qe($,"post",Ke,Qe,Ze)},Gt=async $=>{var Ke,Qe,Ze,G,et;if((($==null?void 0:$.MDG_MAT_DYN_BTN_ACTION_TYPE)==="handleValidate"||($==null?void 0:$.MDG_MAT_DYN_BTN_ACTION_TYPE)==="handleValidate1")&&(((Ke=N==null?void 0:N.payloadData)==null?void 0:Ke.RequestType)===c.CREATE||((Qe=N==null?void 0:N.payloadData)==null?void 0:Qe.RequestType)===c.CREATE_WITH_UPLOAD))try{const He=await n.validateMaterials();rn(He)}catch(He){Wn(He);return}u(""),Oe("success"),gn($),dt($.MDG_MAT_DYN_BTN_COMMENT_BOX_NAME),q($.MDG_MAT_DYN_BTN_COMMENT_BOX_INPUT===((Ze=ys)==null?void 0:Ze.MANDATORY)),St($.MDG_MAT_DYN_BTN_COMMENT_BOX_INPUT===((G=ys)==null?void 0:G.MANDATORY)||$.MDG_MAT_DYN_BTN_COMMENT_BOX_INPUT==="Optional"),Rt($.MDG_MAT_DYN_BTN_ACTION_TYPE),$.MDG_MAT_DYN_BTN_BUTTON_NAME===wn.SEND_BACK||$.MDG_MAT_DYN_BTN_BUTTON_NAME===wn.CORRECTION?an(!0):an(!1),$.MDG_MAT_DYN_BTN_BUTTON_NAME===wn.SAP_SYNDICATE?vt(!0):vt(!1),$.MDG_MAT_DYN_BTN_COMMENT_BOX_INPUT===((et=ys)==null?void 0:et.MANDATORY)||$.MDG_MAT_DYN_BTN_COMMENT_BOX_INPUT==="Optional"?Nn():qn($.MDG_MAT_DYN_BTN_ACTION_TYPE,$)},tt=()=>{_e(),W(!0);const $=x===c.CREATE||x===c.CREATE_WITH_UPLOAD?`/${Yt}/massAction/createMaterialApprovalSubmit`:x===c.EXTEND||x===c.EXTEND_WITH_UPLOAD?`/${Yt}/massAction/extendMaterialApprovalSubmit`:`/${Yt}/massAction/changeMaterialApprovalSubmit`,Ke=G=>{G.statusCode>=200&&G.statusCode<300?(W(!1),At(`Request Submitted for Approval with Request ID ${G==null?void 0:G.body}`,"success"),Ge(Qs(0)),ne("/masterDataCockpit/materialMaster/material")):(W(!1),At(G==null?void 0:G.message,"error"))},Qe=()=>{W(!1),At("Failed Submitting Request.","error")};var Ze;Ze=x===c.CREATE||x===c.EXTEND||x===c.EXTEND_WITH_UPLOAD||x===c.CREATE_WITH_UPLOAD?Xt(N):Zt(!0),qe($,"post",Ke,Qe,Ze)},Xn=$=>{var He;_e(),W(!0);var Ke=x===c.CREATE||x===c.CREATE_WITH_UPLOAD?`/${Yt}/massAction/createMaterialApproved`:x===c.EXTEND||x===c.EXTEND_WITH_UPLOAD?`/${Yt}/massAction/extendMaterialApproved`:Y.ATTRIBUTE_2===c.FINANCE_COSTING?`/${Yt}/${Ot.MASS_ACTION.FINANCE_COSTING_APPROVED}`:`/${Yt}/massAction/changeMaterialApproved`;const Qe=rt=>{rt.statusCode>=200&&rt.statusCode<300?(W(!1),At($==null?void 0:$.MDG_MAT_DYN_BTN_SNACKBAR_SUCC_MSG,"success"),Ge(Qs(0)),ne("/masterDataCockpit/materialMaster/material")):(W(!1),At($==null?void 0:$.MDG_MAT_DYN_BTN_SNACKBAR_FAIL_MSG,"error"))},Ze=rt=>{At((rt==null?void 0:rt.message)||"Failed Submitting Request.","error"),W(!1)};var G;const et={requestId:(He=N==null?void 0:N.payloadData)==null?void 0:He.RequestId,taskId:(Y==null?void 0:Y.taskId)||"",taskName:(Y==null?void 0:Y.taskDesc)||"",comments:Fe||b,creationDate:Y!=null&&Y.createdOn?Pr(Y==null?void 0:Y.createdOn):null,dueDate:Y!=null&&Y.criticalDeadline?Pr(Y==null?void 0:Y.criticalDeadline):null};G=x===c.CREATE||x===c.CREATE_WITH_UPLOAD||x===c.EXTEND||x===c.EXTEND_WITH_UPLOAD?Xt(N):Zt(!0),qe(Ke,"post",Qe,Ze,Y.ATTRIBUTE_2===c.FINANCE_COSTING?et:G)},Hn=()=>{_e(),W(!0);const $=x===c.CREATE||x===c.CREATE_WITH_UPLOAD?`/${Yt}${Ot.MASS_ACTION.MATERIAL_SEND_TO_LEVEL}`:x===c.EXTEND||x===c.EXTEND_WITH_UPLOAD?`/${Yt}${Ot.MASS_ACTION.MATERIAL_SEND_TO_LEVEL}`:`/${Yt}${Ot.MASS_ACTION.MATERIAL_SEND_TO_LEVEL}`,Ke=x===c.CREATE||x===c.CREATE_WITH_UPLOAD||x===c.EXTEND||x===c.EXTEND_WITH_UPLOAD?Xt(N):Zt(!0);qe($,"post",G=>{(G==null?void 0:G.statusCode)===wt.STATUS_200?(At(G.message,"success"),Ge(xl({data:{}})),ne(ns.MY_TASK)):At(G.error,"error"),W(!1)},G=>{At(G.error,"error"),W(!1)},Ke)},ds=()=>{_e(),W(!0);const $=x===c.CREATE||x===c.CREATE_WITH_UPLOAD?`/${Yt}${Ot.MASS_ACTION.MATERIAL_SEND_TO_LEVEL}`:x===c.EXTEND||x===c.EXTEND_WITH_UPLOAD?`/${Yt}${Ot.MASS_ACTION.MATERIAL_SEND_TO_LEVEL}`:`/${Yt}${Ot.MASS_ACTION.MATERIAL_SEND_TO_LEVEL}`,Ke=x===c.CREATE||x===c.CREATE_WITH_UPLOAD||x===c.EXTEND||x===c.EXTEND_WITH_UPLOAD?Xt(N):Zt(!0);qe($,"post",G=>{(G==null?void 0:G.statusCode)===wt.STATUS_200?(At(G.message,"success"),Ge(xl({data:{}})),ne(ns.MY_TASK)):At(G.error,"error"),W(!1)},G=>{At(G.error,"error"),W(!1)},Ke)},ss=()=>{_e(),W(!0);const $=x===c.CREATE||x===c.CREATE_WITH_UPLOAD?`/${Yt}${Ot.MASS_ACTION.CREATE_MATERIAL_REJECTION}`:x===c.EXTEND||x===c.EXTEND_WITH_UPLOAD?`/${Yt}${Ot.MASS_ACTION.EXTEND_MATERIAL_REJECTION}`:`/${Yt}${Ot.MASS_ACTION.CHANGE_MATERIAL_REJECTION}`,Ke=x===c.CREATE||x===c.CREATE_WITH_UPLOAD||x===c.EXTEND||x===c.EXTEND_WITH_UPLOAD?Xt(N):Zt(!0);qe($,"post",G=>{(G==null?void 0:G.statusCode)===wt.STATUS_200?(At(G.message,"success"),Ge(xl({data:{}})),ne(ns.MY_TASK)):At(G.error,"error"),W(!1)},G=>{At(G.error,"error"),W(!1)},Ke)},_t=$=>{W(!0);const Ke=(Y==null?void 0:Y.ATTRIBUTE_2)===c.FINANCE_COSTING?`/${C}${Ot.MASS_ACTION.VALIDATE_FINANCE_COSTING}?requestId=${Wt==null?void 0:Wt.slice(3)}`:`/${C}${Ot.MASS_ACTION.VALIDATE_MATERIAL}`,Qe=(Y==null?void 0:Y.ATTRIBUTE_2)===c.FINANCE_COSTING?yn():x===c.CREATE||x===c.CREATE_WITH_UPLOAD||x===c.EXTEND||x===c.EXTEND_WITH_UPLOAD?Xt(N):x===c.CHANGE||x===c.CHANGE_WITH_UPLOAD?lt&&Wt?Zt(!0):!lt&&!Wt?Zt(!1):!lt&&Wt?Zt(!0):[]:[];qe(Ke,"post",et=>{if(W(!1),(et==null?void 0:et.statusCode)===wt.STATUS_200){if(At(($==null?void 0:$.MDG_MAT_DYN_BTN_SNACKBAR_SUCC_MSG)||(et==null?void 0:et.message),"success"),(Ln||lt)&&(x===c.CHANGE||x===c.CHANGE_WITH_UPLOAD)||x===c.CREATE_WITH_UPLOAD||x===c.EXTEND_WITH_UPLOAD||x===c.EXTEND){ne(ns.REQUEST_BENCH);return}ne(ns.MY_TASK)}else At(($==null?void 0:$.MDG_MAT_DYN_BTN_SNACKBAR_FAIL_MSG)||"Validation failed.","error")},()=>{At($==null?void 0:$.MDG_MAT_DYN_BTN_SNACKBAR_FAIL_MSG,"error"),W(!1)},Qe)},dn=()=>{_e(),W(!0);const $=x===c.CREATE||x===c.CREATE_WITH_UPLOAD?`/${Yt}/massAction/createMaterialApprovalSubmit`:x===c.EXTEND||x===c.EXTEND_WITH_UPLOAD?`/${Yt}/massAction/extendMaterialSubmitForReview`:`/${Yt}/massAction/changeMaterialApprovalSubmit`,Ke=G=>{G.statusCode>=200&&G.statusCode<300?(W(!1),At(`Request Submitted for Approval with Request ID ${G==null?void 0:G.body}`,"success"),Ae(),Ge(Qs(0)),ne("/masterDataCockpit/materialMaster/material")):At(G==null?void 0:G.message,"error")},Qe=G=>{At((G==null?void 0:G.error)||"Failed Submitting Request.","error"),W(!1)};var Ze;Ze=x===c.CREATE||x===c.CREATE_WITH_UPLOAD||x===c.EXTEND||x===c.EXTEND_WITH_UPLOAD?Xt(N):Zt(!0),qe($,"post",Ke,Qe,Ze),D(!0),Ae()},Ht=()=>{_e(),W(!0);const $=`/${Yt}${Ot.MASS_ACTION.EXTEND_MATERIAL_SAVE_AS_DRAFT}`,Ke=G=>{G.statusCode===wt.STATUS_200?(W(!1),At(G==null?void 0:G.message,"success"),ne(ns.REQUEST_BENCH)):(W(!1),At(G==null?void 0:G.message,"error"))},Qe=G=>{At(G==null?void 0:G.error,"error"),W(!1)};let Ze;Ze=Xt(N),qe($,"post",Ke,Qe,Ze)},qn=($,Ke)=>{switch($){case"handleSubmitForApproval":tt();break;case"handleSubmitForReview":dn();break;case"handleSendBack":Hn();break;case"handleCorrection":ds();break;case"handleReject":ss();break;case"Validate":Rn(Ke);break;case"handleValidate":Rn(Ke);break;case"handleSAPSyndication":Xn(Ke);break;case"handleDraft":Ht();break;case"handleSubmit":dn();break;case"handleReview":v();break;default:console.log("Unknown action type")}},v=()=>{_e(),n.setIsAccepted&&n.setIsAccepted(!0)},Rn=$=>{var Ke,Qe;Ie((Ke=wn)==null?void 0:Ke.VALIDATE),_(J((Qe=Zs)==null?void 0:Qe.VALIDATE_MSG)),x===c.CREATE||x===c.EXTEND||x===c.CREATE_WITH_UPLOAD||x===c.EXTEND_WITH_UPLOAD||(Y==null?void 0:Y.ATTRIBUTE_2)===c.FINANCE_COSTING?_t($):Ss($)},Ss=$=>{Array.isArray(I)?Is($):typeof I=="object"&&B($)},en=()=>{const $=yt==null?void 0:yt["Config Data"],Ke={};return Object.entries($).forEach(([Qe,Ze])=>{const G=Ze.filter(et=>{var He;return et.visibility===((He=ys)==null?void 0:He.MANDATORY)}).map(et=>({jsonName:et.jsonName,fieldName:et.fieldName}));if(!(G!=null&&G.some(et=>et.jsonName==="Material"))){const et=Ze.find(He=>He.jsonName==="Material");et&&G.push({jsonName:et.jsonName,fieldName:et.fieldName})}(G==null?void 0:G.length)>0&&(Ke[Qe]=G)}),Ke},us=($,Ke)=>{var Qe,Ze;if(Array.isArray(I)){const G=Dn===((Qe=Pt)==null?void 0:Qe.LOGISTIC)?[...Ke,{jsonName:"AltUnit",fieldName:"Alternative Unit of Measure"}]:Dn===((Ze=Pt)==null?void 0:Ze.UPD_DESC)?[...Ke,{jsonName:"Langu",fieldName:"Language"}]:Ke,et={};return $==null||$.forEach((He,rt)=>{var An;const Kt=(An=G==null?void 0:G.filter(gt=>!He[gt==null?void 0:gt.jsonName]||He[gt==null?void 0:gt.jsonName]===""))==null?void 0:An.map(gt=>gt==null?void 0:gt.fieldName);(Kt==null?void 0:Kt.length)>0&&(et[rt]={id:He.id,slNo:He.slNo,missingFields:Kt})}),et}else if(typeof I=="object"){let G={},et=0;return Object.keys($).forEach(He=>{$[He].forEach(rt=>{var An;const Kt=(An=Ke[He])==null?void 0:An.filter(gt=>!rt[gt.jsonName]||rt[gt.jsonName]==="").map(gt=>gt.fieldName);Kt.length>0&&(G[et]={id:rt.id,slNo:rt.slNo,type:rt.type,missingFields:Kt},et++)})}),G}},B=$=>{var G,et,He,rt;const Ke=Object.fromEntries(Object.entries(I).map(([Kt,An])=>[Kt,An.filter(gt=>{var ft;return(ft=M==null?void 0:M[Kt])==null?void 0:ft.includes(gt.id)})])),Qe=en(),Ze=us(Ke,Qe);if(Ge(Ei(Ze)),Object.keys(Ze).length>0){const Kt=Object.keys(Ze).map(ft=>{var Vn,Ts,Es;return{"Table Name":(Vn=Ze[ft])==null?void 0:Vn.type,"Sl. No":(Ts=Ze[ft])==null?void 0:Ts.slNo,"Missing Fields":(Es=Ze[ft].missingFields)==null?void 0:Es.join(", ")}});te(!0),Oe("danger"),dt("Please Fill All the Mandatory Fields : ");const An=(G=Object.keys(Kt[0]))==null?void 0:G.map(ft=>({field:ft,headerName:(ft==null?void 0:ft.charAt(0).toUpperCase())+(ft==null?void 0:ft.slice(1)),flex:ft==="Sl. No"?.5:ft==="Missing Fields"?3:1.5,align:"center",headerAlign:"center"}));Re(An);const gt=Kt==null?void 0:Kt.map(ft=>({...ft,id:Sl()}));fe(gt),re(!0),Nn(),Ge(Rl(!0))}else{if(x===((et=c)==null?void 0:et.CHANGE)||x===((He=c)==null?void 0:He.CHANGE_WITH_UPLOAD)){if(!Wt||Wt&&ke&&((rt=Object==null?void 0:Object.keys(ke))!=null&&rt.length)){le();return}_t($);return}Se("success"),u("Data Validated Successfully"),Ae(),Ge(Rl(!1)),n==null||n.setCompleted([!0,!0])}},Is=$=>{var Ze,G,et,He;const Ke=I==null?void 0:I.filter(rt=>M==null?void 0:M.includes(rt.id)),Qe=us(Ke,yt==null?void 0:yt["Mandatory Fields"]);if(Ge(Ei(Qe)),Object.keys(Qe).length>0){const rt=Object.keys(Qe).map(gt=>{var ft,Vn;return{"Sl. No":(ft=Qe[gt])==null?void 0:ft.slNo,"Missing Fields":(Vn=Qe[gt].missingFields)==null?void 0:Vn.join(", ")}});te(!0),Oe("danger"),dt("Please Fill All the Mandatory Fields : ");const Kt=(Ze=Object.keys(rt[0]))==null?void 0:Ze.map(gt=>({field:gt,headerName:(gt==null?void 0:gt.charAt(0).toUpperCase())+(gt==null?void 0:gt.slice(1)),flex:gt==="Sl. No"?.5:gt==="Missing Fields"?3:1,align:"center",headerAlign:"center"}));Re(Kt);const An=rt==null?void 0:rt.map(gt=>({...gt,id:Sl()}));fe(An),re(!0),Nn(),Ge(Rl(!0))}else{if(x===((G=c)==null?void 0:G.CHANGE)||x===((et=c)==null?void 0:et.CHANGE_WITH_UPLOAD)){if(!Wt||Wt&&ke&&((He=Object==null?void 0:Object.keys(ke))!=null&&He.length)){le();return}_t($);return}Se("success"),u("Data Validated Successfully"),Ae(),Ge(Rl(!1)),n==null||n.setCompleted([!0,!0])}},Bn=()=>{var $;if(L&&!b){Ce(!0);return}else jn==="scheduleSyndication"?A!=null&&A.current&&(($=A==null?void 0:A.current)==null||$.handlePriorityDialogClickOpen()):qn(Jt,mt);Ne()},Ne=()=>{Ct(!1),se(""),Ce(!1),q(!1)},Nn=()=>{Ct(!0)};function ls(){Ft(),Ie("")}const vn=()=>{ls()};return f(Fs,{children:[e(Yl,{sx:{position:"fixed",bottom:0,left:0,right:0},elevation:2,children:f(kc,{className:"container_BottomNav",showLabels:!0,sx:{display:"flex",justifyContent:"space-between",alignItems:"center",gap:1,width:"100%"},children:[(!Wt||lt&&(Et==null?void 0:Et.reqStatus)===((Gn=cs)==null?void 0:Gn.DRAFT))&&(y==null?void 0:y.RequestType)===c.CHANGE&&(y==null?void 0:y.TemplateName)===((zt=Pt)==null?void 0:zt.SET_DNU)&&e(Be,{sx:{flex:2,marginLeft:"90px"},children:f("span",{children:[e("strong",{children:"Note"}),": All default values for ",e("strong",{children:"Set To DNU"})," template will be fetched after ",e("strong",{children:"Validation"}),"."]})}),f(Be,{sx:{display:"flex",gap:1},children:[x!==c.EXTEND_WITH_UPLOAD&&x!==c.EXTEND&&e(Ut,{children:!Wt||lt&&((Un=Ls)!=null&&Un.includes(Et==null?void 0:Et.reqStatus))?f(Ut,{children:[e(nt,{variant:"contained",color:"primary",onClick:()=>{var $,Ke;if(Ie(wn.SAVE),_(J(($=Zs)==null?void 0:$.SAVE_AS_DRAFT_MSG)),(y==null?void 0:y.RequestType)===c.CHANGE&&(!Wt||Wt&&ke&&((Ke=Object==null?void 0:Object.keys(ke))!=null&&Ke.length))){le();return}Ft()},children:J("Save As Draft")}),((zn=N==null?void 0:N.payloadData)==null?void 0:zn.RequestType)===c.CREATE&&mn===hi.REQUEST_DETAILS&&e(bn,{title:io.VALIDATE_MANDATORY,children:e(nt,{variant:"contained",color:"primary",onClick:n==null?void 0:n.validateMaterials,children:J("Validate")})}),mn===hi.REQUEST_DETAILS&&((y==null?void 0:y.RequestType)===c.CHANGE||(y==null?void 0:y.RequestType)===c.CHANGE_WITH_UPLOAD||(y==null?void 0:y.RequestType)===c.CREATE_WITH_UPLOAD||(y==null?void 0:y.RequestType)===c.EXTEND_WITH_UPLOAD)?e(Ut,{children:e(nt,{variant:"contained",color:"primary",onClick:Rn,children:J("Validate")})}):e(Ut,{children:mn===hi.PREVIEW&&e(nt,{variant:"contained",color:"primary",onClick:vn,disabled:(y==null?void 0:y.RequestType)===((Qn=c)==null?void 0:Qn.CHANGE)||(y==null?void 0:y.RequestType)===((hs=c)==null?void 0:hs.CHANGE_WITH_UPLOAD)||(y==null?void 0:y.RequestType)===c.CREATE_WITH_UPLOAD||(y==null?void 0:y.RequestType)===c.EXTEND_WITH_UPLOAD?(y==null?void 0:y.RequestStatus)!==((gs=cs)==null?void 0:gs.VALIDATED_REQUESTOR):n==null?void 0:n.submitForApprovalDisabled,children:J("Submit")})})]}):null}),((y==null?void 0:y.RequestType)===c.EXTEND||(y==null?void 0:y.RequestType)===c.EXTEND_WITH_UPLOAD&&(!Wt||lt&&((Cn=Ls)==null?void 0:Cn.includes(Et==null?void 0:Et.reqStatus))||!lt&&Wt)||!lt&&Wt)&&((ms=n==null?void 0:n.filteredButtons)==null?void 0:ms.map(($,Ke)=>{var Kt,An,gt,ft,Vn,Ts,Es,Gs,l,o,m;const{MDG_MAT_DYN_BTN_BUTTON_NAME:Qe,MDG_MAT_DYN_BTN_BUTTON_STATUS:Ze}=$,G=Qe==="SAP Syndication"||Qe==="Submit",et=Qe==="Forward"||Qe==="Submit",He=((Kt=ae==null?void 0:ae.requestHeaderData)==null?void 0:Kt.RequestStatus)==="Validated-MDM"||(y==null?void 0:y.RequestStatus)==="Validated-MDM"||(y==null?void 0:y.RequestStatus)==="Validated-Requestor"||((An=ae==null?void 0:ae.childRequestHeaderData)==null?void 0:An.RequestStatus)==="Validated-MDM"||((gt=ae==null?void 0:ae.childRequestHeaderData)==null?void 0:gt.RequestStatus)==="Validated-Requestor"||((ft=n==null?void 0:n.childRequestHeaderData)==null?void 0:ft.RequestStatus)==="Validated-MDM"||((Vn=n==null?void 0:n.childRequestHeaderData)==null?void 0:Vn.RequestStatus)==="Validated-Requestor";let rt=Ze==="DISABLED";return G&&He&&(rt=!1),(et&&((y==null?void 0:y.RequestType)===((Ts=c)==null?void 0:Ts.CREATE)||(y==null?void 0:y.RequestType)===((Es=c)==null?void 0:Es.CREATE_WITH_UPLOAD))&&!(n!=null&&n.submitForApprovalDisabled)||((y==null?void 0:y.RequestType)===((Gs=c)==null?void 0:Gs.CHANGE)||(y==null?void 0:y.RequestType)===((l=c)==null?void 0:l.CHANGE_WITH_UPLOAD)||(y==null?void 0:y.RequestType)===((o=c)==null?void 0:o.EXTEND)||(y==null?void 0:y.RequestType)===((m=c)==null?void 0:m.EXTEND_WITH_UPLOAD))&&et&&He)&&(rt=!1),e(nt,{variant:"contained",size:"small",sx:{...zl,mr:1},disabled:rt||ie,onClick:()=>Gt($),children:$.MDG_MAT_DYN_BTN_BUTTON_NAME},Ke)}))]})]})}),e(Kl,{dialogState:tn,openReusableDialog:Nn,closeReusableDialog:Ne,dialogTitle:bt,dialogMessage:ht,handleDialogConfirm:Bn,dialogOkText:"OK",dialogSeverity:ye,showCancelButton:!0,showInputText:O,inputText:b,blurLoading:ie,setInputText:se,mandatoryTextInput:L,remarksError:p,isTable:ee,tableColumns:Q,tableRows:H,isShowWFLevel:(n==null?void 0:n.showWfLevels)&&Kn,isSyndicationBtn:de,selectedLevel:ge,handleLevelChange:be,workFlowLevels:n.workFlowLevels,setSyndicationType:Jn,syndicationType:jn,isMassSyndication:Tn}),e(Ka,{ref:A,dialogTitle:bt,setDialogTitle:dt,messageDialogMessage:j,setMessageDialogMessage:u,messageDialogSeverity:ye,setMessageDialogSeverity:Oe,handleMessageDialogClickOpen:Nn,blurLoading:ie,setBlurLoading:W,handleMessageDialogClose:Ne,createPayloadFromReduxState:Xt,successMsg:ue,setSuccessMsg:D,setTextInput:St,inputText:O,handleSnackBarOpen:Ae,taskData:Y,userData:pe,currentButtonState:mt,requestType:x,module:Ds.MAT}),e(Qa,{open:Mt,onClose:Ve,handleOk:$e,message:R}),j&&e(Cl,{openSnackBar:oe,alertMsg:j,alertType:E,handleSnackBarClose:Me}),e(hl,{blurLoading:ie,loaderMessage:V}),f(Ws,{hideBackdrop:!1,elevation:2,PaperProps:{sx:{boxShadow:"none"}},open:K,onClose:_e,maxWidth:"xl",children:[f(Hs,{sx:{justifyContent:"space-between",alignItems:"center",height:"max-content",padding:".5rem",paddingLeft:"1rem",backgroundColor:"#EAE9FF40",display:"flex"},children:[e(st,{variant:"h6",children:Tt===wn.SAVE?"Save As Draft":"Remarks"}),e(cn,{sx:{width:"max-content"},onClick:_e,children:e(Dl,{})})]}),e(es,{sx:{padding:".5rem 1rem"},children:Tt!==wn.SAVE?e(Fs,{sx:{marginTop:"16px"},children:e(Be,{sx:{minWidth:400},children:f(ro,{sx:{height:"auto"},fullWidth:!0,children:[e(Sn,{sx:{backgroundColor:"#F5F5F5","& .MuiOutlinedInput-root":{"& fieldset":{borderColor:we?(kn=(Zn=We)==null?void 0:Zn.error)==null?void 0:kn.dark:"rgba(0, 0, 0, 0.23)"},"&:hover fieldset":{borderColor:we?(bs=(fs=We)==null?void 0:fs.error)==null?void 0:bs.dark:"rgba(0, 0, 0, 0.23)"},"&.Mui-focused fieldset":{borderColor:we?(Vs=(Rs=We)==null?void 0:Rs.error)==null?void 0:Vs.dark:(ut=(Fn=We)==null?void 0:Fn.primary)==null?void 0:ut.dark}}},value:Fe,onChange:fn,inputProps:{maxLength:P},multiline:!0,placeholder:"Enter Remarks"}),e($c,{sx:{textAlign:"right",color:we?(vs=(_n=We)==null?void 0:_n.error)==null?void 0:vs.dark:"rgba(0, 0, 0, 0.6)",marginTop:"4px"},children:`${(Fe==null?void 0:Fe.length)||0}/${P}`})]})})}):e(Be,{sx:{margin:"15px"},children:e(st,{sx:{fontWeight:"200"},children:Zs.DRAFT_MESSAGE})})}),f(ts,{sx:{display:"flex",justifyContent:"end"},children:[e(nt,{sx:{width:"max-content",textTransform:"capitalize"},onClick:_e,children:J("Cancel")}),e(nt,{className:"button_primary--normal",type:"save",disabled:ie,onClick:jt,variant:"contained",children:Tt===wn.SAVE?"Yes":"Submit"})]})]})]})},kl=[{code:"5000083",desc:"Linen Mens Shirt"},{code:"GEN001",desc:"Beverage Base"},{code:"GEN002",desc:"Snack Mix"},{code:"GEN003",desc:"Cosmetic Foundation"},{code:"GEN004",desc:"Pharmaceutical Tablet"},{code:"GEN005",desc:"Electronic Component"}],Ti={5000083:[{code:"5000083001",desc:"Linen Mens Shirt, Small-38, Deep Blue"},{code:"5000083002",desc:"Linen Mens Shirt, Medium-40, Deep Blue"},{code:"5000083003",desc:"Linen Mens Shirt, Large-42, Deep Blue"}],GEN001:[{code:"VAR001",desc:"Coca Cola - 330ml Can"},{code:"VAR002",desc:"Coca Cola - 500ml Bottle"},{code:"VAR003",desc:"Pepsi - 330ml Can"},{code:"VAR004",desc:"Sprite - 330ml Can"},{code:"VAR005",desc:"Fanta - 330ml Can"}],GEN002:[{code:"VAR006",desc:"Trail Mix - Nuts & Raisins"},{code:"VAR007",desc:"Trail Mix - Chocolate Mix"},{code:"VAR008",desc:"Popcorn - Salted"},{code:"VAR009",desc:"Crackers - Cheese"},{code:"VAR010",desc:"Chips - BBQ Flavor"}],GEN003:[{code:"VAR011",desc:"Foundation - Light Beige"},{code:"VAR012",desc:"Foundation - Medium Tan"},{code:"VAR013",desc:"Foundation - Dark Brown"},{code:"VAR014",desc:"Foundation - Fair Ivory"},{code:"VAR015",desc:"Foundation - Olive Tone"}]},md=({open:n,onClose:N,articleNumber:ae,articleDetails:I,title:M="Add Components",handleSave:x,...ue})=>{const[D,E]=i.useState(null),[Se,oe]=i.useState(null),[z,R]=i.useState([]),[_,j]=i.useState(!1),[u,ie]=i.useState([]),W=u.reduce((ee,re)=>ee+re.quantity,0);i.useEffect(()=>{if(I&&n&&I.GenericMaterial&&I.GenericMaterialDescription){const ee={code:I.GenericMaterial,desc:I.GenericMaterialDescription};E(ee),oe(ee);const re=Ti[I.GenericMaterial]||[];if(R(re),I.Tocomponentsdata&&I.Tocomponentsdata.length>0){const H=I.Tocomponentsdata.map(fe=>{const Q=re.find(Re=>Re.code===fe.VariantCode);return{code:fe.VariantCode,desc:fe.VariantDescription||(Q?Q.desc:""),quantity:fe.Quantity||1,uom:fe.Uom||"EA - Each"}});ie(H)}}},[I,n]);const V=async(ee,re)=>{if(E(re),re){const H=kl.find(fe=>fe.code===re.code);H&&oe(H),j(!0),R([]),setTimeout(()=>{const fe=Ti[re.code]||[];R(fe),j(!1)},1500)}else oe(null),R([])},a=async(ee,re)=>{if(oe(re),re){const H=kl.find(fe=>fe.desc===re.desc);H&&E(H),j(!0),R([]),setTimeout(()=>{const fe=Ti[re.code]||[];R(fe),j(!1)},1500)}else E(null),R([])},K=(ee,re)=>{ie(re?H=>[...H,{...ee,quantity:1,uom:"EA - Each"}]:H=>H.filter(fe=>fe.code!==ee.code))},Le=(ee,re)=>{re<1||ie(H=>H.map(fe=>fe.code===ee?{...fe,quantity:re}:fe))},Fe=ee=>{ie(re=>re.map(H=>H.code===ee?{...H,quantity:H.quantity+1}:H))},ze=ee=>{ie(re=>re.map(H=>H.code===ee?{...H,quantity:Math.max(1,H.quantity-1)}:H))},ge=()=>{const ee={ArticleComponentsId:(I==null?void 0:I.ArticleComponentsId)||"",GenericMaterial:(D==null?void 0:D.code)||null,GenericMaterialDescription:(Se==null?void 0:Se.desc)||null,Tocomponentsdata:u.map(re=>{var fe;const H=(fe=I==null?void 0:I.Tocomponentsdata)==null?void 0:fe.find(Q=>Q.VariantCode===re.code);return{ComponentsId:(H==null?void 0:H.ComponentsId)||"",VariantCode:re.code,VariantDescription:re.desc,Quantity:re.quantity,Uom:re.uom||"EA - Each"}})};x&&x({id:ae,field:"articleComponents",value:ee}),N()},Te=()=>{E(null),oe(null),R([]),ie([]),j(!1),N()};return f(Ws,{open:n,onClose:Te,maxWidth:!1,PaperProps:{sx:{width:"900px",maxWidth:"90vw"}},...ue,children:[e(Hs,{sx:{pb:1},children:f(Be,{display:"flex",justifyContent:"space-between",alignItems:"center",children:[e(Be,{children:e(st,{variant:"h6",sx:{fontWeight:600},children:M})}),e(Be,{display:"flex",alignItems:"center",gap:2,children:e(cn,{onClick:Te,size:"small",children:e(Dl,{})})})]})}),f(es,{sx:{pt:2,pb:1},children:[e(Yl,{elevation:0,sx:{p:2,mb:2,bgcolor:"#f8f9fa",border:"1px solid #e0e0e0"},children:f(ve,{container:!0,spacing:2,children:[f(ve,{item:!0,xs:6,children:[e(st,{variant:"caption",display:"block",sx:{fontWeight:600,mb:.5,color:"#666"},children:"GENERIC MATERIAL CODE"}),e(ul,{options:kl,getOptionLabel:ee=>ee.code,value:D,onChange:V,renderInput:ee=>e(Sn,{...ee,variant:"outlined",size:"small",placeholder:"Select code...",sx:{"& .MuiInputBase-root":{bgcolor:"white",height:"36px"}}}),isOptionEqualToValue:(ee,re)=>ee.code===re.code})]}),f(ve,{item:!0,xs:6,children:[e(st,{variant:"caption",display:"block",sx:{fontWeight:600,mb:.5,color:"#666"},children:"DESCRIPTION"}),e(ul,{options:kl,getOptionLabel:ee=>ee.desc,value:Se,onChange:a,renderInput:ee=>e(Sn,{...ee,variant:"outlined",size:"small",placeholder:"Select description...",sx:{"& .MuiInputBase-root":{bgcolor:"white",height:"36px"}}}),isOptionEqualToValue:(ee,re)=>ee.desc===re.desc})]})]})}),_&&f(Be,{display:"flex",justifyContent:"center",alignItems:"center",py:2,children:[e(Oi,{size:24}),e(st,{variant:"body2",ml:1.5,color:"text.secondary",children:"Loading variants..."})]}),z.length>0&&!_&&f(Be,{children:[f(Be,{display:"flex",justifyContent:"space-between",alignItems:"center",mb:1.5,children:[e(st,{variant:"subtitle1",sx:{fontWeight:600},children:"Available Variants"}),e(Be,{display:"flex",alignItems:"center",gap:1,children:u.length>0&&e(Ri,{label:`Total Quantity: ${W}`,color:"primary",size:"small",variant:"filled"})})]}),e(Be,{sx:{maxHeight:"400px",overflowY:"auto"},children:z.map((ee,re)=>{var Q;const H=u.find(Re=>Re.code===ee.code),fe=u.find(Re=>Re.code===ee.code);return e(Pc,{elevation:0,sx:{border:H?"2px solid #1976d2":"1px solid #e0e0e0",mb:1,transition:"all 0.2s ease","&:hover":{boxShadow:1}},children:e(pc,{sx:{p:1.5,"&:last-child":{pb:1.5}},children:f(ve,{container:!0,spacing:1.5,alignItems:"center",children:[e(ve,{item:!0,xs:12,sm:6,children:e(Ai,{sx:{m:0},control:e(al,{checked:!!H,onChange:Re=>K(ee,Re.target.checked),color:"primary",size:"small"}),label:f(Be,{ml:1,children:[e(st,{variant:"body2",sx:{fontWeight:500,lineHeight:1.2},children:ee.code}),e(st,{variant:"body2",color:"text.secondary",sx:{lineHeight:1.2},children:ee.desc})]})})}),H&&f(Ut,{children:[f(ve,{item:!0,xs:6,sm:3,children:[e(st,{variant:"caption",display:"block",sx:{fontWeight:600,mb:.5,color:"#666"},children:"Quantity"}),e(Sn,{type:"number",size:"small",value:(fe==null?void 0:fe.quantity)||1,onChange:Re=>Le(ee.code,parseInt(Re.target.value)||1),InputProps:{startAdornment:e(pr,{position:"start",children:e(cn,{size:"small",onClick:()=>ze(ee.code),disabled:(fe==null?void 0:fe.quantity)<=1,sx:{p:.3},children:e(rd,{fontSize:"small"})})}),endAdornment:e(pr,{position:"end",children:e(cn,{size:"small",onClick:()=>Fe(ee.code),sx:{p:.3},children:e(ko,{fontSize:"small"})})})},inputProps:{min:1,style:{textAlign:"center",padding:"6px 8px"}},sx:{"& .MuiInputBase-root":{height:"32px"}}})]}),f(ve,{item:!0,xs:6,sm:3,children:[e(st,{variant:"caption",display:"block",sx:{fontWeight:600,mb:.5,color:"#666"},children:"UOM"}),e(Sn,{value:((Q=fe==null?void 0:fe.uom)==null?void 0:Q.split(" - ")[0])||"EA",size:"small",disabled:!0,fullWidth:!0,inputProps:{style:{textAlign:"center",padding:"6px 8px"}},sx:{"& .MuiInputBase-root":{height:"32px"}}})]})]})]})})},ee.code)})})]})]}),f(ts,{sx:{px:3,py:2,gap:1.5},children:[e(nt,{onClick:Te,variant:"outlined",sx:{minWidth:"100px"},children:"Close"}),f(nt,{onClick:ge,variant:"contained",disabled:u.length===0||!D||!Se,sx:{minWidth:"140px",fontWeight:600},children:["Save (",u.length,")"]})]})]})},Bo=n=>{var a,K,Le,Fe,ze,ge,Te,ee,re,H,fe;const{customError:N}=Os(),[ae,I]=i.useState(!1),[M,x]=i.useState(null),ue=Z(Q=>Q.payload),[D,E]=i.useState(((Le=(K=(a=ue==null?void 0:ue[n.materialID])==null?void 0:a.payloadData)==null?void 0:K.Classification)==null?void 0:Le.classification)||[]),[Se,oe]=i.useState([]),z=as();i.useEffect(()=>{var Q,Re,he,te,Mt,T,J,p;(te=(he=(Re=(Q=ue==null?void 0:ue[n.materialID])==null?void 0:Q.payloadData)==null?void 0:Re.Classification)==null?void 0:he.basic)!=null&&te.Classtype&&Mi((p=(J=(T=(Mt=ue==null?void 0:ue[n.materialID])==null?void 0:Mt.payloadData)==null?void 0:T.Classification)==null?void 0:J.basic)==null?void 0:p.Classtype,z)},[(Te=(ge=(ze=(Fe=ue==null?void 0:ue[n.materialID])==null?void 0:Fe.payloadData)==null?void 0:ze.Classification)==null?void 0:ge.basic)==null?void 0:Te.Classtype]),i.useEffect(()=>{var Q,Re,he,te,Mt,T,J,p,Ce,L,q,b,se;(te=(he=(Re=(Q=ue==null?void 0:ue[n.materialID])==null?void 0:Q.payloadData)==null?void 0:Re.Classification)==null?void 0:he.basic)!=null&&te.Classnum&&(!(D!=null&&D.length)||D!=null&&D.length&&((Mt=D[0])==null?void 0:Mt.className)!=((Ce=(p=(J=(T=ue==null?void 0:ue[n.materialID])==null?void 0:T.payloadData)==null?void 0:J.Classification)==null?void 0:p.basic)==null?void 0:Ce.Classnum))&&R((se=(b=(q=(L=ue==null?void 0:ue[n.materialID])==null?void 0:L.payloadData)==null?void 0:q.Classification)==null?void 0:b.basic)==null?void 0:se.Classnum)},[(fe=(H=(re=(ee=ue==null?void 0:ue[n.materialID])==null?void 0:ee.payloadData)==null?void 0:re.Classification)==null?void 0:H.basic)==null?void 0:fe.Classnum]),i.useEffect(()=>{z(pn({materialID:(n==null?void 0:n.materialID)||"",keyName:"",data:D??null,viewID:n==null?void 0:n.activeViewTab,itemID:"classification"}))},[D]);const R=Q=>{const Re=te=>{if((te==null?void 0:te.statusCode)===wt.STATUS_200){const Mt=te.body.map((T,J)=>({id:J+1,characteristic:T.code,description:T.desc,value:"",className:Q}));E(Mt)}},he=te=>{N(te)};qe(`/${C}${Ot.DATA.GET_CHARACTERISTICS_BY_CLASS}?className=${Q}`,"get",Re,he)},_=Q=>{x(Q),I(!0),j(Q.characteristic)},j=Q=>{const Re=te=>{(te==null?void 0:te.statusCode)===wt.STATUS_200&&oe(te.body)},he=te=>{N(te)};qe(`/${C}${Ot.DATA.GET_CHARACTERISTIC_VALUES}?characteristics=${Q}`,"get",Re,he)},u=()=>{I(!1),x(null)},ie=Q=>{x(Re=>({...Re,value:Q}))},W=()=>{E(Q=>Q.map(Re=>Re.id===M.id?{...Re,value:M.value}:Re)),I(!1)},V=[{field:"characteristic",headerName:"Characteristic",flex:1,headerClassName:"super-app-theme--header",renderHeader:()=>e(st,{variant:"body2",fontWeight:"bold",children:"Characteristic"})},{field:"description",headerName:"Description",flex:2,headerClassName:"super-app-theme--header",renderHeader:()=>e(st,{variant:"body2",fontWeight:"bold",children:"Description"})},{field:"value",flex:1,headerAlign:"left",align:"left",headerClassName:"super-app-theme--header",renderHeader:()=>f(Be,{sx:{display:"flex",alignItems:"center"},children:[e(st,{variant:"body2",fontWeight:"bold",children:"Value"}),e(st,{color:"error",sx:{ml:.5},children:"*"})]}),renderCell:Q=>e("span",{children:Array.isArray(Q.value)&&Q.value.length>1?f(Ut,{children:[Q.value[0],"...",e("span",{style:{verticalAlign:"middle"},children:e("span",{style:{cursor:"pointer",color:"#888"},title:Q.value.join(", "),children:f("svg",{xmlns:"http://www.w3.org/2000/svg",height:"22",width:"22",viewBox:"0 0 24 24",style:{verticalAlign:"middle"},children:[e("circle",{cx:"12",cy:"12",r:"10",fill:"#e0e0e0"}),e("text",{x:"12",y:"17",textAnchor:"middle",fontSize:"16",fill:"#555",fontFamily:"Arial",fontWeight:"bold",children:"i"})]})})})]}):Q.value.length===1?Q.value:"--"})},{field:"actions",headerName:"Actions",width:100,sortable:!1,headerClassName:"super-app-theme--header",renderHeader:()=>e(st,{variant:"body2",fontWeight:"bold",children:"Actions"}),renderCell:Q=>e(cn,{color:"primary",size:"small",onClick:()=>_(Q.row),children:e(od,{})})}];return f(Be,{sx:{backgroundColor:"white",border:`1px solid ${We.hover.hoverbg}`,borderRadius:"8px",boxShadow:"0px 2px 14px 0px rgba(48, 38, 185, 0.10)",px:3,py:2,mb:3,mt:2},children:[e(st,{sx:{fontSize:"12px",fontWeight:"700",paddingBottom:"10px"},children:n==null?void 0:n.characteristicDetails[0]}),D.length>0?f("div",{style:{width:"100%",height:D.length*53+56},children:[" ",e(Ll,{rows:D,columns:V,hideFooter:!0,disableColumnMenu:!0,disableSelectionOnClick:!0,sx:{fontFamily:"'Roboto','Helvetica','Arial',sans-serif",fontSize:"0.875rem","& .MuiDataGrid-columnHeaders":{backgroundColor:"#f3f3fc",borderBottom:"1px solid #dcdcdc"},"& .MuiDataGrid-columnHeaderTitle":{fontWeight:600,fontSize:"0.875rem"},"& .MuiDataGrid-cell":{color:"#333",fontSize:"0.875rem"}}})]}):e(st,{variant:"body2",sx:{color:"#888"},children:"No characteristic data available."}),f(Ws,{open:ae,onClose:u,fullWidth:!0,maxWidth:"sm",children:[e(Hs,{children:"Edit Entry"}),f(es,{children:[e(Sn,{margin:"dense",label:"Characteristic",fullWidth:!0,value:(M==null?void 0:M.characteristic)||"",disabled:!0}),e(Sn,{margin:"dense",label:"Description",fullWidth:!0,value:(M==null?void 0:M.description)||"",disabled:!0}),e(ul,{multiple:!0,freeSolo:!1,options:Se,getOptionLabel:Q=>Q.code||"",value:Se.filter(Q=>((M==null?void 0:M.value)||[]).includes(Q.code)),onChange:(Q,Re)=>{const he=Re.map(te=>te.code);ie(he)},renderInput:Q=>e(Sn,{...Q,margin:"dense",label:"Value",fullWidth:!0,variant:"outlined"})})]}),f(ts,{children:[e(nt,{onClick:u,color:"secondary",children:"Cancel"}),e(nt,{onClick:W,variant:"contained",children:"Save"})]})]})]})},fd="/assets/UOM_BOX-89f6ce52.png",Nd=n=>{const N=n.materialID||"SupplierForm",ae=Z(Se=>{var oe,z,R,_,j;return((j=(_=(R=(z=(oe=Se.payload)==null?void 0:oe[N])==null?void 0:z.payloadData)==null?void 0:R[S.SUPPLIER_FORM])==null?void 0:_.basic)==null?void 0:j.Togenericarraydata)||[]}),[I,M]=i.useState([{Json401:"",Json402:"",Json403:"",GenericArrayId:null}]),x=as();i.useEffect(()=>{ae&&ae.length>0&&M(ae)},[ae]);const ue=(Se,oe,z)=>{let R=JSON.parse(JSON.stringify(I));R[Se][oe]=z,M(R),x(pn({materialID:N,viewID:S.SUPPLIER_FORM,itemID:"basic",keyName:"Togenericarraydata",data:R}))},D=()=>{const Se=[...I,{Json401:"",Json402:"",Json403:""}];M(Se),x(pn({materialID:N,viewID:S.SUPPLIER_FORM,itemID:"basic",keyName:"Togenericarraydata",data:Se}))},E=Se=>{const oe=I.filter((z,R)=>R!==Se);M(oe),x(pn({materialID:N,viewID:S.SUPPLIER_FORM,itemID:"basic",keyName:"Togenericarraydata",data:oe}))};return f(Be,{sx:{mt:3},children:[e(st,{fontWeight:700,mb:2,children:"Prepack Article Details"}),f(ve,{container:!0,spacing:2,sx:{fontWeight:600,mb:1},children:[e(ve,{item:!0,xs:4,children:e(st,{children:"Shirt Size"})}),e(ve,{item:!0,xs:4,children:e(st,{children:"Shirt Colour"})}),e(ve,{item:!0,xs:3,children:e(st,{children:"Shirt Quantity"})}),e(ve,{item:!0,xs:1})," "]}),I.map((Se,oe)=>f(ve,{container:!0,spacing:2,alignItems:"center",mb:1,children:[e(ve,{item:!0,xs:4,children:e(Sn,{fullWidth:!0,size:"small",placeholder:"Size",value:Se.Json401,onChange:z=>ue(oe,"Json401",z.target.value),disabled:n.disabled})}),e(ve,{item:!0,xs:4,children:e(Sn,{fullWidth:!0,size:"small",placeholder:"Colour",value:Se.Json402,onChange:z=>ue(oe,"Json402",z.target.value),disabled:n.disabled})}),e(ve,{item:!0,xs:3,children:e(Sn,{fullWidth:!0,size:"small",placeholder:"Quantity",value:Se.Json403,onChange:z=>ue(oe,"Json403",z.target.value),disabled:n.disabled})}),e(ve,{item:!0,xs:1,children:oe!==0&&e(bn,{title:"Remove Row",children:e(cn,{onClick:()=>E(oe),color:"error",children:e($o,{})})})})]},oe)),e(nt,{variant:"outlined",onClick:D,children:"+ Add Row"})]})},Cd=n=>{var R,_,j,u;const N=n.materialID||"SupplierForm",ae=Z(ie=>{var W,V,a,K,Le;return((Le=(K=(a=(V=(W=ie.payload)==null?void 0:W[N])==null?void 0:V.payloadData)==null?void 0:a[S.CHARACTERISTIC])==null?void 0:K.basic)==null?void 0:Le.ToCharCreationValueData)||[]}),I=Z(ie=>ie.payload),M=(u=(j=(_=(R=I==null?void 0:I[N])==null?void 0:R.payloadData)==null?void 0:_[S.CHARACTERISTIC])==null?void 0:j.basic)==null?void 0:u.Json451,x=[{Json466:"WHITE",Json467:"White",CharCreationValueId:null},{Json466:"BROWN",Json467:"Brown",CharCreationValueId:null},{Json466:"GREY",Json467:"Grey",CharCreationValueId:null},{Json466:"MAROON",Json467:"maroon",CharCreationValueId:null}],[ue,D]=i.useState([{Json466:"",Json467:"",CharCreationValueId:null}]),E=as();i.useEffect(()=>{ae&&ae.length>0&&D(ae)},[ae]),i.useEffect(()=>{M&&(ue==null?void 0:ue.length)<2&&!(ae!=null&&ae.length)&&(D(x),E(pn({materialID:N,viewID:S.CHARACTERISTIC,itemID:"basic",keyName:"ToCharCreationValueData",data:x})))},[M]);const Se=(ie,W,V)=>{let a=JSON.parse(JSON.stringify(ue));a[ie][W]=V,D(a),E(pn({materialID:N,viewID:S.CHARACTERISTIC,itemID:"basic",keyName:"ToCharCreationValueData",data:a}))},oe=()=>{const ie=[...ue,{Json466:"",Json467:"",CharCreationValueId:null}];D(ie),E(pn({materialID:N,viewID:S.CHARACTERISTIC,itemID:"basic",keyName:"ToCharCreationValueData",data:ie}))},z=ie=>{const W=ue.filter((V,a)=>a!==ie);D(W),E(pn({materialID:N,viewID:S.CHARACTERISTIC,itemID:"basic",keyName:"ToCharCreationValueData",data:W}))};return f(Be,{sx:{mt:3},children:[e(st,{fontWeight:700,mb:2,children:"Characteristic Details"}),f(ve,{container:!0,spacing:2,sx:{fontWeight:600,mb:1},children:[e(ve,{item:!0,xs:4,children:e(st,{children:"Characteristic Value"})}),e(ve,{item:!0,xs:4,children:e(st,{children:"Description"})}),e(ve,{item:!0,xs:1})," "]}),ue.map((ie,W)=>f(ve,{container:!0,spacing:2,alignItems:"center",mb:1,children:[e(ve,{item:!0,xs:4,children:e(Sn,{fullWidth:!0,size:"small",placeholder:"Size",value:ie.Json466,onChange:V=>Se(W,"Json466",V.target.value),disabled:n.disabled})}),e(ve,{item:!0,xs:4,children:e(Sn,{fullWidth:!0,size:"small",placeholder:"Colour",value:ie.Json467,onChange:V=>Se(W,"Json467",V.target.value),disabled:n.disabled})}),e(ve,{item:!0,xs:1,children:W!==0&&e(bn,{title:"Remove Row",children:e(cn,{onClick:()=>z(W),color:"error",children:e($o,{})})})})]},W)),e(nt,{variant:"outlined",onClick:oe,children:"+ Add Row"})]})},_d=n=>{var te,Mt;const N=Z(T=>T.payload),ae=Z(T=>{var J;return(J=T==null?void 0:T.request)==null?void 0:J.materialRows}),I=(Mt=(te=N==null?void 0:N[n.materialID])==null?void 0:te.headerData)==null?void 0:Mt.orgData;Z(T=>T.userManagement.taskData);const[M,x]=i.useState({}),ue=Z(T=>{var J;return(J=T.payload.payloadData)==null?void 0:J.RequestType}),[D,E]=i.useState([]),[Se,oe]=i.useState([]),[z,R]=i.useState(!1),_=as(),{getDtCall:j,dtData:u}=Uo(),{customError:ie}=Os(),{t:W}=Nl(),V=i.useRef({});function a(T,J){const p={plant:"Plant",salesOrg:"SalesOrg",dc:"Distribution Channel",sloc:"Storage Location",mrpProfile:"MRP Profile",warehouse:"Warehouse",purchasingOrg:"Purchasing Org",store:"Store",distributionCenter:"Distribution Center",supplier:"Supplier"},Ce=T.split("-");return J.map((L,q)=>{const b=p[L],se=Ce[q]||"N/A";return`${b} - ${se}`}).join(", ")}const K=T=>[...new Set(T)].join("$^$"),Le=T=>{const J=new Map;return T.forEach(({CountryName:p,Country:Ce})=>{J.set(Ce,p)}),Array.from(J,([p,Ce])=>({Country:p,CountryName:Ce}))},Fe=T=>T.map(({Country:J})=>J).join("$^$"),ze=T=>{const J=`/${Yt}${Ot.TAX_DATA.GET_COUNTRY_SALESORG}`,p={salesOrg:"I00X"};R(!1),qe(J,"post",q=>{const b=q==null?void 0:q.body,se=Le(b),O=Fe(se);ge(O)},q=>{R(!1),ie(el.NO_DATA_AVAILABLE)},p)},ge=T=>{const J=`/${Yt}${Ot.TAX_DATA.GET_TAX_COUNTRY}`;qe(J,"post",q=>{var Oe,ht,It,bt;R(!1);const b=q==null?void 0:q.body,se=((bt=(It=(ht=(Oe=N[n==null?void 0:n.materialID])==null?void 0:Oe.payloadData)==null?void 0:ht.TaxData)==null?void 0:It.TaxData)==null?void 0:bt.TaxDataSet)||[],O={},St=b.filter(dt=>dt.TaxType);St.forEach(({TaxClass:dt,TaxClassDesc:tn})=>{O[dt]=tn});const ye=se.map(dt=>{const tn=St.filter(mt=>mt.TaxType===dt.TaxType&&mt.Country===dt.Country).map(mt=>({code:mt.TaxClass,desc:mt.TaxClassDesc}));let Ct=dt.SelectedTaxClass;return Ct&&O[Ct.TaxClass]&&(Ct={...Ct,TaxClassDesc:O[Ct.TaxClass]}),{...dt,options:tn,SelectedTaxClass:Ct}});St.forEach(({TaxType:dt,SequenceNo:tn,Country:Ct,TaxClass:mt,TaxClassDesc:gn})=>{if(!ye.some(Rt=>Rt.TaxType===dt&&Rt.Country===Ct)){const Rt=St.filter(Tt=>Tt.TaxType===dt&&Tt.Country===Ct).map(Tt=>({code:Tt.TaxClass,desc:Tt.TaxClassDesc}));ye.push({TaxType:dt,SequenceNo:tn,Country:Ct,options:Rt,SelectedTaxClass:null})}}),_(pn({materialID:(n==null?void 0:n.materialID)||"",keyName:"TaxDataSet",data:ye,viewID:"TaxData",itemID:"TaxData"}))},q=>{R(!1),ie(el.NO_DATA_AVAILABLE)},{country:T})},Te=ae==null?void 0:ae.find(T=>(T==null?void 0:T.id)===n.materialID);i.useEffect(()=>{var T,J,p,Ce,L,q,b,se,O,St,ye,Oe;if(I){const ht=!!((J=(T=N[n.materialID])==null?void 0:T.headerData)!=null&&J.refMaterialData),It=lo(I,(p=N==null?void 0:N[n.materialID])==null?void 0:p.payloadData,n==null?void 0:n.materialID,_);if(x(It),!ht&&!n.isDisplay&&It.hasOwnProperty(S.SALES)&&((Ce=n==null?void 0:n.selectedViews)!=null&&Ce.includes(S.SALES))&&It[S.SALES].reduxCombinations.forEach((bt,dt)=>{ue!==c.EXTEND&&re({comb:bt,dt:Xs.SALES_DIV_PRICE_MAPPING},I[dt])}),(!ht&&((L=n==null?void 0:n.selectedViews)!=null&&L.includes(S.SALES))||(q=n==null?void 0:n.selectedViews)!=null&&q.includes(S.ACCOUNTING)||(b=n==null?void 0:n.selectedViews)!=null&&b.includes(S.COSTING))&&I.forEach((bt,dt)=>{ue!==c.EXTEND&&!n.isDisplay&&H({combinations:It,index:dt,dt:Xs.REG_PLNT_INSPSTK_MAPPING},bt)}),ue===c.EXTEND){let bt={copyPayload:{payloadData:(se=N[n.materialID])==null?void 0:se.payloadData,unitsOfMeasureData:(O=N[n.materialID])==null?void 0:O.unitsOfMeasureData,additionalData:(St=N[n.materialID])==null?void 0:St.additionalData}};N!=null&&N.OrgElementDefaultValues&&!n.isDisplay&&ee(It,bt)}else ue===c.CREATE&&(ht||N!=null&&N.OrgElementDefaultValues)&&!n.isDisplay&&ee(It,(Oe=(ye=N[n.materialID])==null?void 0:ye.headerData)==null?void 0:Oe.refMaterialData)}else x({})},[I]),i.useEffect(()=>{if(I){const T=[...new Set(I==null?void 0:I.map(J=>{var p;return(p=J.salesOrg)==null?void 0:p.code}))];K(T),ze()}else(n==null?void 0:n.moduleName)==="Article"&&n.activeViewTab==="Basic Data"&&ze()},[I,n==null?void 0:n.callGetCountryBasedonSalesOrg,n==null?void 0:n.moduleName,n.activeViewTab]),i.useEffect(()=>{var T,J,p,Ce,L,q,b,se,O,St,ye,Oe,ht,It,bt,dt,tn,Ct,mt,gn,Jt,Rt,Tt,Ie,pe,Y,ne,Ge,Xe,Et,y,ke,yt,Dn,Zt,nn,Wt,lt,kt,Bt,Ln,xt,rn,Wn,yn,Xt,we,on,Kn;if(u){if(((T=u.customParam)==null?void 0:T.dt)===Xs.SALES_DIV_PRICE_MAPPING&&((J=n==null?void 0:n.selectedViews)!=null&&J.includes(S.SALES))){const an=(Ce=Object.keys((p=u==null?void 0:u.data)==null?void 0:p.result[0]))!=null&&Ce.length?(se=(b=(q=(L=u==null?void 0:u.data)==null?void 0:L.result)==null?void 0:q[0])==null?void 0:b.MDG_MAT_SALESDIV_PRCICEGRP_MAPPING[0])==null?void 0:se.MDG_MAT_MATERIAL_PRICING_GROUP:"";ue!==c.EXTEND&&ue!==c.CREATE_WITH_UPLOAD&&an&&Q((O=u.customParam)==null?void 0:O.comb,"MatPrGrp",an,"Sales")}else if(((St=u.customParam)==null?void 0:St.dt)===Xs.REG_PLNT_INSPSTK_MAPPING){let an=(ye=u.customParam)==null?void 0:ye.combinations,de=(Oe=u.customParam)==null?void 0:Oe.org;if(an!=null&&an.hasOwnProperty(S.SALES)&&((ht=n==null?void 0:n.selectedViews)!=null&&ht.includes(S.SALES))){const vt=(bt=Object.keys((It=u==null?void 0:u.data)==null?void 0:It.result[0]))!=null&&bt.length?(mt=(Ct=(tn=(dt=u==null?void 0:u.data)==null?void 0:dt.result)==null?void 0:tn[0])==null?void 0:Ct.MDG_MAT_REG_PLNT_INSPSTK_ITMCATGRP_MAPPING[0])==null?void 0:mt.MDG_MAT_ITEM_CAT_GROUP:"";ue!==c.EXTEND&&ue!==c.CREATE_WITH_UPLOAD&&vt&&Q(((gn=de==null?void 0:de.salesOrg)==null?void 0:gn.code)+"-"+((Rt=(Jt=de==null?void 0:de.dc)==null?void 0:Jt.value)==null?void 0:Rt.code),"ItemCat",vt,"Sales")}if(an.hasOwnProperty(S.PURCHASING)&&((Tt=n==null?void 0:n.selectedViews)!=null&&Tt.includes(S.PURCHASING))){const vt=(pe=Object.keys((Ie=u==null?void 0:u.data)==null?void 0:Ie.result[0]))!=null&&pe.length?(Xe=(Ge=(ne=(Y=u==null?void 0:u.data)==null?void 0:Y.result)==null?void 0:ne[0])==null?void 0:Ge.MDG_MAT_REG_PLNT_INSPSTK_ITMCATGRP_MAPPING[0])==null?void 0:Xe.MDG_MAT_POST_TO_INSP_STOCK:"";ue!==c.EXTEND&&ue!==c.CREATE_WITH_UPLOAD&&vt&&Q((y=(Et=de==null?void 0:de.plant)==null?void 0:Et.value)==null?void 0:y.code,"IndPostToInspStock",vt,"Purchasing")}if(an.hasOwnProperty(S.ACCOUNTING)&&((ke=n==null?void 0:n.selectedViews)!=null&&ke.includes(S.ACCOUNTING))){const vt=(Dn=Object.keys((yt=u==null?void 0:u.data)==null?void 0:yt.result[0]))!=null&&Dn.length?(lt=(Wt=(nn=(Zt=u==null?void 0:u.data)==null?void 0:Zt.result)==null?void 0:nn[0])==null?void 0:Wt.MDG_MAT_REG_PLNT_INSPSTK_ITMCATGRP_MAPPING[0])==null?void 0:lt.MDG_MAT_PRICE_UNIT:"";ue!==c.EXTEND&&ue!==c.CREATE_WITH_UPLOAD&&vt&&Q((Bt=(kt=de==null?void 0:de.plant)==null?void 0:kt.value)==null?void 0:Bt.code,"PriceUnit",vt,"Accounting")}if(an.hasOwnProperty(S.COSTING)&&((Ln=n==null?void 0:n.selectedViews)!=null&&Ln.includes(S.COSTING))){const vt=(rn=Object.keys((xt=u==null?void 0:u.data)==null?void 0:xt.result[0]))!=null&&rn.length?(we=(Xt=(yn=(Wn=u==null?void 0:u.data)==null?void 0:Wn.result)==null?void 0:yn[0])==null?void 0:Xt.MDG_MAT_REG_PLNT_INSPSTK_ITMCATGRP_MAPPING[0])==null?void 0:we.MDG_MAT_COSTING_LOT_SIZE:"";ue!==c.EXTEND&&ue!==c.CREATE_WITH_UPLOAD&&vt&&Q((Kn=(on=de==null?void 0:de.plant)==null?void 0:on.value)==null?void 0:Kn.code,"Lotsizekey",vt,"Costing")}}}},[u]);const ee=(T,J)=>{var L;let p=(L=N[n.materialID])==null?void 0:L.payloadData,Ce=N==null?void 0:N.OrgElementDefaultValues;Object.keys(T).forEach(q=>{var se;let b=(se=T[q])==null?void 0:se.reduxCombinations;b==null||b.forEach(O=>{var Oe,ht,It,bt,dt,tn,Ct,mt,gn,Jt;const St=(Oe=p==null?void 0:p[q])==null?void 0:Oe[O],ye=!St||Object.keys(St).length===0;q!==S.BASIC_DATA&&((ht=J==null?void 0:J.copyPayload)!=null&&ht.payloadData[q]||Ce)&&ye&&(n!=null&&n.allTabsData[q])&&(Object.keys(n==null?void 0:n.allTabsData[q]).forEach(Rt=>{const Tt=n==null?void 0:n.allTabsData[q][Rt];Array.isArray(Tt)&&Tt.forEach(Ie=>{var Y,ne,Ge,Xe;const pe=Ie==null?void 0:Ie.jsonName;if(pe){const Et=(ne=(Y=J==null?void 0:J.copyPayload)==null?void 0:Y.payloadData[q])==null?void 0:ne[pe],y=((Xe=(Ge=Ce==null?void 0:Ce[q])==null?void 0:Ge[O])==null?void 0:Xe[pe])||"";let ke=Ml(pe,Et,n==null?void 0:n.allTabsData[q],y);ke&&_(pn({materialID:n==null?void 0:n.materialID,viewID:q,itemID:O,keyName:pe,data:ke}))}})}),q===S.SALES&&(_(pn({materialID:n==null?void 0:n.materialID,viewID:S.TAX_DATA,itemID:S.TAX_DATA,data:(dt=(bt=(It=J==null?void 0:J.copyPayload)==null?void 0:It.payloadData)==null?void 0:bt.TaxData)==null?void 0:dt.TaxData})),Object.keys((n==null?void 0:n.allTabsData[S.SALES_GENERAL])||{}).forEach(Rt=>{const Tt=n==null?void 0:n.allTabsData[S.SALES_GENERAL][Rt];Array.isArray(Tt)&&Tt.forEach(Ie=>{var Y,ne,Ge,Xe,Et;const pe=Ie==null?void 0:Ie.jsonName;if(pe){const y=(Ge=(ne=(Y=J==null?void 0:J.copyPayload)==null?void 0:Y.payloadData[S.SALES_GENERAL])==null?void 0:ne[S.SALES_GENERAL])==null?void 0:Ge[pe];let ke=Ml(pe,y,n==null?void 0:n.allTabsData[S.SALES_GENERAL],(Et=(Xe=Ce==null?void 0:Ce[S.SALES_GENERAL])==null?void 0:Xe[S.SALES_GENERAL])==null?void 0:Et[pe]);ke&&_(pn({materialID:n==null?void 0:n.materialID,viewID:S.SALES_GENERAL,itemID:S.SALES_GENERAL,keyName:pe,data:ke}))}})})),q===S.PURCHASING&&((Ct=(tn=J==null?void 0:J.copyPayload)==null?void 0:tn.payloadData)!=null&&Ct[S.PURCHASING_GENERAL])&&Object.keys((n==null?void 0:n.allTabsData[S.PURCHASING_GENERAL])||{}).forEach(Rt=>{const Tt=n==null?void 0:n.allTabsData[S.PURCHASING_GENERAL][Rt];Array.isArray(Tt)&&Tt.forEach(Ie=>{var Y,ne,Ge,Xe,Et;const pe=Ie==null?void 0:Ie.jsonName;if(pe){const y=(Ge=(ne=(Y=J==null?void 0:J.copyPayload)==null?void 0:Y.payloadData[S.PURCHASING_GENERAL])==null?void 0:ne[S.PURCHASING_GENERAL])==null?void 0:Ge[pe];let ke=Ml(pe,y,n==null?void 0:n.allTabsData[S.PURCHASING_GENERAL],(Et=(Xe=Ce==null?void 0:Ce[S.PURCHASING_GENERAL])==null?void 0:Xe[S.PURCHASING_GENERAL])==null?void 0:Et[pe]);ke&&_(pn({materialID:n==null?void 0:n.materialID,viewID:S.PURCHASING_GENERAL,itemID:S.PURCHASING_GENERAL,keyName:pe,data:ke}))}})}),q===S.STORAGE&&((Jt=(gn=(mt=J==null?void 0:J.copyPayload)==null?void 0:mt.payloadData)==null?void 0:gn[S.STORAGE_GENERAL])!=null&&Jt[S.STORAGE_GENERAL])&&Object.keys((n==null?void 0:n.allTabsData[S.STORAGE_GENERAL])||{}).forEach(Rt=>{const Tt=n==null?void 0:n.allTabsData[S.STORAGE_GENERAL][Rt];Array.isArray(Tt)&&Tt.forEach(Ie=>{var Y,ne,Ge,Xe,Et;const pe=Ie==null?void 0:Ie.jsonName;if(pe){const y=(Ge=(ne=(Y=J==null?void 0:J.copyPayload)==null?void 0:Y.payloadData[S.STORAGE_GENERAL])==null?void 0:ne[S.STORAGE_GENERAL])==null?void 0:Ge[pe];let ke=Ml(pe,y,n==null?void 0:n.allTabsData[S.STORAGE_GENERAL],(Et=(Xe=Ce==null?void 0:Ce[S.STORAGE_GENERAL])==null?void 0:Xe[S.STORAGE_GENERAL])==null?void 0:Et[pe]);ke&&_(pn({materialID:n==null?void 0:n.materialID,viewID:S.STORAGE_GENERAL,itemID:S.STORAGE_GENERAL,keyName:pe,data:ke}))}})}))})})},re=(T,J)=>{var Ce,L;let p={decisionTableId:null,decisionTableName:Xs.SALES_DIV_PRICE_MAPPING,version:"v1",conditions:[{"MDG_CONDITIONS.MDG_MAT_SALES_ORG":(Ce=J==null?void 0:J.salesOrg)==null?void 0:Ce.code,"MDG_CONDITIONS.MDG_MAT_DIVISION":(L=N==null?void 0:N.payloadData)==null?void 0:L.Division}]};T.org=J,j(p,T)},H=(T,J)=>{var Ce,L,q;let p={decisionTableId:null,decisionTableName:Xs.REG_PLNT_INSPSTK_MAPPING,version:"v1",conditions:[{"MDG_CONDITIONS.MDG_MAT_REGION":(Ce=N==null?void 0:N.payloadData)==null?void 0:Ce.Region,"MDG_CONDITIONS.MDG_MAT_PLANT":(q=(L=J==null?void 0:J.plant)==null?void 0:L.value)==null?void 0:q.code}]};T.org=J,j(p,T)};i.useEffect(()=>{var p;if(!I||!M[n.activeViewTab])return;const{reduxCombinations:T=[]}=M[n.activeViewTab],J=T==null?void 0:T.map(Ce=>{var L;return((L=n==null?void 0:n.missingValidationPlant)==null?void 0:L.includes(Ce))&&n.mandatoryFailedView===n.activeViewTab});if(oe(J),n.missingValidationPlant||(p=n.missingValidationPlant)!=null&&p.length){const Ce=n.missingValidationPlant[0],L=V==null?void 0:V.current[Ce];L&&(L!=null&&L.scrollIntoView)&&setTimeout(()=>L.scrollIntoView({behavior:"smooth",block:"center"}),700)}},[n.activeViewTab,I,n==null?void 0:n.missingValidationPlant,M]);const fe=(T,J,p)=>(Ce,L)=>{oe(q=>({...q,[p]:L}))},Q=(T,J,p,Ce)=>{_(pn({materialID:(n==null?void 0:n.materialID)||"",keyName:J||"",data:p??null,viewID:Ce,itemID:T}))},Re=(T,J)=>T.some(p=>J.includes(p.fieldName)),he=i.useMemo(()=>{var O,St,ye,Oe,ht,It,bt,dt,tn,Ct,mt,gn,Jt,Rt,Tt;const T=M[n.activeViewTab]||{},{displayCombinations:J=[],reduxCombinations:p=[],requiredKeys:Ce=[]}=T,L=Object.entries((n==null?void 0:n.basicDataTabDetails)||{}),q=(O=n.allTabsData)!=null&&O.hasOwnProperty(S.SALES_GENERAL)?Object.entries(n.allTabsData[S.SALES_GENERAL]):[],b=(St=n.allTabsData)!=null&&St.hasOwnProperty(S.PURCHASING_GENERAL)?Object.entries(n.allTabsData[S.PURCHASING_GENERAL]):[],se=(ye=n.allTabsData)!=null&&ye.hasOwnProperty(S.STORAGE_GENERAL)?Object.entries(n.allTabsData[S.STORAGE_GENERAL]):[];return n.activeViewTab==="Basic Data"?f(Ut,{children:[L.map(Ie=>{var pe;return f(ve,{item:!0,md:12,sx:{backgroundColor:"white",maxHeight:"max-content",height:"max-content",borderRadius:"8px",border:`1px solid ${(pe=n==null?void 0:n.missingValidationPlant)!=null&&pe.includes(S.BASIC_DATA)&&Re(Ie[1],n.missingFields)&&!(Te!=null&&Te.validated)?We.error.dark:We.hover.hoverbg} `,mt:.25,boxShadow:"0px 2px 14px 0px rgba(48, 38, 185, 0.10)",...cl},children:[e(ve,{container:!0,children:e(st,{sx:{fontSize:"12px",fontWeight:"700",paddingBottom:"10px"},children:Ie[0]})}),e(Be,{children:e(ve,{container:!0,spacing:1,children:[...Ie[1]].filter(Y=>Y.visibility!=="Hidden").sort((Y,ne)=>Y.sequenceNo-ne.sequenceNo).map(Y=>e(il,{disabled:n==null?void 0:n.disabled,field:Y,dropDownData:n.dropDownData,materialID:n==null?void 0:n.materialID,selectedMaterialNumber:n==null?void 0:n.selectedMaterialNumber,viewName:n==null?void 0:n.activeViewTab,plantData:"basic",missingFields:Array.isArray(n.missingFields)?n.missingFields:[]},Y.fieldName))})})]},Ie[0])}),(n==null?void 0:n.moduleName)==="Article"&&e(Jr,{materialID:n.materialID,selectedMaterialNumber:n==null?void 0:n.selectedMaterialNumber,loading:z})]}):n.activeViewTab===S.SUPPLIER_FORM||n.activeViewTab==="Characteristic"||n.activeViewTab==="Merchant Input"?f(Ut,{children:[L.map(Ie=>{var pe;return f(ve,{item:!0,md:12,sx:{backgroundColor:"white",maxHeight:"max-content",height:"max-content",borderRadius:"8px",border:`1px solid ${(pe=n==null?void 0:n.missingValidationPlant)!=null&&pe.includes(S.SUPPLIER_FORM)&&!(Te!=null&&Te.validated)?We.error.dark:We.hover.hoverbg} `,mt:.25,boxShadow:"0px 2px 14px 0px rgba(48, 38, 185, 0.10)",...cl},children:[e(ve,{container:!0,children:e(st,{sx:{fontSize:"12px",fontWeight:"700",paddingBottom:"10px"},children:Ie[0]})}),f(Be,{children:[e(ve,{container:!0,spacing:2,children:[...Ie[1]].filter(Y=>Y.visibility!=="Hidden").filter(Y=>Y.fieldType!=="Table").sort((Y,ne)=>Y.sequenceNo-ne.sequenceNo).reduce((Y,ne,Ge,Xe)=>{if(Ge%2===0){const Et=[ne];Ge+1<Xe.length&&Et.push(Xe[Ge+1]),Y.push(Et)}return Y},[]).map((Y,ne)=>{var Ge,Xe,Et,y;return e(ve,{item:!0,xs:12,children:f(ve,{container:!0,spacing:2,alignItems:"center",children:[e(ve,{item:!0,xs:Y[1]?5:12,children:e(bn,{title:(Ge=Y[0])!=null&&Ge.fieldTooltip?(Xe=Y[0])==null?void 0:Xe.fieldTooltip:"",placement:"top",children:e(Be,{sx:{width:"100%"},children:e(il,{disabled:n==null?void 0:n.disabled,field:Y[0],dropDownData:n.dropDownData,materialID:n==null?void 0:n.materialID,selectedMaterialNumber:n==null?void 0:n.selectedMaterialNumber,viewName:n==null?void 0:n.activeViewTab,plantData:"basic",width:"35vw"},Y[0].fieldName)})})}),Y[1]&&f(Ut,{children:[e(ve,{item:!0,xs:2,sx:{textAlign:"center",display:"flex",alignItems:"center",justifyContent:"center"},children:e(st,{sx:{color:"gray",fontSize:"16px",fontWeight:"bold"},children:"|"})}),e(ve,{item:!0,xs:5,children:e(bn,{title:(Et=Y[1])!=null&&Et.fieldTooltip?(y=Y[1])==null?void 0:y.fieldTooltip:"",placement:"top",children:e(Be,{sx:{width:"100%"},children:e(il,{disabled:n==null?void 0:n.disabled,field:Y[1],dropDownData:n.dropDownData,materialID:n==null?void 0:n.materialID,selectedMaterialNumber:n==null?void 0:n.selectedMaterialNumber,viewName:n==null?void 0:n.activeViewTab,plantData:"basic",width:"35vw"},Y[1].fieldName)})})})]})]})},`row-${ne}`)})}),n.activeViewTab==="Supplier Form"&&Ie[0]==="All of the standard SAP UOM information"&&e(Be,{sx:{display:"flex",justifyContent:"start",alignItems:"center",mt:2,mb:2},children:e("img",{src:fd,alt:"UOM Box",style:{maxWidth:"80%",height:"auto",borderRadius:"8px",boxShadow:"0px 2px 8px rgba(0, 0, 0, 0.1)"}})})]})]},Ie[0])}),e(ve,{md:12,sx:{backgroundColor:"white",maxHeight:"max-content",height:"max-content",borderRadius:"8px",border:`1px solid ${We.hover.hoverbg}`,mt:.25,boxShadow:"0px 2px 14px 0px rgba(48, 38, 185, 0.10)",...cl},children:f(Be,{children:[n.activeViewTab===S.SUPPLIER_FORM&&e(Nd,{disabled:n==null?void 0:n.disabled,materialID:n.materialID}),n.activeViewTab==="Characteristic"&&e(Cd,{disabled:n==null?void 0:n.disabled,materialID:n.materialID})]})},"supplierTable")]}):n.activeViewTab===S.CLASSIFICATION?f(Ut,{children:[f(ve,{md:12,sx:{backgroundColor:"white",maxHeight:"max-content",height:"max-content",borderRadius:"8px",border:`1px solid ${We.hover.hoverbg} `,mt:.25,boxShadow:"0px 2px 14px 0px rgba(48, 38, 185, 0.10)",...cl},children:[e(ve,{container:!0,children:e(st,{sx:{fontSize:"12px",fontWeight:"700",paddingBottom:"10px"},children:(Oe=L[0])==null?void 0:Oe[0]})}),e(Be,{children:e(ve,{container:!0,spacing:1,children:[...(ht=L[0])==null?void 0:ht[1]].filter(Ie=>Ie.visibility!==ys.HIDDEN1).sort((Ie,pe)=>Ie.sequenceNo-pe.sequenceNo).map(Ie=>e(Ut,{children:(Ie==null?void 0:Ie.visibility)==ys.HIDDEN?e(il,{classNum:n==null?void 0:n.classNum,disabled:n==null?void 0:n.disabled,field:Ie,dropDownData:n.dropDownData,materialID:n==null?void 0:n.materialID,selectedMaterialNumber:n==null?void 0:n.selectedMaterialNumber,viewName:n==null?void 0:n.activeViewTab,plantData:"basic",matType:n==null?void 0:n.matType,missingFields:Array.isArray(n.missingFields)?n.missingFields:[]},Ie.fieldName):e(il,{classNum:n==null?void 0:n.classNum,disabled:n==null?void 0:n.disabled,field:Ie,dropDownData:n.dropDownData,materialID:n==null?void 0:n.materialID,selectedMaterialNumber:n==null?void 0:n.selectedMaterialNumber,viewName:n==null?void 0:n.activeViewTab,plantData:"basic",matType:n==null?void 0:n.matType,missingFields:Array.isArray(n.missingFields)?n.missingFields:[]},Ie.fieldName)}))})})]},(It=L[0])==null?void 0:It[0]),(n==null?void 0:n.moduleName)===Ds.MAT&&e(Ha,{characteristicDetails:L[1],materialID:n.materialID,selectedMaterialNumber:n==null?void 0:n.selectedMaterialNumber,classNum:n==null?void 0:n.classNum,disabled:n.disabled,dropDownData:n.dropDownData,activeViewTab:n.activeViewTab}),(n==null?void 0:n.moduleName)===Ds.ART&&e(Bo,{characteristicDetails:L[1],materialID:n.materialID,selectedMaterialNumber:n==null?void 0:n.selectedMaterialNumber,classNum:n==null?void 0:n.classNum,disabled:n.disabled,dropDownData:n.dropDownData,activeViewTab:n.activeViewTab})]}):J.length?f(Ut,{children:[n.activeViewTab===S.SALES&&f(Ut,{children:[e(Jr,{materialID:n.materialID,selectedMaterialNumber:n==null?void 0:n.selectedMaterialNumber,loading:z}),(q==null?void 0:q.length)>0&&e(gi,{materialID:n.materialID,selectedMaterialNumber:n==null?void 0:n.selectedMaterialNumber,GeneralFields:q,disabled:n.disabled,dropDownData:n.dropDownData,viewName:(bt=S)==null?void 0:bt.SALES_GENERAL,isMandatoryFailed:((dt=n==null?void 0:n.missingValidationPlant)==null?void 0:dt.includes(S.SALES_GENERAL))&&!(Te!=null&&Te.validated),missingFields:(tn=n.missingFields)==null?void 0:tn[S.SALES_GENERAL]})]}),n.activeViewTab===S.PURCHASING&&f(Ut,{children:[" ",(b==null?void 0:b.length)>0&&e(gi,{materialID:n.materialID,selectedMaterialNumber:n==null?void 0:n.selectedMaterialNumber,GeneralFields:b,disabled:n.disabled,dropDownData:n.dropDownData,viewName:(Ct=S)==null?void 0:Ct.PURCHASING_GENERAL,isMandatoryFailed:((mt=n==null?void 0:n.missingValidationPlant)==null?void 0:mt.includes(S.PURCHASING_GENERAL))&&!(Te!=null&&Te.validated),missingFields:(gn=n.missingFields)==null?void 0:gn[S.PURCHASING_GENERAL]})]}),n.activeViewTab===S.STORAGE&&f(Ut,{children:[" ",(se==null?void 0:se.length)>0&&e(gi,{materialID:n.materialID,selectedMaterialNumber:n==null?void 0:n.selectedMaterialNumber,GeneralFields:se,disabled:n.disabled,dropDownData:n.dropDownData,viewName:(Jt=S)==null?void 0:Jt.STORAGE_GENERAL,isMandatoryFailed:((Rt=n==null?void 0:n.missingValidationPlant)==null?void 0:Rt.includes(S.STORAGE_GENERAL))&&!(Te!=null&&Te.validated),missingFields:(Tt=n.missingFields)==null?void 0:Tt[S.STORAGE_GENERAL]})]}),J.map((Ie,pe)=>{var Y,ne,Ge,Xe,Et;return f(oo,{ref:y=>{V.current[Ie]=y},sx:{marginBottom:"20px",boxShadow:3,borderRadius:"10px",borderColor:(Y=n==null?void 0:n.missingValidationPlant)!=null&&Y.includes(Ie)&&n.mandatoryFailedView===n.activeViewTab&&!(Te!=null&&Te.validated)?(Ge=(ne=We)==null?void 0:ne.error)==null?void 0:Ge.dark:(Xe=We)==null?void 0:Xe.primary.white},onChange:fe(Ie,Ce,pe),expanded:Se[pe]===!0,children:[e(ao,{expandIcon:e(co,{}),sx:{backgroundColor:We.primary.whiteSmoke,borderRadius:"10px",padding:"8px 16px","&:hover":{backgroundColor:We.hover.hoverbg}},children:e(st,{variant:"h6",sx:{fontWeight:"bold"},children:a(Ie,Ce)})}),e(uo,{children:((Et=D[pe])==null?void 0:Et.value)===1?f(Be,{sx:{display:"flex",alignItems:"center",justifyContent:"center",minHeight:"200px"},children:[" ",e(Oi,{})]}):L.map(y=>f(ve,{item:!0,md:12,sx:{backgroundColor:"white",maxHeight:"max-content",height:"max-content",borderRadius:"8px",border:"1px solid #E0E0E0",mt:.25,boxShadow:"0px 2px 14px 0px rgba(48, 38, 185, 0.10)",...cl},children:[e(ve,{container:!0,children:e(st,{sx:{fontSize:"12px",fontWeight:"700",paddingBottom:"10px"},children:W(y[0])})}),e(Be,{children:e(ve,{container:!0,spacing:1,children:[...y[1]].filter(ke=>ke.visibility!=="Hidden").sort((ke,yt)=>ke.sequenceNo-yt.sequenceNo).map(ke=>{var yt;return e(il,{disabled:n==null?void 0:n.disabled,field:ke,dropDownData:n.dropDownData,materialID:n==null?void 0:n.materialID,selectedMaterialNumber:n==null?void 0:n.selectedMaterialNumber,viewName:n==null?void 0:n.activeViewTab,plantData:p[pe],missingFields:(yt=n.missingFields)==null?void 0:yt[p[pe]]},ke.fieldName)})})})]},y[0]))})]},pe)})]}):e(st,{variant:"body2",sx:{margin:"20px",color:"gray"},children:"No Org Data selected."})},[M,n.activeViewTab,n.basicDataTabDetails,D,n.materialID,n.missingValidationPlant,Se]);return e(Ut,{children:he})},Od=ho(()=>({customTabs:{"& .MuiTabs-scroller":{overflowX:"auto !important",overflowY:"hidden !important"}}})),$l=[{code:"00",desc:"Single article"},{code:"01",desc:"Generic article"},{code:"10",desc:"Sales set"},{code:"11",desc:"Prepack"},{code:"12",desc:"Display"},{code:"22",desc:"Group Article"}],ol=[],Id=[{code:"I00X",desc:"Purchasing Org 1"},{code:"1710",desc:"Purchasing Org 2"},{code:"C01",desc:"Purchasing Org 3"}],bd=[],Rd=[],Md=[],xd=n=>{var Hi,qi,Bi,Fi,Vi,wi,ji,Ji,Xi,zi,Yi,Ki,Qi,Zi,er,tr,nr,sr,lr,ir,rr,or,cr,ar,dr,ur;const N=Od(),{customError:ae}=Os(),I=as(),{getDynamicWorkflowDT:M}=Go(),{fetchMaterialFieldConfig:x}=vi(),{getNextDisplayDataForCreate:ue}=yi(),{fetchValuationClassData:D}=Ro(),E=Z(t=>t.payload.payloadData),Se=E==null?void 0:E.RequestType,oe=Z(t=>t.request.salesOrgDTData),z=Z(t=>t.applicationConfig),R=Z(t=>t.paginationData),_=Z(t=>t.payload),j=Z(t=>t.request.requestHeader),u=Z(t=>t.request.materialRows),ie=Z(t=>t.payload.payloadData),W=Z(t=>{var s;return((s=t.AllDropDown)==null?void 0:s.dropDown)||{}}),V=Z(t=>t.tabsData.allTabsData);Z(t=>t.userManagement.userData),Z(t=>t.userManagement.roles);let a=Z(t=>t.userManagement.taskData);const K=Z(t=>t.tabsData.allMaterialFieldConfigDT),Le=rl(),Fe=new URLSearchParams(Le.search),ze=Fe.get("reqBench"),ge=Fe.get("RequestId"),[Te,ee]=i.useState(!1),[re,H]=i.useState(!1),[fe,Q]=i.useState(!1),[Re,he]=i.useState(0),[te,Mt]=i.useState(null),[T,J]=i.useState(null),[p,Ce]=i.useState(ol),[L,q]=i.useState({data:{},isVisible:!1}),[b,se]=i.useState(u||[]),O=Z(t=>t.selectedSections.selectedSections),[St,ye]=i.useState(!!(b!=null&&b.length)),[Oe,ht]=i.useState(!1),[It,bt]=i.useState(!1),[dt,tn]=i.useState(""),{fetchTabSpecificData:Ct}=Mo(),[mt,gn]=i.useState([]),[Jt,Rt]=i.useState(0),[Tt,Ie]=i.useState(null),[pe,Y]=i.useState(!1),[ne,Ge]=i.useState(!0),[Xe,Et]=i.useState(!1),[y,ke]=i.useState(b.length+1),[yt,Dn]=i.useState(0),[Zt,nn]=i.useState(u.length>0),[Wt,lt]=i.useState({}),[kt,Bt]=i.useState({}),[Ln,xt]=i.useState(0),[rn,Wn]=i.useState([]),[yn,Xt]=i.useState({}),[we,on]=i.useState({}),[Kn,an]=i.useState(!1),[de,vt]=i.useState(""),[jn,Jn]=i.useState("Supplier Form"),[Tn,At]=i.useState(!1);let mn={id:0,salesOrg:null,dc:{value:null,options:[]},plant:{value:null,options:[]},sloc:{value:null,options:[]},warehouse:{value:null,options:[]},mrpProfile:null,distributionCenter:{value:null,options:[]},purchasingOrg:{value:null,options:[]},store:{value:null,options:[]},supplier:{value:null,options:[]}};const[P,A]=i.useState([mn]),[Ae,Me]=i.useState(!1),[le,Ve]=i.useState(null),[$e,Ft]=i.useState("yes"),[_e,fn]=i.useState([]),[be,jt]=i.useState(null),Gt=(Hi=_==null?void 0:_[be])==null?void 0:Hi.headerData,[tt,Xn]=i.useState("success"),[Hn,ds]=i.useState(!1),[ss,_t]=i.useState([]),[dn,Ht]=i.useState(""),[qn,v]=i.useState(""),[Rn,Ss]=i.useState(""),en=Z(t=>t.tabsData.articleViews),{checkValidation:us}=Ad(_,K,p),{t:B}=Nl(),{getDtCall:Is,dtData:Bn}=Uo(),[Ne,Nn]=i.useState(!1),[ls,vn]=i.useState({}),[is,sn]=i.useState({}),[En,Ye]=i.useState("10"),it="403055",[pt,$t]=i.useState(0),[Gn,zt]=i.useState(!1),Un=Z(t=>{var s;return((s=t.materialDropDownData)==null?void 0:s.dropDown)||{}}),zn=[{region:"US",temp:"MIDDLE EAST HUB"},{region:"US",temp:"SOUTHERN HUB"},{region:"EUR",temp:"NORTH HUB"},{region:"EUR",temp:"CENTRAL HUB"},{region:"EUR",temp:"WEST HUB"}],[Qn,hs]=i.useState(null),[gs,Cn]=i.useState(""),[ms,Zn]=i.useState(""),kn=i.useRef(P),[fs,bs]=i.useState(!1),Rs=(qi=_==null?void 0:_[be])==null?void 0:qi.payloadData,{fetchDataAndDispatch:Vs}=xo(),Fn=["Sales Org","Plant","Distribution Channel","Storage Location","Warehouse"],[ut,_n]=i.useState({}),[vs,$]=i.useState(!1),[Ke,Qe]=i.useState(0),[Ze,G]=i.useState({"Material No":!1}),{getContryBasedOnPlant:et}=Do({doAjax:qe,customError:ae,fetchDataAndDispatch:Vs,destination_ArticleMgmt:C}),[He,rt]=i.useState([]),{filteredButtons:Kt,showWfLevels:An}=Sd(a,z,Bs,wn),gt=xi(Kt,[qs.HANDLE_SUBMIT_FOR_APPROVAL,qs.HANDLE_SAP_SYNDICATION,qs.HANDLE_SUBMIT_FOR_REVIEW]),{showSnackbar:ft}=bi(),Vn=40;i.useEffect(()=>{var t,s,r,h,d,g,k,X,Pe,ce,me,w;if(se(u),nn((u==null?void 0:u.length)>0),be&&vt(be),(u==null?void 0:u.length)>0&&ge&&!(be||Rn)){jt((t=u==null?void 0:u[0])==null?void 0:t.id),Ss((s=u==null?void 0:u[0])==null?void 0:s.materialNumber),gl((h=(r=u==null?void 0:u[0])==null?void 0:r.materialType)==null?void 0:h.code),vt((d=u==null?void 0:u[0])==null?void 0:d.id);const Ue=go(_),at=To(Ue);let Nt=JSON.parse(JSON.stringify(at));I(Eo(Nt)),I(ml({keyName:"selectedMaterialID",data:(g=u==null?void 0:u[0])==null?void 0:g.id})),(Pe=(X=_==null?void 0:_[(k=u==null?void 0:u[0])==null?void 0:k.id])==null?void 0:X.Tochildrequestheaderdata)!=null&&Pe.ChildRequestId&&I(ml({keyName:"childRequestId",data:(w=(me=_==null?void 0:_[(ce=u==null?void 0:u[0])==null?void 0:ce.id])==null?void 0:me.Tochildrequestheaderdata)==null?void 0:w.ChildRequestId}))}},[be,u]),i.useEffect(()=>{var t,s;(t=u==null?void 0:u[0])!=null&&t.materialType&&(Zl({row:u[0]}),Tl(u)&&(Ge(!1),ye(!1))),u!=null&&u.length&&xt((s=u==null?void 0:u.at(-1))==null?void 0:s.lineNumber),I(si({keyName:"VarOrdUn",data:Wc})),!(u!=null&&u.length)&&!ge&&Ts(),I(si({keyName:"Json452",data:Hc})),I(si({keyName:"Json451",data:[{code:"COLOUR",desc:"TSHIRT_COLOUR"}]}))},[]),i.useEffect(()=>{var t,s,r,h;if(Bn&&((t=Bn==null?void 0:Bn.customParam)==null?void 0:t.dt)===Xs.MDG_ORG_ELEMENT_DEFAULT_VALUE){const d=qc((h=(r=(s=Bn==null?void 0:Bn.data)==null?void 0:s.result)==null?void 0:r[0])==null?void 0:h.MDG_ORG_ELEMENT_DEFAULT_VALUE_ACTION_TYPE);I(Bc({data:d}))}},[Bn]);const Ts=()=>{let t={decisionTableId:null,decisionTableName:Xs.MDG_ORG_ELEMENT_DEFAULT_VALUE,version:"v1",conditions:[{"MDG_CONDITIONS.MDG_REGION":E==null?void 0:E.Region,"MDG_CONDITIONS.MDG_SCENARIO":E==null?void 0:E.RequestType}]};Is(t,{dt:Xs.MDG_ORG_ELEMENT_DEFAULT_VALUE})};i.useEffect(()=>{const t=async()=>{var s,r;try{const h=await M(Se,E==null?void 0:E.Region,"",(r=(s=_[be])==null?void 0:s.Tochildrequestheaderdata)==null?void 0:r.MaterialGroupType,a==null?void 0:a.ATTRIBUTE_3,"v1","MDG_ARTICLE_DYNAMIC_WF_DT",Ds.ART);rt(h)}catch(h){ae(h)}};Se&&(E!=null&&E.Region)&&be&&(a!=null&&a.ATTRIBUTE_3)&&t()},[Se,E==null?void 0:E.Region,be,a==null?void 0:a.ATTRIBUTE_3]),i.useEffect(()=>{$e==="no"&&(_n({}),Ve(null),Ie(null))},[$e]),i.useEffect(()=>{var t,s,r,h,d,g;be&&(V!=null&&V[S.BASIC_DATA])&&((t=_[be])!=null&&t.headerData.refMaterialData||_!=null&&_.OrgElementDefaultValues)&&!((h=(r=(s=_[be])==null?void 0:s.payloadData)==null?void 0:r["Basic Data"])!=null&&h.basic)&&!ge&&Es((g=(d=_[be])==null?void 0:d.headerData)==null?void 0:g.refMaterialData)},[be,V]),i.useEffect(()=>{(b==null?void 0:b.length)===0&&ye(!1)},[b,Ae]),i.useEffect(()=>{le!=null&&le.code?Gs(le==null?void 0:le.code,"extended"):Bt(t=>({...t,"Sales Org":[]})),_n(t=>({...t,[ps.SALES_ORG]:null}))},[le]),i.useEffect(()=>{var t;(t=ut==null?void 0:ut["Material Type"])!=null&&t.code&&(As(),Ve(null),Ie(null))},[(Bi=ut==null?void 0:ut["Material Type"])==null?void 0:Bi.code]),i.useEffect(()=>{["Distribution Channel","Plant"].forEach(s=>{_n(r=>({...r,[s]:""})),kt[s]&&Bt(r=>({...r,[s]:[]}))})},[(Fi=ut==null?void 0:ut["Sales Org"])==null?void 0:Fi.code]),i.useEffect(()=>{["Storage Location","Warehouse"].forEach(s=>{_n(r=>({...r,[s]:""})),kt[s]&&Bt(r=>({...r,[s]:[]}))})},[(Vi=ut==null?void 0:ut.Plant)==null?void 0:Vi.code]),i.useEffect(()=>{ut[ps.SALES_ORG]&&(U(),_n(t=>({...t,[ps.DIST_CHNL]:null,[ps.PLANT]:null})))},[ut[ps.SALES_ORG]]);const Es=t=>{var g,k,X,Pe,ce,me;const s=((X=(k=(g=t==null?void 0:t.copyPayload)==null?void 0:g.payloadData)==null?void 0:k["Basic Data"])==null?void 0:X.basic)||{},r=((ce=(Pe=_==null?void 0:_.OrgElementDefaultValues)==null?void 0:Pe[S.BASIC_DATA])==null?void 0:ce[S.BASIC_DATA])||{};new Set([...Object.keys(r),...Object.keys(s)]).forEach(w=>{const Ue=(s==null?void 0:s[w])||"",at=(r==null?void 0:r[w])||"",Nt=w==="Division"?E==null?void 0:E.Division:Ml(w,Ue,V["Basic Data"],at);I(pn({materialID:be,viewID:"Basic Data",itemID:"basic",keyName:w,data:Nt}))});let d=(me=t==null?void 0:t.copyPayload)==null?void 0:me.unitsOfMeasureData;if(d!=null&&d.length){let w=[];d==null||d.forEach(Ue=>{w.push({...Ue,id:(Ue==null?void 0:Ue.id)||w.length+1})}),I(Wr({materialID:be,data:w}))}},Gs=(t,s)=>{const r=d=>{G(g=>({...g,"Sales Org":!1})),(d==null?void 0:d.statusCode)===wt.STATUS_200&&Bt(s==="notExtended"?g=>({...g,"Sales Org":d.body}):g=>({...g,"Sales Org":(d==null?void 0:d.body.length)>0?d.body:[]}))},h=()=>{G(d=>({...d,"Sales Org":!1}))};G(d=>({...d,"Sales Org":!0})),qe(`/${C}/data/${s==="notExtended"?"getSalesOrgNotExtended":"getSalesOrgExtended"}?materialNo=${t}&region=${E==null?void 0:E.Region}`,"get",r,h)},l=(t,s,r)=>{G(k=>({...k,Plant:!0}));const h=k=>{G(X=>({...X,Plant:!1})),(k==null?void 0:k.statusCode)===wt.STATUS_200&&Bt(s==="notExtended"?X=>({...X,Plant:k.body}):X=>({...X,Plant:(k==null?void 0:k.body.length)>0?k.body:[]}))},d=()=>{G(k=>({...k,Plant:!1}))},g=r?`&salesOrg=${r.code}`:"";qe(`/${C}/data/${s==="notExtended"?"getPlantNotExtended":"getPlantExtended"}?materialNo=${t}&region=${E==null?void 0:E.Region}${g}`,"get",h,d)},o=(t,s,r)=>{G(k=>({...k,Warehouse:!0}));const h=k=>{G(X=>({...X,Warehouse:!1})),(k==null?void 0:k.statusCode)===wt.STATUS_200&&Bt(s==="notExtended"?X=>({...X,Warehouse:k.body}):X=>({...X,Warehouse:(k==null?void 0:k.body.length)>0?k.body:[]}))},d=()=>{G(k=>({...k,Warehouse:!1}))},g=r?`&plant=${r.code}`:"";qe(`/${C}/data/${s==="notExtended"?"getWarehouseNotExtended":"getWarehouseExtended"}?materialNo=${t}&region=${E==null?void 0:E.Region}${g}`,"get",h,d)},m=(t,s,r)=>{G(g=>({...g,"Storage Location":!0}));const h=g=>{G(k=>({...k,"Storage Location":!1})),(g==null?void 0:g.statusCode)===wt.STATUS_200&&Bt(k=>{var X;return{...k,[(X=ps)==null?void 0:X.STORAGE_LOC]:g.body||[]}})},d=g=>{ae(g),G(k=>({...k,"Storage Location":!1}))};qe(`/${C}/data/getStorageLocationExtended?plant=${s==null?void 0:s.code}&materialNo=${t}&region=${E==null?void 0:E.Region}&salesOrg=${r==null?void 0:r.code}`,"get",h,d)},U=()=>{var r;G(h=>({...h,"Distribution Channel":!0}));const t=h=>{G(d=>({...d,"Distribution Channel":!1})),(h==null?void 0:h.statusCode)===wt.STATUS_200&&Bt(d=>{var g;return{...d,[(g=ps)==null?void 0:g.DIST_CHNL]:h.body&&(h==null?void 0:h.body)}})},s=h=>{ae(h),G(d=>({...d,"Distribution Channel":!1}))};qe(`/${C}/data/getDistributionChannelExtended?materialNo=${le==null?void 0:le.code}&salesOrg=${(r=ut[ps.SALES_ORG])==null?void 0:r.code}`,"get",t,s)};i.useEffect(()=>{["Mrp Profile"].forEach(Yo),(u==null?void 0:u.length)===0&&(Se===c.CREATE||Se===c.CREATE_WITH_UPLOAD)&&Me(!0),F(),Ee()},[]),i.useEffect(()=>{var s,r,h,d,g,k,X,Pe,ce,me,w,Ue,at,Nt,un,$n,qt,rs,$s,Ps,tl,nl,sl,js,Js,hn;kn.current=P,P.some(Ms=>{var xn,os,_l;return((xn=Ms==null?void 0:Ms.salesOrg)==null?void 0:xn.code)&&!((_l=(os=Ms==null?void 0:Ms.dc)==null?void 0:os.value)!=null&&_l.code)})?bs(!1):((r=(s=P[0])==null?void 0:s.salesOrg)!=null&&r.code&&((g=(d=(h=P[0])==null?void 0:h.dc)==null?void 0:d.value)!=null&&g.code)||!((ce=(X=(k=_[be])==null?void 0:k.headerData)==null?void 0:X.views)!=null&&ce.includes((Pe=S)==null?void 0:Pe.SALES)))&&((Ue=(w=(me=P[0])==null?void 0:me.plant)==null?void 0:w.value)!=null&&Ue.code)&&((un=(Nt=(at=P[0])==null?void 0:at.sloc)==null?void 0:Nt.value)!=null&&un.code||!(($s=(qt=($n=_[be])==null?void 0:$n.headerData)==null?void 0:qt.views)!=null&&$s.includes((rs=S)==null?void 0:rs.STORAGE)))&&((nl=(tl=(Ps=P[0])==null?void 0:Ps.warehouse)==null?void 0:tl.value)!=null&&nl.code||(E==null?void 0:E.Region)==="EUR"||!((hn=(js=(sl=_[be])==null?void 0:sl.headerData)==null?void 0:js.views)!=null&&hn.includes((Js=S)==null?void 0:Js.WAREHOUSE)))?bs(!0):bs(!1)},[P]),i.useEffect(()=>{ye(!0),Ge(!0)},[(wi=_[be])==null?void 0:wi.headerData,(ji=_[be])==null?void 0:ji.payloadData]),i.useEffect(()=>{var t,s,r,h,d,g,k,X;(h=(r=(s=(t=_==null?void 0:_[be])==null?void 0:t.payloadData)==null?void 0:s.Classification)==null?void 0:r.basic)!=null&&h.Classtype&&Mi((X=(k=(g=(d=_==null?void 0:_[be])==null?void 0:d.payloadData)==null?void 0:g.Classification)==null?void 0:k.basic)==null?void 0:X.Classtype,I)},[(Yi=(zi=(Xi=(Ji=_==null?void 0:_[be])==null?void 0:Ji.payloadData)==null?void 0:Xi.Classification)==null?void 0:zi.basic)==null?void 0:Yi.Classtype]),i.useEffect(()=>{var t,s,r,h,d,g;(s=(t=_[be])==null?void 0:t.headerData)!=null&&s.globalMaterialDescription&&(I(pn({materialID:be,viewID:"Supplier Form",itemID:"basic",keyName:"Json1",data:(h=(r=_[be])==null?void 0:r.headerData)==null?void 0:h.globalMaterialDescription})),I(pn({materialID:be,viewID:S.MERCHANT_INPUT,itemID:"basic",keyName:"Json1",data:(g=(d=_[be])==null?void 0:d.headerData)==null?void 0:g.globalMaterialDescription})))},[(Qi=(Ki=_[be])==null?void 0:Ki.headerData)==null?void 0:Qi.globalMaterialDescription]);const F=()=>{if(E!=null&&E.Region){const t=r=>{(r==null?void 0:r.statusCode)===wt.STATUS_200&&Bt(h=>({...h,"Sales Organization":r.body?r==null?void 0:r.body:[]}))},s=r=>{ae(r)};qe(`/${C}${Ot.DATA.GET_SALES_ORG}?region=${E==null?void 0:E.Region}`,"get",t,s)}},Ee=()=>{if(E!=null&&E.Region){const t=r=>{if((r==null?void 0:r.statusCode)===wt.STATUS_200){let h=kn.current?JSON.parse(JSON.stringify(kn.current)):JSON.parse(JSON.stringify(P));Bt(d=>({...d,Plant:r.body?r==null?void 0:r.body:[]})),A(h),kn.current=h}},s=r=>{ae(r)};qe(`/${C}${Ot.DATA.GET_PLANT}?region=${E==null?void 0:E.Region}`,"get",t,s)}},xe=(t,s)=>{if(t){G(d=>({...d,[El.STORAGE_LOCATION]:{...d[El.STORAGE_LOCATION],[s]:!0}}));const r=d=>{if(G(g=>({...g,[El.STORAGE_LOCATION]:{...g[El.STORAGE_LOCATION],[s]:!1}})),(d==null?void 0:d.statusCode)===wt.STATUS_200){let g=kn.current?JSON.parse(JSON.stringify(kn.current)):JSON.parse(JSON.stringify(P));s!==-1&&(g[s].sloc.options=Cs(d.body)),A(g),kn.current=g}},h=d=>{ae(d),G(g=>({...g,[El.STORAGE_LOCATION]:{...g[El.STORAGE_LOCATION],[s]:!1}}))};qe(`/${C}${Ot.DATA.GET_STORAGE_LOCATION}?region=${E==null?void 0:E.Region}&plant=${t==null?void 0:t.code}`,"get",r,h)}},De=(t,s="",r)=>new Promise((h,d)=>{var me;const g=[{materialNo:t,requestNo:s||(j==null?void 0:j.requestId)}],k=w=>{var Ue;(Ue=w==null?void 0:w.body)!=null&&Ue.length?(ft(`Duplicate article number ${w.body[0].split("$^$")[0]} (${w.body[0].split("$^$")[1]})`,"error"),h(!0)):h(!1)},X=w=>{ae(w),h(!1)};let Pe=0;Object.keys(_).forEach((w,Ue)=>{var at,Nt;(w.includes("-")||/\d/.test(w))&&((Nt=(at=_[w])==null?void 0:at.headerData)==null?void 0:Nt.materialNumber)===t&&Pe++});let ce=0;Object.keys(_).forEach(w=>{var Ue,at,Nt,un;(w.includes("-")||/\d/.test(w))&&(at=(Ue=_[w])==null?void 0:Ue.headerData)!=null&&at.globalMaterialDescription&&((un=(Nt=_[w])==null?void 0:Nt.headerData)==null?void 0:un.globalMaterialDescription)===r&&ce++}),Pe>1?(ft(`${el.DUPLICATE_MATERIAL}${t}`,"error"),h(!0)):ce>1?(ft(`${el.DUPLICATE_MATERIAL_DESCRIPTION}${r}`,"error"),h(!0)):qe(`/${C}${(me=Ot.MASS_ACTION)==null?void 0:me.MAT_NO_DUPLICATE_CHECK}`,"post",k,X,g)}),ot=async()=>{let t=[...b],s=!0;return Cn(!0),Zn(io.VALIDATING_MATS),new Promise(async(r,h)=>{for(let g=0;g<(b==null?void 0:b.length);g++){const k=b[g],{missingFields:X,viewType:Pe,isValid:ce,plant:me=[]}=us(k.id,(k==null?void 0:k.orgData)||[],!1,!1,!1);if(_t(me),ce){let w=!1;ce&&(!ge||k!=null&&k.isMatNoChanged)&&(w=await De(k.materialNumber,ge,k==null?void 0:k.globalMaterialDescription)),w&&(s=!1),t=t==null?void 0:t.map(Ue=>Ue.id===k.id?{...Ue,validated:!w}:Ue),I(Pn(t))}else{if(s=!1,t=t.map(w=>w.id===k.id?{...w,validated:!1}:w),I(Pn(t)),(X==null?void 0:X.length)>0)if(typeof X=="object"&&!Array.isArray(X)){const w=Object.entries(X).map(([Ue,at])=>`Combination ${Ue}: ${at.join(", ")}`);ft(`Line No ${k.lineNumber} : Please fill all the Mandatory fields in ${Pe||""}: ${w.join(" | ")}`,"error")}else ft(`Line No ${k.lineNumber} : Please fill all the Mandatory fields in ${Pe||""}: ${X.join(", ")}`,"error");break}}s?r(!0):h(),Cn(!1);const d=Tl(t);ye(!d),Ge(!d),s&&ft("Validation successful for all articles.","success")})},Je=t=>{var s,r;if(t){let h=JSON.parse(JSON.stringify(((r=(s=_==null?void 0:_[be])==null?void 0:s.headerData)==null?void 0:r.calledMrpCodes)||[]))||[];t.forEach((g,k)=>{var X,Pe,ce,me,w,Ue,at,Nt,un;(X=g==null?void 0:g.mrpProfile)!=null&&X.code&&!((w=(ce=(Pe=_==null?void 0:_[be])==null?void 0:Pe.headerData)==null?void 0:ce.calledMrpCodes)!=null&&w.includes((me=g==null?void 0:g.mrpProfile)==null?void 0:me.code))&&(Lt((at=(Ue=g==null?void 0:g.plant)==null?void 0:Ue.value)==null?void 0:at.code,(Nt=g==null?void 0:g.mrpProfile)==null?void 0:Nt.code),h.push((un=g==null?void 0:g.mrpProfile)==null?void 0:un.code))}),I(xs({materialID:be,keyName:"calledMrpCodes",data:h}));const d=b==null?void 0:b.map(g=>g.id===be?{...g,calledMrpCodes:h}:g);I(Pn(d))}},Lt=(t,s,r)=>{var k;const h={mrpProfile:s},d=X=>{X.body[0]&&Object.keys(X==null?void 0:X.body[0]).filter(ce=>X==null?void 0:X.body[0][ce]).forEach(ce=>{ct(t,ce,X==null?void 0:X.body[0][ce],S.MRP)})},g=X=>{ae(X)};qe(`/${C}${(k=Ot.MASS_ACTION)==null?void 0:k.MRP_DEFAULT_VALUES}`,"post",d,g,h)},ct=(t,s,r,h)=>{I(pn({materialID:be||"",keyName:s||"",data:r??null,viewID:h,itemID:t}))},Vt=(t,s)=>{const r=d=>{(d==null?void 0:d.statusCode)===200?(Xt(g=>({...g,globalViews:d==null?void 0:d.body})),on(g=>({...g,[t]:d==null?void 0:d.body})),I(xs({materialID:t,keyName:"globalViews",data:d==null?void 0:d.body}))):(Xt(g=>({...g,globalViews:[]})),on(g=>({...g,[t]:[]})),I(xs({materialID:t,keyName:"globalViews",data:[]})))},h=d=>{ae(d)};qe(`/${C}/data/getViewForMaterialType?materialType=${s}`,"get",r,h)};i.useEffect(()=>{Tl(u)&&(u!=null&&u.length)||ze||ge?(n.setCompleted([!0,!0]),n==null||n.setIsAttachmentTabEnabled(!0)):(n.setCompleted([!0,!1]),n==null||n.setIsAttachmentTabEnabled(!1))},[u]);const ln=Ln+10,Dt=()=>{var r,h,d;const t=Sl(),s={id:t,included:!0,lineNumber:ln,industrySector:(r=zc)==null?void 0:r.DEFAULT_IND_SECTOR,articleCategory:((h=ie==null?void 0:ie.ArticleCategory)==null?void 0:h.code)??"",materialType:((d=ie==null?void 0:ie.MatlType)==null?void 0:d.code)??"",materialNumber:(le==null?void 0:le.code)||"",globalMaterialDescription:"",views:[],orgData:"",validated:ri.default,withReference:$e};I(ql({materialID:t,data:s})),I(Pn([...b,s])),ke(y+1),xt(ln),nn(!0),ye(!0),Ge(!0),jt(t),gl("")},Mn=()=>{Me(!1),(E==null?void 0:E.RequestType)==="Create"?Dt():(E==null?void 0:E.RequestType)==="Change"&&ht(!0)},On=()=>{ei()},As=(t="",s=!0)=>{var g,k;const r={materialNo:t??"",salesOrg:((g=oe==null?void 0:oe.uniqueSalesOrgList)==null?void 0:g.map(X=>X.code).join("$^$"))||"",top:500,skip:s?0:Jt,matlType:((k=ut==null?void 0:ut["Material Type"])==null?void 0:k.code)??""};G(X=>({...X,"Material No":!0}));const h=X=>{(X==null?void 0:X.statusCode)===wt.STATUS_200&&(X!=null&&X.body)&&gn(s?X==null?void 0:X.body:Pe=>[...Pe,...X==null?void 0:X.body]),Y(!1),G(Pe=>({...Pe,"Material No":!1}))},d=()=>{Y(!1),G(X=>({...X,"Material No":!1}))};Y(!0),qe(`/${C}/data/getSearchParamsMaterialNo`,"post",h,d,r)},ws=((Zi=rn==null?void 0:rn[0])==null?void 0:Zi.External)==="X",Gl=((er=rn==null?void 0:rn[1])==null?void 0:er.External)==="X",Ql=rn==null?void 0:rn.some(t=>t.ExtNAwock==="X");(t=>{const s=new Set;let r=null;t==null||t.forEach(d=>{d.External==="X"&&d.ExtNAwock==="X"?(s.add(`External Number Range: Allowed (${d.FromNumber}-${d.ToNumber})`),s.add("Ext W/O Check: Allowed")):d.External!=="X"&&d.ExtNAwock==="X"?(s.add("Internal Number Range: Allowed"),s.add("Ext W/O Check: Allowed")):d.External==="X"&&d.ExtNAwock!=="X"?(s.add(`External Number Range: Allowed (${d.FromNumber}-${d.ToNumber})`),r="Ext W/O Check: Not Allowed"):d.External!=="X"&&d.ExtNAwock!=="X"&&(s.add("Internal Number Range: Allowed"),r="Ext W/O Check: Not Allowed")});const h=Array.from(s);return r&&h.push(r),h.map((d,g)=>e("div",{children:e(st,{children:d})},g))})(rn);function gl(t){var h;const s=(E==null?void 0:E.Region)||_s.US;if(!K.some(d=>d[s]&&d[s][t])&&t)x(t,s);else if(!t)I(fl({}));else{const d=K==null?void 0:K.find(g=>(g==null?void 0:g[s])&&(g==null?void 0:g[s][t]));d&&I(fl((h=d[s][t])==null?void 0:h.allfields))}t&&D(t)}const Yn=t=>{const{id:s,field:r,value:h}=t;vt(s);let d=b.map(g=>g.id===s?{...g,[r]:h}:g);Xt(g=>({...g,[r]:h})),r===ii.MATERIALTYPE&&(fo([mn]),I(xs({materialID:s,keyName:"orgData",data:""})),d=d.map(g=>g.id===s?{...g,orgData:""}:g),gl(h==null?void 0:h.code)),r===ii.INCLUDED&&(Tl(d)?(ye(!1),Ge(!1)):(ye(!0),Ge(!0))),r===ii.VIEWS&&(ye(!0),Ge(!0)),se(d),I(xs({materialID:s,keyName:r,data:h})),I(Pn(d))},Zl=t=>{var s,r,h,d,g;jt(t.row.id),Ss(t.row.materialNumber),gl((r=(s=t==null?void 0:t.row)==null?void 0:s.materialType)==null?void 0:r.code),A((d=(h=t==null?void 0:t.row)==null?void 0:h.orgData)!=null&&d.length?(g=t.row)==null?void 0:g.orgData:[mn])},Fo=()=>{bt(!0)},ei=()=>{bt(!1)},ti=(t,s)=>{s==="backdropClick"||s==="escapeKeyDown"||At(!1)},Vo=()=>{Ce((we==null?void 0:we[de])||ol),Dn(0),Jn((we==null?void 0:we[de][0])||ol[0])},wo=()=>{if(Me(!1),$e==="yes")if(_e!=null&&_e.length){let t=[...b];_e==null||_e.forEach(s=>{var k,X;const r=Sl();let h=JSON.parse(JSON.stringify(s));h!=null&&h.refMaterialData&&delete h.refMaterialData;let d=JSON.parse(JSON.stringify((k=_==null?void 0:_[s.id])==null?void 0:k.payloadData));h.id=r,h.lineNumber=ln,h.globalMaterialDescription="",h.materialNumber="",h.validated=ri.default,I(ql({materialID:r,data:h,payloadData:d})),t.push(h),se(t),I(Pn(t)),ke(y+1),xt(ln),nn(!0),ye(!0),Ge(!0);let g=(X=_==null?void 0:_[s.id])==null?void 0:X.unitsOfMeasureData;if(g!=null&&g.length){let Pe=[];g==null||g.forEach(ce=>{var me,w,Ue;Pe.push({...ce,eanUpc:"",eanCategory:"",length:"",width:"",height:"",volume:"",grossWeight:"",netWeight:"",eanCategory:(E==null?void 0:E.Region)===((me=_s)==null?void 0:me.US)?ce==null?void 0:ce.EanCat:"",eanUpc:(ce==null?void 0:ce.EanCat)==="MB"&&(E==null?void 0:E.Region)===((w=_s)==null?void 0:w.US)||(E==null?void 0:E.Region)===((Ue=_s)==null?void 0:Ue.EUR)?"":ce==null?void 0:ce.EanUpc,id:(ce==null?void 0:ce.id)||Pe.length+1})}),I(Wr({materialID:r,data:Pe}))}}),fn([])}else le&&jo();else Mn()},jo=()=>{var h,d,g,k,X,Pe,ce;Cn(!0);let t={material:le==null?void 0:le.code,wareHouseNumber:(h=ut==null?void 0:ut.Warehouse)==null?void 0:h.code,storageLocation:(d=ut==null?void 0:ut["Storage Location"])==null?void 0:d.code,salesOrg:(g=ut==null?void 0:ut["Sales Org"])==null?void 0:g.code,distributionChannel:(k=ut==null?void 0:ut["Distribution Channel"])==null?void 0:k.code,valArea:(X=ut==null?void 0:ut.Plant)==null?void 0:X.code,plant:(Pe=ut==null?void 0:ut.Plant)==null?void 0:Pe.code};const s=me=>{var w,Ue,at,Nt,un,$n,qt,rs,$s,Ps,tl,nl,sl;if(Cn(!1),_n({}),me!=null&&me.body[0]){Br(me==null?void 0:me.body,E);let js=[...b];const Js=Sl();let hn={},Ms=[...ol,...(Ue=(((w=me.body[0])==null?void 0:w.Views)||"").split(",").map(xn=>xn.trim()==="Storage"?S.STORAGE:xn.trim()))==null?void 0:Ue.filter(xn=>!Li.includes(xn)).filter(xn=>!ol.includes(xn))];hn.id=Js,hn.included=!0,hn.lineNumber=ln,hn.globalMaterialDescription="",hn.materialType={code:((at=me.body[0])==null?void 0:at.MatlType)||"",desc:((un=(Nt=W==null?void 0:W.MatlType)==null?void 0:Nt.find(xn=>{var os;return xn.code===((os=me.body[0])==null?void 0:os.MatlType)}))==null?void 0:un.desc)||""},hn.industrySector={code:(($n=me.body[0])==null?void 0:$n.IndSector)||"",desc:((rs=(qt=W==null?void 0:W.IndSector)==null?void 0:qt.find(xn=>{var os;return xn.code===((os=me.body[0])==null?void 0:os.IndSector)}))==null?void 0:rs.desc)||""},hn.articleCategory={code:(($s=me.body[0])==null?void 0:$s.ArticleCategory)||"01",desc:((Ps=$l==null?void 0:$l.find(xn=>{var os;return xn.code===((os=me.body[0])==null?void 0:os.IndSector)}))==null?void 0:Ps.desc)||""},hn.materialNumber="",hn.views=Ms,(E==null?void 0:E.Region)===((tl=_s)==null?void 0:tl.EUR)&&(hn.views=((nl=hn==null?void 0:hn.views)==null?void 0:nl.filter(xn=>xn!==S.WAREHOUSE))||[]),hn.validated=ri.default,hn.withReference=$e,hn.refMaterialData=Br(me.body,E),I(ql({materialID:Js,data:hn,payloadData:{}})),js.push(hn),se(js),I(Pn(js)),ke(y+1),xt(ln),nn(!0),ye(!0),Ge(!0),gl((sl=me.body[0])==null?void 0:sl.MatlType),jt(Js)}else Cn(!1),ft(el.NO_MATERIAL_FOUND,"warning"),Me(!0)},r=me=>{ae(me),Cn(!1),Me(!0)};_n({}),Ve(null),Ie(null),qe(`/${C}${(ce=Ot.DATA)==null?void 0:ce.GET_COPY_MATERIAL}`,"post",s,r,t)},Ul=!Ls.includes(n==null?void 0:n.requestStatus)||ge&&!ze,Us=(a==null?void 0:a.taskDesc)!=="Generic Article Activation Task"||!a||Object.keys(a).length===0,Jo=(t,s)=>{var r;qa.fire({title:B("Are you sure?"),text:B("Changing the article type will reset all the field values entered!"),icon:"warning",showCancelButton:!0,confirmButtonColor:(r=We.primary)==null?void 0:r.main,cancelButtonColor:We.error.red,confirmButtonText:B("Yes, do it!"),cancelButtonText:B("Cancel"),reverseButtons:!0}).then(h=>{h.isConfirmed&&(Yn({id:de,field:fi.VIEWS,value:t}),I(Jc({materialId:s})),Yn({id:s,field:"materialType",value:t}))})},Xo=t=>{var s,r;if(_){const h=(r=(s=_==null?void 0:_[be])==null?void 0:s.headerData)==null?void 0:r.TovariantData,d=Array.isArray(h)?h.some(g=>g&&g.id&&(g==null?void 0:g.id)!==""):!1;sn(g=>({...g,[t]:h})),vn(g=>({...g,[t]:h})),zt(d)}},Ui=[{field:"included",headerName:B("Included"),flex:.5,align:"center",headerAlign:"center",renderCell:t=>e(al,{checked:t.row.included,disabled:Ul,onChange:s=>Yn({id:t.row.id,field:"included",value:s.target.checked})})},{field:"lineNumber",headerName:B("Line Number"),flex:.5,editable:!0,align:"center",headerAlign:"center"},{field:"materialType",headerName:B("Article Type"),flex:.7,align:"center",headerAlign:"center",renderHeader:()=>f("span",{children:[B("Article Type"),e("span",{style:{color:"red"},children:"*"})]}),...Se===c.CREATE||Se===c.CREATE_WITH_UPLOAD?{renderCell:t=>e(Qt,{options:Hr||[],value:t.row.materialType,onChange:s=>{t.row.materialType?Jo(s,t.row.id):Yn({id:t.row.id,field:"materialType",value:s})},placeholder:B("Select Article Type"),minWidth:"90%",listWidth:235})}:{editable:!1,renderCell:t=>{var s,r;return((r=(s=_==null?void 0:_[t.row.id])==null?void 0:s.headerData)==null?void 0:r.materialType)||""}}},{field:"articleCategory",headerName:B("Article Category"),flex:.7,align:"center",headerAlign:"center",renderHeader:()=>f("span",{children:[B("Article Category"),e("span",{style:{color:"red"},children:"*"})]}),...Se===c.CREATE||Se===c.CREATE_WITH_UPLOAD?{renderCell:t=>e(Qt,{options:$l||[],value:t.row.articleCategory,onChange:s=>Yn({id:t.row.id,field:"articleCategory",value:s}),placeholder:B("Select Article Category"),minWidth:"90%",listWidth:235})}:{editable:!1,renderCell:t=>{var s,r;return((r=(s=_==null?void 0:_[t.row.id])==null?void 0:s.headerData)==null?void 0:r.articleCategory)||""}}},{field:"materialNumber",headerName:B("Article Number"),flex:.7,editable:!1,align:"center",headerAlign:"center",hide:Us,renderHeader:()=>f("span",{children:[B("Article Number"),!Us&&e("span",{style:{color:"red"},children:"*"})]}),renderCell:t=>{var g,k;const[s,r]=i.useState({[(g=t==null?void 0:t.row)==null?void 0:g.id]:t.row.materialNumber}),h=t.row.id,d=X=>{const Pe=X.target.value.toUpperCase(),ce=(E==null?void 0:E.Region)==="US"?Pe.replace(/[^A-Z0-9-]/g,"").replace(/-{2,}/g,"-"):Pe.replace(/[^A-Z0-9]/g,"");r(w=>({...w,[h]:ce})),Yn({id:t.row.id,field:"materialNumber",value:ce});const me=b.map(w=>w.id===t.row.id?{...w,isMatNoChanged:!0,materialNumber:ce}:w);I(Pn(me))};return e(Ut,{children:(E==null?void 0:E.RequestType)===c.CREATE||(E==null?void 0:E.RequestType)===c.CREATE_WITH_UPLOAD?e(Sn,{fullWidth:!0,placeholder:B("ENTER ARTICLE NUMBER"),variant:"outlined",size:"small",name:"article number",value:s[h]||((k=t==null?void 0:t.row)==null?void 0:k.materialNumber)||"",onChange:X=>{d(X)},sx:{"& .MuiInputBase-root.Mui-disabled":{"& > input":{WebkitTextFillColor:We.black.dark,color:We.black.dark}}},disabled:Us}):t.row.materialNumber})}},{field:"apparelMcat",headerName:B("Merchandising Category"),flex:.8,editable:!1,align:"center",headerAlign:"center",renderHeader:()=>f("span",{children:[B("Merchandising Category"),e("span",{style:{color:"red"},children:"*"})]}),renderCell:t=>{var r;const s=Un.MatlGroup||[];return e(Qt,{options:s.length>0?s:[],value:(r=t==null?void 0:t.row)==null?void 0:r.apparelMcat,onChange:h=>{Yn({id:t.row.id,field:"apparelMcat",value:h})},placeholder:B("Select Merchandising Category"),minWidth:"90%",listWidth:235})}},{field:"globalMaterialDescription",flex:.7,headerName:B("Article Description"),renderHeader:()=>f("span",{children:[B("Article Description"),e("span",{style:{color:"red"},children:"*"})]}),renderCell:t=>{var X,Pe;const[s,r]=i.useState({[(X=t==null?void 0:t.row)==null?void 0:X.id]:t.row.globalMaterialDescription}),h=t.row.id,d=i.useRef(null),g=ce=>{var Ue,at;const me=(at=(Ue=ce.target)==null?void 0:Ue.value)==null?void 0:at.toUpperCase();(me==null?void 0:me.length)>Vn&&(d.current||(d.current=setTimeout(()=>{ft(`Article Description cannot exceed ${Vn} characters`,"error"),d.current=null},1e3)));const w=me.replace(/[^A-Z0-9-]/g,"").slice(0,Vn);r(Nt=>({...Nt,[h]:w})),Yn({id:t.row.id,field:"globalMaterialDescription",value:w})},k=(((Pe=s[h])==null?void 0:Pe.length)||0)===40;return e(Be,{sx:{display:"flex",alignItems:"center",width:"100%"},children:e(bn,{title:s[h]||"",arrow:!0,placement:"top",children:e(Sn,{fullWidth:!0,variant:"outlined",size:"small",placeholder:B("ENTER ARTICLE DESCRIPTION"),value:s[h]||"",onChange:g,error:k,sx:{flexGrow:1,"& .MuiOutlinedInput-root":{"& fieldset":{borderColor:k?We.error.dark:void 0},"&:hover fieldset":{borderColor:k?We.error.dark:void 0},"&.Mui-focused fieldset":{borderColor:k?We.error.dark:void 0}}}})})})},align:"center",headerAlign:"center",editable:!1},{...Se===c.CREATE||Se===c.CREATE_WITH_UPLOAD?{field:"views",headerName:"",flex:1,align:"center",headerAlign:"center",hide:Us,renderCell:t=>{var s,r,h,d,g,k,X,Pe;return f(Ut,{children:[e(nt,{variant:"contained",size:"small",disabled:!((s=t==null?void 0:t.row)!=null&&s.materialType)||Us,onClick:()=>{an(!0),vt(t.row.id)},children:B("Views")}),e(nt,{variant:"contained",disabled:!(((h=(r=t==null?void 0:t.row)==null?void 0:r.views)==null?void 0:h.length)>1)||Us,size:"small",sx:{marginLeft:"4px"},onClick:()=>{var ce,me,w;At(!0),vt(t.row.id),A((me=(ce=t==null?void 0:t.row)==null?void 0:ce.orgData)!=null&&me.length?(w=t.row)==null?void 0:w.orgData:[mn])},children:B("Area of Validity")}),(((g=(d=t.row)==null?void 0:d.articleCategory)==null?void 0:g.code)==="01"||((k=t.row)==null?void 0:k.articleCategory)==="01")&&e(nt,{variant:"contained",disabled:Us,size:"small",sx:{marginLeft:"4px"},onClick:()=>{var ce,me;ge&&Xo((ce=t==null?void 0:t.row)==null?void 0:ce.lineNumber),Nn(!0),Ye((me=t==null?void 0:t.row)==null?void 0:me.lineNumber)},children:B("Variant")}),(((Pe=(X=t.row)==null?void 0:X.articleCategory)==null?void 0:Pe.code)=="11"||t.row.articleCategory=="11")&&e(nt,{variant:"contained",size:"small",sx:{marginLeft:"4px"},onClick:()=>{Et(!0),vt(t.row.id)},children:B("Components")})]})}}:{}},{field:"action",headerName:B("Action"),flex:.5,align:"center",headerAlign:"center",renderCell:t=>{let s=Fc(t==null?void 0:t.row);const r=async h=>{var w,Ue,at;h.stopPropagation();const{missingFields:d,viewType:g,isValid:k,plant:X=[]}=us(t.row.id,((w=t==null?void 0:t.row)==null?void 0:w.orgData)||[],ws,Gl,Ql);if(_t(X),d)if(typeof d=="object"&&!Array.isArray(d)){const Nt=Object.entries(d).map(([un,$n])=>`Combination ${un}: ${$n.join(", ")}`);ft(`${B("Line No")} ${t.row.lineNumber} : ${B("Please fill all the Mandatory fields in")} ${g||""}: ${Nt.join(" | ")}`,"error")}else ft(`${B("Line No")} ${t.row.lineNumber} : ${B("Please fill all the Mandatory fields in")} ${g||""}: ${d.join(", ")}`,"error");let Pe=!1;k&&(!ge||(Ue=t.row)!=null&&Ue.isMatNoChanged)&&!Us&&(Pe=await De(t.row.materialNumber,ge,(at=t==null?void 0:t.row)==null?void 0:at.globalMaterialDescription)),s=k&&!Pe?"success":"error",s==="success"&&ft("Validation successful","success");const ce=b.map(Nt=>Nt.id===t.row.id?{...Nt,validated:k&&!Pe}:Nt);I(Pn(ce));const me=Tl(ce);ye(!me),Ge(!me)};return f(Fs,{direction:"row",alignItems:"center",sx:{marginLeft:"0.5rem",magrinRight:"0.5rem"},spacing:.5,children:[e(bn,{title:s==="success"?"Validated Successfully":B(s==="error"?"Validation Failed":"Click to Validate"),children:e(cn,{onClick:r,color:s==="success"?"success":s==="error"?"error":"default",children:s==="error"?e(Vc,{}):e(sd,{})})}),!Ul&&e(bn,{title:B("Delete Row"),children:e(cn,{onClick:()=>{q({...L,data:t,isVisible:!0})},color:"error",children:e(dl,{})})})]})}}],zo=(t,s)=>{var r,h;Dn(s),Jn(((r=t==null?void 0:t.target)==null?void 0:r.id)==="AdditionalKey"?"Additional Data":(h=p==null?void 0:p.filter(d=>{var g;return(g=en==null?void 0:en[ks])==null?void 0:g.includes(d)}))==null?void 0:h[s])},Yo=t=>{const s={"Sales Org":"/getSalesOrg","Mrp Profile":"/getMRPProfile"},r=d=>{const g=Cs(d.body);Bt(k=>({...k,[t]:g}))},h=d=>ae(d);qe(`/${C}/data${s[t]}`,"get",r,h)},Ko=t=>{No(t,p,Rs,be,P,I,Ks)},Qo=t=>{Xc(t,p,Rs,be,P,I,Ks,S)},Zo=(t,s,r)=>(h,d)=>{var w,Ue,at;let g={},k="",X="";r==="Purchasing"||r==="Costing"?(g={materialNo:s==null?void 0:s.Material,plant:s==null?void 0:s.Plant},X=s==null?void 0:s.Plant,k=`/${C}/data/displayLimitedPlantData`):r==="Accounting"?(g={materialNo:s==null?void 0:s.Material,valArea:s==null?void 0:s.ValArea},X=s==null?void 0:s.ValArea,k=`/${C}/data/displayLimitedAccountingData`):r==="Sales"&&(g={materialNo:s==null?void 0:s.Material,salesOrg:s==null?void 0:s.SalesOrg,distChnl:s==null?void 0:s.DistrChan},X=`${s==null?void 0:s.SalesOrg}-${s==null?void 0:s.DistrChan}`,k=`/${C}/data/displayLimitedSalesData`);const Pe=Nt=>{var un,$n,qt;r==="Purchasing"||r==="Costing"?I(Ks({materialID:be,viewID:r,itemID:s==null?void 0:s.Plant,data:(un=Nt==null?void 0:Nt.body)==null?void 0:un.SpecificPlantDataViewDto[0]})):r==="Accounting"?I(Ks({materialID:be,viewID:r,itemID:s==null?void 0:s.ValArea,data:($n=Nt==null?void 0:Nt.body)==null?void 0:$n.SpecificAccountingDataViewDto[0]})):r==="Sales"&&I(Ks({materialID:be,viewID:r,itemID:`${s==null?void 0:s.SalesOrg}-${s==null?void 0:s.DistrChan}`,data:(qt=Nt==null?void 0:Nt.body)==null?void 0:qt.SpecificSalesDataViewDto[0]}))},ce=()=>{};!((at=(Ue=(w=_==null?void 0:_[be])==null?void 0:w.payloadData)==null?void 0:Ue[r])!=null&&at[X])&&qe(k,"post",Pe,ce,g),J(d?t:null)};function ec(){const t=p&&(P==null?void 0:P.length)>0&&(p==null?void 0:p.length)>0&&(p==null?void 0:p.filter(s=>{var r;return(r=en==null?void 0:en[ks])==null?void 0:r.includes(s)})[yt]);return V&&jn&&(V[jn]||jn==="Additional Data")?jn==="Additional Data"&&!Us?[e(vo,{disableCheck:ze&&!Ls.includes(n==null?void 0:n.requestStatus),materialID:be,selectedMaterialNumber:Rn})]:[e(_d,{disabled:ze&&!Ls.includes(n==null?void 0:n.requestStatus),selectedMaterialNumber:Rn,materialID:be,basicData:Wt,setBasicData:lt,dropDownData:kt,basicDataTabDetails:V[t],allTabsData:V,activeViewTab:t,selectedViews:p,handleAccordionClick:Zo,missingValidationPlant:ss,isDisplay:ge||ze,moduleName:"Article"})]:e(Ut,{})}const ki=t=>{var h,d;const s=((d=(h=t==null?void 0:t.target)==null?void 0:h.value)==null?void 0:d.toUpperCase())||"";Ie(null),Rt(0),te&&clearTimeout(te);const r=setTimeout(()=>{As(s,!0)},500);Mt(r)},tc=(t,s)=>{const r=le==null?void 0:le.code,h=$e==="yes"?"extended":"notExtended";_n(d=>({...d,[t]:s})),t==="Sales Org"&&s?l(r,h,s):t==="Plant"&&s&&(o(r,h,s),m(r,s,ut["Sales Org"]))},nc=(t,s,r)=>{t==="Sales Organization"&&(s?(A(h=>h.map((d,g)=>g===r?{...d,salesOrg:s}:d)),$i(s,r).then(h=>{})):A(h=>h.map((d,g)=>g===r?{...d,salesOrg:null}:d)))},$i=(t,s,r="",h="")=>new Promise((d,g)=>{G(ce=>({...ce,"Distribution Channel":{...ce["Distribution Channel"],[s]:!0}}));let k={salesOrg:t==null?void 0:t.code};const X=ce=>{G(w=>({...w,"Distribution Channel":{...w["Distribution Channel"],[s]:!1}}));let me=JSON.parse(JSON.stringify(r||kn.current));if(me[s].dc.options=Cs(ce.body),A(me),kn.current=me,h){I(xs({materialID:h==null?void 0:h.id,keyName:"orgData",data:me}));let w=(b==null?void 0:b.length)||[JSON.parse(JSON.stringify(h))],Ue=w.findIndex(at=>at.id===(h==null?void 0:h.id));w[Ue].orgData=me,I(Pn(w)),d({org:me,material:w[Ue]})}else d(""),G(w=>({...w,"Distribution Channel":{...w["Distribution Channel"],[s]:!1}}))},Pe=ce=>{ae(ce),G(me=>({...me,"Distribution Channel":{...me["Distribution Channel"],[s]:!1}}))};qe(`/${C}/data/getDistrChan`,"post",X,Pe,k)}),sc=(t,s)=>{let r=JSON.parse(JSON.stringify(P));r[s].dc.value=t,A(r)},lc=t=>{let s=JSON.parse(JSON.stringify(P));s.splice(t,1),A(s)},ic=(t,s)=>{let r=JSON.parse(JSON.stringify(P));r[s].plant.value=t,r[s].sloc.value={},r[s].sloc.options=[],r[s].warehouse.value={},r[s].warehouse.options=[],A(r),xe(t,s),kn.current=r},rc=(t,s)=>{let r=JSON.parse(JSON.stringify(P));r[s].distributionCenter.value=t,A(r)},oc=(t,s)=>{let r=JSON.parse(JSON.stringify(P));r[s].purchasingOrg.value=t,A(r)},cc=(t,s)=>{let r=JSON.parse(JSON.stringify(P));r[s].supplier.value=t,A(r)},ac=(t,s)=>{let r=JSON.parse(JSON.stringify(P));r[s].store.value=t,A(r)},dc=(t,s)=>{let r=JSON.parse(JSON.stringify(P));r[s].salesUnit.value=t,A(r)},uc=(t,s)=>{let r=JSON.parse(JSON.stringify(P));r[s].plantGroup.value=t,A(r)},hc=(t,s)=>{let r=JSON.parse(JSON.stringify(P));r[s].priceList.value=t,A(r)},gc=()=>{let t=JSON.parse(JSON.stringify(P));t.push(mn),A(t)},Tc=t=>{if(!(t!=null&&t.temp)||(t==null?void 0:t.temp)===(Qn==null?void 0:Qn.temp))return;Cn(!0);let s={decisionTableId:null,decisionTableName:"MDG_MAT_ORGDATA_TEMPLATE_CONFIG",version:"v2",rulePolicy:null,validityDate:null,conditions:[{"MDG_CONDITIONS.MDG_MAT_REGION":(E==null?void 0:E.Region)||_s.US,"MDG_CONDITIONS.MDG_MAT_TEMPLATE":t.temp||""}],systemFilters:null,systemOrders:null,filterString:null};const r=d=>{var g,k;if(d.statusCode===wt.STATUS_200){Cn(!1);let X=(k=(g=d==null?void 0:d.data)==null?void 0:g.result[0])==null?void 0:k.MDG_MAT_ORGDATA_TEMPLATE_CONFIG,Pe=[];X==null||X.forEach((ce,me)=>{var $n;let w=JSON.parse(JSON.stringify(mn));w.salesOrg={},w.salesOrg.code=ce.MDG_MAT_SALES_ORG,w.salesOrg.desc=ce.MDG_MAT_SALES_ORG_DESC,w.plant.value={},w.plant.value.code=ce.MDG_MAT_PLANT,w.plant.value.desc=ce.MDG_MAT_PLANT_DESC;let Ue=($n=qn==null?void 0:qn.filter(qt=>qt.MDG_MAT_SALES_ORG===ce.MDG_MAT_SALES_ORG))==null?void 0:$n.map(qt=>({code:qt.MDG_MAT_PLANT,desc:qt.MDG_MAT_PLANT_DESC}));Ue=Ue==null?void 0:Ue.filter((qt,rs,$s)=>rs===$s.findIndex(Ps=>Ps.code===qt.code)),w.plant.options=Ue==null?void 0:Ue.sort((qt,rs)=>qt.code-rs.code);let at=qn==null?void 0:qn.filter(qt=>qt.MDG_MAT_SALES_ORG===ce.MDG_MAT_SALES_ORG&&qt.MDG_MAT_PLANT===ce.MDG_MAT_PLANT),Nt=at==null?void 0:at.map(qt=>({code:qt.MDG_MAT_STORAGE_LOCATION,desc:qt.MDG_MAT_STORE_LOC_DESC})),un=at==null?void 0:at.map(qt=>qt.MDG_MAT_WAREHOUSE?{code:qt.MDG_MAT_WAREHOUSE,desc:qt.MDG_MAT_WAREHOUSE_DESC}:null).filter(Boolean);ce.MDG_MAT_STORAGE_LOCATION&&(w.sloc.value={},w.sloc.value.code=ce.MDG_MAT_STORAGE_LOCATION,w.sloc.value.desc=ce.MDG_MAT_STORE_LOC_DESC),w.sloc.options=Nt,ce.MDG_MAT_WAREHOUSE&&(w.warehouse.value={},w.warehouse.value.code=ce.MDG_MAT_WAREHOUSE||"",w.warehouse.value.desc=ce.MDG_MAT_WAREHOUSE_DESC||""),w.warehouse.options=un,Pe.push(w)}),kn.current=Pe,A(Pe),Pi(Pe,0)}else ae("Something went wrong"),Cn(!1),ft("No Area of Validity found","error")},h=d=>{ae("Something went wrong"),Cn(!1),ft("No Area of Validity found","error")};z.environment==="localhost"?qe(`/${Bs}${Ot.INVOKE_RULES.LOCAL}`,"post",r,h,s):qe(`/${Bs}${Ot.INVOKE_RULES.PROD}`,"post",r,h,s)},Pi=async(t,s)=>{s<(t==null?void 0:t.length)&&(await $i(t[s].salesOrg,s),s++,Pi(t,s))},Ec=()=>{const t=L==null?void 0:L.data;se(b==null?void 0:b.filter(s=>{var r;return s.id!==((r=t==null?void 0:t.row)==null?void 0:r.id)})),I(Co(t==null?void 0:t.row.id)),gl(""),I(Pn(b==null?void 0:b.filter(s=>{var r;return s.id!==((r=t==null?void 0:t.row)==null?void 0:r.id)}))),b!=null&&b.length?b.filter(s=>{var r,h;return((r=s.params)==null?void 0:r.id)!==((h=t==null?void 0:t.row)==null?void 0:h.id)}).every(s=>s.validated)&&ye(!1):ye(!1),q({...L,isVisible:!1})};i.useEffect(()=>{var g,k,X,Pe;const t=p==null?void 0:p.includes((g=S)==null?void 0:g.SALES),s=p==null?void 0:p.includes((k=S)==null?void 0:k.SALES_PLANT),r=p==null?void 0:p.includes((X=S)==null?void 0:X.STORAGE),h=p==null?void 0:p.includes((Pe=S)==null?void 0:Pe.STORAGE_PLANT),d=P==null?void 0:P.some(ce=>{var me,w;return(w=(me=ce==null?void 0:ce.plant)==null?void 0:me.value)==null?void 0:w.code});t&&!s&&d&&Ce(ce=>{var Ue,at;const me=[...ce],w=me.indexOf((Ue=S)==null?void 0:Ue.SALES);return me.splice(w+1,0,(at=S)==null?void 0:at.SALES_PLANT),me}),r&&!h&&Ce(ce=>{var Ue,at;const me=[...ce],w=me.indexOf((Ue=S)==null?void 0:Ue.STORAGE);return me.splice(w+1,0,(at=S)==null?void 0:at.STORAGE_PLANT),me})},[p,P]);const pi=t=>{!t||!Array.isArray(t)||t.forEach(s=>{var r,h,d,g,k,X,Pe,ce,me,w,Ue,at,Nt,un,$n,qt;if((h=(r=s.plant)==null?void 0:r.value)!=null&&h.code){if(Ct((g=(d=s.plant)==null?void 0:d.value)==null?void 0:g.code,S.PLANT),(k=s.salesOrg)!=null&&k.code||(Pe=(X=s.dc)==null?void 0:X.value)!=null&&Pe.code){const rs=`${((ce=s.salesOrg)==null?void 0:ce.code)||""}-${((w=(me=s.dc)==null?void 0:me.value)==null?void 0:w.code)||""}`;Ct(rs,S.SALES)}(at=(Ue=s.warehouse)==null?void 0:Ue.value)!=null&&at.code&&Ct((un=(Nt=s.warehouse)==null?void 0:Nt.value)==null?void 0:un.code,S.WAREHOUSE),et((qt=($n=s.plant)==null?void 0:$n.value)==null?void 0:qt.code)}})};i.useEffect(()=>{if(ge){const t=Gt==null?void 0:Gt.orgData;(t==null?void 0:t.length)>0&&t.some(s=>{var r,h,d,g,k;return((h=(r=s.plant)==null?void 0:r.value)==null?void 0:h.code)&&(((d=s.salesOrg)==null?void 0:d.code)||((k=(g=s.dc)==null?void 0:g.value)==null?void 0:k.code))})&&pi(t)}},[Gt==null?void 0:Gt.orgData]);const Wi=t=>{I(Al(t)),he(t)};i.useEffect(()=>{var t,s;(R==null?void 0:R.page)!==0&&(Se===((t=c)==null?void 0:t.CREATE_WITH_UPLOAD)||Se===((s=c)==null?void 0:s.CREATE))&&ue(),he((R==null?void 0:R.page)||0)},[R==null?void 0:R.page]);const Ac=()=>{ee(!Te),re&&H(!1)},Sc=()=>{H(!re),Te&&ee(!1)},mc=()=>{var s,r;const t=(r=(s=_==null?void 0:_[be])==null?void 0:s.payloadData.Classification)==null?void 0:r.classification;if(Array.isArray(t)&&t.length>0){const h=[];t[1].value.map(d=>{t[0].value.map(g=>{h.push({size:g,item:!1,color:d,id:""})})}),vn(d=>({...d,[En]:h}))}$t(h=>h+1)},ks=i.useMemo(()=>{var s;const t=b==null?void 0:b.find(r=>r.id===de);return((s=t==null?void 0:t.materialType)==null?void 0:s.code)??(t==null?void 0:t.materialType)},[b,de]);return i.useEffect(()=>{var t,s;if(de&&ks){const r=we==null?void 0:we[de],h=b==null?void 0:b.find(g=>g.id===de),d=h==null?void 0:h.views;if(r||((d==null?void 0:d.length)>0?on(g=>({...g,[de]:d})):Vt(de,ks)),de&&((t=we[de])==null?void 0:t.length)>0&&((s=en[ks])==null?void 0:s.length)>0){const g=[...en[ks].filter(k=>we[de].includes(k)),...we[de].filter(k=>!en[ks].includes(k))];Ce(g),Dn(0),Jn(g[0]),Yn({id:de,field:"views",value:g})}}},[ks,de,we,en]),f("div",{children:[e("div",{style:{padding:"0",width:"100%",margin:"0",marginTop:"20px"},children:f(Be,{sx:{position:Te?"fixed":"relative",top:Te?0:"auto",left:Te?0:"auto",right:Te?0:"auto",bottom:Te?0:"auto",width:Te?"100vw":"100%",height:Te?"100vh":"auto",zIndex:Te?1004:void 0,backgroundColor:Te?"white":"transparent",padding:Te?"20px":"0",display:"flex",flexDirection:"column",boxShadow:Te?"0px 0px 15px rgba(0, 0, 0, 0.2)":"none",transition:"all 0.3s ease",borderRadius:"8px",border:"1px solid #e0e0e0"},children:[f(Be,{sx:{display:"flex",justifyContent:"space-between",alignItems:"center",padding:"8px 16px",backgroundColor:"#f5f5f5",borderRadius:"8px 8px 0 0"},children:[e(st,{variant:"h6",children:B("Article Data")}),f(Be,{sx:{display:"flex",alignItems:"center",gap:1},children:[f(nt,{variant:"contained",color:"primary",size:"small",onClick:()=>{Se===c.CREATE&&(Me(!0),fn([]),Ve(null),_n({}),gn([]))},children:["+ ",B("Add")]}),e(bn,{title:B(Te?"Exit Zoom":"Zoom In"),sx:{zIndex:"1009"},children:e(cn,{onClick:Ac,color:"primary",sx:{backgroundColor:"rgba(0, 0, 0, 0.05)","&:hover":{backgroundColor:"rgba(0, 0, 0, 0.1)"}},children:Te?e(Kr,{}):e(Qr,{})})})]})]}),ge&&b&&(b==null?void 0:b.length)>0?e("div",{style:{width:"100%",height:"100%",overflowX:"auto"},children:e("div",{style:{height:"100%"},children:e(Ll,{rows:b,columns:Ui,pageSize:50,autoHeight:!1,page:Re,rowCount:(R==null?void 0:R.totalElements)||0,rowsPerPageOptions:[50],onRowClick:Zl,onCellEditCommit:Yn,onPageChange:t=>Wi(t),pagination:!0,disableSelectionOnClick:!0,getRowClassName:t=>t.id===be?"selected-row":"",style:{border:"1px solid #ccc",borderRadius:"8px",width:"100%",height:Te?"calc(100vh - 150px)":`${Math.min(b.length*50+130,300)}px`,overflow:"auto"},sx:{"& .selected-row":{backgroundColor:"rgb(234 233 255)"}}})})}):e(Ut,{children:e("div",{style:{width:"100%",height:"100%",overflowX:"auto"},children:e("div",{style:{height:"100%"},children:e(Ll,{autoHeight:!1,rows:b,columns:Ui,pageSize:50,page:Re,rowsPerPageOptions:[50],onRowClick:Zl,onCellEditCommit:Yn,onPageChange:t=>Wi(t),disableSelectionOnClick:!0,getRowClassName:t=>t.id===be?"selected-row":"",style:{border:"1px solid #ccc",borderRadius:"8px",width:"100%",height:Te?"calc(100vh - 150px)":`${Math.min(b.length*50+130,300)}px`,overflow:"auto"},sx:{"& .selected-row":{backgroundColor:"rgb(234 233 255)"}}})})})})]})}),Se===c.CREATE||Se===c.CREATE_WITH_UPLOAD||a!=null&&a.ATTRIBUTE_1?be&&Zt&&(b==null?void 0:b.length)>0&&(O==null?void 0:O.length)>0&&V&&((tr=Object.getOwnPropertyNames(V))==null?void 0:tr.length)>0&&f(Be,{sx:{position:re?"fixed":"relative",top:re?0:"auto",left:re?0:"auto",right:re?0:"auto",bottom:re?0:"auto",width:re?"100vw":"100%",height:re?"100vh":"auto",zIndex:re?1004:void 0,backgroundColor:re?"white":"transparent",padding:re?"20px":"0",marginTop:"20px",display:"flex",flexDirection:"column",boxShadow:re?"0px 0px 15px rgba(0, 0, 0, 0.2)":"none",transition:"all 0.3s ease",borderRadius:"8px",border:"1px solid #e0e0e0"},children:[f(Be,{sx:{display:"flex",justifyContent:"space-between",alignItems:"center",padding:"8px 16px",borderRadius:"8px 8px 0 0"},children:[e(st,{variant:"h6",children:B("View Details")}),e(bn,{title:B(re?"Exit Zoom":"Zoom In"),sx:{zIndex:"1009"},children:e(cn,{onClick:Sc,color:"primary",sx:{backgroundColor:"rgba(0, 0, 0, 0.05)","&:hover":{backgroundColor:"rgba(0, 0, 0, 0.1)"}},children:re?e(Kr,{}):e(Qr,{})})})]}),f(Be,{sx:{flexGrow:1,display:"flex",flexDirection:"column"},children:[f(Ao,{value:yt,onChange:zo,className:N.customTabs,"aria-label":"article tabs",sx:{top:0,position:"sticky",zIndex:1e3,backgroundColor:We.background.container,borderBottom:`1px solid ${We.border.light}`,"& .MuiTab-root":{minHeight:"48px",textTransform:"none",fontSize:"14px",fontWeight:600,color:We.black.graphite,"&.Mui-selected":{color:We.primary.main,fontWeight:700},"&:hover":{color:We.primary.main,opacity:.8}},"& .MuiTabs-indicator":{backgroundColor:We.primary.main,height:"3px"}},children:[p&&(P==null?void 0:P.length)>0&&(p==null?void 0:p.length)>0&&((nr=en==null?void 0:en[ks])==null?void 0:nr.length)>0?(()=>{const t=(a==null?void 0:a.taskDesc)==="Supplier Data Review Task";let s=p.filter(r=>{var h;return(h=en==null?void 0:en[ks])==null?void 0:h.includes(r)});return t&&!fe&&(s=s.filter(r=>r==="Supplier Form")),s.map((r,h)=>e(Vl,{label:B(r)},h))})():null,!Us&&e(Vl,{label:B("Additional Data"),id:"AdditionalKey"},"Additional data")]}),e(Be,{sx:{padding:2,marginTop:2,flexGrow:1,overflow:"auto",height:re?"calc(100vh - 180px)":"auto"},children:ec()}),(!Ul||ge&&!ze||ze&&Ls.includes(n==null?void 0:n.requestStatus))&&e(Be,{sx:{borderTop:"1px solid #e0e0e0",padding:"16px"},children:e(Za,{activeTab:yt,submitForApprovalDisabled:!Tl(u),filteredButtons:gt,validateMaterials:ot,workFlowLevels:He,showWfLevels:An,childRequestHeaderData:(sr=_==null?void 0:_[be])==null?void 0:sr.Tochildrequestheaderdata,setIsAccepted:Q,module:(lr=Ds)==null?void 0:lr.ART})})]})]}):e(Ut,{}),e(Kl,{dialogState:It,openReusableDialog:Fo,closeReusableDialog:ei,dialogTitle:"Warning",dialogMessage:dt,showCancelButton:!1,handleOk:On,handleDialogConfirm:ei,dialogOkText:"OK",dialogSeverity:"danger"}),Ne&&f(Ws,{open:Ne,onClose:()=>Nn(!1),maxWidth:"lg",fullWidth:!0,children:[e(Hs,{sx:{backgroundColor:"#EAE9FF",marginBottom:".5rem",display:"flex",alignItems:"center",justifyContent:"space-between"},children:f(ve,{container:!0,alignItems:"center",children:[f(ve,{item:!0,xs:10,children:[B("Variants"),f(st,{children:["Generic Article: ",it]})]}),e(ve,{item:!0,xs:2,sx:{display:"flex",justifyContent:"flex-end"},children:e(cn,{"aria-label":"close",onClick:()=>Nn(!1),sx:{color:t=>t.palette.grey[500]},size:"large",children:e(Dl,{})})})]})}),e(Be,{sx:{px:10,py:1},children:f(So,{activeStep:pt,sx:{justifyContent:"center"},children:[e(Si,{children:e(mi,{onClick:()=>$t(0),children:B("Classification")})}),e(Si,{children:e(mi,{onClick:()=>$t(1),children:B("Variants")})})]})}),e(Be,{sx:{display:"flex",justifyContent:"end",mr:4},children:pt===1&&e(bn,{title:B("Generate Variant IDs"),disableInteractive:!0,placement:"top",children:e("span",{children:e(nt,{onClick:()=>{const t=(ls[En]||[]).map((s,r)=>({...s,id:s.item?`${it}${(r+1).toString().padStart(2,"0")}`:""}));vn(s=>({...s,[En]:t})),I(xs({materialID:be,keyName:"TovariantData",data:t}))},variant:"outlined",color:"primary",children:e(po,{sx:{verticalAlign:"middle"}})})})})}),pt===0&&f(Ut,{children:[f(es,{children:[f(ve,{md:12,sx:{backgroundColor:"white",maxHeight:"max-content",height:"max-content",borderRadius:"8px",border:`1px solid ${We.hover.hoverbg} `,mt:.25,boxShadow:"0px 2px 14px 0px rgba(48, 38, 185, 0.10)",...cl},children:[e(ve,{container:!0,children:e(st,{sx:{fontSize:"12px",fontWeight:"700",paddingBottom:"10px"},children:V.Classification&&Object.keys(V.Classification).length>0?(ir=Object.entries(V.Classification||{})[0])==null?void 0:ir[0]:{}})}),e(Be,{children:e(ve,{container:!0,spacing:1,children:(or=(rr=Object.entries(V.Classification||{})[0])==null?void 0:rr[1])==null?void 0:or.map(t=>e(Ut,{children:e(il,{field:t,dropDownData:kt,materialID:be,selectedMaterialNumber:Rn,viewName:"Classification",plantData:"basic",matType:"Article"},t.fieldName)}))})})]},V.Classification&&Object.keys(V.Classification).length>0?(cr=Object.entries(V.Classification||{})[0])==null?void 0:cr[0]:{}),e(Bo,{characteristicDetails:["Characteristic Details"],materialID:be,selectedMaterialNumber:Rn,disabled:ze&&!Ls.includes(n==null?void 0:n.requestStatus),dropDownData:kt,activeViewTab:"Classification"})]}),e(ts,{children:e(nt,{onClick:()=>mc(),variant:"contained",color:"primary",children:B("Next")})})]}),pt===1&&f(Ut,{children:[e(es,{children:ge&&Gn?Array.isArray(is[En])&&e(Be,{mb:2,children:f(pl,{children:[e(Wl,{children:f(ll,{children:[e(je,{children:e("b",{children:B("Size Ch. Value")})}),e(je,{children:e("b",{children:B("Colour Ch. Value")})}),e(je,{children:B("")}),e(je,{children:e("b",{children:B("Generic ID")})})]})}),e(Hl,{children:(ar=is[En])==null?void 0:ar.map((t,s)=>f(ll,{children:[e(je,{children:t==null?void 0:t.size}),e(je,{children:t==null?void 0:t.color}),e(je,{children:e(al,{checked:t==null?void 0:t.item,onChange:r=>{const h=ls[En].map((d,g)=>g===s?{...d,item:r.target.checked}:d);vn(d=>({...d,[En]:h}))}})}),e(je,{children:t==null?void 0:t.id})]},t.id))})]})}):Array.isArray(ls[En])&&e(Be,{mb:2,children:f(pl,{children:[e(Wl,{children:f(ll,{children:[e(je,{children:e("b",{children:B("Size Ch. Value")})}),e(je,{children:e("b",{children:B("Colour Ch. Value")})}),e(je,{children:B("")}),e(je,{children:e("b",{children:B("Generic ID")})})]})}),e(Hl,{children:(dr=ls[En])==null?void 0:dr.map((t,s)=>f(ll,{children:[e(je,{children:t==null?void 0:t.size}),e(je,{children:t==null?void 0:t.color}),e(je,{children:e(al,{checked:t==null?void 0:t.item,onChange:r=>{const h=ls[En].map((d,g)=>g===s?{...d,item:r.target.checked}:d);vn(d=>({...d,[En]:h}))}})}),e(je,{children:t==null?void 0:t.id})]},s))})]})})}),f(ts,{children:[e(nt,{onClick:()=>$t(t=>t-1),variant:"outlined",color:"primary",children:B("Back")}),e(nt,{onClick:()=>{Nn(!1),Yn({id:de,field:"materialNumber",value:it})},variant:"contained",color:"primary",children:B("Save")})]})]})]}),Kn&&e(Ws,{fullWidth:!0,maxWidth:!1,open:!0,onClose:ti,sx:{display:"flex",justifyContent:"center"},disableEscapeKeyDown:!0,children:f(Be,{sx:{width:"600px !important"},children:[f(Hs,{sx:{backgroundColor:"#EAE9FF",marginBottom:".5rem"},children:[e(yl,{style:{height:"20px",width:"20px",marginBottom:"-5px"}}),e("span",{children:B("Select Views")})]}),e(es,{sx:{paddingBottom:".5rem"},children:f(Be,{display:"flex",alignItems:"center",sx:{flex:1,padding:"22px 0px",gap:"5px"},children:[e(ul,{size:"small",multiple:!0,fullWidth:!0,options:(we==null?void 0:we[de])||[],disableCloseOnSelect:!0,value:p,slotProps:{paper:{sx:{maxHeight:250,overflow:"hidden"}}},onChange:(t,s)=>{a!=null&&a.requestId||(Ce([...ol,...s.filter(r=>!ol.includes(r))]),Yn({id:de,field:fi.VIEWS,value:s}))},renderOption:(t,s,{selected:r})=>f("li",{...t,children:[e(al,{checked:r,sx:{marginRight:1}}),s]}),renderTags:(t,s)=>t.map((r,h)=>{const{key:d,...g}=s({index:h});return e(Ri,{label:r,...g},d)}),renderInput:t=>e(Sn,{...t,label:B("Select Views")})}),e(nt,{variant:"contained",size:"small",onClick:()=>Vo(),children:B("Select all")})]})}),e(ts,{children:e(nt,{onClick:()=>{an(!1),Yn({id:de,field:"views",value:p})},variant:"contained",children:B("Ok")})})]})}),Tn&&f(Ws,{fullWidth:!0,maxWidth:"xl",open:!0,onClose:ti,disableEscapeKeyDown:!0,sx:{"& .MuiDialog-paper":{padding:2,borderRadius:2}},children:[f(Hs,{sx:{display:"flex",alignItems:"center",gap:1,backgroundColor:"#EAE9FF"},children:[e(yl,{fontSize:"small"}),e("span",{children:B("Select Area of Validity")}),e(Be,{sx:{position:"absolute",right:"7%",width:"15%"},children:e(ul,{options:zn.filter(t=>t.region===(E==null?void 0:E.Region)),value:Qn,size:"small",disabled:Ul,isOptionEqualToValue:(t,s)=>t.region===s.region,onChange:(t,s)=>{hs(s),Tc(s)},getOptionLabel:t=>t==null?void 0:t.temp,renderInput:t=>e(Sn,{...t,label:B("Select Template"),sx:{minWidth:165}}),sx:{"& .MuiAutocomplete-popper":{minWidth:250}}})}),e(cn,{onClick:ti,sx:{position:"absolute",right:15},children:e(Dl,{})})]}),e(es,{sx:{padding:0},children:e(mo,{component:Yl,children:f(pl,{children:[e(Wl,{children:f(ll,{children:[e(je,{align:"center",children:B("S NO.")}),e(je,{align:"center",children:B("Sales Org")}),e(je,{align:"center",children:B("Distribution Channel")}),e(je,{align:"center",children:B("Plant")}),e(je,{align:"center",children:B("Distribution Center")}),e(je,{align:"center",children:B("Purchasing Org")}),e(je,{align:"center",children:B("Supplier")}),e(je,{align:"center",children:B("Store")}),e(je,{align:"center",children:B("Sales Unit")}),e(je,{align:"center",children:B("Plant Group")}),e(je,{align:"center",children:B("Price List")}),P.length>1&&e(je,{align:"center",children:B("Action")})]})}),e(Hl,{children:P.map((t,s)=>{var r,h,d,g,k,X,Pe,ce,me,w,Ue,at,Nt,un,$n,qt,rs,$s,Ps,tl,nl,sl,js,Js,hn,Ms,xn,os,_l,hr,gr,Tr,Er,Ar,Sr,mr,fr,Nr,Cr,_r,Or,Ir,br,Rr,Mr,xr,Dr,Lr,yr;return f(ll,{sx:{padding:"12px"},children:[e(je,{children:e(st,{variant:"body2",children:s+1})}),e(je,{children:e(Qt,{options:kt["Sales Organization"],value:t.salesOrg,onChange:In=>nc("Sales Organization",In,s),placeholder:B("Select Sales Org"),minWidth:165,listWidth:215,title:((r=t==null?void 0:t.salesOrg)==null?void 0:r.code)+` - ${(h=t==null?void 0:t.salesOrg)==null?void 0:h.desc}`,disabled:!li(ui.salesOrg,p)})}),e(je,{children:e(Qt,{options:((d=t.dc)==null?void 0:d.options)||[],isLoading:((g=Ze["Distribution Channel"])==null?void 0:g[s])||!1,value:(k=t.dc)==null?void 0:k.value,onChange:In=>sc(In,s),placeholder:B("Select DC"),disabled:!li(ui.distributionChannel,p),minWidth:165,listWidth:215,title:((Pe=(X=t==null?void 0:t.dc)==null?void 0:X.value)==null?void 0:Pe.code)+` - ${(me=(ce=t==null?void 0:t.dc)==null?void 0:ce.value)==null?void 0:me.desc}`})}),e(je,{children:e(Qt,{options:kt.Plant||[],value:(w=t.plant)==null?void 0:w.value,onChange:In=>ic(In,s),placeholder:B("Select Plant"),disabled:!li(ui.plant,p),minWidth:165,listWidth:215,title:((at=(Ue=t==null?void 0:t.plant)==null?void 0:Ue.value)==null?void 0:at.code)+` - ${(un=(Nt=t==null?void 0:t.plant)==null?void 0:Nt.value)==null?void 0:un.desc}`})}),e(je,{children:e(Qt,{options:kt.Plant||[],value:($n=t.distributionCenter)==null?void 0:$n.value,onChange:In=>rc(In,s),placeholder:B("Select Distribution Center"),minWidth:165,listWidth:215,title:((rs=(qt=t==null?void 0:t.distributionCenter)==null?void 0:qt.value)==null?void 0:rs.code)+` - ${(Ps=($s=t==null?void 0:t.distributionCenter)==null?void 0:$s.value)==null?void 0:Ps.desc}`})}),e(je,{children:e(Qt,{options:Id||[],value:(tl=t.purchasingOrg)==null?void 0:tl.value,onChange:In=>oc(In,s),placeholder:B("Select Purchasing Org"),minWidth:165,listWidth:215,title:((sl=(nl=t==null?void 0:t.purchasingOrg)==null?void 0:nl.value)==null?void 0:sl.code)+` - ${(Js=(js=t==null?void 0:t.purchasingOrg)==null?void 0:js.value)==null?void 0:Js.desc}`})}),e(je,{children:e(Qt,{options:Un.Json301||[],value:(hn=t.supplier)==null?void 0:hn.value,onChange:In=>cc(In,s),placeholder:B("Select Supplier"),minWidth:165,listWidth:215,title:((xn=(Ms=t==null?void 0:t.supplier)==null?void 0:Ms.value)==null?void 0:xn.code)+` - ${(_l=(os=t==null?void 0:t.supplier)==null?void 0:os.value)==null?void 0:_l.desc}`})}),e(je,{children:e(Qt,{options:kt.Plant||[],value:(hr=t.store)==null?void 0:hr.value,onChange:In=>ac(In,s),placeholder:B("Select Store"),minWidth:165,listWidth:215,title:((Tr=(gr=t==null?void 0:t.store)==null?void 0:gr.value)==null?void 0:Tr.code)+` - ${(Ar=(Er=t==null?void 0:t.store)==null?void 0:Er.value)==null?void 0:Ar.desc}`})}),e(je,{children:e(Qt,{options:bd||[],value:(Sr=t.salesUnit)==null?void 0:Sr.value,onChange:In=>dc(In,s),placeholder:B("Select Sales Unit"),minWidth:165,listWidth:215,title:((fr=(mr=t==null?void 0:t.salesUnit)==null?void 0:mr.value)==null?void 0:fr.code)+` - ${(Cr=(Nr=t==null?void 0:t.salesUnit)==null?void 0:Nr.value)==null?void 0:Cr.desc}`})}),e(je,{children:e(Qt,{options:Rd||[],value:(_r=t.plantGroup)==null?void 0:_r.value,onChange:In=>uc(In,s),placeholder:B("Select Plant Group"),minWidth:165,listWidth:215,title:((Ir=(Or=t==null?void 0:t.plantGroup)==null?void 0:Or.value)==null?void 0:Ir.code)+` - ${(Rr=(br=t==null?void 0:t.plantGroup)==null?void 0:br.value)==null?void 0:Rr.desc}`})}),e(je,{children:e(Qt,{options:Md||[],value:(Mr=t.priceList)==null?void 0:Mr.value,onChange:In=>hc(In,s),placeholder:B("Select Price List"),minWidth:165,listWidth:215,title:((Dr=(xr=t==null?void 0:t.priceList)==null?void 0:xr.value)==null?void 0:Dr.code)+` - ${(yr=(Lr=t==null?void 0:t.priceList)==null?void 0:Lr.value)==null?void 0:yr.desc}`})}),P.length>1&&f(je,{align:"right",children:[e(cn,{size:"small",color:"primary",onClick:()=>{$(!0),Qe({orgRowLength:P.length,copyFor:s});const In=P.filter(ni=>{var Ol,Il;return(Il=(Ol=ni.plant)==null?void 0:Ol.value)==null?void 0:Il.code}).map(ni=>{var Ol,Il;return(Il=(Ol=ni.plant)==null?void 0:Ol.value)==null?void 0:Il.code});$e==="yes"&&Qo(In)},style:{display:s===0?"none":"inline-flex"},children:e(_i,{})}),e(cn,{size:"small",color:"error",onClick:()=>lc(s),children:e(dl,{})})]})]},s)})})]})})}),f(ts,{sx:{justifyContent:"flex-end",gap:.5},children:[f(nt,{onClick:gc,variant:"contained",disabled:!fs,children:["+ ",B("Add")]}),e(bn,{title:fs?"":B("Please fill all the fields of first row at least"),arrow:!0,children:e("span",{children:e(nt,{onClick:()=>{var t,s;if(At(!1),(s=(t=P[0].plant)==null?void 0:t.value)!=null&&s.code){pi(P),Yn({id:de,field:"orgData",value:P}),Je(P);const r=b==null?void 0:b.map(h=>h.id===be?{...h,orgData:P}:h);if(I(Pn(r)),$e==="no"){const h=P.filter(d=>{var g,k;return(k=(g=d.plant)==null?void 0:g.value)==null?void 0:k.code}).map(d=>{var g,k;return(k=(g=d.plant)==null?void 0:g.value)==null?void 0:k.code});h.length>0&&Ko(h)}hs(null)}},variant:"contained",disabled:!fs,tooltip:fs?"":B("Please fill all the fields of first row at least"),children:B("Ok")})})})]})]}),Ae&&f(Ws,{fullWidth:!0,open:!0,maxWidth:"lg",sx:{"&::webkit-scrollbar":{width:"1px"}},children:[e(Hs,{sx:{display:"flex",alignItems:"center",justifyContent:"space-between",padding:"0.75rem 1rem",backgroundColor:"#EAE9FF",borderBottom:"1px solid #d6d6f0"},children:f(Be,{sx:{display:"flex",alignItems:"center"},children:[e(ld,{sx:{mr:1,color:"#3C3C66"}}),e(st,{variant:"h6",sx:{fontWeight:600,color:"#3C3C66"},children:B("Add New Article")})]})}),f(es,{sx:{padding:".5rem 1rem",alignItems:"center",justifyContent:"center",margin:"0px 25px"},children:[f(ro,{component:"fieldset",sx:{paddingBottom:"2%"},children:[e(wc,{component:"legend",sx:{padding:"15px 0px",fontWeight:"600",fontSize:"15px"},children:B("How would you like to proceed?")}),f(jc,{row:!0,"aria-label":"profit-center-number",name:"profit-center-number",value:$e,onChange:t=>Ft(t.target.value),children:[e(Ai,{value:"yes",control:e(qr,{}),label:B("With Reference")}),e(Ai,{value:"no",control:e(qr,{}),label:B("Without Reference")})]})]}),f(ve,{container:!0,spacing:2,children:[e(ve,{item:!0,xs:12,children:f(ve,{container:!0,spacing:2,children:[e(ve,{item:!0,xs:3,children:e(Qt,{options:Hr||[],value:ut[ps.MATERIAL_TYPE]||"",onChange:t=>{_n(s=>({...s,[ps.MATERIAL_TYPE]:t}))},placeholder:B("Select Article Type"),minWidth:180,listWidth:266,disabled:(_e==null?void 0:_e.length)||$e==="no",getOptionLabel:t=>t!=null&&t.desc?`${t.code} - ${t.desc}`:(t==null?void 0:t.code)||"",renderOption:(t,s)=>f("li",{...t,children:[e("strong",{children:s==null?void 0:s.code}),s!=null&&s.desc?` - ${s==null?void 0:s.desc}`:""]})})}),e(ve,{item:!0,xs:3,children:e(Qt,{options:mt,value:Tt||le,onChange:t=>{Ve(t),Ie(t),t||ki(t)},minWidth:180,listWidth:266,placeholder:B("Select Article"),disabled:(_e==null?void 0:_e.length)||$e==="no",getOptionLabel:t=>t!=null&&t.desc?`${t.code} - ${t.desc}`:(t==null?void 0:t.code)||"",renderOption:(t,s)=>f("li",{...t,children:[e("strong",{children:s==null?void 0:s.code}),s!=null&&s.desc?` - ${s==null?void 0:s.desc}`:""]}),handleInputChange:ki,sx:{minWidth:270,"& .MuiAutocomplete-popper":{minWidth:306}},isLoading:Ze["Material No"]})}),Fn==null?void 0:Fn.slice(0,2).map(t=>e(ve,{item:!0,xs:3,children:e(Qt,{options:(kt==null?void 0:kt[t])||[],value:ut[t]||"",onChange:s=>{tc(t,s)},placeholder:B(`Select ${t}`),minWidth:180,listWidth:306,sx:{minWidth:270,"& .MuiAutocomplete-popper":{minWidth:306}},disabled:(_e==null?void 0:_e.length)||$e==="no",isLoading:Ze[t]})},t))]})}),e(ve,{item:!0,xs:12,children:f(ve,{container:!0,spacing:2,alignItems:"center",children:[e(ve,{item:!0,xs:3,children:e(Qt,{options:(kt==null?void 0:kt[Fn[2]])||[],value:ut[Fn[2]]||"",onChange:t=>{_n(s=>({...s,[Fn[2]]:t}))},placeholder:B(`Select ${Fn[2]}`),minWidth:180,listWidth:306,sx:{minWidth:270,"& .MuiAutocomplete-popper":{minWidth:306}},disabled:(_e==null?void 0:_e.length)||$e==="no",isLoading:Ze["Distribution Channel"]===!0})}),Fn==null?void 0:Fn.slice(3).map(t=>e(ve,{item:!0,xs:3,children:e(Qt,{options:(kt==null?void 0:kt[t])||[],value:ut[t]||"",onChange:s=>{_n(r=>({...r,[t]:s}))},placeholder:B(`Select ${t}`),minWidth:180,listWidth:306,sx:{minWidth:270,"& .MuiAutocomplete-popper":{minWidth:306}},disabled:(_e==null?void 0:_e.length)||$e==="no",isLoading:Ze[t]})},t)),(u==null?void 0:u.length)>0&&f(Ut,{children:[e(ve,{item:!0,xs:1,sx:{textAlign:"center"},children:e(st,{variant:"body1",sx:{fontWeight:"bold",color:"gray"},children:"OR"})}),e(ve,{item:!0,xs:3,children:e(Qt,{options:u.map(t=>({...t,code:t.lineNumber,desc:""})),value:_e[0],onChange:t=>{fn(t?[t]:[]),_n({}),Ve(null),Ie(null)},minWidth:180,listWidth:266,placeholder:B("Select Article Line Number"),disabled:(le==null?void 0:le.code)||$e==="no",getOptionLabel:t=>t!=null&&t.desc?`${t.code} - ${t.desc}`:(t==null?void 0:t.code)||"",renderOption:(t,s)=>f("li",{...t,children:[e("strong",{children:s==null?void 0:s.code}),s!=null&&s.desc?` - ${s==null?void 0:s.desc}`:""]}),sx:{minWidth:270,"& .MuiAutocomplete-popper":{minWidth:306}}})})]})]})})]})]}),f(ts,{sx:{display:"flex",justifyContent:"end"},children:[e(nt,{sx:{width:"max-content",textTransform:"capitalize"},onClick:()=>Me(!1),variant:"outlined",children:B("Cancel")}),e(nt,{className:"button_primary--normal",type:"save",disabled:!(_e!=null&&_e.length||le!=null&&le.code)&&$e==="yes",onClick:wo,variant:"contained",children:B("Proceed")})]})]}),(L==null?void 0:L.isVisible)&&f(Xl,{isOpen:L==null?void 0:L.isVisible,titleIcon:e(dl,{size:"small",color:"error",sx:{fontSize:"20px"}}),Title:B("Delete Row")+"!",handleClose:()=>q({...L,isVisible:!1}),children:[e(es,{sx:{mt:2},children:B(Zs.DELETE_MESSAGE)}),f(ts,{children:[e(nt,{variant:"outlined",size:"small",sx:{...Di},onClick:()=>q({...L,isVisible:!1}),children:B(wl.CANCEL)}),e(nt,{variant:"contained",size:"small",sx:{...zl},onClick:Ec,children:B(wl.DELETE)})]})]}),vs&&e(Ho,{open:vs,onClose:()=>$(!1),title:Ii.COPY_ORG_DATA_VALES_HEADING,selectedMaterialPayload:Rs,lengthOfOrgRow:Ke,materialID:be,orgRows:P}),dn&&e(Cl,{openSnackBar:Hn,alertMsg:dn,alertType:tt,handleSnackBarClose:()=>ds(!1)}),de&&Xe&&e(md,{open:Xe,onClose:()=>{Et(!1)},articleNumber:de,articleDetails:(ur=b==null?void 0:b.filter(t=>t.id===de)[0])==null?void 0:ur.articleComponents,handleSave:Yn}),e(hl,{blurLoading:gs,loaderMessage:ms}),e(Jl,{})]})},Dd=({openSearchMat:n,setOpenSearchMat:N,AddCopiedMaterial:ae})=>{const[I,M]=i.useState(!1),x=Z(E=>E.AllDropDown.dropDown),ue={Extend:[{key:"Material Type",options:_o},{key:"Material Number",options:[]},{key:"Plant",options:[]},{key:"Sales Org",options:[]},{key:"Distribution Channel",options:[]},{key:"Storage Location",options:[]},{key:"Division",options:[]}]},D=(E,Se="0",oe)=>{var j,u,ie,W,V,a,K,Le,Fe,ze,ge,Te;const z={materialNo:((u=(j=E==null?void 0:E["Material Number"])==null?void 0:j.map(ee=>ee.code))==null?void 0:u.join(","))??"",division:((W=(ie=E==null?void 0:E.Division)==null?void 0:ie.map(ee=>ee.code))==null?void 0:W.join(","))??"",plant:((a=(V=E==null?void 0:E.Plant)==null?void 0:V.map(ee=>ee.code))==null?void 0:a.join(","))??"",salesOrg:((Le=(K=E==null?void 0:E["Sales Org"])==null?void 0:K.map(ee=>ee.code))==null?void 0:Le.join(","))??"",distrChan:((ze=(Fe=E==null?void 0:E["Distribution Channel"])==null?void 0:Fe.map(ee=>ee.code))==null?void 0:ze.join(","))??"",storageLocation:((Te=(ge=E==null?void 0:E["Storage Location"])==null?void 0:ge.map(ee=>ee.code))==null?void 0:Te.join(","))??"",top:200,skip:Se},R=ee=>{var re;if((ee==null?void 0:ee.statusCode)===wt.STATUS_200){const H=(re=ee==null?void 0:ee.body)==null?void 0:re.map(fe=>{if(fe.Views){const Q=fe.Views.split(",").map(Re=>Re.trim()).filter(Re=>!Li.includes(Re)).join(",");return{...fe,Views:Q}}return fe});ae(H||[]),oe==null||oe(H||[]),M(!1)}},_=()=>{M(!1),oe==null||oe([])};M(!0),qe(`/${C}${Ot.DATA.GET_EXTEND_SEARCH_SET}`,"post",R,_,z)};return f(Ut,{children:[e(Ba,{open:n,onClose:()=>N(!1),parameters:ue.Extend,onSearch:(E,Se,oe)=>D(E,Se,oe),templateName:"Extend",name:"Extend",allDropDownData:x,buttonName:"Search"}),e(hl,{blurLoading:I})]})},Ld=ho(()=>({customTabs:{"& .MuiTabs-scroller":{overflowX:"auto !important",overflowY:"hidden !important"}}})),Ns={NOT_EXTENDED:"notExtended",EXTENDED:"Extended"},yd=n=>{var ft,Vn,Ts,Es,Gs;const N=Ld(),{customError:ae}=Os(),I=as(),{fetchMaterialFieldConfig:M}=vi(),{getNextDisplayDataForCreate:x}=yi(),{fetchValuationClassData:ue}=Ro(),D=Z(l=>l.payload.payloadData),E=D==null?void 0:D.RequestType,Se=Z(l=>l.applicationConfig),oe=Z(l=>l.paginationData),z=Z(l=>l.payload),R=Z(l=>l.request.materialRows),_=Z(l=>{var o;return((o=l.AllDropDown)==null?void 0:o.dropDown)||{}}),j=Z(l=>l.tabsData.allTabsData);let u=Z(l=>l.userManagement.taskData);const ie=Z(l=>l.tabsData.allMaterialFieldConfigDT),W=rl(),V=new URLSearchParams(W.search),a=V.get("RequestId"),K=V.get("RequestType"),[Le,Fe]=i.useState(0),[ze,ge]=i.useState(null),[Te,ee]=i.useState(null),re="Basic Data",[H,fe]=i.useState([re]),[Q,Re]=i.useState([]),[he,te]=i.useState(R||[]),Mt=Z(l=>l.selectedSections.selectedSections),[T,J]=i.useState(!1),[p,Ce]=i.useState(!1),[L,q]=i.useState(""),[b,se]=i.useState([]),[O,St]=i.useState(0),[ye,Oe]=i.useState({code:"",desc:""}),[ht,It]=i.useState(!1),{fetchDataAndDispatch:bt}=xo(),[dt,tn]=i.useState(!0),[Ct,mt]=i.useState(he.length+1),[gn,Jt]=i.useState(0),[Rt,Tt]=i.useState(R.length>0),[Ie,pe]=i.useState({}),[Y,ne]=i.useState({}),[Ge,Xe]=i.useState([]),[Et,y]=i.useState({}),[ke,yt]=i.useState([]),[Dn,Zt]=i.useState(!1),[nn,Wt]=i.useState(""),[lt,kt]=i.useState("Basic Data"),[Bt,Ln]=i.useState(!1),[xt,rn]=i.useState(null),Wn=Z(l=>l.request.salesOrgDTData),yn=(ft=z==null?void 0:z[xt])==null?void 0:ft.headerData,Xt=(D==null?void 0:D.Region)===_s.EUR?{id:0,salesOrg:null,dc:{value:null,options:[]},plant:{value:null,options:[]},sloc:{value:null,options:[]},mrpProfile:null}:{id:0,salesOrg:null,dc:{value:null,options:[]},plant:{value:null,options:[]},sloc:{value:null,options:[]},warehouse:{value:null,options:[]},mrpProfile:null},[we,on]=i.useState([Xt]),[Kn,an]=i.useState([]),[de,vt]=i.useState({id:1,plant:{value:null,options:[]},salesOrg:null,dc:{value:null,options:[]},sloc:{value:null,options:[]},mrpProfile:null,warehouse:{value:null,options:[]}}),[jn,Jn]=i.useState(!1),[Tn,At]=i.useState({}),[mn,P]=i.useState("success"),A=(Vn=z==null?void 0:z[xt])==null?void 0:Vn.payloadData,[Ae,Me]=i.useState(!1),[le,Ve]=i.useState([]),[$e,Ft]=i.useState(""),[_e,fn]=i.useState([]),{getDynamicWorkflowDT:be}=Go(),[jt,Gt]=i.useState(!1),[tt,Xn]=i.useState(!1),[Hn,ds]=i.useState(!1),[ss,_t]=i.useState(""),[dn,Ht]=i.useState({"Sales Organization":!1,"Distribution Channel":{},Plant:{},"Storage Location":{},warehouse:{},"Mrp Profile":!1}),[qn,v]=i.useState(!1),[Rn,Ss]=i.useState(0),{fetchTabSpecificData:en}=Mo(),{getContryBasedOnPlant:us}=Do({doAjax:qe,customError:ae,fetchDataAndDispatch:bt,destination_ArticleMgmt:C}),{extendFilteredButtons:B,showWfLevels:Is}=ed(u,Se,Bs,wn),Bn=xi(B,[qs.HANDLE_SUBMIT_FOR_APPROVAL,qs.HANDLE_SAP_SYNDICATION,qs.HANDLE_SUBMIT_FOR_REVIEW,qs.HANDLE_SUBMIT]),Ne=l=>{!l||!Array.isArray(l)||l.forEach(o=>{var m,U,F,Ee,xe,De,ot,Je,Lt,ct,Vt,ln,Dt,Mn,On,As;if((U=(m=o.plant)==null?void 0:m.value)!=null&&U.code){if(en((Ee=(F=o.plant)==null?void 0:F.value)==null?void 0:Ee.code,S.PLANT),(xe=o.salesOrg)!=null&&xe.code||(ot=(De=o.dc)==null?void 0:De.value)!=null&&ot.code){const ws=`${((Je=o.salesOrg)==null?void 0:Je.code)||""}-${((ct=(Lt=o.dc)==null?void 0:Lt.value)==null?void 0:ct.code)||""}`;en(ws,S.SALES)}(ln=(Vt=o.warehouse)==null?void 0:Vt.value)!=null&&ln.code&&en((Mn=(Dt=o.warehouse)==null?void 0:Dt.value)==null?void 0:Mn.code,S.WAREHOUSE),us((As=(On=o.plant)==null?void 0:On.value)==null?void 0:As.code)}})},Nn=l=>{if(!l||!Array.isArray(l))return[];let o=(D==null?void 0:D.Region)===_s.EUR?l==null?void 0:l.filter(m=>m!==S.WAREHOUSE&&m!==S.WORKSCHEDULING&&m!==S.WORK_SCHEDULING):[...l];return o.sort((m,U)=>m===S.BASIC_DATA?-1:U===S.BASIC_DATA?1:0),o},ls=async()=>{var l,o;try{const m=await be(E,D==null?void 0:D.Region,"",(o=(l=z[xt])==null?void 0:l.Tochildrequestheaderdata)==null?void 0:o.MaterialGroupType,u==null?void 0:u.ATTRIBUTE_3);fn(m)}catch(m){ae(m)}};i.useEffect(()=>{E&&(D!=null&&D.Region)&&xt&&(u!=null&&u.ATTRIBUTE_3)&&ls()},[E,D==null?void 0:D.Region,xt,u==null?void 0:u.ATTRIBUTE_3]),i.useEffect(()=>{var l,o,m,U,F,Ee,xe,De;(U=(m=(o=(l=z[xt])==null?void 0:l.payloadData)==null?void 0:o[S.CLASSIFICATION])==null?void 0:m.basic)!=null&&U.Classtype&&Mi((De=(xe=(Ee=(F=z[xt])==null?void 0:F.payloadData)==null?void 0:Ee[S.CLASSIFICATION])==null?void 0:xe.basic)==null?void 0:De.Classtype,I)},[xt]),i.useEffect(()=>{var l,o,m,U,F,Ee,xe,De,ot,Je,Lt,ct,Vt,ln,Dt;if(te(R),Tt((R==null?void 0:R.length)>0),(R==null?void 0:R.length)>0){rn((l=R==null?void 0:R[0])==null?void 0:l.id),pt(((m=(o=R==null?void 0:R[0])==null?void 0:o.materialType)==null?void 0:m.code)||((U=R==null?void 0:R[0])==null?void 0:U.materialType)),Jt(0),_t((F=R==null?void 0:R[0])==null?void 0:F.materialNumber),kt(S.BASIC_DATA),fe((xe=(Ee=R==null?void 0:R[0])==null?void 0:Ee.views)!=null&&xe.length?Nn((De=R==null?void 0:R[0])==null?void 0:De.views):Nn([re]));const Mn=go(z),On=To(Mn);let As=JSON.parse(JSON.stringify(On));I(Eo(As)),I(ml({keyName:"selectedMaterialID",data:(ot=R==null?void 0:R[0])==null?void 0:ot.id})),(ct=(Lt=z==null?void 0:z[(Je=R==null?void 0:R[0])==null?void 0:Je.id])==null?void 0:Lt.Tochildrequestheaderdata)!=null&&ct.ChildRequestId&&I(ml({keyName:"childRequestId",data:(Dt=(ln=z==null?void 0:z[(Vt=R==null?void 0:R[0])==null?void 0:Vt.id])==null?void 0:ln.Tochildrequestheaderdata)==null?void 0:Dt.ChildRequestId}))}},[R]),i.useEffect(()=>{(he==null?void 0:he.length)===0&&J(!1)},[he]),i.useEffect(()=>{["Sales Organization","Mrp Profile"].forEach(fs)},[]),i.useEffect(()=>{if(a){const l=yn==null?void 0:yn.orgData;(l==null?void 0:l.length)>0&&l.some(o=>{var m,U,F,Ee,xe;return((U=(m=o.plant)==null?void 0:m.value)==null?void 0:U.code)&&(((F=o.salesOrg)==null?void 0:F.code)||((xe=(Ee=o.dc)==null?void 0:Ee.value)==null?void 0:xe.code))})&&Ne(l)}},[yn==null?void 0:yn.orgData]),i.useEffect(()=>{var F,Ee,xe,De;const l=H==null?void 0:H.includes((F=S)==null?void 0:F.SALES),o=H==null?void 0:H.includes((Ee=S)==null?void 0:Ee.SALES_PLANT),m=H==null?void 0:H.includes((xe=S)==null?void 0:xe.STORAGE),U=H==null?void 0:H.includes((De=S)==null?void 0:De.STORAGE_PLANT);l&&!o&&fe(ot=>{var ct,Vt;const Je=[...ot],Lt=Je.indexOf((ct=S)==null?void 0:ct.SALES);return Je.splice(Lt+1,0,(Vt=S)==null?void 0:Vt.SALES_PLANT),Je}),m&&!U&&fe(ot=>{var ct,Vt;const Je=[...ot],Lt=Je.indexOf((ct=S)==null?void 0:ct.STORAGE);return Je.splice(Lt+1,0,(Vt=S)==null?void 0:Vt.STORAGE_PLANT),Je})},[H]);const vn=()=>{Un()},is=(l="",o=!1)=>{var Ee,xe,De,ot;const m={materialNo:l??"",top:500,skip:o?0:O,salesOrg:((xe=(Ee=Wn==null?void 0:Wn.uniqueSalesOrgList)==null?void 0:Ee.map(Je=>Je.code))==null?void 0:xe.join("$^$"))||""},U=Je=>{(Je==null?void 0:Je.statusCode)===wt.STATUS_200&&(se(o?Je==null?void 0:Je.body:Lt=>[...Lt,...Je==null?void 0:Je.body]),It(!1))},F=()=>{It(!1)};It(!0),qe(`/${C}${(ot=(De=Ot)==null?void 0:De.DATA)==null?void 0:ot.GET_SEARCH_PARAMS_MATERIAL_NO}`,"post",U,F,m)},sn=!Ls.includes(n==null?void 0:n.requestStatus);function En(l){const o=U=>{var F;if((U==null?void 0:U.statusCode)===wt.STATUS_200){let Ee=(F=U==null?void 0:U.body)==null?void 0:F.filter(xe=>!Li.includes(xe));Ee=Ee==null?void 0:Ee.map(xe=>xe==="Storage"?S.STORAGE:xe),(D==null?void 0:D.Region)===_s.EUR&&(Ee=Ee==null?void 0:Ee.filter(xe=>xe!==S.WAREHOUSE&&xe!==S.WORK_SCHEDULING&&xe!==S.WORKSCHEDULING)),yt(Ee)}},m=U=>{ae(U)};qe(`/${C}/data/getViewForMaterialType?materialType=${l}`,"get",o,m)}i.useEffect(()=>{is()},[]);const Ye=((Ts=Ge==null?void 0:Ge[1])==null?void 0:Ts.External)==="X",it=Ge==null?void 0:Ge.some(l=>l.ExtNAwock==="X");function pt(l){var U;const o=(D==null?void 0:D.Region)||_s.US;if(!ie.some(F=>F[o]&&F[o][l])&&l)M(l,o);else if(!l)I(fl({}));else{const F=ie==null?void 0:ie.find(Ee=>(Ee==null?void 0:Ee[o])&&(Ee==null?void 0:Ee[o][l]));F&&I(fl((U=F[o][l])==null?void 0:U.allfields))}l&&ue(l)}const $t=l=>{const{id:o,field:m,value:U}=l,F=he.map(Ee=>Ee.id===o?{...Ee,[m]:U}:Ee);y({...Et,[m]:U}),m===fi.MATERIALTYPE&&(En(U),fe([re]),fo([Xt]),I(xs({materialID:o,keyName:"views",data:[re]})),I(xs({materialID:o,keyName:"orgData",data:""})),pt(U==null?void 0:U.code)),te(F),I(xs({materialID:o,keyName:m,data:U}))},Gn=l=>{var o,m,U,F,Ee,xe,De,ot,Je;rn(l.row.id),At(l.row),_t(l.row.materialNumber),yt((o=l==null?void 0:l.row)==null?void 0:o.views),pt(((U=(m=l==null?void 0:l.row)==null?void 0:m.materialType)==null?void 0:U.code)||((F=l.row)==null?void 0:F.materialType)),fe((Ee=l==null?void 0:l.row)!=null&&Ee.views?(xe=l.row)==null?void 0:xe.views:[re]),on((ot=(De=l==null?void 0:l.row)==null?void 0:De.orgData)!=null&&ot.length?(Je=l.row)==null?void 0:Je.orgData:[Xt]),Jt(0),kt("Basic Data")},zt=()=>{Ce(!0)},Un=()=>{Ce(!1)},zn=(l,o)=>{o==="backdropClick"||o==="escapeKeyDown"||Ln(!1)},Qn=()=>fe(Nn(ke)),hs=l=>{if(Jn(!1),l!=null&&l.length){let o=[...he];l==null||l.forEach(m=>{var xe,De,ot;const U=m==null?void 0:m.Material;let F={...m},Ee=(xe=z==null?void 0:z[m.id])!=null&&xe.payloadData?JSON.parse(JSON.stringify((De=z==null?void 0:z[m.id])==null?void 0:De.payloadData)):"";F.id=U,F.globalMaterialDescription="",F.materialNumber="",F.included=!0,F.industrySector=m==null?void 0:m.IndSector,F.materialType=m==null?void 0:m.MatlType,F.materialNumber=m==null?void 0:m.Material,F.globalMaterialDescription=m==null?void 0:m.MaterialDescrption,F.views=m!=null&&m.Views?(ot=m==null?void 0:m.Views.split(","))==null?void 0:ot.map(Je=>Je.trim()==="Storage"?S.STORAGE:Je.trim()):[re],F.validated=!1,o.push(F),I(ql({materialID:U,data:F,payloadData:Ee}))}),Re(m=>[...m,...o.map(U=>({material:U==null?void 0:U.Material,views:U==null?void 0:U.views}))]),te(o),I(Pn(o)),mt(Ct+1),Tt(!0),tn(!0)}},gs=[{field:"included",headerName:"Included",flex:.5,align:"center",headerAlign:"center",renderCell:l=>{var o;return l!=null&&l.row?e(al,{checked:(o=l==null?void 0:l.row)==null?void 0:o.included,disabled:sn,onChange:m=>{var U;(U=l==null?void 0:l.row)!=null&&U.id&&$t({id:l.row.id,field:"included",value:m.target.checked})}}):null}},{field:"lineNumber",headerName:"Line Number",flex:.6,editable:E==="Create",align:"center",headerAlign:"center",renderCell:l=>{const m=((R==null?void 0:R.findIndex(U=>{var F;return(U==null?void 0:U.id)===((F=l==null?void 0:l.row)==null?void 0:F.id)}))+1)*10;return e("div",{children:m})}},{field:"industrySector",headerName:"Industry Sector",flex:1,align:"center",headerAlign:"center",renderCell:l=>{var o,m,U,F,Ee;return e(Qt,{options:(_==null?void 0:_.IndSector)||[],value:(o=l==null?void 0:l.row)==null?void 0:o.industrySector,onChange:xe=>$t({id:l.row.id,field:"industrySector",value:xe}),placeholder:"Select Industry Sector",minWidth:"90%",disabled:!0,listWidth:232,title:`${((U=(m=l.row)==null?void 0:m.industrySector)==null?void 0:U.code)||""} - ${((Ee=(F=l.row)==null?void 0:F.industrySector)==null?void 0:Ee.desc)||""}`})}},{field:"materialType",headerName:"Material Type",flex:1,align:"center",headerAlign:"center",renderCell:l=>{var o,m,U,F,Ee;return e(Qt,{options:_o||[],value:(o=l==null?void 0:l.row)==null?void 0:o.materialType,onChange:xe=>$t({id:l.row.id,field:"materialType",value:xe}),placeholder:"Select Material Type",disabled:!0,minWidth:"90%",listWidth:232,title:`${((U=(m=l.row)==null?void 0:m.materialType)==null?void 0:U.code)||""} - ${((Ee=(F=l.row)==null?void 0:F.materialType)==null?void 0:Ee.desc)||""}`})}},{field:"materialNumber",headerName:"Material Number",flex:E==="Extend"?.8:1,editable:!(!Ye&&!it),align:"center",headerAlign:"center",renderHeader:()=>f("span",{children:["Material Number",e("span",{style:{color:"red"},children:"*"})]}),renderCell:l=>{var o,m;return e(Ut,{children:(D==null?void 0:D.RequestType)===c.EXTEND?e(Sn,{fullWidth:!0,placeholder:"Enter Material Number",variant:"outlined",size:"small",name:"material number",value:(o=l==null?void 0:l.row)==null?void 0:o.materialNumber,sx:{"& .MuiInputBase-root.Mui-disabled":{"& > input":{WebkitTextFillColor:We.black.dark,color:We.black.dark}}},onChange:(U,F)=>$t({id:l.row.id,field:"materialNumber",value:F}),disabled:!Ye&&!it}):(m=l==null?void 0:l.row)==null?void 0:m.materialNumber})}},{field:"globalMaterialDescription",flex:E==="Extend"?.8:1,headerName:"Material Description",renderHeader:()=>f("span",{children:["Material Description",e("span",{style:{color:"red"},children:"*"})]}),renderCell:l=>{var o,m;return e(Ut,{children:(D==null?void 0:D.RequestType)===c.EXTEND?e(Sn,{fullWidth:!0,placeholder:"Enter Material Description",variant:"outlined",disabled:!0,size:"small",name:"material description",sx:{"& .MuiInputBase-root.Mui-disabled":{"& > input":{WebkitTextFillColor:We.black.dark,color:We.black.dark}}},onChange:(U,F)=>$t({id:l.row.id,field:"globalMaterialDescription",value:F}),value:(o=l==null?void 0:l.row)==null?void 0:o.globalMaterialDescription}):(m=l==null?void 0:l.row)==null?void 0:m.globalMaterialDescription})},align:"center",headerAlign:"center",editable:!0},{field:"views",headerName:"",flex:E==="Extend"?1.5:1,align:"center",headerAlign:"center",renderCell:l=>f(Fs,{direction:"row",spacing:0,alignItems:"center",children:[e(nt,{variant:"contained",size:"small",sx:{minWidth:80},onClick:()=>{var o,m;En(l.row.materialType),Zt(!0),Wt(l.row.id),At(l.row),fe((o=l==null?void 0:l.row)!=null&&o.Views?(m=l==null?void 0:l.row)==null?void 0:m.Views:[re])},children:"Views"}),e(Xr,{color:"disabled",fontSize:"small",sx:{mx:.5}}),e(nt,{variant:"contained",size:"small",sx:{minWidth:100},onClick:()=>{var o,m,U,F;Ln(!0),Wt(l.row.id),on((m=(o=l==null?void 0:l.row)==null?void 0:o.orgData)!=null&&m.length?(U=l.row)==null?void 0:U.orgData:[Xt]),At(l.row),Cn((F=l==null?void 0:l.row)==null?void 0:F.materialNumber,Ns.NOT_EXTENDED),Xn(!1)},children:"ORG Data"}),e(Xr,{color:"disabled",fontSize:"small",sx:{mx:.5}}),e(bn,{title:"Click after changing Views or ORG Data",children:e(cn,{onClick:()=>{var o,m;Ln(!0),Wt(l.row.id),Xn(!0),At(l.row),Cn((o=l==null?void 0:l.row)==null?void 0:o.materialNumber,Ns.EXTENDED),vt(Kn.find(U=>{var F;return U.id===((F=l.row)==null?void 0:F.id)})||{id:(m=l.row)==null?void 0:m.id,plant:null,salesOrg:null,dc:{value:null,options:[]},sloc:{value:null,options:[]},mrpProfile:null,warehouse:null})},disabled:sn,color:"primary",size:"small",children:e(_i,{})})})]})},{field:"action",headerName:"Action",flex:.9,align:"center",headerAlign:"center",renderCell:l=>e(Fs,{direction:"row",alignItems:"center",sx:{marginLeft:"0.5rem",magrinRight:"0.5rem"},spacing:.5,children:!a&&e(bn,{title:"Delete Row",children:e(cn,{onClick:()=>{te(he.filter(o=>o.id!==l.row.id)),I(Co(l.row.id)),I(Pn(he.filter(o=>o.id!==l.row.id))),he!=null&&he.length||J(!1)},color:"error",children:e(dl,{})})})})}],Cn=(l,o)=>{Ht(F=>({...F,"Sales Organization":!0}));const m=F=>{if((F==null?void 0:F.statusCode)===wt.STATUS_200){let Ee;o===Ns.NOT_EXTENDED?Ee=Cs(F.body):Ee=F.body.length>0?Cs(F.body):[],ne(xe=>({...xe,"Sales Organization":Ee}))}Ht(Ee=>({...Ee,"Sales Organization":!1}))},U=()=>{Ht(F=>({...F,"Sales Organization":!1}))};qe(`/${C}/data/${o===Ns.NOT_EXTENDED?"getSalesOrgNotExtended":"getSalesOrgExtended"}?materialNo=${l}&region=${D==null?void 0:D.Region}`,"get",m,U)},ms=(l,o,m,U)=>{Ht(De=>({...De,Plant:{...De.Plant,[U]:!0}}));const F=De=>{if((De==null?void 0:De.statusCode)===wt.STATUS_200){let ot;o===Ns.NOT_EXTENDED?ot=Cs(De.body):ot=De.body.length>0?Cs(De.body||[]):[],ne(Je=>({...Je,Plant:ot}))}Ht(ot=>({...ot,Plant:{...ot.Plant,[U]:!1}}))},Ee=()=>{Ht(De=>({...De,Plant:{...De.Plant,[U]:!1}}))},xe=m?`&salesOrg=${m.code}`:"";qe(`/${C}/data/${o===Ns.NOT_EXTENDED?"getPlantNotExtended":"getPlantExtended"}?materialNo=${l}&region=${D==null?void 0:D.Region}${xe}`,"get",F,Ee)},Zn=(l,o,m,U)=>{Ht(De=>({...De,warehouse:{...De.warehouse,[U]:!0}}));const F=De=>{if((De==null?void 0:De.statusCode)===wt.STATUS_200){let ot;o===Ns.NOT_EXTENDED?ot=Cs(De.body):ot=De.body.length>0?Cs(De.body||[]):[],ne(Je=>({...Je,warehouse:ot}))}Ht(ot=>({...ot,warehouse:{...ot.warehouse,[U]:!1}}))},Ee=()=>{Ht(De=>({...De,warehouse:{...De.warehouse,[U]:!1}}))},xe=m?`&plant=${m.code}`:"";qe(`/${C}/data/${o===Ns.NOT_EXTENDED?"getWarehouseNotExtended":"getWarehouseExtended"}?materialNo=${l}&region=${D==null?void 0:D.Region}${xe}`,"get",F,Ee)},kn=(l,o)=>{var m;Jt(o),kt(((m=l==null?void 0:l.target)==null?void 0:m.id)==="AdditionalKey"?"Additional Data":H==null?void 0:H[o])},fs=l=>{Ht(F=>({...F,[l]:!0}));const o={"Sales Organization":"/getSalesOrg","Mrp Profile":"/getMRPProfile"},m=F=>{if((F==null?void 0:F.statusCode)===wt.STATUS_200){const Ee=Cs(F.body);ne(xe=>({...xe,[l]:Ee}))}Ht(Ee=>({...Ee,[l]:!1}))},U=F=>{ae(F),Ht(Ee=>({...Ee,[l]:!1}))};qe(`/${C}/data${o[l]}`,"get",m,U)},bs=l=>{No(l,H,A,xt,we,I,Ks)},Rs=(l,o,m)=>(U,F)=>{var ct,Vt,ln;let Ee={},xe="",De="";m==="Purchasing"||m==="Costing"?(Ee={materialNo:o==null?void 0:o.Material,plant:o==null?void 0:o.Plant},De=o==null?void 0:o.Plant,xe=`/${C}/data/displayLimitedPlantData`):m==="Accounting"?(Ee={materialNo:o==null?void 0:o.Material,valArea:o==null?void 0:o.ValArea},De=o==null?void 0:o.ValArea,xe=`/${C}/data/displayLimitedAccountingData`):m==="Sales"&&(Ee={materialNo:o==null?void 0:o.Material,salesOrg:o==null?void 0:o.SalesOrg,distChnl:o==null?void 0:o.DistrChan},De=`${o==null?void 0:o.SalesOrg}-${o==null?void 0:o.DistrChan}`,xe=`/${C}/data/displayLimitedSalesData`);const ot=Dt=>{var Mn,On,As;(Dt==null?void 0:Dt.statusCode)===wt.STATUS_200&&(m==="Purchasing"||m==="Costing"?I(Ks({materialID:xt,viewID:m,itemID:o==null?void 0:o.Plant,data:(Mn=Dt==null?void 0:Dt.body)==null?void 0:Mn.SpecificPlantDataViewDto[0]})):m==="Accounting"?I(Ks({materialID:xt,viewID:m,itemID:o==null?void 0:o.ValArea,data:(On=Dt==null?void 0:Dt.body)==null?void 0:On.SpecificAccountingDataViewDto[0]})):m==="Sales"&&I(Ks({materialID:xt,viewID:m,itemID:`${o==null?void 0:o.SalesOrg}-${o==null?void 0:o.DistrChan}`,data:(As=Dt==null?void 0:Dt.body)==null?void 0:As.SpecificSalesDataViewDto[0]})))},Je=()=>{};!((ln=(Vt=(ct=z==null?void 0:z[xt])==null?void 0:ct.payloadData)==null?void 0:Vt[m])!=null&&ln[De])&&qe(xe,"post",ot,Je,Ee),ee(F?l:null)},Vs=()=>j&&lt&&(j[lt]||lt==="Additional Data")?lt==="Additional Data"?[e(vo,{disabled:sn,materialID:xt,selectedMaterialNumber:ss})]:[e(Fa,{disabled:sn,materialID:xt,basicData:Ie,setBasicData:pe,dropDownData:Y,allTabsData:j,basicDataTabDetails:j[lt],activeViewTab:lt,selectedViews:H,handleAccordionClick:Rs,missingValidationPlant:le,selectedMaterialNumber:ss,callGetCountryBasedonSalesOrg:Hn})]:e(Ut,{}),Fn=l=>{const o=l.target.value;Oe({code:o,desc:""}),St(0),ze&&clearTimeout(ze);const m=setTimeout(()=>{is(o,!0)},500);ge(m)};i.useEffect(()=>{O>0&&is(ye==null?void 0:ye.code)},[O]);const ut=(l,o,m)=>{var U;if(l==="Sales Organization"){_n(o,m);const F=(U=he==null?void 0:he.find(Ee=>Ee.id===nn))==null?void 0:U.materialNumber;ms(F,tt?Ns.EXTENDED:Ns.NOT_EXTENDED,o,m)}},_n=(l,o,m="",U="")=>(Ht(F=>({...F,"Distribution Channel":{...F["Distribution Channel"],[o]:!0}})),new Promise((F,Ee)=>{var Je;const xe=Lt=>{if(Lt.statusCode===wt.STATUS_200){const ct=Cs(Lt.body);let Vt=JSON.parse(JSON.stringify(m||we));tt?vt(ln=>({...ln,salesOrg:l,dc:{value:null,options:(ct==null?void 0:ct.length)>0?ct:[]}})):(Vt[o].salesOrg=l,Vt[o].dc.options=ct,on(Vt))}Ht(ct=>({...ct,"Distribution Channel":{...ct["Distribution Channel"],[o]:!1}})),F(Lt)},De=Lt=>{Ht(ct=>({...ct,"Distribution Channel":{...ct["Distribution Channel"],[o]:!1}})),Ee(Lt)};let ot=(Je=he==null?void 0:he.find(Lt=>Lt.id===nn))==null?void 0:Je.materialNumber;ot&&qe(`/${C}/data/${tt?"getDistributionChannelExtended":"getDistributionChannelNotExtended"}?materialNo=${ot}&salesOrg=${l==null?void 0:l.code}`,"get",xe,De)})),vs=(l,o)=>{var U;$(l,o);const m=(U=he==null?void 0:he.find(F=>F.id===nn))==null?void 0:U.materialNumber;Zn(m,tt?Ns.EXTENDED:Ns.NOT_EXTENDED,l,o)},$=(l,o,m="",U)=>{var Je;Ht(Lt=>({...Lt,"Storage Location":{...Lt["Storage Location"],[o]:!0}}));const F=Lt=>{if(Ht(ct=>({...ct,"Storage Location":{...ct["Storage Location"],[o]:!1}})),Lt.statusCode===wt.STATUS_200){const ct=Cs(Lt.body);let Vt=JSON.parse(JSON.stringify(m||we));tt?vt(ln=>({...ln,plant:{value:l,options:[]},sloc:{value:null,options:(ct==null?void 0:ct.length)>0?ct:[]}})):(Vt[o].plant.value=l,Vt[o].sloc.options=ct,on(Vt))}if(U){I(xs({materialID:U==null?void 0:U.id,keyName:"orgData",data:rowOption}));let ct=(he==null?void 0:he.length)||[JSON.parse(JSON.stringify(U))],Vt=ct.findIndex(ln=>ln.id===(U==null?void 0:U.id));ct[Vt].orgData=rowOption,I(Pn(ct))}},Ee=Lt=>{ae(Lt),Ht(ct=>({...ct,"Storage Location":{...ct["Storage Location"],[o]:!1}}))};let xe=(Je=he.find(Lt=>Lt.id===nn))==null?void 0:Je.materialNumber;const De=we[o],ot=De!=null&&De.salesOrg?`&salesOrg=${De.salesOrg.code}`:"";xe&&qe(`/${C}/data/${tt?"getStorageLocationExtended":"getStorageLocationNotExtended"}?materialNo=${xe}&region=${D==null?void 0:D.Region}&plant=${l==null?void 0:l.code}${ot}`,"get",F,Ee)},Ke=(l,o)=>{let m=JSON.parse(JSON.stringify(we));m[o].dc.value=l,on(m)},Qe=l=>{let o=JSON.parse(JSON.stringify(we));o.splice(l,1),on(o)},Ze=(l,o)=>{let m=JSON.parse(JSON.stringify(we));m[o].sloc.value=l,on(m)},G=(l,o)=>{let m=JSON.parse(JSON.stringify(we));m[o].warehouse.value=l,on(m)},et=(l,o)=>{let m=JSON.parse(JSON.stringify(we));m[o].mrpProfile=l,on(m)},He=()=>{let l=JSON.parse(JSON.stringify(we));l.push({id:1,plant:{value:null,options:[]},salesOrg:null,dc:{value:null,options:[]},sloc:{value:null,options:[]},warehouse:{value:null,options:[]},mrpProfile:null}),on(l)},rt=(l,o,m,U)=>{var De,ot,Je,Lt,ct,Vt,ln,Dt,Mn,On,As;const F={material:Tn==null?void 0:Tn.materialNumber,wareHouseNumber:((ot=(De=l==null?void 0:l.warehouse)==null?void 0:De.value)==null?void 0:ot.code)??"",plant:((Lt=(Je=l==null?void 0:l.plant)==null?void 0:Je.value)==null?void 0:Lt.code)??"",salesOrg:((ct=l==null?void 0:l.salesOrg)==null?void 0:ct.code)??"",storageLocation:((ln=(Vt=l==null?void 0:l.sloc)==null?void 0:Vt.value)==null?void 0:ln.code)??"",distributionChannel:((Mn=(Dt=l==null?void 0:l.dc)==null?void 0:Dt.value)==null?void 0:Mn.code)??"",valArea:((As=(On=l==null?void 0:l.plant)==null?void 0:On.value)==null?void 0:As.code)??""},Ee=ws=>{const Gl=Kc(ws==null?void 0:ws.body,o,m,U,Tn),Ql=E===c.EXTEND_WITH_UPLOAD||K===c.EXTEND_WITH_UPLOAD?Qc(z,Gl):Zc(z,Gl);I(Oo({data:Ql})),ds(!Hn)},xe=ws=>{ae(ws)};qe(`/${C}${Ot.DATA.COPY_FROM_MATERIAL_ORG_ELMS_ETEXTEND}`,"post",Ee,xe,F)},Kt=[{id:1,plant:{value:null,options:[]},salesOrg:null,dc:{value:null,options:[]},sloc:{value:null,options:[]},warehouse:{value:null,options:[]},mrpProfile:null}],An=l=>{I(Al(l)),Fe(l)};i.useEffect(()=>{var l,o;(oe==null?void 0:oe.page)!==0&&(E===((l=c)==null?void 0:l.EXTEND_WITH_UPLOAD)||E===((o=c)==null?void 0:o.EXTEND))&&x(),Fe((oe==null?void 0:oe.page)||0)},[oe==null?void 0:oe.page]);const gt=tt?Kt:we;return f("div",{children:[e("div",{style:{padding:"0",width:"100%",margin:"0"},children:f("div",{style:{height:300,width:"100%"},children:[e(Be,{sx:{display:"flex",justifyContent:"flex-end",marginBottom:"10px"},children:e(nt,{variant:"contained",color:"primary",onClick:()=>{Gt(!0)},disabled:T||sn||a&&(n==null?void 0:n.requestStatus)!==cs.DRAFT,children:"+ Add"})}),he&&(he==null?void 0:he.length)>0?e(Ut,{children:e(Ll,{rows:he,columns:gs,pageSize:50,page:Le,rowsPerPageOptions:[50],rowCount:(oe==null?void 0:oe.totalElements)||0,onRowClick:Gn,onCellEditCommit:$t,onPageChange:l=>An(l),disableSelectionOnClick:!0,getRowClassName:l=>l.id===xt?"selected-row":"",style:{border:"1px solid #ccc",borderRadius:"8px",width:"100%"},sx:{"& .selected-row":{backgroundColor:"rgb(234 233 255)"}}})}):e(Ut,{children:e(Ll,{rows:he,columns:gs,pageSize:5,rowsPerPageOptions:[5],page:Le,onRowClick:Gn,onCellEditCommit:$t,onPageChange:l=>An(l),disableSelectionOnClick:!0,getRowClassName:l=>l.id===xt?"selected-row":"",style:{border:"1px solid #ccc",borderRadius:"8px",width:"100%"},sx:{"& .selected-row":{backgroundColor:"rgb(234 233 255)"}}})})]})}),xt&&Rt&&(he==null?void 0:he.length)>0&&Mt.length>0&&j&&((Es=Object.getOwnPropertyNames(j))==null?void 0:Es.length)>0&&f(Be,{sx:{marginTop:"45px"},children:[f(Ao,{sx:{top:0,position:"sticky",zIndex:1e3,backgroundColor:We.background.container,borderBottom:`1px solid ${We.border.light}`,"& .MuiTab-root":{minHeight:"48px",textTransform:"none",fontSize:"14px",fontWeight:600,color:We.black.graphite,"&.Mui-selected":{color:We.primary.main,fontWeight:700},"&:hover":{color:We.primary.main,opacity:.8}},"& .MuiTabs-indicator":{backgroundColor:We.primary.main,height:"3px"}},value:gn,onChange:kn,className:N.customTabs,"aria-label":"material tabs",children:[H&&we.length>0&&(H==null?void 0:H.length)>0?H==null?void 0:H.map((l,o)=>e(Vl,{label:l},o)):e(Ut,{}),e(Vl,{label:"Additional Data",id:"AdditionalKey"},"Additional data")]}),(he==null?void 0:he.length)>0&&e(Be,{sx:{padding:2,marginTop:2},children:Vs()}),e(qo,{activeTab:gn,submitForApprovalDisabled:dt,filteredButtons:Bn,workFlowLevels:_e,showWfLevels:Is,childRequestHeaderData:(Gs=z==null?void 0:z[xt])==null?void 0:Gs.Tochildrequestheaderdata})]}),e("div",{}),e(Kl,{dialogState:p,openReusableDialog:zt,closeReusableDialog:Un,dialogTitle:"Warning",dialogMessage:L,showCancelButton:!1,handleOk:vn,handleDialogConfirm:Un,dialogOkText:"OK",dialogSeverity:"danger"}),Dn&&e(Ws,{fullWidth:!0,maxWidth:!1,open:!0,onClose:zn,sx:{display:"flex",justifyContent:"center"},disableEscapeKeyDown:!0,children:f(Be,{sx:{width:"600px !important"},children:[f(Hs,{sx:{backgroundColor:"#EAE9FF",marginBottom:".5rem"},children:[e(yl,{style:{height:"20px",width:"20px",marginBottom:"-5px"}}),e("span",{children:"Select Views"})]}),e(es,{sx:{paddingBottom:".5rem"},children:f(Be,{display:"flex",alignItems:"center",sx:{flex:1,padding:"22px 0px",gap:"5px"},children:[e(ul,{size:"small",multiple:!0,fullWidth:!0,options:ke,disabled:sn,disableCloseOnSelect:!0,value:H==null?void 0:H.filter(l=>!Yc.includes(l)),onChange:(l,o)=>{fe([re,...o.filter(m=>m!==re)]),$t({id:nn,field:"views",value:o})},getOptionDisabled:l=>l===re,renderOption:(l,o,{selected:m})=>{var Ee;const U=Q.find(xe=>(xe==null?void 0:xe.material)===(Tn==null?void 0:Tn.materialNumber)),F=((Ee=U==null?void 0:U.views)==null?void 0:Ee.includes(o))||!1;return f("li",{...l,children:[e(al,{checked:m||o=="Basic Data",sx:{marginRight:1}}),o," ",F?"(extended)":""]})},renderTags:(l,o)=>l.map((m,U)=>{var ot;const{key:F,...Ee}=o({index:U}),xe=Q.find(Je=>(Je==null?void 0:Je.material)===(Tn==null?void 0:Tn.materialNumber)),De=((ot=xe==null?void 0:xe.views)==null?void 0:ot.includes(m))||!1;return e(Ri,{label:`${m} ${De?"(extended)":""}`,...Ee,disabled:m===re},F)}),renderInput:l=>e(Sn,{...l,label:"Select Views"})}),e(nt,{variant:"contained",disabled:sn,size:"small",onClick:()=>Qn(),children:"Select all"})]})}),f(ts,{children:[e(nt,{onClick:()=>{Zt(!1)},color:"error",variant:"outlined",sx:{height:36,minWidth:"3.5rem",textTransform:"none",borderColor:"#cc3300",fontWeight:500},children:"Cancel"}),e(nt,{onClick:()=>{Zt(!1),$t({id:nn,field:"views",value:H})},variant:"contained",children:"OK"})]})]})}),Bt&&f(Ws,{fullWidth:!0,maxWidth:"xl",open:!0,onClose:zn,disableEscapeKeyDown:!0,sx:{"& .MuiDialog-paper":{padding:2,borderRadius:2}},children:[f(Hs,{sx:{display:"flex",alignItems:"center",gap:1,backgroundColor:"#EAE9FF"},children:[e(yl,{fontSize:"small"}),tt?e("span",{children:"Select org data for copy"}):e("span",{children:"Select org data to be extended"}),e(cn,{onClick:zn,sx:{position:"absolute",right:15},children:e(Dl,{})})]}),e(es,{sx:{padding:0},children:e(mo,{component:Yl,children:f(pl,{children:[e(Wl,{children:f(ll,{children:[!tt&&e(je,{align:"center",children:"S NO."}),e(je,{align:"center",children:"Sales Org"}),e(je,{align:"center",children:"Distribution Channel"}),e(je,{align:"center",children:"Plant"}),e(je,{align:"center",children:"Storage Location"}),(D==null?void 0:D.Region)!==_s.EUR&&e(je,{align:"center",children:"Warehouse"}),e(je,{align:"center",children:"MRP Profile"}),(we==null?void 0:we.length)>1&&!tt&&e(je,{align:"center",children:"Action"})]})}),e(Hl,{children:gt==null?void 0:gt.map((l,o)=>{var m,U,F,Ee,xe,De,ot,Je,Lt,ct,Vt,ln;return f(ll,{sx:{padding:"12px"},children:[!tt&&e(je,{children:e(st,{variant:"body2",children:o+1})}),e(je,{children:e(Qt,{options:Y["Sales Organization"],value:tt?de==null?void 0:de.salesOrg:l==null?void 0:l.salesOrg,onChange:Dt=>ut("Sales Organization",Dt,o),placeholder:"Select Sales Org",disabled:sn,isFieldError:!1,minWidth:165,isLoading:dn["Sales Organization"]})}),e(je,{children:e(Qt,{options:tt?(U=de==null?void 0:de.dc)==null?void 0:U.options:(m=l.dc)==null?void 0:m.options,value:tt?(Ee=de==null?void 0:de.dc)==null?void 0:Ee.value:(F=l.dc)==null?void 0:F.value,onChange:Dt=>tt?vt(Mn=>{var On;return{...Mn,dc:{value:Dt,options:(On=de==null?void 0:de.dc)==null?void 0:On.options}}}):Ke(Dt,o),placeholder:"Select DC",disabled:sn,isFieldError:!1,minWidth:165,isLoading:dn["Distribution Channel"][o]})}),e(je,{children:e(Qt,{options:Y.Plant||[],value:tt?(De=de==null?void 0:de.plant)==null?void 0:De.value:(xe=l.plant)==null?void 0:xe.value,onChange:Dt=>vs(Dt,o),placeholder:"Select Plant",disabled:sn,isFieldError:!1,minWidth:165,isLoading:dn.Plant[o]})}),e(je,{children:e(Qt,{options:tt?(Je=de==null?void 0:de.sloc)==null?void 0:Je.options:(ot=l==null?void 0:l.sloc)==null?void 0:ot.options,value:tt?(ct=de==null?void 0:de.sloc)==null?void 0:ct.value:(Lt=l==null?void 0:l.sloc)==null?void 0:Lt.value,onChange:Dt=>tt?vt(Mn=>{var On;return{...Mn,sloc:{value:Dt,options:(On=de==null?void 0:de.sloc)==null?void 0:On.options}}}):Ze(Dt,o),placeholder:"Select Sloc",disabled:sn,isFieldError:!1,minWidth:165,isLoading:dn["Storage Location"][o]})}),(D==null?void 0:D.Region)!==_s.EUR&&e(je,{children:e(Qt,{options:Y.warehouse||[],value:tt?(ln=de==null?void 0:de.warehouse)==null?void 0:ln.value:(Vt=l==null?void 0:l.warehouse)==null?void 0:Vt.value,onChange:Dt=>tt?vt(Mn=>{var On;return{...Mn,warehouse:{value:Dt,options:(On=de==null?void 0:de.warehouse)==null?void 0:On.options}}}):G(Dt,o),placeholder:"Select Warehouse",disabled:sn,isFieldError:!1,minWidth:165,isLoading:dn.warehouse[o]})}),e(je,{children:e(Qt,{options:Y["Mrp Profile"]||[],value:tt?de==null?void 0:de.mrpProfile:l.mrpProfile,onChange:Dt=>tt?vt(Mn=>({...Mn,mrpProfile:Dt})):et(Dt,o),placeholder:"Select MRP Profile",disabled:sn,isFieldError:!1,minWidth:165,isLoading:dn["Mrp Profile"]})}),we.length>1&&f(je,{align:"right",children:[e(cn,{size:"small",color:"primary",disabled:sn,onClick:()=>{v(!0),Ss({orgRowLength:we.length,copyFor:o})},style:{display:o===0?"none":"inline-flex"},children:e(_i,{})}),e(cn,{style:{display:o===0?"none":"inline-flex"},size:"small",color:"error",onClick:()=>Qe(o),children:e(dl,{})})]})]},o)})})]})})}),f(ts,{sx:{justifyContent:"flex-end",gap:.5},children:[!tt&&e(nt,{onClick:He,disabled:sn,variant:"contained",children:"+ Add"}),e(nt,{onClick:()=>{if(Ln(!1),we[0].plant&&($t({id:nn,field:"orgData",value:we}),!tt)){Ne(we);const o=he==null?void 0:he.map(m=>(m==null?void 0:m.id)===nn?{...m,orgData:we}:m);I(Pn(o))}const l=we.filter(o=>{var m,U;return(U=(m=o.plant)==null?void 0:m.value)==null?void 0:U.code}).map(o=>{var m,U;return(U=(m=o.plant)==null?void 0:m.value)==null?void 0:U.code});l.length>0&&bs(l),tt&&(an(o=>{const m=o.findIndex(U=>U.id===de.id);return m!==-1?o.map((U,F)=>F===m?{...U,...de}:U):[...o,de]}),rt(de,we,D,H))},variant:"contained",children:"Ok"})]})]}),qn&&e(Ho,{open:qn,onClose:()=>v(!1),title:Ii.COPY_ORG_DATA_VALES_HEADING,selectedMaterialPayload:A,lengthOfOrgRow:Rn,materialID:xt,orgRows:we}),$e&&e(Cl,{openSnackBar:Ae,alertMsg:$e,alertType:mn,handleSnackBarClose:()=>Me(!1)}),e(Dd,{openSearchMat:jt,materialOptions:b,handleMatInputChange:Fn,inputState:ye,setOpenSearchMat:Gt,dropDownData:Y,AddCopiedMaterial:hs}),e(Jl,{})]})},vd=({params:n,field:N,isFieldError:ae,isFieldDisable:I,isNewRow:M,keyName:x,handleChangeValue:ue,handleRemoveError:D,charCount:E,setCharCount:Se,isFocused:oe,setIsFocused:z})=>{var W;const[R,_]=i.useState(n.row[N.jsonName]||""),j=n.row.id,u=E[x]===(N==null?void 0:N.maxLength),ie=V=>{const a=V.target.value;_(a),ue(n.row,j,N.jsonName,(a==null?void 0:a.toUpperCase())||"",N.viewName,N.fieldName,x),Se(K=>({...K,[x]:a.length}))};return e(bn,{title:(W=n.row[N.jsonName])==null?void 0:W.toUpperCase(),arrow:!0,placement:"top",children:e(Sn,{fullWidth:!0,placeholder:`ENTER ${N.fieldName.toUpperCase()}`,variant:"outlined",size:"small",value:R,disabled:I||!M&&N.visibility===ys.DISPLAY,inputProps:{maxLength:N.maxLength,style:{textTransform:"uppercase"}},InputProps:{sx:{"& .MuiOutlinedInput-notchedOutline":{borderColor:ae?We.error.dark:void 0},"&.Mui-disabled":{"& input":{WebkitTextFillColor:We.text.primary,color:We.text.primary}}}},onFocus:V=>{V.stopPropagation(),z({...oe,[x]:!0}),Se(a=>({...a,[x]:V.target.value.length})),ae&&D(j,N.fieldName)},onKeyDown:V=>V.key===" "&&V.stopPropagation(),onClick:V=>V.stopPropagation(),onChange:ie,onBlur:()=>z({...oe,[x]:!1}),helperText:oe[x]&&(u?"Max Length Reached":`${E[x]||0}/${N.maxLength}`),FormHelperTextProps:{sx:{color:u?We.error.dark:We.primary.darkPlus,position:"absolute",bottom:"-20px"}},sx:{"& .MuiInputBase-root":{height:"34px"},"& .MuiOutlinedInput-root":{"&.Mui-focused fieldset":{borderColor:u?We.error.dark:""}}}})})},Gd=n=>{var yt,Dn,Zt,nn,Wt,lt,kt,Bt,Ln,xt,rn,Wn,yn,Xt,we,on,Kn,an,de,vt,jn,Jn,Tn,At,mn,P;const{customError:N}=Os(),{getNextDisplayDataForChange:ae}=yi(),I=Z(A=>A.tabsData.changeFieldsDT),M=Z(A=>A.payload.payloadData),x=I==null?void 0:I["Config Data"],ue=Z(A=>A.payload.tablesList),D=Z(A=>A.payload.changeFieldRows),E=Z(A=>A.payload.changeFieldRowsDisplay),Se=Z(A=>A.payload.changeLogData),oe=Z(A=>A.payload.matNoList),z=Z(A=>A.payload.newRowIds),R=Z(A=>A.materialDropDownData.dropDown||{}),_=Z(A=>A.payload.dataLoading),j=Z(A=>A.payload.errorData),u=Z(A=>A.payload.selectedRows),ie=Z(A=>{var Ae;return(Ae=A.request.requestHeader)==null?void 0:Ae.requestId}),W=Z(A=>A.userManagement.userData),V=Z(A=>A.userManagement.taskData),a=Z(A=>A.paginationData),K=Z(A=>A.payload.templateArray),Le=Z(A=>A.payload.requestorPayload),[Fe,ze]=i.useState([]),[ge,Te]=i.useState({}),[ee,re]=i.useState({}),[H,fe]=i.useState(""),[Q,Re]=i.useState("success"),[he,te]=i.useState(!1),[Mt,T]=i.useState(""),J=Z(A=>A.tabsData.dataLoading),[p,Ce]=i.useState({data:{},isVisible:!1}),L=as(),q=rl(),b=new URLSearchParams(q.search),se=b.get("reqBench"),O=b.get("RequestId"),{t:St}=Nl();let ye=q.state;i.useEffect(()=>{D&&(Fe==null?void 0:Fe.length)===0&&ze(JSON.parse(JSON.stringify(D)))},[D]);const[Oe,ht]=i.useState(0),[It,bt]=i.useState(10),dt=(A,Ae)=>{ht(isNaN(Ae)?0:Ae)},tn=A=>{const Ae=A.target.value;bt(Ae),ht(0)},Ct=()=>{te(!0)},mt=()=>{te(!1)},gn=()=>{const A=(D==null?void 0:D.length)>0?Object.keys(D[0]):[],Ae=A==null?void 0:A.reduce((Ve,$e)=>(Ve[$e]=$e==="id"?Sl():$e==="slNo"?1:"",Ve),{}),Me=[Ae,...D].map((Ve,$e)=>({...Ve,slNo:$e+1})),le=[Ae,...E[a==null?void 0:a.page]||[]].map((Ve,$e)=>({...Ve,slNo:$e+1}));L(wr([Ae==null?void 0:Ae.id,...z])),L(Bl(Me)),L(Fl({...E,[a==null?void 0:a.page]:le})),L(Io([Ae==null?void 0:Ae.id,...u])),L(Rl(!0)),n==null||n.setCompleted([!0,!1])},Jt=Fr(ea,M==null?void 0:M.TemplateName),Rt=Fr(ta,Jt),Tt=(Rt==null?void 0:Rt.length)>1,Ie=(A,Ae)=>{const Me=Fe==null?void 0:Fe.find(le=>{const Ve=le.Material===(A==null?void 0:A.Material)&&(le==null?void 0:le[Rt[0]])===(A==null?void 0:A[Rt[0]]);return Tt?Ve&&(le==null?void 0:le[Rt[1]])===(A==null?void 0:A[Rt[1]]):Ve});if(Me)return Me[Ae]},pe=(A,Ae,Me,le)=>{var $e;const Ve=($e=Fe==null?void 0:Fe[Me])==null?void 0:$e.find(Ft=>{let _e=Ft.Material===(A==null?void 0:A.Material);return(le==null?void 0:le.length)>0&&(_e=_e&&(Ft==null?void 0:Ft[le[0]])===(A==null?void 0:A[le[0]]),(le==null?void 0:le.length)>1&&(_e=_e&&(Ft==null?void 0:Ft[le[1]])===(A==null?void 0:A[le[1]]))),_e});return Ve?Ve[Ae]:"-"},Y=A=>{L(na(A))},{handleObjectChangeFieldRows:ne}=Va(D,E,a,Jt,W,ie,K,Y,pe,n==null?void 0:n.RequestId),Ge=(A,Ae,Me,le,Ve,$e)=>{var Ft,_e,fn,be;if(Array.isArray(D)){if(Me==="AltUnit"||Me==="Langu"){const _t=oa(A,E==null?void 0:E[a==null?void 0:a.page],oe,le,M==null?void 0:M.TemplateName);if(_t==="matError"){Re("error"),T(St((Ft=Zs)==null?void 0:Ft.MATL_ERROR_MSG)),Ct();return}else if(_t==="altUnitError"){Re("error"),T(St((_e=Zs)==null?void 0:_e.ALTUNIT_ERROR_MSG)),Ct();return}else if(_t==="languError"){Re("error"),T(St((fn=Zs)==null?void 0:fn.LANG_ERROR_MSG)),Ct();return}}const jt=D==null?void 0:D.map(_t=>{var dn,Ht;return(_t==null?void 0:_t.id)===Ae?{..._t,[Me]:le,...Me==="Material"?{...(M==null?void 0:M.TemplateName)===((dn=Pt)==null?void 0:dn.UPD_DESC)?{Langu:""}:{},...(M==null?void 0:M.TemplateName)===((Ht=Pt)==null?void 0:Ht.LOGISTIC)?{AltUnit:""}:{}}:{}}:_t});L(Bl(jt));const Gt=(be=E==null?void 0:E[a==null?void 0:a.page])==null?void 0:be.map(_t=>{var dn,Ht;return(_t==null?void 0:_t.id)===Ae?{..._t,[Me]:le,...Me==="Material"?{...(M==null?void 0:M.TemplateName)===((dn=Pt)==null?void 0:dn.UPD_DESC)?{Langu:""}:{},...(M==null?void 0:M.TemplateName)===((Ht=Pt)==null?void 0:Ht.LOGISTIC)?{AltUnit:""}:{}}:{}}:_t});L(Fl({...E,[a==null?void 0:a.page]:Gt}));const tt=ca(),Xn=_t=>_t!=null&&_t.toString().startsWith("/Date(")&&(_t!=null&&_t.toString().endsWith(")/"))?ha(_t):_t;let Hn={ObjectNo:`${A==null?void 0:A.Material}$$${A==null?void 0:A[Rt[0]]}${Tt?`$$${A==null?void 0:A[Rt[1]]}`:""}`,ChangedBy:W.emailId,ChangedOn:tt.sapFormat,FieldName:$e??Me,PreviousValue:Ie(A,Me)??"-",SAPValue:Ie(A,Me)??"-",CurrentValue:Xn(le)??""};Y(Hn);let ds={RequestId:ie||(n==null?void 0:n.RequestId),changeLogId:(A==null?void 0:A.ChangeLogId)??null,[Jt]:[...K,Hn]};const ss=aa(ds,Jt);L(da(ss))}else typeof D=="object"&&D[Ve]&&ne(Ve,Ae,Me,le,$e)},Xe=(A,Ae)=>{const Me={};Object.keys(j).forEach(le=>{var $e;const Ve=j[le];if(Ve.id===A){const Ft=($e=Ve==null?void 0:Ve.missingFields)==null?void 0:$e.filter(_e=>_e!==Ae);Ft.length>0&&(Me[le]={...Ve,missingFields:Ft})}else Me[le]={...Ve}}),L(Ei(Me))},Et=()=>{var Me,le,Ve,$e;const A=p==null?void 0:p.data,Ae=(Me=A==null?void 0:A.row)==null?void 0:Me.id;if(Array.isArray(D)){const _e=D.filter(Gt=>(Gt==null?void 0:Gt.id)!==Ae).map((Gt,tt)=>({...Gt,slNo:tt+1}));L(Bl(_e));const fn={...E,[a==null?void 0:a.page]:(Ve=(le=E[a==null?void 0:a.page])==null?void 0:le.filter(Gt=>(Gt==null?void 0:Gt.id)!==Ae))==null?void 0:Ve.map((Gt,tt)=>({...Gt,slNo:tt+1}))};L(Fl(fn));const be=z==null?void 0:z.filter(Gt=>Gt!==Ae);L(wr(be));const jt=D.find(Gt=>Gt.id===Ae);if(jt){const Gt=`${jt.Material}$$${jt[Rt[0]]}${Tt?`$$${jt[Rt[1]]}`:""}`,tt=JSON.parse(JSON.stringify(Se));if(($e=tt[jt.Material])!=null&&$e[Jt]){const Xn=tt[jt.Material][Jt].filter(Hn=>Hn.ObjectNo!==Gt&&Hn.ObjectNo!==`${jt.Material}$$`);Xn.length===0?(delete tt[jt.Material][Jt],Object.keys(tt[jt.Material]).length===0&&(delete tt[jt.Material],delete tt[""])):tt[jt.Material][Jt]=Xn}L(ua(tt))}}Ce({...p,isVisible:!1})},y=(A,Ae)=>{var $e,Ft,_e,fn,be,jt,Gt,tt,Xn,Hn,ds,ss,_t,dn,Ht,qn;const Me=[{headerName:"Sl. No.",field:"slNo",align:"center",flex:(M==null?void 0:M.TemplateName)===(($e=Pt)==null?void 0:$e.LOGISTIC)||(M==null?void 0:M.TemplateName)===((Ft=Pt)==null?void 0:Ft.MRP)&&A==="Plant Data"?void 0:.1,width:(M==null?void 0:M.TemplateName)===((_e=Pt)==null?void 0:_e.LOGISTIC)||(M==null?void 0:M.TemplateName)===((fn=Pt)==null?void 0:fn.MRP)&&A==="Plant Data"?1:void 0},...Ae.map(v=>{var Rn,Ss,en,us,B,Is,Bn;return{headerName:f("span",{children:[v.fieldName,v.visibility===((Rn=ys)==null?void 0:Rn.MANDATORY)&&e("span",{style:{color:(en=(Ss=We)==null?void 0:Ss.error)==null?void 0:en.dark,marginLeft:4},children:"*"})]}),field:v.jsonName,flex:(M==null?void 0:M.TemplateName)===((us=Pt)==null?void 0:us.LOGISTIC)||(M==null?void 0:M.TemplateName)===((B=Pt)==null?void 0:B.MRP)&&(v==null?void 0:v.viewName)==="Plant Data"?void 0:1,width:(M==null?void 0:M.TemplateName)===((Is=Pt)==null?void 0:Is.LOGISTIC)||(M==null?void 0:M.TemplateName)===((Bn=Pt)==null?void 0:Bn.MRP)&&(v==null?void 0:v.viewName)==="Plant Data"?200:void 0,renderCell:Ne=>{var En,Ye,it,pt,$t,Gn,zt,Un,zn,Qn,hs,gs,Cn,ms,Zn,kn,fs,bs,Rs,Vs,Fn,ut,_n,vs,$,Ke,Qe,Ze;const Nn=(En=Object==null?void 0:Object.values(j))==null?void 0:En.find(G=>{var et;return(G==null?void 0:G.id)===((et=Ne==null?void 0:Ne.row)==null?void 0:et.id)}),ls=`${(Ye=Ne==null?void 0:Ne.row)==null?void 0:Ye.id}-${v==null?void 0:v.jsonName}`,vn=(it=Nn==null?void 0:Nn.missingFields)==null?void 0:it.includes(v==null?void 0:v.fieldName),is=z==null?void 0:z.includes((pt=Ne==null?void 0:Ne.row)==null?void 0:pt.id),sn=!!(se&&!(($t=Ls)!=null&&$t.includes(ye==null?void 0:ye.reqStatus)));if(v.fieldType===oi.INPUT)return e(vd,{params:Ne,field:v,isFieldError:vn,isFieldDisable:sn,isNewRow:is,keyName:ls,handleChangeValue:Ge,handleRemoveError:Xe,charCount:ee,setCharCount:re,isFocused:ge,setIsFocused:Te});if(v.fieldType===oi.DROPDOWN){const G=z==null?void 0:z.includes((Gn=Ne==null?void 0:Ne.row)==null?void 0:Gn.id),et=(v==null?void 0:v.jsonName)!=="Unittype1"&&(v==null?void 0:v.jsonName)!=="Spproctype"&&(v==null?void 0:v.jsonName)!=="MrpCtrler"?(zt=R==null?void 0:R[v==null?void 0:v.jsonName])==null?void 0:zt.find(He=>{var rt;return He.code===((rt=Ne==null?void 0:Ne.row)==null?void 0:rt[v==null?void 0:v.jsonName])}):(v==null?void 0:v.jsonName)==="Spproctype"||(v==null?void 0:v.jsonName)==="MrpCtrler"?(Qn=(zn=R==null?void 0:R[v==null?void 0:v.jsonName])==null?void 0:zn[(Un=Ne==null?void 0:Ne.row)==null?void 0:Un.Plant])==null?void 0:Qn.find(He=>{var rt;return He.code===((rt=Ne==null?void 0:Ne.row)==null?void 0:rt[v==null?void 0:v.jsonName])}):(Cn=(gs=R==null?void 0:R[v==null?void 0:v.jsonName])==null?void 0:gs[(hs=Ne==null?void 0:Ne.row)==null?void 0:hs.WhseNo])==null?void 0:Cn.find(He=>{var rt;return He.code===((rt=Ne==null?void 0:Ne.row)==null?void 0:rt[v==null?void 0:v.jsonName])});return e(Qt,{options:(v==null?void 0:v.jsonName)==="Unittype1"?(Zn=R==null?void 0:R.Unittype1)==null?void 0:Zn[(ms=Ne==null?void 0:Ne.row)==null?void 0:ms.WhseNo]:(v==null?void 0:v.jsonName)==="Spproctype"?(fs=R==null?void 0:R.Spproctype)==null?void 0:fs[(kn=Ne==null?void 0:Ne.row)==null?void 0:kn.Plant]:(v==null?void 0:v.jsonName)==="MrpCtrler"?(Rs=R==null?void 0:R.MrpCtrler)==null?void 0:Rs[(bs=Ne==null?void 0:Ne.row)==null?void 0:bs.Plant]:R!=null&&R[v==null?void 0:v.jsonName]?R==null?void 0:R[v==null?void 0:v.jsonName]:[],value:et||((Vs=Ne==null?void 0:Ne.row)!=null&&Vs[v==null?void 0:v.jsonName]?{code:(Fn=Ne==null?void 0:Ne.row)==null?void 0:Fn[v==null?void 0:v.jsonName],desc:""}:null),onChange:He=>{Ge(Ne.row,Ne.row.id,v==null?void 0:v.jsonName,He==null?void 0:He.code,A,v==null?void 0:v.fieldName),vn&&Xe(Ne.row.id,v==null?void 0:v.fieldName)},listWidth:150,placeholder:`Select ${v.fieldName}`,disabled:sn?!0:G?!1:(v==null?void 0:v.visibility)===((ut=ys)==null?void 0:ut.DISPLAY),isFieldError:vn})}else if(v.fieldType===oi.DATE_FIELD){const G=z==null?void 0:z.includes((_n=Ne==null?void 0:Ne.row)==null?void 0:_n.id),et=(vs=Ne==null?void 0:Ne.row)!=null&&vs[v==null?void 0:v.jsonName]?(()=>{var rt;const He=(rt=Ne==null?void 0:Ne.row)==null?void 0:rt[v==null?void 0:v.jsonName];if(He.startsWith("/Date(")&&He.endsWith(")/")){const Kt=parseInt(He.slice(6,-2));return new Date(Kt)}return typeof He=="string"&&He.match(/^\d{4}-\d{2}-\d{2}/)?new Date(He):sa(He,["YYYY-MM-DD HH:mm:ss.S","DD MMM YYYY HH:mm:ss UTC"]).toDate()})():null;return e(bn,{title:($=Ne==null?void 0:Ne.row)==null?void 0:$[v==null?void 0:v.jsonName],arrow:!0,placement:"top",children:e(la,{dateAdapter:ia,children:e(dd,{disabled:sn?!0:G?!1:(v==null?void 0:v.visibility)===((Ke=ys)==null?void 0:Ke.DISPLAY),slotProps:{textField:{size:"small",fullWidth:!0,InputProps:{sx:{"& .MuiOutlinedInput-notchedOutline":{borderColor:vn?(Ze=(Qe=We)==null?void 0:Qe.error)==null?void 0:Ze.dark:void 0}}}}},value:et,onChange:He=>{if(He){const rt=`/Date(${Date.parse(He)})/`;Ge(Ne.row,Ne.row.id,v==null?void 0:v.jsonName,rt,A,v==null?void 0:v.fieldName)}else Ge(Ne.row,Ne.row.id,v==null?void 0:v.jsonName,null,A,v==null?void 0:v.fieldName);vn&&Xe(Ne.row.id,v==null?void 0:v.fieldName)},onError:He=>{He&&!vn&&N(el.DATE_VALIDATION_ERROR,He)},maxDate:new Date(9999,11,31)})})})}else return Ne.value||"-"}}}),{...(((M==null?void 0:M.TemplateName)===((be=Pt)==null?void 0:be.LOGISTIC)||(M==null?void 0:M.TemplateName)===((jt=Pt)==null?void 0:jt.UPD_DESC))&&!O||((M==null?void 0:M.TemplateName)===((Gt=Pt)==null?void 0:Gt.LOGISTIC)||(M==null?void 0:M.TemplateName)===((tt=Pt)==null?void 0:tt.UPD_DESC))&&O&&((V==null?void 0:V.taskDesc)===((Xn=Vr)==null?void 0:Xn.REQUESTOR)||(ye==null?void 0:ye.reqStatus)===((Hn=cs)==null?void 0:Hn.DRAFT)))&&{field:"action",headerName:"Action",flex:(M==null?void 0:M.TemplateName)===((ds=Pt)==null?void 0:ds.LOGISTIC)||(M==null?void 0:M.TemplateName)===((ss=Pt)==null?void 0:ss.MRP)&&(field==null?void 0:field.viewName)==="Plant Data"?void 0:1,width:(M==null?void 0:M.TemplateName)===((_t=Pt)==null?void 0:_t.LOGISTIC)||(M==null?void 0:M.TemplateName)===((dn=Pt)==null?void 0:dn.MRP)&&(field==null?void 0:field.viewName)==="Plant Data"?200:void 0,align:"center",headerAlign:"center",renderCell:v=>{var Rn;return e(Fs,{direction:"row",alignItems:"center",sx:{marginLeft:"0.5rem",magrinRight:"0.5rem"},spacing:.5,children:e(bn,{title:"Delete Row",children:e(cn,{disabled:!(z!=null&&z.includes((Rn=v==null?void 0:v.row)==null?void 0:Rn.id)),onClick:()=>{Ce({data:v,isVisible:!0})},color:"error",children:e(dl,{})})})})}}}],le=Array.isArray(D)?(E==null?void 0:E[a==null?void 0:a.page])||[]:((Ht=E==null?void 0:E[a==null?void 0:a.page])==null?void 0:Ht[A])||[],Ve=Array.isArray(u)?u:u[A];return f("div",{style:{height:400,width:"100%"},children:[e(ra,{paginationLoading:_,rows:le,rowCount:(le==null?void 0:le.length)??0,columns:Me,getRowIdValue:"id",rowHeight:70,isLoading:_,tempheight:"calc(100vh - 380px)",page:Oe,pageSize:It,selectionModel:Ve,onPageChange:dt,onPageSizeChange:tn,onCellEditCommit:Ge,checkboxSelection:!(se&&!((qn=Ls)!=null&&qn.includes(ye==null?void 0:ye.reqStatus))),disableSelectionOnClick:!0,showCustomNavigation:!0,hideFooter:!0}),(p==null?void 0:p.isVisible)&&f(Xl,{isOpen:p==null?void 0:p.isVisible,titleIcon:e(dl,{size:"small",color:"error",sx:{fontSize:"20px"}}),Title:St("Delete Row")+"!",handleClose:()=>Ce({...p,isVisible:!1}),children:[e(es,{sx:{mt:2},children:St(Zs.DELETE_MESSAGE)}),f(ts,{children:[e(nt,{variant:"outlined",size:"small",sx:{...Di},onClick:()=>Ce({...p,isVisible:!1}),children:St(wl.CANCEL)}),e(nt,{variant:"contained",size:"small",sx:{...zl},onClick:Et,children:St(wl.DELETE)})]})]})]})},ke=x&&Object.keys(x);if(i.useEffect(()=>{var A,Ae,Me;(a==null?void 0:a.page)+1&&((M==null?void 0:M.RequestType)===((A=c)==null?void 0:A.CHANGE)||(M==null?void 0:M.RequestType)===((Ae=c)==null?void 0:Ae.CHANGE_WITH_UPLOAD))&&(O&&(!Le||((Me=Object==null?void 0:Object.keys(Le))==null?void 0:Me.length)===0)?(ae("display"),ht(0)):(ae("requestor"),ht(0)),H==="prev"?L(Ni(!1)):H==="next"&&(a==null?void 0:a.currentElements)>=(a==null?void 0:a.totalElements)&&L(Ni(!0)))},[a==null?void 0:a.page]),(ke==null?void 0:ke.length)===1){const A=ke[0],Ae=x[A];return f(Fs,{children:[f(Fs,{direction:"row",justifyContent:"space-between",mb:1.5,children:[((M==null?void 0:M.TemplateName)===((yt=Pt)==null?void 0:yt.LOGISTIC)||(M==null?void 0:M.TemplateName)===((Dn=Pt)==null?void 0:Dn.UPD_DESC))&&!O||((M==null?void 0:M.TemplateName)===((Zt=Pt)==null?void 0:Zt.LOGISTIC)||(M==null?void 0:M.TemplateName)===((nn=Pt)==null?void 0:nn.UPD_DESC))&&O&&((V==null?void 0:V.taskDesc)===((Wt=Vr)==null?void 0:Wt.REQUESTOR)||(ye==null?void 0:ye.reqStatus)===((lt=cs)==null?void 0:lt.DRAFT))?e(nt,{variant:"contained",color:"primary",onClick:gn,startIcon:e(ko,{}),sx:{borderRadius:"10px",boxShadow:"0px 4px 8px rgba(0, 0, 0, 0.15)"},children:"Add Row"}):e(Be,{sx:{width:0,height:0}}),f(Be,{sx:{display:"flex",justifyContent:"center",alignItems:"center",backgroundImage:"linear-gradient(180deg,rgb(242, 241, 255) 0%,rgb(255, 255, 255) 100%)",border:"1px solid #E0E0E0",padding:"5px",borderRadius:"10px",mt:-1,boxShadow:"0px 2px 10px rgba(0, 0, 0, 0.08)"},children:[e(bn,{title:"Previous",placement:"top",arrow:!0,children:e(cn,{disabled:(a==null?void 0:a.page)===0||!1,onClick:()=>{fe("prev"),L(Al((a==null?void 0:a.page)-1))},children:e(Ci,{sx:{color:(a==null?void 0:a.page)===0?(Bt=(kt=We)==null?void 0:kt.secondary)==null?void 0:Bt.grey:(xt=(Ln=We)==null?void 0:Ln.primary)==null?void 0:xt.main,fontSize:"1.5rem",marginRight:"2px"}})})}),f("span",{style:{marginRight:"2px"},children:[e("strong",{style:{color:(Wn=(rn=We)==null?void 0:rn.primary)==null?void 0:Wn.main},children:"Materials :"})," ",f("strong",{children:[(a==null?void 0:a.page)*(a==null?void 0:a.size)+1," -"," ",a==null?void 0:a.currentElements]})," ",e("span",{children:"of"})," ",e("strong",{children:a==null?void 0:a.totalElements})]}),e(bn,{title:"Next",placement:"top",arrow:!0,children:e(cn,{disabled:(a==null?void 0:a.currentElements)>=(a==null?void 0:a.totalElements)||!1,onClick:()=>{fe("next"),L(Al((a==null?void 0:a.page)+1))},children:e(zr,{sx:{color:(a==null?void 0:a.currentElements)>=(a==null?void 0:a.totalElements)?(Xt=(yn=We)==null?void 0:yn.secondary)==null?void 0:Xt.grey:(on=(we=We)==null?void 0:we.primary)==null?void 0:on.main,fontSize:"1.5rem"}})})})]})]}),e("div",{children:y(A,Ae)}),e(Cl,{openSnackBar:he,alertMsg:Mt,alertType:Q,handleSnackBarClose:mt})]})}return f(Ut,{children:[e(hl,{blurLoading:J}),!J&&e(Ut,{children:x?f("div",{children:[f(Be,{sx:{display:"flex",justifyContent:"center",alignItems:"center",backgroundImage:"linear-gradient(180deg, rgb(242, 241, 255) 0%, rgb(255, 255, 255) 100%)",border:"1px solid #E0E0E0",borderRadius:"10px",padding:"5px",width:"fit-content",marginLeft:"auto",mt:-1,mb:2,boxShadow:"0px 2px 10px rgba(0, 0, 0, 0.08)"},children:[e(bn,{title:"Previous",placement:"top",arrow:!0,children:e(cn,{disabled:(a==null?void 0:a.page)===0||!1,onClick:()=>{L(Al((a==null?void 0:a.page)-1))},children:e(Ci,{sx:{color:(a==null?void 0:a.page)===0?(an=(Kn=We)==null?void 0:Kn.secondary)==null?void 0:an.grey:(vt=(de=We)==null?void 0:de.primary)==null?void 0:vt.main,fontSize:"1.5rem",marginRight:"2px"}})})}),f("span",{style:{marginRight:"2px"},children:[e("strong",{style:{color:(Jn=(jn=We)==null?void 0:jn.primary)==null?void 0:Jn.main},children:"Materials :"})," ",f("strong",{children:[(a==null?void 0:a.page)*(a==null?void 0:a.size)+1," -"," ",a==null?void 0:a.currentElements]})," ",e("span",{children:"of"})," ",e("strong",{children:a==null?void 0:a.totalElements})]}),e(bn,{title:"Next",placement:"top",arrow:!0,children:e(cn,{disabled:(a==null?void 0:a.currentElements)>=(a==null?void 0:a.totalElements)||!1,onClick:()=>{L(Al((a==null?void 0:a.page)+1))},children:e(zr,{sx:{color:(a==null?void 0:a.currentElements)>=(a==null?void 0:a.totalElements)?(At=(Tn=We)==null?void 0:Tn.secondary)==null?void 0:At.grey:(P=(mn=We)==null?void 0:mn.primary)==null?void 0:P.main,fontSize:"1.5rem"}})})})]}),ke==null?void 0:ke.map(A=>ue!=null&&ue.includes(A)?f(oo,{sx:{marginBottom:"20px",boxShadow:3},children:[e(ao,{expandIcon:e(co,{}),"aria-controls":`${A}-content`,id:`${A}-header`,sx:{backgroundImage:"linear-gradient(180deg,rgb(242, 241, 255) 0%,rgb(255, 255, 255) 100%)",padding:"8px 16px","&:hover":{backgroundImage:"linear-gradient(90deg,rgb(242, 242, 255) 0%,rgb(239, 232, 255) 100%)"}},children:e(st,{variant:"h6",sx:{fontWeight:"bold"},children:A})}),e(uo,{sx:{height:"calc(100vh - 300px)"},children:y(A,x[A])})]},A):null)]}):e(st,{children:"No data available"})})]})},Ud=()=>{const{customError:n}=Os(),[N,ae]=i.useState([]),[I,M]=i.useState(!1),x=Z(_=>_.userManagement.taskData),[ue,D]=i.useState([]),E=Z(_=>_.applicationConfig),Se=as();let oe={handleSubmitForApproval:6,handleSendBack:1,handleReject:3,handleValidate:5,handleSAPSyndication:8,handleIdGenerator:4,handleSubmitForReview:7,handleCorrection:2};const z=vl(Ys.CURRENT_TASK,!0,{});return i.useEffect(()=>{const _=(x==null?void 0:x.taskDesc)||(z==null?void 0:z.taskDesc),j=N==null?void 0:N.filter(ie=>ie.MDG_MAT_DYN_BTN_TASK_NAME===_),u=j==null?void 0:j.sort((ie,W)=>{const V=oe[ie.MDG_MAT_DYN_BTN_ACTION_TYPE],a=oe[W.MDG_MAT_DYN_BTN_ACTION_TYPE];return V-a});D(u),Se(ga(u)),(u.find(ie=>ie.MDG_MAT_DYN_BTN_BUTTON_NAME===wn.SEND_BACK)||u.find(ie=>ie.MDG_MAT_DYN_BTN_BUTTON_NAME===wn.CORRECTION))&&M(!0)},[N]),{getButtonsDisplay:()=>{let _={decisionTableId:null,decisionTableName:"MDG_MAT_DYN_BUTTON_CONFIG",version:"v4",rulePolicy:null,validityDate:null,conditions:[{"MDG_CONDITIONS.MDG_MAT_DYN_BTN_MODULE_NAME":"Article","MDG_CONDITIONS.MDG_MAT_DYN_BTN_REQUEST_TYPE":(x==null?void 0:x.ATTRIBUTE_2)||(z==null?void 0:z.ATTRIBUTE_2)}],systemFilters:null,systemOrders:null,filterString:null};const j=ie=>{var W,V;if(ie.statusCode===200){let a=(V=(W=ie==null?void 0:ie.data)==null?void 0:W.result[0])==null?void 0:V.MDG_MAT_DYN_BUTTON_CONFIG;ae(a)}},u=ie=>{n(ie)};E.environment==="localhost"?qe(`/${Bs}${Ot.INVOKE_RULES.LOCAL}`,"post",j,u,_):qe(`/${Bs}${Ot.INVOKE_RULES.PROD}`,"post",j,u,_)},showWfLevels:I}},kd=n=>{const[N,ae]=i.useState(!0),I=Z(Te=>Te.materialDropDownData.dropDown),M=Z(Te=>Te.payload),x=Z(Te=>Te.payload.payloadData),ue=x==null?void 0:x.RequestType,[D,E]=i.useState(!1),[Se,oe]=i.useState(!1),z=Z(Te=>Te.userManagement.taskData),R=Z(Te=>Te.payload.filteredButtons),_=rl(),j=new URLSearchParams(_.search),u=j.get("RequestType"),ie=j.get("RequestId"),W=Z(Te=>Te.payload.changeFieldRows),V=Z(Te=>Te.payload.dynamicKeyValues),a=jl(),{getButtonsDisplay:K,showWfLevels:Le}=Ud(),{wfLevels:Fe}=wa({initialPayloadRequestType:ue,initialPayload:x,dynamicData:V,taskData:z,singlePayloadData:M}),ze=xi(R,[qs.HANDLE_SUBMIT_FOR_APPROVAL,qs.HANDLE_SAP_SYNDICATION,qs.HANDLE_SUBMIT_FOR_REVIEW]);i.useEffect(()=>{(z!=null&&z.ATTRIBUTE_1||u)&&K()},[z]),i.useEffect(()=>{((W==null?void 0:W.length)!==0&&(W==null?void 0:W.length)!==void 0||!ge())&&(oe(!0),E(!0))},[W]);const ge=()=>{var Te;return(Te=Object==null?void 0:Object.values(W))==null?void 0:Te.every(ee=>(Array==null?void 0:Array.isArray(ee))&&(ee==null?void 0:ee.length)===0)};return i.useEffect(()=>{n.downloadClicked&&ae(!0)},[n.downloadClicked]),f("div",{children:[((x==null?void 0:x.TemplateName)&&(W&&(W==null?void 0:W.length)===0||ge())||n.downloadClicked)&&e(ja,{open:N,onClose:()=>{var Te;ae(!1),n==null||n.setDownloadClicked(!1),ie||a((Te=ns)==null?void 0:Te.REQUEST_BENCH)},parameters:Ta[x==null?void 0:x.TemplateName],templateName:x==null?void 0:x.TemplateName,setShowTable:E,allDropDownData:I,setDownloadClicked:n==null?void 0:n.setDownloadClicked}),(D||Se)&&!(n!=null&&n.downloadClicked)&&f(Ut,{children:[e(Gd,{setCompleted:n==null?void 0:n.setCompleted,RequestId:ie}),e(qo,{filteredButtons:ze,setCompleted:n==null?void 0:n.setCompleted,showWfLevels:Le,workFlowLevels:Fe})]}),e(Jl,{})]})},$d=()=>{const{fetchDataAndDispatch:n}=$a();return{fetchAllDropdownMasterData:()=>{[{url:`/${C}/data/getEanCat`,keyName:"CategoryOfInternationalArticleNumberEAN"},{url:`/${C}/data/getGenItemCatGroup`,keyName:"ItemCat"},{url:`/${C}/data/getTaxInd`,keyName:"Json121"},{url:`/${C}/data/getMatlGrp4`,keyName:"MatlGrp4"},{url:`/${C}/data/getMatlGrp5`,keyName:"MatlGrp5"},{url:`/${C}/data/getMatfrgtgrp`,keyName:"Matfrgtgrp"},{url:`/${C}/data/getBaseUom`,keyName:"CompUom"},{url:`/${C}/data/getBaseUom`,keyName:"CompUom"},{url:`/${C}/data/getBaseUom`,keyName:"DelyUom"},{url:`/${C}/data/getBaseUom`,keyName:"AltUnit"},{url:`/${C}/data/getBaseUom`,keyName:"UnitDim"},{url:`/${C}/data/getBaseUom`,keyName:"CommCoUn"},{url:`/${C}/data/getBaseUom`,keyName:"LeqUnit1"},{url:`/${C}/data/getBaseUom`,keyName:"Json103"},{url:`/${C}/data/getBaseUom`,keyName:"Json75"},{url:`/${C}/data/getBaseUom`,keyName:"Json156"},{url:`/${C}/data/getUnitType`,keyName:"Unittype1"},{url:`/${C}/data/getSproctype`,keyName:"Sproctype"},{url:`/${C}/data/getVarOrdUn`,keyName:"VarOrdUn"},{url:`/${C}/data/getMatlGroup`,keyName:"MatlGroup"},{url:`/${C}/data/getMatlGroup`,keyName:"MatlGrp"},{url:`/${C}/data/getUnitOfWt`,keyName:"UnitOfWt"},{url:`/${C}/data/getVolumeUnit`,keyName:"Volumeunit"},{url:`/${C}/data/getHazMatProf`,keyName:"Hazmatprof"},{url:`/${C}/data/getItemCat`,keyName:"Json213"},{url:`/${C}/data/getSalesUnit`,keyName:"Json201"},{url:`/${C}/data/getPurValkey`,keyName:"PurValkey"},{url:`/${C}/data/getBasicMatl`,keyName:"BasicMatl"},{url:`/${C}/data/getXDistChainStatus`,keyName:"SalStatus"},{url:`/${C}/data/getEanCat`,keyName:"EanCat"},{url:`/${C}/data/getLangu`,keyName:"Langu"},{url:`/${C}/data/getSalesOrg`,keyName:"SalesOrg"},{url:`/${C}/data/getMatlStats`,keyName:"MatlStats"},{url:`/${C}/data/getMatPrGrp`,keyName:"MatPrGrp"},{url:`/${C}/data/getAcctAssgt`,keyName:"AcctAssgt"},{url:`/${C}/data/getDepcountry`,keyName:"Depcountry"},{url:`/${C}/data/getXPlant`,keyName:"PurStatus"},{url:`/${C}/data/getPlantSpMatlStatus`,keyName:"PurStstus"},{url:`/${C}/data/getPlantSpMatlStatus`,keyName:"PlantSpMatlStatus"},{url:`/${C}/data/getXPlant`,keyName:"CrossPlantMaterialStatus"},{url:`/${C}/data/getExtMatlGrp`,keyName:"Extmatgrp"},{url:`/${C}/data/getLoadingGroup`,keyName:"Loadinggrp"},{url:`/${C}/data/getAvailCheck`,keyName:"Availcheck"},{url:`/${C}/data/getCountryOfOrigin`,keyName:"Countryori"},{url:`/${C}/data/getRebateGrp`,keyName:"RebateGrp"},{url:`/${C}/data/getMRPType`,keyName:"MrpType"},{url:`/${C}/data/getMRPType`,keyName:"Json151"},{url:`/${C}/data/getLotSizingProcedure`,keyName:"Lotsizekey"},{url:`/${C}/data/getProcurementType`,keyName:"ProcType"},{url:`/${C}/data/getBackflush`,keyName:"Backflush"},{url:`/${C}/data/getPeriodInd`,keyName:"PeriodInd"},{url:`/${C}/data/getPlanningStrategyGroup`,keyName:"PlanStrgp"},{url:`/${C}/data/getConsumptionMode`,keyName:"Consummode"},{url:`/${C}/data/getConsumptionPeriodBkwd`,keyName:"BwdCons"},{url:`/${C}/data/getConsumptionPeriodFwd`,keyName:"FwdCons"},{url:`/${C}/data/getIndividualColl`,keyName:"DepReqId"},{url:`/${C}/data/getSaftyTimeIndicator`,keyName:"SaftyTId"},{url:`/${C}/data/getMixedMRP`,keyName:"MixedMrp"},{url:`/${C}/data/getRequirementGroup`,keyName:"GrpReqmts"},{url:`/${C}/data/getPriceUnit`,keyName:"PriceUnit"},{url:`/${C}/data/getMatlType`,keyName:"MatlType"},{url:`/${C}/data/getIndSector`,keyName:"IndSector"},{url:`/${C}/data/getTransGrp`,keyName:"TransGrp"},{url:`/${C}/data/getProfitCenter`,keyName:"ProfitCtr"},{url:`/${C}/data/getMatlGrp2`,keyName:"MatlGrp2"},{url:`/${C}/data/getProdAllocation`,keyName:"ProdAlloc"},{url:`/${C}/data/getVarianceKey`,keyName:"VarianceKey"},{url:`/${C}/data/getConsumptionPeriodFwd`,keyName:"ConsumptionPeriodFwd"},{url:`/${C}/data/getVendorDetails`,keyName:"Supplier"},{url:`/${C}/data/getVendorDetails`,keyName:"Json215"},{url:`/${C}/data/getBomUsage`,keyName:"BomUsage"},{url:`/${C}/data/getBomItemCategory`,keyName:"Category"},{url:`/${C}/data/getPlant`,keyName:"ProcurementPlant"},{url:`/${C}/data/getPurchaseOrg`,keyName:"PurchaseOrg"},{url:`/${C}/data/getTemperatureCondition`,keyName:"TempConds"},{url:`/${C}/data/getLabelType`,keyName:"LabelType"},{url:`/${C}/data/getLabelForm`,keyName:"LabelForm"},{url:`/${C}/data/getRoundingRuleSLED`,keyName:"RoundUpRuleExpirationDate"},{url:`/${C}/data/getExpirationDate`,keyName:"SledBbd"},{url:`/${C}/data/getSerialNumberLevel`,keyName:"SerializationLevel"},{url:`/${C}/data/getUnitOfIssue`,keyName:"IssueUnit"},{url:`/${C}/data/getTimeUnit`,keyName:"StgePdUn"},{url:`/${C}/data/getSerialNumberProfile`,keyName:"SernoProf"},{url:`/${C}/data/getDistributionProfile`,keyName:"Json125"},{url:`/${C}/data/getRndingProfile`,keyName:"Json209"},{url:`/${C}/data/getStockDeterminationGroup`,keyName:"DetermGrp"},{url:`/${C}/data/getIUIDType`,keyName:"IuidType"},{url:`/${C}/data/getClassType`,keyName:"Classtype"},{url:`/${C}/data/getVendorDetails`,keyName:"Json301"},{url:`/${C}/data/getPurGroup`,keyName:"Json13"},{url:`/${C}/data/getOrderUnit`,keyName:"Json45"},{url:`/${C}/data/getOrderUnit`,keyName:"Json252"},{url:`/${C}/data/getGeneric2`,keyName:"Json16"},{url:`/${C}/data/getGeneric3`,keyName:"Json26"},{url:`/${C}/data/getGeneric4`,keyName:"Json2"},{url:`/${C}/data/getCountryOfOrigin`,keyName:"Json4"},{url:`/${C}/data/getBaseUom`,keyName:"BaseUom"},{url:`/${C}/data/getProdHier`,keyName:"ProdHier"},{url:`/${C}/data/getExtMatlGrp`,keyName:"Extmatlgrp"},{url:`/${C}/data/getDivision`,keyName:"Division"},{url:`/${C}/data/getStorageCondition`,keyName:"StorConds"},{url:`/${C}/data/getContainerRequirements`,keyName:"Container"},{url:`/${C}/data/getHazMatNo`,keyName:"HazMatNo"},{url:`/${C}/data/getTransGrp`,keyName:"TransGrp"},{url:`/${C}/data/getMaterialGroupPack`,keyName:"MatGroupPackagingMat"},{url:`/${C}/data/getGenItemCatGroup`,keyName:"GItemCat"},{url:`/${C}/data/getXDistChainStatus`,keyName:"XSalStatus"},{url:`/${C}/data/getCSalStatus`,keyName:"CSalStatus"},{url:`/${C}/data/getPeriodIndicatorSLED`,keyName:"PeriodIndExpirationDate"},{url:`/${C}/data/getMatlGroup`,keyName:"MatlGroup"},{url:`/${C}/data/getLoadingGroup`,keyName:"Json302"},{url:`/${C}/data/getPurGroup`,keyName:"Json303"},{url:`/${C}/data/getCountryOfOrigin`,keyName:"Json304"},{url:`/${C}/data/getCountryOfOrigin`,keyName:"Json305"},{url:`/${C}/data/getGeneric1`,keyName:"Json306"},{url:`/${C}/data/getIndSector`,keyName:"Json307"},{url:`/${C}/data/getGeneric5`,keyName:"Json51"},{url:`/${C}/data/getGeneric6`,keyName:"Json60"},{url:`/${C}/data/getGeneric6`,keyName:"Json61"},{url:`/${C}/data/getBaseUom`,keyName:"Json75"},{url:`/${C}/data/getLangu`,keyName:"Json74"},{url:`/${C}/data/getDChainSpecStatus`,keyName:"Json71"},{url:`/${C}/data/getVendorDetails`,keyName:"Json301"},{url:`/${C}/data/getGeneric7`,keyName:"Json48"},{url:`/${C}/data/getBaseUom`,keyName:"Json45"},{url:`/${C}/data/getLoadingGroup`,keyName:"Json127"}].forEach(({url:I,keyName:M})=>{n(I,M)})}}},Pd=()=>{const n=as(),[N,ae]=i.useState(!1),[I,M]=i.useState(null),{getChangeTemplate:x}=bo(),{fetchDisplayDataRows:ue}=pa(),{createFCRows:D}=Po(),{customError:E}=Os(),{showSnackbar:Se}=bi();return{getDisplayData:i.useCallback(async(z,R,_,j,u)=>new Promise((ie,W)=>{ae(!0),M(null),n(ci(!0));const V=z,a=vl(Ys.CURRENT_TASK,!0,{}),K=R||(j==null?void 0:j.ATTRIBUTE_2)||(a==null?void 0:a.ATTRIBUTE_2);let Le=_?{massCreationId:u!=null&&u.isBifurcated?"":K===c.CREATE||K===c.CREATE_WITH_UPLOAD?V:"",massChildCreationId:u!=null&&u.isBifurcated&&(K===c.CREATE||K===c.CREATE_WITH_UPLOAD)?V:"",massChangeId:u!=null&&u.isBifurcated?"":K===c.CHANGE||K===c.CHANGE_WITH_UPLOAD?V:"",massExtendId:u!=null&&u.isBifurcated?"":K===c.EXTEND||K===c.EXTEND_WITH_UPLOAD?V:"",massSchedulingId:u!=null&&u.isBifurcated?"":K===c.FINANCE_COSTING?V:"",screenName:K===c.FINANCE_COSTING?"":K,dtName:K===c.FINANCE_COSTING?"":"MDG_MAT_MATERIAL_FIELD_CONFIG",version:K===c.FINANCE_COSTING?"":"v2",page:0,size:K===c.FINANCE_COSTING?100:K===c.CHANGE||K===c.CHANGE_WITH_UPLOAD?10:50,sort:"",ApproverGroup:j==null?void 0:j.ATTRIBUTE_5,Region:"",massChildSchedulingId:u!=null&&u.isBifurcated&&K===c.FINANCE_COSTING?V:"",massChildExtendId:u!=null&&u.isBifurcated&&(K===c.EXTEND||K===c.EXTEND_WITH_UPLOAD)?V:"",massChildChangeId:u!=null&&u.isBifurcated&&(K===c.CHANGE||K===c.CHANGE_WITH_UPLOAD)?V:""}:{massCreationId:"",massChangeId:"",massSchedulingId:K===c.FINANCE_COSTING||K==="Finance Costing"?V:"",massExtendId:"",screenName:K==="MASS_CREATE"||K==="Mass Create"||K===c.CREATE?c.CREATE:K===c.FINANCE_COSTING?"":c.CHANGE,dtName:K===c.FINANCE_COSTING?"":"MDG_MAT_MATERIAL_FIELD_CONFIG",version:K===c.FINANCE_COSTING?"":"v2",page:0,size:K===c.FINANCE_COSTING||R===c.FINANCE_COSTING?100:R===c.CHANGE||R===c.CHANGE_WITH_UPLOAD||K===c.CHANGE||K===c.CHANGE_WITH_UPLOAD?10:50,sort:"",ApproverGroup:j==null?void 0:j.ATTRIBUTE_5,Region:"",massChildCreationId:K==="MASS_CREATE"||K==="Mass Create"||K===c.CREATE||K===c.CREATE_WITH_UPLOAD?V:"",massChildSchedulingId:"",massChildExtendId:K===c.EXTEND||K===c.EXTEND_WITH_UPLOAD?V:"",massChildChangeId:K==="MASS_CHANGE"||K==="Mass Change"||K===c.CHANGE||K===c.CHANGE_WITH_UPLOAD?V:""};const Fe=async ge=>{var Te,ee,re,H,fe,Q,Re,he,te,Mt,T,J,p,Ce,L,q,b,se,O,St,ye;try{if((ge==null?void 0:ge.statusCode)===wt.STATUS_200){n(ci(!1)),ae(!1);const Oe=ge.body;if(n(Ea(ge==null?void 0:ge.totalElements)),(ge==null?void 0:ge.totalPages)===1||(ge==null?void 0:ge.currentPage)+1===(ge==null?void 0:ge.totalPages)?(n(jr(ge==null?void 0:ge.totalElements)),n(Ni(!0))):n(jr(((ge==null?void 0:ge.currentPage)+1)*(ge==null?void 0:ge.pageSize))),(j==null?void 0:j.ATTRIBUTE_2)===c.CHANGE||(j==null?void 0:j.ATTRIBUTE_2)===c.CHANGE_WITH_UPLOAD||R===c.CHANGE_WITH_UPLOAD||R===c.CHANGE){n(Pl({keyName:"requestHeaderData",data:(Te=Oe[0])==null?void 0:Te.Torequestheaderdata})),x(((ee=Oe[0])==null?void 0:ee.Torequestheaderdata)||"",Oe[0]||{}),ue(Oe),ie(ge);return}if(R===c.FINANCE_COSTING||(j==null?void 0:j.ATTRIBUTE_2)===c.FINANCE_COSTING){const dt={ReqCreatedBy:(H=(re=Oe[0])==null?void 0:re.Torequestheaderdata)==null?void 0:H.ReqCreatedBy,RequestStatus:(Q=(fe=Oe[0])==null?void 0:fe.Torequestheaderdata)==null?void 0:Q.RequestStatus,Region:(he=(Re=Oe[0])==null?void 0:Re.Torequestheaderdata)==null?void 0:he.Region,ReqCreatedOn:new Date().toISOString(),ReqUpdatedOn:new Date().toISOString(),RequestType:(Mt=(te=Oe[0])==null?void 0:te.Torequestheaderdata)==null?void 0:Mt.RequestType,RequestDesc:(J=(T=Oe[0])==null?void 0:T.Torequestheaderdata)==null?void 0:J.RequestDesc,RequestPriority:(Ce=(p=Oe[0])==null?void 0:p.Torequestheaderdata)==null?void 0:Ce.RequestPriority,LeadingCat:(q=(L=Oe[0])==null?void 0:L.Torequestheaderdata)==null?void 0:q.LeadingCat,RequestId:(se=(b=Oe[0])==null?void 0:b.Torequestheaderdata)==null?void 0:se.RequestId,TemplateName:(St=(O=Oe[0])==null?void 0:O.Torequestheaderdata)==null?void 0:St.TemplateName};n(xl({data:dt}));const tn=await D(Oe);n(Aa(tn)),ie(ge);return}const ht=await Sa(Oe);await n(Oo({data:ht==null?void 0:ht.payload}));const It=Object.keys(ht==null?void 0:ht.payload).filter(dt=>!isNaN(Number(dt))),bt={};It.forEach(dt=>{bt[dt]=ht==null?void 0:ht.payload[dt]}),n(Pn((ye=Object.values(bt))==null?void 0:ye.map(dt=>dt.headerData))),ie(ge)}else Se(ge==null?void 0:ge.message,"error")}catch(Oe){E(el.ERROR_GET_DISPLAY_DATA),M(Oe),ae(!1),W(Oe)}},ze=ge=>{E(el.ERROR_FETCHING_DATA),M(ge),ae(!1),n(ci(!1)),W(ge)};qe(`/${C}/data/displayMassMaterialDTO`,"post",Fe,ze,Le)}),[n]),loading:N,error:I,clearError:()=>M(null)}},Wu=()=>{var Tn,At,mn,P,A,Ae,Me,le,Ve,$e,Ft,_e,fn,be,jt,Gt,tt,Xn,Hn,ds,ss,_t,dn,Ht,qn,v,Rn,Ss,en,us,B,Is,Bn,Ne,Nn,ls,vn,is,sn,En;const{customError:n}=Os(),[N,ae]=i.useState(!1),[I,M]=i.useState([]),[x,ue]=i.useState(!1),[D,E]=i.useState(!1),[Se,oe]=i.useState(!1),[z,R]=i.useState(""),[_,j]=i.useState(!1),[u,ie]=i.useState([]),[W,V]=i.useState(!1),[a,K]=i.useState(!1),[Le,Fe]=i.useState(""),[ze,ge]=i.useState(),[Te,ee]=i.useState(""),[re,H]=i.useState(!1),[fe,Q]=i.useState(""),[Re,he]=i.useState("success"),[te,Mt]=i.useState(!1),[T,J]=i.useState(!1),[p,Ce]=i.useState(!1),[L,q]=i.useState(!1),b=as(),se=Z(Ye=>Ye.applicationConfig),O=Z(Ye=>Ye.payload.payloadData),St=Z(Ye=>Ye.payload),ye=Z(Ye=>{var it;return(it=Ye.request.requestHeader)==null?void 0:it.requestId});Z(Ye=>Ye.request.requestHeader.requestType);const Oe=Z(Ye=>{var it;return(it=Ye.userManagement)==null?void 0:it.taskData});Z(Ye=>{var it;return(it=Ye.materialDropDownData)==null?void 0:it.isOdataApiCalled});const{getDtCall:ht,dtData:It}=eo(),bt=jl(),[dt,tn]=i.useState(!0),Ct=Z(Ye=>Ye.request.tabValue),{t:mt}=Nl(),{fetchAllDropdownMasterData:gn}=$d(),{getRequestHeaderTemplate:Jt}=Wo(),Rt=[mt("Request Header"),mt("Article List"),mt("Attachments & Remarks"),mt("Preview")],[Tt,Ie]=i.useState([!1]),pe=Ye=>{b(Qs(Ye))},Y=rl(),ne=Y.state,Xe=new URLSearchParams(Y.search.split("?")[1]).get("RequestId"),Et=new URLSearchParams(Y.search),y=Et.get("RequestId"),ke=Et.get("RequestType"),yt=Et.get("reqBench"),Dn=!(Oe!=null&&Oe.taskId)&&!yt,{createPayloadFromReduxState:Zt}=yo({initialReqScreen:Dn,isReqBench:yt}),{changePayloadForTemplate:nn}=Lo(O==null?void 0:O.TemplateName),Wt=((Tn=Y.state)==null?void 0:Tn.isChildRequest)??(y&&!yt)??!1,lt=(O==null?void 0:O.RequestType)===((At=c)==null?void 0:At.CHANGE)||(O==null?void 0:O.RequestType)===((mn=c)==null?void 0:mn.CHANGE_WITH_UPLOAD)?nn(!!y):Zt(St),kt={materialDetails:lt,dtName:ai((A=(P=lt==null?void 0:lt[0])==null?void 0:P.Torequestheaderdata)==null?void 0:A.RequestType).dtName,version:ai((Me=(Ae=lt==null?void 0:lt[0])==null?void 0:Ae.Torequestheaderdata)==null?void 0:Me.RequestType).version,requestId:((Ve=(le=lt==null?void 0:lt[0])==null?void 0:le.Torequestheaderdata)==null?void 0:Ve.RequestId)||"",scenario:(_e=ai((Ft=($e=lt==null?void 0:lt[0])==null?void 0:$e.Torequestheaderdata)==null?void 0:Ft.RequestType))==null?void 0:_e.scenario,templateName:(O==null?void 0:O.RequestType)===((fn=c)==null?void 0:fn.CHANGE)||(O==null?void 0:O.RequestType)===((be=c)==null?void 0:be.CHANGE_WITH_UPLOAD)?(Gt=(jt=lt==null?void 0:lt[0])==null?void 0:jt.Torequestheaderdata)==null?void 0:Gt.TemplateName:"",matlType:"ALL",region:((Xn=(tt=lt==null?void 0:lt[0])==null?void 0:tt.Torequestheaderdata)==null?void 0:Xn.Region)||""},{getDisplayData:Bt}=Pd(),Ln=()=>{V(!0)},xt=()=>{V(!1)},rn=()=>{K(!0)},Wn=Ye=>{K(Ye)},yn=()=>{Te==="success"?bt("/requestBench"):xt()},Xt=()=>{ue(!0)},we=Ye=>{let it="";ke===c.CREATE_WITH_UPLOAD?it="getAllMaterialsFromExcel":ke===c.EXTEND_WITH_UPLOAD?it="getAllMaterialsFromExcelForMassExtend":it="getAllMaterialsFromExcelForMassChange",Q("Initiating Excel Upload"),H(!0);const pt=new FormData;[...Ye].forEach(zt=>pt.append("files",zt)),pt.append("dtName",ke===c.CREATE_WITH_UPLOAD||ke===c.EXTEND_WITH_UPLOAD?"MDG_MAT_MASS_CREATE_EXTEND_FIELD_CONFIG":"MDG_MAT_CHANGE_TEMPLATE"),pt.append("version",ke===c.CREATE_WITH_UPLOAD||ke===c.EXTEND_WITH_UPLOAD?"v1":"v5"),pt.append("requestId",Xe||""),pt.append("region",O!=null&&O.Region?O==null?void 0:O.Region:"US"),pt.append("matlType","ALL");const $t=zt=>{var Un;(zt==null?void 0:zt.statusCode)===wt.STATUS_200?(j(!1),H(!1),Q(""),bt((Un=ns)==null?void 0:Un.REQUEST_BENCH)):(j(!1),H(!1),ge(zt==null?void 0:zt.message),Q(""),he("error"),de())},Gn=zt=>{H(!1),ge(zt==null?void 0:zt.message),Q(""),he("error"),de()};qe(`/${C}/massAction/${it}`,"postformdata",$t,Gn,pt)};i.useEffect(()=>((async()=>{var it;if(Xe){const pt=vl(Ys.CURRENT_TASK,!0,{}),$t=ke||(Oe==null?void 0:Oe.ATTRIBUTE_2)||(pt==null?void 0:pt.ATTRIBUTE_2);await Bt(y,$t,yt,Oe,ne,"Article"),(ke===c.CHANGE_WITH_UPLOAD&&!((it=ne==null?void 0:ne.material)!=null&&it.length)||ke===c.CREATE_WITH_UPLOAD||ke===c.EXTEND_WITH_UPLOAD)&&((ne==null?void 0:ne.reqStatus)===cs.DRAFT||(ne==null?void 0:ne.reqStatus)===cs.UPLOAD_FAILED)?(b(Qs(0)),E(!1),oe(!1)):(b(Qs(1)),E(!0),oe(!0)),J(!0)}else b(Qs(0))})(),()=>{b(no([])),b(ma()),b(fa()),b(Na()),b(Ca()),b(_a()),b(fl({})),b(xl({data:{}})),b(Oa([])),b(Ia([])),b(ba({})),b(Ra()),b(Io([])),b(Bl([])),b(Pn([])),b(Fl({})),di(Ys.CURRENT_TASK),di(Ys.ROLE)}),[Xe,b]);function on(Ye){let it={decisionTableId:null,decisionTableName:so.MDG_MAT_REGION_DIVISION_MAPPING,version:"v1",conditions:[{"MDG_CONDITIONS.MDG_MAT_REGION":Ye}]};ht(it)}i.useEffect(()=>{O!=null&&O.Region&&on(O==null?void 0:O.Region)},[O==null?void 0:O.Region]),i.useEffect(()=>{var Ye,it;if(It){const $t=[...Ma((it=(Ye=It==null?void 0:It.result)==null?void 0:Ye[0])==null?void 0:it.MDG_MAT_REGION_DIVISION_MAPPING)].sort((Gn,zt)=>Gn.code.localeCompare(zt.code));b(zs({keyName:"Division",data:$t})),tn(!1),Q(xa.DT_LOADING)}},[It]),i.useEffect(()=>(gn(),Da(Ys.MODULE,Ds.ART),Jt(),Kn(),b(Pn([])),b(zs({keyName:"Region",data:La})),b(zs({keyName:"DiversionControlFlag",data:ya})),R(va("MAT")),()=>{b(to({})),di(Ys.MODULE)}),[]),i.useEffect(()=>{D&&Ie([!0])},[D]);const Kn=()=>{let Ye={decisionTableId:null,decisionTableName:"MDG_ATTACHMENTS_LIST_DT",version:"v1",rulePolicy:null,validityDate:null,conditions:[{"MDG_CONDITIONS.MDG_ATTACHMENTS_OBJECT_TYPE":"Article","MDG_CONDITIONS.MDG_ATTACHMENTS_SCENARIO":"Create","MDG_CONDITIONS.MDG_ATTACHMENTS_REG_COMP_CODE":1}],systemFilters:null,systemOrders:null,filterString:null};ae(!0);const it=$t=>{var Gn,zt;if(ae(!1),$t.statusCode===200){const zn=((zt=(Gn=$t==null?void 0:$t.data)==null?void 0:Gn.result[0])==null?void 0:zt.MDG_ATTACHMENTS_ACTION_TYPE)||[];ie(zn)}},pt=$t=>{n($t)};se.environment==="localhost"?qe(`/${Bs}/rest/v1/invoke-rules`,"post",it,pt,Ye):qe(`/${Bs}/v1/invoke-rules`,"post",it,pt,Ye)},an=()=>{var zt,Un,zn,Qn,hs,gs;const Ye=y!=null&&y.includes("FCA")?Ot.EXCEL.DOWNLOAD_EXCEL_FINANCE:Ot.EXCEL.DOWNLOAD_EXCEL_MAT;Q("Please wait 1-2 minutes while real-time data are exported into the template. Thank you for your patience."),H(!0);let it={massSchedulingId:O==null?void 0:O.RequestId},pt={dtName:(O==null?void 0:O.RequestType)===((zt=c)==null?void 0:zt.CHANGE)||(O==null?void 0:O.RequestType)===((Un=c)==null?void 0:Un.CHANGE_WITH_UPLOAD)?"MDG_MAT_CHANGE_TEMPLATE":"MDG_MAT_MASS_CREATE_EXTEND_FIELD_CONFIG",version:(O==null?void 0:O.RequestType)===((zn=c)==null?void 0:zn.CHANGE)||(O==null?void 0:O.RequestType)===((Qn=c)==null?void 0:Qn.CHANGE_WITH_UPLOAD)?"v4":"v1",requestId:(O==null?void 0:O.RequestId)||ye||"",scenario:(O==null?void 0:O.RequestType)===((hs=c)==null?void 0:hs.CHANGE)||(O==null?void 0:O.RequestType)===((gs=c)==null?void 0:gs.CHANGE_WITH_UPLOAD)?"Change with Upload":"Create with Upload",templateName:(O==null?void 0:O.TemplateName)||"",region:(O==null?void 0:O.Region)||"",matlType:"ALL"};const $t=Cn=>{const ms=URL.createObjectURL(Cn),Zn=document.createElement("a");Zn.href=ms,Zn.setAttribute("download",`${O!=null&&O.TemplateName?O==null?void 0:O.TemplateName:y!=null&&y.includes("FCA")?c.FINANCE_COSTING:"Mass_Create"}_Data Export.xlsx`),document.body.appendChild(Zn),Zn.click(),document.body.removeChild(Zn),URL.revokeObjectURL(ms),H(!1),Q(""),ge(`${O!=null&&O.TemplateName?O==null?void 0:O.TemplateName:y!=null&&y.includes("FCA")?c.FINANCE_COSTING:"Mass_Create"}_Data Export.xlsx has been exported successfully.`),he("success"),de()},Gn=()=>{};qe(`/${C}${Ye}`,"postandgetblob",$t,Gn,y!=null&&y.includes("FCA")?it:pt)},de=()=>{Mt(!0)},vt=()=>{Mt(!1)},jn=()=>{var Ye,it,pt;Xe&&!yt?bt((Ye=ns)==null?void 0:Ye.MY_TASK):yt?bt((it=ns)==null?void 0:it.REQUEST_BENCH):!Xe&&!yt&&bt((pt=ns)==null?void 0:pt.ARTICLE_MASTER_DATA)},Jn=()=>{q(!1)};return f(Ut,{children:[dt&&e(hl,{blurLoading:re,loaderMessage:fe}),f(Be,{sx:{padding:2},children:[f(ve,{sx:{display:"flex",justifyContent:"space-between",alignItems:"center"},children:[ye||Xe?f(Fs,{direction:"row",spacing:1,children:[f(st,{variant:"h6",sx:{mb:1,textAlign:"left",display:"flex",alignItems:"center",gap:1},children:[e(cd,{sx:{fontSize:"1.5rem"}}),mt("Request Header ID"),": ",e("span",{children:ye||Xe})]}),(Oe==null?void 0:Oe.taskDesc)&&e(st,{variant:"h6",sx:{mb:1,textAlign:"left",display:"flex",alignItems:"center",gap:1,color:"#3B30C8"},children:`- ${Oe==null?void 0:Oe.taskDesc}`})]}):e("div",{style:{flex:1}}),Ct===1&&f(Be,{sx:{display:"flex",justifyContent:"flex-end",gap:"1rem"},children:[e(nt,{variant:"outlined",size:"small",title:mt("Download Error Report"),disabled:!y,onClick:()=>{bt(`/requestBench/errorHistory?RequestId=${y||""}`,{state:{display:!0,childRequest:Wt,module:Ds.ART}})},color:"primary",children:e(Ga,{sx:{padding:"2px"}})}),(O==null?void 0:O.RequestType)===c.CREATE||(O==null?void 0:O.RequestType)===c.EXTEND||(O==null?void 0:O.RequestType)===c.EXTEND_WITH_UPLOAD||(O==null?void 0:O.RequestType)===c.CREATE_WITH_UPLOAD||Xe!=null&&Xe.includes("FCA")?e(nt,{variant:"outlined",disabled:!y,size:"small",onClick:()=>Ce(!0),title:Xe!=null&&Xe.includes("FCA")?mt("Finance Costing Change Log"):mt("Create Change Log"),children:e(Zr,{sx:{padding:"2px"}})}):e(nt,{variant:"outlined",disabled:!y,size:"small",onClick:rn,title:mt("Change Log"),children:e(Zr,{sx:{padding:"2px"}})}),e(nt,{variant:"outlined",disabled:!y,size:"small",onClick:an,title:mt("Export Excel"),children:e(id,{sx:{padding:"2px"}})})]}),a&&e(Ja,{open:!0,closeModal:Wn,requestId:ye||Xe,requestType:O==null?void 0:O.RequestType}),p&&e(Xa,{module:(Hn=Ds)==null?void 0:Hn.ART,open:!0,closeModal:()=>Ce(!1),requestId:ye||Xe,requestType:O==null?void 0:O.RequestType})]}),(O==null?void 0:O.TemplateName)&&f(st,{variant:"h6",sx:{mb:1,textAlign:"left",display:"flex",alignItems:"center",gap:1},children:[e(ad,{sx:{fontSize:"1.5rem"}}),mt("Template Name"),": ",e("span",{children:O==null?void 0:O.TemplateName})]}),e(cn,{onClick:()=>{var Ye,it;if(yt&&!((Ye=Ls)!=null&&Ye.includes(O==null?void 0:O.RequestStatus))){bt((it=ns)==null?void 0:it.REQUEST_BENCH);return}q(!0)},color:"primary","aria-label":"upload picture",component:"label",sx:{left:"-10px"},title:mt("Back"),children:e(Ci,{sx:{fontSize:"25px",color:"#000000"}})}),e(So,{nonLinear:!0,activeStep:Ct,sx:{display:"flex",alignItems:"center",justifyContent:"center",margin:"25px 14%",marginTop:"-35px"},children:Rt.map((Ye,it)=>e(Si,{children:e(mi,{color:"error",disabled:it===1&&!D||it===2&&!Se||it===3&&!Se,onClick:()=>pe(it),sx:{fontSize:"50px",fontWeight:"bold"},children:e("span",{style:{fontSize:"15px",fontWeight:"bold"},children:Ye})})},Ye))}),e(Kl,{dialogState:W,openReusableDialog:Ln,closeReusableDialog:xt,dialogTitle:Le,dialogMessage:ze,handleDialogConfirm:xt,dialogOkText:"OK",handleOk:yn,dialogSeverity:Te}),e(hl,{blurLoading:re,loaderMessage:fe}),Ct===0&&f(Ut,{children:[e(Ed,{setIsSecondTabEnabled:E,setIsAttachmentTabEnabled:oe,requestStatus:ne!=null&&ne.reqStatus?ne==null?void 0:ne.reqStatus:cs.ENABLE_FOR_FIRST_TIME,downloadClicked:x,setDownloadClicked:ue}),(ke===c.CHANGE_WITH_UPLOAD||ke===c.CREATE_WITH_UPLOAD||ke===c.EXTEND_WITH_UPLOAD)&&((ne==null?void 0:ne.reqStatus)==cs.DRAFT&&!((ds=ne==null?void 0:ne.material)!=null&&ds.length)||(ne==null?void 0:ne.reqStatus)==cs.UPLOAD_FAILED)&&e(ud,{handleDownload:Xt,setEnableDocumentUpload:j,enableDocumentUpload:_,handleUploadMaterial:we}),((O==null?void 0:O.RequestType)===((ss=c)==null?void 0:ss.CHANGE)||(O==null?void 0:O.RequestType)===((_t=c)==null?void 0:_t.CHANGE_WITH_UPLOAD))&&!y&&(O==null?void 0:O.DirectAllowed)!=="X"&&(O==null?void 0:O.DirectAllowed)!==void 0&&f(st,{sx:{fontSize:"13px",fontWeight:"500",color:(Ht=(dn=We)==null?void 0:dn.error)==null?void 0:Ht.dark,marginTop:"1rem",marginLeft:"0.5rem"},children:[e(Be,{component:"span",sx:{fontWeight:"bold"},children:"Note:"})," ","You are not authorized to Tcode"," ",f(Be,{component:"span",sx:{fontWeight:"bold"},children:[" ","MM02."]})]})]}),Ct===1&&((O==null?void 0:O.RequestType)===((qn=c)==null?void 0:qn.CREATE)||(Oe==null?void 0:Oe.ATTRIBUTE_2)===((v=c)==null?void 0:v.CREATE)||ke===((Rn=c)==null?void 0:Rn.CREATE)||ke===((Ss=c)==null?void 0:Ss.CREATE_WITH_UPLOAD)?e(xd,{requestStatus:ne!=null&&ne.reqStatus?ne==null?void 0:ne.reqStatus:cs.ENABLE_FOR_FIRST_TIME,mandFields:I,addHardCodeData:T,setIsAttachmentTabEnabled:oe,setCompleted:Ie}):(O==null?void 0:O.RequestType)===((en=c)==null?void 0:en.EXTEND)||(Oe==null?void 0:Oe.ATTRIBUTE_2)===((us=c)==null?void 0:us.EXTEND)||(Oe==null?void 0:Oe.ATTRIBUTE_2)===((B=c)==null?void 0:B.EXTEND_WITH_UPLOAD)||ke===((Is=c)==null?void 0:Is.EXTEND)||ke===((Bn=c)==null?void 0:Bn.EXTEND_WITH_UPLOAD)?e(yd,{requestStatus:ne!=null&&ne.reqStatus?ne==null?void 0:ne.reqStatus:cs.ENABLE_FOR_FIRST_TIME,mandFields:I,addHardCodeData:T,setIsAttachmentTabEnabled:oe,setCompleted:Ie}):(O==null?void 0:O.RequestType)===((Ne=c)==null?void 0:Ne.FINANCE_COSTING)||(Oe==null?void 0:Oe.ATTRIBUTE_2)===((Nn=c)==null?void 0:Nn.FINANCE_COSTING)||ke===((ls=c)==null?void 0:ls.FINANCE_COSTING)?e(za,{setCompleted:Ie}):e(kd,{setIsAttachmentTabEnabled:!0,setCompleted:Ie,downloadClicked:x,setDownloadClicked:ue})),Ct===2&&e(td,{requestStatus:ne!=null&&ne.reqStatus?ne==null?void 0:ne.reqStatus:cs.ENABLE_FOR_FIRST_TIME,attachmentsData:u,requestIdHeader:ye||Xe,pcNumber:z,module:(vn=Ds)==null?void 0:vn.ART,artifactName:Ua.ART}),Ct===3&&e(Be,{sx:{width:"100%",overflow:"auto"},children:e(nd,{requestStatus:ne!=null&&ne.reqStatus?ne==null?void 0:ne.reqStatus:cs.ENABLE_FOR_FIRST_TIME,module:(is=Ds)==null?void 0:is.ART,payloadData:St,payloadForDownloadExcel:kt})})]}),e(Cl,{openSnackBar:te,alertMsg:ze,alertType:Re,handleSnackBarClose:vt}),L&&f(Xl,{isOpen:L,titleIcon:e(ka,{size:"small",sx:{color:(En=(sn=We)==null?void 0:sn.secondary)==null?void 0:En.amber,fontSize:"20px"}}),Title:mt("Warning"),handleClose:Jn,children:[e(es,{sx:{mt:2},children:mt(Zs.LEAVE_PAGE_MESSAGE)}),f(ts,{children:[e(nt,{variant:"outlined",size:"small",sx:{...Di},onClick:Jn,children:mt("No")}),e(nt,{variant:"contained",size:"small",sx:{...zl},onClick:jn,children:mt("Yes")})]})]})]})};export{Wu as default};
