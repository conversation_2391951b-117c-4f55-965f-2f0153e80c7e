import{s as $e,b as Xe,aP as Ye,a as be,r,n as Oe,j as a,U as je,O as E,c as p,V as et,W as tt,d as v,i as nt,X as f,Y as Be,$ as ke,a4 as at,a5 as st,a6 as Ue,a7 as Ke,a8 as ot,a9 as lt,ab as Pe,ac as it,ad as rt,at as U,k as ct,A as dt,an as He,w as Ve,aO as X,C as O,dg as re,Z as ut,x as gt,c5 as ht,ae as ft,a0 as pt,o as Me,g as yt,P as Ct,N as _t,Q as Nt,S as Re,af as St,ag as Et,ah as At,bK as Le,dh as Dt,F as Ge,J as ve,aD as Bt,av as Mt,ay as Tt,az as It,aA as mt,aB as xt,aC as kt,T as Te,B as Z,aN as Kt,aJ as Pt,aK as we,aL as Rt}from"./index-f7d9b065.js";import{S as Lt}from"./SingleSelectDropdown-aee403d4.js";import{R as Gt}from"./ReusablePresetFilter-36c3fb2e.js";import{L as vt}from"./LargeDropdown-b2630df6.js";import{d as wt}from"./History-13dab512.js";const zt=U(ct,{target:"et9bhzr5"})(({theme:A})=>({marginTop:"0px !important",border:`1px solid ${A.palette.primary.main}`,borderRadius:"8px",boxShadow:"0 2px 4px rgba(0,0,0,0.05)","&:not(:last-child)":{borderBottom:0},"&:before":{display:"none"}}),""),$t=U(dt,{target:"et9bhzr4"})(({theme:A})=>({minHeight:"2rem !important",margin:"0px !important",backgroundColor:A.palette.primary.light,borderRadius:"8px 8px 0 0",transition:"all 0.2s ease-in-out","&:hover":{backgroundColor:`${A.palette.primary.light}20`}}),""),Yt=U(E,{target:"et9bhzr3"})({name:"seull4",styles:"padding:0.75rem;gap:0.5rem"}),bt=U(E,{target:"et9bhzr2"})({name:"1ikq1ll",styles:"display:flex;justify-content:flex-end;padding-right:0.75rem;padding-bottom:0.75rem;padding-top:0rem;gap:0.5rem"}),ze=U(He,{target:"et9bhzr1"})({name:"1x9mjbh",styles:"border-radius:4px;padding:4px 12px;text-transform:none;font-size:0.875rem"}),Ie=U(v,{target:"et9bhzr0"})(({theme:A})=>({fontSize:"0.75rem",color:A.palette.primary.dark,marginBottom:"0.25rem",fontWeight:500}),""),Ot=({searchParameters:A,onSearch:R,onClear:ce,filterFieldData:Y,setFilterFieldData:D,items:w})=>{var W,$,oe,J,x,k,e,le,ie;const B=$e(),j=Xe(),{customError:de}=Ye(),{t:m}=be(),[M,ee]=r.useState([]),[b,H]=r.useState([]),[z,ue]=r.useState({}),[V,y]=r.useState({}),[ge,he]=r.useState("");r.useState([]);const[te,T]=r.useState(!1),[ne,fe]=r.useState(null),pe=Oe(s=>s.commonFilter.BankKey),me=48,ye=8,Ce={PaperProps:{style:{maxHeight:me*4.5+ye,width:250}}},_e=()=>{var s;B(Ve({module:(s=X)==null?void 0:s.BK})),D(u=>{const d={...u};return Object.keys(d).forEach(N=>{d[N]={code:"",desc:""}}),d}),ee([]),ce()},ae=(s,u)=>{var S,C;const d=s===((S=f)==null?void 0:S.BANKCTRY)?u?[u]:[]:Array.isArray(u)?u:[];ue(h=>({...h,[s]:d}));let N={...pe,[s]:d.map(h=>h==null?void 0:h.code).join("$^$")};B(gt({module:(C=ht)==null?void 0:C.BK,filterData:N}))},Ne=(s,u,d,N)=>{T(_=>({..._,[s]:!0}));const S=_=>{T(K=>({...K,[s]:!1})),H(K=>({...K,[s]:(_==null?void 0:_.body)||[]}))},C=()=>{T(_=>({..._,[s]:!1}))},h={[d]:N,top:100,skip:0};O(`/${re}/data/${u}`,"post",S,C,h)},Se=(s,u)=>{const d=s.target.value;he(d);const N=se.find(h=>h.key===u),{endpoint:S,payloadKey:C}=N;if(ne&&clearTimeout(ne),d.length>=4){const h=setTimeout(()=>{Ne(u,S,C,d)},200);fe(h)}},F=(s,u,d={})=>{const N=C=>{H(h=>({...h,[u]:(C==null?void 0:C.body)||[]}))},S=()=>{de(ft.ERROR_FETCHING_DATA)};O(`/${re}/data/${s}`,"post",N,S,d)},Ee=(s,u)=>{var _,K;const d=s==null?void 0:s.MDG_MAT_JSON_FIELD_NAME,N=m(s==null?void 0:s.MDG_MAT_UI_FIELD_NAME),S={matGroup:b[d]||[],selectedMaterialGroup:z[d]||[],setSelectedMaterialGroup:L=>ae(d,L),placeholder:`SELECT ${N}`,isDropDownLoading:V[d],minWidth:"90%",onInputChange:L=>Se(L,d)},C={[f.BANKCTRY]:()=>{var L;return a(Lt,{options:b[d]||[],value:((L=z[d])==null?void 0:L[0])||null,onChange:Ae=>ae(d,Ae),placeholder:`SELECT ${N}`,disabled:V[d],isLoading:V[d],minWidth:"90%"})},[f.REGION]:()=>a(vt,{...S}),default:()=>a(pt,{...S})},h=C[d]||C.default;return p(E,{item:!0,md:2,children:[p(Ie,{sx:Be,children:[N,a("span",{style:{color:(K=(_=ut)==null?void 0:_.error)==null?void 0:K.dark},children:"*"})]}),a(ke,{size:"small",fullWidth:!0,children:h()})]},u)},se=[{key:(W=f)==null?void 0:W.REGION,endpoint:"getRegionBasedOnCountry",dependsOn:($=f)==null?void 0:$.BANKCTRY},{key:(oe=f)==null?void 0:oe.BANKKEYY,endpoint:"getSearchParamsBankKey",payloadKey:"bankKey"},{key:(J=f)==null?void 0:J.BANKNO,endpoint:"getSearchParamsBankNumber",payloadKey:"bankNumber"},{key:(x=f)==null?void 0:x.CITY,endpoint:"getSearchParamsCity",payloadKey:"city"},{key:(k=f)==null?void 0:k.STREETLNG,endpoint:"getSearchParamsStreet",payloadKey:"street"},{key:(e=f)==null?void 0:e.BANKBRANCH,endpoint:"getSearchParamsBankBranch",payloadKey:"bankBranch"},{key:(le=f)==null?void 0:le.BANKCTRY,endpoint:"postCountry"},{key:(ie=f)==null?void 0:ie.BANKNAME,endpoint:"getSearchParamsBankName",payloadKey:"bankName"}];return r.useEffect(()=>{se.forEach(({key:s,endpoint:u,dependsOn:d,payloadKey:N})=>{var S,C;if(!N)if(d){const h=z[d]||[];if(h.length){const _=((C=(S=h[0])==null?void 0:S.code)==null?void 0:C.trim())||"";_&&F(u,s,{bankCtry:_})}}else F(u,s)})},[z]),a(E,{container:!0,sx:je,children:a(E,{item:!0,md:12,children:p(zt,{defaultExpanded:!0,className:"filterBK",children:[p($t,{expandIcon:a(et,{sx:{fontSize:"1.25rem",color:j.palette.primary.dark}}),"aria-controls":"panel1a-content",id:"panel1a-header",children:[a(tt,{sx:{fontSize:"1.25rem",marginRight:1,color:j.palette.primary.dark}}),a(v,{sx:{fontSize:"0.875rem",fontWeight:600,color:j.palette.primary.dark},children:m("Filter Bank Key")})]}),p(nt,{sx:{padding:"1rem 1rem 0.5rem"},children:[p(Yt,{container:!0,children:[p(E,{container:!0,rowSpacing:1,spacing:2,alignItems:"center",sx:{padding:"0rem 1rem 0.5rem"},children:[A==null?void 0:A.filter(s=>s.MDG_MAT_VISIBILITY!=="Hidden").sort((s,u)=>s.MDG_MAT_SEQUENCE_NO-u.MDG_MAT_SEQUENCE_NO).map((s,u)=>{const d=s==null?void 0:s.MDG_MAT_JSON_FIELD_NAME;return[f.REGION,f.BANKKEYY,f.BANKNO,f.CITY,f.STREETLNG,f.BANKBRANCH,f.BANKCTRY,f.BANKNAME].includes(d)?Ee(s,u):null}),p(E,{item:!0,md:2,children:[a(Ie,{sx:Be,children:m("Add New Filters")}),a(ke,{sx:{width:"100%"},children:a(at,{sx:{font_Small:Be,fontSize:"12px",width:"100%"},size:"small",multiple:!0,limitTags:2,value:M,onChange:s=>ee(s.target.value),renderValue:s=>s.join(", "),MenuProps:Ce,endAdornment:M.length>0&&a(st,{position:"end",sx:{marginRight:"10px"},children:a(Ue,{size:"small",onClick:()=>ee([]),"aria-label":"Clear selections",children:a(Ke,{})})}),children:w==null?void 0:w.map(s=>p(ot,{value:s.title,children:[a(lt,{checked:M.indexOf(s.title)>-1}),s.title]},s.title))})})]})]}),a(E,{container:!0,sx:{flexDirection:"row",padding:"0rem 1rem 0.5rem"},gap:1,children:M.map((s,u)=>a(E,{item:!0,md:2,children:a(Ie,{sx:{fontSize:"12px"},children:m(s)})},u))})]}),p(bt,{children:[a(ze,{variant:"outlined",size:"small",startIcon:a(Ke,{sx:{fontSize:"1rem"}}),onClick:_e,children:m("Clear")}),a(E,{sx:{...Pe},children:a(Gt,{moduleName:"BankKey",handleSearch:R})}),a(ze,{variant:"contained",size:"small",startIcon:a(it,{sx:{fontSize:"1rem"}}),sx:{...rt,...Pe},onClick:R,children:m("Search")})]})]})]})})})},Jt=()=>{var xe;Ye(),Me();const{getDtCall:A,dtData:R}=Me(),{getDtCall:ce,dtData:Y}=Me(),D=$e(),w=yt(),{t:B}=be(),[j,de]=r.useState(!1),[m,M]=r.useState(!1),[ee,b]=r.useState(!1),[H,z]=r.useState(!1),[ue,V]=r.useState(null),[y,ge]=r.useState([]),[he,te]=r.useState([...y]),[T,ne]=r.useState((xe=Ct)==null?void 0:xe.TOP_SKIP),[fe,pe]=r.useState({}),[me,ye]=r.useState([]),[Ce,_e]=r.useState([]),[ae,Ne]=r.useState([]),[Se,F]=r.useState(!1),[Ee,se]=r.useState(),[W,$]=r.useState(0),[oe,J]=r.useState(0),[x,k]=r.useState(0);r.useState(!0);const e=Oe(o=>o.commonFilter.BankKey);let le={};const ie=(o,l)=>{var g;let c={};b(!0);const i=n=>{b(!1);const P=n.body;ye(I=>({...I,[l]:P}))},t=n=>{b(!1)};l===((g=X)==null?void 0:g.BK)?O(o,"post",i,t,c):O(o,"get",i,t)},s=o=>{var c;let l={decisionTableId:null,decisionTableName:ve.MDG_MAT_SEARCHSCREEN_COLUMN,version:"v2",conditions:[{"MDG_CONDITIONS.MDG_MAT_REGION":"US","MDG_CONDITIONS.MDG_MODULE":(c=X)==null?void 0:c.BK,"MDG_CONDITIONS.MDG_MAT_SEARCHTYPE":"Master Data","MDG_CONDITIONS.MDG_MAT_VIEW_TYPE":"NA"}]};A(l)},u=()=>{var l;let o={decisionTableId:null,decisionTableName:ve.MDG_MAT_SEARCHSCREEN_PARAMETER,version:"v2",conditions:[{"MDG_CONDITIONS.MDG_MAT_REGION":"US","MDG_CONDITIONS.MDG_MODULE":(l=X)==null?void 0:l.BK,"MDG_CONDITIONS.MDG_MAT_SEARCHTYPE":"Master Data","MDG_CONDITIONS.MDG_MAT_VIEW_TYPE":"NA"}]};ce(o)},d=(o,l)=>({field:o,headerName:B(l),editable:!1,flex:1,renderCell:c=>{const i=c.value?c.value.split(",").map(n=>n.trim()):[],t=i.length-1;if(i.length===0)return"-";const g=n=>{const[P,...I]=n.split("-");return p(Ge,{children:[a("strong",{children:P}),I.length?` - ${I.join("-")}`:""]})};return p(Z,{sx:{display:"flex",alignItems:"center",width:"100%",minWidth:0},children:[a(Te,{title:i[0],placement:"top",arrow:!0,children:a(v,{variant:"body2",sx:{overflow:"hidden",textOverflow:"ellipsis",whiteSpace:"nowrap",flex:1,minWidth:0},children:g(i[0])})}),t>0&&a(Z,{sx:{display:"flex",alignItems:"center",ml:1,flexShrink:0},children:a(Te,{arrow:!0,placement:"right",title:p(Z,{sx:{p:1,maxHeight:200,overflowY:"auto"},children:[p(v,{variant:"subtitle2",sx:{fontWeight:600,mb:1},children:["Additional ",l,"s (",t,")"]}),i.slice(1).map((n,P)=>a(v,{variant:"body2",sx:{mb:.5},children:g(n)},P))]}),children:p(Z,{sx:{display:"flex",alignItems:"center",cursor:"pointer"},children:[a(Kt,{sx:{fontSize:"1rem",color:"primary.main","&:hover":{color:"primary.dark"}}}),p(v,{variant:"caption",sx:{ml:.5,color:"primary.main",fontSize:"11px"},children:["+",t]})]})})})]})}}),N=(o,l)=>({field:o,headerName:B(l),editable:!1,flex:1,renderCell:c=>{var g;const[i,...t]=((g=c.value)==null?void 0:g.split(" - "))||[];return p("span",{style:{flex:1,wordBreak:"break-word",whiteSpace:"normal"},children:[a("strong",{children:i})," ",t.length?`- ${t.join(" - ")}`:""]})}}),S=()=>({field:"dataValidation",headerName:B("Audit History"),editable:!1,flex:1,renderCell:o=>a(Z,{sx:{display:"flex",justifyContent:"center",alignItems:"center",height:"100%"},children:a(Te,{title:"View Audit Log",placement:"top",children:a(Ue,{onClick:c=>{var i,t;c.stopPropagation(),w((i=Le)==null?void 0:i.AUDIT_LOG,{state:{materialNumber:o.row.BankKey,module:(t=X)==null?void 0:t.BK}})},size:"small",sx:{color:"primary.main",marginLeft:"20px","&:hover":{color:"primary.dark",backgroundColor:"rgba(25, 118, 210, 0.04)",transform:"scale(1.05)",marginLeft:"20px"},transition:"all 0.2s ease-in-out"},children:a(wt,{sx:{fontSize:"1.5rem"}})})})})}),C=o=>{const l=[];let c=(o==null?void 0:o.sort((i,t)=>i.MDG_MAT_SEQUENCE_NO-t.MDG_MAT_SEQUENCE_NO))||[];return c&&(c==null||c.forEach(i=>{if((i==null?void 0:i.MDG_MAT_VISIBILITY)===Bt.DISPLAY&&i!=null&&i.MDG_MAT_UI_FIELD_NAME){const t=i.MDG_MAT_JSON_FIELD_NAME,g=i.MDG_MAT_UI_FIELD_NAME;t==="DataValidation"?l.push(S()):i.MDG_MAT_FIELD_TYPE==="Multiple"?l.push(d(t,g)):i.MDG_MAT_FIELD_TYPE==="Single"&&l.push(N(t,g))}})),l},h=o=>{if(!o){$(oe),k(0),te([...y]);return}const l=y.filter(c=>{var g;let i=!1,t=Object.keys(c);for(let n=0;n<t.length&&(i=c[t[n]]?(c==null?void 0:c[t==null?void 0:t[n]])&&((g=c==null?void 0:c[t==null?void 0:t[n]].toString().toLowerCase())==null?void 0:g.indexOf(o==null?void 0:o.toLowerCase()))!=-1:!1,!i);n++);return i});te([...l]),$(l==null?void 0:l.length)},_=o=>{M(!0),k(0);let l={bankCountry:(e==null?void 0:e.BankCtry)??"",bankKey:(e==null?void 0:e.BankKey)??"",bankName:(e==null?void 0:e.BankName)??"",swiftCode:(e==null?void 0:e.swiftCode)??"",bankNumber:(e==null?void 0:e.BankNo)??"",createdBy:(e==null?void 0:e.createdBy)??"",region:(e==null?void 0:e.Region)??"",branch:(e==null?void 0:e.BankBranch)??"",street:(e==null?void 0:e.StreetLng)??"",city:(e==null?void 0:e.City)??"",top:100,skip:0};const c=t=>{var I,Q,q,G;if((t==null?void 0:t.statusCode)===Pt.STATUS_200){var g=[];for(let De=0;De<((Q=(I=t==null?void 0:t.body)==null?void 0:I.list)==null?void 0:Q.length);De++){var n=(q=t==null?void 0:t.body)==null?void 0:q.list[De],P={id:we(),BankName:n.BankName!==""?`${n.BankName}`:"Not Available",BankKey:n.BankKey!==""?`${n.BankKey}`:"Not Available",BankNo:n.BankNumber!==""?`${n.BankNumber}`:"Not Available",BankBranch:n.BankBranch!==""?`${n.BankBranch}`:"Not Available",BankCtry:n.BankCtry!==""?`${n.BankCtry}`:"Not Available",Region:n.region!==""?`${n.Region}`:"Not Available",StreetLng:n.Street!==""?`${n.Street}`:"Not Available",City:n.City!==""?`${n.City}`:"Not Available"};g.push(P)}ge(g),M(!1),$((G=t.body)==null?void 0:G.count),J(t.count),D(Rt({module:"BankKey"}))}else t.statusCode===400&&(setSearchDialogTitle("Warning"),setSearchDialogMessage("Please Select Lesser Fields as the URL is getting too long !!"),handleSearchDialogClickOpen())},i=t=>{console.log(t),M(!1)};O(`/${re}/data/getBankKeysBasedOnAdditionalParams`,"post",c,i,l)},K=()=>{var i;M(!0);let o={bankCountry:(e==null?void 0:e.bankCountry)??"",bankKey:(e==null?void 0:e.bankKey)??"",bankName:(e==null?void 0:e.bankName)??"",swiftCode:(e==null?void 0:e.swiftCode)??"",bankNumber:(e==null?void 0:e.bankNumber)??"",createdBy:((i=e==null?void 0:e.createdBy)==null?void 0:i.code)??"",region:(e==null?void 0:e.Region)??"",branch:(e==null?void 0:e.branch)??"",street:(e==null?void 0:e.street)??"",city:(e==null?void 0:e.city)??"",top:100,skip:(y==null?void 0:y.length)??0};const l=t=>{var I,Q,q;var g=[];for(let G=0;G<((Q=(I=t==null?void 0:t.body)==null?void 0:I.list)==null?void 0:Q.length);G++){var n=t==null?void 0:t.body.list[G],P={id:we(),bankName:n==null?void 0:n.BankName,bankKey:n==null?void 0:n.BankKey,bankNumber:n==null?void 0:n.BankNumber,bankBranch:n==null?void 0:n.BankBranch,bankCountry:n==null?void 0:n.BankCtry,region:n==null?void 0:n.Region,street:n==null?void 0:n.Street,city:n==null?void 0:n.City};g.push(P)}ge(G=>[...G,...g]),M(!1),$(g.length),J((q=t==null?void 0:t.body)==null?void 0:q.count)},c=t=>{console.log(t)};O(`/${re}/data/getBankKeysBasedOnAdditionalParams`,"post",l,c,o)};r.useEffect(()=>{x*T>=(y==null?void 0:y.length)&&K()},[x,T]);const L=o=>{o.map(l=>y.find(c=>c.id===l))},Ae=o=>{const l=o.target.value;ne(l),k(0)},Fe=(o,l)=>{k(isNaN(l)?0:l)},We=()=>{F(!0),Qe()},Je=()=>{F(!0),k(0)},Qe=()=>{k(0),M(!0)},qe=()=>{_()},Ze=()=>{D(Mt()),D(Tt()),D(It()),D(mt([])),D(xt({})),D(kt({}))};return r.useEffect(()=>{H&&(_(),z(!1))},[H]),r.useEffect(()=>(Ze(),ie([le]),()=>{D(Ve({module:"BankKey",days:7}))}),[]),r.useEffect(()=>te([...y]),[y]),r.useEffect(()=>{var o,l,c,i;if(R){const t=C((l=(o=R==null?void 0:R.result)==null?void 0:o[0])==null?void 0:l.MDG_MAT_SEARCHSCREEN_COLUMN_ACTION_TYPE);_e(t)}if(Y){const t=(i=(c=Y==null?void 0:Y.result)==null?void 0:c[0])==null?void 0:i.MDG_MAT_SEARCHSCREEN_PARAMETER_ACTION_TYPE,g=t==null?void 0:t.filter(n=>n.MDG_MAT_FILTER_TYPE==="Additional").map(n=>({title:B(n.MDG_MAT_UI_FIELD_NAME)}));Ne(t),se(g)}},[R,Y]),r.useEffect(()=>{_()},[T]),r.useEffect(()=>{s(),u()},[]),r.useEffect(()=>{Se||x!=0&&x*T>=(y==null?void 0:y.length)&&K()},[x,T]),a(Ge,{children:a("div",{style:{..._t,backgroundColor:"#FAFCFF"},children:p(Re,{spacing:1,children:[p(E,{container:!0,mt:0,sx:Nt,children:[p(E,{item:!0,md:5,children:[a(v,{variant:"h3",children:a("strong",{children:B("Bank Key")})}),a(v,{variant:"body2",color:"#777",children:B("This view displays the Bank Key")})]}),a(Ot,{searchParameters:ae,onSearch:_,onClear:()=>z(!0),filterFieldData:fe,setFilterFieldData:pe,items:Ee})]}),a(E,{item:!0,sx:{position:"relative"},children:a(Re,{children:a(St,{isLoading:m,paginationLoading:m,module:"Bank Key",width:"100%",title:B("List of Bank Key")+" ("+W+")",rows:he??[],columns:Ce??[],showSearch:!0,showRefresh:!0,showSelectedCount:!0,showExport:!0,onSearch:o=>h(o),onRefresh:qe,pageSize:T,page:x,onPageSizeChange:Ae,rowCount:W??(y==null?void 0:y.length)??0,onPageChange:Fe,getRowIdValue:"id",hideFooter:!0,checkboxSelection:!0,disableSelectionOnClick:!0,status_onRowSingleClick:!0,tempheight:"calc(100vh - 320px)",onRowsSelectionHandler:L,callback_onRowSingleClick:o=>{const l=o.row.BankKey;de(!0),w(`/masterDataCockpit/bankKey/displayBankKeySAPData/${l}`,{state:o.row})},showCustomNavigation:!0,stopPropagation_Column:"action",status_onRowDoubleClick:!0,showFirstPageoptions:!0,showSelectAllOptions:!0,onSelectAllOptions:We,onSelectFirstPageOptions:Je})})}),a(Et,{sx:{position:"fixed",bottom:0,left:0,right:0},elevation:2,children:a(At,{className:"container_BottomNav",showLabels:!0,sx:{display:"flex",justifyContent:"flex-end",gap:1},value:ue,onChange:o=>{V(o)},children:a(He,{size:"small",variant:"contained",onClick:()=>{var o;w(`/${(o=Le)==null?void 0:o.CREATE_BK}`),D(Dt())},className:"createRequestButtonBK",children:B("Create Request")})})})]})})})};export{Jt as default};
