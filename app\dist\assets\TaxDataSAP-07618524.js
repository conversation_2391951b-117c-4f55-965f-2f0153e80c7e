import{n as f,r as b,aW as u,ae as T,j as a,d as h,c as t,Z as d,V as D,A as W,bl as y,ag as E,bm as _,bn as k,bo as p,bp as i,bq as R,i as v,k as w}from"./index-f7d9b065.js";const N=({materialID:g})=>{var l,c;const e=f(r=>{var n,o,s,x;return(x=(s=(o=(n=r.payload[g])==null?void 0:n.payloadData)==null?void 0:o.TaxData)==null?void 0:s.TaxData)==null?void 0:x.UniqueTaxDataSet}),[A,m]=b.useState((l=u)==null?void 0:l.TAXDATA_LOADING);return b.useEffect(()=>{let r;return(e==null?void 0:e.length)===0&&(r=setTimeout(()=>{m(T.NO_DATA_AVAILABLE)},500)),()=>{r&&clearTimeout(r)}},[e]),!e||e.length===0?a(h,{sx:{textAlign:"center",marginTop:"10px"},children:A}):t(w,{sx:{marginBottom:"20px",boxShadow:3,borderRadius:"10px",borderColor:(c=d)==null?void 0:c.primary.white},defaultExpanded:!0,children:[a(W,{expandIcon:a(D,{}),sx:{backgroundColor:d.primary.whiteSmoke,borderRadius:"10px",padding:"8px 16px","&:hover":{backgroundColor:d.hover.hoverbg}},children:a(h,{variant:"h6",sx:{fontWeight:"bold"},children:"Tax Classification"})}),a(v,{children:t(y,{component:E,sx:{maxWidth:"100%"},children:[a(h,{variant:"h6",sx:{p:1,fontWeight:"bold",textAlign:"center"},children:"Tax Data"}),t(_,{children:[a(k,{children:t(p,{sx:{backgroundColor:d.primary.whiteSmoke},children:[a(i,{sx:{fontWeight:"bold"},children:"Country"}),a(i,{sx:{fontWeight:"bold"},children:"Tax Type"}),a(i,{sx:{fontWeight:"bold"},children:"Tax Class"}),a(i,{sx:{fontWeight:"bold"},children:"Description"})]})}),a(R,{children:e.map(({Country:r,TaxType:n,SelectedTaxClass:o},s)=>t(p,{children:[a(i,{sx:{fontWeight:"bold"},children:r}),a(i,{sx:{fontWeight:"bold"},children:n}),a(i,{children:(o==null?void 0:o.TaxClass)||"N/A"}),a(i,{children:(o==null?void 0:o.TaxClassDesc)||"N/A"})]},`${r}-${n}-${s}`))})]})]})})]},"Tax_Classification_Static")};export{N as T};
