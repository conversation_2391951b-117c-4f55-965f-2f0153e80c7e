import{q6 as _,x0 as $,cb as e,x1 as ee,qa as R,r as N,qx as M,q2 as le,qu as X,wo as ne,qc as H,qf as D,qh as O,qe as P,qd as Q,x2 as oe,x3 as ie,x4 as Z}from"./index-f7d9b065.js";import{e as Y}from"./TableContainer-1e78f44b.js";import{c as re}from"./CircularProgress-8950bd0a.js";const te=_($)({borderRadius:"0.25rem",boxShadow:"var(--shadow-1)",display:"flex",overflow:"auto",flexDirection:"column",justifyContent:"center",alignItems:"flex-start",alignSelf:"stretch",fontFamily:"inherit"}),ae=function({open:c,...n}){return e.jsx(te,{open:c,...n})};const se="_customMenuItem_y78mz_25",de={customMenuItem:se},U=({children:c,classes:n,component:k,dense:l=!1,disableGutters:x=!1,divider:m=!1,focusVisibleClassName:C,selected:g=!1,sx:y,...b})=>e.jsx(ee,{classes:n,component:k,dense:l,disableGutters:x,divider:m,focusVisibleClassName:C,selected:g,sx:{display:"flex",padding:"0.75rem 0.5rem",alignItems:"center",gap:"0.625rem",alignSelf:"stretch",color:"var(--text-primary)",overflow:"hidden",textOverflow:"ellipsis",fontSize:"0.875rem",fontFamily:"inherit",fontWeight:"400",lineHeight:"normal",letterSpacing:"0.01094rem","&:hover":{backgroundColor:"var(--background-read-only)"},"& .Mui-selected":{backgroundColor:"var(--primary-light)"},...y},...b,className:de.customMenuItem,children:c}),ce=c=>e.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"none",...c,children:e.jsx("g",{id:"Icons /General",children:e.jsx("path",{d:"M17.5 12.5V15.8333C17.5 16.2754 17.3244 16.6993 17.0118 17.0118C16.6993 17.3244 16.2754 17.5 15.8333 17.5H4.16667C3.72464 17.5 3.30072 17.3244 2.98816 17.0118C2.67559 16.6993 2.5 16.2754 2.5 15.8333V12.5M5.83333 8.33333L10 12.5M10 12.5L14.1667 8.33333M10 12.5V2.5",stroke:"currentColor",strokeWidth:"1.25",strokeLinecap:"round",strokeLinejoin:"round"})})}),he=R(ce),ue=c=>e.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"none",...c,children:e.jsx("g",{id:"Icons /General",children:e.jsx("path",{d:"M10.0001 13.3333V17.5M13.3334 11.6667V17.5M16.6667 8.33333V17.5M18.3334 2.5L11.1284 9.705C11.0897 9.7438 11.0437 9.77459 10.9931 9.79559C10.9425 9.8166 10.8882 9.82741 10.8334 9.82741C10.7786 9.82741 10.7243 9.8166 10.6737 9.79559C10.6231 9.77459 10.5771 9.7438 10.5384 9.705L7.79508 6.96167C7.71694 6.88355 7.61098 6.83967 7.5005 6.83967C7.39001 6.83967 7.28405 6.88355 7.20591 6.96167L1.66675 12.5M3.33341 15V17.5M6.66675 11.6667V17.5",stroke:"currentColor",strokeWidth:"1.25",strokeLinecap:"round",strokeLinejoin:"round"})})}),ve=R(ue),Ce=c=>e.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"none",...c,children:e.jsx("g",{id:"Icons /General",children:e.jsx("path",{d:"M2.5 7.5H17.5M2.5 12.5H17.5M7.5 7.5V17.5M12.5 7.5V17.5M4.16667 2.5H15.8333C16.7538 2.5 17.5 3.24619 17.5 4.16667V15.8333C17.5 16.7538 16.7538 17.5 15.8333 17.5H4.16667C3.24619 17.5 2.5 16.7538 2.5 15.8333V4.16667C2.5 3.24619 3.24619 2.5 4.16667 2.5Z",stroke:"currentColor",strokeWidth:"1.25",strokeLinecap:"round",strokeLinejoin:"round"})})}),pe=R(Ce),xe="data:image/png;base64,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",B={MIXED:{chartKey:"line",stacked:!1,horizontal:!1,library:"APEX",isEnabled:!1,nonConvertible:["STACK_BAR","STACK_COLUMN","PIE","DONUT","STACK_AREA","STACK_LINE"]},BAR:{chartKey:"bar",stacked:!1,horizontal:!0,library:"APEX",isEnabled:!0,nonConvertible:["STACK_BAR","STACK_COLUMN","PIE","DONUT","STACK_AREA","STACK_LINE"]},COLUMN:{chartKey:"bar",stacked:!1,horizontal:!1,library:"APEX",isEnabled:!0,nonConvertible:["STACK_BAR","STACK_COLUMN","PIE","DONUT","STACK_AREA","STACK_LINE"]},LINE:{chartKey:"line",stacked:!1,horizontal:!1,library:"APEX",isEnabled:!0,nonConvertible:["STACK_BAR","STACK_COLUMN","PIE","DONUT","STACK_AREA","STACK_LINE"]},AREA:{chartKey:"area",stacked:!1,horizontal:!1,library:"APEX",isEnabled:!0,nonConvertible:["STACK_BAR","STACK_COLUMN","PIE","DONUT","STACK_AREA","STACK_LINE"]},STACK_COLUMN:{chartKey:"bar",stacked:!0,horizontal:!1,library:"APEX",isEnabled:!0,nonConvertible:["LINE","AREA","COLUMN","BAR","PIE","DONUT"]},STACK_BAR:{chartKey:"bar",stacked:!0,horizontal:!0,library:"APEX",isEnabled:!0,nonConvertible:["LINE","AREA","COLUMN","BAR","PIE","DONUT"]},STACK_AREA:{chartKey:"area",stacked:!0,horizontal:!1,library:"APEX",isEnabled:!0,nonConvertible:["LINE","AREA","COLUMN","BAR","PIE","DONUT"]},STACK_LINE:{chartKey:"line",stacked:!0,horizontal:!1,library:"APEX",isEnabled:!0,nonConvertible:["LINE","AREA","COLUMN","BAR","PIE","DONUT"]},DONUT:{chartKey:"donut",stacked:null,horizontal:null,library:"APEX",isEnabled:!0,nonConvertible:["STACK_BAR","STACK_COLUMN","STACK_AREA","STACK_LINE","LINE","AREA","COLUMN","BAR"]},PIE:{chartKey:"pie",stacked:null,horizontal:null,library:"APEX",isEnabled:!0,nonConvertible:["STACK_BAR","STACK_COLUMN","STACK_AREA","STACK_LINE","LINE","AREA","COLUMN","BAR"]}},V=["#7f86ca","#f1a957","#8bc882","#61b0ee","#fd78b6","#ceae9f","#cb7ad5","#ff826f","#f2c276"];function Ae(c,n,k,l){var x,m,C,g,y,b,d,w,j,I;const t=structuredClone(n==null?void 0:n.data);(t==null?void 0:t.length)===1&&!((x=t==null?void 0:t[0])!=null&&x.name)&&(t[0].name=((m=n==null?void 0:n.graphDetails)==null?void 0:m.yTitle)||"");let A=!1,f=!1;const a=(C=B)==null?void 0:C[c],h={chart:{type:a==null?void 0:a.chartKey,stacked:(a==null?void 0:a.stacked)??!1,fontFamily:"Roboto",width:"400px",height:"400px",toolbar:{show:!1},animations:{enabled:!0,speed:800,dynamicAnimation:{enabled:!0,speed:600}},events:{zoomed:function(){A=!0},click:function(T,E,i){var L,z,u;if(A)return A=!1,!1;if(f)return f=!1,!1;if(l!=null&&l.onClick){const v=(z=(L=i==null?void 0:i.config)==null?void 0:L.series)==null?void 0:z[i==null?void 0:i.seriesIndex],K=(u=v==null?void 0:v.data)==null?void 0:u[i==null?void 0:i.dataPointIndex];v&&K&&(l==null||l.onClick({selectedDataPoint:K,name:v==null?void 0:v.name,type:v==null?void 0:v.type,chartKey:c,dataPointIndex:i==null?void 0:i.dataPointIndex,seriesIndex:i==null?void 0:i.seriesIndex,values:n},T,E,i))}},dataPointSelection:function(T,E,i){var L,z;if(l!=null&&l.onClick){const u=(L=i==null?void 0:i.series)==null?void 0:L[i==null?void 0:i.seriesIndex],v=(z=u==null?void 0:u.data)==null?void 0:z[i==null?void 0:i.dataPointIndex];u&&v&&(f=!0,l==null||l.onClick({selectedDataPoint:v,name:u==null?void 0:u.name,type:u==null?void 0:u.type,chartKey:c,dataPointIndex:i==null?void 0:i.dataPointIndex,seriesIndex:i==null?void 0:i.seriesIndex,values:n},T,E,i))}}}},fill:{opacity:1,type:"solid"},legend:{show:!0,position:"bottom",fontSize:"10px",fontFamily:"Roboto",fontWeight:400,height:40,labels:{colors:"var(--text-secondary)"},markers:{size:5,strokeWidth:0,shape:"circle"}},plotOptions:{bar:{horizontal:(a==null?void 0:a.horizontal)||!1,borderRadius:1,columnWidth:"40%",borderRadiusApplication:"end",borderRadiusWhenStacked:"last",distributed:!(a!=null&&a.stacked||((g=n==null?void 0:n.data)==null?void 0:g.length)>1)}},xaxis:{type:"category",title:{text:a!=null&&a.horizontal?(y=n==null?void 0:n.graphDetails)==null?void 0:y.yTitle:((b=n==null?void 0:n.graphDetails)==null?void 0:b.xTitle)||((d=n==null?void 0:n.graphDetails)==null?void 0:d.xTitle),offsetY:(a==null?void 0:a.chartKey)==="bar"&&a!=null&&a.horizontal?24:6,style:{fontSize:"0.75rem",fontWeight:400,color:"var(--text-primary)"}},axisBorder:{show:!1},axisTicks:{show:!0,height:10,color:"var(--divider-secondary)"},labels:{show:!0,offsetY:0,rotate:-45,rotateAlways:!1,hideOverlappingLabels:!0,trim:!0,style:{fontSize:"0.625rem",fontWeight:400,colors:"var(--text-secondary)",fontFamily:"Roboto"}}},yaxis:{title:{text:a!=null&&a.horizontal?(w=n==null?void 0:n.graphDetails)==null?void 0:w.xTitle:((j=n==null?void 0:n.graphDetails)==null?void 0:j.yTitle)||((I=n==null?void 0:n.graphDetails)==null?void 0:I.yTitle),style:{fontSize:"0.75rem",fontWeight:400,color:"var(--text-primary)"}},labels:{style:{fontSize:"0.625rem",fontWeight:400,colors:"var(--text-secondary)",fontFamily:"Roboto"}},axisBorder:{show:!0,color:"var(--divider-secondary)"},axisTicks:{show:!0,width:6,color:"var(--divider-secondary)"}},colors:k??V,dataLabels:{enabled:!1},tooltip:{enabled:!0,followCursor:!0,marker:{show:!0}},stroke:{show:!0,width:3,lineCap:"square",curve:"smooth"},grid:{show:!0,borderColor:"var(--divider-secondary)",strokeDashArray:0,position:"back",xaxis:{lines:{show:!0}},yaxis:{lines:{show:!0}}}};return{series:t||[],options:h}}function fe(c,n,k,l){var x,m;const C=(x=B)==null?void 0:x[c],g={chart:{type:C==null?void 0:C.chartKey,toolbar:{show:!1},fontFamily:"Roboto",events:{dataPointSelection:function(y,b,d){var w,j,I,t,A,f,a;if(l!=null&&l.onClick){const h=(I=(j=(w=d==null?void 0:d.w)==null?void 0:w.config)==null?void 0:j.labels)==null?void 0:I[d==null?void 0:d.dataPointIndex],T=(f=(A=(t=d==null?void 0:d.w)==null?void 0:t.config)==null?void 0:A.series)==null?void 0:f[d==null?void 0:d.dataPointIndex];h&&T&&(l==null||l.onClick({selectedDataPoint:{x:h,y:T,id:(a=n.id)==null?void 0:a[d==null?void 0:d.dataPointIndex]},name:h,type:C==null?void 0:C.chartKey,chartKey:c,dataPointIndex:d==null?void 0:d.dataPointIndex,seriesIndex:d==null?void 0:d.seriesIndex,values:n},y,b,d))}}}},legend:{show:!0,position:"bottom",fontSize:"10px",fontFamily:"Roboto",fontWeight:400,height:40,labels:{colors:"var(--text-secondary)"},markers:{size:5,strokeWidth:0,shape:"circle"}},plotOptions:{pie:{startAngle:0,endAngle:360,expandOnClick:!0,offsetX:0,offsetY:0,customScale:1,dataLabels:{offset:0,minAngleToShowLabel:20},donut:{size:"50%"}}},tooltip:{enabled:!0,followCursor:!0,marker:{show:!0}},dataLabels:{enabled:!1},labels:(n==null?void 0:n.label)||[],colors:k??V};return{series:((m=n==null?void 0:n.series)==null?void 0:m.map(Number))||(n==null?void 0:n.series)||[],options:g}}function Ee({showDownload:c=!1,showGraphName:n=!1,graphColor:k=V,values:l,isLoading:x=!1,error:m=!1,isTable:C=!1}){var g,y,b,d,w,j,I;const t=(g=l==null?void 0:l.graphDetails)==null?void 0:g.chartType,A=N.useRef(null),f=N.useRef(null),a=o=>{var r,s,p;let S;return t==="PIE"||t==="DONUT"?S=fe(t,o,k):S=Ae(t,o,k),(r=A.current)!=null&&r.chart&&(A.current.chart.updateOptions((s=h==null?void 0:h.graphData)==null?void 0:s.options),A.current.chart.updateSeries((p=h==null?void 0:h.graphData)==null?void 0:p.series)),S},[h,T]=N.useState({chartKey:(b=(y=B)==null?void 0:y[t])==null?void 0:b.chartKey,graphData:a(l)}),[E,i]=N.useState("graph"),[L,z]=N.useState(null);N.useEffect(()=>{T(o=>{var r,s;return{...o,chartKey:(s=(r=B)==null?void 0:r[t])==null?void 0:s.chartKey,graphData:a(l)}})},[x,l]);const u=()=>i(E==="graph"?"table":"graph"),v=o=>z(o.currentTarget),K=()=>z(null),q=!!L,G=o=>{f.current&&(o==="png"?oe:ie)(f.current,{backgroundColor:"white",cacheBust:!0}).then(r=>{var s;return Z.saveAs(r,`${(s=l==null?void 0:l.graphDetails)==null?void 0:s.graphName}.${o}`)}).catch(r=>console.error(`Error generating ${o}:`,r))},F=()=>{var o;let r="";return t==="PIE"||t==="DONUT"?(r=`Measure,Value
`,l==null||l.series.forEach((s,p)=>{r+=`${l==null?void 0:l.label[p]},${s}
`})):(r="Categories,"+(l==null?void 0:l.data.map(s=>s.name).join(","))+`
`,(o=l==null?void 0:l.data[0])==null||o.data.forEach((s,p)=>{r+=s.x+","+(l==null?void 0:l.data.map(S=>{var W;return(W=S.data[p])==null?void 0:W.y}).join(","))+`
`})),r},J=()=>{var o;const r=F(),s=new Blob([r],{type:"text/csv;charset=utf-8;"});Z.saveAs(s,`${(o=l==null?void 0:l.graphDetails)==null?void 0:o.graphName}.csv`)};return e.jsxs(M,{sx:{maxWidth:"100%",maxHeight:"100%",margin:"1rem"},children:[e.jsxs(M,{sx:{display:"flex",flexDirection:"row",justifyContent:"space-between",alignItems:"center",margin:"0rem 1rem"},children:[n?e.jsx(le,{variant:"subtitle2",children:(d=l==null?void 0:l.graphDetails)==null?void 0:d.graphName}):null,e.jsxs("div",{style:{justifyContent:"center",display:"flex",flexDirection:"row",alignItems:"center"},children:[C&&e.jsx(X,{onClick:u,"aria-label":E==="graph"?"Switch to Table View":"Switch to Graph View",children:E==="graph"?e.jsx(pe,{size:"xsmall"}):e.jsx(ve,{size:"xsmall"})}),c?e.jsxs("div",{children:[e.jsx(X,{"aria-label":"download",onClick:v,children:e.jsx(he,{size:"xsmall"})}),e.jsxs(ae,{anchorEl:L,open:q,onClose:K,children:[E==="graph"?e.jsxs(e.Fragment,{children:[e.jsx(U,{sx:{width:"5rem"},onClick:()=>G("svg"),children:"SVG"}),e.jsx(U,{sx:{width:"5rem"},onClick:()=>G("png"),children:"PNG"})]}):null,e.jsx(U,{sx:{width:"5rem"},onClick:J,children:"CSV"})]})]}):null]})]}),x?e.jsx(M,{sx:{alignItems:"center",height:"400px",justifyContent:"space-evenly"},children:e.jsx(re,{})}):m?e.jsx(M,{sx:{alignItems:"center",height:"400px",justifyContent:"space-evenly"},children:e.jsx("img",{src:xe,style:{margin:"6rem"},alt:"Graph failed to load",height:"200px",width:"200px"})}):E==="graph"?e.jsx("div",{ref:f,children:e.jsx(ne,{options:(w=h==null?void 0:h.graphData)==null?void 0:w.options,series:(j=h==null?void 0:h.graphData)==null?void 0:j.series,type:h==null?void 0:h.chartKey,height:t==="PIE"||t==="DONUT"?415:400})}):t==="PIE"||t==="DONUT"?e.jsx(Y,{sx:{height:"415px",overflow:"auto"},children:e.jsxs(H,{size:"small",children:[e.jsx(D,{children:e.jsxs(O,{children:[e.jsx(P,{children:"Measure"}),e.jsx(P,{children:"Value"})]})}),e.jsx(Q,{children:l==null?void 0:l.series.map((o,r)=>{var s;const p=(s=l==null?void 0:l.label)==null?void 0:s[r];return e.jsxs(O,{children:[e.jsx(P,{children:p}),e.jsx(P,{children:typeof o=="object"?JSON.stringify(o):o})]},p||`row-${r}`)})})]})}):e.jsx(Y,{sx:{height:"415px",overflow:"auto"},children:e.jsxs(H,{size:"small",children:[e.jsx(D,{children:e.jsxs(O,{children:[e.jsx(P,{children:"Categories"}),l==null?void 0:l.data.map(o=>e.jsx(P,{children:o.name},o.name))]})}),e.jsx(Q,{children:(I=l==null?void 0:l.data[0])==null?void 0:I.data.map(o=>e.jsxs(O,{children:[e.jsx(P,{children:o.x}),l==null?void 0:l.data.map(r=>{var s;return e.jsx(P,{children:(s=r.data.find(p=>p.x===o.x))==null?void 0:s.y},`${r.name}-${o.x}`)})]},o.x))})]})})]})}const me=c=>e.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"none",...c,children:e.jsxs("g",{id:"Icons /General",children:[e.jsx("path",{d:"M7.62109 2.5H3.45443C2.99419 2.5 2.62109 2.8731 2.62109 3.33333V9.16667C2.62109 9.6269 2.99419 10 3.45443 10H7.62109C8.08133 10 8.45443 9.6269 8.45443 9.16667V3.33333C8.45443 2.8731 8.08133 2.5 7.62109 2.5Z",stroke:"currentColor",strokeWidth:"1.25",strokeLinecap:"round",strokeLinejoin:"round"}),e.jsx("path",{d:"M16.7871 2.5H12.6204C12.1602 2.5 11.7871 2.8731 11.7871 3.33333V5.83333C11.7871 6.29357 12.1602 6.66667 12.6204 6.66667H16.7871C17.2473 6.66667 17.6204 6.29357 17.6204 5.83333V3.33333C17.6204 2.8731 17.2473 2.5 16.7871 2.5Z",stroke:"currentColor",strokeWidth:"1.25",strokeLinecap:"round",strokeLinejoin:"round"}),e.jsx("path",{d:"M11.3691 14.0996V13.4004C11.3691 12.9872 11.6877 12.6455 12.0813 12.6455C12.7597 12.6455 13.0371 12.137 12.696 11.5132C12.5011 11.1557 12.6173 10.6909 12.9583 10.4843L13.6068 10.0909C13.9029 9.90421 14.2852 10.0155 14.4613 10.3293L14.5026 10.4048C14.8399 11.0285 15.3946 11.0285 15.7357 10.4048L15.7769 10.3293C15.9531 10.0155 16.3354 9.90421 16.6315 10.0909L17.2799 10.4843C17.621 10.6909 17.7372 11.1557 17.5423 11.5132C17.2012 12.137 17.4786 12.6455 18.157 12.6455C18.5468 12.6455 18.8691 12.9832 18.8691 13.4004V14.0996C18.8691 14.5128 18.5506 14.8545 18.157 14.8545C17.4786 14.8545 17.2012 15.363 17.5423 15.9868C17.7372 16.3483 17.621 16.8091 17.2799 17.0157L16.6315 17.4091C16.3354 17.5958 15.9531 17.4845 15.7769 17.1707L15.7357 17.0952C15.3984 16.4715 14.8437 16.4715 14.5026 17.0952L14.4613 17.1707C14.2852 17.4845 13.9029 17.5958 13.6068 17.4091L12.9583 17.0157C12.6173 16.8091 12.5011 16.3443 12.696 15.9868C13.0371 15.363 12.7597 14.8545 12.0813 14.8545C11.6877 14.8545 11.3691 14.5128 11.3691 14.0996Z",stroke:"currentColor",strokeWidth:"1.25",strokeMiterlimit:"10",strokeLinecap:"round",strokeLinejoin:"round"}),e.jsx("path",{d:"M15.7441 13.75C15.7441 14.0952 15.4643 14.375 15.1191 14.375C14.774 14.375 14.4941 14.0952 14.4941 13.75C14.4941 13.4048 14.774 13.125 15.1191 13.125C15.4643 13.125 15.7441 13.4048 15.7441 13.75Z",stroke:"currentColor",strokeWidth:"1.25",strokeMiterlimit:"10",strokeLinecap:"round",strokeLinejoin:"round"}),e.jsx("path",{d:"M7.62109 13.3333H3.45443C2.99419 13.3333 2.62109 13.7063 2.62109 14.1666V16.6666C2.62109 17.1268 2.99419 17.4999 3.45443 17.4999H7.62109C8.08133 17.4999 8.45443 17.1268 8.45443 16.6666V14.1666C8.45443 13.7063 8.08133 13.3333 7.62109 13.3333Z",stroke:"currentColor",strokeWidth:"1.25",strokeLinecap:"round",strokeLinejoin:"round"})]})}),ke=R(me);export{xe as A,Ee as B,pe as a,he as b,ke as c,U as h,ve as i,ae as l,de as s};
