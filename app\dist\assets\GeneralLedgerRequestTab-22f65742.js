import{r,j as t,aR as Hs,g as Gn,s as tn,n as O,aP as _n,u as kn,aO as Xt,C as A,cd as N,a8 as fs,a4 as Cs,aa as Ho,Z as Jo,a9 as vo,zQ as js,zR as qn,zS as Xs,e_ as Dt,zT as zs,c as x,aF as Rn,F as wo,ai as mo,B as ae,d as Je,al as ro,an as ke,aX as re,am as co,aG as Cn,af as Un,ag as Dn,bf as Pn,eY as os,zU as zn,zV as ms,zW as ys,cq as Ss,fH as Ro,bK as Mt,df as ue,aT as So,fP as Q,fQ as Z,aM as As,ae as Yn,zX as Vs,d0 as Gs,bI as mn,da as Kn,zs as Es,bJ as Ys,dG as Ks,xi as Qs,a as Hn,eS as it,O as Tt,b1 as Zs,aZ as Js,aD as ws,zY as Po,z9 as er,ap as _s,zZ as tr,cz as or,zb as Nn,aY as nr,b as sr,z_ as rr,z$ as ks,A0 as yo,a6 as hn,T as Ln,aj as jo,$ as cr,bP as ar,bQ as bs,bG as xs,b3 as Qn,bE as Zn,gq as pn,bl as dr,bm as lr,bn as ir,bo as Ns,bp as vn,bq as ur,L as Ms,gr as qs,h as vs,f as Bs,b6 as Ls,b5 as Ds,A1 as On,A2 as en,xH as gr,A3 as hr,A4 as Tn,dX as to,aK as fn,de as Jn,A5 as In,fF as Mo,A6 as pr,A7 as Tr,aJ as Oe,z as Wn,A8 as wn,fG as Fo,bU as Vn,A9 as $s,Aa as fr,y3 as Cr,bc as mr,cA as yr,xY as Sr,ce as Ar,x_ as Er,zn as br,Ab as xr,d3 as Nr,d4 as Lr,d5 as Dr,cI as xn,d6 as Ir,d7 as Or,d8 as Gr,cH as _r,a$ as kr,d9 as Mr,fY as qr,br as vr,ad as Br,Ac as $r,Ad as Ur}from"./index-f7d9b065.js";import{d as Fn}from"./FeedOutlined-41109ec9.js";import{u as ns,E as Wr}from"./ErrorReportDialog-cb66d1ed.js";import{S as ss,D as es,B as rs,A as Fr,P as Rr}from"./PreviewPage-634057fa.js";import{F as qo}from"./FilterChangeDropdown-22e24089.js";import{u as cs,g as as,F as Pr}from"./FilterFieldGlobal-f8e8f75f.js";import{O as Hr,C as jr}from"./ChangeLogGL-8c860c66.js";import{E as Xr}from"./ExcelOperationsCard-49e9ffd2.js";import{d as zr}from"./PermIdentityOutlined-0746a749.js";import{S as No}from"./SingleSelectDropdown-aee403d4.js";import{d as ts}from"./Description-ab582559.js";import{u as Us}from"./useGeneralLedgerFieldConfig-900ee175.js";import{d as Vr}from"./TaskAlt-0afc1812.js";import{d as Bn,a as $n}from"./CloseFullscreen-2870eb3e.js";import{G as Is}from"./GenericTabsGlobal-613ace00.js";import{d as Yr}from"./TrackChangesTwoTone-7a2ab513.js";import{d as Kr}from"./FileUploadOutlined-4a68a28a.js";import"./lz-string-0665f106.js";import"./ErrorHistory-ef441d1f.js";import"./CheckCircleOutline-e186af3e.js";import"./AttachFile-8d552da8.js";import"./UtilDoc-6f590135.js";import"./FileDownloadOutlined-59854a55.js";import"./VisibilityOutlined-b6cd6d28.js";import"./DeleteOutlined-e668453f.js";import"./CloudUpload-0ba6431e.js";import"./utilityImages-067c3dc2.js";import"./Delete-5278579a.js";import"./ReusablePromptBox-e1871d49.js";import"./featureConfig-652a9f8d.js";import"./DataObject-52409c14.js";import"./Download-52c4427b.js";import"./useFinanceCostingRows-ffbb569f.js";import"./AdapterDayjs-2a9281df.js";import"./advancedFormat-4f6292d4.js";import"./customParseFormat-1bc1aa07.js";import"./isBetween-c1c6beb9.js";import"./DatePicker-a8e9bd4a.js";import"./useMobilePicker-9b56b5b6.js";import"./CSSTransition-cd337b47.js";import"./CloudDownload-9a7605e9.js";import"./AttachmentUploadDialog-43cc9099.js";const Os=r.forwardRef(function(dt,We){return t(Hs,{direction:"down",ref:We,...dt})}),Ws=({reqBench:Ue,requestId:dt,apiResponses:We,downloadClicked:st,setDownloadClicked:m,moduleName:ie,setIsAttachmentTabEnabled:C,isDisabled:Ke})=>{var sn,po,Xo,To,zo;const q=Gn(),w=tn(),{fetchedGeneralLedgerData:T,originalGLData:ut,fetchReqBenchData:Ge,originalReqBenchData:Et,changedFieldsMap:Pe}=O(o=>o.generalLedger),W=O(o=>o.generalLedger.payload.requestHeaderData),{updateChangeLogGlForChange:gt}=cs(),P=O(o=>o.request.requestHeader),It=O(o=>o.payload.filteredButtons),zt=(Ge==null?void 0:Ge.reqStatus)==="Validated-Requestor",qt=O(o=>o.commonFilter.GeneralLedger),{customError:de}=_n(),qe=O(o=>o==null?void 0:o.userManagement.taskData),rt=kn(),Ft=new URLSearchParams(rt.search),Qe=Ft.get("RequestId"),ft=Ft.get("reqBench");Ft.get("RequestId");const Ot=as(Pn.MODULE,!0,{}),Lo=os[Ot]||(()=>({}));O(o=>o.request.requestHeader);const Ee=O(o=>o==null?void 0:o.changeLog.createChangeLogDataGL),Xe=O(Lo),[ao,oo]=r.useState(!0),[Ze,Vt]=r.useState("");r.useState("");const[Yt,Ao]=r.useState(null),[j,Do]=r.useState([]),[ze,Eo]=r.useState(!1),[he,Gt]=r.useState([]),[Ct,Kt]=r.useState([]),[G,vt]=r.useState([]),[ne,He]=r.useState([]),[k,Qt]=r.useState(!1),[lo,mt]=r.useState(!1),[bo,no]=r.useState(""),[io,ct]=r.useState(""),[ye,we]=r.useState(""),[le,Bt]=r.useState("systemGenerated");r.useState([]),r.useState(""),r.useState([]),r.useState([]),r.useState([]);const[lt,Zt]=r.useState([]),[$t,Ve]=r.useState(!1),[ht,B]=r.useState(""),[Fe,me]=r.useState("success"),[ee,ve]=r.useState(!1),[be,Le]=r.useState([]),[xe,et]=r.useState(!1),[uo,Io]=r.useState([]),[bt,Jt]=r.useState(!1),{getButtonsDisplayGlobal:on}=ns(),_t=O(o=>{var l;return((l=o==null?void 0:o.payload)==null?void 0:l.changeFieldSelectiondata)||[]}),se=["Taxcategory","Sortkey","HouseBank","AccountId","ReconAcc"],[go,yt]=r.useState(!1),[Re,Me]=r.useState({title:"",message:"",subText:"",buttonText:"",redirectTo:""});r.useEffect(()=>{(T==null?void 0:T.length)>0&&Ue!=="true"&&(yt(!0),C(!0))},[]),r.useEffect(()=>{Te()},[]),r.useEffect(()=>{var o;(qe!=null&&qe.ATTRIBUTE_1||Qe)&&on((o=Xt)==null?void 0:o.GL,"MDG_DYN_BTN_DT","v3")},[qe]);const ho=o=>{const l=E=>{Do(E.body),w(Dt({keyName:"CompanyCode",data:E.body}))},u=E=>{de(E)};A(`/${N}/data/getCompanyCode?chartAccount=${o}`,"get",l,u)},pt=()=>{const o=u=>{Le(u.body),w(Dt({keyName:"AccountType",data:u.body}))},l=u=>{de(u)};A(`/${N}/data/getGLAccountType`,"get",o,l)},De=(o,l)=>{const u=S=>{w(ue({keyName:"FieldStsGrp",data:S.body,keyName2:l}))},E=S=>{de(S)};A(`/${N}/data/getFieldStatusGroup?fieldStatusVariant=${o}`,"get",u,E)},wt=(o,l)=>{const u=S=>{w(ue({keyName:"Taxcategory",data:S.body||[],keyName2:l}))},E=S=>{de(S)};A(`/${N}/data/getTaxCategory?companyCode=${o}`,"get",u,E)},at=(o,l)=>{const u=S=>{w(ue({keyName:"House Bank",data:S.body||[],keyName2:l}))},E=S=>{de(S)};A(`/${N}/data/getHouseBank?companyCode=${o}`,"get",u,E)},X=(o,l)=>{const u=S=>{w(ue({keyName:"CostEleCategory",data:S.body||[],keyName2:l}))},E=S=>{de(S)};A(`/${N}/data/getCostElementCategory?accountType=${o}`,"get",u,E)},Be=o=>{const l=E=>{w(ue({keyName:"ReconAcc",data:E.body,keyName2:o}))},u=E=>{de(E)};A(`/${N}/data/getReconAccountForAccountType`,"get",l,u)},Ut=(o,l)=>{const u=S=>{w(ue({keyName:"Sortkey",data:S.body||[],keyName2:l}))},E=S=>{de(S)};A(`/${N}/data/getSortKey`,"get",u,E)},Te=()=>{const o=u=>{w(Dt({keyName:"COA",data:u.body}))},l=u=>{de(u)};A(`/${N}/data/getChartOfAccounts`,"get",o,l)},ge=()=>{oo(!1),m(!1),q("/requestbench")},xo=o=>{if(!ne.length)return;const l=ne==null?void 0:ne.map(S=>({glAccount:S==null?void 0:S.code,compCode:S==null?void 0:S.compCode,changedFieldsToCheck:P!=null&&P.fieldName?P==null?void 0:P.fieldName:W==null?void 0:W.FieldName})),u=S=>{if(!(S==null?void 0:S.some(z=>(z==null?void 0:z.statusCode)!==200)))o==="OK"?(oo(!1),Ue!=="true"&&(yt(!0),C(!0)),Se()):o==="Download"&&J();else{const z=S.filter(Ae=>Ae.statusCode===400);let Ne=[];z==null||z.forEach((Ae,fe)=>{var H,$o,Vo,Ht,rn,cn,Yo,an,Ko,Uo,dn;const At={id:`${(H=Ae==null?void 0:Ae.body)==null?void 0:H.glAccount}_${fe}`,objectNo:($o=Ae==null?void 0:Ae.body)==null?void 0:$o.glAccount,reqId:(rn=(Ht=(Vo=Ae==null?void 0:Ae.body)==null?void 0:Vo.matchingRequests)==null?void 0:Ht.map(Wt=>Wt==null?void 0:Wt.matchingRequestHeaderId))==null?void 0:rn.filter(Boolean),childReqId:(an=(Yo=(cn=Ae==null?void 0:Ae.body)==null?void 0:cn.matchingRequests)==null?void 0:Yo.map(Wt=>Wt==null?void 0:Wt.matchingChildHeaderIdsSet))==null?void 0:an.filter(Boolean),requestedBy:(dn=(Uo=(Ko=Ae==null?void 0:Ae.body)==null?void 0:Ko.matchingRequests)==null?void 0:Uo.map(Wt=>Wt==null?void 0:Wt.RequestCreatedBy))==null?void 0:dn.filter(Boolean)};Ne.push(At)}),Io(Ne),Jt(!0)}},E=S=>{de(S)};A(`/${N}/${So.DATA.GET_DUPLICATE_GL_REQUEST}`,"post",u,E,l)},eo=o=>{xo(o)},ot=[{field:"included",headerName:"Included",sortable:!1,filterable:!1,width:65,disableColumnMenu:!0,renderHeader:()=>{const o=Ue?Ge:T,l=o.every(E=>E.included),u=o.some(E=>E.included);return t(vo,{checked:l,indeterminate:!l&&u,onChange:E=>{const S=o.map(g=>({...g,included:E.target.checked}));w(Ue?qn(S):zn(S))}})},renderCell:o=>{var E;const l=Ue?Ge:T,u=l.findIndex(S=>S.id===o.row.id);return t(vo,{checked:((E=l[u])==null?void 0:E.included)||!1,onChange:S=>{const g=[...l];g[u]={...g[u],included:S.target.checked},w(Ue?qn(g):zn(g))}})}},{field:"lineNumber",headerName:"Sl No"},{field:"GLAccount",headerName:"General Ledger",width:150,editable:!1,renderCell:o=>t("span",{style:{color:"#9e9e9e",pointerEvents:"none",cursor:"default"},children:o.value||""})},{field:"CompanyCode",headerName:"Company Codes",width:150,editable:!1,renderCell:o=>t("span",{style:{color:"#9e9e9e",pointerEvents:"none",cursor:"default"},children:o.value||""})},{field:"Accounttype",headerName:"Account Type",width:150,editable:!1,renderCell:o=>t("span",{style:{color:"#9e9e9e",pointerEvents:"none",cursor:"default"},children:o.value||""})},{field:"AccountGroup",headerName:"Account Group",width:150,editable:!1,renderCell:o=>t("span",{style:{color:"#9e9e9e",pointerEvents:"none",cursor:"default"},children:o.value||""})},{field:"COA",headerName:"Chart Of Account",width:150,editable:!1},{field:"GLname",headerName:"Short Text",width:200,editable:!0},{field:"Description",headerName:"Long Text",width:200,editable:!0},{field:"Balanceinlocrcy",headerName:"Only Balance In Local Currency",width:250,editable:!0},{field:"Taxcategory",headerName:"Tax Category",width:250,editable:!0},{field:"Pstnwotax",headerName:"Posting Without Tax Allowed",width:250,editable:!0},{field:"ReconAcc",headerName:"Recon. Account For Account Type",width:150},{field:"Openitmmanage",headerName:"Open Item Management",width:150},{field:"OpenItemManagebyLedgerGrp",headerName:"Open Item Management By Ledger Group",width:150},{field:"Sortkey",headerName:"Sort Key",width:150,editable:!0},{field:"FieldStsGrp",headerName:"Field Status Group",width:150,editable:!0},{field:"PostAuto",headerName:"Post Automatically Only",width:150,editable:!0},{field:"HouseBank",headerName:"House Bank",width:150,editable:!0},{field:"AccountId",headerName:"Account Id",width:150,editable:!0},{field:"PostingBlockedCoCd",headerName:"Blocked For Posting Company Code",width:150,editable:!0},{field:"PostingBlockedCOA",headerName:"Blocked For Posting at COA",width:150,editable:!0},{field:"name2",headerName:"Name 2",width:150,editable:!0},{field:"name3",headerName:"Name 3",width:150,editable:!0},{field:"name4",headerName:"Name 4",width:150,editable:!0}],xt=(o,l,u)=>{var g;const E=o.target.value;gt({uniqueId:l.row.id,filedName:(g=l==null?void 0:l.colDef)==null?void 0:g.headerName,jsonName:l==null?void 0:l.field,currentValue:E,requestId:P==null?void 0:P.RequestId,childRequestId:dt,accountNumber:l.row.GLAccount});const S={...l.row,[l.field]:E};w(Ue?ms(S):ys(S))},y=o=>l=>{var E;const u=l.target.value.toUpperCase();o.api.setEditCellValue({id:o.id,field:o.field,value:u}),gt({uniqueId:o.row.id,filedName:(E=o==null?void 0:o.colDef)==null?void 0:E.headerName,jsonName:o==null?void 0:o.field,currentValue:u,requestId:P==null?void 0:P.RequestId,childRequestId:dt,accountNumber:o.row.GLAccount})},M=o=>l=>{var u;o.api.setEditCellValue({id:o.id,field:o.field,value:!o.value}),gt({uniqueId:o.row.id,filedName:(u=o==null?void 0:o.colDef)==null?void 0:u.headerName,jsonName:o==null?void 0:o.field,currentValue:!o.value,requestId:P==null?void 0:P.RequestId,childRequestId:dt,accountNumber:o.row.GLAccount})},$=(W==null?void 0:W.FieldName)||[],te=ot.slice(0,6),K=ot.slice(5).filter(o=>$==null?void 0:$.includes(o.headerName)).map(o=>se!=null&&se.includes(o==null?void 0:o.field)?{...o,editable:!1,renderCell:l=>{var E,S;const u=l.value||"";return t(Cs,{value:u,onChange:g=>xt(g,l),size:"small",fullWidth:!0,sx:{minHeight:"36px"},children:Array.isArray((S=Xe==null?void 0:Xe[o==null?void 0:o.field])==null?void 0:S[(E=l==null?void 0:l.row)==null?void 0:E.id])&&Xe[o.field][l.row.id].length>0?Xe[o.field][l.row.id].map((g,z)=>t(fs,{value:g==null?void 0:g.code,children:g==null?void 0:g.code},z)):null})}}:["HouseBank","FieldStsGrp"].includes(o.field)?{...o,editable:!1,renderCell:l=>{var E,S;const u=l.value||"";return l.row,t(Cs,{value:u,onChange:g=>xt(g,l),size:"small",fullWidth:!0,sx:{minHeight:"36px"},children:Array.isArray((S=Xe==null?void 0:Xe[o==null?void 0:o.field])==null?void 0:S[(E=l==null?void 0:l.row)==null?void 0:E.id])&&Xe[o.field][l.row.id].length>0?Xe[o.field][l.row.id].map((g,z)=>t(fs,{value:g==null?void 0:g.code,children:g==null?void 0:g.code},z)):null})}}:["Description","GLname","GLAccount"].includes(o.field)?{...o,editable:!0,renderCell:l=>t(Ho,{value:l.value||"",onChange:y(l),variant:"outlined",size:"small",fullWidth:!0}),renderEditCell:l=>t(Ho,{value:l.value||"",onChange:y(l),variant:"outlined",size:"small",fullWidth:!0,placeholder:o.field==="longDescription"?"Enter Long Description":"Enter Short Description",sx:{"& .MuiInputBase-root.Mui-disabled":{"& > input":{WebkitTextFillColor:"#000",color:"#000"}}}})}:["Openitmmanage","Pstnwotax","OpenItemManagebyLedgerGrp","PostingBlockedCoCd","PostingBlockedCOA","PostAuto","Balanceinlocrcy"].includes(o.field)?{...o,editable:!0,renderCell:l=>t(vo,{sx:{padding:0,marginTop:"5px","&.Mui-disabled":{color:Jo.hover.light},"&.Mui-disabled.Mui-checked":{color:Jo.hover.light}},checked:l.value,onChange:M(l)}),renderEditCell:l=>t(vo,{sx:{padding:0,marginTop:"5px","&.Mui-disabled":{color:Jo.hover.light},"&.Mui-disabled.Mui-checked":{color:Jo.hover.light}},checked:l.value,onChange:M(l)})}:{...o,editable:!0}),Y=[...te,...K],ce=Y.length<10?Y.map(o=>o.field==="included"||o.field==="lineNumber"?{...o,width:o.width||100,flex:void 0}:{...o,flex:2,minWidth:200}):Y,L=o=>(w(ys(o)),o),Ce=o=>(w(ms(o)),o),je=o=>{Ao(o.row)};r.useEffect(()=>{ho(Ze)},[Ze]);const _e=o=>{var S;let l={glAccount:"",chartOfAccount:Ze,postAutoOnly:"",companyCode:he==null?void 0:he.join(","),taxCategory:"",glAcctLongText:"",postingWithoutTaxAllowed:"",blockedForPostingInCOA:"",shortText:"",blockedForPostingInCompany:"",accountGroup:"",glAccountType:(S=o==null?void 0:o[0])==null?void 0:S.code,fieldStatusGroup:"",openItemMgmtbyLedgerGroup:"",openItemManagement:"",reconAccountforAcctType:"",fromDate:Ss(qt==null?void 0:qt.createdOn[0]).format("YYYY-MM-DDT00:00:00")??"",toDate:Ss(qt==null?void 0:qt.createdOn[1]).format("YYYY-MM-DDT00:00:00")??"",createdBy:"",top:100,skip:0};const u=g=>{var z,Ne;if(g.statusCode===200){let Ae=[];(Ne=(z=g==null?void 0:g.body)==null?void 0:z.list)==null||Ne.forEach(fe=>{let At={};At.code=fe==null?void 0:fe.GLAccount,At.desc=fe==null?void 0:fe.GLname,At.coa=fe==null?void 0:fe.COA,At.accType=fe==null?void 0:fe.Accounttype,At.accGroup=fe==null?void 0:fe.AccountGroup,At.compCode=fe==null?void 0:fe.CompanyCode,Ae.push(At)}),vt(fe=>[...new Set([...fe,...Ae])])}},E=g=>{de(g)};A(`/${N}/data/getGeneralLedgersBasedOnAdditionalParams`,"post",u,E,l)},Se=()=>{if(!ne.length||!Ze)return;const o=[];ne.forEach(S=>{o.push({glAccount:S==null?void 0:S.code,chartOfAccount:S==null?void 0:S.coa,companyCode:S==null?void 0:S.compCode})});const l={glAccCOACoCode:o},u=S=>{const g=(S==null?void 0:S.body)||[];Zt(g);const z=Vs(g);g==null||g.map(H=>{De(H==null?void 0:H.CompanyCode,H==null?void 0:H.GeneralLedgerID)}),z==null||z.map(H=>{H!=null&&H.COA&&Ut(H==null?void 0:H.CompanyCode,H==null?void 0:H.id),H!=null&&H.CompanyCode&&(wt(H==null?void 0:H.CompanyCode,H==null?void 0:H.id),at(H==null?void 0:H.CompanyCode,H==null?void 0:H.id),De(H==null?void 0:H.CompanyCode,H==null?void 0:H.id),Be(H==null?void 0:H.id)),H!=null&&H.Accounttype&&X(H==null?void 0:H.Accounttype,H==null?void 0:H.id)});let Ne={};z==null||z.forEach(H=>{const $o=(H==null?void 0:H.id)??"";Ne[$o]={...H}});const At={requestHeaderData:{},rowsHeaderData:{},rowsBodyData:Ne};w(Ue==="true"?qn(z):zn(z)),w(Gs(At))},E=S=>{};A(`/${N}/data/getGeneralLedgersData`,"post",u,E,l)};r.useEffect(()=>{var o;if(Ue==="true"&&Array.isArray(We)&&We.length>0&&Ge.length===0){let l="";((o=We[0])==null?void 0:o.GeneralLedgerID)!==null&&(l=js(We)),(l==null?void 0:l.length)>0&&(l==null||l.map(u=>{u!=null&&u.COA&&Ut(u==null?void 0:u.COA,u==null?void 0:u.id),u!=null&&u.compCode&&(wt(u==null?void 0:u.compCode,u==null?void 0:u.id),at(u==null?void 0:u.compCode,u==null?void 0:u.id),De(u==null?void 0:u.compCode,u==null?void 0:u.id),Be(u==null?void 0:u.id)),u!=null&&u.accountType&&X(u==null?void 0:u.accountType,u==null?void 0:u.id)}),w(qn(l)),w(Xs(l)))}},[We,Ue]),r.useEffect(()=>{if((_t==null?void 0:_t.length)>0){const o=_t.filter(u=>(u==null?void 0:u.MDG_MAT_TEMPLATE)===(W==null?void 0:W.TemplateName)).sort((u,E)=>{const S=Number(u==null?void 0:u.MDG_MAT_FIELD_SEQUENCE)||0,g=Number(E==null?void 0:E.MDG_MAT_FIELD_SEQUENCE)||0;return S-g}),l=[...new Set(o.map(u=>u==null?void 0:u.MDG_MAT_UI_FIELD_NAME).filter(Boolean))].map(u=>({code:u}));w(Dt({keyName:"FieldName",data:l||[]}))}},[_t]),r.useEffect(()=>{st&&oo(!0)},[st]);const I=(We??[]).map(o=>{let l={};if(typeof o.changedFields=="object"&&o.changedFields!==null)l=o.changedFields;else if(typeof o.ChangedFields=="string")try{l=JSON.parse(o.ChangedFields)}catch{l={}}const{changedFields:u,ChangedFields:E,...S}=o;return{...S,changedFields:l}});r.useEffect(()=>{if(!I||I.length===0)return;const o={};I.forEach(l=>{o[l.CostCenterID]=l.changedFields||{}}),w(zs(o))},[We]);const Ye=(o="",l="")=>{const u=Ro(W,P,qe,ft,Ge,T,o,l,""),E=g=>{(g==null?void 0:g.statusCode)===200||(g==null?void 0:g.statusCode)===201?(Me({title:Q.TITLE,message:g.message,subText:Q.SUBTEXT,buttonText:Q.BUTTONTEXT,redirectTo:Q.REDIRECT}),et(!0)):(g==null?void 0:g.statusCode)===500||(g==null?void 0:g.statusCode)===501?(Me({title:Z.TITLE,message:g.message,subText:Z.SUBTEXT,buttonText:Z.BUTTONTEXT,redirectTo:Z.REDIRECT}),et(!0)):(Ve(!0),B("Unexpected response received."))},S=g=>{ve(!1),me("error"),B("Error occurred while saving the draft."),Ve(!0)};A(`/${N}/massAction/changeGeneralLedgersSaveAsDraft`,"POST",E,S,u)},Nt=(o="",l="")=>{const u=Ro(W,P,qe,ft,Ge,T,o,l,Ee),E=g=>{ve(!1),me("success"),B("Cost Centers change submission for save as draft initiated"),Ve(!0),setTimeout(()=>{q("/requestbench")},2e3)},S=g=>{ve(!1),me("error"),B("Error occurred while saving the draft."),Ve(!0)};A(`/${N}/massAction/changeGeneralLedgersSaveAsDraft`,"POST",E,S,u)},Lt=(o="",l="")=>{const u=Ro(W,P,qe,ft,Ge,T,o,l,Ee),E=g=>{(g==null?void 0:g.statusCode)===200||(g==null?void 0:g.statusCode)===201?(Me({title:Q.TITLE,message:g.message,subText:Q.SUBTEXT,buttonText:Q.BUTTONTEXT,redirectTo:Q.REDIRECT}),et(!0)):(g==null?void 0:g.statusCode)===500||(g==null?void 0:g.statusCode)===501?(Me({title:Z.TITLE,message:g.message,subText:Z.SUBTEXT,buttonText:Z.BUTTONTEXT,redirectTo:Z.REDIRECT}),et(!0)):(Ve(!0),B("Unexpected response received."))},S=g=>{ve(!1),me("error"),B("Error occurred while Reject."),Ve(!0)};A(`/${N}/massAction/changeGeneralLedgersSaveAsDraft`,"POST",E,S,u)},Rt=(o="",l="")=>{const u=Ro(W,P,qe,ft,Ge,T,o,l,Ee),E=g=>{(g==null?void 0:g.statusCode)===200||(g==null?void 0:g.statusCode)===201?(Me({title:Q.TITLE,message:g.message,subText:Q.SUBTEXT,buttonText:Q.BUTTONTEXT,redirectTo:Q.REDIRECT}),et(!0)):(g==null?void 0:g.statusCode)===500||(g==null?void 0:g.statusCode)===501?(Me({title:Z.TITLE,message:g.message,subText:Z.SUBTEXT,buttonText:Z.BUTTONTEXT,redirectTo:Z.REDIRECT}),et(!0)):(Ve(!0),B("Unexpected response received."))},S=g=>{ve(!1),me("error"),B("Error occurred while saving submit for review."),Ve(!0)};A(`/${N}/massAction/changeGeneralLedgersSubmitForReview`,"POST",E,S,u)},oe=(o="",l="")=>{const u=Ro(W,P,qe,ft,Ge,T,o,l,Ee),E=g=>{ve(!1),me("success"),B("Cost Centers successfuly Approved"),Ve(!0),setTimeout(()=>{q("/requestbench")},2e3)},S=g=>{ve(!1),me("error"),B("Error occurred while saving submit for review."),Ve(!0)};A(`/${N}/massAction/changeGeneralLedgersApprovalSubmit`,"POST",E,S,u)},Oo=(o="",l="")=>{ct(!0);const u=Ro(W,P,qe,ft,Ge,T,o="",l=""),E=g=>{ct(!1),(g==null?void 0:g.statusCode)===200||(g==null?void 0:g.statusCode)===201?(Me({title:Q.TITLE,message:g.message,subText:Q.SUBTEXT,buttonText:Q.BUTTONTEXT,redirectTo:Q.REDIRECT}),et(!0)):(g==null?void 0:g.statusCode)===500||(g==null?void 0:g.statusCode)===501?(Me({title:Z.TITLE,message:g.message,subText:Z.SUBTEXT,buttonText:Z.BUTTONTEXT,redirectTo:Z.REDIRECT}),et(!0)):(Ve(!0),B("Unexpected response received."))},S=g=>{ve(!1),me("error"),B("Error occurred while Validate."),Ve(!0)};A(`/${N}/massAction/validateMassGeneralLedger`,"POST",E,S,u)},Bo=(o,l="",u="")=>{const E=Ro(W,P,qe,ft,Ge,T,l,u,Ee),S=z=>{(z==null?void 0:z.statusCode)===200||(z==null?void 0:z.statusCode)===201?(Me({title:Q.TITLE,message:z.message,subText:Q.SUBTEXT,buttonText:Q.BUTTONTEXT,redirectTo:Q.REDIRECT}),et(!0)):(z==null?void 0:z.statusCode)===500||(z==null?void 0:z.statusCode)===501?(Me({title:Z.TITLE,message:z.message,subText:Z.SUBTEXT,buttonText:Z.BUTTONTEXT,redirectTo:Z.REDIRECT}),et(!0)):(Ve(!0),B("Unexpected response received."))},g=z=>{showSnackbar(z==null?void 0:z.message,"error"),ct(!1)};A(o==="VALIDATE"?`/${N}/massAction/validateMassGeneralLedger`:`/${N}/massAction/changeGeneralLedgersApproved`,"POST",S,g,E)},v=()=>{mt(!0)},F=()=>{mt(!1)},J=()=>{Eo(!0)},St=()=>{Eo(!1),Bt("systemGenerated")},Pt=o=>{var l;Bt((l=o==null?void 0:o.target)==null?void 0:l.value)},nn=()=>{le==="systemGenerated"&&(tt(),St()),le==="mailGenerated"&&(so(),St())},tt=()=>{var S;we("Please wait 1-2 minutes while real-time data are downloaded into the template. Thank you for your patience."),ct(!0),oo(!1),m(!1),Qe||q((S=Mt)==null?void 0:S.REQUEST_BENCH);const o=[];ne.forEach(g=>{o.push({glAccount:g==null?void 0:g.code,coa:g==null?void 0:g.coa,compCode:g==null?void 0:g.compCode,accountType:g==null?void 0:g.accType})});let l={dtName:"MDG_GL_CHANGE_TEMPLATE_DT",version:"v3",templateHeaders:$==null?void 0:$.join(","),templateName:W==null?void 0:W.TemplateName,requestId:(W==null?void 0:W.RequestId)||(P==null?void 0:P.requestId)||"",GlAccount:o,headers:$};const u=g=>{var Ae;if((g==null?void 0:g.size)==0){ct(!1),we(""),As((Ae=Yn)==null?void 0:Ae.NO_DATA_FOUND,"error",{position:"top-center",largeWidth:!0}),setTimeout(()=>{var fe;q((fe=Mt)==null?void 0:fe.REQUEST_BENCH)},2600);return}const z=URL.createObjectURL(g),Ne=document.createElement("a");Ne.href=z,Ne.setAttribute("download",`${W==null?void 0:W.TemplateName}_Mass Change.xlsx`),document.body.appendChild(Ne),Ne.click(),document.body.removeChild(Ne),URL.revokeObjectURL(z),ct(!1),we(""),Qt(!0),no(`${W==null?void 0:W.TemplateName}_Mass Change.xlsx has been downloaded successfully.`),me("success"),v(),setTimeout(()=>{var fe;q((fe=Mt)==null?void 0:fe.REQUEST_BENCH)},2600)},E=()=>{var g;ct(!1),we(""),As((g=Yn)==null?void 0:g.ERR_DOWNLOADING_EXCEL,"error",{position:"top-center"}),setTimeout(()=>{var z;q((z=Mt)==null?void 0:z.REQUEST_BENCH)},2600)};A(`/${N}/excel/downloadExcelWithData`,"postandgetblob",u,E,l)},so=()=>{var S,g,z;ct(!0),onClose();let o=((S=Templates[P==null?void 0:P.TemplateName])==null?void 0:S.map(Ne=>Ne.key))||[],l={};activeTab===0?l={materialDetails:[o.reduce((Ne,Ae)=>(Ne[Ae]=convertedValues!=null&&convertedValues[Ae]?convertedValues==null?void 0:convertedValues[Ae]:"",Ne),{})],templateHeaders:P!=null&&P.FieldName?(g=P.FieldName)==null?void 0:g.join("$^$"):"",requestId:Qe||(P==null?void 0:P.RequestId)||"",templateName:P!=null&&P.TemplateName?P.TemplateName:"",dtName:"MDG_MAT_CHANGE_TEMPLATE",version:"v4",rolePrefix:""}:l={materialDetails:[o.reduce((Ne,Ae)=>(Ne[Ae]=rowsOfMaterialData.map(fe=>{var At;return(At=fe[Ae])==null?void 0:At.trim()}).filter(fe=>fe!=="").join(",")||"",Ne),{})],templateHeaders:P!=null&&P.FieldName?(z=P.FieldName)==null?void 0:z.join("$^$"):"",requestId:Qe||(P==null?void 0:P.RequestId)||"",templateName:P!=null&&P.TemplateName?P.TemplateName:"",dtName:"MDG_MAT_CHANGE_TEMPLATE",version:"v4",rolePrefix:""};const u=()=>{ct(!1),we(""),Qt(!0),no("Download has been started. You will get the Excel file via email."),me("success"),v(),setTimeout(()=>{var Ne;q((Ne=Mt)==null?void 0:Ne.REQUEST_BENCH)},2600)},E=()=>{ct(!1),Qt(!0),no("Oops! Something went wrong. Please try again later."),me("danger"),v(),setTimeout(()=>{var Ne;q((Ne=Mt)==null?void 0:Ne.REQUEST_BENCH)},2600)};A(`/${destination_MaterialMgmt}/excel/downloadExcelWithDataInMail`,"postandgetblob",u,E,l)};return Y.map(o=>({...o,renderCell:l=>{var E;const u=(E=Pe[l.row.CostCenterID])==null?void 0:E[o.field];return t("div",{style:{backgroundColor:u?"rgba(255, 229, 100, 0.6)":"inherit",padding:"0 4px",borderRadius:4,height:"100%",display:"flex",alignItems:"center"},children:l.value})}})),x("div",{children:[t(ss,{open:xe,onClose:()=>et(!1),title:Re.title,message:Re.message,subText:Re.subText,buttonText:Re.buttonText,redirectTo:Re.redirectTo}),bt&&t(Hr,{duplicateFieldsArr:uo,moduleName:(sn=Xt)==null?void 0:sn.GL,open:bt,onClose:()=>Jt(!1)}),k&&t(Rn,{openSnackBar:lo,alertMsg:bo,alertType:Fe,handleSnackBarClose:F}),((W==null?void 0:W.TemplateName)||st)&&x(wo,{children:[(T==null?void 0:T.length)===0&&Ue!=="true"&&x(wo,{children:[x(mo,{open:ao,TransitionComponent:Os,keepMounted:!0,onClose:(o,l)=>{l==="backdropClick"||l==="escapeKeyDown"||ge()},maxWidth:"sm",fullWidth:!0,children:[x(ae,{sx:{backgroundColor:"#e3f2fd",padding:"1rem 1.5rem",display:"flex",alignItems:"center"},children:[t(Fn,{color:"primary",sx:{marginRight:"0.5rem"}}),x(Je,{variant:"h6",component:"div",color:"primary",children:[W==null?void 0:W.TemplateName," Search Filter(s)"]})]}),x(ro,{sx:{padding:"1.5rem 1.5rem 1rem"},children:[t(ae,{sx:{marginBottom:"1rem"},children:t(qo,{param:{key:"chartOfAccount",label:"Chart Of Account"},dropDownData:{chartOfAccount:(Xe==null?void 0:Xe.COA)||[]},selectedValues:{chartOfAccount:Ze?[{code:Ze}]:[]},handleSelectionChange:(o,l)=>{Vt(l.length>0?l[0].code||l[0]:""),He([]),Kt([]),Gt([]),pt(l[0].code)},formatOptionLabel:o=>o.code&&o.desc?`${o.code} - ${o.desc}`:o.code||"",singleSelect:!0,errors:{}})}),t(ae,{sx:{marginBottom:"1rem"},children:t(qo,{param:{key:"companyCode",label:"Company Code"},dropDownData:{companyCode:j||[]},selectedValues:{companyCode:he.map(o=>(j==null?void 0:j.find(l=>l.code===o))||{code:o})},handleSelectAll:o=>{he.length===(j==null?void 0:j.length)?Gt([]):Gt((j==null?void 0:j.map(l=>l.code))||[])},handleSelectionChange:(o,l)=>{Gt(l.map(u=>typeof u=="string"?u:u.code||u)),He([]),Kt([])},formatOptionLabel:o=>o.code&&o.desc?`${o.code} - ${o.desc}`:o.code||"",isSelectAll:!0,errors:{}})}),t(ae,{sx:{marginBottom:"1rem"},children:t(qo,{param:{key:"accountType",label:"account type"},dropDownData:{accountType:be||[]},selectedValues:{accountType:Ct.map(o=>(be==null?void 0:be.find(l=>l.code===o))||{code:o})},handleSelectionChange:(o,l)=>{Kt(l.map(u=>typeof u=="string"?u:u.code||u)),He([]),_e(l)},formatOptionLabel:o=>o.code&&o.desc?`${o.code} - ${o.desc}`:o.code||"",singleSelect:!0,errors:{}})}),t(ae,{sx:{marginBottom:"1rem"},children:t(qo,{param:{key:"generalLedger",label:"general Ledger"},dropDownData:{generalLedger:G},selectedValues:{generalLedger:(ne==null?void 0:ne.map(o=>({code:o.code})))||[]},handleSelectAll:o=>{(ne==null?void 0:ne.length)===(G==null?void 0:G.length)?He([]):He(G||[])},handleSelectionChange:(o,l)=>{const u=l.map(E=>typeof E=="string"?G.find(S=>S.code===E)||{code:E}:(!E.desc||!E.coa||!E.accGroup||!E.accType)&&G.find(S=>S.code===E.code)||E);He(u)},formatOptionLabel:o=>typeof o=="string"?o:(o==null?void 0:o.code)||o,isSelectAll:!0,errors:{}})})]}),t(co,{sx:{padding:"0.5rem 1.5rem",display:"flex",alignItems:"center"},children:x(ae,{sx:{display:"flex",gap:1},children:[t(ke,{onClick:ge,color:"error",variant:"outlined",sx:{height:36,minWidth:"3.5rem",textTransform:"none",borderColor:"#cc3300",fontWeight:500},children:"Cancel"}),(W==null?void 0:W.RequestType)!==((po=re)==null?void 0:po.CHANGE_WITH_UPLOAD)&&t(ke,{onClick:()=>{eo("OK")},variant:"contained",sx:{height:36,minWidth:"3.5rem",backgroundColor:"#3B30C8",textTransform:"none",fontWeight:500,"&:hover":{backgroundColor:"#2c278f"}},children:"OK"}),(W==null?void 0:W.RequestType)===((Xo=re)==null?void 0:Xo.CHANGE_WITH_UPLOAD)&&t(ke,{onClick:()=>{J()},variant:"contained",sx:{height:36,minWidth:"3.5rem",backgroundColor:"#3B30C8",textTransform:"none",fontWeight:500,"&:hover":{backgroundColor:"#2c278f"}},children:"Download"})]})})]}),t(es,{onDownloadTypeChange:nn,open:ze,downloadType:le,handleDownloadTypeChange:Pt,onClose:St}),t(Cn,{blurLoading:io,loaderMessage:ye})]}),go&&x(ae,{sx:{mt:4,px:4},children:[t(Je,{variant:"h5",sx:{fontWeight:600,mb:2},children:"General ledger Lists"}),t(Dn,{elevation:3,sx:{borderRadius:3,overflow:"hidden",border:"1px solid #e0e0e0",backgroundColor:"#fafbff"},children:t(ae,{sx:{p:2},children:t(Un,{rows:T,columns:ce,pageSize:10,tempheight:"50vh",getRowIdValue:"id",editMode:"row",status_onRowSingleClick:!0,callback_onRowSingleClick:je,processRowUpdate:L,experimentalFeatures:{newEditingApi:!0},isCellEditable:o=>!["costCenter","companyCode"].includes(o.field),getRowClassName:o=>(Yt==null?void 0:Yt.id)===o.row.id?"Mui-selected":""})})}),x(ae,{sx:{display:"flex",justifyContent:"right",mt:3,gap:2},children:[t(ke,{variant:"contained",color:"primary",onClick:Ye,children:"Save as draft"}),t(ke,{variant:"contained",color:"primary",onClick:Oo,children:"Validate"}),t(ke,{variant:"contained",color:"secondary",onClick:Rt,disabled:!zt,children:"Submit"})]})]})]}),x(wo,{children:[Ge.length===0&&Ue==="true"&&x(wo,{children:[x(mo,{open:ao,TransitionComponent:Os,keepMounted:!0,onClose:(o,l)=>{l==="backdropClick"||l==="escapeKeyDown"||ge()},maxWidth:"sm",fullWidth:!0,children:[x(ae,{sx:{backgroundColor:"#e3f2fd",padding:"1rem 1.5rem",display:"flex",alignItems:"center"},children:[t(Fn,{color:"primary",sx:{marginRight:"0.5rem"}}),x(Je,{variant:"h6",component:"div",color:"primary",children:[W==null?void 0:W.TemplateName," Search Filter(s)"]})]}),x(ro,{sx:{padding:"1.5rem 1.5rem 1rem"},children:[t(ae,{sx:{marginBottom:"1rem"},children:t(qo,{param:{key:"chartOfAccount",label:"Chart Of Account"},dropDownData:{chartOfAccount:(Xe==null?void 0:Xe.COA)||[]},selectedValues:{chartOfAccount:Ze?[{code:Ze}]:[]},handleSelectionChange:(o,l)=>{Vt(l.length>0?l[0].code||l[0]:""),He([]),Kt([]),Gt([]),pt(l[0].code)},formatOptionLabel:o=>o.code&&o.desc?`${o.code} - ${o.desc}`:o.code||"",singleSelect:!0,errors:{}})}),t(ae,{sx:{marginBottom:"1rem"},children:t(qo,{param:{key:"companyCode",label:"Company Code"},dropDownData:{companyCode:j||[]},selectedValues:{companyCode:he.map(o=>(j==null?void 0:j.find(l=>l.code===o))||{code:o})},handleSelectAll:o=>{he.length===(j==null?void 0:j.length)?Gt([]):Gt((j==null?void 0:j.map(l=>l.code))||[])},handleSelectionChange:(o,l)=>{Gt(l.map(u=>typeof u=="string"?u:u.code||u)),He([]),Kt([])},formatOptionLabel:o=>o.code&&o.desc?`${o.code} - ${o.desc}`:o.code||"",isSelectAll:!0,errors:{}})}),t(ae,{sx:{marginBottom:"1rem"},children:t(qo,{param:{key:"accountType",label:"account type"},dropDownData:{accountType:be||[]},selectedValues:{accountType:Ct.map(o=>(be==null?void 0:be.find(l=>l.code===o))||{code:o})},handleSelectionChange:(o,l)=>{Kt(l.map(u=>typeof u=="string"?u:u.code||u)),He([]),_e(l)},formatOptionLabel:o=>o.code&&o.desc?`${o.code} - ${o.desc}`:o.code||"",singleSelect:!0,errors:{}})}),t(ae,{sx:{marginBottom:"1rem"},children:t(qo,{param:{key:"generalLedger",label:"general Ledger"},dropDownData:{generalLedger:G},selectedValues:{generalLedger:(ne==null?void 0:ne.map(o=>({code:o.code})))||[]},handleSelectAll:o=>{(ne==null?void 0:ne.length)===(G==null?void 0:G.length)?He([]):He(G||[])},handleSelectionChange:(o,l)=>{const u=l.map(E=>typeof E=="string"?G.find(S=>S.code===E)||{code:E}:(!E.desc||!E.coa||!E.accGroup||!E.accType)&&G.find(S=>S.code===E.code)||E);He(u)},formatOptionLabel:o=>typeof o=="string"?o:(o==null?void 0:o.code)||o,isSelectAll:!0,errors:{}})})]}),t(co,{sx:{padding:"0.5rem 1.5rem",display:"flex",alignItems:"center"},children:x(ae,{sx:{display:"flex",gap:1},children:[t(ke,{onClick:ge,color:"error",variant:"outlined",sx:{height:36,minWidth:"3.5rem",textTransform:"none",borderColor:"#cc3300",fontWeight:500},children:"Cancel"}),(W==null?void 0:W.RequestType)!==((To=re)==null?void 0:To.CHANGE_WITH_UPLOAD)&&t(ke,{onClick:()=>{eo("OK")},variant:"contained",sx:{height:36,minWidth:"3.5rem",backgroundColor:"#3B30C8",textTransform:"none",fontWeight:500,"&:hover":{backgroundColor:"#2c278f"}},children:"OK"}),(W==null?void 0:W.RequestType)===((zo=re)==null?void 0:zo.CHANGE_WITH_UPLOAD)&&t(ke,{onClick:()=>{J()},variant:"contained",sx:{height:36,minWidth:"3.5rem",backgroundColor:"#3B30C8",textTransform:"none",fontWeight:500,"&:hover":{backgroundColor:"#2c278f"}},children:"Download"})]})})]}),t(es,{onDownloadTypeChange:nn,open:ze,downloadType:le,handleDownloadTypeChange:Pt,onClose:St}),t(Cn,{blurLoading:io,loaderMessage:ye})]}),Ue==="true"&&x(ae,{sx:{marginTop:"20px",padding:"16px"},children:[t(Je,{variant:"h5",gutterBottom:!0,children:"General Ledger Lists"}),t(Dn,{elevation:4,sx:{p:0,borderRadius:2,overflow:"hidden",mt:"50px"},children:t("div",{children:t(Un,{rows:Ge??[],columns:ce,pageSize:10,tempheight:"50vh",getRowIdValue:"id",status_onRowSingleClick:!0,editMode:"cell",callback_onRowSingleClick:je,processRowUpdate:Ce,experimentalFeatures:{newEditingApi:!0},isCellEditable:o=>!["costCenter","companyCode"].includes(o.field),getRowClassName:o=>(Yt==null?void 0:Yt.id)===o.row.id?"Mui-selected":""})})}),t(ae,{sx:{display:"flex",justifyContent:"right",mt:3,gap:2},children:t(rs,{handleSaveAsDraft:Ye,handleSubmitForReview:Rt,handleSubmitForApprove:oe,handleSendBack:Nt,handleRejectAndCancel:Lt,handleValidateAndSyndicate:Bo,validateAllRows:Oo,filteredButtons:It,moduleName:ie})})]})]})]})},Qr=()=>{const dt=window.location.hash.split("/");dt[dt.length-1],_n(),O(ie=>{var C,Ke;return(Ke=(C=ie==null?void 0:ie.costCenter)==null?void 0:C.payload)==null?void 0:Ke.requestHeaderData});const We=O(ie=>ie.applicationConfig),st=tn();return{getChangeTemplate:()=>{var q;let ie={decisionTableId:null,decisionTableName:"MDG_GL_CHANGE_TEMPLATE_DT",version:"v3",rulePolicy:null,validityDate:null,conditions:[{"MDG_CONDITIONS.MDG_MAT_ROLE":Kn.REQ_INITIATE_FIN,"MDG_CONDITIONS.MDG_GL_ACCOUNT_TYPE":"X","MDG_CONDITIONS.MDG_GL_SCENARIO":(q=re)==null?void 0:q.CHANGE}],systemFilters:null,systemOrders:null,filterString:null};const C=w=>{var T,ut;if(w.statusCode===200){let Ge=(ut=(T=w==null?void 0:w.data)==null?void 0:T.result[0])==null?void 0:ut.MDG_MAT_CHANGE_TEMPLATE;st(Es(Ge));const Et=[...new Set(Ge.map(de=>de==null?void 0:de.MDG_MAT_TEMPLATE).filter(Boolean))].map(de=>({code:de}));st(Ys({keyName:"TemplateName",data:Et})),st(Es(Ge));let Pe=[],W=[],gt=[];Ge==null||Ge.map((de,qe)=>{if(de.MDG_FIELD_SELECTION_LVL=="COMPANY CODE"){let rt={};rt.id=qe,rt.name=de.MDG_SELECT_OPTION,Pe.push(rt)}else if(de.MDG_FIELD_SELECTION_LVL=="CHART OF ACCOUNT"){let rt={};rt.id=qe,rt.name=de.MDG_SELECT_OPTION,W.push(rt)}else{let rt={};rt.id=qe,rt.name=de.MDG_SELECT_OPTION,gt.push(rt)}});const P=new Set,It=Pe.filter(de=>P.has(de.name)?!1:(P.add(de.name),!0)),zt=new Set,qt=W.filter(de=>zt.has(de.name)?!1:(zt.add(de.name),!0));setDataList(It),setDataListCOA(qt),setDataListBlocked(gt)}handleClose()},Ke=w=>{};We.environment==="localhost"?A(`/${mn}/rest/v1/invoke-rules`,"post",C,Ke,ie):A(`/${mn}/v1/invoke-rules`,"post",C,Ke,ie)}}},Zr=()=>{const Ue=O(ie=>ie.generalLedger.payload.requestHeaderData);console.log(Ue,"initialPayload");const dt=O(ie=>ie.applicationConfig),We=tn(),st="General Ledger";return{getRequestHeaderTemplateGl:()=>{let ie={decisionTableId:null,decisionTableName:"MDG_FMD_REQUEST_HEADER_CONFIG",version:"v2",rulePolicy:null,validityDate:null,conditions:[{"MDG_CONDITIONS.MDG_MAT_SCENARIO":(Ue==null?void 0:Ue.RequestType)||"Create","MDG_CONDITIONS.MDG_MAT_MODULE_NAME":st}],systemFilters:null,systemOrders:null,filterString:null};const C=q=>{var w,T;if(q.statusCode===200){const Et={"Header Data":((T=(w=q==null?void 0:q.data)==null?void 0:w.result[0])==null?void 0:T.MDG_MAT_REQUEST_HEADER_CONFIG).sort((Pe,W)=>Pe.MDG_MAT_SEQUENCE_NO-W.MDG_MAT_SEQUENCE_NO).map(Pe=>({fieldName:Pe.MDG_MAT_UI_FIELD_NAME,sequenceNo:Pe.MDG_MAT_SEQUENCE_NO,fieldType:Pe.MDG_MAT_FIELD_TYPE,maxLength:Pe.MDG_MAT_MAX_LENGTH,value:Pe.MDG_MAT_DEFAULT_VALUE,visibility:Pe.MDG_MAT_VISIBILITY,jsonName:Pe.MDG_MAT_JSON_FIELD_NAME}))};We(Ks({tab:"Request Header",data:Et})),We(Qs(Et))}},Ke=q=>{console.log(q)};dt.environment==="localhost"?A(`/${mn}${So.INVOKE_RULES.LOCAL}`,"post",C,Ke,ie):A(`/${mn}${So.INVOKE_RULES.PROD}`,"post",C,Ke,ie)}}},Jr=({apiResponse:Ue,reqBench:dt,downloadClicked:We,setDownloadClicked:st,setIsSecondTabEnabled:m})=>{const C=window.location.hash.split("/"),{t:Ke}=Hn();C[C.length-1],new Date().toLocaleDateString("en-GB");const w=tn(),T=O(B=>B.generalLedger.payload.requestHeaderData),ut=O(B=>B.request.requestHeader),Ge=O(B=>B.userManagement.userData),Et=O(B=>B.tabsData.requestHeaderData);O(B=>B.AllDropDown.dropDown.FieldName||[]);const Pe=O(B=>{var Fe;return(Fe=B==null?void 0:B.generalLedger)==null?void 0:Fe.selectedOptionsForTemplate}),W=kn(),gt=new URLSearchParams(W.search);gt.get("reqBench");const P=gt.get("RequestId"),It=`/Date(${Date.now()})/`;O(B=>B.commonFilter.GeneralLedger);const[zt,qt]=r.useState(""),[de,qe]=r.useState(""),[rt,Ft]=r.useState(""),[Qe,ft]=r.useState(""),[Ot,Lo]=r.useState([]),Ee=Gn(),[Xe,ao]=r.useState(!1);r.useState(!1),r.useState("");const[oo,Ze]=r.useState("success"),[Vt,Yt]=r.useState(!1),[Ao,j]=r.useState("systemGenerated"),[Do,ze]=r.useState(),[Eo,he]=r.useState(!1),[Gt,Ct]=r.useState(!1),[Kt,G]=r.useState(!1),[vt,ne]=r.useState(""),[He,k]=r.useState(""),[Qt,lo]=r.useState(!1),{getChangeTemplate:mt}=Qr(),{getRequestHeaderTemplateGl:bo}=Zr(),no=[{code:"Create",tooltip:"Create New General Directly in Application"},{code:"Change",tooltip:"Modify Existing General Ledger Directly in Application"},{code:"Extend",tooltip:"Extend General Ledger Directly in Application"},{code:"Create with Upload",tooltip:"Create New General with Excel Upload"},{code:"Change with Upload",tooltip:"Modify Existing General Ledger with Excel Upload"}],io=O(B=>{var Fe,me;return((me=(Fe=B==null?void 0:B.AllDropDown)==null?void 0:Fe.dropDown)==null?void 0:me.TemplateName)||[]}),ct=[{code:"High",desc:""},{code:"Medium",desc:""},{code:"Low",desc:""}];w(it({keyName:"RequestStatus",data:"DRAFT"})),w(it({keyName:"ReqCreatedBy",data:Ge==null?void 0:Ge.user_id})),r.useEffect(()=>{var B;if(We){if((T==null?void 0:T.RequestType)===re.CREATE_WITH_UPLOAD){lo(!0);return}if((T==null?void 0:T.RequestType)===((B=re)==null?void 0:B.CHANGE_WITH_UPLOAD)){Ct(!0);return}}},[We]);const ye=()=>{var Fe,me;let B=!0;return T&&((Fe=Et[Object.keys(Et)])!=null&&Fe.length)?(me=Et[Object.keys(Et)[0]])==null||me.forEach(ee=>{var ve;!T[ee.jsonName]&&ee.visibility===((ve=ws)==null?void 0:ve.MANDATORY)&&(B=!1)}):B=!1,B};r.useEffect(()=>{((T==null?void 0:T.RequestType)==="Change"||(T==null?void 0:T.RequestType)==="Change with Upload")&&mt()},[T==null?void 0:T.RequestType]),r.useEffect(()=>{bo()},[T==null?void 0:T.RequestType]),r.useEffect(()=>{const B=zt&&de&&rt.trim()!=="",Fe=zt!=="Change"||zt==="Change with Upload"||Qe&&Ot.length>0;ao(B&&Fe)},[zt,de,rt,Qe,Ot]);const we=()=>{var be;const B=new Date(T==null?void 0:T.ReqCreatedOn).getTime(),Fe=Pe==null?void 0:Pe.join(",");Ct(!1);const me={RequestId:"",ReqCreatedBy:"<EMAIL>",ReqCreatedOn:B?`/Date(${B})/`:It,ReqUpdatedOn:B?`/Date(${B})/`:It,RequestType:(T==null?void 0:T.RequestType)||"",RequestDesc:((be=T==null?void 0:T.RequestDesc)==null?void 0:be.toUpperCase())||"",RequestStatus:"DRAFT",RequestPriority:(T==null?void 0:T.RequestPriority)||"",FieldName:Fe,TemplateName:(T==null?void 0:T.TemplateName)||"",ChangeCategory:"",IsHierarchyGroup:!1,Region:(T==null?void 0:T.Region)||""},ee=Le=>{var xe,et,uo;if(w(Po(!0)),he(!0),ze(`Request Header Created Successfully! Request ID: ${(xe=Le==null?void 0:Le.body)==null?void 0:xe.requestId}`),Yt(!1),Ze("success"),Bt(),w(er(Le==null?void 0:Le.body)),w(_s(Le.body)),w(tr({keyName:or.REQUEST_ID,data:(et=Le==null?void 0:Le.body)==null?void 0:et.requestId})),(T==null?void 0:T.RequestType)===re.CREATE_WITH_UPLOAD||(T==null?void 0:T.RequestType)===re.EXTEND_WITH_UPLOAD){lo(!0);return}if((T==null?void 0:T.RequestType)===((uo=re)==null?void 0:uo.CHANGE_WITH_UPLOAD)){Ct(!0);return}setTimeout(()=>{w(Nn(1)),m(!0)},2500)},ve=Le=>{he(!0),Ze("error"),ze("Error occured while saving Request Header")};A(`/${N}/massAction/createRequestHeader`,"post",ee,ve,me)},le=()=>{var B;st(!1),lo(!1),j("systemGenerated"),P||Ee((B=Mt)==null?void 0:B.REQUEST_BENCH)},Bt=()=>{G(!0)},lt=()=>{G(!1)},Zt=B=>{var Fe;j((Fe=B==null?void 0:B.target)==null?void 0:Fe.value)},$t=()=>{Ao==="systemGenerated"&&(Ve(),le()),Ao==="mailGenerated"&&(ht(),le())},Ve=()=>{var ve;ne("Please wait 1-2 minutes while real-time data are downloaded into the template. Thank you for your patience."),k(!0);let B={scenario:(T==null?void 0:T.RequestType)||(ut==null?void 0:ut.requestType)||"",rolePrefix:(ve=Kn)==null?void 0:ve.REQ_INITIATE_DOWNLOAD,dtName:"MDG_GL_FIELD_CONFIG",version:"v3",requestId:(T==null?void 0:T.RequestId)||(ut==null?void 0:ut.requestId)||""};const Fe=be=>{if((be==null?void 0:be.size)==0){k(!1),ne(""),he(!0),ze("No data found for the selected criteria."),Ze("danger"),Bt();return}const Le=URL.createObjectURL(be),xe=document.createElement("a");xe.href=Le,xe.setAttribute("download","Mass_Create.xlsx"),document.body.appendChild(xe),xe.click(),document.body.removeChild(xe),URL.revokeObjectURL(Le),k(!1),ne(""),he(!0),ze(`${T!=null&&T.TemplateName?`${T==null?void 0:T.TemplateName}_Mass Change`:"Mass_Create"}.xlsx has been downloaded successfully.`),Ze("success"),Bt(),setTimeout(()=>{Ee("/requestBench")},2600)},me=()=>{k(!1)},ee=`/${N}${(T==null?void 0:T.RequestType)===re.EXTEND_WITH_UPLOAD?So.EXCEL.DOWNLOAD_EXCEL_FOR_EXTEND:So.EXCEL.DOWNLOAD_EXCEL}`;A(ee,"postandgetblob",Fe,me,B)},ht=()=>{var ve;k(!0);let B={region:T==null?void 0:T.Region,scenario:T==null?void 0:T.RequestType,matlType:"ALL",dtName:"MDG_MAT_MASS_CREATE_EXTEND_FIELD_CONFIG",version:"v1",rolePrefix:(ve=Kn)==null?void 0:ve.REQ_INITIATE_DOWNLOAD,requestId:T!=null&&T.RequestId?T==null?void 0:T.RequestId:""};const Fe=()=>{var be;k(!1),ne(""),he(!0),ze((be=nr)==null?void 0:be.DOWNLOAD_MAIL_INITIATED),Ze("success"),Bt(),setTimeout(()=>{var Le;Ee((Le=Mt)==null?void 0:Le.REQUEST_BENCH)},2600)},me=()=>{var be;k(!1),he(!0),ze((be=Yn)==null?void 0:be.ERR_DOWNLOADING_EXCEL),Ze("danger"),Bt(),setTimeout(()=>{var Le;Ee((Le=Mt)==null?void 0:Le.REQUEST_BENCH)},2600)},ee=`/${destination_MaterialMgmt}${(T==null?void 0:T.RequestType)===re.EXTEND_WITH_UPLOAD?So.EXCEL.DOWNLOAD_EXCEL_FOR_EXTEND_MAIL:So.EXCEL.DOWNLOAD_EXCEL_MAIL}`;A(ee,"post",Fe,me,B)};return t("div",{children:x(Js,{spacing:2,children:[Object.entries(Et).map(([B,Fe])=>x(Tt,{item:!0,md:12,sx:{backgroundColor:"white",maxHeight:"max-content",height:"max-content",borderRadius:"8px",border:"1px solid #E0E0E0",mt:.25,boxShadow:"0px 2px 14px 0px rgba(48, 38, 185, 0.10)",...Zs},children:[t(Je,{sx:{fontSize:"12px",fontWeight:"700",paddingBottom:"10px"},children:B}),t(ae,{children:t(Tt,{container:!0,spacing:1,children:Fe.filter(me=>me.visibility!=="Hidden").sort((me,ee)=>me.sequenceNo-ee.sequenceNo).map(me=>t(Pr,{isHeader:!0,field:me,dropDownData:{RequestType:no,RequestPriority:ct,TemplateName:io},disabled:P,requestHeader:!0,module:"GeneralLedger"},me.id))})}),!P&&!(ut!=null&&ut.requestId)&&t(ae,{sx:{display:"flex",justifyContent:"flex-end",marginTop:"20px"},children:t(ke,{variant:"contained",color:"primary",disabled:!ye(),onClick:we,children:Ke("Save Request Header")})})]},B)),t(Cn,{blurLoading:He,loaderMessage:vt}),Eo&&t(Rn,{openSnackBar:Kt,alertMsg:Do,alertType:oo,handleSnackBarClose:lt}),Gt&&t(Ws,{downloadClicked:We,setDownloadClicked:st}),t(es,{onDownloadTypeChange:$t,open:Qt,downloadType:Ao,handleDownloadTypeChange:Zt,onClose:le})]})})},wr=({reqBench:Ue,apiResponses:dt,setCompleted:We,setIsAttachmentTabEnabled:st,moduleName:m,isDisabled:ie})=>{var ds,ls,is,us,gs,hs;const[C,Ke]=r.useState(null),[q,w]=r.useState(0);r.useState({});const[T,ut]=r.useState([]),[Ge,Et]=r.useState([]),[Pe,W]=r.useState([]),[gt,P]=r.useState([]);r.useState([]),r.useState([]);const[It,zt]=r.useState([]),[qt,de]=r.useState([]),[qe,rt]=r.useState([]);r.useState(""),r.useState([]);const[Ft,Qe]=r.useState([]),[ft,Ot]=r.useState([]),[Lo,Ee]=r.useState(!1),[Xe,ao]=r.useState([]),[oo,Ze]=r.useState(!1),[Vt,Yt]=r.useState(!1),[Ao,j]=r.useState(!1);r.useState("");const[Do,ze]=r.useState(!1),[Eo,he]=r.useState(""),[Gt,Ct]=r.useState("success"),[Kt,G]=r.useState(!1),[vt,ne]=r.useState({}),[He,k]=r.useState(!1),[Qt,lo]=r.useState(""),{getButtonsDisplayGlobal:mt}=ns(),[bo,no]=r.useState({}),[io,ct]=r.useState({}),[ye,we]=r.useState(!1),[le,Bt]=r.useState(!1),[lt,Zt]=r.useState(!1),[$t,Ve]=r.useState([]);r.useState(!1);const[ht,B]=r.useState("yes"),[Fe,me]=r.useState(null),[ee,ve]=r.useState({}),[be,Le]=r.useState({}),[xe,et]=r.useState([]),[uo,Io]=r.useState({"Material No":!1}),[bt,Jt]=r.useState(0),[on,_t]=r.useState(!1);r.useState({});const[se,go]=r.useState(!0),[yt,Re]=r.useState(!1),[Me,ho]=r.useState([]),[pt,De]=r.useState(!1);r.useState(!1);const[wt,at]=r.useState({title:"",message:"",subText:"",buttonText:"",redirectTo:""}),X=tn(),Be=sr(),Ut=Gn(),{t:Te}=Hn(),ge=O(e=>e.payload.payloadData),xo=ge==null?void 0:ge.RequestType,eo=e=>z.includes(e);let ot=O(e=>e==null?void 0:e.userManagement.taskData);const{updateChangeLogGl:xt}=cs(),y=O(e=>{var c;return(c=e==null?void 0:e.generalLedger)==null?void 0:c.validatedRowsStatus}),M=O(e=>{var c;return(c=e==null?void 0:e.generalLedger)==null?void 0:c.selecteddropdownDataForExtendedCode}),$="Basic Data",[te,K]=r.useState([$]),Y=O(e=>e.generalLedger.selectedRowId),ce=O(e=>e.generalLedger.dropdownDataForExtendedCode),L=O(e=>(e.generalLedger.generalLedgerTabs||[]).filter(i=>i.tab!=="Initial Screen"));O(e=>e.changeLog.createChangeLogDataGL||[]);const Ce=O(e=>e.changeLog.createChangeLogDataGL),je=kn(),_e=new URLSearchParams(je.search),Se=O(e=>e.generalLedger.isOpenDialog);_e.get("reqBench");const I=_e.get("RequestId"),Ye=_e.get("RequestId"),Nt=as(Pn.MODULE,!0,{}),Lt=O(e=>e.request.requestHeader),Rt=os[Nt]||(()=>({})),oe=O(Rt);O(e=>e.requestHeader);const{loading:Oo,error:Bo,fetchGeneralLedgerFieldConfig:v}=Us(),F=O(e=>e.generalLedger.payload.rowsHeaderData);r.useState(F||[]);const J=O(e=>e.generalLedger.payload),St=O(e=>{var c;return((c=e.generalLedger.payload)==null?void 0:c.rowsBodyData)||{}});let Pt=(is=(ls=St==null?void 0:St[(ds=F[0])==null?void 0:ds.id])==null?void 0:ls.Torequestheaderdata)==null?void 0:is.RequestStatus;const nn=O(e=>{var c,i;return((i=(c=e.generalLedger.payload)==null?void 0:c.rowsHeaderData[0])==null?void 0:i.companyCode)||{}}),{customError:tt}=_n();O(e=>{var c;return(c=e.generalLedger.payload)==null?void 0:c.rowsHeaderData});const so=O(e=>e.payload.dynamicKeyValues),sn=O(e=>e.payload.filteredButtons),po=O(e=>e.commonFilter.GeneralLedger),Xo={code:"ALL",desc:"Select All"},To=["Chart Of Account","Company Code","Account Type","Account Group","GL Account"],zo=(e,c)=>{if(c==null?void 0:c.some(p=>p.code===Xo.code)){const p=ce==null?void 0:ce[C==null?void 0:C.id];Ve(p),X(On({uniqueId:C==null?void 0:C.id,data:p}))}else X(On({uniqueId:C==null?void 0:C.id,data:c}))},o=e=>e.code==="ALL"?e.desc:`${e.code} - ${e.desc}`,l=()=>{we(!ye),le&&Bt(!1)},u=()=>{X(Po(!1))},E=()=>{Bt(!le),ye&&we(!1)};r.useEffect(()=>{X(rr())},[]),r.useEffect(()=>{F.length>0&&!C&&Ke(F[0])},[F,C]),r.useEffect(()=>{var e;F.length>=1&&((e=F[0])!=null&&e.chartOfAccount)&&X(Po(!1))},[]),r.useEffect(()=>{!(L!=null&&L.length)&&ht==="yes"&&v()},[]),r.useEffect(()=>{ht==="no"&&ve({})},[ht]),r.useEffect(()=>{const e=T==null?void 0:T.filter(c=>c.code!==nn);Et(e)},[]),r.useEffect(()=>{(ot!=null&&ot.ATTRIBUTE_1||I)&&mt("General Ledger","MDG_DYN_BTN_DT","v3")},[ot]),r.useEffect(()=>{if(F.length===0&&Ze(!0),!Array.isArray(F)){Ze(!1);return}const e=F.every(c=>{const i=c==null?void 0:c.id;if(!i)return!1;const p=(vt==null?void 0:vt[String(i)])===!0,f=!Uo(i);return p&&f});Ze(e)},[F,St,vt,bo,io]);const S=()=>{const e=[];return L.forEach(c=>{const i=c.data;Object.values(i).forEach(p=>{e.push(...p)})}),e},g=[{field:"included",headerName:"",width:80,minWidth:80,align:"center",headerAlign:"center",sortable:!1,disableColumnMenu:!0,renderHeader:()=>{const e=F.length>0&&F.every(i=>i.included),c=F.some(i=>i.included);return t(vo,{indeterminate:!e&&c,checked:e,disabled:ie,onChange:i=>{const p=i.target.checked,f=F.map(s=>({...s,included:p}));X(en(f))}})},renderCell:e=>t(vo,{checked:e.row.included,disabled:ie,onChange:c=>Ht(c.target.checked,e.row.id,"included")})},{field:"lineNumber",headerName:"SL No,",width:100,minWidth:100,align:"center",headerAlign:"center"},{field:"chartOfAccount",headerName:"Chart Of Account",align:"center",headerAlign:"center",width:250,minWidth:200,renderHeader:()=>x("span",{children:[Te("Chart Of Account"),eo("chartOfAccount")&&t("span",{style:{color:"red"},children:" *"})]}),renderCell:e=>t(No,{options:(oe==null?void 0:oe.COA)||[],value:e.row.chartOfAccount,onChange:c=>Ht(c,e.row.id,"chartOfAccount"),placeholder:Te("Select Chart Of Account"),disabled:ie,minWidth:"90%",listWidth:235})},{field:"companyCode",headerName:"Company Code",align:"center",headerAlign:"center",width:250,minWidth:200,renderHeader:()=>x("span",{children:[Te("Company Code"),eo("companyCode")&&t("span",{style:{color:"red"},children:" *"})]}),renderCell:e=>t(No,{options:(oe==null?void 0:oe.CompanyCode)||[],value:e.row.companyCode,onChange:c=>Ht(c,e.row.id,"companyCode"),placeholder:Te("Select Company Code"),disabled:ie,minWidth:"90%",listWidth:235})},{field:"accountType",headerName:"Account Type",width:250,minWidth:200,renderHeader:()=>x("span",{children:[Te("Account Type"),eo("accountType")&&t("span",{style:{color:"red"},children:" *"})]}),renderCell:e=>t(No,{options:(oe==null?void 0:oe.accountType)||[],value:e.row.accountType,onChange:c=>Ht(c,e.row.id,"accountType"),placeholder:Te("Select Account Type"),disabled:ie,minWidth:"90%",listWidth:235})},{field:"accountGroup",headerName:"Account Group",width:250,minWidth:200,renderHeader:()=>x("span",{children:[Te("Account Group"),eo("accountGroup")&&t("span",{style:{color:"red"},children:" *"})]}),renderCell:e=>t(No,{options:(oe==null?void 0:oe.accountGroup)||[],value:e.row.accountGroup,onChange:c=>Ht(c,e.row.id,"accountGroup"),placeholder:Te("Select Account Group"),disabled:ie,minWidth:"90%",listWidth:235})},{field:"glAccountNumber",headerName:"General Ledger Number",width:250,minWidth:200,renderHeader:()=>x("span",{children:[Te("General Ledger Number"),eo("glAccountNumber")&&t("span",{style:{color:"red"},children:" *"})]}),renderCell:e=>{var U;const c=e.row.accountGroup;let i;typeof c!="object"?i=(U=oe==null?void 0:oe.accountGroup)==null?void 0:U.filter(R=>R.code===c)[0]:i=c;const p=e.row.glAccountNumber||"",f=p.length>0&&p.length<10;return t(Ho,{value:p,onChange:R=>{const Ie=R.target.value.slice(0,10);Ht(Ie,e.row.id,"glAccountNumber")},onKeyDown:R=>{["Backspace","Delete","Tab","Escape","Enter","ArrowLeft","ArrowRight"].includes(R.key)||/^\d$/.test(R.key)||R.preventDefault()},onPaste:R=>{const Ie=R.clipboardData.getData("text");/^\d+$/.test(Ie)||R.preventDefault()},disabled:ie,variant:"outlined",size:"small",placeholder:i!=null&&i.FromAcct&&(i!=null&&i.ToAcct)?`${i==null?void 0:i.FromAcct} - ${i==null?void 0:i.ToAcct}`:"-",fullWidth:!0,error:f,helperText:f?"Number should be 10 digits":"",FormHelperTextProps:{sx:{marginTop:"30px",paddingLeft:"15px",position:"absolute",style:{paddingBottom:"0px"}}},inputProps:{inputMode:"numeric",maxLength:10},sx:{position:"relative","& .MuiInputBase-root":{height:"45px",alignItems:"center"},"& .MuiFormHelperText-root":{marginLeft:"0px"},"& .MuiInputBase-root.Mui-disabled":{"& > input":{WebkitTextFillColor:Jo.black.dark,color:Jo.black.dark}}}})}},{field:"shortDescription",headerName:"Short Description",width:250,minWidth:200,renderCell:e=>{var s;const c=e.row.shortDescription||"",[i,p]=r.useState(c),f=d=>{var U;p(d.target.value[0]===" "?d.target.value.trimStart():(U=d.target.value)==null?void 0:U.replace(/[^a-zA-Z0-9-&()#.'/$%, ]/g,"").replace(/\s{2,}/g," ").replace(/\s*([-&()#.'/$%,])\s*/g,"$1").replace(/([-&()#.'/$%,])\s+/g,"$1").trimStart())};return e==null||e.row,t(Ho,{variant:"outlined",size:"small",fullWidth:!0,value:i,onFocus:()=>_t(!0),disabled:ie,placeholder:"Enter Short Description",inputProps:{style:{textTransform:"uppercase"},maxLength:(s=e==null?void 0:e.row)==null?void 0:s.maxLength},onChange:d=>{f(d),d.target.value.replace(/[^a-zA-Z0-9-&()#.'/$%, ]/g,"").replace(/\s{2,}/g," ").replace(/\s*([-&()#.'/$%,])\s*/g,"$1").replace(/([-&()#.'/$%,])\s+/g,"$1").trimStart()},onBlur:d=>{d.stopPropagation(),Ht(d.target.value.toUpperCase(),e.row.id,"shortDescription"),_t(!1)},onKeyDown:d=>{d.stopPropagation()}})}},{field:"longDescription",headerName:"Long Description",width:250,minWidth:200,renderCell:e=>{var s;const c=e.row.longDescription||"",[i,p]=r.useState(c),f=d=>{var U;p(d.target.value[0]===" "?d.target.value.trimStart():(U=d.target.value)==null?void 0:U.replace(/[^a-zA-Z0-9-&()#.'/$%, ]/g,"").replace(/\s{2,}/g," ").replace(/\s*([-&()#.'/$%,])\s*/g,"$1").replace(/([-&()#.'/$%,])\s+/g,"$1").trimStart())};return e==null||e.row,t(Ho,{variant:"outlined",size:"small",fullWidth:!0,value:i,onFocus:()=>_t(!0),disabled:ie,placeholder:"Enter Long Description",inputProps:{style:{textTransform:"uppercase"},maxLength:(s=e==null?void 0:e.row)==null?void 0:s.maxLength},onChange:d=>{f(d),d.target.value.replace(/[^a-zA-Z0-9-&()#.'/$%, ]/g,"").replace(/\s{2,}/g," ").replace(/\s*([-&()#.'/$%,])\s*/g,"$1").replace(/([-&()#.'/$%,])\s+/g,"$1").trimStart()},onBlur:d=>{d.stopPropagation(),Ht(d.target.value.toUpperCase(),e.row.id,"longDescription"),_t(!1)},onKeyDown:d=>{d.stopPropagation()}})}},{field:"businessSegment",headerName:"Business Segment",width:250,minWidth:200,renderCell:e=>t(No,{options:[{code:"CRUDE",desc:""},{code:"INTERSTATE",desc:""},{code:"NA",desc:""}],value:e.row.businessSegment,onChange:c=>Ht(c,e.row.id,"businessSegment"),placeholder:Te("Select Business Segment"),disabled:ie,minWidth:"90%",listWidth:235})},{field:"CoCodeToExtend",headerName:"Company Code Extend To",width:250,minWidth:200,align:"center",headerAlign:"center",renderCell:e=>t(ke,{variant:"contained",size:"small",sx:{marginLeft:"4px"},onClick:()=>{Zt(!0)},disabled:ie,children:"Company Code Extend To"})},{field:"action",headerName:"Action",width:80,minWidth:80,headerAlign:"center",renderHeader:()=>t("span",{style:{fontWeight:"bold"},children:Te("Action")}),renderCell:e=>{var Ie,$e,jt;const c=e.row.id,i={id:c,...St[c]},p=jn(c);S();const f=parseInt((Ie=e.row.accountGroup)==null?void 0:Ie.FromAcct),s=parseInt(($e=e.row.accountGroup)==null?void 0:$e.ToAcct),d=parseInt((jt=e.row)==null?void 0:jt.glAccountNumber),U=!isNaN(f)&&!isNaN(s)&&d>=f&&d<=s;return t(ae,{children:t(Ln,{title:p==="success"?"Validated Successfully":p==="error"?"Validation Failed":"Click to Validate",children:t(hn,{onClick:Wo=>{Wo.stopPropagation(),d==""||d==NaN||d==null?U?fe(e.row,i,L):(Ct("error"),he("GlAccount not In Range."),ze(!0)):fe(e.row,i,L)},color:p,children:p==="error"?t(gr,{}):t(Vr,{})})})})}}],z=["chartOfAccount","companyCode","accountType","accountGroup","glAccountNumber","longDescription","businessSegment"],Ne=(e,c)=>JSON.stringify(e)===JSON.stringify(c),Ae=()=>{ze(!1)},fe=(e,c,i)=>{const p=[],f=(e==null?void 0:e.lineNumber)||F.findIndex(R=>R.id===e.id)+1,s=i.flatMap(R=>Object.values(R.data).flat());s==null||s.forEach(R=>{if(R.visibility==="Mandatory"){const Ie=c[R.jsonName];(Ie==null||typeof Ie=="string"&&Ie.trim()==="")&&p.push(`Line ${f} - ${R.fieldName}`)}});const d={companyCode:"Company Code",glAccountNumber:"General Ledger Number",businessSegment:"Business Segment",controllingArea:"Controlling Area",longDescription:"Long Text",shortDescription:"Short Text",longDescription:"Long Text",chartOfAccount:"Chart Of Account",accountType:"Account Type",accountGroup:"Account Group"};z.forEach(R=>{const Ie=e[R],$e=d[R]||R;Ie==null||typeof Ie=="string"&&Ie.trim()===""?p.push(`Line ${f} - ${$e}`):R==="glAccountNumber"&&(Ie.length!==10||!/^[a-zA-Z0-9]+$/.test(Ie))&&p.push(`Line ${f} - ${$e}`)});const U=p.length>0?"error":"success";if(X(Tr({rowId:e.id,status:U})),U==="error"){const R=[...new Set(p)];ao(R),Ee(!0)}else no(R=>({...R,[e.id]:JSON.parse(JSON.stringify(e))})),ct(R=>{const{id:Ie,...$e}=c;return{...R,[e.id]:JSON.parse(JSON.stringify($e))}}),Ct("success"),ne(R=>({...R,[e.id]:!0})),he("Validated Successfully"),ze(!0),We([!0,!1]),st(!0)},At=(e,c,i)=>{const p=[],f=(e==null?void 0:e.lineNumber)||F.findIndex(U=>U.id===e.id)+1,s=i.flatMap(U=>Object.values(U.data).flat());s==null||s.forEach(U=>{if(U.visibility==="Mandatory"){const R=c[U.jsonName];(R==null||typeof R=="string"&&R.trim()==="")&&p.push(`Line ${f} - ${U.fieldName}`)}});const d={companyCode:"Company Code",glAccountNumber:"General Ledger Number",businessSegment:"Business Segment",controllingArea:"Controlling Area",longDescription:"Long Text",shortDescription:"Short Text",longDescription:"Long Text"};return z.forEach(U=>{const R=e[U],Ie=d[U]||U;R==null||typeof R=="string"&&R.trim()===""?p.push(`Line ${f} - ${Ie}`):U==="glAccountNumber"&&(R.length!==10||!/^[a-zA-Z0-9]+$/.test(R))&&p.push(`Line ${f} - ${Ie}`)}),{missing:p,status:p.length>0?"error":"success"}},H=(e="",c="")=>{k(!0);const i=Mo(J,Lt,I,ot,so,Ce,M,e,c),p=s=>{k(!1),(s==null?void 0:s.statusCode)===Oe.STATUS_200||(s==null?void 0:s.statusCode)===Oe.STATUS_201?(at({title:Q.TITLE,message:s.message,subText:Q.SUBTEXT,buttonText:Q.BUTTONTEXT,redirectTo:Q.REDIRECT}),De(!0)):(s==null?void 0:s.statusCode)===Oe.STATUS_500||(s==null?void 0:s.statusCode)===Oe.STATUS_501?(at({title:Z.TITLE,message:s.message,subText:Z.SUBTEXT,buttonText:Z.BUTTONTEXT,redirectTo:Z.REDIRECT}),De(!0)):(ze(!0),he("Unexpected response received."))},f=s=>{k(!1),j(!0),he("Error occurred while validating the request")};A(`/${N}/massAction/validateMassGeneralLedger`,"POST",p,f,i)},$o=()=>{let e=[],c="";if(F.forEach(i=>{const p=St[i.id],{missing:f,status:s}=At(i,p,L);c=hr(F,St),ne(d=>({...d,[i.id]:s})),s==="error"?e.push(...f):(no(d=>({...d,[i.id]:JSON.parse(JSON.stringify(i))})),ct(d=>{const{id:U,...R}=p;return{...d,[i.id]:JSON.parse(JSON.stringify(R))}}),ne(d=>({...d,[i.id]:s})))}),e.length>0){const i=[...new Set(e)];ao(i),Ee(!0)}else{if(c.length>0){ho(c),Re(!0);return}H(),Ct("success"),he("All Rows Validated Successfully"),ze(!0),We([!0,!1]),st(!0)}},Vo=()=>{Ee(!1)},Ht=(e,c,i)=>{if(i==="chartOfAccount"&&(yn(e==null?void 0:e.code),gn(e==null?void 0:e.code,c),_(c),D(c)),i==="companyCode"){Sn(e==null?void 0:e.code,c),En(e==null?void 0:e.code,c),bn(e==null?void 0:e.code,c),An(e==null?void 0:e.code,c),ln(c),un(c);let f=T==null?void 0:T.filter(s=>s.code!==(e==null?void 0:e.code));X(Tn({uniqueId:c,data:f??[]})),X(it({uniqueId:Y||(C==null?void 0:C.id),keyName:"CompanyCode",data:e==null?void 0:e.code,viewID:"Comp Codes"}))}i==="accountType"&&(Qo(e==null?void 0:e.code),X(it({uniqueId:Y||(C==null?void 0:C.id),keyName:"Accounttype",data:e==null?void 0:e.code,viewID:"Type/Description"})),Ye&&!to.includes(Pt)&&xt({uniqueId:Y||(C==null?void 0:C.id),viewName:"Type/Description",plantData:"",fieldName:"Account Type",jsonName:"Accounttype",currentValue:e==null?void 0:e.code,requestId:ge==null?void 0:ge.RequestId,childRequestId:Ye})),i==="accountGroup"&&(X(it({uniqueId:Y||(C==null?void 0:C.id),keyName:"AccountGroup",data:e==null?void 0:e.code,viewID:"Type/Description"})),Ye&&!to.includes(Pt)&&xt({uniqueId:Y||(C==null?void 0:C.id),viewName:"Type/Description",plantData:"",fieldName:"Account Group",jsonName:"AccountGroup",currentValue:e==null?void 0:e.code,requestId:ge==null?void 0:ge.RequestId,childRequestId:Ye})),i==="longDescription"&&(X(it({uniqueId:Y||(C==null?void 0:C.id),keyName:"Description",data:e,viewID:"Basic Data"})),Ye&&!to.includes(Pt)&&xt({uniqueId:Y||(C==null?void 0:C.id),viewName:"Basic Data",plantData:"",fieldName:"Long Text",jsonName:"Description",currentValue:e,requestId:ge==null?void 0:ge.RequestId,childRequestId:Ye})),i==="shortDescription"&&(X(it({uniqueId:Y||(C==null?void 0:C.id),keyName:"GLname",data:e,viewID:"Basic Data"})),Ye&&!to.includes(Pt)&&xt({uniqueId:Y||(C==null?void 0:C.id),viewName:"Basic Data",plantData:"",fieldName:"Short Text",jsonName:"GLname",currentValue:e,requestId:ge==null?void 0:ge.RequestId,childRequestId:Ye}));const p=F.map(f=>f.id===c?{...f,[i]:e}:f);X(en(p))},rn=e=>{const c=e.row;Ke(c),X(yo(c==null?void 0:c.id))},cn=()=>{const c={id:fn(),chartOfAccount:"",lineNumber:F.length>0?Math.max(...F.map(i=>i.lineNumber))+10:bt+10,companyCode:"",accountType:"",accountGroup:"",glAccountNumber:"",businessSegment:"",included:!0,isNew:!0};Jt(F.length>0?Math.max(...F.map(i=>i.lineNumber))+10:bt+10),X(en([...F,c])),Ke(c),X(yo(c==null?void 0:c.id)),ne(i=>({...i,[c.id]:!1}))},Yo=(e,c)=>{w(c)},an={Description:"longDescription",GLname:"shortDescription",AccountType:"accountType",AccountGroup:"accountGroup"},Ko=(e,c)=>{const i={...e};delete i.id;for(const[p,f]of Object.entries(an)){const s=f.split(".").reduce((d,U)=>d?d[U]:void 0,c);i[p]===s&&delete i[p]}return i},Uo=e=>{const c=bo[e],i=io[e],p=F.find(U=>U.id===e),f=St[e];if(!c||!i||!p||!f)return!0;const s=Ko(f,p),d=Ko(i,c);return!Ne(c,p)||!Ne(d,s)},dn=()=>{var e,c,i,p;if(X(Po(!1)),ht==="yes"){if(xe!=null&&xe.length){let f=xe[0];const s=fn();Jt(F.length>0?Math.max(...F.map(Co=>Co.lineNumber))+10:bt+10);const d={id:s,chartOfAccount:f==null?void 0:f.chartOfAccount,companyCode:f==null?void 0:f.companyCode,lineNumber:F.length>0?Math.max(...F.map(Co=>Co.lineNumber))+10:bt+10,accountType:f==null?void 0:f.accountType,accountGroup:f==null?void 0:f.accountGroup,glAccountNumber:"0000",businessSegment:"",included:!0,isNew:!0};if(Ke(d),X(yo(d==null?void 0:d.id)),d!=null&&d.chartOfAccount&&(yn(d==null?void 0:d.chartOfAccount),gn(d==null?void 0:d.chartOfAccount,d==null?void 0:d.id),_(d==null?void 0:d.id),D(d==null?void 0:d.id)),d!=null&&d.companyCode){Sn(d==null?void 0:d.companyCode,s),En(d==null?void 0:d.companyCode,s),bn(d==null?void 0:d.companyCode,s),An(d==null?void 0:d.companyCode,s),ln(s),un(s);let Co=T==null?void 0:T.filter(ko=>ko.code!==(d==null?void 0:d.companyCode));X(Tn({uniqueId:s,data:Co??[]})),X(it({uniqueId:s,keyName:"CompanyCode",data:d==null?void 0:d.companyCode,viewID:"Comp Codes"}))}d!=null&&d.accountType&&(Qo(d==null?void 0:d.accountType,s),X(it({uniqueId:s,keyName:"Accounttype",data:d==null?void 0:d.accountType,viewID:"Type/Description"})),Ye&&!to.includes(Pt)&&xt({uniqueId:s,viewName:"Type/Description",plantData:"",fieldName:"Account Type",jsonName:"Accounttype",currentValue:d==null?void 0:d.accountType,requestId:ge==null?void 0:ge.RequestId,childRequestId:Ye})),d!=null&&d.accountGroup&&(X(it({uniqueId:s,keyName:"AccountGroup",data:d==null?void 0:d.accountGroup,viewID:"Type/Description"})),Ye&&!to.includes(Pt)&&xt({uniqueId:s,viewName:"Type/Description",plantData:"",fieldName:"Account Group",jsonName:"AccountGroup",currentValue:d==null?void 0:d.accountGroup,requestId:ge==null?void 0:ge.RequestId,childRequestId:Ye}));const U=J==null?void 0:J.rowsBodyData[f==null?void 0:f.id],R=U&&typeof U=="object"?{...Object.fromEntries(Object.entries(U).map(([Co,ko])=>[Co,Co==="GLname"||Co==="Description"?"":ko])),CompanyCode:d==null?void 0:d.companyCode}:{},Ie=J==null?void 0:J.requestHeaderData;let jt={...(J==null?void 0:J.rowsBodyData)||{},[d==null?void 0:d.id]:{...R}};const ps=[...J==null?void 0:J.rowsHeaderData,d];X(Jn({requestHeaderData:Ie,rowsBodyData:jt,rowsHeaderData:ps}))}else if((F==null?void 0:F.length)>=0){const f=fn();Jt(F.length>0?Math.max(...F.map(d=>d.lineNumber))+10:bt+10);const s={id:f,chartOfAccount:(e=ee==null?void 0:ee["Chart Of Account"])==null?void 0:e.code,companyCode:(c=ee==null?void 0:ee["Company Code"])==null?void 0:c.code,lineNumber:F.length>0?Math.max(...F.map(d=>d.lineNumber))+10:bt+10,accountType:(i=ee==null?void 0:ee["Account Type"])==null?void 0:i.code,accountGroup:(p=ee==null?void 0:ee["Account Group"])==null?void 0:p.code,glAccountNumber:"",businessSegment:"",included:!0,isNew:!0};if(Ke(s),X(yo(s==null?void 0:s.id)),s!=null&&s.chartOfAccount&&(yn(s==null?void 0:s.chartOfAccount,s==null?void 0:s.id),gn(s==null?void 0:s.chartOfAccount,s==null?void 0:s.id),_(s==null?void 0:s.id),D(s==null?void 0:s.id)),s!=null&&s.companyCode){Sn(s==null?void 0:s.companyCode,s==null?void 0:s.id),En(s==null?void 0:s.companyCode,s==null?void 0:s.id),bn(s==null?void 0:s.companyCode,s==null?void 0:s.id),An(s==null?void 0:s.companyCode,s==null?void 0:s.id),ln(s==null?void 0:s.id),un(s==null?void 0:s.id);let d=T==null?void 0:T.filter(U=>U.code!==(s==null?void 0:s.companyCode));X(Tn({uniqueId:f,data:d??[]})),X(it({uniqueId:f,keyName:"CompanyCode",data:s==null?void 0:s.companyCode,viewID:"Comp Codes"}))}s!=null&&s.accountType&&(Qo(s==null?void 0:s.accountType,s==null?void 0:s.id),X(it({uniqueId:f,keyName:"Accounttype",data:s==null?void 0:s.accountType,viewID:"Type/Description"})),Ye&&!to.includes(Pt)&&xt({uniqueId:f,viewName:"Type/Description",plantData:"",fieldName:"Account Type",jsonName:"Accounttype",currentValue:s==null?void 0:s.accountType,requestId:ge==null?void 0:ge.RequestId,childRequestId:Ye})),s!=null&&s.accountGroup&&(X(it({uniqueId:f,keyName:"AccountGroup",data:s==null?void 0:s.accountGroup,viewID:"Type/Description"})),Ye&&!to.includes(Pt)&&xt({uniqueId:f,viewName:"Type/Description",plantData:"",fieldName:"Account Group",jsonName:"AccountGroup",currentValue:s==null?void 0:s.accountGroup,requestId:ge==null?void 0:ge.RequestId,childRequestId:Ye})),X(en([...F,s])),ne(d=>({...d,[s.id]:!1})),X(it({uniqueId:f,keyName:"AccountGroup",data:s==null?void 0:s.accountGroup,viewID:"Type/Description"})),v(),Wt(s),s!=null&&s.accountType&&(Qo(s==null?void 0:s.accountType,s==null?void 0:s.id),X(it({uniqueId:f,keyName:"Accounttype",data:s==null?void 0:s.accountType,viewID:"Type/Description"})),Ye&&!to.includes(Pt)&&xt({uniqueId:f,viewName:"Type/Description",plantData:"",fieldName:"Account Type",jsonName:"Accounttype",currentValue:s==null?void 0:s.accountType,requestId:ge==null?void 0:ge.RequestId,childRequestId:Ye}))}}else cn()},Wt=(e,c)=>{const i={glAccCOACoCode:[{glAccount:e==null?void 0:e.glAccountNumber,chartOfAccount:e==null?void 0:e.chartOfAccount,companyCode:e==null?void 0:e.companyCode}]},p=s=>{const d=(s==null?void 0:s.body)||[];if(!d[0]){k(!1),X(Po(!1));return}const R=d.reduce((ko,Zo)=>({...ko,...Zo.typeNDescriptionViewDto,...Zo.controlDataViewDto,...Zo.createBankInterestViewDto,...Zo.keywordNTranslationViewDto,...Zo.informationViewDto}),{}),Ie=R&&typeof R=="object"?{...Object.fromEntries(Object.entries(R).map(([ko,Zo])=>[ko,ko==="GLname"||ko==="Description"?"":Zo])),CompanyCode:e==null?void 0:e.companyCode}:{},$e=J==null?void 0:J.requestHeaderData;let Wo={...(J==null?void 0:J.rowsBodyData)||{},[e==null?void 0:e.id]:{...Ie}};const Ts=[...J==null?void 0:J.rowsHeaderData,e],Co={requestHeaderData:$e,rowsBodyData:Wo,rowsHeaderData:Ts};u(),X(Jn(Co))},f=s=>{};A(`/${N}/data/getGeneralLedgersData`,"post",p,f,i)},jn=e=>{const c=y[e];return Uo(e),c?Uo(e)?"error":c:"default"};r.useEffect(()=>{Ke(F[0])},[]);const yn=e=>{const c=p=>{ut(p.body),X(Dt({keyName:"CompanyCode",data:p.body}))},i=p=>{tt(p)};A(`/${N}/data/getCompanyCode?chartAccount=${e}`,"get",c,i)},Mn=e=>{const c=p=>{Le(f=>({...f,"Company Code":p.body}))},i=p=>{tt(p)};A(`/${N}/data/getCompanyCode?chartAccount=${e}`,"get",c,i)},Xn=()=>{const e=i=>{W(i.body),Le(p=>({...p,"Account Type":i.body})),X(Dt({keyName:"accountType",data:i.body})),X(ue({keyName:"Accounttype",data:i.body||[],keyName2:Y??(C==null?void 0:C.id)}))},c=i=>{tt(i)};A(`/${N}/data/getGLAccountType`,"get",e,c)},Sn=(e,c="")=>{const i=f=>{X(ue({keyName:"AccountCurrency",data:f.body||[],keyName2:c!=null&&c.trim()?c:Y&&(C==null?void 0:C.id)}))},p=f=>{tt(f)};A(`/${N}/data/getAccountCurrency?companyCode=${e}`,"get",i,p)},An=(e,c)=>{const i=f=>{X(ue({keyName:"FieldStsGrp",data:f.body||[],keyName2:Y||(C==null?void 0:C.id),id:c}))},p=f=>{tt(f)};A(`/${N}/data/getFieldStatusGroup?fieldStatusVariant=${e}`,"get",i,p)},En=(e,c="")=>{const i=f=>{X(ue({keyName:"Taxcategory",data:f.body||[],keyName2:c!=null&&c.trim()?c:Y&&(C==null?void 0:C.id)}))},p=f=>{tt(f)};A(`/${N}/data/getTaxCategory?companyCode=${e}`,"get",i,p)},bn=(e,c="")=>{const i=f=>{X(ue({keyName:"HouseBank",data:f.body||[],keyName2:c!=null&&c.trim()?c:Y&&(C==null?void 0:C.id)}))},p=f=>{tt(f)};A(`/${N}/data/getHouseBank?companyCode=${e}`,"get",i,p)},Qo=(e,c="")=>{const i=f=>{X(ue({keyName:"CostEleCategory",data:f.body||[],keyName2:c!=null&&c.trim()?c:Y&&(C==null?void 0:C.id)}))},p=f=>{tt(f)};A(`/${N}/data/getCostElementCategory?accountType=${e}`,"get",i,p)},ln=(e="")=>{const c=p=>{X(ue({keyName:"ReconAcc",data:p.body||[],keyName2:e!=null&&e.trim()?e:Y&&(C==null?void 0:C.id)}))},i=p=>{tt(p)};A(`/${N}/data/getReconAccountForAccountType`,"get",c,i)},un=(e="")=>{const c=p=>{X(ue({keyName:"Planninglevel",data:p.body||[],keyName2:e!=null&&e.trim()?e:Y&&(C==null?void 0:C.id)}))},i=p=>{tt(p)};A(`/${N}/data/getPlanningLevel`,"get",c,i)},gn=(e,c="")=>{const i=f=>{var d;let s=[];(d=f==null?void 0:f.body)==null||d.map(U=>{let R={};R.code=U==null?void 0:U.AccountGroup,R.desc=U==null?void 0:U.Description,R.FromAcct=U==null?void 0:U.FromAcct,R.ToAcct=U==null?void 0:U.ToAcct,s==null||s.push(R)}),W(s),X(ue({keyName:"AccountGroup",data:s||[],keyName2:c!=null&&c.trim()?c:Y&&(C==null?void 0:C.id)})),X(Dt({keyName:"accountGroup",data:s}))},p=f=>{tt(f)};A(`/${N}/data/getAccountGroup?chartAccount=${e}`,"get",i,p)},n=e=>{const c=p=>{var s;let f=[];(s=p==null?void 0:p.body)==null||s.map(d=>{let U={};U.code=d==null?void 0:d.AccountGroup,U.desc=d==null?void 0:d.Description,U.FromAcct=d==null?void 0:d.FromAcct,U.ToAcct=d==null?void 0:d.ToAcct,f==null||f.push(U)}),Le(d=>({...d,"Account Group":f}))},i=p=>{tt(p)};A(`/${N}/data/getAccountGroup?chartAccount=${e}`,"get",c,i)},a=()=>{const e=i=>{Le(p=>({...p,"Chart Of Account":i.body})),X(Dt({keyName:"COA",data:i.body}))},c=i=>{tt(i)};A(`/${N}/data/getChartOfAccounts`,"get",e,c)},h=()=>{const e=i=>{de(i.body)},c=i=>{tt(i)};A(`/${In}/data/getBusinessSegment`,"get",e,c)};r.useEffect(()=>{a(),Xn(),h()},[]),r.useState({});const b=()=>{const e=i=>{Qe(i.body),X({type:"SET_DROPDOWN",payload:{keyName:"Segment",data:i.body}})},c=i=>{tt(i)};A(`/${In}/data/getSegment`,"get",e,c)};r.useEffect(()=>{b()},[]);const D=(e="")=>{const c=p=>{Ot(p.body),X(ue({keyName:"Language",data:p.body||[],keyName2:e!=null&&e.trim()?e:Y&&(C==null?void 0:C.id)}))},i=p=>{tt(p)};A(`/${N}/data/getLanguageKey`,"get",c,i)},_=(e="")=>{const c=p=>{Ot(p.body),X(ue({keyName:"Sortkey",data:p.body||[],keyName2:e!=null&&e.trim()?e:Y&&(C==null?void 0:C.id)}))},i=p=>{tt(p)};A(`/${N}/data/getSortKey`,"get",c,i)},V=(e="",c="")=>{k(!0);const i=Mo(J,Lt,I,ot,so,Ce,M,e,c),p=s=>{k(!1),G(!1),Ct("success"),he("General Ledger Submission saved as draft."),ze(!0),setTimeout(()=>{Ut("/requestbench")},2e3)},f=s=>{k(!1),G(!1),Ct("error"),he("Error occurred while saving the draft."),ze(!0)};A(`/${N}/massAction/generalLedgersSaveAsDraft`,"POST",p,f,i)},pe=(e="",c="")=>{lo(""),k(!0);const i=Mo(J,Lt,I,ot,so,Ce,M,e,c),p=s=>{k(!1),(s==null?void 0:s.statusCode)===Oe.STATUS_200||(s==null?void 0:s.statusCode)===Oe.STATUS_201?(at({title:Q.TITLE,message:s.message,subText:Q.SUBTEXT,buttonText:Q.BUTTONTEXT,redirectTo:Q.REDIRECT}),De(!0)):(s==null?void 0:s.statusCode)===Oe.STATUS_500||(s==null?void 0:s.statusCode)===Oe.STATUS_501?(at({title:Z.TITLE,message:s.message,subText:Z.SUBTEXT,buttonText:Z.BUTTONTEXT,redirectTo:Z.REDIRECT}),De(!0)):(ze(!0),he("Unexpected response received."))},f=s=>{k(!1),j(!0),he("Error occurred while saving the draft")};A(`/${N}/massAction/generalLedgersSubmitForReview`,"POST",p,f,i)},kt=(e,c="",i="")=>{k(!0);const p=Mo(J,Lt,I,ot,so,Ce,M,c,i),f=d=>{k(!1),(d==null?void 0:d.statusCode)===Oe.STATUS_200||(d==null?void 0:d.statusCode)===Oe.STATUS_201?(at({title:Q.TITLE,message:d.message,subText:Q.SUBTEXT,buttonText:Q.BUTTONTEXT,redirectTo:Q.REDIRECT}),De(!0)):(d==null?void 0:d.statusCode)===Oe.STATUS_500||(d==null?void 0:d.statusCode)===Oe.STATUS_501?(k(!1),at({title:Z.TITLE,message:d.message,subText:Z.SUBTEXT,buttonText:Z.BUTTONTEXT,redirectTo:Z.REDIRECT}),De(!0)):(ze(!0),he("Unexpected response received."))},s=d=>{k(!1),j(!0),he("Error occurred while validating the request")};A(e==="VALIDATE"?`/${N}/massAction/validateMassGeneralLedger`:`/${N}/massAction/createGeneralLedgersApproved`,"POST",f,s,p)},Go=(e="",c="")=>{k(!0);const i=Mo(J,Lt,I,ot,so,Ce,M,e,c),p=s=>{k(!1),j(!0),he("General Ledger submission for Approve initiated"),setTimeout(()=>{Ut("/requestbench")},1e3)},f=s=>{k(!1),j(!0),he("Error occurred while saving the draft")};A(`/${N}/massAction/generalLedgersApprovalSubmit`,"POST",p,f,i)},fo=(e,c)=>{Zt(!1)},nt=(e="",c="")=>{k(!0);const i=Mo(J,Lt,I,ot,so,Ce,M,e,c),p=s=>{k(!1),(s==null?void 0:s.statusCode)===Oe.STATUS_200||(s==null?void 0:s.statusCode)===Oe.STATUS_201?(at({title:Q.TITLE,message:s.message,subText:Q.SUBTEXT,buttonText:Q.BUTTONTEXT,redirectTo:Q.REDIRECT}),De(!0)):(s==null?void 0:s.statusCode)===Oe.STATUS_500||(s==null?void 0:s.statusCode)===Oe.STATUS_501?(at({title:Z.TITLE,message:s.message,subText:Z.SUBTEXT,buttonText:Z.BUTTONTEXT,redirectTo:Z.REDIRECT}),De(!0)):(ze(!0),he("Unexpected response received."))},f=s=>{k(!1),j(!0),he("Error occurred while saving the draft")};A(`/${N}/massAction/generalLedgersSendForCorrection`,"POST",p,f,i)},_o=(e="",c="")=>{k(!0);const i=Mo(J,Lt,I,ot,so,Ce,M,e,c),p=s=>{k(!1),(s==null?void 0:s.statusCode)===Oe.STATUS_200||(s==null?void 0:s.statusCode)===Oe.STATUS_201?(at({title:Q.TITLE,message:s.message,subText:Q.SUBTEXT,buttonText:Q.BUTTONTEXT,redirectTo:Q.REDIRECT}),De(!0)):(s==null?void 0:s.statusCode)===Oe.STATUS_500||(s==null?void 0:s.statusCode)===Oe.STATUS_501?(at({title:Z.TITLE,message:s.message,subText:Z.SUBTEXT,buttonText:Z.BUTTONTEXT,redirectTo:Z.REDIRECT}),De(!0)):(ze(!0),he("Unexpected response received."))},f=s=>{k(!1),j(!0),he("Error occurred while sending for correction")};A(`/${N}/massAction/generalLedgersSendForReview`,"POST",p,f,i)},Fs=()=>{var p,f,s,d;let e={glAccount:"",chartOfAccount:(p=ee==null?void 0:ee["Chart Of Account"])==null?void 0:p.code,postAutoOnly:"",companyCode:((f=ee==null?void 0:ee["Company Code"])==null?void 0:f.code)??"",taxCategory:"",glAcctLongText:"",postingWithoutTaxAllowed:"",blockedForPostingInCOA:"",shortText:"",blockedForPostingInCompany:"",accountGroup:((s=ee==null?void 0:ee["Account Group"])==null?void 0:s.code)??"",glAccountType:((d=ee==null?void 0:ee["Account Type"])==null?void 0:d.code)??"",fieldStatusGroup:"",openItemMgmtbyLedgerGroup:"",openItemManagement:"",reconAccountforAcctType:"",fromDate:Wn(po==null?void 0:po.createdOn[0]).format("YYYY-MM-DDT00:00:00")??"",toDate:Wn(po==null?void 0:po.createdOn[1]).format("YYYY-MM-DDT00:00:00")??"",createdBy:"",top:100,skip:0};const c=U=>{var R,Ie;if(U.statusCode===200){let $e=[];(Ie=(R=U==null?void 0:U.body)==null?void 0:R.list)==null||Ie.forEach(jt=>{let Wo={};Wo.code=jt==null?void 0:jt.GLAccount,Wo.desc=jt==null?void 0:jt.GLname,$e.push(Wo)}),Le(jt=>({...jt,"GL Account":$e}))}},i=U=>{tt(U)};A(`/${N}/data/getGeneralLedgersBasedOnAdditionalParams`,"post",c,i,e)},Rs=(e,c)=>{const i={...ee,[e]:c},p=pr(i,e);ve(p),e==="Chart Of Account"&&c&&(Mn(c==null?void 0:c.code),n(c==null?void 0:c.code)),e==="Account Group"&&Fs()},Ps=(e="",c="")=>{var d,U,R,Ie;k(!0);let i=(Ie=(R=(d=So)==null?void 0:d.MASTER_BUTTON_APIS)==null?void 0:R[(U=Xt)==null?void 0:U.GL])==null?void 0:Ie[xo];const p=Mo(J,Lt,I,ot,so,e,c),f=$e=>{k(!1),($e==null?void 0:$e.statusCode)===Oe.STATUS_200||($e==null?void 0:$e.statusCode)===Oe.STATUS_201?(at({title:Q.TITLE,message:$e.message,subText:Q.SUBTEXT,buttonText:Q.BUTTONTEXT,redirectTo:Q.REDIRECT}),De(!0)):($e==null?void 0:$e.statusCode)===Oe.STATUS_500||($e==null?void 0:$e.statusCode)===Oe.STATUS_501?(at({title:Z.TITLE,message:$e.message,subText:Z.SUBTEXT,buttonText:Z.BUTTONTEXT,redirectTo:Z.REDIRECT}),De(!0)):(ze(!0),he("Unexpected response received."))},s=$e=>{k(!1),j(!0),he("Error occurred while rejecting the request")};A(i==null?void 0:i.URL,"POST",f,s,p)};return x("div",{children:[t(ss,{open:pt,onClose:()=>De(!1),title:wt.title,message:wt.message,subText:wt.subText,buttonText:wt.buttonText,redirectTo:wt.redirectTo}),t(Rn,{openSnackBar:Do,alertMsg:Eo,handleSnackBarClose:Ae,alertType:Gt,isLoading:Kt}),Bo&&t(Je,{color:"error",children:Te("Error loading data")}),x(ae,{sx:{position:ye?"fixed":"relative",top:ye?0:"auto",left:ye?0:"auto",right:ye?0:"auto",bottom:ye?0:"auto",width:ye?"100vw":"100%",height:ye?"100vh":"auto",zIndex:ye?1004:1,backgroundColor:ye?"white":"transparent",padding:ye?"20px":"0",display:"flex",flexDirection:"column",boxShadow:ye?"0px 0px 15px rgba(0, 0, 0, 0.2)":"none",transition:"all 0.3s ease",borderRadius:"8px",border:"1px solid #e0e0e0"},children:[x(ae,{sx:{display:"flex",justifyContent:"space-between",alignItems:"center",padding:"8px 16px",backgroundColor:"#f5f5f5",borderRadius:"8px 8px 0 0"},children:[t(Je,{gutterBottom:!0,sx:{fontWeight:"bold",fontSize:"16px",mb:-2},children:Te("General Ledger Data")}),x(ae,{sx:{display:"flex",alignItems:"center",gap:1},children:[t(ke,{variant:"contained",color:"primary",startIcon:t(ks,{}),onClick:()=>{X(Po(!0)),et([]),me(null),ve({}),Ke(null),X(yo([]))},disabled:!oo,sx:{mb:1},children:Te("Add")}),t(Ln,{title:ye?"Exit Zoom":"Zoom In",sx:{zIndex:"1009"},children:t(hn,{onClick:l,color:"primary",sx:{backgroundColor:"rgba(0, 0, 0, 0.05)","&:hover":{backgroundColor:"rgba(0, 0, 0, 0.1)"}},children:ye?t(Bn,{}):t($n,{})})})]})]}),t("div",{style:{width:"100%",height:"100%",overflowX:"auto"},children:t("div",{style:{height:"100%"},children:t("div",{children:t(Un,{isLoading:Oo,rows:F,columns:g,pageSize:10,tempheight:"50vh",getRowIdValue:"id",status_onRowSingleClick:!0,callback_onRowSingleClick:rn,getRowClassName:e=>(C==null?void 0:C.id)===e.row.id?"Mui-selected":""})})})})]}),Se&&x(mo,{fullWidth:!0,open:Se,maxWidth:"lg",sx:{"&::webkit-scrollbar":{width:"1px"}},children:[t(jo,{sx:{justifyContent:"space-between",alignItems:"center",height:"max-content",padding:".5rem",paddingLeft:"1rem",backgroundColor:"#EAE9FF",display:"flex"},children:t(Je,{variant:"h6",children:Te("Add New General Ledger")})}),x(ro,{sx:{padding:".5rem 1rem",alignItems:"center",justifyContent:"center",margin:"0px 25px"},children:[x(cr,{component:"fieldset",sx:{paddingBottom:"2%"},children:[t(Je,{sx:{padding:"15px 0px",fontWeight:"600",fontSize:"15px",color:Be.palette.primary.dark},children:Te("How would you like to proceed?")}),x(ar,{row:!0,"aria-label":"profit-center-number",name:"profit-center-number",value:ht,onChange:e=>B(e.target.value),children:[t(xs,{value:"yes",control:t(bs,{}),label:Te("With Reference")}),t(xs,{value:"no",control:t(bs,{}),label:Te("Without Reference")})]})]}),ht==="yes"&&x(Tt,{container:!0,spacing:2,children:[t(Tt,{item:!0,xs:12,children:t(Tt,{container:!0,spacing:2,children:To==null?void 0:To.slice(0,4).map(e=>x(Tt,{item:!0,xs:3,children:[x(Je,{variant:"subtitle2",gutterBottom:!0,children:[e,t("span",{style:{color:"red"},children:"*"})]}),t(No,{options:(be==null?void 0:be[e])||[],value:ee[e]||"",onChange:c=>{Rs(e,c)},placeholder:Te(`Select ${e}`),minWidth:180,listWidth:306,sx:{minWidth:270,"& .MuiAutocomplete-popper":{minWidth:306}},disabled:(xe==null?void 0:xe.length)||ht==="no",isLoading:uo[e]})]},e))})}),t(Tt,{item:!0,xs:12,children:x(Tt,{container:!0,spacing:2,alignItems:"center",children:[To==null?void 0:To.slice(4).map(e=>x(Tt,{item:!0,xs:3,children:[x(Je,{variant:"subtitle2",gutterBottom:!0,children:[e,t("span",{style:{color:"red"},children:"*"})]}),t(No,{options:(be==null?void 0:be[e])||[],value:ee[e]||"",onChange:c=>{ve(i=>({...i,[e]:c}))},placeholder:Te(`Select ${e}`),minWidth:180,listWidth:306,sx:{minWidth:270,"& .MuiAutocomplete-popper":{minWidth:306}},disabled:(xe==null?void 0:xe.length)||ht==="no",isLoading:uo[e]})]},e)),(F==null?void 0:F.length)>0&&x(wo,{children:[t(Tt,{item:!0,xs:1,sx:{display:"flex",justifyContent:"center",alignItems:"flex-start",mt:3.5},children:t(Je,{variant:"body1",sx:{fontWeight:"bold",color:"gray"},children:"OR"})}),x(Tt,{item:!0,xs:3,children:[t(Je,{variant:"subtitle2",gutterBottom:!0,children:"General Ledger Line Number"}),t(No,{options:F.map(e=>({...e,code:e.lineNumber,desc:""})),value:xe[0],onChange:e=>{et(e?[e]:[]),ve({}),me(null)},minWidth:180,listWidth:266,placeholder:Te("Select General Ledger Number"),disabled:ht==="no",getOptionLabel:e=>e!=null&&e.desc?`${e.code} - ${e.desc}`:(e==null?void 0:e.code)||"",renderOption:(e,c)=>x("li",{...e,children:[t("strong",{children:c==null?void 0:c.code}),c!=null&&c.desc?` - ${c==null?void 0:c.desc}`:""]}),sx:{minWidth:270,"& .MuiAutocomplete-popper":{minWidth:306}}})]})]})]})})]})]}),x(co,{sx:{display:"flex",justifyContent:"end"},children:[t(ke,{sx:{width:"max-content",textTransform:"capitalize"},onClick:u,variant:"outlined",children:"Cancel"}),t(ke,{className:"button_primary--normal",type:"save",onClick:dn,variant:"contained",disabled:ht==="yes"&&!((xe==null?void 0:xe.length)>0||["Chart Of Account","Company Code","Account Type","Account Group","GL Account"].every(e=>{var c;return((c=ee==null?void 0:ee[e])==null?void 0:c.code)&&ee[e].code.trim()!==""})),children:"Proceed"})]})]}),lt&&t(mo,{fullWidth:!0,maxWidth:!1,open:!0,onClose:fo,sx:{display:"flex",justifyContent:"center"},disableEscapeKeyDown:!0,children:x(ae,{sx:{width:"600px !important"},children:[x(jo,{sx:{backgroundColor:"#EAE9FF",marginBottom:".5rem"},children:[t(ts,{style:{height:"20px",width:"20px",marginBottom:"-5px"}}),t("span",{children:"Select Company Code to Extend"})]}),t(ro,{sx:{paddingBottom:".5rem"},children:t(ae,{display:"flex",alignItems:"center",sx:{flex:1,padding:"22px 0px",gap:"5px"},children:t(Qn,{size:"small",multiple:!0,fullWidth:!0,options:[Xo,...ce==null?void 0:ce[C==null?void 0:C.id]],value:M==null?void 0:M[C==null?void 0:C.id],getOptionLabel:o,disableCloseOnSelect:!0,isOptionEqualToValue:(e,c)=>(e==null?void 0:e.code)===(c==null?void 0:c.code),onChange:zo,renderOption:(e,c,{selected:i})=>x("li",{...e,children:[t(vo,{checked:i,sx:{marginRight:1}}),o(c)]}),renderTags:(e,c)=>e.map((i,p)=>{const{key:f,...s}=c({index:p});return t(Zn,{label:`${i==null?void 0:i.code}`,...s},f)}),renderInput:e=>t(Ho,{...e})})})}),t(co,{children:t(ke,{onClick:()=>{Zt(!1),handleCellEdit({id:rowId,field:"views",value:te})},variant:"contained",children:"Ok"})})]})}),x(mo,{open:yt,onClose:()=>Re(!1),maxWidth:"sm",fullWidth:!0,children:[x(jo,{id:"missing-fields-dialog-title",sx:{backgroundColor:"#fff3e0",color:"#e65100",display:"flex",alignItems:"center",gap:1,fontWeight:"bold"},children:[t(pn,{fontSize:"medium"}),Te("Duplicate Description")]}),t(ro,{dividers:!0,children:Me.length===0?t(Je,{children:"No duplicates found."}):t(dr,{component:Dn,elevation:0,children:x(lr,{size:"small",children:[t(ir,{children:x(Ns,{children:[t(vn,{children:t("strong",{children:"Type"})}),t(vn,{children:t("strong",{children:"Remarks"})})]})}),t(ur,{children:Me.map((e,c)=>x(Ns,{children:[t(vn,{children:e.type}),x(vn,{children:[t("strong",{children:e.value})," found in Line(s): ",e.lines.join(", ")]})]},c))})]})})}),t(co,{children:t(ke,{onClick:()=>Re(!1),children:"Close"})})]}),x(mo,{open:Lo,onClose:Vo,"aria-labelledby":"missing-fields-dialog-title",maxWidth:"sm",fullWidth:!0,children:[x(jo,{id:"missing-fields-dialog-title",sx:{backgroundColor:"#fff3e0",color:"#e65100",display:"flex",alignItems:"center",gap:1,fontWeight:"bold"},children:[t(pn,{fontSize:"medium"}),Te("Missing Mandatory Fields")]}),x(ro,{sx:{pt:2},children:[t(Je,{variant:"body1",gutterBottom:!0,children:Te("Please complete the following mandatory fields:")}),t(Bs,{dense:!0,children:Xe.map((e,c)=>x(Ms,{disablePadding:!0,children:[t(qs,{sx:{minWidth:30},children:t(pn,{fontSize:"small",color:"warning"})}),t(vs,{primary:e})]},c))})]}),t(co,{sx:{pr:3,pb:2},children:t(ke,{onClick:Vo,variant:"contained",color:"warning",sx:{textTransform:"none",fontWeight:500},children:"Close"})})]}),C&&(Ue==="true"&&Y?x(ae,{sx:{position:le?"fixed":"relative",top:le?0:"auto",left:le?0:"auto",right:le?0:"auto",bottom:le?0:"auto",width:le?"100vw":"100%",height:le?"100vh":"auto",zIndex:le?1004:1,backgroundColor:le?"white":"transparent",padding:le?"20px":"0",marginTop:"20px",display:"flex",flexDirection:"column",boxShadow:le?"0px 0px 15px rgba(0, 0, 0, 0.2)":"none",transition:"all 0.3s ease",borderRadius:"8px",border:"1px solid #e0e0e0"},children:[x(ae,{sx:{display:"flex",justifyContent:"space-between",alignItems:"center",padding:"8px 16px",borderRadius:"8px 8px 0 0"},children:[t(Je,{variant:"h6",children:Te("View Details")}),t(Ln,{title:le?"Exit Zoom":"Zoom In",sx:{zIndex:"1009"},children:t(hn,{onClick:E,color:"primary",sx:{backgroundColor:"rgba(0, 0, 0, 0.05)","&:hover":{backgroundColor:"rgba(0, 0, 0, 0.1)"}},children:le?t(Bn,{}):t($n,{})})})]}),x(ae,{sx:{mt:3},children:[t(Ds,{value:q,onChange:Yo,indicatorColor:"primary",textColor:"primary","aria-label":"Request tabs",variant:"scrollable",scrollButtons:"auto",sx:{borderBottom:1,borderColor:"divider",mb:2},children:L.map((e,c)=>t(Ls,{label:e.tab},c))}),t(Dn,{elevation:2,sx:{p:3,borderRadius:4},children:L[q]&&(oe!=null&&oe.accountType)?t(Is,{disabled:ie,basicDataTabDetails:L[q].data,dropDownData:{CompanyCode:oe==null?void 0:oe.accountType,Country:qe,Accounttype:oe==null?void 0:oe.accountType,AccountGroup:oe==null?void 0:oe.accountGroup,Segment:Ft,Language:oe==null?void 0:oe.Language,Template:It,COArea:qt,TaxJurisdiction:gt},activeViewTab:L[q].tab,uniqueId:(C==null?void 0:C.id)||Y||((us=F[0])==null?void 0:us.id),selectedRow:C||{},module:"GeneralLedger"}):""})]})]}):x(ae,{sx:{position:le?"fixed":"relative",top:le?0:"auto",left:le?0:"auto",right:le?0:"auto",bottom:le?0:"auto",width:le?"100vw":"100%",height:le?"100vh":"auto",zIndex:le?1004:1,backgroundColor:le?"white":"transparent",padding:le?"20px":"0",marginTop:"20px",display:"flex",flexDirection:"column",boxShadow:le?"0px 0px 15px rgba(0, 0, 0, 0.2)":"none",transition:"all 0.3s ease",borderRadius:"8px",border:"1px solid #e0e0e0"},children:[x(ae,{sx:{display:"flex",justifyContent:"space-between",alignItems:"center",padding:"8px 16px",borderRadius:"8px 8px 0 0"},children:[t(Je,{variant:"h6",children:Te("View Details")}),t(Ln,{title:le?"Exit Zoom":"Zoom In",sx:{zIndex:"1009"},children:t(hn,{onClick:E,color:"primary",sx:{backgroundColor:"rgba(0, 0, 0, 0.05)","&:hover":{backgroundColor:"rgba(0, 0, 0, 0.1)"}},children:le?t(Bn,{}):t($n,{})})})]}),x(ae,{sx:{flexGrow:1,display:"flex",flexDirection:"column"},children:[t(Ds,{value:q,onChange:Yo,indicatorColor:"primary",textColor:"primary","aria-label":"Request tabs",variant:"scrollable",scrollButtons:"auto",sx:{borderBottom:1,borderColor:"divider",mb:2},children:L.map((e,c)=>t(Ls,{label:e.tab},c))}),t(Dn,{elevation:2,sx:{p:3,borderRadius:4},children:L[q]&&(oe!=null&&oe.AccountGroup)?t(Is,{disabled:ie,basicDataTabDetails:L[q].data,dropDownData:{CompanyCode:oe==null?void 0:oe.accountType,Country:qe,Accounttype:oe==null?void 0:oe.accountType,AccountGroup:oe==null?void 0:oe.accountGroup,Segment:Ft,Language:ft,Template:It,COArea:qt,TaxJurisdiction:gt},activeViewTab:L[q].tab,uniqueId:(C==null?void 0:C.id)||Y||((gs=F[0])==null?void 0:gs.id),selectedRow:C||{},module:"GeneralLedger"}):""})]})]})),t(rs,{handleSaveAsDraft:V,handleSubmitForReview:pe,handleSubmitForApprove:Go,handleSendBack:nt,handleCorrection:_o,handleRejectAndCancel:Ps,handleValidateAndSyndicate:kt,validateAllRows:$o,isSaveAsDraftEnabled:Vt,filteredButtons:sn,validateEnabled:se,moduleName:(hs=Xt)==null?void 0:hs.GL}),t(Cn,{blurLoading:He,loaderMessage:Qt})]})},ec=({reqBench:Ue,apiResponses:dt,setIsAttachmentTabEnabled:We,moduleName:st})=>{var Qo,ln,un,gn;const[m,ie]=r.useState(null);r.useState(0),r.useState({});const[C,Ke]=r.useState([]);r.useState([]);const[q,w]=r.useState([]);r.useState([]),r.useState([]),r.useState([]),r.useState([]);const[T,ut]=r.useState([]);r.useState([]),r.useState(""),r.useState([]);const[Ge,Et]=r.useState([]),[Pe,W]=r.useState([]),[gt,P]=r.useState(!1),[It,zt]=r.useState([]),[qt,de]=r.useState(!1),[qe,rt]=r.useState(!1),[Ft,Qe]=r.useState(!1);r.useState("");const[ft,Ot]=r.useState(!1),[Lo,Ee]=r.useState(""),[Xe,ao]=r.useState("success"),[oo,Ze]=r.useState(!1),[Vt,Yt]=r.useState({}),[Ao,j]=r.useState(!1),[Do,ze]=r.useState(""),{getButtonsDisplayGlobal:Eo}=ns(),[he,Gt]=r.useState({}),[Ct,Kt]=r.useState({}),[G,vt]=r.useState(!1),[ne,He]=r.useState(!1),[k,Qt]=r.useState(!1),[lo,mt]=r.useState(!1),[bo,no]=r.useState([]),[io,ct]=r.useState(!1),[ye,we]=r.useState("yes"),[le,Bt]=r.useState(null),[lt,Zt]=r.useState({}),[$t,Ve]=r.useState({}),[ht,B]=r.useState([]),[Fe,me]=r.useState({"Material No":!1}),[ee,ve]=r.useState(0);r.useState(!1),r.useState({});const[be,Le]=r.useState(!0),[xe,et]=r.useState([]),[uo,Io]=r.useState(!1);r.useState(!1);const[bt,Jt]=r.useState({title:"",message:"",subText:"",buttonText:"",redirectTo:""}),[on,_t]=r.useState(!1),se=tn(),go=Gn(),{t:yt}=Hn(),Re=O(n=>n.payload.payloadData);Re==null||Re.RequestType;let Me=O(n=>n==null?void 0:n.userManagement.taskData);const{updateChangeLogGl:ho}=cs();O(n=>{var a;return(a=n==null?void 0:n.generalLedger)==null?void 0:a.validatedRowsStatus});const pt=O(n=>{var a;return(a=n==null?void 0:n.generalLedger)==null?void 0:a.selecteddropdownDataForExtendedCode}),De=O(n=>{var a;return(a=n==null?void 0:n.generalLedger)==null?void 0:a.selectedcopyFromCompanyCode}),wt="Basic Data",[at,X]=r.useState([wt]),Be=O(n=>n.generalLedger.selectedRowId),Ut=O(n=>n.generalLedger.dropdownDataForExtendedCode),Te=O(n=>n.generalLedger.copyFromCompanyCode);let ge=Object.keys(Me).length>0;const xo=O(n=>(n.generalLedger.generalLedgerTabs||[]).filter(h=>h.tab!=="Initial Screen"));O(n=>n.changeLog.createChangeLogDataGL||[]),O(n=>n.changeLog.createChangeLogDataGL);const eo=kn(),ot=new URLSearchParams(eo.search);ot.get("reqBench");const xt=ot.get("RequestId"),y=ot.get("RequestId"),M=as(Pn.MODULE,!0,{}),$=O(n=>n.request.requestHeader),te=os[M]||(()=>({}));O(te),O(n=>n.requestHeader);const{loading:K,error:Y,fetchGeneralLedgerFieldConfig:ce}=Us(),L=O(n=>n.generalLedger.payload.rowsHeaderData);r.useState(L||[]);const Ce=O(n=>n.generalLedger.payload),je=O(n=>{var a;return((a=n.generalLedger.payload)==null?void 0:a.rowsBodyData)||{}});let _e=(un=(ln=je==null?void 0:je[(Qo=L[0])==null?void 0:Qo.id])==null?void 0:ln.Torequestheaderdata)==null?void 0:un.RequestStatus;O(n=>{var a,h;return((h=(a=n.generalLedger.payload)==null?void 0:a.rowsHeaderData[0])==null?void 0:h.companyCode)||{}});const{customError:Se}=_n();O(n=>{var a;return(a=n.generalLedger.payload)==null?void 0:a.rowsHeaderData});const I=O(n=>n.payload.dynamicKeyValues),Ye=O(n=>n.payload.filteredButtons),Nt=O(n=>n.commonFilter.GeneralLedger),Lt={code:"ALL",desc:"Select All"},Rt=["Chart Of Account","Company Code","Account Type","Account Group","GL Account"],oe=(n,a)=>{if(a==null?void 0:a.some(b=>b.code===Lt.code)){const b=Ut==null?void 0:Ut[m==null?void 0:m.id];no(b),se(On({uniqueId:m==null?void 0:m.id,data:b}))}else se(On({uniqueId:m==null?void 0:m.id,data:a}))},Oo=(n,a)=>{if(a==null?void 0:a.some(b=>b.code===Lt.code)){const b=Te==null?void 0:Te[m==null?void 0:m.id];no(b),se(wn({uniqueId:m==null?void 0:m.id,data:b}))}else se(wn({uniqueId:m==null?void 0:m.id,data:a}))},Bo=n=>n.code==="ALL"?n==null?void 0:n.desc:`${n==null?void 0:n.code} - ${n==null?void 0:n.desc}`,v=n=>n.code==="ALL"?n==null?void 0:n.desc:`${n==null?void 0:n.code}`,F=()=>{vt(!G),ne&&He(!1)};r.useEffect(()=>{var n;Ue=="true"&&(L==null?void 0:L.length)>0&&(ie(L[0]),se(yo((n=L[0])==null?void 0:n.id)))},[]),r.useEffect(()=>{!(xo!=null&&xo.length)&&ye==="yes"&&ce()},[]),r.useEffect(()=>{ye==="no"&&Zt({})},[ye]),r.useEffect(()=>{var n;(Me!=null&&Me.ATTRIBUTE_1||xt)&&Eo((n=Xt)==null?void 0:n.GL,"MDG_DYN_BTN_DT","v3")},[Me]),r.useEffect(()=>{const n=L.length>0&&L.every(a=>{const h=Vt[a.id]===!0,b=!o(a.id);return h&&b});de(n),Le(n)},[L,je,Vt,he,Ct]);const J=[{field:"included",headerName:"Included",width:100,minWidth:100,align:"center",headerAlign:"center"},{field:"lineNumber",headerName:"SL No,",width:100,minWidth:100,align:"center",headerAlign:"center"},{field:"chartOfAccount",headerName:"Chart Of Account",align:"center",headerAlign:"center",width:250,minWidth:200},{field:"accountType",headerName:"Account Type",width:250,minWidth:200},{field:"accountGroup",headerName:"Account Group",width:250,minWidth:250},{field:"glAccountNumber",headerName:"General Ledger Number",width:250,minWidth:250},{field:"businessSegment",headerName:"Business Segment",width:250,minWidth:200,renderCell:n=>t(No,{options:[{code:"CRUDE",desc:""},{code:"INTERSTATE",desc:""},{code:"NA",desc:""}],value:n.row.businessSegment,onChange:a=>sn(a,n.row.id,"businessSegment"),placeholder:yt("Select Business Segment"),disabled:!1,minWidth:"90%",listWidth:235})},{field:"CoCodeToExtend",headerName:"Company Code Extend To",width:250,minWidth:200,align:"center",headerAlign:"center",renderCell:n=>t(ke,{variant:"contained",size:"small",sx:{marginLeft:"4px"},onClick:()=>{Qt(!0)},children:"Company Code Extend To"})},{field:"CopyFromCompanyCode",headerName:"Copy From Company Code",width:250,minWidth:200,align:"center",headerAlign:"center",renderCell:n=>t(ke,{variant:"contained",size:"small",sx:{marginLeft:"4px"},onClick:()=>{mt(!0)},children:"Copy From Company Code"})}],St=(n,a)=>JSON.stringify(n)===JSON.stringify(a),Pt=()=>{Ot(!1)};r.useEffect(()=>{const n=L.length>0&&L.every(a=>Vt[a.id]===!0);de(n),rt(n)},[L,Vt]);const nn=(n="",a="")=>{j(!0);const h=Fo(Ce,$,L,Me,pt,I,De);if(h==null?void 0:h.some(D=>{var _,V;return!((_=D.CompanyCode)!=null&&_.trim())||!((V=D.CoCodeToExtend)!=null&&V.trim())}))j(!1),_t(!0);else{const D=V=>{j(!1),(V==null?void 0:V.statusCode)===Oe.STATUS_200||(V==null?void 0:V.statusCode)===Oe.STATUS_201?(Jt({title:Q.TITLE,message:V.message,subText:Q.SUBTEXT,buttonText:Q.BUTTONTEXT,redirectTo:Q.REDIRECT}),Io(!0)):(V==null?void 0:V.statusCode)===Oe.STATUS_500||(V==null?void 0:V.statusCode)===Oe.STATUS_501?(Jt({title:Z.TITLE,message:V.message,subText:Z.SUBTEXT,buttonText:Z.BUTTONTEXT,redirectTo:Z.REDIRECT}),Io(!0)):(Ot(!0),Ee("Unexpected response received."))},_=V=>{j(!1),Qe(!0),Ee("Error occurred while validating the request")};A(`/${N}/massAction/validateMassGeneralLedger`,"POST",D,_,h)}},tt=()=>{P(!1)},so=(n,a,h)=>{let b={glAccount:a,chartOfAccount:n};const D=V=>{var kt;let pe=[];(kt=V==null?void 0:V.body)==null||kt.map(Go=>{let fo={};fo.code=Go==null?void 0:Go.CompanyCode,pe==null||pe.push(fo)}),se($s({uniqueId:h,data:pe}))},_=V=>{Se(V)};A(`/${N}${So.DATA.GET_COMPANY_CODE_EXTEND_TO_GL_ACCOUNT}`,"post",D,_,b)},sn=(n,a,h)=>{if(h==="chartOfAccount"&&(E(n==null?void 0:n.code),Ht(n==null?void 0:n.code,a),Uo(a),Ko()),h==="companyCode"){z(n==null?void 0:n.code,a),Ae(n==null?void 0:n.code,a),fe(n==null?void 0:n.code,a),At(n==null?void 0:n.code),Ne(n==null?void 0:n.code,a),$o(a),Vo(a);let D=C==null?void 0:C.filter(_=>_.code!==(n==null?void 0:n.code));se(Tn({uniqueId:a,data:D})),se(it({uniqueId:Be||(m==null?void 0:m.id),keyName:"CompanyCode",data:n==null?void 0:n.code,viewID:"Comp Codes"}))}h==="accountType"&&(H(n==null?void 0:n.code),se(it({uniqueId:Be||(m==null?void 0:m.id),keyName:"Accounttype",data:n==null?void 0:n.code,viewID:"Type/Description"})),y&&!to.includes(_e)&&ho({uniqueId:Be||(m==null?void 0:m.id),viewName:"Type/Description",plantData:"",fieldName:"Account Type",jsonName:"Accounttype",currentValue:n==null?void 0:n.code,requestId:Re==null?void 0:Re.RequestId,childRequestId:y})),h==="accountGroup"&&(se(it({uniqueId:Be||(m==null?void 0:m.id),keyName:"AccountGroup",data:n==null?void 0:n.code,viewID:"Type/Description"})),y&&!to.includes(_e)&&ho({uniqueId:Be||(m==null?void 0:m.id),viewName:"Type/Description",plantData:"",fieldName:"Account Group",jsonName:"AccountGroup",currentValue:n==null?void 0:n.code,requestId:Re==null?void 0:Re.RequestId,childRequestId:y})),h==="longDescription"&&(se(it({uniqueId:Be||(m==null?void 0:m.id),keyName:"Description",data:n,viewID:"Basic Data"})),y&&!to.includes(_e)&&ho({uniqueId:Be||(m==null?void 0:m.id),viewName:"Basic Data",plantData:"",fieldName:"Long Text",jsonName:"Description",currentValue:n,requestId:Re==null?void 0:Re.RequestId,childRequestId:y})),h==="shortDescription"&&(se(it({uniqueId:Be||(m==null?void 0:m.id),keyName:"GLname",data:n,viewID:"Basic Data"})),y&&!to.includes(_e)&&ho({uniqueId:Be||(m==null?void 0:m.id),viewName:"Basic Data",plantData:"",fieldName:"Short Text",jsonName:"GLname",currentValue:n,requestId:Re==null?void 0:Re.RequestId,childRequestId:y}));const b=L.map(D=>D.id===a?{...D,[h]:n}:D);se(en(b))},po=n=>{const a=n.row;ie(a),se(yo(a==null?void 0:a.GeneralLedgerID))},Xo=()=>{const a={id:fn(),chartOfAccount:"",lineNumber:ee+1,companyCode:"",accountType:"",accountGroup:"",glAccountNumber:"",businessSegment:"",included:!0,isNew:!0};se(en([...L,a])),ie(a),se(yo(a==null?void 0:a.id)),Yt(h=>({...h,[a.id]:!1}))},To={Description:"longDescription",GLname:"shortDescription",AccountType:"accountType",AccountGroup:"accountGroup"},zo=(n,a)=>{const h={...n};delete h.id;for(const[b,D]of Object.entries(To)){const _=D.split(".").reduce((V,pe)=>V?V[pe]:void 0,a);h[b]===_&&delete h[b]}return h},o=n=>{const a=he[n],h=Ct[n],b=L.find(pe=>pe.id===n),D=je[n];if(!a||!h||!b||!D)return!0;const _=zo(D,b),V=zo(h,a);return!St(a,b)||!St(V,_)},l=(n,a,h)=>{const b=_=>{var V;if((_==null?void 0:_.statusCode)===200){let pe=[];(V=_==null?void 0:_.body)==null||V.map((kt,Go)=>{let fo={};fo.code=kt.code,fo.desc=kt.desc,pe.push(fo)}),se(Tn({uniqueId:h,data:pe}))}},D=_=>{};A(`/${N}/data/getAvailableCompCodesToExtend?coa=${n}&glAccount=${a}`,"get",b,D)},u=()=>{var n;if(ct(!1),ye==="yes"){if((L==null?void 0:L.length)>=0){fn(),ve(ee+1);const a=xe.map((h,b)=>{const D=fn();return l(h.coa,h.code,D),so(h.coa,h.code,D),{id:D,chartOfAccount:h.coa,companyCode:h.companyCode,lineNumber:ee+b+10,accountType:h.accType,accountGroup:h.accGroup,glAccountNumber:h.code,businessSegment:"",included:!0,isNew:!0}});ie(a),se(yo((n=a[0])==null?void 0:n.id)),se(en([...L,...a]))}}else Xo()};r.useEffect(()=>{ie(L[0])},[]);const E=n=>{const a=b=>{Ke(b.body),se(Dt({keyName:"CompanyCode",data:b.body}))},h=b=>{Se(b)};A(`/${N}/data/getCompanyCode?chartAccount=${n}`,"get",a,h)},S=n=>{const a=b=>{Ve(D=>({...D,"Company Code":b.body}))},h=b=>{Se(b)};A(`/${N}/data/getCompanyCode?chartAccount=${n}`,"get",a,h)},g=()=>{const n=h=>{w(h.body),Ve(b=>({...b,"Account Type":h.body})),se(Dt({keyName:"accountType",data:h.body})),se(ue({keyName:"Accounttype",data:h.body||[],keyName2:Be??(m==null?void 0:m.id)}))},a=h=>{Se(h)};A(`/${N}/data/getGLAccountType`,"get",n,a)},z=(n,a="")=>{const h=D=>{se(ue({keyName:"AccountCurrency",data:D.body||[],keyName2:a!=null&&a.trim()?a:Be&&(m==null?void 0:m.id)}))},b=D=>{Se(D)};A(`/${N}/data/getAccountCurrency?companyCode=${n}`,"get",h,b)},Ne=(n,a)=>{const h=D=>{se(ue({keyName:"FieldStsGrp",data:D.body||[],keyName2:Be||(m==null?void 0:m.id),id:a}))},b=D=>{Se(D)};A(`/${N}/data/getFieldStatusGroup?fieldStatusVariant=${n}`,"get",h,b)},Ae=(n,a="")=>{const h=D=>{se(ue({keyName:"Taxcategory",data:D.body||[],keyName2:a!=null&&a.trim()?a:Be&&(m==null?void 0:m.id)}))},b=D=>{Se(D)};A(`/${N}/data/getTaxCategory?companyCode=${n}`,"get",h,b)},fe=(n,a="")=>{const h=D=>{se(ue({keyName:"HouseBank",data:D.body||[],keyName2:a!=null&&a.trim()?a:Be&&(m==null?void 0:m.id)}))},b=D=>{Se(D)};A(`/${N}/data/getHouseBank?companyCode=${n}`,"get",h,b)},At=n=>{const a=b=>{se(ue({keyName:"AccountId",data:b.body||[],keyName2:Be||(m==null?void 0:m.id)||companyCode}))},h=b=>{Se(b)};A(`/${N}/data/getAccountId?companyCode=${n}`,"get",a,h)},H=(n,a="")=>{const h=D=>{se(ue({keyName:"CostEleCategory",data:D.body||[],keyName2:a!=null&&a.trim()?a:Be&&(m==null?void 0:m.id)}))},b=D=>{Se(D)};A(`/${N}/data/getCostElementCategory?accountType=${n}`,"get",h,b)},$o=(n="")=>{const a=b=>{se(ue({keyName:"ReconAcc",data:b.body||[],keyName2:n!=null&&n.trim()?n:Be&&(m==null?void 0:m.id)}))},h=b=>{Se(b)};A(`/${N}/data/getReconAccountForAccountType`,"get",a,h)},Vo=(n="")=>{const a=b=>{se(ue({keyName:"Planninglevel",data:b.body||[],keyName2:n!=null&&n.trim()?n:Be&&(m==null?void 0:m.id)}))},h=b=>{Se(b)};A(`/${N}/data/getPlanningLevel`,"get",a,h)},Ht=(n,a="")=>{const h=D=>{var V;let _=[];(V=D==null?void 0:D.body)==null||V.map(pe=>{let kt={};kt.code=pe==null?void 0:pe.AccountGroup,kt.desc=pe==null?void 0:pe.Description,kt.FromAcct=pe==null?void 0:pe.FromAcct,kt.ToAcct=pe==null?void 0:pe.ToAcct,_==null||_.push(kt)}),w(_),se(ue({keyName:"AccountGroup",data:_||[],keyName2:a!=null&&a.trim()?a:Be&&(m==null?void 0:m.id)})),se(Dt({keyName:"accountGroup",data:_}))},b=D=>{Se(D)};A(`/${N}/data/getAccountGroup?chartAccount=${n}`,"get",h,b)},rn=n=>{const a=b=>{var _;let D=[];(_=b==null?void 0:b.body)==null||_.map(V=>{let pe={};pe.code=V==null?void 0:V.AccountGroup,pe.desc=V==null?void 0:V.Description,pe.FromAcct=V==null?void 0:V.FromAcct,pe.ToAcct=V==null?void 0:V.ToAcct,D==null||D.push(pe)}),Ve(V=>({...V,"Account Group":D}))},h=b=>{Se(b)};A(`/${N}/data/getAccountGroup?chartAccount=${n}`,"get",a,h)},cn=()=>{const n=h=>{Ve(b=>({...b,"Chart Of Account":h.body})),se(Dt({keyName:"COA",data:h.body}))},a=h=>{Se(h)};A(`/${N}/data/getChartOfAccounts`,"get",n,a)},Yo=()=>{const n=h=>{ut(h.body)},a=h=>{Se(h)};A(`/${In}/data/getBusinessSegment`,"get",n,a)};r.useEffect(()=>{cn(),g(),Yo()},[]),r.useState({});const an=()=>{const n=h=>{Et(h.body),se({type:"SET_DROPDOWN",payload:{keyName:"Segment",data:h.body}})},a=h=>{Se(h)};A(`/${In}/data/getSegment`,"get",n,a)};r.useEffect(()=>{an()},[]);const Ko=()=>{const n=h=>{W(h.body),se(ue({keyName:"Language",data:h.body||[],keyName2:Be??(m==null?void 0:m.id)}))},a=h=>{Se(h)};A(`/${In}/data/getLanguageKey`,"get",n,a)},Uo=(n="")=>{const a=b=>{W(b.body),se(ue({keyName:"Sortkey",data:b.body||[],keyName2:n!=null&&n.trim()?n:Be&&(m==null?void 0:m.id)}))},h=b=>{Se(b)};A(`/${N}/data/getSortKey`,"get",a,h)},dn=(n="",a="")=>{j(!0);const h=Fo(Ce,$,L,Me,pt,I,De,n,a),b=_=>{j(!1),Ze(!1),ao("success"),Ee("General Ledger Submission saved as draft."),Ot(!0),setTimeout(()=>{go("/requestbench")},2e3)},D=_=>{j(!1),Ze(!1),ao("error"),Ee("Error occurred while saving the draft."),Ot(!0)};A(`/${N}/massAction/generalLedgersSaveAsDraft`,"POST",b,D,h)},Wt=(n="",a="")=>{ze(""),j(!0);const h=Fo(Ce,$,L,Me,pt,I,De,n,a),b=_=>{j(!1),(_==null?void 0:_.statusCode)===Oe.STATUS_200||(_==null?void 0:_.statusCode)===Oe.STATUS_201?(Jt({title:Q.TITLE,message:_.message,subText:Q.SUBTEXT,buttonText:Q.BUTTONTEXT,redirectTo:Q.REDIRECT}),Io(!0)):(_==null?void 0:_.statusCode)===Oe.STATUS_500||(_==null?void 0:_.statusCode)===Oe.STATUS_501?(Jt({title:Z.TITLE,message:_.message,subText:Z.SUBTEXT,buttonText:Z.BUTTONTEXT,redirectTo:Z.REDIRECT}),Io(!0)):(Ot(!0),Ee("Unexpected response received."))},D=_=>{j(!1),Qe(!0),Ee("Error occurred while saving the draft")};A(`/${N}/massAction/extendGeneralLedgersSubmitForReview`,"POST",b,D,h)},jn=(n,a="",h="")=>{j(!0);const b=Fo(Ce,$,L,Me,pt,I,De,a="",h=""),D=V=>{j(!1),Qe(!0),n==="VALIDATE"?Ee("General Ledger Validation initiated"):n==="syndicate"&&Ee("General Ledger Syndication initiated"),setTimeout(()=>{go("/requestbench")},1e3)},_=V=>{j(!1),Qe(!0),Ee("Error occurred while validating the request")};A(n==="VALIDATE"?`/${N}/massAction/validateMassGeneralLedger`:`/${N}/massAction/extendGeneralLedgersApproved`,"POST",D,_,b)},yn=(n="",a="")=>{j(!0);const h=Fo(Ce,$,L,Me,pt,I,De,n,a),b=_=>{j(!1),Qe(!0),Ee("General Ledger submission for Approve initiated"),setTimeout(()=>{go("/requestbench")},1e3)},D=_=>{j(!1),Qe(!0),Ee("Error occurred while saving the draft")};A(`/${N}/massAction/generalLedgersApprovalSubmit`,"POST",b,D,h)},Mn=(n,a)=>{Qt(!1)},Xn=(n="",a="")=>{j(!0);const h=Fo(Ce,$,L,Me,pt,I,De,n,a),b=_=>{j(!1),Qe(!0),Ee("Profit Centers submission for Approve initiated"),setTimeout(()=>{go("/requestbench")},1e3)},D=_=>{j(!1),Qe(!0),Ee("Error occurred while saving the draft")};A(`/${Vn}/massAction/profitCentersSendForCorrection`,"POST",b,D,h)},Sn=(n="",a="")=>{j(!0);const h=Fo(Ce,$,L,Me,pt,I,De,n,a),b=_=>{j(!1),Qe(!0),Ee((_==null?void 0:_.message)??"General Ledgers Sent for Correction !"),setTimeout(()=>{go("/requestbench")},1e3)},D=_=>{j(!1),Qe(!0),Ee("Error occurred while sending for correction")};A(`/${Vn}/massAction/profitCentersSendForReview`,"POST",b,D,h)},An=()=>{var b,D,_,V;let n={glAccount:"",chartOfAccount:(b=lt==null?void 0:lt["Chart Of Account"])==null?void 0:b.code,postAutoOnly:"",companyCode:((D=lt==null?void 0:lt["Company Code"])==null?void 0:D.code)??"",taxCategory:"",glAcctLongText:"",postingWithoutTaxAllowed:"",blockedForPostingInCOA:"",shortText:"",blockedForPostingInCompany:"",accountGroup:((_=lt==null?void 0:lt["Account Group"])==null?void 0:_.code)??"",glAccountType:((V=lt==null?void 0:lt["Account Type"])==null?void 0:V.code)??"",fieldStatusGroup:"",openItemMgmtbyLedgerGroup:"",openItemManagement:"",reconAccountforAcctType:"",fromDate:Wn(Nt==null?void 0:Nt.createdOn[0]).format("YYYY-MM-DDT00:00:00")??"",toDate:Wn(Nt==null?void 0:Nt.createdOn[1]).format("YYYY-MM-DDT00:00:00")??"",createdBy:"",top:100,skip:0};const a=pe=>{var kt,Go;if(pe.statusCode===200){let fo=[];(Go=(kt=pe==null?void 0:pe.body)==null?void 0:kt.list)==null||Go.forEach(nt=>{let _o={};_o.code=nt==null?void 0:nt.GLAccount,_o.desc=nt==null?void 0:nt.GLname,_o.coa=nt==null?void 0:nt.COA,_o.accType=nt==null?void 0:nt.Accounttype,_o.companyCode=nt==null?void 0:nt.CompanyCode,_o.accGroup=nt==null?void 0:nt.AccountGroup,fo.push(_o)}),Ve(nt=>({...nt,"GL Account":fo}))}},h=pe=>{Se(pe)};A(`/${N}/data/getGeneralLedgersBasedOnAdditionalParams`,"post",a,h,n)},En=(n,a)=>{Zt(h=>({...h,[n]:a})),n==="Chart Of Account"&&a&&(S(a==null?void 0:a.code),rn(a==null?void 0:a.code)),n==="Account Group"&&An()},bn=(n="",a="")=>{j(!0);const h=Fo(Ce,$,L,Me,pt,I,De,n,a),b=_=>{j(!1),Qe(!0),Ee((_==null?void 0:_.message)??"General Ledgers Rejected !"),setTimeout(()=>{go("/requestbench")},1e3)},D=_=>{j(!1),Qe(!0),Ee("Error occurred while rejecting the request")};A(`/${Vn}/massAction/profitCentersRejected`,"POST",b,D,h)};return x("div",{children:[t(ss,{open:uo,onClose:()=>Io(!1),title:bt.title,message:bt.message,subText:bt.subText,buttonText:bt.buttonText,redirectTo:bt.redirectTo}),t(Rn,{openSnackBar:ft,alertMsg:Lo,handleSnackBarClose:Pt,alertType:Xe,isLoading:oo}),Y&&t(Je,{color:"error",children:yt("Error loading data")}),t("div",{style:{padding:"0",width:"100%",margin:"0",marginTop:"20px"},children:x(ae,{sx:{position:G?"fixed":"relative",top:G?0:"auto",left:G?0:"auto",right:G?0:"auto",bottom:G?0:"auto",width:G?"100vw":"100%",height:G?"100vh":"auto",zIndex:G?1004:1,backgroundColor:G?"white":"transparent",padding:G?"20px":"0",display:"flex",flexDirection:"column",boxShadow:G?"0px 0px 15px rgba(0, 0, 0, 0.2)":"none",transition:"all 0.3s ease",borderRadius:"8px",border:"1px solid #e0e0e0"},children:[x(ae,{sx:{display:"flex",justifyContent:"space-between",alignItems:"center",padding:"8px 16px",backgroundColor:"#f5f5f5",borderRadius:"8px 8px 0 0"},children:[t(Je,{gutterBottom:!0,sx:{fontWeight:"bold",fontSize:"16px",mb:-2},children:yt("General Ledger Data")}),x(ae,{sx:{display:"flex",alignItems:"center",gap:1},children:[t(ke,{variant:"contained",color:"primary",startIcon:t(ks,{}),onClick:()=>{ct(!0),B([]),Bt(null),Zt({}),ie(null),se(yo([]))},sx:{mb:1},children:yt("Add")}),t(Ln,{title:G?"Exit Zoom":"Zoom In",sx:{zIndex:"1009"},children:t(hn,{onClick:F,color:"primary",sx:{backgroundColor:"rgba(0, 0, 0, 0.05)","&:hover":{backgroundColor:"rgba(0, 0, 0, 0.1)"}},children:G?t(Bn,{}):t($n,{})})})]})]}),t("div",{style:{width:"100%",height:"100%",overflowX:"auto"},children:t("div",{style:{height:"100%"},children:t("div",{children:t(Un,{isLoading:K,rows:L,columns:J,pageSize:10,tempheight:"50vh",getRowIdValue:"id",status_onRowSingleClick:!0,callback_onRowSingleClick:po,getRowClassName:n=>(m==null?void 0:m.id)===n.row.id?"Mui-selected":""})})})})]})}),io&&x(mo,{fullWidth:!0,open:!0,maxWidth:"lg",sx:{"&::webkit-scrollbar":{width:"1px"}},children:[x(ae,{sx:{backgroundColor:"#e3f2fd",padding:"1rem 1.5rem",display:"flex",alignItems:"center"},children:[t(Fn,{color:"primary",sx:{marginRight:"0.5rem"}}),t(Je,{variant:"h6",component:"div",color:"primary",children:"Extend Search Filter(s)"})]}),t(ro,{sx:{padding:".5rem 1rem",alignItems:"center",justifyContent:"center",margin:"0px 25px"},children:x(Tt,{container:!0,spacing:2,children:[t(Tt,{item:!0,xs:12,children:t(Tt,{container:!0,spacing:2,children:Rt==null?void 0:Rt.slice(0,4).map(n=>t(Tt,{item:!0,xs:3,children:t(No,{options:($t==null?void 0:$t[n])||[],value:lt[n]||"",onChange:a=>{En(n,a)},placeholder:yt(`Select ${n}`),minWidth:180,listWidth:306,sx:{minWidth:270,"& .MuiAutocomplete-popper":{minWidth:306}},disabled:(ht==null?void 0:ht.length)||ye==="no",isLoading:Fe[n]})},n))})}),t(Tt,{item:!0,xs:12,children:t(Tt,{container:!0,spacing:2,alignItems:"center",children:Rt==null?void 0:Rt.slice(4).map(n=>t(Tt,{item:!0,xs:3,children:t(qo,{param:{key:"generalLedger",label:"general Ledger"},dropDownData:{generalLedger:($t==null?void 0:$t[n])||[]},selectedValues:{generalLedger:xe||[]},handleSelectAll:a=>{const h=($t==null?void 0:$t[a])||[];(xe==null?void 0:xe.length)===h.length?et([]):et(h)},handleSelectionChange:(a,h)=>{et(h||[])},formatOptionLabel:a=>typeof a=="string"?a:(a==null?void 0:a.code)||"",isSelectAll:!0,errors:{}})},n))})})]})}),x(co,{sx:{display:"flex",justifyContent:"end"},children:[t(ke,{sx:{width:"max-content",textTransform:"capitalize"},onClick:()=>ct(!1),variant:"outlined",children:yt("Cancel")}),t(ke,{className:"button_primary--normal",type:"save",onClick:u,variant:"contained",children:yt("OK")})]})]}),x(mo,{open:on,onClose:()=>_t(!1),maxWidth:"sm",fullWidth:!0,children:[x(jo,{id:"missing-fields-dialog-title",sx:{backgroundColor:"#fff3e0",color:"#e65100",display:"flex",alignItems:"center",gap:1,fontWeight:"bold"},children:[t(pn,{fontSize:"medium"}),yt("Validation Of Comany Codes.")]}),t(ro,{dividers:!0,children:t(Je,{children:"Company Code or CoCodeToExtend cannot be blank."})}),t(co,{children:t(ke,{onClick:()=>_t(!1),children:"Close"})})]}),k&&t(mo,{fullWidth:!0,maxWidth:!1,open:!0,onClose:Mn,sx:{display:"flex",justifyContent:"center"},disableEscapeKeyDown:!0,children:x(ae,{sx:{width:"600px !important"},children:[x(jo,{sx:{backgroundColor:"#EAE9FF",marginBottom:".5rem"},children:[t(ts,{style:{height:"20px",width:"20px",marginBottom:"-5px"}}),t("span",{children:"Select Company Code to Extend"})]}),t(ro,{sx:{paddingBottom:".5rem"},children:t(ae,{display:"flex",alignItems:"center",sx:{flex:1,padding:"22px 0px",gap:"5px"},children:t(Qn,{size:"small",multiple:!0,fullWidth:!0,options:[Lt,...Ut==null?void 0:Ut[m==null?void 0:m.id]],value:(pt==null?void 0:pt[m==null?void 0:m.id])??[],getOptionLabel:v,disableCloseOnSelect:!0,isOptionEqualToValue:(n,a)=>(n==null?void 0:n.code)===(a==null?void 0:a.code),onChange:oe,renderOption:(n,a,{selected:h})=>x("li",{...n,children:[t(vo,{checked:h,sx:{marginRight:1}}),Bo(a)]}),renderTags:(n,a)=>n.map((h,b)=>{const{key:D,..._}=a({index:b});return t(Zn,{label:`${h==null?void 0:h.code}`,..._},D)}),renderInput:n=>t(Ho,{...n})})})}),t(co,{children:t(ke,{onClick:()=>{Qt(!1),handleCellEdit({id:rowId,field:"views",value:at})},variant:"contained",children:"Ok"})})]})}),lo&&t(mo,{fullWidth:!0,maxWidth:!1,open:!0,onClose:Mn,sx:{display:"flex",justifyContent:"center"},disableEscapeKeyDown:!0,children:x(ae,{sx:{width:"600px !important"},children:[x(jo,{sx:{backgroundColor:"#EAE9FF",marginBottom:".5rem"},children:[t(ts,{style:{height:"20px",width:"20px",marginBottom:"-5px"}}),t("span",{children:"Copy From Company Code"})]}),t(ro,{sx:{paddingBottom:".5rem"},children:t(ae,{display:"flex",alignItems:"center",sx:{flex:1,padding:"22px 0px",gap:"5px"},children:t(Qn,{size:"small",multiple:!0,fullWidth:!0,options:[Lt,...Te==null?void 0:Te[m==null?void 0:m.id]],value:(De==null?void 0:De[m==null?void 0:m.id])??[],getOptionLabel:v,disableCloseOnSelect:!0,disabled:ge,isOptionEqualToValue:(n,a)=>n.code===a.code,onChange:Oo,renderOption:(n,a,{selected:h})=>x("li",{...n,children:[t(vo,{checked:h,sx:{marginRight:1}}),v(a)]}),renderTags:(n,a)=>n.map((h,b)=>{const{key:D,..._}=a({index:b});return t(Zn,{label:`${h.code}`,..._},D)}),renderInput:n=>t(Ho,{...n})})})}),t(co,{children:t(ke,{onClick:()=>{mt(!1),handleCellEdit({id:rowId,field:"views",value:at})},variant:"contained",children:"Ok"})})]})}),x(mo,{open:gt,onClose:tt,"aria-labelledby":"missing-fields-dialog-title",maxWidth:"sm",fullWidth:!0,children:[x(jo,{id:"missing-fields-dialog-title",sx:{backgroundColor:"#fff3e0",color:"#e65100",display:"flex",alignItems:"center",gap:1,fontWeight:"bold"},children:[t(pn,{fontSize:"medium"}),yt("Missing Mandatory Fields")]}),x(ro,{sx:{pt:2},children:[t(Je,{variant:"body1",gutterBottom:!0,children:yt("Please complete the following mandatory fields:")}),t(Bs,{dense:!0,children:It.map((n,a)=>x(Ms,{disablePadding:!0,children:[t(qs,{sx:{minWidth:30},children:t(pn,{fontSize:"small",color:"warning"})}),t(vs,{primary:n})]},a))})]}),t(co,{sx:{pr:3,pb:2},children:t(ke,{onClick:tt,variant:"contained",color:"warning",sx:{textTransform:"none",fontWeight:500},children:"Close"})})]}),t(rs,{handleSaveAsDraft:dn,handleSubmitForReview:Wt,handleSubmitForApprove:yn,handleSendBack:Xn,handleCorrection:Sn,handleRejectAndCancel:bn,handleValidateAndSyndicate:jn,validateAllRows:nn,isSaveAsDraftEnabled:qe,filteredButtons:Ye,validateEnabled:be,moduleName:(gn=Xt)==null?void 0:gn.GL}),t(Cn,{blurLoading:Ao,loaderMessage:Do})]})},tc=["Request Header","General Ledger List","Attachments & Comments","Preview"],jc=()=>{var Re,Me,ho,pt,De,wt,at,X,Be,Ut,Te,ge,xo,eo,ot,xt;const{t:Ue}=Hn(),dt=O(y=>y.CommonStepper.activeStep),We=O(y=>y.generalLedger.payload.requestHeaderData),st=O(y=>{var M;return(M=y.request.requestHeader)==null?void 0:M.requestId}),m=O(y=>y.request.requestHeader),{fetchedGeneralLedgerData:ie,fetchReqBenchData:C}=O(y=>y.generalLedger),Ke=O(y=>y.request.requestHeader),q=tn(),[w,T]=r.useState(!1),[ut,Ge]=r.useState(!1),[Et,Pe]=r.useState(!1),[W,gt]=r.useState(!1),P=kn(),[It,zt]=r.useState([]),[qt,de]=r.useState([]),qe=Gn(),[rt,Ft]=r.useState(!1),[Qe,ft]=r.useState(""),[Ot,Lo]=r.useState(!1),[Ee,Xe]=r.useState(!1),[ao,oo]=r.useState(!1),[Ze,Vt]=r.useState(!1),[Yt,Ao]=r.useState([]),[j,Do]=r.useState(""),[ze,Eo]=r.useState(),[he,Gt]=r.useState(!1),{customError:Ct}=_n(),Kt=y=>{q(Nn(y))},G=P.state,vt=new URLSearchParams(P.search),ne=vt.get("RequestId"),He=vt.get("RequestId"),k=vt.get("RequestType");O(y=>{var M;return((M=y.AllDropDown)==null?void 0:M.dropDown)||{}});const Qt=O(y=>y.applicationConfig),lo=()=>{gt(!0)},mt=O(y=>y.generalLedger.payload),bo=(Re=mt==null?void 0:mt.requestHeaderData)==null?void 0:Re.TemplateName,{toPDF:no,targetRef:io}=fr({filename:"my-component.pdf"});O(y=>y.profitCenterTab);const ct=O(y=>y.payload.dynamicKeyValues);let ye=O(y=>y==null?void 0:y.userManagement.taskData);const we=vt.get("reqBench")==="true"||ye&&Object.keys(ye).length>0?"true":"false",le=(G==null?void 0:G.childRequestIds)!=="Not Available"&&typeof ye=="object"&&ye!==null&&Object.keys(ye).length===0&&we==="true",Bt=()=>{Vt(!0)},lt=y=>{Vt(y)},Zt=y=>{let M=k===re.CREATE_WITH_UPLOAD?"getAllGLFromExcelWithLimitedFields":k===re.CHANGE_WITH_UPLOAD?"getAllGLFromExcelWithLimitedFieldsForMassChange":"";ft("Initiating Excel Upload"),Ft(!0);const $=new FormData;[...y].forEach(Y=>$.append("files",Y)),$.append("dtName",k===re.CREATE_WITH_UPLOAD||k===re.EXTEND_WITH_UPLOAD?"MDG_GL_FIELD_CONFIG":"MDG_GL_CHANGE_TEMPLATE_DT"),$.append("version",k===re.CREATE_WITH_UPLOAD||k===re.EXTEND_WITH_UPLOAD||k===re.CHANGE_WITH_UPLOAD?"v3":"v4"),$.append("requestId",ne||""),$.append("IsSunoco","false"),$.append("screenName",k||"");const te=Y=>{var ce,L;Y.statusCode===200?(Xe(!1),Ft(!1),ft(""),qe((ce=Mt)==null?void 0:ce.REQUEST_BENCH)):(Xe(!1),Ft(!1),ft(""),qe((L=Mt)==null?void 0:L.REQUEST_BENCH))},K=Y=>{var ce;Ft(!1),ft(""),qe((ce=Mt)==null?void 0:ce.REQUEST_BENCH)};A(`/${N}/massAction/${M}`,"postformdata",te,K,$)},$t=(y,M,$,te)=>{const K=ce=>{var je,_e;q(Dt({keyName:"CompanyCode",data:ce.body}));let L=(je=ce==null?void 0:ce.body)==null?void 0:je.filter(Se=>Se.code!==$);const Ce=te==null?void 0:te.split(",");if((Ce==null?void 0:Ce.length)>0){const Se=(_e=ce==null?void 0:ce.body)==null?void 0:_e.filter(I=>Ce.includes(I.code));q(On({uniqueId:M,data:Se}))}q(Tn({uniqueId:M,data:L}))},Y=ce=>{};A(`/${N}/data/getCompanyCode?chartAccount=${y}`,"get",K,Y)},Ve=(y,M,$)=>{let te={glAccount:$,chartOfAccount:y};const K=ce=>{var Ce;let L=[];(Ce=ce==null?void 0:ce.body)==null||Ce.map(je=>{let _e={};_e.code=je==null?void 0:je.CompanyCode,L==null||L.push(_e)}),q($s({uniqueId:M,data:L}))},Y=ce=>{Ct(ce)};A(`/${N}${So.DATA.GET_COMPANY_CODE_EXTEND_TO_GL_ACCOUNT}`,"post",K,Y,te)},ht=y=>{const M=te=>{q(ue({keyName:"Sortkey",data:te.body||[],keyName2:y}))},$=te=>{};A(`/${N}/data/getSortKey`,"get",M,$)},B=y=>{const M=te=>{q(Dt({keyName:"accountType",data:te.body})),q(ue({keyName:"Accounttype",data:te.body||[],keyName2:y}))},$=te=>{};A(`/${N}/data/getGLAccountType`,"get",M,$)},Fe=(y,M)=>{const $=K=>{q(ue({keyName:"AccountCurrency",data:K.body||[],keyName2:M}))},te=K=>{};A(`/${N}/data/getAccountCurrency?companyCode=${y}`,"get",$,te)},me=(y,M)=>{const $=K=>{q(ue({keyName:"Taxcategory",data:K.body||[],keyName2:M}))},te=K=>{};A(`/${N}/data/getTaxCategory?companyCode=${y}`,"get",$,te)},ee=(y,M)=>{const $=K=>{q(ue({keyName:"HouseBank",data:K.body||[],keyName2:M}))},te=K=>{};A(`/${N}/data/getHouseBank?companyCode=${y}`,"get",$,te)},ve=(y,M)=>{const $=K=>{q(ue({keyName:"AccountId",data:K.body||[],keyName2:M}))},te=K=>{};A(`/${N}/data/getAccountId?companyCode=${y}`,"get",$,te)},be=(y,M)=>{const $=K=>{q(ue({keyName:"CostEleCategory",data:K.body||[],keyName2:M}))},te=K=>{};A(`/${N}/data/getCostElementCategory?accountType=${y}`,"get",$,te)},Le=(y,M)=>{const $=K=>{q(ue({keyName:"ReconAcc",data:K.body||[],keyName2:M}))},te=K=>{};A(`/${N}/data/getReconAccountForAccountType`,"get",$,te)},xe=(y,M)=>{const $=K=>{q(ue({keyName:"Planninglevel",data:K.body||[],keyName2:M}))},te=K=>{};A(`/${N}/data/getPlanningLevel`,"get",$,te)},et=(y,M)=>{const $=K=>{q(ue({keyName:"FieldStsGrp",data:K.body||[],keyName2:M}))},te=K=>{};A(`/${N}/data/getFieldStatusGroup?fieldStatusVariant=${y}`,"get",$,te)},uo=(y,M)=>{const $=K=>{var ce;let Y=[];(ce=K==null?void 0:K.body)==null||ce.map(L=>{let Ce={};Ce.code=L==null?void 0:L.AccountGroup,Ce.desc=L==null?void 0:L.Description,Ce.FromAcct=L==null?void 0:L.FromAcct,Ce.ToAcct=L==null?void 0:L.ToAcct,Y==null||Y.push(Ce)}),q(ue({keyName:"AccountGroup",data:Y||[],keyName2:M})),q(Dt({keyName:"accountGroup",data:Y}))},te=K=>{};A(`/${N}/data/getAccountGroup?chartAccount=${y}`,"get",$,te)},bt={generalLedgerDetails:k===((Me=re)==null?void 0:Me.CREATE)||k===((ho=re)==null?void 0:ho.CREATE_WITH_UPLOAD)?Mo(mt,m,ne,ye,ct,"",""):Ro(Ke,Ke,ye,we,C,ie),dtName:k===((pt=re)==null?void 0:pt.CREATE)||k===((De=re)==null?void 0:De.CREATE_WITH_UPLOAD)?"MDG_GL_FIELD_CONFIG":"MDG_CHANGE_TEMPLATE_DT",version:(k===((wt=re)==null?void 0:wt.CREATE)||k===((at=re)==null?void 0:at.CREATE_WITH_UPLOAD),"v3"),requestId:ne||"",scenario:k===((X=re)==null?void 0:X.CREATE)||k===((Be=re)==null?void 0:Be.CREATE_WITH_UPLOAD)?(Ut=re)==null?void 0:Ut.CREATE_WITH_UPLOAD:(Te=re)==null?void 0:Te.CHANGE_WITH_UPLOAD,templateName:"",region:"US"},Jt=y=>{const M=te=>{q(ue({keyName:"Language",data:te.body||[],keyName2:y}))},$=te=>{};A(`/${N}/data/getLanguageKey`,"get",M,$)},on=async y=>{var $,te,K;const M=(G==null?void 0:G.childRequestIds)!=="Not Available";if(we==="true"){const Y={sort:"id,asc",parentId:M?"":y,massCreationId:M&&(k==="Create"||k==="Mass Create"||k===(($=re)==null?void 0:$.CREATE_WITH_UPLOAD))?y:"",massChangeId:M&&(k==="Change"||k==="Mass Change"||k===((te=re)==null?void 0:te.CHANGE_WITH_UPLOAD))?y:"",massExtendId:M&&k===((K=re)==null?void 0:K.EXTEND)?y:"",page:0,size:10};return new Promise((ce,L)=>{const Ce=async _e=>{var Rt,oe,Oo,Bo;const Se=(_e==null?void 0:_e.body)||[];q(yo((Rt=Se[0])==null?void 0:Rt.GeneralLedgerID)),((oe=Se[0])==null?void 0:oe.GeneralLedgerID)==null?q(Po(!0)):q(Po(!1));const I=(Oo=_e==null?void 0:_e.body[0])==null?void 0:Oo.Torequestheaderdata,Ye=(Bo=_e==null?void 0:_e.body[0])==null?void 0:Bo.TotalIntermediateTasks;q($r({RequestId:I==null?void 0:I.RequestId,RequestPrefix:I==null?void 0:I.RequestPrefix,ReqCreatedBy:I==null?void 0:I.ReqCreatedBy,ReqCreatedOn:I==null?void 0:I.ReqCreatedOn,ReqUpdatedOn:I==null?void 0:I.ReqUpdatedOn,RequestType:I==null?void 0:I.RequestType,RequestDesc:I==null?void 0:I.RequestDesc,RequestStatus:I==null?void 0:I.RequestStatus,RequestPriority:I==null?void 0:I.RequestPriority,FieldName:I==null?void 0:I.FieldName,TemplateName:I==null?void 0:I.TemplateName,Division:I==null?void 0:I.Division,region:I==null?void 0:I.region,leadingCat:I==null?void 0:I.leadingCat,firstProd:I==null?void 0:I.firstProd,launchDate:I==null?void 0:I.launchDate,isBifurcated:I==null?void 0:I.isBifurcated,screenName:I==null?void 0:I.screenName,TotalIntermediateTasks:Ye})),zt(Se);const Nt=Ur(Se),Lt=Se.map(async v=>{const F=v==null?void 0:v.GeneralLedgerID,J=[];if(v!=null&&v.COA&&(J.push($t(v==null?void 0:v.COA,F,v==null?void 0:v.CompanyCode,v==null?void 0:v.CoCodeToExtend)),J.push(Ve(v==null?void 0:v.COA,F,v==null?void 0:v.GLAccount)),J.push(B(F)),J.push(uo(v==null?void 0:v.COA,F)),J.push(Jt(F)),J.push(ht(F))),v!=null&&v.CompanyCode){J.push(Fe(v==null?void 0:v.CompanyCode,F)),J.push(me(v==null?void 0:v.CompanyCode,F)),J.push(ee(v==null?void 0:v.CompanyCode,F)),J.push(ve(v==null?void 0:v.CompanyCode,F)),J.push(Le(v==null?void 0:v.CompanyCode,F)),J.push(xe(v==null?void 0:v.CompanyCode,F)),J.push(et(v==null?void 0:v.CompanyCode,F));const St=[{code:v==null?void 0:v.CompanyCode}];q(wn({uniqueId:F,data:St}))}return v!=null&&v.Accounttype&&J.push(be(v==null?void 0:v.Accounttype,F)),Promise.all(J)});await Promise.all(Lt),setTimeout(()=>{q(Jn(Nt==null?void 0:Nt.payload))},2e3),q(Gs(Nt==null?void 0:Nt.payload)),oo(!0),ce()},je=_e=>{L(_e)};A(`/${N}/data/displayMassGeneralLedger`,"post",Ce,je,Y)})}},_t=()=>{const y={decisionTableId:null,decisionTableName:"MDG_ATTACHMENTS_LIST_DT",version:"v1",rulePolicy:null,validityDate:null,conditions:[{"MDG_CONDITIONS.MDG_ATTACHMENTS_OBJECT_TYPE":"Material","MDG_CONDITIONS.MDG_ATTACHMENTS_SCENARIO":"Create","MDG_CONDITIONS.MDG_ATTACHMENTS_REG_COMP_CODE":1}],systemFilters:null,systemOrders:null,filterString:null},M=K=>{var Y,ce,L;if((K==null?void 0:K.statusCode)===200){const Ce=((L=(ce=(Y=K==null?void 0:K.data)==null?void 0:Y.result)==null?void 0:ce[0])==null?void 0:L.MDG_ATTACHMENTS_ACTION_TYPE)??[];Ce.map((je,_e)=>({id:_e,attachmentName:je==null?void 0:je.MDG_ATTACHMENTS_NAME,changeEntryFields:je==null?void 0:je.MDG_ATTACH_CHNG_ENT_FIELDS})),Ao(Ce)}},$=K=>{},te=Qt.environment==="localhost"?`/${mn}/rest/v1/invoke-rules`:`/${mn}/v1/invoke-rules`;A(te,"post",M,$,y)},se=()=>{var te,K;let y="";(k===re.CREATE_WITH_UPLOAD||k===re.CREATE)&&(y={dtName:"MDG_GL_FIELD_CONFIG",version:"v3",requestId:He,scenario:(te=re)==null?void 0:te.CREATE_WITH_UPLOAD,isChild:G!=null&&G.isChildRequest?!0:!!le}),(k===re.CHANGE_WITH_UPLOAD||k===re.CHANGE)&&(y={dtName:"MDG_GL_CHANGE_TEMPLATE_DT",version:"v3",requestId:He,scenario:(K=re)==null?void 0:K.CHANGE_WITH_UPLOAD,isChild:G!=null&&G.isChildRequest?!0:!!Object.keys(ye).length});const M=Y=>{const ce=URL.createObjectURL(Y),L=document.createElement("a");L.href=ce,L.setAttribute("download",`${y!=null&&y.scenario?y==null?void 0:y.scenario:"Mass_Create"}_Data Export.xlsx`),document.body.appendChild(L),L.click(),document.body.removeChild(L),URL.revokeObjectURL(ce),Ft(!1),ft(""),Eo(`${y!=null&&y.scenario?y==null?void 0:y.scenario:"Mass_Create"}_Data Export.xlsx has been exported successfully.`)},$=Y=>{Ct(Y)};A(`/${N}${So.EXCEL.DOWNLOAD_EXCEL_GL}`,"postandgetblob",M,$,y)};r.useEffect(()=>{w&&de([!0])},[w]),r.useEffect(()=>{Do(Cr("GL"))},[]),r.useEffect(()=>{mr(Pn.MODULE,Xt.GL),_t(),q(Dt({keyName:"Region",data:yr}))},[]),r.useEffect(()=>{},[mt]),r.useEffect(()=>((async()=>{He?(await on(He),(k===re.CHANGE_WITH_UPLOAD&&!(G!=null&&G.length)||k===re.CREATE_WITH_UPLOAD||k===re.EXTEND_WITH_UPLOAD)&&((G==null?void 0:G.reqStatus)===xn.DRAFT||(G==null?void 0:G.reqStatus)===xn.UPLOAD_FAILED)?(q(Nn(0)),T(!1),Pe(!1)):(q(Nn(1)),T(!0),Pe(!0)),Ge(!0)):q(Nn(0))})(),()=>{q(Sr()),q(Ar()),q(_s({})),q(Er()),q(br()),q(xr()),q(Dt({keyName:"FieldName",data:[]}))}),[ne,q]);const go=()=>{var y,M,$;ne&&!we?qe((y=Mt)==null?void 0:y.MY_TASK):we?qe((M=Mt)==null?void 0:M.REQUEST_BENCH):!ne&&!we&&qe(($=Mt)==null?void 0:$.MASTER_DATA_GL)},yt=()=>{Lo(!1)};return x("div",{children:[t(Wr,{dialogState:he,closeReusableDialog:()=>Gt(!1),module:(ge=Xt)==null?void 0:ge.GL,isHierarchyCheck:!1}),x(ae,{sx:{padding:2},children:[x(Tt,{sx:{display:"flex",justifyContent:"space-between",alignItems:"center"},children:[ne||st?x(ae,{children:[x(Je,{variant:"h6",sx:{mb:1,textAlign:"left",display:"flex",alignItems:"center",gap:1},children:[t(zr,{sx:{fontSize:"1.5rem"}}),Ue("Request Header ID:")," ",t("span",{children:st?m==null?void 0:m.requestId:`${ne}`})]}),bo&&x(Je,{variant:"h6",sx:{mb:1,textAlign:"left",display:"flex",alignItems:"center",gap:1},children:[t(Fn,{sx:{fontSize:"1.5rem"}}),"Template Name: ",t("span",{children:bo})]})]}):t("div",{style:{flex:1}}),Ze&&t(jr,{module:Xt.GL,open:!0,closeModal:lt,requestId:ne,requestType:k}),dt===1&&x(ae,{sx:{display:"flex",justifyContent:"flex-end",gap:"1rem"},children:[t(ke,{variant:"outlined",size:"small",title:"Error Report",disabled:!He,onClick:()=>Gt(!0),color:"primary",children:t(Gr,{sx:{padding:"2px"}})}),t(ke,{variant:"outlined",disabled:!He,size:"small",onClick:Bt,title:"Change Log",children:t(Yr,{sx:{padding:"2px"}})}),t(ke,{variant:"outlined",disabled:!He,size:"small",onClick:se,title:"Export Excel",children:t(Kr,{sx:{padding:"2px"}})})]})]}),t(hn,{onClick:()=>{var y,M,$;if(we&&!((M=_r)!=null&&M.includes((y=mt==null?void 0:mt.requestHeaderData)==null?void 0:y.RequestStatus))){qe(($=Mt)==null?void 0:$.MASTER_DATA_GL);return}Lo(!0)},color:"primary","aria-label":"upload picture",component:"label",sx:{left:"-10px"},title:"Back",children:t(kr,{sx:{fontSize:"25px",color:"#000000"}})}),t(Dr,{nonLinear:!0,activeStep:dt,sx:{display:"flex",alignItems:"center",justifyContent:"center",margin:"25px 14%",marginTop:"-35px"},children:tc.map((y,M)=>t(Nr,{completed:qt[M],children:t(Lr,{color:"error",disabled:M===1&&!w||M===2&&!Et,onClick:()=>Kt(M),sx:{fontSize:"50px",fontWeight:"bold"},children:t("span",{style:{fontSize:"15px",fontWeight:"bold"},children:Ue(y)})})},y))}),dt===0&&x(wo,{children:[t(Jr,{apiResponse:It,reqBench:we,downloadClicked:W,setDownloadClicked:gt,setIsSecondTabEnabled:T,setIsAttachmentTabEnabled:Pe}),(k===re.CHANGE_WITH_UPLOAD||k===re.CREATE_WITH_UPLOAD||k===re.EXTEND_WITH_UPLOAD)&&((G==null?void 0:G.reqStatus)==xn.DRAFT&&!((xo=G==null?void 0:G.material)!=null&&xo.length)||(G==null?void 0:G.reqStatus)==xn.UPLOAD_FAILED)&&t(Xr,{handleDownload:lo,setEnableDocumentUpload:Xe,enableDocumentUpload:Ee,handleUploadMaterial:Zt})]}),dt===1&&We.RequestType&&(We.RequestType==="Change"||We.RequestType==="Change with Upload"?t(Ws,{reqBench:we,requestId:ne,apiResponses:It,setIsAttachmentTabEnabled:Pe,setCompleted:de,downloadClicked:W,setDownloadClicked:gt,moduleName:Xt.GL,isDisabled:!0}):We.RequestType==="Extend"||We.RequestType==="Extend with Upload"?t(ec,{reqBench:we,requestId:ne,apiResponses:It,setIsAttachmentTabEnabled:!0,setCompleted:de,downloadClicked:W,setDownloadClicked:gt,moduleName:Xt.GL,isDisabled:le}):t(wo,{children:t(wr,{reqBench:we,apiResponses:It,setCompleted:de,setIsAttachmentTabEnabled:Pe,moduleName:Xt.GL,isDisabled:le})})),dt===2&&t(Fr,{requestStatus:G!=null&&G.reqStatus?G==null?void 0:G.reqStatus:xn.ENABLE_FOR_FIRST_TIME,attachmentsData:Yt,requestIdHeader:st||ne,pcNumber:j,module:Xt.GL,childRequestIds:G==null?void 0:G.childRequestIds,artifactName:Ir.GL}),dt===3&&t(ae,{ref:io,sx:{width:"100%",overflow:"auto"},children:t(Rr,{module:(eo=Xt)==null?void 0:eo.GL,payloadForPreviewDownloadExcel:bt})})]}),t(Cn,{blurLoading:rt,loaderMessage:Qe}),Ot&&x(Or,{isOpen:Ot,titleIcon:t(Mr,{size:"small",sx:{color:(xt=(ot=Jo)==null?void 0:ot.secondary)==null?void 0:xt.amber,fontSize:"20px"}}),Title:"Warning",handleClose:yt,children:[t(ro,{sx:{mt:2},children:qr.LEAVE_PAGE_MESSAGE}),x(co,{children:[t(ke,{variant:"outlined",size:"small",sx:{...vr},onClick:yt,children:"No"}),t(ke,{variant:"contained",size:"small",sx:{...Br},onClick:go,children:"Yes"})]})]})]})};export{jc as default};
