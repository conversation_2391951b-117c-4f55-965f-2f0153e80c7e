import{s as P,aP as Q,n as G,r as p,cZ as q,bf as w,aX as W,da as B,bI as K,aT as F,C as j,aJ as k,gL as H,gM as x,gN as J,dI as $,gO as X,aD as Z,dc as V,dd as z}from"./index-f7d9b065.js";const ee=(M,n)=>{var D,l;let t={};M==null||M.forEach(e=>{e.MDG_MAT_VISIBILITY===Z.MANDATORY&&e.MDG_MAT_VIEW_NAME!=="Header"&&(t[n]||(t[n]={}),t[n][e.MDG_MAT_VIEW_NAME]||(t[n][e.MDG_MAT_VIEW_NAME]=[]),t[n][e.MDG_MAT_VIEW_NAME].push({jsonName:e.MDG_MAT_JSON_FIELD_NAME,fieldName:e.MDG_MAT_UI_FIELD_NAME}))});let u=(D=M==null?void 0:M.filter(e=>e.MDG_MAT_VIEW_NAME!=="Header"))==null?void 0:D.sort((e,s)=>e.MDG_MAT_SEQUENCE_NO-s.MDG_MAT_SEQUENCE_NO);const I=V(u,"MDG_MAT_VIEW_NAME");let o=[];(l=Object.entries(I))==null||l.forEach(([e,s])=>{let A=V(s,"MDG_MAT_CARD_NAME"),N=[];Object.entries(A).forEach(([S,a])=>{a.sort((_,i)=>_.MDG_MAT_SEQUENCE_NO-i.MDG_MAT_SEQUENCE_NO);let r=a==null?void 0:a.map(_=>({fieldName:_.MDG_MAT_UI_FIELD_NAME,sequenceNo:_.MDG_MAT_SEQUENCE_NO,fieldType:_.MDG_MAT_FIELD_TYPE,maxLength:_.MDG_MAT_MAX_LENGTH,dataType:_.MDG_MAT_DATA_TYPE,viewName:_.MDG_MAT_VIEW_NAME,cardName:_.MDG_MAT_CARD_NAME,cardSeq:_.MDG_MAT_CARD_SEQUENCE,viewSeq:_.MDG_MAT_VIEW_SEQUENCE,value:_.MDG_MAT_DEFAULT_VALUE,visibility:_.MDG_MAT_VISIBILITY,jsonName:_.MDG_MAT_JSON_FIELD_NAME}));N.push({cardName:S,cardSeq:a[0].MDG_MAT_CARD_SEQUENCE,cardDetails:r})}),N.sort((S,a)=>S.cardSeq-a.cardSeq),o.push({viewName:e,viewSeq:s[0].MDG_MAT_VIEW_SEQUENCE,cards:N})}),o.sort((e,s)=>e.viewSeq-s.viewSeq);let T=z(o),c={};return T.forEach(e=>{let s={};e.cards.forEach(A=>{s[A.cardName]=A.cardDetails}),c[e.viewName]=s}),{transformedData:c,mandatoryFields:t}},ae=()=>{const M=P(),{customError:n}=Q(),t=G(a=>{var r,_;return(_=(r=a.bankKey)==null?void 0:r.payload)==null?void 0:_.requestHeaderData}),u=G(a=>a.applicationConfig),{taskData:I}=G(a=>a.userManagement);G(a=>a.userManagement.userData);const[o,T]=p.useState(!1),[c,D]=p.useState(null),l=window.location.href.includes("displayBankKeySAPData"),e=q(w.CURRENT_TASK);let s=null;s=typeof e=="string"?JSON.parse(e):e;let A=s==null?void 0:s.ATTRIBUTE_5;const N=async(a,r)=>{var _,i;if(T(!0),t!=null&&t.RequestType||l){const b={decisionTableId:null,decisionTableName:"MDG_BNKY_FIELD_CONFIG",version:"v2",rulePolicy:null,validityDate:null,conditions:[{"MDG_CONDITIONS.MDG_MAT_SCENARIO":W.CREATE,"MDG_CONDITIONS.MDG_MAT_COUNTRY":r||"US","MDG_CONDITIONS.MDG_MAT_GROUP_ROLE":l?(_=B)==null?void 0:_.REQ_DISPLAY_FIN:I.ATTRIBUTE_5?I.ATTRIBUTE_5:A||((i=B)==null?void 0:i.REQ_INITIATE_FIN),"MDG_CONDITIONS.MDG_MAT_REGION":(t==null?void 0:t.Region)||"US"}],systemFilters:null,systemOrders:null,filterString:null},h=E=>{var f,d,y,C;if(E.statusCode===k.STATUS_200){if(Array.isArray((f=E==null?void 0:E.data)==null?void 0:f.result)&&((d=E==null?void 0:E.data)!=null&&d.result.every(O=>Object.keys(O).length!==0))){let O=(C=(y=E==null?void 0:E.data)==null?void 0:y.result[0])==null?void 0:C.MDG_BOM_MATERIAL_FIELD_CONFIG_ACTION_TYPE;const{transformedData:g,mandatoryFields:L}=ee(O,a);let R=Object.keys(g);const Y=R.map(U=>({keyName:a,tab:U,data:g[U]}));M(H(Y)),M(x(L)),M(J({BankKey:{allfields:$(R),mandatoryFields:L}}))}else M(X({BankKey:{}}));T(!1)}},m=E=>{n(E),D(E),T(!1)},v=u.environment==="localhost"?`/${K}${F.INVOKE_RULES.LOCAL}`:`/${K}${F.INVOKE_RULES.PROD}`;j(v,"post",h,m,b)}};return{loading:o,error:c,fetchBankKeyFieldConfig:(a,r)=>{try{N(a,r)}catch(_){D(_),T(!1)}}}};export{ae as u};
