import{s as v,aP as b,n as A,r as y,aX as q,da as Q,bI as g,aT as m,C as w,aJ as j,db as R,dc as U,dd as P,aD as V}from"./index-f7d9b065.js";const Y=M=>{let l={},o=M==null?void 0:M.sort((t,a)=>t.MDG_MAT_SEQUENCE_NO-a.MDG_MAT_SEQUENCE_NO);const i=U(o,"MDG_MAT_VIEW_NAME");let T=[];Object.entries(i).forEach(([t,a])=>{let D=U(a,"MDG_MAT_CARD_NAME"),r=[];Object.entries(D).forEach(([N,e])=>{e.sort((s,O)=>s.MDG_MAT_SEQUENCE_NO-O.MDG_MAT_SEQUENCE_NO);let n=e.map(s=>({fieldName:s.MDG_MAT_UI_FIELD_NAME,sequenceNo:s.MDG_MAT_SEQUENCE_NO,fieldType:s.MDG_MAT_FIELD_TYPE,maxLength:s.MDG_MAT_MAX_LENGTH,dataType:s.MDG_MAT_DATA_TYPE,viewName:s.MDG_MAT_VIEW_NAME,cardName:s.MDG_MAT_CARD_NAME,cardSeq:s.MDG_MAT_CARD_SEQUENCE,viewSeq:s.MDG_MAT_VIEW_SEQUENCE,value:s.MDG_MAT_DEFAULT_VALUE,visibility:s.MDG_MAT_VISIBILITY,jsonName:s.MDG_MAT_JSON_FIELD_NAME}));r.push({cardName:N,cardSeq:e[0].MDG_MAT_CARD_SEQUENCE,cardDetails:n})}),r.sort((N,e)=>N.cardSeq-e.cardSeq),T.push({viewName:t,viewSeq:a[0].MDG_MAT_VIEW_SEQUENCE,cards:r})}),T.sort((t,a)=>t.viewSeq-a.viewSeq);let I=P(T),E={};return I.forEach(t=>{let a={};t.cards.forEach(D=>{a[D.cardName]=D.cardDetails,t.viewName!=="Request Header"&&D.cardDetails.forEach(r=>{r.visibility===V.MANDATORY&&(l[r.viewName]||(l[r.viewName]=[]),l[r.viewName].push({jsonName:r==null?void 0:r.jsonName,fieldName:r==null?void 0:r.fieldName}))})}),E[t.viewName]=a}),{transformedData:E,mandatoryFields:l}},K=()=>{const M=v(),{customError:l}=b(),o=A(e=>{var n;return(n=e.payload)==null?void 0:n.payloadData}),i=A(e=>e.applicationConfig);A(e=>e.userManagement.userData);const T=A(e=>{var n;return((n=e.internalOrder)==null?void 0:n.fieldConfigByOrderType)||{}}),[I,E]=y.useState(!1),[t,a]=y.useState(null),D=e=>T[e]||null,r=async e=>{E(!0);const n={decisionTableId:null,decisionTableName:"MDG_INTORD_FIELD_CONFIG",version:"v2",rulePolicy:null,validityDate:null,conditions:[{"MDG_CONDITIONS.MDG_MAT_SCENARIO":(o==null?void 0:o.RequestType)||q.CREATE,"MDG_CONDITIONS.MDG_ORDER_TYPE":e||"TUK1","MDG_CONDITIONS.MDG_MAT_REGION":(o==null?void 0:o.Region)||"US","MDG_CONDITIONS.MDG_MAT_GROUP_ROLE":Q.REQ_INITIATE_FIN}],systemFilters:null,systemOrders:null,filterString:null},s=_=>{var f,u,S,C;if(_.statusCode===j.STATUS_200){if(Array.isArray((f=_==null?void 0:_.data)==null?void 0:f.result)&&((u=_==null?void 0:_.data)!=null&&u.result.every(c=>Object.keys(c).length!==0))){let c=(C=(S=_==null?void 0:_.data)==null?void 0:S.result[0])==null?void 0:C.MDG_BOM_MATERIAL_FIELD_CONFIG_ACTION_TYPE;const{transformedData:d,mandatoryFields:L}=Y(c);let G=Object.keys(d);const h=G.map(p=>({tab:p,data:d[p]}));M(R({orderType:e||"TUK1",fieldData:{rawResponse:c,transformedData:d,mandatoryFields:L,allTabsData:h,ioTabsData:G}}))}else M(R({orderType:e||"TUK1",fieldData:{}}));E(!1)}},O=_=>{l(_),a(_),E(!1)},F=i.environment==="localhost"?`/${g}${m.INVOKE_RULES.LOCAL}`:`/${g}${m.INVOKE_RULES.PROD}`;w(F,"post",s,O,n)};return{loading:I,error:t,fetchInternalOrderFieldConfig:e=>{try{r(e)}catch(n){a(n),E(!1)}},getFieldConfigForOrderType:D,fieldConfigByOrderType:T}};export{K as u};
