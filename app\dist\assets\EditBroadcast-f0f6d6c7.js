import{r as _,hA as He,hB as C,hC as zt,l as ce,m as Ft,cs as H,dy as Wt,d as p,aa as Yt,a4 as Kt,B as K,bE as Xt,n as qt,u as Gt,g as Jt,C as Me,c as l,j as n,O as D,a8 as de,aZ as Q,$ as Re,F as Zt,aE as Ie,dm as ee,a6 as ke,a$ as Qt,T as ea,ah as ta,an as $e,ag as aa,ds as ra,aF as oa,aG as na,dz as sa,aT as ia,cq as ue}from"./index-f7d9b065.js";import{a as la,d as ca}from"./SlideshowOutlined-37fd5794.js";import{d as da}from"./CloudUpload-0ba6431e.js";import{S as ua}from"./SingleSelectDropdown-aee403d4.js";import{l as pa}from"./index-d550e3b0.js";import{r as ya,V as ha}from"./memoize-one.esm-93cbac03.js";import{r as Ve}from"./index-7ffbe79f.js";import{M as fa}from"./UtilDoc-6f590135.js";import{D as Le}from"./DatePicker-78c32993.js";var ma=Object.create,te=Object.defineProperty,_a=Object.getOwnPropertyDescriptor,ga=Object.getOwnPropertyNames,Pa=Object.getPrototypeOf,ba=Object.prototype.hasOwnProperty,va=(e,t)=>{for(var a in t)te(e,a,{get:t[a],enumerable:!0})},ze=(e,t,a,o)=>{if(t&&typeof t=="object"||typeof t=="function")for(let r of ga(t))!ba.call(e,r)&&r!==a&&te(e,r,{get:()=>t[r],enumerable:!(o=_a(t,r))||o.enumerable});return e},be=(e,t,a)=>(a=e!=null?ma(Pa(e)):{},ze(t||!e||!e.__esModule?te(a,"default",{value:e,enumerable:!0}):a,e)),wa=e=>ze(te({},"__esModule",{value:!0}),e),Fe={};va(Fe,{callPlayer:()=>Ba,getConfig:()=>La,getSDK:()=>$a,isBlobUrl:()=>ja,isMediaStream:()=>Na,lazy:()=>Da,omit:()=>Ua,parseEndTime:()=>Ra,parseStartTime:()=>Ma,queryString:()=>ka,randomString:()=>Ia,supportsWebKitPresentationMode:()=>Ha});var ae=wa(Fe),Oa=be(_),Sa=be(pa),Ta=be(He);const Da=e=>Oa.default.lazy(async()=>{const t=await e();return typeof t.default=="function"?t:t.default}),Ea=/[?&#](?:start|t)=([0-9hms]+)/,Ca=/[?&#]end=([0-9hms]+)/,me=/(\d+)(h|m|s)/g,Aa=/^\d+$/;function We(e,t){if(e instanceof Array)return;const a=e.match(t);if(a){const o=a[1];if(o.match(me))return xa(o);if(Aa.test(o))return parseInt(o)}}function xa(e){let t=0,a=me.exec(e);for(;a!==null;){const[,o,r]=a;r==="h"&&(t+=parseInt(o,10)*60*60),r==="m"&&(t+=parseInt(o,10)*60),r==="s"&&(t+=parseInt(o,10)),a=me.exec(e)}return t}function Ma(e){return We(e,Ea)}function Ra(e){return We(e,Ca)}function Ia(){return Math.random().toString(36).substr(2,5)}function ka(e){return Object.keys(e).map(t=>`${t}=${e[t]}`).join("&")}function pe(e){return window[e]?window[e]:window.exports&&window.exports[e]?window.exports[e]:window.module&&window.module.exports&&window.module.exports[e]?window.module.exports[e]:null}const B={},$a=function(t,a,o=null,r=()=>!0,d=Sa.default){const S=pe(a);return S&&r(S)?Promise.resolve(S):new Promise((s,T)=>{if(B[t]){B[t].push({resolve:s,reject:T});return}B[t]=[{resolve:s,reject:T}];const x=b=>{B[t].forEach(I=>I.resolve(b))};if(o){const b=window[o];window[o]=function(){b&&b(),x(pe(a))}}d(t,b=>{b?(B[t].forEach(I=>I.reject(b)),B[t]=null):o||x(pe(a))})})};function La(e,t){return(0,Ta.default)(t.config,e.config)}function Ua(e,...t){const a=[].concat(...t),o={},r=Object.keys(e);for(const d of r)a.indexOf(d)===-1&&(o[d]=e[d]);return o}function Ba(e,...t){if(!this.player||!this.player[e]){let a=`ReactPlayer: ${this.constructor.displayName} player could not call %c${e}%c – `;return this.player?this.player[e]||(a+="The method was not available"):a+="The player was not available",console.warn(a,"font-weight: bold",""),null}return this.player[e](...t)}function Na(e){return typeof window<"u"&&typeof window.MediaStream<"u"&&e instanceof window.MediaStream}function ja(e){return/^blob:/.test(e)}function Ha(e=document.createElement("video")){const t=/iPhone|iPod/.test(navigator.userAgent)===!1;return e.webkitSupportsPresentationMode&&typeof e.webkitSetPresentationMode=="function"&&t}var ve=Object.defineProperty,Va=Object.getOwnPropertyDescriptor,za=Object.getOwnPropertyNames,Fa=Object.prototype.hasOwnProperty,Wa=(e,t)=>{for(var a in t)ve(e,a,{get:t[a],enumerable:!0})},Ya=(e,t,a,o)=>{if(t&&typeof t=="object"||typeof t=="function")for(let r of za(t))!Fa.call(e,r)&&r!==a&&ve(e,r,{get:()=>t[r],enumerable:!(o=Va(t,r))||o.enumerable});return e},Ka=e=>Ya(ve({},"__esModule",{value:!0}),e),Ye={};Wa(Ye,{AUDIO_EXTENSIONS:()=>we,DASH_EXTENSIONS:()=>st,FLV_EXTENSIONS:()=>it,HLS_EXTENSIONS:()=>Se,MATCH_URL_DAILYMOTION:()=>at,MATCH_URL_FACEBOOK:()=>Ge,MATCH_URL_FACEBOOK_WATCH:()=>Je,MATCH_URL_KALTURA:()=>nt,MATCH_URL_MIXCLOUD:()=>rt,MATCH_URL_MUX:()=>qe,MATCH_URL_SOUNDCLOUD:()=>Ke,MATCH_URL_STREAMABLE:()=>Ze,MATCH_URL_TWITCH_CHANNEL:()=>tt,MATCH_URL_TWITCH_VIDEO:()=>et,MATCH_URL_VIDYARD:()=>ot,MATCH_URL_VIMEO:()=>Xe,MATCH_URL_WISTIA:()=>Qe,MATCH_URL_YOUTUBE:()=>_e,VIDEO_EXTENSIONS:()=>Oe,canPlay:()=>qa});var Xa=Ka(Ye),Ue=ae;const _e=/(?:youtu\.be\/|youtube(?:-nocookie|education)?\.com\/(?:embed\/|v\/|watch\/|watch\?v=|watch\?.+&v=|shorts\/|live\/))((\w|-){11})|youtube\.com\/playlist\?list=|youtube\.com\/user\//,Ke=/(?:soundcloud\.com|snd\.sc)\/[^.]+$/,Xe=/vimeo\.com\/(?!progressive_redirect).+/,qe=/stream\.mux\.com\/(?!\w+\.m3u8)(\w+)/,Ge=/^https?:\/\/(www\.)?facebook\.com.*\/(video(s)?|watch|story)(\.php?|\/).+$/,Je=/^https?:\/\/fb\.watch\/.+$/,Ze=/streamable\.com\/([a-z0-9]+)$/,Qe=/(?:wistia\.(?:com|net)|wi\.st)\/(?:medias|embed)\/(?:iframe\/)?([^?]+)/,et=/(?:www\.|go\.)?twitch\.tv\/videos\/(\d+)($|\?)/,tt=/(?:www\.|go\.)?twitch\.tv\/([a-zA-Z0-9_]+)($|\?)/,at=/^(?:(?:https?):)?(?:\/\/)?(?:www\.)?(?:(?:dailymotion\.com(?:\/embed)?\/video)|dai\.ly)\/([a-zA-Z0-9]+)(?:_[\w_-]+)?(?:[\w.#_-]+)?/,rt=/mixcloud\.com\/([^/]+\/[^/]+)/,ot=/vidyard.com\/(?:watch\/)?([a-zA-Z0-9-_]+)/,nt=/^https?:\/\/[a-zA-Z]+\.kaltura.(com|org)\/p\/([0-9]+)\/sp\/([0-9]+)00\/embedIframeJs\/uiconf_id\/([0-9]+)\/partner_id\/([0-9]+)(.*)entry_id.([a-zA-Z0-9-_].*)$/,we=/\.(m4a|m4b|mp4a|mpga|mp2|mp2a|mp3|m2a|m3a|wav|weba|aac|oga|spx)($|\?)/i,Oe=/\.(mp4|og[gv]|webm|mov|m4v)(#t=[,\d+]+)?($|\?)/i,Se=/\.(m3u8)($|\?)/i,st=/\.(mpd)($|\?)/i,it=/\.(flv)($|\?)/i,ge=e=>{if(e instanceof Array){for(const t of e)if(typeof t=="string"&&ge(t)||ge(t.src))return!0;return!1}return(0,Ue.isMediaStream)(e)||(0,Ue.isBlobUrl)(e)?!0:we.test(e)||Oe.test(e)||Se.test(e)||st.test(e)||it.test(e)},qa={youtube:e=>e instanceof Array?e.every(t=>_e.test(t)):_e.test(e),soundcloud:e=>Ke.test(e)&&!we.test(e),vimeo:e=>Xe.test(e)&&!Oe.test(e)&&!Se.test(e),mux:e=>qe.test(e),facebook:e=>Ge.test(e)||Je.test(e),streamable:e=>Ze.test(e),wistia:e=>Qe.test(e),twitch:e=>et.test(e)||tt.test(e),dailymotion:e=>at.test(e),mixcloud:e=>rt.test(e),vidyard:e=>ot.test(e),kaltura:e=>nt.test(e),file:ge};var Te=Object.defineProperty,Ga=Object.getOwnPropertyDescriptor,Ja=Object.getOwnPropertyNames,Za=Object.prototype.hasOwnProperty,Qa=(e,t)=>{for(var a in t)Te(e,a,{get:t[a],enumerable:!0})},er=(e,t,a,o)=>{if(t&&typeof t=="object"||typeof t=="function")for(let r of Ja(t))!Za.call(e,r)&&r!==a&&Te(e,r,{get:()=>t[r],enumerable:!(o=Ga(t,r))||o.enumerable});return e},tr=e=>er(Te({},"__esModule",{value:!0}),e),lt={};Qa(lt,{default:()=>rr});var ar=tr(lt),E=ae,w=Xa,rr=[{key:"youtube",name:"YouTube",canPlay:w.canPlay.youtube,lazyPlayer:(0,E.lazy)(()=>C(()=>import("./YouTube-bfcf359c.js").then(e=>e.Y),["assets/YouTube-bfcf359c.js","assets/index-f7d9b065.js","assets/index-94cddb9a.css"]))},{key:"soundcloud",name:"SoundCloud",canPlay:w.canPlay.soundcloud,lazyPlayer:(0,E.lazy)(()=>C(()=>import("./SoundCloud-f38a1851.js").then(e=>e.S),["assets/SoundCloud-f38a1851.js","assets/index-f7d9b065.js","assets/index-94cddb9a.css"]))},{key:"vimeo",name:"Vimeo",canPlay:w.canPlay.vimeo,lazyPlayer:(0,E.lazy)(()=>C(()=>import("./Vimeo-df686f15.js").then(e=>e.V),["assets/Vimeo-df686f15.js","assets/index-f7d9b065.js","assets/index-94cddb9a.css"]))},{key:"mux",name:"Mux",canPlay:w.canPlay.mux,lazyPlayer:(0,E.lazy)(()=>C(()=>import("./Mux-261ab3be.js").then(e=>e.M),["assets/Mux-261ab3be.js","assets/index-f7d9b065.js","assets/index-94cddb9a.css"]))},{key:"facebook",name:"Facebook",canPlay:w.canPlay.facebook,lazyPlayer:(0,E.lazy)(()=>C(()=>import("./Facebook-7901b544.js").then(e=>e.F),["assets/Facebook-7901b544.js","assets/index-f7d9b065.js","assets/index-94cddb9a.css"]))},{key:"streamable",name:"Streamable",canPlay:w.canPlay.streamable,lazyPlayer:(0,E.lazy)(()=>C(()=>import("./Streamable-dbe482d0.js").then(e=>e.S),["assets/Streamable-dbe482d0.js","assets/index-f7d9b065.js","assets/index-94cddb9a.css"]))},{key:"wistia",name:"Wistia",canPlay:w.canPlay.wistia,lazyPlayer:(0,E.lazy)(()=>C(()=>import("./Wistia-db25ef7b.js").then(e=>e.W),["assets/Wistia-db25ef7b.js","assets/index-f7d9b065.js","assets/index-94cddb9a.css"]))},{key:"twitch",name:"Twitch",canPlay:w.canPlay.twitch,lazyPlayer:(0,E.lazy)(()=>C(()=>import("./Twitch-d7495046.js").then(e=>e.T),["assets/Twitch-d7495046.js","assets/index-f7d9b065.js","assets/index-94cddb9a.css"]))},{key:"dailymotion",name:"DailyMotion",canPlay:w.canPlay.dailymotion,lazyPlayer:(0,E.lazy)(()=>C(()=>import("./DailyMotion-028ca844.js").then(e=>e.D),["assets/DailyMotion-028ca844.js","assets/index-f7d9b065.js","assets/index-94cddb9a.css"]))},{key:"mixcloud",name:"Mixcloud",canPlay:w.canPlay.mixcloud,lazyPlayer:(0,E.lazy)(()=>C(()=>import("./Mixcloud-044934e3.js").then(e=>e.M),["assets/Mixcloud-044934e3.js","assets/index-f7d9b065.js","assets/index-94cddb9a.css"]))},{key:"vidyard",name:"Vidyard",canPlay:w.canPlay.vidyard,lazyPlayer:(0,E.lazy)(()=>C(()=>import("./Vidyard-415380e4.js").then(e=>e.V),["assets/Vidyard-415380e4.js","assets/index-f7d9b065.js","assets/index-94cddb9a.css"]))},{key:"kaltura",name:"Kaltura",canPlay:w.canPlay.kaltura,lazyPlayer:(0,E.lazy)(()=>C(()=>import("./Kaltura-c8c80a1c.js").then(e=>e.K),["assets/Kaltura-c8c80a1c.js","assets/index-f7d9b065.js","assets/index-94cddb9a.css"]))},{key:"file",name:"FilePlayer",canPlay:w.canPlay.file,canEnablePIP:e=>w.canPlay.file(e)&&(document.pictureInPictureEnabled||(0,E.supportsWebKitPresentationMode)())&&!w.AUDIO_EXTENSIONS.test(e),lazyPlayer:(0,E.lazy)(()=>C(()=>import("./FilePlayer-825195f0.js").then(e=>e.F),["assets/FilePlayer-825195f0.js","assets/index-f7d9b065.js","assets/index-94cddb9a.css"]))}],or=Object.create,re=Object.defineProperty,nr=Object.getOwnPropertyDescriptor,sr=Object.getOwnPropertyNames,ir=Object.getPrototypeOf,lr=Object.prototype.hasOwnProperty,cr=(e,t)=>{for(var a in t)re(e,a,{get:t[a],enumerable:!0})},ct=(e,t,a,o)=>{if(t&&typeof t=="object"||typeof t=="function")for(let r of sr(t))!lr.call(e,r)&&r!==a&&re(e,r,{get:()=>t[r],enumerable:!(o=nr(t,r))||o.enumerable});return e},dr=(e,t,a)=>(a=e!=null?or(ir(e)):{},ct(t||!e||!e.__esModule?re(a,"default",{value:e,enumerable:!0}):a,e)),ur=e=>ct(re({},"__esModule",{value:!0}),e),dt={};cr(dt,{defaultProps:()=>hr,propTypes:()=>yr});var ut=ur(dt),pr=dr(zt);const{string:g,bool:O,number:N,array:ye,oneOfType:W,shape:A,object:v,func:m,node:Be}=pr.default,yr={url:W([g,ye,v]),playing:O,loop:O,controls:O,volume:N,muted:O,playbackRate:N,width:W([g,N]),height:W([g,N]),style:v,progressInterval:N,playsinline:O,pip:O,stopOnUnmount:O,light:W([O,g,v]),playIcon:Be,previewTabIndex:N,previewAriaLabel:g,fallback:Be,oEmbedUrl:g,wrapper:W([g,m,A({render:m.isRequired})]),config:A({soundcloud:A({options:v}),youtube:A({playerVars:v,embedOptions:v,onUnstarted:m}),facebook:A({appId:g,version:g,playerId:g,attributes:v}),dailymotion:A({params:v}),vimeo:A({playerOptions:v,title:g}),mux:A({attributes:v,version:g}),file:A({attributes:v,tracks:ye,forceVideo:O,forceAudio:O,forceHLS:O,forceSafariHLS:O,forceDisableHls:O,forceDASH:O,forceFLV:O,hlsOptions:v,hlsVersion:g,dashVersion:g,flvVersion:g}),wistia:A({options:v,playerId:g,customControls:ye}),mixcloud:A({options:v}),twitch:A({options:v,playerId:g}),vidyard:A({options:v})}),onReady:m,onStart:m,onPlay:m,onPause:m,onBuffer:m,onBufferEnd:m,onEnded:m,onError:m,onDuration:m,onSeek:m,onPlaybackRateChange:m,onPlaybackQualityChange:m,onProgress:m,onClickPreview:m,onEnablePIP:m,onDisablePIP:m},P=()=>{},hr={playing:!1,loop:!1,controls:!1,volume:null,muted:!1,playbackRate:1,width:"640px",height:"360px",style:{},progressInterval:1e3,playsinline:!1,pip:!1,stopOnUnmount:!0,light:!1,fallback:null,wrapper:"div",previewTabIndex:0,previewAriaLabel:"",oEmbedUrl:"https://noembed.com/embed?url={url}",config:{soundcloud:{options:{visual:!0,buying:!1,liking:!1,download:!1,sharing:!1,show_comments:!1,show_playcount:!1}},youtube:{playerVars:{playsinline:1,showinfo:0,rel:0,iv_load_policy:3,modestbranding:1},embedOptions:{},onUnstarted:P},facebook:{appId:"1309697205772819",version:"v3.3",playerId:null,attributes:{}},dailymotion:{params:{api:1,"endscreen-enable":!1}},vimeo:{playerOptions:{autopause:!1,byline:!1,portrait:!1,title:!1},title:null},mux:{attributes:{},version:"2"},file:{attributes:{},tracks:[],forceVideo:!1,forceAudio:!1,forceHLS:!1,forceDASH:!1,forceFLV:!1,hlsOptions:{},hlsVersion:"1.1.4",dashVersion:"3.1.3",flvVersion:"1.5.0",forceDisableHls:!1},wistia:{options:{},playerId:null,customControls:null},mixcloud:{options:{hide_cover:1}},twitch:{options:{},playerId:null},vidyard:{options:{}}},onReady:P,onStart:P,onPlay:P,onPause:P,onBuffer:P,onBufferEnd:P,onEnded:P,onError:P,onDuration:P,onSeek:P,onPlaybackRateChange:P,onPlaybackQualityChange:P,onProgress:P,onClickPreview:P,onEnablePIP:P,onDisablePIP:P};var fr=Object.create,q=Object.defineProperty,mr=Object.getOwnPropertyDescriptor,_r=Object.getOwnPropertyNames,gr=Object.getPrototypeOf,Pr=Object.prototype.hasOwnProperty,br=(e,t,a)=>t in e?q(e,t,{enumerable:!0,configurable:!0,writable:!0,value:a}):e[t]=a,vr=(e,t)=>{for(var a in t)q(e,a,{get:t[a],enumerable:!0})},pt=(e,t,a,o)=>{if(t&&typeof t=="object"||typeof t=="function")for(let r of _r(t))!Pr.call(e,r)&&r!==a&&q(e,r,{get:()=>t[r],enumerable:!(o=mr(t,r))||o.enumerable});return e},yt=(e,t,a)=>(a=e!=null?fr(gr(e)):{},pt(t||!e||!e.__esModule?q(a,"default",{value:e,enumerable:!0}):a,e)),wr=e=>pt(q({},"__esModule",{value:!0}),e),f=(e,t,a)=>(br(e,typeof t!="symbol"?t+"":t,a),a),ht={};vr(ht,{default:()=>oe});var Or=wr(ht),Ne=yt(_),Sr=yt(Ve),ft=ut,Tr=ae;const Dr=5e3;class oe extends Ne.Component{constructor(){super(...arguments),f(this,"mounted",!1),f(this,"isReady",!1),f(this,"isPlaying",!1),f(this,"isLoading",!0),f(this,"loadOnReady",null),f(this,"startOnPlay",!0),f(this,"seekOnPlay",null),f(this,"onDurationCalled",!1),f(this,"handlePlayerMount",t=>{if(this.player){this.progress();return}this.player=t,this.player.load(this.props.url),this.progress()}),f(this,"getInternalPlayer",t=>this.player?this.player[t]:null),f(this,"progress",()=>{if(this.props.url&&this.player&&this.isReady){const t=this.getCurrentTime()||0,a=this.getSecondsLoaded(),o=this.getDuration();if(o){const r={playedSeconds:t,played:t/o};a!==null&&(r.loadedSeconds=a,r.loaded=a/o),(r.playedSeconds!==this.prevPlayed||r.loadedSeconds!==this.prevLoaded)&&this.props.onProgress(r),this.prevPlayed=r.playedSeconds,this.prevLoaded=r.loadedSeconds}}this.progressTimeout=setTimeout(this.progress,this.props.progressFrequency||this.props.progressInterval)}),f(this,"handleReady",()=>{if(!this.mounted)return;this.isReady=!0,this.isLoading=!1;const{onReady:t,playing:a,volume:o,muted:r}=this.props;t(),!r&&o!==null&&this.player.setVolume(o),this.loadOnReady?(this.player.load(this.loadOnReady,!0),this.loadOnReady=null):a&&this.player.play(),this.handleDurationCheck()}),f(this,"handlePlay",()=>{this.isPlaying=!0,this.isLoading=!1;const{onStart:t,onPlay:a,playbackRate:o}=this.props;this.startOnPlay&&(this.player.setPlaybackRate&&o!==1&&this.player.setPlaybackRate(o),t(),this.startOnPlay=!1),a(),this.seekOnPlay&&(this.seekTo(this.seekOnPlay),this.seekOnPlay=null),this.handleDurationCheck()}),f(this,"handlePause",t=>{this.isPlaying=!1,this.isLoading||this.props.onPause(t)}),f(this,"handleEnded",()=>{const{activePlayer:t,loop:a,onEnded:o}=this.props;t.loopOnEnded&&a&&this.seekTo(0),a||(this.isPlaying=!1,o())}),f(this,"handleError",(...t)=>{this.isLoading=!1,this.props.onError(...t)}),f(this,"handleDurationCheck",()=>{clearTimeout(this.durationCheckTimeout);const t=this.getDuration();t?this.onDurationCalled||(this.props.onDuration(t),this.onDurationCalled=!0):this.durationCheckTimeout=setTimeout(this.handleDurationCheck,100)}),f(this,"handleLoaded",()=>{this.isLoading=!1})}componentDidMount(){this.mounted=!0}componentWillUnmount(){clearTimeout(this.progressTimeout),clearTimeout(this.durationCheckTimeout),this.isReady&&this.props.stopOnUnmount&&(this.player.stop(),this.player.disablePIP&&this.player.disablePIP()),this.mounted=!1}componentDidUpdate(t){if(!this.player)return;const{url:a,playing:o,volume:r,muted:d,playbackRate:S,pip:s,loop:T,activePlayer:x,disableDeferredLoading:b}=this.props;if(!(0,Sr.default)(t.url,a)){if(this.isLoading&&!x.forceLoad&&!b&&!(0,Tr.isMediaStream)(a)){console.warn(`ReactPlayer: the attempt to load ${a} is being deferred until the player has loaded`),this.loadOnReady=a;return}this.isLoading=!0,this.startOnPlay=!0,this.onDurationCalled=!1,this.player.load(a,this.isReady)}!t.playing&&o&&!this.isPlaying&&this.player.play(),t.playing&&!o&&this.isPlaying&&this.player.pause(),!t.pip&&s&&this.player.enablePIP&&this.player.enablePIP(),t.pip&&!s&&this.player.disablePIP&&this.player.disablePIP(),t.volume!==r&&r!==null&&this.player.setVolume(r),t.muted!==d&&(d?this.player.mute():(this.player.unmute(),r!==null&&setTimeout(()=>this.player.setVolume(r)))),t.playbackRate!==S&&this.player.setPlaybackRate&&this.player.setPlaybackRate(S),t.loop!==T&&this.player.setLoop&&this.player.setLoop(T)}getDuration(){return this.isReady?this.player.getDuration():null}getCurrentTime(){return this.isReady?this.player.getCurrentTime():null}getSecondsLoaded(){return this.isReady?this.player.getSecondsLoaded():null}seekTo(t,a,o){if(!this.isReady){t!==0&&(this.seekOnPlay=t,setTimeout(()=>{this.seekOnPlay=null},Dr));return}if(a?a==="fraction":t>0&&t<1){const d=this.player.getDuration();if(!d){console.warn("ReactPlayer: could not seek using fraction – duration not yet available");return}this.player.seekTo(d*t,o);return}this.player.seekTo(t,o)}render(){const t=this.props.activePlayer;return t?Ne.default.createElement(t,{...this.props,onMount:this.handlePlayerMount,onReady:this.handleReady,onPlay:this.handlePlay,onPause:this.handlePause,onEnded:this.handleEnded,onLoaded:this.handleLoaded,onError:this.handleError}):null}}f(oe,"displayName","Player");f(oe,"propTypes",ft.propTypes);f(oe,"defaultProps",ft.defaultProps);var Er=Object.create,G=Object.defineProperty,Cr=Object.getOwnPropertyDescriptor,Ar=Object.getOwnPropertyNames,xr=Object.getPrototypeOf,Mr=Object.prototype.hasOwnProperty,Rr=(e,t,a)=>t in e?G(e,t,{enumerable:!0,configurable:!0,writable:!0,value:a}):e[t]=a,Ir=(e,t)=>{for(var a in t)G(e,a,{get:t[a],enumerable:!0})},mt=(e,t,a,o)=>{if(t&&typeof t=="object"||typeof t=="function")for(let r of Ar(t))!Mr.call(e,r)&&r!==a&&G(e,r,{get:()=>t[r],enumerable:!(o=Cr(t,r))||o.enumerable});return e},J=(e,t,a)=>(a=e!=null?Er(xr(e)):{},mt(t||!e||!e.__esModule?G(a,"default",{value:e,enumerable:!0}):a,e)),kr=e=>mt(G({},"__esModule",{value:!0}),e),h=(e,t,a)=>(Rr(e,typeof t!="symbol"?t+"":t,a),a),_t={};Ir(_t,{createReactPlayer:()=>zr});var $r=kr(_t),j=J(_),Lr=J(He),he=J(ya),je=J(Ve),X=ut,gt=ae,Ur=J(Or);const Br=(0,gt.lazy)(()=>C(()=>import("./Preview-eb5adee8.js").then(e=>e.P),["assets/Preview-eb5adee8.js","assets/index-f7d9b065.js","assets/index-94cddb9a.css"])),Nr=typeof window<"u"&&window.document&&typeof document<"u",jr=typeof ce<"u"&&ce.window&&ce.window.document,Hr=Object.keys(X.propTypes),Vr=Nr||jr?j.Suspense:()=>null,Y=[],zr=(e,t)=>{var a;return a=class extends j.Component{constructor(){super(...arguments),h(this,"state",{showPreview:!!this.props.light}),h(this,"references",{wrapper:o=>{this.wrapper=o},player:o=>{this.player=o}}),h(this,"handleClickPreview",o=>{this.setState({showPreview:!1}),this.props.onClickPreview(o)}),h(this,"showPreview",()=>{this.setState({showPreview:!0})}),h(this,"getDuration",()=>this.player?this.player.getDuration():null),h(this,"getCurrentTime",()=>this.player?this.player.getCurrentTime():null),h(this,"getSecondsLoaded",()=>this.player?this.player.getSecondsLoaded():null),h(this,"getInternalPlayer",(o="player")=>this.player?this.player.getInternalPlayer(o):null),h(this,"seekTo",(o,r,d)=>{if(!this.player)return null;this.player.seekTo(o,r,d)}),h(this,"handleReady",()=>{this.props.onReady(this)}),h(this,"getActivePlayer",(0,he.default)(o=>{for(const r of[...Y,...e])if(r.canPlay(o))return r;return t||null})),h(this,"getConfig",(0,he.default)((o,r)=>{const{config:d}=this.props;return Lr.default.all([X.defaultProps.config,X.defaultProps.config[r]||{},d,d[r]||{}])})),h(this,"getAttributes",(0,he.default)(o=>(0,gt.omit)(this.props,Hr))),h(this,"renderActivePlayer",o=>{if(!o)return null;const r=this.getActivePlayer(o);if(!r)return null;const d=this.getConfig(o,r.key);return j.default.createElement(Ur.default,{...this.props,key:r.key,ref:this.references.player,config:d,activePlayer:r.lazyPlayer||r,onReady:this.handleReady})})}shouldComponentUpdate(o,r){return!(0,je.default)(this.props,o)||!(0,je.default)(this.state,r)}componentDidUpdate(o){const{light:r}=this.props;!o.light&&r&&this.setState({showPreview:!0}),o.light&&!r&&this.setState({showPreview:!1})}renderPreview(o){if(!o)return null;const{light:r,playIcon:d,previewTabIndex:S,oEmbedUrl:s,previewAriaLabel:T}=this.props;return j.default.createElement(Br,{url:o,light:r,playIcon:d,previewTabIndex:S,previewAriaLabel:T,oEmbedUrl:s,onClick:this.handleClickPreview})}render(){const{url:o,style:r,width:d,height:S,fallback:s,wrapper:T}=this.props,{showPreview:x}=this.state,b=this.getAttributes(o),I=typeof T=="string"?this.references.wrapper:void 0;return j.default.createElement(T,{ref:I,style:{...r,width:d,height:S},...b},j.default.createElement(Vr,{fallback:s},x?this.renderPreview(o):this.renderActivePlayer(o)))}},h(a,"displayName","ReactPlayer"),h(a,"propTypes",X.propTypes),h(a,"defaultProps",X.defaultProps),h(a,"addCustomPlayer",o=>{Y.push(o)}),h(a,"removeCustomPlayers",()=>{Y.length=0}),h(a,"canPlay",o=>{for(const r of[...Y,...e])if(r.canPlay(o))return!0;return!1}),h(a,"canEnablePIP",o=>{for(const r of[...Y,...e])if(r.canEnablePIP&&r.canEnablePIP(o))return!0;return!1}),a};var Fr=Object.create,ne=Object.defineProperty,Wr=Object.getOwnPropertyDescriptor,Yr=Object.getOwnPropertyNames,Kr=Object.getPrototypeOf,Xr=Object.prototype.hasOwnProperty,qr=(e,t)=>{for(var a in t)ne(e,a,{get:t[a],enumerable:!0})},Pt=(e,t,a,o)=>{if(t&&typeof t=="object"||typeof t=="function")for(let r of Yr(t))!Xr.call(e,r)&&r!==a&&ne(e,r,{get:()=>t[r],enumerable:!(o=Wr(t,r))||o.enumerable});return e},Gr=(e,t,a)=>(a=e!=null?Fr(Kr(e)):{},Pt(t||!e||!e.__esModule?ne(a,"default",{value:e,enumerable:!0}):a,e)),Jr=e=>Pt(ne({},"__esModule",{value:!0}),e),bt={};qr(bt,{default:()=>to});var Zr=Jr(bt),Pe=Gr(ar),Qr=$r;const eo=Pe.default[Pe.default.length-1];var to=(0,Qr.createReactPlayer)(Pe.default,eo);const ao=Ft(Zr),ro="569576829775-45noron6i0dj5j5l5ljin32fa635ts1m.apps.googleusercontent.com",oo="https://www.googleapis.com/auth/youtube.upload",no=H(Wt)(({theme:e})=>({padding:e.spacing(3),marginBottom:e.spacing(2),borderRadius:e.spacing(2),boxShadow:"0 2px 12px rgba(0,0,0,0.08)"})),so=H(p)(({theme:e})=>({fontSize:"18px",fontWeight:600,color:e.palette.text.primary,marginBottom:e.spacing(2),borderBottom:`2px solid ${e.palette.primary.main}`,paddingBottom:e.spacing(1)})),fe=H(Yt)(({theme:e})=>({"& .MuiOutlinedInput-root":{borderRadius:e.spacing(1)}})),io=H(Kt)(({theme:e})=>({borderRadius:e.spacing(1),backgroundColor:e.palette.background.paper})),lo=H(K)(({theme:e})=>({border:`2px dashed ${e.palette.divider}`,borderRadius:e.spacing(2),padding:e.spacing(3),textAlign:"center",cursor:"pointer",transition:"all 0.3s ease","&:hover":{borderColor:e.palette.primary.main,backgroundColor:e.palette.action.hover}})),co=H(Xt)(({status:e})=>({fontSize:"14px",borderRadius:"4px",fontWeight:500,backgroundColor:e==="Active"?"#cdefd6":e==="Draft"?"#FFC88787":e==="Archived"?"#FAFFC0":"#cddcef"})),uo=()=>{const e=qt(i=>i.userManagement.userData),t=Gt(),a=Jt(),r=new URLSearchParams(t.search).get("BroadcastId"),d=new Date,S=new Date;S.setDate(d.getDate()+7);const[s,T]=_.useState({title:"",description:"",category:"",startDate:d,endDate:S,module:"",files:null,file:"",status:"",link:""}),[x,b]=_.useState(!1),[I,se]=_.useState(!1),[vt,k]=_.useState(!1),[wt,De]=_.useState(!1),[Z,Ee]=_.useState(!1),[po,V]=_.useState(!1),[z,Ce]=_.useState([]),[ie,le]=_.useState(!1),[Ot,F]=_.useState(!1),[St,yo]=_.useState(""),[Tt,ho]=_.useState("success"),[Dt,Ae]=_.useState(),[Et,Ct]=_.useState(null);_.useEffect(()=>{(()=>{if(!window.google){const c=document.createElement("script");c.src="https://accounts.google.com/gsi/client",c.async=!0,c.defer=!0,document.head.appendChild(c)}})(),Mt()},[]);const At=async()=>new Promise((i,c)=>{window.google?window.google.accounts.oauth2.initTokenClient({client_id:ro,scope:oo,callback:u=>{u.error?(console.error("Authentication error:",u.error),c(u.error)):(Ct(u.access_token),i(u.access_token))}}).requestAccessToken():c("Google API not loaded")}),xt=async()=>{try{return Et||await At()}catch(i){throw console.error("Failed to get authentication token:",i),i}},Mt=()=>{const i=u=>{const y=u.broadcastDetailsDto;T({title:y.broadcastTitle||"",description:y.description||"",category:y.broadcastCategory||"",startDate:y.createdDate?new Date(y.createdDate):d,endDate:y.endDate?new Date(y.endDate):S,createdBy:y.createdBy||"",module:y.module||"",file:y.fileName||"",status:y.status||"",link:y.externalUrl||"",files:null})},c=u=>{console.error("Error fetching broadcast details:",u)};Me(`/${ee}/broadcastManagement/getBroadcastDetailsById/${r}`,"get",i,c)},$=(i,c)=>{T(u=>({...u,[i]:c})),z.includes(i)&&Ce(u=>u.filter(y=>y!==i))},Rt=i=>{T(c=>({...c,files:i.target.files}))},It=()=>{const i=[],c=["category","title","description"];return s.module&&c.push("module"),c.forEach(u=>{(!s[u]||s[u].trim()==="")&&i.push(u)}),s.startDate>=s.endDate&&i.push("dateRange"),Ce(i),i.length===0},M=()=>["Inactive","Archived"].includes(s.status),kt=async(i,c)=>{var U,R;const u=sa(ee);F(!0);const y=new FormData;y.append("file",i),c&&y.append("accessToken",c);const L=await fetch(`${u}${(R=(U=ia)==null?void 0:U.API)==null?void 0:R.UPLOAD_VIDEO}`,{method:"POST",body:y});if(!L.ok){const Vt=await L.text();throw new Error(`Upload failed: ${L.status} ${L.statusText} - ${Vt}`)}return await L.json()},$t=async(i=!1)=>{const c=new FormData;s.files&&[...s.files].forEach(y=>c.append("files",y));const u={broadcastId:r,broadcastCategory:s.category,broadcastTitle:s.title,createdBy:(e==null?void 0:e.displayName)||"",startDate:ue(s.startDate).format("YYYY-MM-DD HH:mm:ss.000"),endDate:ue(s.endDate).format("YYYY-MM-DD HH:mm:ss.000"),description:s.description,module:s.module,createdDate:ue(d).format("YYYY-MM-DD HH:mm:ss.000"),externalUrl:s.link,...i&&{status:"Draft"}};return c.append("broadcastDetails",JSON.stringify(u)),c},Lt=async(i=!1)=>{if(!It()){V(!0),k(!0);return}try{let c=null;if(s.category==="Videos"&&!i&&s.files&&s.files.length>0){le(!0);try{c=await xt()}catch{le(!1),V(!0),k(!0);return}le(!1);const U=[...s.files].map(R=>kt(R,c));try{const R=await Promise.all(U)}catch(R){console.error("Video upload failed:",R),V(!0),k(!0);return}}else F(!0);const u=await $t(i),y=U=>{b(!1),F(!1),Ae("Broadcast updated successfully!"),se(!0)},L=U=>{console.error("Error updating broadcast:",U),V(!0),k(!0),F(!1),Ae("Failed Updating Broadcast!"),se(!0)};Me(`/${ee}/broadcastManagement/updateBroadcastDetails`,"putformdata",y,L,u)}catch(c){console.error("Error in submission process:",c),V(!0),k(!0),F(!1)}},xe=()=>{b(!0)},Ut=()=>{Lt(!Z)},Bt=()=>{De(!0)},Nt=()=>s.category==="Videos"?".mp4":".jpeg,.jpg,.png",jt=()=>s.category==="Videos"?"Only MP4 format supported":"Only PNG, JPEG, JPG formats supported",Ht=`/${ee}/broadcastManagement/showBroadcastById/${r}`;return l(K,{sx:{padding:2,paddingBottom:10},children:[n(D,{container:!0,sx:{borderRadius:2,marginBottom:2},children:n(D,{container:!0,children:n(D,{item:!0,md:7,style:{padding:"16px",paddingLeft:""},children:l(Q,{direction:"row",children:[n(ke,{onClick:()=>a("/configCockpit/broadcastConfigurations"),color:"primary","aria-label":"back",children:n(Qt,{sx:{fontSize:"25px",color:"#000000"}})}),l(K,{children:[n(p,{variant:"h5",paddingTop:"0.3rem",fontSize:"20px",children:l("strong",{children:["Edit Broadcast: ",r]})}),n(p,{variant:"body2",color:"#777",fontSize:"12px",children:"This view displays the details of the broadcast and allows you to edit it"})]})]})})})}),l(no,{children:[n(so,{children:"Broadcast Configuration"}),l(D,{container:!0,spacing:3,children:[l(D,{item:!0,xs:12,md:6,lg:3,children:[l(p,{variant:"subtitle2",gutterBottom:!0,children:["Broadcast Category ",n("span",{style:{color:"red"},children:"*"})]}),n(Re,{fullWidth:!0,size:"small",children:l(io,{value:s.category,onChange:i=>$("category",i.target.value),displayEmpty:!0,disabled:M(),error:z.includes("category"),renderValue:i=>i||n(p,{color:"text.secondary",children:"Select Category"}),children:[n(de,{value:"",children:n(p,{color:"text.secondary",children:"Select Category"})}),n(de,{value:"Announcements",children:l(Q,{direction:"row",spacing:1,alignItems:"center",children:[n(la,{color:"primary"}),n(p,{children:"Announcements"})]})}),n(de,{value:"Videos",children:l(Q,{direction:"row",spacing:1,alignItems:"center",children:[n(ca,{color:"primary"}),n(p,{children:"Videos"})]})})]})})]}),l(D,{item:!0,xs:12,md:6,lg:3,children:[l(p,{variant:"subtitle2",gutterBottom:!0,children:["Module ",s.module&&n("span",{style:{color:"red"},children:"*"})]}),n(Re,{fullWidth:!0,size:"small",children:n(ua,{options:[{code:"Material",desc:""},{code:"Cost Center",desc:""}],value:s.module,onChange:i=>$("module",i==null?void 0:i.code),placeholder:"Select Module",disabled:M(),minWidth:"100%",error:z.includes("module")})})]}),l(D,{item:!0,xs:12,md:6,lg:3,children:[l(p,{variant:"subtitle2",gutterBottom:!0,children:["Start Date ",n("span",{style:{color:"red"},children:"*"})]}),n(Le,{size:"sm",placeholder:"Select Start Date",value:s.startDate,onChange:i=>$("startDate",i),format:"dd MMM yyyy",disabled:M(),style:{width:"100%",height:"40px"}})]}),l(D,{item:!0,xs:12,md:6,lg:3,children:[l(p,{variant:"subtitle2",gutterBottom:!0,children:["End Date ",n("span",{style:{color:"red"},children:"*"})]}),n(Le,{size:"sm",placeholder:"Select End Date",value:s.endDate,onChange:i=>$("endDate",i),format:"dd MMM yyyy",disabled:M(),style:{width:"100%",height:"40px"}})]}),l(D,{item:!0,xs:12,md:6,lg:3,children:[n(p,{variant:"subtitle2",gutterBottom:!0,children:"Broadcast Status"}),n(co,{status:s.status,label:s.status||"Draft",sx:{marginTop:1}})]}),l(D,{item:!0,xs:12,children:[l(p,{variant:"subtitle2",gutterBottom:!0,children:["Broadcast Title ",n("span",{style:{color:"red"},children:"*"}),l(p,{component:"span",variant:"caption",color:"text.secondary",children:[" ","(Max 100 characters)"]})]}),n(fe,{fullWidth:!0,placeholder:"Enter broadcast title",value:s.title,onChange:i=>$("title",i.target.value),disabled:M(),error:z.includes("title"),inputProps:{maxLength:100},helperText:`${s.title.length}/100`})]}),l(D,{item:!0,xs:12,children:[l(p,{variant:"subtitle2",gutterBottom:!0,children:["Broadcast Description ",n("span",{style:{color:"red"},children:"*"}),l(p,{component:"span",variant:"caption",color:"text.secondary",children:[" ","(Max 300 characters)"]})]}),n(fe,{fullWidth:!0,multiline:!0,rows:4,placeholder:"Enter broadcast description",value:s.description,onChange:i=>$("description",i.target.value),disabled:M(),error:z.includes("description"),inputProps:{maxLength:300},helperText:`${s.description.length}/300`})]}),s.file&&l(D,{item:!0,xs:12,children:[n(p,{variant:"subtitle2",gutterBottom:!0,children:"Current Document"}),l(K,{sx:{display:"flex",alignItems:"center",gap:1},children:[n(p,{variant:"body2",children:s.file}),s.category==="Videos"?n(ea,{title:"View Video",children:n(ke,{onClick:Bt,size:"small",children:ha})}):n(fa,{index:r,name:s.file,isBroadcast:!0})]})]}),!M()&&l(Zt,{children:[l(D,{item:!0,xs:12,md:8,children:[n(p,{variant:"subtitle2",gutterBottom:!0,children:"Upload New Document"}),l(lo,{children:[n("input",{accept:Nt(),style:{display:"none"},id:"file-upload",multiple:!0,type:"file",onChange:Rt}),n("label",{htmlFor:"file-upload",children:l(Q,{spacing:1,alignItems:"center",children:[n(da,{color:"primary",sx:{fontSize:40}}),n(p,{variant:"body2",children:"Click to upload new files"}),n(p,{variant:"caption",color:"text.secondary",children:jt()}),s.files&&l(p,{variant:"caption",color:"primary",children:[s.files.length," file(s) selected"]})]})})]})]}),l(D,{item:!0,xs:12,md:4,children:[n(p,{variant:"subtitle2",gutterBottom:!0,children:"External URL"}),n(fe,{fullWidth:!0,placeholder:"Enter URL (optional)",value:s.link,onChange:i=>$("link",i.target.value),type:"url"})]})]})]})]}),!M()&&n(aa,{sx:{position:"fixed",bottom:0,left:0,right:0,zIndex:5},elevation:2,children:l(ta,{showLabels:!0,className:"container_BottomNav",sx:{display:"flex",justifyContent:"flex-end"},children:[n($e,{size:"small",variant:"outlined",onClick:()=>{Ee(!1),xe()},className:"btn-mr",sx:{marginRight:1},disabled:ie,children:"Save As Draft"}),n($e,{size:"small",variant:"contained",onClick:()=>{Ee(!0),xe()},disabled:ie,children:ie?"Authenticating...":"Publish"})]})}),n(ra,{open:wt,onClose:()=>De(!1),"aria-labelledby":"video-modal",children:n(K,{sx:{position:"absolute",top:"50%",left:"50%",transform:"translate(-50%, -50%)",bgcolor:"background.paper",borderRadius:2,boxShadow:24,p:4,maxWidth:"80vw",maxHeight:"80vh"},children:n(ao,{url:Ht,controls:!0,width:"100%",height:"400px"})})}),n(Ie,{dialogState:x,closeReusableDialog:()=>b(!1),dialogTitle:"Confirm Broadcast Update",dialogMessage:`Are you sure you want to ${Z?"publish":"save as draft"} this broadcast update?${s.category==="Videos"&&Z?" You will need to authenticate with Google for video upload.":""}`,handleDialogConfirm:Ut,handleDialogReject:()=>b(!1),showCancelButton:!0,dialogCancelText:"Cancel",dialogOkText:Z?"Publish":"Save Draft",dialogSeverity:"success"}),n(oa,{openSnackBar:I,alertMsg:Dt,alertType:Tt,handleSnackBarClose:()=>{se(!1),a("/configCockpit/broadcastConfigurations")}}),n(Ie,{dialogState:vt,closeReusableDialog:()=>k(!1),dialogTitle:"Validation Error",dialogMessage:"Please fill in all required fields correctly.",handleDialogConfirm:()=>k(!1),dialogOkText:"OK",dialogSeverity:"error"}),n(na,{blurLoading:Ot,loaderMessage:St})]})},So=Object.freeze(Object.defineProperty({__proto__:null,default:uo},Symbol.toStringTag,{value:"Module"}));export{So as E,Xa as p,ae as u};
