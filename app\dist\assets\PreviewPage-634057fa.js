import{c9 as hs,ca as Ts,cb as fs,cs as Cs,ai as Js,Z as me,an as da,fs as zT,T as Wl,c as D,aj as so,j as t,d as Be,ft as YT,a6 as jl,$ as mi,bP as JT,B as ue,bG as Ui,bQ as Ph,fu as Lh,fv as Bh,al as Xs,am as oo,ae as ho,n as Y,fw as Ga,fx as ie,fy as us,cZ as To,bf as to,u as Vs,fz as Yh,dJ as XT,aX as h,be as f,fA as VT,aO as J,fB as KT,fC as QT,fD as ZT,fE as ef,fF as af,fG as lf,fH as sf,fI as Uh,fJ as of,fK as rf,fL as tf,cR as nf,g as Ni,aZ as Fa,r as N,fM as Mi,fN as wd,aP as Wd,cq as Ai,C as ma,bx as df,F as al,dm as cf,aT as ca,aN as uf,fO as hf,fP as gi,fQ as pi,s as bi,b as jd,cI as pl,ah as Jh,ad as Xh,ag as Fi,ak as Fd,aa as Vh,ez as Kh,aE as Qh,aF as kd,aG as Zh,aD as es,fR as va,fS as Ii,cw as wi,a as Ks,fT as wl,fU as Tf,cH as ro,bD as ff,fV as Cf,aK as Gi,fW as gf,aJ as Zl,fX as eT,fY as Hd,bp as pf,bl as Rf,fZ as Ef,bm as Af,bq as If,bo as Df,c2 as _f,f_ as Nf,f$ as Ke,au as Qe,g0 as Pi,g1 as Gh,g2 as Ri,bK as Zs,g3 as xh,g4 as Li,da as Mf,g5 as Di,bE as Hh,O as lo,b1 as aT,af as bf,g6 as Of,g7 as yf,g8 as vf,g9 as qf,ga as Sf,gb as Pf,gc as Lf,dy as eo,bs as ao,gd as Bf,bS as Uf,ge as lT,gf as Gf,gg as mh,gh as xf,bI as xi,gi as Hf,gj as mf,ei as Ei,gk as Ff,a9 as wf,dj as ze,k as Wf,A as jf,i as kf,gl as $f}from"./index-f7d9b065.js";import{d as zf,a as Yf}from"./AttachFile-8d552da8.js";import{u as sT,a as Jf,M as Xf,b as Vf,D as Kf}from"./UtilDoc-6f590135.js";import{d as Qf}from"./CloudUpload-0ba6431e.js";import{i as Fh}from"./utilityImages-067c3dc2.js";import{a as Zf}from"./FileUploadOutlined-4a68a28a.js";import{d as eC}from"./Delete-5278579a.js";import{a as aC}from"./ReusablePromptBox-e1871d49.js";import{d as lC}from"./Description-ab582559.js";import{d as sC,a as oC}from"./DataObject-52409c14.js";import{d as rC}from"./Download-52c4427b.js";import{u as tC}from"./useFinanceCostingRows-ffbb569f.js";import{d as nC}from"./CheckCircleOutline-e186af3e.js";var Wi={},dC=Ts;Object.defineProperty(Wi,"__esModule",{value:!0});var Hi=Wi.default=void 0,iC=dC(hs()),cC=fs;Hi=Wi.default=(0,iC.default)((0,cC.jsx)("path",{d:"M11 16h2v2h-2zm1-14C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2m0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8m0-14c-2.21 0-4 1.79-4 4h2c0-1.1.9-2 2-2s2 .9 2 2c0 2-3 1.75-3 5h2c0-2.25 3-2.5 3-5 0-2.21-1.79-4-4-4"}),"HelpOutlineTwoTone");const uC=Cs(Js)(({theme:r})=>{var d,R,o,p;return{"& .MuiPaper-root":{borderRadius:"12px",boxShadow:"0 4px 20px rgba(0, 0, 0, 0.1)",border:`1px solid ${(R=(d=me)==null?void 0:d.placeholder)==null?void 0:R.color}`,backgroundColor:(p=(o=me)==null?void 0:o.primary)==null?void 0:p.white,maxWidth:"600px"}}}),hC=Cs(da)(({theme:r})=>{var d,R,o,p;return{borderRadius:"8px",padding:"1.2rem 1rem !important",backgroundColor:(R=(d=me)==null?void 0:d.primary)==null?void 0:R.lightPlus,"&:hover":{backgroundColor:(p=(o=me)==null?void 0:o.info)==null?void 0:p.dark,boxShadow:"0 2px 8px rgba(25, 118, 210, 0.3)"},transition:"all 0.2s ease-in-out",textTransform:"none",fontWeight:500}}),wh=Cs(zT)(({theme:r})=>{var d,R;return{borderRadius:"6px",backgroundColor:(R=(d=me)==null?void 0:d.secondary)==null?void 0:R.lightYellow,display:"flex",alignItems:"center","& .MuiAlert-icon":{display:"flex",alignItems:"center",justifyContent:"center"},marginTop:"1rem"}}),Wh=Cs(Wl)({maxWidth:"none"}),Ig=({onDownloadTypeChange:r,open:d,downloadType:R,handleDownloadTypeChange:o,onClose:p})=>{var O,l,u,x,ee,m;const e=R==="systemGenerated"?(O=ho)==null?void 0:O.SYSTEM_GENERATED_MSG:(l=ho)==null?void 0:l.EMAIL_DELIVERY_MSG;return D(uC,{open:d,onClose:p,children:[D(so,{sx:{backgroundColor:(x=(u=me)==null?void 0:u.success)==null?void 0:x.light,padding:"1rem 1.5rem",borderBottom:`1px solid ${(m=(ee=me)==null?void 0:ee.primary)==null?void 0:m.whiteSmoke}`,display:"flex",justifyContent:"space-between",alignItems:"center",mb:2},children:[t(Be,{variant:"h6",sx:{fontWeight:600,color:"#333",letterSpacing:"0.2px"},children:"Select Download Option"}),t(jl,{size:"small",onClick:p,children:t(YT,{fontSize:"small"})})]}),t(Xs,{sx:{padding:"1.5rem"},children:D(mi,{component:"fieldset",sx:{width:"100%"},children:[D(JT,{"aria-label":"download-option",name:"download-option",value:R,onChange:o,sx:{display:"flex",flexDirection:"row",gap:2,alignItems:"center"},children:[D(ue,{sx:{flex:1,padding:"0.4rem",borderRadius:"6px",backgroundColor:R==="systemGenerated"?"#f0f4ff":"#ffffff",border:R==="systemGenerated"?"1px solid #1976d2":"1px solid #e0e0e0",transition:"all 0.2s ease-in-out","&:hover":{backgroundColor:"#f7f9fc"},display:"flex",alignItems:"center",justifyContent:"space-between"},children:[t(Ui,{value:"systemGenerated",control:t(Ph,{color:"primary",size:"small"}),label:D(ue,{sx:{display:"flex",alignItems:"center",gap:.5},children:[t(Lh,{sx:{mr:1,color:"#1976d2"}}),t(Be,{sx:{fontWeight:500,color:"#333",fontSize:"0.85rem"},children:"System-Generated"})]}),sx:{flexGrow:1,margin:0}}),t(Wh,{title:t("span",{style:{whiteSpace:"nowrap",fontSize:"12px"},children:"Download Excel file instantly"}),arrow:!0,placement:"top",children:t(Hi,{sx:{color:"#1976d2",fontSize:"1.1rem",verticalAlign:"middle",mr:1}})})]}),D(ue,{sx:{flex:1,padding:"0.4rem",borderRadius:"8px",backgroundColor:R==="mailGenerated"?"#f0f4ff":"#ffffff",border:R==="mailGenerated"?"1px solid #1976d2":"1px solid #e0e0e0",transition:"all 0.2s ease-in-out","&:hover":{backgroundColor:"#f7f9fc"},display:"flex",alignItems:"center",justifyContent:"space-between"},children:[t(Ui,{value:"mailGenerated",control:t(Ph,{color:"primary",size:"small"}),label:D(ue,{sx:{display:"flex",alignItems:"center",gap:.5},children:[t(Bh,{sx:{mr:1,color:"#1976d2"}}),t(Be,{sx:{fontWeight:500,color:"#333",fontSize:"0.85rem"},children:"Mail-Generated"})]}),sx:{flexGrow:1,margin:0}}),t(Wh,{title:t("span",{style:{whiteSpace:"nowrap",fontSize:"12px"},children:"Receive the Excel file via email"}),arrow:!0,placement:"top",children:t(Hi,{sx:{color:"#1976d2",fontSize:"1.1rem",verticalAlign:"middle",mr:1}})})]})]}),t(wh,{severity:"info",children:t(Be,{sx:{fontSize:"0.9rem",color:"#555"},children:e[0]})}),t(wh,{severity:"info",children:t(Be,{sx:{fontSize:"0.9rem",color:"#555"},children:e[1]})})]})}),t(oo,{sx:{padding:"0 1.5rem 1.5rem"},children:t(hC,{variant:"contained",onClick:r,startIcon:R==="systemGenerated"?t(Lh,{}):t(Bh,{}),children:R==="systemGenerated"?"Download":"Send Email"})})]})},ji=r=>{const d=Y(W=>W.payload.changeFieldRows),R=Y(W=>W.payload.changeLogData),o=Y(W=>W.request),p=Y(W=>W.payload),e=Y(W=>W.payload.dynamicKeyValues),O=Y(W=>W.payload.selectedRows),l=Y(W=>W.userManagement.taskData),u=r||(e==null?void 0:e.templateName),x=(W,c)=>{var T,U,g,L,B,b;return R[W]?{RequestId:((L=p==null?void 0:p.payloadData)==null?void 0:L.RequestId)||((B=p==null?void 0:p.changeLogData)==null?void 0:B.RequestId),ChildRequestId:((b=e==null?void 0:e.childRequestHeaderData)==null?void 0:b.ChildRequestId)??null,ChangeLogId:c??null,...R[W]}:{RequestId:((T=p==null?void 0:p.payloadData)==null?void 0:T.RequestId)||((U=p==null?void 0:p.changeLogData)==null?void 0:U.RequestId),ChildRequestId:((g=e==null?void 0:e.childRequestHeaderData)==null?void 0:g.ChildRequestId)??null,ChangeLogId:c??null}},ee=W=>{var c,T,U,g,L,B,b,q,P,G,F,w,j,Z;if(r===((c=Ga)==null?void 0:c.LOGISTIC)||(e==null?void 0:e.templateName)===((T=Ga)==null?void 0:T.LOGISTIC))return m(W);if(r===((U=Ga)==null?void 0:U.ITEM_CAT)||(e==null?void 0:e.templateName)===((g=Ga)==null?void 0:g.ITEM_CAT))return Ce(W);if(r===((L=Ga)==null?void 0:L.MRP)||(e==null?void 0:e.templateName)===((B=Ga)==null?void 0:B.MRP))return re(W);if(r===((b=Ga)==null?void 0:b.UPD_DESC)||(e==null?void 0:e.templateName)===((q=Ga)==null?void 0:q.UPD_DESC))return Q(W);if(r===((P=Ga)==null?void 0:P.WARE_VIEW_2)||(e==null?void 0:e.templateName)===((G=Ga)==null?void 0:G.WARE_VIEW_2))return n(W);if(r===((F=Ga)==null?void 0:F.CHG_STAT)||(e==null?void 0:e.templateName)===((w=Ga)==null?void 0:w.CHG_STAT))return Oe(W);if(r===((j=Ga)==null?void 0:j.SET_DNU)||(e==null?void 0:e.templateName)===((Z=Ga)==null?void 0:Z.SET_DNU))return he(W)},m=W=>{const c=d.reduce((T,U)=>{if((O==null?void 0:O.length)!==0&&!(O!=null&&O.includes(U==null?void 0:U.id)))return T;const g=U==null?void 0:U.Material;return T[g]||(T[g]=[]),T[g].push(U),T},{});if(W){const T=["id","MaterialId","ClientId","slNo","ChangeLogId","MatlType","Version"];return Object.keys(c).map(g=>{var G,F,w,j,Z,le,te,i,y,V,v,z,K,Ee,X,ye;const L=c[g],{MaterialId:B,ClientId:b,ChangeLogId:q,Version:P}=L[(L==null?void 0:L.length)-1];return{MaterialId:B,Version:P,ChangeLogId:q,Material:g,MatlType:((w=(F=c[g])==null?void 0:F[((G=c[g])==null?void 0:G.length)-1])==null?void 0:w.MatlType)||"",Function:"UPD",TaskId:(e==null?void 0:e.otherPayloadData.TaskId)||"",TaskName:(e==null?void 0:e.otherPayloadData.TaskName)||"",creationTime:(e==null?void 0:e.otherPayloadData.CreationTime)||"",dueDate:(e==null?void 0:e.otherPayloadData.DueDate)||"",IsFirstCreate:!1,MassEditId:e==null?void 0:e.otherPayloadData.MassEditId,MassChildEditId:e==null?void 0:e.otherPayloadData.MassChildEditId,TotalIntermediateTasks:e==null?void 0:e.otherPayloadData.TotalIntermediateTasks,IntermediateTaskCount:e==null?void 0:e.otherPayloadData.IntermediateTaskCount,Toclientdata:{ClientId:b,Material:g,Function:"UPD"},Touomdata:L.map(je=>{const $={...je,Function:"UPD"};return T.forEach(De=>delete $[De]),$}),Tochildrequestheaderdata:{ChildRequestId:((j=e==null?void 0:e.childRequestHeaderData)==null?void 0:j.ChildRequestId)||null,MaterialGroupType:((Z=e==null?void 0:e.childRequestHeaderData)==null?void 0:Z.MaterialGroupType)||null,TaskId:(l==null?void 0:l.taskId)||null,Comments:(e==null?void 0:e.Comments)||"",TotalIntermediateTasks:((le=e==null?void 0:e.childRequestHeaderData)==null?void 0:le.TotalIntermediateTasks)||null,IntermediateTaskCount:((te=e==null?void 0:e.childRequestHeaderData)==null?void 0:te.IntermediateTaskCount)||null,ReqCreatedBy:((i=e==null?void 0:e.childRequestHeaderData)==null?void 0:i.ReqCreatedBy)||null,ReqCreatedOn:((y=e==null?void 0:e.childRequestHeaderData)==null?void 0:y.ReqCreatedOn)||null,ReqUpdatedOn:((V=e==null?void 0:e.childRequestHeaderData)==null?void 0:V.ReqUpdatedOn)||null,RequestType:((v=e==null?void 0:e.childRequestHeaderData)==null?void 0:v.RequestType)||null,RequestPrefix:((z=e==null?void 0:e.childRequestHeaderData)==null?void 0:z.RequestPrefix)||null,RequestDesc:((K=e==null?void 0:e.childRequestHeaderData)==null?void 0:K.RequestDesc)||null,RequestPriority:((Ee=e==null?void 0:e.childRequestHeaderData)==null?void 0:Ee.RequestPriority)||null,RequestStatus:((X=e==null?void 0:e.childRequestHeaderData)==null?void 0:X.RequestStatus)||null,CurrentLevel:(l==null?void 0:l.ATTRIBUTE_3)||"",CurrentLevelName:(l==null?void 0:l.ATTRIBUTE_4)||"",ParticularLevel:(e==null?void 0:e.Level)||"-1",TaskName:(l==null?void 0:l.taskDesc)||"",ApproverGroup:(l==null?void 0:l.ATTRIBUTE_5)||"",TaskCreatedOn:l!=null&&l.createdOn?ie(l==null?void 0:l.createdOn):null,DueDate:l!=null&&l.compDeadline?ie(l==null?void 0:l.compDeadline):null,Version:((ye=e==null?void 0:e.childRequestHeaderData)==null?void 0:ye.Version)||us.DEFAULT_VERSION},Torequestheaderdata:(e==null?void 0:e.requestHeaderData)||{},Tomaterialerrordata:(e==null?void 0:e.errorData[g])||{},TemplateName:u,changeLogData:x(g,q),...(e==null?void 0:e.Comments)&&{Comments:(e==null?void 0:e.Comments)||""}}})}else{const T=["id","slNo","MatlType"];return Object.keys(c).map(g=>{var L,B,b,q,P,G,F,w,j,Z,le,te,i,y,V,v,z,K;return{Touomdata:c[g].map(Ee=>{const X={...Ee,Function:"UPD"};return T.forEach(ye=>delete X[ye]),X}),Torequestheaderdata:{RequestId:(L=o==null?void 0:o.requestHeader)==null?void 0:L.requestId,ReqCreatedBy:(B=o==null?void 0:o.requestHeader)==null?void 0:B.reqCreatedBy,ReqCreatedOn:ie((b=o==null?void 0:o.requestHeader)==null?void 0:b.reqCreatedOn),ReqUpdatedOn:ie((q=o==null?void 0:o.requestHeader)==null?void 0:q.reqCreatedOn),RequestType:(P=o==null?void 0:o.requestHeader)==null?void 0:P.requestType,RequestPriority:(G=o==null?void 0:o.requestHeader)==null?void 0:G.requestPriority,RequestDesc:(F=o==null?void 0:o.requestHeader)==null?void 0:F.requestDesc,RequestStatus:(w=o==null?void 0:o.requestHeader)==null?void 0:w.requestStatus,FirstProd:((j=p==null?void 0:p.payloadData)==null?void 0:j.FirstProductionDate)||null,LaunchDate:((Z=p==null?void 0:p.payloadData)==null?void 0:Z.LaunchDate)||null,LeadingCat:(le=o==null?void 0:o.requestHeader)==null?void 0:le.leadingCat,Division:(te=o==null?void 0:o.requestHeader)==null?void 0:te.division,Region:(i=o==null?void 0:o.requestHeader)==null?void 0:i.region,TemplateName:(y=o==null?void 0:o.requestHeader)==null?void 0:y.templateName,FieldName:(V=o==null?void 0:o.requestHeader)==null?void 0:V.fieldName},Tochildrequestheaderdata:{},Toclientdata:{ClientId:null,Function:"UPD"},Material:g,MatlType:((K=(z=c[g])==null?void 0:z[((v=c[g])==null?void 0:v.length)-1])==null?void 0:K.MatlType)||"",TemplateName:u,IsFirstCreate:!0,Function:"UPD",changeLogData:x(g),...(e==null?void 0:e.Comments)&&{Comments:(e==null?void 0:e.Comments)||""}}})}},Ce=W=>{const c=d.reduce((T,U)=>{if((O==null?void 0:O.length)!==0&&!(O!=null&&O.includes(U==null?void 0:U.id)))return T;const g=U==null?void 0:U.Material;return T[g]||(T[g]=[]),T[g].push(U),T},{});if(W){const T=["id","MaterialId","ClientId","slNo","ChangeLogId","MatlType","Version"];return Object.keys(c).map(g=>{var P,G,F,w,j,Z,le,te,i,y,V,v,z,K,Ee,X,ye,je;const L=c[g],{MaterialId:B,ClientId:b,Version:q}=L[0];return{MaterialId:B,Version:q,Material:g,MassChildEditId:e==null?void 0:e.otherPayloadData.MassChildEditId,MatlType:((F=(G=c[g])==null?void 0:G[((P=c[g])==null?void 0:P.length)-1])==null?void 0:F.MatlType)||"",Function:"UPD",TaskId:(e==null?void 0:e.otherPayloadData.TaskId)||"",TaskName:(e==null?void 0:e.otherPayloadData.TaskName)||"",creationTime:(e==null?void 0:e.otherPayloadData.CreationTime)||"",dueDate:(e==null?void 0:e.otherPayloadData.DueDate)||"",IsFirstCreate:!1,MassEditId:e==null?void 0:e.otherPayloadData.MassEditId,TotalIntermediateTasks:e==null?void 0:e.otherPayloadData.TotalIntermediateTasks,IntermediateTaskCount:e==null?void 0:e.otherPayloadData.IntermediateTaskCount,Tosalesdata:L.map($=>{const De={...$,Function:"UPD"};return T.forEach(de=>delete De[de]),De}),Tochildrequestheaderdata:{ChildRequestId:((w=e==null?void 0:e.childRequestHeaderData)==null?void 0:w.ChildRequestId)||null,MaterialGroupType:((j=e==null?void 0:e.childRequestHeaderData)==null?void 0:j.MaterialGroupType)||null,TaskId:(l==null?void 0:l.taskId)||null,Comments:(e==null?void 0:e.Comments)||"",TotalIntermediateTasks:((Z=e==null?void 0:e.childRequestHeaderData)==null?void 0:Z.TotalIntermediateTasks)||null,IntermediateTaskCount:((le=e==null?void 0:e.childRequestHeaderData)==null?void 0:le.IntermediateTaskCount)||null,ReqCreatedBy:((te=e==null?void 0:e.childRequestHeaderData)==null?void 0:te.ReqCreatedBy)||null,ReqCreatedOn:((i=e==null?void 0:e.childRequestHeaderData)==null?void 0:i.ReqCreatedOn)||null,ReqUpdatedOn:((y=e==null?void 0:e.childRequestHeaderData)==null?void 0:y.ReqUpdatedOn)||null,RequestType:((V=e==null?void 0:e.childRequestHeaderData)==null?void 0:V.RequestType)||null,RequestPrefix:((v=e==null?void 0:e.childRequestHeaderData)==null?void 0:v.RequestPrefix)||null,RequestDesc:((z=e==null?void 0:e.childRequestHeaderData)==null?void 0:z.RequestDesc)||null,RequestPriority:((K=e==null?void 0:e.childRequestHeaderData)==null?void 0:K.RequestPriority)||null,RequestStatus:((Ee=e==null?void 0:e.childRequestHeaderData)==null?void 0:Ee.RequestStatus)||null,CurrentLevel:(l==null?void 0:l.ATTRIBUTE_3)||"",CurrentLevelName:(l==null?void 0:l.ATTRIBUTE_4)||"",ParticularLevel:(e==null?void 0:e.Level)||"-1",TaskName:(l==null?void 0:l.taskDesc)||"",ApproverGroup:(l==null?void 0:l.ATTRIBUTE_5)||"",TaskCreatedOn:l!=null&&l.createdOn?ie(l==null?void 0:l.createdOn):null,DueDate:l!=null&&l.compDeadline?ie(l==null?void 0:l.compDeadline):null,Version:((X=e==null?void 0:e.childRequestHeaderData)==null?void 0:X.RequestStatus)||us.DEFAULT_VERSION},Torequestheaderdata:(e==null?void 0:e.requestHeaderData)||{},Tomaterialerrordata:(e==null?void 0:e.errorData[g])||{},TemplateName:u,changeLogData:x(g,(je=(ye=c[g])==null?void 0:ye[0])==null?void 0:je.ChangeLogId),...(e==null?void 0:e.Comments)&&{Comments:(e==null?void 0:e.Comments)||""}}})}else{const T=["id","slNo","MatlType"];return Object.keys(c).map(g=>{var L,B,b,q,P,G,F,w,j,Z,le,te,i,y,V,v,z,K;return{Tosalesdata:c[g].map(Ee=>{const X={...Ee,Function:"UPD"};return T.forEach(ye=>delete X[ye]),X}),Torequestheaderdata:{RequestId:(L=o==null?void 0:o.requestHeader)==null?void 0:L.requestId,ReqCreatedBy:(B=o==null?void 0:o.requestHeader)==null?void 0:B.reqCreatedBy,ReqCreatedOn:ie((b=o==null?void 0:o.requestHeader)==null?void 0:b.reqCreatedOn),ReqUpdatedOn:ie((q=o==null?void 0:o.requestHeader)==null?void 0:q.reqCreatedOn),RequestType:(P=o==null?void 0:o.requestHeader)==null?void 0:P.requestType,RequestPriority:(G=o==null?void 0:o.requestHeader)==null?void 0:G.requestPriority,RequestDesc:(F=o==null?void 0:o.requestHeader)==null?void 0:F.requestDesc,RequestStatus:(w=o==null?void 0:o.requestHeader)==null?void 0:w.requestStatus,FirstProd:((j=p==null?void 0:p.payloadData)==null?void 0:j.FirstProductionDate)||null,LaunchDate:((Z=p==null?void 0:p.payloadData)==null?void 0:Z.LaunchDate)||null,LeadingCat:(le=o==null?void 0:o.requestHeader)==null?void 0:le.leadingCat,Division:(te=o==null?void 0:o.requestHeader)==null?void 0:te.division,Region:(i=o==null?void 0:o.requestHeader)==null?void 0:i.region,TemplateName:(y=o==null?void 0:o.requestHeader)==null?void 0:y.templateName,FieldName:(V=o==null?void 0:o.requestHeader)==null?void 0:V.fieldName},Tochildrequestheaderdata:{},Material:g,MatlType:((K=(z=c[g])==null?void 0:z[((v=c[g])==null?void 0:v.length)-1])==null?void 0:K.MatlType)||"",TemplateName:u,IsFirstCreate:!0,Function:"UPD",changeLogData:x(g),...(e==null?void 0:e.Comments)&&{Comments:(e==null?void 0:e.Comments)||""}}})}},re=W=>{if(W){const c={},T=["id","slNo","type","MaterialId","Plant","ChangeLogId","MatlType","Version"],U=["id","slNo","type","MaterialId","ChangeLogId","Version"];return Object.keys(d).forEach(L=>{d[L].forEach(B=>{const{Material:b,MaterialId:q,ChangeLogId:P,Version:G}=B;c[b]||(c[b]={Toclientdata:null,Toplantdata:[],MaterialId:q,Version:G,ChangeLogId:P,MatlType:""});const F={...B};L==="Basic Data"&&!c[b].Toclientdata?(c[b].MatlType=(F==null?void 0:F.MatlType)||"",T.forEach(w=>delete F[w]),c[b].Toclientdata=F):L==="Plant Data"&&(U.forEach(w=>delete F[w]),c[b].Toplantdata.push(F))})}),Object.keys(c).map(L=>{var B,b,q,P,G,F,w,j,Z,le,te,i,y,V;return{...c[L],Material:L,Function:"UPD",MassChildEditId:e==null?void 0:e.otherPayloadData.MassChildEditId,TaskId:(e==null?void 0:e.otherPayloadData.TaskId)||"",TaskName:(e==null?void 0:e.otherPayloadData.TaskName)||"",creationTime:(e==null?void 0:e.otherPayloadData.CreationTime)||"",dueDate:(e==null?void 0:e.otherPayloadData.DueDate)||"",IsFirstCreate:!1,MassEditId:e==null?void 0:e.otherPayloadData.MassEditId,TotalIntermediateTasks:e==null?void 0:e.otherPayloadData.TotalIntermediateTasks,IntermediateTaskCount:e==null?void 0:e.otherPayloadData.IntermediateTaskCount,Torequestheaderdata:(e==null?void 0:e.requestHeaderData)||{},Tomaterialerrordata:(e==null?void 0:e.errorData[L])||{},TemplateName:u,Tochildrequestheaderdata:{ChildRequestId:((B=e==null?void 0:e.childRequestHeaderData)==null?void 0:B.ChildRequestId)||null,MaterialGroupType:((b=e==null?void 0:e.childRequestHeaderData)==null?void 0:b.MaterialGroupType)||null,TaskId:(l==null?void 0:l.taskId)||null,Comments:(e==null?void 0:e.Comments)||"",TotalIntermediateTasks:((q=e==null?void 0:e.childRequestHeaderData)==null?void 0:q.TotalIntermediateTasks)||null,IntermediateTaskCount:((P=e==null?void 0:e.childRequestHeaderData)==null?void 0:P.IntermediateTaskCount)||null,ReqCreatedBy:((G=e==null?void 0:e.childRequestHeaderData)==null?void 0:G.ReqCreatedBy)||null,ReqCreatedOn:((F=e==null?void 0:e.childRequestHeaderData)==null?void 0:F.ReqCreatedOn)||null,ReqUpdatedOn:((w=e==null?void 0:e.childRequestHeaderData)==null?void 0:w.ReqUpdatedOn)||null,RequestType:((j=e==null?void 0:e.childRequestHeaderData)==null?void 0:j.RequestType)||null,RequestPrefix:((Z=e==null?void 0:e.childRequestHeaderData)==null?void 0:Z.RequestPrefix)||null,RequestDesc:((le=e==null?void 0:e.childRequestHeaderData)==null?void 0:le.RequestDesc)||null,RequestPriority:((te=e==null?void 0:e.childRequestHeaderData)==null?void 0:te.RequestPriority)||null,RequestStatus:((i=e==null?void 0:e.childRequestHeaderData)==null?void 0:i.RequestStatus)||null,CurrentLevel:(l==null?void 0:l.ATTRIBUTE_3)||"",CurrentLevelName:(l==null?void 0:l.ATTRIBUTE_4)||"",ParticularLevel:(e==null?void 0:e.Level)||"-1",TaskName:(l==null?void 0:l.taskDesc)||"",ApproverGroup:(l==null?void 0:l.ATTRIBUTE_5)||"",TaskCreatedOn:l!=null&&l.createdOn?ie(l==null?void 0:l.createdOn):null,DueDate:l!=null&&l.compDeadline?ie(l==null?void 0:l.compDeadline):null,Version:((y=e==null?void 0:e.childRequestHeaderData)==null?void 0:y.Version)||us.DEFAULT_VERSION},changeLogData:x(L,(V=c[L])==null?void 0:V.ChangeLogId),...(e==null?void 0:e.Comments)&&{Comments:(e==null?void 0:e.Comments)||""}}})}else{const c={},T=["id","slNo","type","Plant","MatlType"],U=["id","slNo","type"];return Object.keys(d).forEach(L=>{d[L].forEach(B=>{const{Material:b}=B;c[b]||(c[b]={Toclientdata:null,Toplantdata:[],MatlType:""});const q={...B};L==="Basic Data"&&!c[b].Toclientdata?(c[b].MatlType=(q==null?void 0:q.MatlType)||"",T.forEach(P=>delete q[P]),c[b].Toclientdata={...q,Function:"UPD"}):L==="Plant Data"&&(U.forEach(P=>delete q[P]),c[b].Toplantdata.push({...q,Function:"UPD"}))})}),Object.keys(c).map(L=>{var B,b,q,P,G,F,w,j,Z,le,te,i,y,V,v,z,K;return{...c[L],Torequestheaderdata:{RequestId:(B=o==null?void 0:o.requestHeader)==null?void 0:B.requestId,ReqCreatedBy:(b=o==null?void 0:o.requestHeader)==null?void 0:b.reqCreatedBy,ReqCreatedOn:ie((q=o==null?void 0:o.requestHeader)==null?void 0:q.reqCreatedOn),ReqUpdatedOn:ie((P=o==null?void 0:o.requestHeader)==null?void 0:P.reqCreatedOn),RequestType:(G=o==null?void 0:o.requestHeader)==null?void 0:G.requestType,RequestPriority:(F=o==null?void 0:o.requestHeader)==null?void 0:F.requestPriority,RequestDesc:(w=o==null?void 0:o.requestHeader)==null?void 0:w.requestDesc,RequestStatus:(j=o==null?void 0:o.requestHeader)==null?void 0:j.requestStatus,FirstProd:((Z=p==null?void 0:p.payloadData)==null?void 0:Z.FirstProductionDate)||null,LaunchDate:((le=p==null?void 0:p.payloadData)==null?void 0:le.LaunchDate)||null,LeadingCat:(te=o==null?void 0:o.requestHeader)==null?void 0:te.leadingCat,Division:(i=o==null?void 0:o.requestHeader)==null?void 0:i.division,Region:(y=o==null?void 0:o.requestHeader)==null?void 0:y.region,TemplateName:(V=o==null?void 0:o.requestHeader)==null?void 0:V.templateName,FieldName:(v=o==null?void 0:o.requestHeader)==null?void 0:v.fieldName},Tochildrequestheaderdata:{},Material:L,TemplateName:(z=o==null?void 0:o.requestHeader)==null?void 0:z.templateName,IsFirstCreate:!0,Function:"UPD",MassEditId:(K=o==null?void 0:o.requestHeader)==null?void 0:K.requestId,changeLogData:x(L),...(e==null?void 0:e.Comments)&&{Comments:(e==null?void 0:e.Comments)||""}}})}},Oe=W=>{if(W){const c={},T=["id","slNo","type","MaterialId","Plant","ChangeLogId","MatlType","Version"],U=["id","slNo","type","MaterialId","ChangeLogId","Version"],g=["id","slNo","type","MaterialId","ChangeLogId","Version"];return Object.keys(d).forEach(B=>{d[B].forEach(b=>{const{Material:q,MaterialId:P,ChangeLogId:G,Version:F}=b;c[q]||(c[q]={Toclientdata:null,Toplantdata:[],Tosalesdata:[],MaterialId:P,Version:F,ChangeLogId:G,MatlType:""});const w={...b};B==="Basic Data"&&!c[q].Toclientdata?(c[q].MatlType=(w==null?void 0:w.MatlType)||"",T.forEach(j=>delete w[j]),c[q].Toclientdata=w):B==="Plant Data"?(U.forEach(j=>delete w[j]),c[q].Toplantdata.push(w)):B==="Sales Data"&&(g.forEach(j=>delete w[j]),c[q].Tosalesdata.push(w))})}),Object.keys(c).map(B=>{var b,q,P,G,F,w,j,Z,le,te,i,y,V,v;return{...c[B],Material:B,MassChildEditId:e==null?void 0:e.otherPayloadData.MassChildEditId,Function:"UPD",TaskId:(e==null?void 0:e.otherPayloadData.TaskId)||"",TaskName:(e==null?void 0:e.otherPayloadData.TaskName)||"",creationTime:(e==null?void 0:e.otherPayloadData.CreationTime)||"",dueDate:(e==null?void 0:e.otherPayloadData.DueDate)||"",IsFirstCreate:!1,MassEditId:e==null?void 0:e.otherPayloadData.MassEditId,TotalIntermediateTasks:e==null?void 0:e.otherPayloadData.TotalIntermediateTasks,IntermediateTaskCount:e==null?void 0:e.otherPayloadData.IntermediateTaskCount,Torequestheaderdata:(e==null?void 0:e.requestHeaderData)||{},Tomaterialerrordata:(e==null?void 0:e.errorData[B])||{},TemplateName:u,Tochildrequestheaderdata:{ChildRequestId:((b=e==null?void 0:e.childRequestHeaderData)==null?void 0:b.ChildRequestId)||null,MaterialGroupType:((q=e==null?void 0:e.childRequestHeaderData)==null?void 0:q.MaterialGroupType)||null,TaskId:(l==null?void 0:l.taskId)||null,Comments:(e==null?void 0:e.Comments)||"",TotalIntermediateTasks:((P=e==null?void 0:e.childRequestHeaderData)==null?void 0:P.TotalIntermediateTasks)||null,IntermediateTaskCount:((G=e==null?void 0:e.childRequestHeaderData)==null?void 0:G.IntermediateTaskCount)||null,ReqCreatedBy:((F=e==null?void 0:e.childRequestHeaderData)==null?void 0:F.ReqCreatedBy)||null,ReqCreatedOn:((w=e==null?void 0:e.childRequestHeaderData)==null?void 0:w.ReqCreatedOn)||null,ReqUpdatedOn:((j=e==null?void 0:e.childRequestHeaderData)==null?void 0:j.ReqUpdatedOn)||null,RequestType:((Z=e==null?void 0:e.childRequestHeaderData)==null?void 0:Z.RequestType)||null,RequestPrefix:((le=e==null?void 0:e.childRequestHeaderData)==null?void 0:le.RequestPrefix)||null,RequestDesc:((te=e==null?void 0:e.childRequestHeaderData)==null?void 0:te.RequestDesc)||null,RequestPriority:((i=e==null?void 0:e.childRequestHeaderData)==null?void 0:i.RequestPriority)||null,RequestStatus:((y=e==null?void 0:e.childRequestHeaderData)==null?void 0:y.RequestStatus)||null,CurrentLevel:(l==null?void 0:l.ATTRIBUTE_3)||"",CurrentLevelName:(l==null?void 0:l.ATTRIBUTE_4)||"",ParticularLevel:(e==null?void 0:e.Level)||"-1",TaskName:(l==null?void 0:l.taskDesc)||"",ApproverGroup:(l==null?void 0:l.ATTRIBUTE_5)||"",TaskCreatedOn:l!=null&&l.createdOn?ie(l==null?void 0:l.createdOn):null,DueDate:l!=null&&l.compDeadline?ie(l==null?void 0:l.compDeadline):null,Version:((V=e==null?void 0:e.childRequestHeaderData)==null?void 0:V.Version)||us.DEFAULT_VERSION},changeLogData:x(B,(v=c[B])==null?void 0:v.ChangeLogId),...(e==null?void 0:e.Comments)&&{Comments:(e==null?void 0:e.Comments)||""}}})}else{const c={},T=["id","slNo","type","Plant","MatlType"],U=["id","slNo","type"],g=["id","slNo","type"];return Object.keys(d).forEach(B=>{d[B].forEach(b=>{const{Material:q}=b;c[q]||(c[q]={Toclientdata:null,Toplantdata:[],Tosalesdata:[],MatlType:""});const P={...b};B==="Basic Data"&&!c[q].Toclientdata?(c[q].MatlType=(P==null?void 0:P.MatlType)||"",T.forEach(G=>delete P[G]),c[q].Toclientdata={...P,Function:"UPD"}):B==="Plant Data"?(U.forEach(G=>delete P[G]),c[q].Toplantdata.push({...P,Function:"UPD"})):B==="Sales Data"&&(g.forEach(G=>delete P[G]),c[q].Tosalesdata.push({...P,Function:"UPD"}))})}),Object.keys(c).map(B=>{var b,q,P,G,F,w,j,Z,le,te,i,y,V,v,z,K,Ee;return{...c[B],Torequestheaderdata:{RequestId:(b=o==null?void 0:o.requestHeader)==null?void 0:b.requestId,ReqCreatedBy:(q=o==null?void 0:o.requestHeader)==null?void 0:q.reqCreatedBy,ReqCreatedOn:ie((P=o==null?void 0:o.requestHeader)==null?void 0:P.reqCreatedOn),ReqUpdatedOn:ie((G=o==null?void 0:o.requestHeader)==null?void 0:G.reqCreatedOn),RequestType:(F=o==null?void 0:o.requestHeader)==null?void 0:F.requestType,RequestPriority:(w=o==null?void 0:o.requestHeader)==null?void 0:w.requestPriority,RequestDesc:(j=o==null?void 0:o.requestHeader)==null?void 0:j.requestDesc,RequestStatus:(Z=o==null?void 0:o.requestHeader)==null?void 0:Z.requestStatus,FirstProd:((le=p==null?void 0:p.payloadData)==null?void 0:le.FirstProductionDate)||null,LaunchDate:((te=p==null?void 0:p.payloadData)==null?void 0:te.LaunchDate)||null,LeadingCat:(i=o==null?void 0:o.requestHeader)==null?void 0:i.leadingCat,Division:(y=o==null?void 0:o.requestHeader)==null?void 0:y.division,Region:(V=o==null?void 0:o.requestHeader)==null?void 0:V.region,TemplateName:(v=o==null?void 0:o.requestHeader)==null?void 0:v.templateName,FieldName:(z=o==null?void 0:o.requestHeader)==null?void 0:z.fieldName},Tochildrequestheaderdata:{},Material:B,TemplateName:(K=o==null?void 0:o.requestHeader)==null?void 0:K.templateName,IsFirstCreate:!0,Function:"UPD",MassEditId:(Ee=o==null?void 0:o.requestHeader)==null?void 0:Ee.requestId,changeLogData:x(B),...(e==null?void 0:e.Comments)&&{Comments:(e==null?void 0:e.Comments)||""}}})}},he=W=>{if(W){const c={},T=["id","slNo","type","MaterialId","Plant","ChangeLogId","MatlType","Version"],U=["id","slNo","type","MaterialId","ChangeLogId","Version"],g=["id","slNo","type","MaterialId","ChangeLogId","Version"],L=["id","slNo","type","MaterialId","ChangeLogId","Version"];return Object.keys(d).forEach(b=>{d[b].forEach(q=>{const{Material:P,MaterialId:G,ChangeLogId:F,Version:w}=q;c[P]||(c[P]={Toclientdata:null,Toplantdata:[],Tosalesdata:[],Tomaterialdescription:[],MaterialId:G,Version:w,ChangeLogId:F,MatlType:""});const j={...q};b==="Basic Data"&&!c[P].Toclientdata?(c[P].MatlType=(j==null?void 0:j.MatlType)||"",T.forEach(Z=>delete j[Z]),c[P].Toclientdata=j):b==="Plant Data"?(U.forEach(Z=>delete j[Z]),c[P].Toplantdata.push(j)):b==="Sales Data"?(g.forEach(Z=>delete j[Z]),c[P].Tosalesdata.push(j)):b==="Description"&&(L.forEach(Z=>delete j[Z]),c[P].Tomaterialdescription.push(j))})}),Object.keys(c).map(b=>{var q,P,G,F,w,j,Z,le,te,i,y,V,v,z;return{...c[b],Material:b,MassChildEditId:e==null?void 0:e.otherPayloadData.MassChildEditId,Function:"UPD",TaskId:(e==null?void 0:e.otherPayloadData.TaskId)||"",TaskName:(e==null?void 0:e.otherPayloadData.TaskName)||"",creationTime:(e==null?void 0:e.otherPayloadData.CreationTime)||"",dueDate:(e==null?void 0:e.otherPayloadData.DueDate)||"",IsFirstCreate:!1,MassEditId:e==null?void 0:e.otherPayloadData.MassEditId,TotalIntermediateTasks:e==null?void 0:e.otherPayloadData.TotalIntermediateTasks,IntermediateTaskCount:e==null?void 0:e.otherPayloadData.IntermediateTaskCount,Torequestheaderdata:(e==null?void 0:e.requestHeaderData)||{},Tomaterialerrordata:(e==null?void 0:e.errorData[b])||{},TemplateName:u,Tochildrequestheaderdata:{ChildRequestId:((q=e==null?void 0:e.childRequestHeaderData)==null?void 0:q.ChildRequestId)||null,MaterialGroupType:((P=e==null?void 0:e.childRequestHeaderData)==null?void 0:P.MaterialGroupType)||null,TaskId:(l==null?void 0:l.taskId)||null,Comments:(e==null?void 0:e.Comments)||"",TotalIntermediateTasks:((G=e==null?void 0:e.childRequestHeaderData)==null?void 0:G.TotalIntermediateTasks)||null,IntermediateTaskCount:((F=e==null?void 0:e.childRequestHeaderData)==null?void 0:F.IntermediateTaskCount)||null,ReqCreatedBy:((w=e==null?void 0:e.childRequestHeaderData)==null?void 0:w.ReqCreatedBy)||null,ReqCreatedOn:((j=e==null?void 0:e.childRequestHeaderData)==null?void 0:j.ReqCreatedOn)||null,ReqUpdatedOn:((Z=e==null?void 0:e.childRequestHeaderData)==null?void 0:Z.ReqUpdatedOn)||null,RequestType:((le=e==null?void 0:e.childRequestHeaderData)==null?void 0:le.RequestType)||null,RequestPrefix:((te=e==null?void 0:e.childRequestHeaderData)==null?void 0:te.RequestPrefix)||null,RequestDesc:((i=e==null?void 0:e.childRequestHeaderData)==null?void 0:i.RequestDesc)||null,RequestPriority:((y=e==null?void 0:e.childRequestHeaderData)==null?void 0:y.RequestPriority)||null,RequestStatus:((V=e==null?void 0:e.childRequestHeaderData)==null?void 0:V.RequestStatus)||null,CurrentLevel:(l==null?void 0:l.ATTRIBUTE_3)||"",CurrentLevelName:(l==null?void 0:l.ATTRIBUTE_4)||"",ParticularLevel:(e==null?void 0:e.Level)||"-1",TaskName:(l==null?void 0:l.taskDesc)||"",ApproverGroup:(l==null?void 0:l.ATTRIBUTE_5)||"",TaskCreatedOn:l!=null&&l.createdOn?ie(l==null?void 0:l.createdOn):null,DueDate:l!=null&&l.compDeadline?ie(l==null?void 0:l.compDeadline):null,Version:((v=e==null?void 0:e.childRequestHeaderData)==null?void 0:v.Version)||us.DEFAULT_VERSION},changeLogData:x(b,(z=c[b])==null?void 0:z.ChangeLogId),...(e==null?void 0:e.Comments)&&{Comments:(e==null?void 0:e.Comments)||""}}})}else{const c={},T=["id","slNo","type","Plant","MatlType"],U=["id","slNo","type"],g=["id","slNo","type"],L=["id","slNo","type"];return Object.keys(d).forEach(b=>{d[b].forEach(q=>{const{Material:P}=q;c[P]||(c[P]={Toclientdata:null,Toplantdata:[],Tosalesdata:[],Tomaterialdescription:[],MatlType:""});const G={...q};b==="Basic Data"&&!c[P].Toclientdata?(c[P].MatlType=(G==null?void 0:G.MatlType)||"",T.forEach(F=>delete G[F]),c[P].Toclientdata={...G,Function:"UPD"}):b==="Plant Data"?(U.forEach(F=>delete G[F]),c[P].Toplantdata.push({...G,Function:"UPD"})):b==="Sales Data"?(g.forEach(F=>delete G[F]),c[P].Tosalesdata.push({...G,Function:"UPD"})):b==="Description"&&(L.forEach(F=>delete G[F]),c[P].Tomaterialdescription.push({...G,Function:"UPD"}))})}),Object.keys(c).map(b=>{var q,P,G,F,w,j,Z,le,te,i,y,V,v,z,K,Ee,X;return{...c[b],Torequestheaderdata:{RequestId:(q=o==null?void 0:o.requestHeader)==null?void 0:q.requestId,ReqCreatedBy:(P=o==null?void 0:o.requestHeader)==null?void 0:P.reqCreatedBy,ReqCreatedOn:ie((G=o==null?void 0:o.requestHeader)==null?void 0:G.reqCreatedOn),ReqUpdatedOn:ie((F=o==null?void 0:o.requestHeader)==null?void 0:F.reqCreatedOn),RequestType:(w=o==null?void 0:o.requestHeader)==null?void 0:w.requestType,RequestPriority:(j=o==null?void 0:o.requestHeader)==null?void 0:j.requestPriority,RequestDesc:(Z=o==null?void 0:o.requestHeader)==null?void 0:Z.requestDesc,RequestStatus:(le=o==null?void 0:o.requestHeader)==null?void 0:le.requestStatus,FirstProd:((te=p==null?void 0:p.payloadData)==null?void 0:te.FirstProductionDate)||null,LaunchDate:((i=p==null?void 0:p.payloadData)==null?void 0:i.LaunchDate)||null,LeadingCat:(y=o==null?void 0:o.requestHeader)==null?void 0:y.leadingCat,Division:(V=o==null?void 0:o.requestHeader)==null?void 0:V.division,Region:(v=o==null?void 0:o.requestHeader)==null?void 0:v.region,TemplateName:(z=o==null?void 0:o.requestHeader)==null?void 0:z.templateName,FieldName:(K=o==null?void 0:o.requestHeader)==null?void 0:K.fieldName},Tochildrequestheaderdata:{},Material:b,TemplateName:(Ee=o==null?void 0:o.requestHeader)==null?void 0:Ee.templateName,IsFirstCreate:!0,Function:"UPD",MassEditId:(X=o==null?void 0:o.requestHeader)==null?void 0:X.requestId,changeLogData:x(b),...(e==null?void 0:e.Comments)&&{Comments:(e==null?void 0:e.Comments)||""}}})}},Q=W=>{const c=d.reduce((T,U)=>{if((O==null?void 0:O.length)!==0&&!(O!=null&&O.includes(U==null?void 0:U.id)))return T;const g=U==null?void 0:U.Material;return T[g]||(T[g]=[]),T[g].push(U),T},{});if(W){const T=["id","MaterialId","ClientId","slNo","ChangeLogId","MatlType","Version"];return Object.keys(c).map(g=>{var P,G,F,w,j,Z,le,te,i,y,V,v,z,K,Ee,X,ye,je;const L=c[g],{MaterialId:B,ClientId:b,Version:q}=L[(L==null?void 0:L.length)-1];return{MaterialId:B,Material:g,MassChildEditId:e==null?void 0:e.otherPayloadData.MassChildEditId,MatlType:((F=(G=c[g])==null?void 0:G[((P=c[g])==null?void 0:P.length)-1])==null?void 0:F.MatlType)||"",Function:"UPD",Version:q,TaskId:(e==null?void 0:e.otherPayloadData.TaskId)||"",TaskName:(e==null?void 0:e.otherPayloadData.TaskName)||"",creationTime:(e==null?void 0:e.otherPayloadData.CreationTime)||"",dueDate:(e==null?void 0:e.otherPayloadData.DueDate)||"",IsFirstCreate:!1,MassEditId:e==null?void 0:e.otherPayloadData.MassEditId,TotalIntermediateTasks:e==null?void 0:e.otherPayloadData.TotalIntermediateTasks,IntermediateTaskCount:e==null?void 0:e.otherPayloadData.IntermediateTaskCount,Toclientdata:{ClientId:b,Material:g,Function:"UPD"},Tochildrequestheaderdata:{ChildRequestId:((w=e==null?void 0:e.childRequestHeaderData)==null?void 0:w.ChildRequestId)||null,MaterialGroupType:((j=e==null?void 0:e.childRequestHeaderData)==null?void 0:j.MaterialGroupType)||null,TaskId:(l==null?void 0:l.taskId)||null,Comments:(e==null?void 0:e.Comments)||"",TotalIntermediateTasks:((Z=e==null?void 0:e.childRequestHeaderData)==null?void 0:Z.TotalIntermediateTasks)||null,IntermediateTaskCount:((le=e==null?void 0:e.childRequestHeaderData)==null?void 0:le.IntermediateTaskCount)||null,ReqCreatedBy:((te=e==null?void 0:e.childRequestHeaderData)==null?void 0:te.ReqCreatedBy)||null,ReqCreatedOn:((i=e==null?void 0:e.childRequestHeaderData)==null?void 0:i.ReqCreatedOn)||null,ReqUpdatedOn:((y=e==null?void 0:e.childRequestHeaderData)==null?void 0:y.ReqUpdatedOn)||null,RequestType:((V=e==null?void 0:e.childRequestHeaderData)==null?void 0:V.RequestType)||null,RequestPrefix:((v=e==null?void 0:e.childRequestHeaderData)==null?void 0:v.RequestPrefix)||null,RequestDesc:((z=e==null?void 0:e.childRequestHeaderData)==null?void 0:z.RequestDesc)||null,RequestPriority:((K=e==null?void 0:e.childRequestHeaderData)==null?void 0:K.RequestPriority)||null,RequestStatus:((Ee=e==null?void 0:e.childRequestHeaderData)==null?void 0:Ee.RequestStatus)||null,CurrentLevel:(l==null?void 0:l.ATTRIBUTE_3)||"",CurrentLevelName:(l==null?void 0:l.ATTRIBUTE_4)||"",ParticularLevel:(e==null?void 0:e.Level)||"-1",TaskName:(l==null?void 0:l.taskDesc)||"",ApproverGroup:(l==null?void 0:l.ATTRIBUTE_5)||"",TaskCreatedOn:l!=null&&l.createdOn?ie(l==null?void 0:l.createdOn):null,DueDate:l!=null&&l.compDeadline?ie(l==null?void 0:l.compDeadline):null,Version:((X=e==null?void 0:e.childRequestHeaderData)==null?void 0:X.Version)||us.DEFAULT_VERSION},Tomaterialdescription:L.map($=>{const De={...$,Function:"UPD"};return T.forEach(de=>delete De[de]),De}),Torequestheaderdata:(e==null?void 0:e.requestHeaderData)||{},Tomaterialerrordata:(e==null?void 0:e.errorData[g])||{},TemplateName:u,...(e==null?void 0:e.Comments)&&{Comments:(e==null?void 0:e.Comments)||""},changeLogData:x(g,(je=(ye=c[g])==null?void 0:ye[0])==null?void 0:je.ChangeLogId)}})}else{const T=["id","slNo","MatlType"];return Object.keys(c).map(g=>{var L,B,b,q,P,G,F,w,j,Z,le,te,i,y,V,v,z,K;return{Tomaterialdescription:c[g].map(Ee=>{const X={...Ee,Function:"UPD"};return T.forEach(ye=>delete X[ye]),X}),Torequestheaderdata:{RequestId:(L=o==null?void 0:o.requestHeader)==null?void 0:L.requestId,ReqCreatedBy:(B=o==null?void 0:o.requestHeader)==null?void 0:B.reqCreatedBy,ReqCreatedOn:ie((b=o==null?void 0:o.requestHeader)==null?void 0:b.reqCreatedOn),ReqUpdatedOn:ie((q=o==null?void 0:o.requestHeader)==null?void 0:q.reqCreatedOn),RequestType:(P=o==null?void 0:o.requestHeader)==null?void 0:P.requestType,RequestPriority:(G=o==null?void 0:o.requestHeader)==null?void 0:G.requestPriority,RequestDesc:(F=o==null?void 0:o.requestHeader)==null?void 0:F.requestDesc,RequestStatus:(w=o==null?void 0:o.requestHeader)==null?void 0:w.requestStatus,FirstProd:((j=p==null?void 0:p.payloadData)==null?void 0:j.FirstProductionDate)||null,LaunchDate:((Z=p==null?void 0:p.payloadData)==null?void 0:Z.LaunchDate)||null,LeadingCat:(le=o==null?void 0:o.requestHeader)==null?void 0:le.leadingCat,Division:(te=o==null?void 0:o.requestHeader)==null?void 0:te.division,Region:(i=o==null?void 0:o.requestHeader)==null?void 0:i.region,TemplateName:(y=o==null?void 0:o.requestHeader)==null?void 0:y.templateName,FieldName:(V=o==null?void 0:o.requestHeader)==null?void 0:V.fieldName},Tochildrequestheaderdata:{},Toclientdata:{ClientId:null,Function:"UPD"},Material:g,MatlType:((K=(z=c[g])==null?void 0:z[((v=c[g])==null?void 0:v.length)-1])==null?void 0:K.MatlType)||"",TemplateName:u,IsFirstCreate:!0,Function:"UPD",...(e==null?void 0:e.Comments)&&{Comments:(e==null?void 0:e.Comments)||""},changeLogData:x(g)}})}},n=W=>{const c=d.reduce((T,U)=>{if((O==null?void 0:O.length)!==0&&!(O!=null&&O.includes(U==null?void 0:U.id)))return T;const g=U==null?void 0:U.Material;return T[g]||(T[g]=[]),T[g].push(U),T},{});if(W){const T=["id","MaterialId","ClientId","slNo","ChangeLogId","MatlType","Version"];return Object.keys(c).map(g=>{var q,P,G,F,w,j,Z,le,te,i,y,V,v,z,K,Ee,X,ye;const L=c[g],{MaterialId:B,Version:b}=L[0];return{MaterialId:B,Material:g,MassChildEditId:e==null?void 0:e.otherPayloadData.MassChildEditId,MatlType:((G=(P=c[g])==null?void 0:P[((q=c[g])==null?void 0:q.length)-1])==null?void 0:G.MatlType)||"",Function:"UPD",Version:b,TaskId:(e==null?void 0:e.otherPayloadData.TaskId)||"",TaskName:(e==null?void 0:e.otherPayloadData.TaskName)||"",creationTime:(e==null?void 0:e.otherPayloadData.CreationTime)||"",dueDate:(e==null?void 0:e.otherPayloadData.DueDate)||"",IsFirstCreate:!1,MassEditId:e==null?void 0:e.otherPayloadData.MassEditId,TotalIntermediateTasks:e==null?void 0:e.otherPayloadData.TotalIntermediateTasks,IntermediateTaskCount:e==null?void 0:e.otherPayloadData.IntermediateTaskCount,Towarehousedata:L.map(je=>{const $={...je,Function:"UPD"};return T.forEach(De=>delete $[De]),$}),Tochildrequestheaderdata:{...e==null?void 0:e.childRequestHeaderData,ChildRequestId:((F=e==null?void 0:e.childRequestHeaderData)==null?void 0:F.ChildRequestId)||null,MaterialGroupType:((w=e==null?void 0:e.childRequestHeaderData)==null?void 0:w.MaterialGroupType)||null,TaskId:(l==null?void 0:l.taskId)||null,Comments:(e==null?void 0:e.Comments)||"",TotalIntermediateTasks:((j=e==null?void 0:e.childRequestHeaderData)==null?void 0:j.TotalIntermediateTasks)||null,IntermediateTaskCount:((Z=e==null?void 0:e.childRequestHeaderData)==null?void 0:Z.IntermediateTaskCount)||null,ReqCreatedBy:((le=e==null?void 0:e.childRequestHeaderData)==null?void 0:le.ReqCreatedBy)||null,ReqCreatedOn:((te=e==null?void 0:e.childRequestHeaderData)==null?void 0:te.ReqCreatedOn)||null,ReqUpdatedOn:((i=e==null?void 0:e.childRequestHeaderData)==null?void 0:i.ReqUpdatedOn)||null,RequestType:((y=e==null?void 0:e.childRequestHeaderData)==null?void 0:y.RequestType)||null,RequestPrefix:((V=e==null?void 0:e.childRequestHeaderData)==null?void 0:V.RequestPrefix)||null,RequestDesc:((v=e==null?void 0:e.childRequestHeaderData)==null?void 0:v.RequestDesc)||null,RequestPriority:((z=e==null?void 0:e.childRequestHeaderData)==null?void 0:z.RequestPriority)||null,RequestStatus:((K=e==null?void 0:e.childRequestHeaderData)==null?void 0:K.RequestStatus)||null,CurrentLevel:(l==null?void 0:l.ATTRIBUTE_3)||"",CurrentLevelName:(l==null?void 0:l.ATTRIBUTE_4)||"",ParticularLevel:(e==null?void 0:e.Level)||"-1",TaskName:(l==null?void 0:l.taskDesc)||"",ApproverGroup:(l==null?void 0:l.ATTRIBUTE_5)||"",TaskCreatedOn:l!=null&&l.createdOn?ie(l==null?void 0:l.createdOn):null,DueDate:l!=null&&l.compDeadline?ie(l==null?void 0:l.compDeadline):null,Version:((Ee=e==null?void 0:e.childRequestHeaderData)==null?void 0:Ee.Version)||us.DEFAULT_VERSION},Torequestheaderdata:(e==null?void 0:e.requestHeaderData)||{},Tomaterialerrordata:(e==null?void 0:e.errorData[g])||{},TemplateName:u,changeLogData:x(g,(ye=(X=c[g])==null?void 0:X[0])==null?void 0:ye.ChangeLogId),...(e==null?void 0:e.Comments)&&{Comments:(e==null?void 0:e.Comments)||""}}})}else{const T=["id","slNo","MatlType"];return Object.keys(c).map(g=>{var L,B,b,q,P,G,F,w,j,Z,le,te,i,y,V,v,z,K;return{Towarehousedata:c[g].map(Ee=>{const X={...Ee,Function:"UPD"};return T.forEach(ye=>delete X[ye]),X}),Torequestheaderdata:{RequestId:(L=o==null?void 0:o.requestHeader)==null?void 0:L.requestId,ReqCreatedBy:(B=o==null?void 0:o.requestHeader)==null?void 0:B.reqCreatedBy,ReqCreatedOn:ie((b=o==null?void 0:o.requestHeader)==null?void 0:b.reqCreatedOn),ReqUpdatedOn:ie((q=o==null?void 0:o.requestHeader)==null?void 0:q.reqCreatedOn),RequestType:(P=o==null?void 0:o.requestHeader)==null?void 0:P.requestType,RequestPriority:(G=o==null?void 0:o.requestHeader)==null?void 0:G.requestPriority,RequestDesc:(F=o==null?void 0:o.requestHeader)==null?void 0:F.requestDesc,RequestStatus:(w=o==null?void 0:o.requestHeader)==null?void 0:w.requestStatus,FirstProd:((j=p==null?void 0:p.payloadData)==null?void 0:j.FirstProductionDate)||null,LaunchDate:((Z=p==null?void 0:p.payloadData)==null?void 0:Z.LaunchDate)||null,LeadingCat:(le=o==null?void 0:o.requestHeader)==null?void 0:le.leadingCat,Division:(te=o==null?void 0:o.requestHeader)==null?void 0:te.division,Region:(i=o==null?void 0:o.requestHeader)==null?void 0:i.region,TemplateName:(y=o==null?void 0:o.requestHeader)==null?void 0:y.templateName,FieldName:(V=o==null?void 0:o.requestHeader)==null?void 0:V.fieldName},Tochildrequestheaderdata:{},Material:g,MatlType:((K=(z=c[g])==null?void 0:z[((v=c[g])==null?void 0:v.length)-1])==null?void 0:K.MatlType)||"",TemplateName:u,IsFirstCreate:!0,Function:"UPD",...(e==null?void 0:e.Comments)&&{Comments:(e==null?void 0:e.Comments)||""},changeLogData:x(g)}})}};return{changePayloadForTemplate:ee}},ki=({initialReqScreen:r,isReqBench:d,remarks:R="",userInput:o="",selectedLevel:p="-1"})=>{var Q;const e=Y(n=>n.payload),O=(Q=e==null?void 0:e.payloadData)==null?void 0:Q.RequestType,l=Y(n=>n.userManagement.taskData),u=Y(n=>n.request),x=To(to.CURRENT_TASK),ee=typeof x=="string"?JSON.parse(x):x,m=Y(n=>n.changeLog.createChangeLogData),Ce=Vs(),Oe=new URLSearchParams(Ce.search.split("?")[1]).get("RequestId");function he(n){const W=[];return Object.keys(n).forEach(c=>{var T,U,g,L,B,b,q,P,G,F,w,j,Z,le,te,i,y,V,v,z,K,Ee,X,ye,je,$,De,de,We,ha,Ta,aa,se,fa,ll,hl,Rl,El,Aa,wa,Ia,ve,Da,ia,sl,ne,E,qe,ra,Ca,Ye,xa,Ze,oa,ol,$a,Ma,rl,za,tl,Tl,ke,_a,Ya,ta,as,nl,ls,dl,fl,Al,ss,xe,Il,Dl,Ha,Cl,kl,Ja,os,_l,Xa,rs,$l,ga,Nl,Ml,zl,Yl,ts,ns,Jl,Xl,Vl,il,bl,Kl,Ol,S,Ge,Je,pa,Ra,qa,Sa,Wa,Pa,Na,cl,ul,Va,Ka,yl,vl,ql,Sl,Pl,gs,ps,Rs,Es,As,Is,Ds,_s,Ns,Ms,bs,Os,ys,vs,qs,Ss,Ps,I,ge,Ae,Me,Te,oe,pe,H,Fe,la,_e,Ue,Ua,Qa,Za,Ll,Bl,Ul,Gl,xl,Hl,ml,Fl,Ls,Bs,na,Ql,fo,Co,go,po,Ro,Eo,Ao,Io,Do,_o,No,Mo,bo,Oo,yo,vo,qo,So,Po,Lo,Bo,Uo,Go,xo,Ho,mo,Fo,wo,Wo,jo,ko,$o,zo,Yo,Jo,Xo,Vo,Ko,Qo,Zo,er,ar,lr,sr,or,rr,tr,nr,dr,ir,cr,ur,hr,Tr,fr,Cr,gr,pr,Rr,Er,Ar,Ir,Dr,_r,Nr,Mr,br,Or,yr,vr,qr,Sr,Pr,Lr,Br,Ur,Gr,xr,Hr,mr,Fr,wr,Wr,jr,kr,$r,zr,Yr,Jr,Xr,Vr,Kr,Qr,Zr,et,at,lt,st,ot,rt,tt,nt,dt,it,ct,ut,ht,Tt,ft,Ct,gt,pt,Rt,Et,At,It,Dt,_t,Nt,Mt,bt,Ot,yt,vt,qt,St,Pt,Lt,Bt,Ut,Gt,xt,Ht,mt,Ft,wt,Wt,jt,kt,$t,zt,Yt,Jt,Xt,Vt,Kt,Qt,Zt,en,an,ln,sn,on,rn,tn,nn,dn,cn,un,hn,Tn,fn,Cn,gn,pn,Rn,En,An,In,Dn,_n,Nn,Mn,bn,On,yn,vn,qn,Sn,Pn,Ln,Bn,Un,Gn,xn,Hn,mn,Fn,wn,Wn,jn,kn,$n,zn,Yn,Jn,Xn,Vn,Kn,Qn,Zn;if(c.includes("-")||/\d/.test(c)){const s=n[c];if((T=s==null?void 0:s.headerData)!=null&&T.included){const Qs=Yh(((U=s==null?void 0:s.headerData)==null?void 0:U.orgData)||[]),$d=(((L=(g=s==null?void 0:s.headerData)==null?void 0:g.views)==null?void 0:L.filter(M=>!XT.includes(M)))||[]).join(",").trim(),ua=(B=s==null?void 0:s.payloadData)!=null&&B.Sales?Object.values(s.payloadData.Sales):[],ed=new Set,zd=Qs.filter(M=>{var Re,Le,fe;if(!((Re=M.salesOrg)!=null&&Re.code)||!((fe=(Le=M.dc)==null?void 0:Le.value)!=null&&fe.code))return!1;const ae=`${M.salesOrg.code}-${M.dc.value.code}`;return ed.has(ae)?!1:(ed.add(ae),!0)}).map((M,ae)=>{var Re,Le,fe,Xe,A,Ve,ce,La,we,be,Us,Gs,xs,Hs,ms,Fs,ws,Ws,js,ks,$s,zs,Ys;return{SalesId:O===h.EXTEND&&r?null:((Re=ua[ae])==null?void 0:Re.SalesId)||"",Function:"INS",Material:((Le=s.headerData)==null?void 0:Le.materialNumber)||"",SalesOrg:((fe=M.salesOrg)==null?void 0:fe.code)||"",DistrChan:((A=(Xe=M.dc)==null?void 0:Xe.value)==null?void 0:A.code)||"",DelFlag:!1,MatlStats:((Ve=ua[ae])==null?void 0:Ve.MatlStats)||"",RebateGrp:((ce=ua[ae])==null?void 0:ce.RebateGrp)||"",CashDisc:((La=ua[ae])==null?void 0:La.CashDisc)||!0,SalStatus:((we=ua[ae])==null?void 0:we.SalStatus)||"",DelyUnit:"0.000",ValidFrom:(be=ua[ae])!=null&&be.ValidFrom?ie((Us=ua[ae])==null?void 0:Us.ValidFrom):null,DelyUom:((Gs=ua[ae])==null?void 0:Gs.DelyUom)||"",DelygPlnt:((xs=ua[ae])==null?void 0:xs.DelygPlnt)||"",MatPrGrp:((Hs=ua[ae])==null?void 0:Hs.MatPrGrp)||"",AcctAssgt:((ms=ua[ae])==null?void 0:ms.AcctAssgt)||"",MatlGrp4:((Fs=ua[ae])==null?void 0:Fs.MatlGrp4)||"",MatlGrp2:((ws=ua[ae])==null?void 0:ws.MatlGrp2)||"",MatlGrp5:((Ws=ua[ae])==null?void 0:Ws.MatlGrp5)||"",BatchMgmt:((js=ua[ae])==null?void 0:js.BatchMgmt)||"",Countryori:((ks=ua[ae])==null?void 0:ks.Countryori)||"",Depcountry:(($s=ua[ae])==null?void 0:$s.Depcountry)||"",SalesUnit:((zs=ua[ae])==null?void 0:zs.SalesUnit)||"",ItemCat:((Ys=ua[ae])==null?void 0:Ys.ItemCat)||""}}),Yd=(b=s==null?void 0:s.payloadData)!=null&&b.Purchasing?Object.entries(s.payloadData.Purchasing):[],Jd=(q=s==null?void 0:s.payloadData)!=null&&q.MRP?Object.entries(s.payloadData.MRP):[],Xd=(P=s==null?void 0:s.payloadData)!=null&&P[f.SALES_PLANT]?Object.entries((G=s.payloadData)==null?void 0:G[f.SALES_PLANT]):[],Vd=(F=s==null?void 0:s.payloadData)!=null&&F[f.STORAGE_PLANT]?Object.entries((w=s.payloadData)==null?void 0:w[f.STORAGE_PLANT]):[],Kd=(j=s==null?void 0:s.payloadData)!=null&&j.Accounting?Object.entries(s.payloadData.Accounting):[];let ad=[];if(((i=(te=(le=(Z=s==null?void 0:s.payloadData)==null?void 0:Z.TaxData)==null?void 0:le.TaxData)==null?void 0:te.TaxDataSet)==null?void 0:i.length)>0){const M={};(z=(v=(V=(y=s==null?void 0:s.payloadData)==null?void 0:y.TaxData)==null?void 0:V.TaxData)==null?void 0:v.TaxDataSet)==null||z.forEach(ae=>{var fe,Xe;const Re=ae.Country;M[Re]||(M[Re]={ControlId:ae.ControlId??null,Function:"INS",Material:((fe=s.headerData)==null?void 0:fe.materialNumber)||"",Depcountry:ae.Country});const Le=Object.keys(M[Re]).filter(A=>A.startsWith("TaxType")).length+1;Le<=9&&(M[Re][`TaxType${Le}`]=ae.TaxType,M[Re][`Taxclass${Le}`]=((Xe=ae.SelectedTaxClass)==null?void 0:Xe.TaxClass)||"")}),ad=Object.values(M)}const ld=(K=s==null?void 0:s.payloadData)!=null&&K.Costing?Object.entries(s.payloadData.Costing):[],Qd=((Ee=s==null?void 0:s.headerData)==null?void 0:Ee.orgData)||[],sd=new Set,Zd=Qd.filter(M=>{var Re,Le;if(!((Le=(Re=M.plant)==null?void 0:Re.value)!=null&&Le.code))return!1;const ae=M.plant.value.code;return sd.has(ae)?!1:(sd.add(ae),!0)}).map(M=>{var fe,Xe,A,Ve,ce;const ae=(Xe=(fe=M.plant)==null?void 0:fe.value)==null?void 0:Xe.code,Re=((A=Kd.find(([La])=>La===ae))==null?void 0:A[1])||{},Le=((Ve=ld.find(([La])=>La===ae))==null?void 0:Ve[1])||{};return{...Re,AccountingId:Re.AccountingId||Le.AccountingId||null,Function:"INS",Material:((ce=s.headerData)==null?void 0:ce.materialNumber)||"",DelFlag:"",PriceCtrl:Re.PriceCtrl||"",MovingPr:Re.MovingPr||Le.MovingPr||"",StdPrice:Re.StdPrice||Le.StdPrice||"",PriceUnit:Re.PriceUnit||"",ValClass:Re.ValClass||"",OrigMat:Le.OrigMat===!0||Le.OrigMat==="X"||Le.OrigMat==="TRUE"?"X":"",ValArea:ae||""}}),ei=(X=s==null?void 0:s.payloadData)!=null&&X.Warehouse?Object.entries(s.payloadData.Warehouse):[],ai=($=(ye=s.headerData)==null?void 0:ye.views)!=null&&$.includes((je=f)==null?void 0:je.WAREHOUSE)?ei.map(([M,ae],Re)=>{var Le;return{WarehouseId:ae.WarehouseId||"",Function:ae.Function||"",Material:((Le=s.headerData)==null?void 0:Le.materialNumber)||"",DelFlag:ae.DelFlag||!0,WhseNo:M||"",SpecMvmt:ae.SpecMvmt||"",LEquip1:ae.LEquip1||"",LeqUnit1:ae.LeqUnit1||"",Unittype1:ae.Unittype1||"",Placement:ae.Placement||""}}):[],no=(de=s==null?void 0:s.payloadData)!=null&&de[(De=f)==null?void 0:De.STORAGE]?Object.values((ha=s.payloadData)==null?void 0:ha[(We=f)==null?void 0:We.STORAGE]):[],od=new Set,li=Qs.filter(M=>{var Re,Le,fe,Xe,A,Ve,ce,La;if(!((Le=(Re=M.plant)==null?void 0:Re.value)!=null&&Le.code)||!((Xe=(fe=M.sloc)==null?void 0:fe.value)!=null&&Xe.code))return!1;const ae=`${(Ve=(A=M.plant)==null?void 0:A.value)==null?void 0:Ve.code}-${(La=(ce=M.sloc)==null?void 0:ce.value)==null?void 0:La.code}`;return od.has(ae)?!1:(od.add(ae),!0)}).map((M,ae)=>{var Re,Le,fe,Xe,A,Ve,ce,La;return{StorageLocationId:((Re=no[ae])==null?void 0:Re.StorageLocationId)||"",Plant:((fe=(Le=M.plant)==null?void 0:Le.value)==null?void 0:fe.code)||"",StgeLoc:((A=(Xe=M.sloc)==null?void 0:Xe.value)==null?void 0:A.code)||"",Material:((Ve=s.headerData)==null?void 0:Ve.materialNumber)||"",PickgArea:((ce=no[ae])==null?void 0:ce.PickgArea)||"",StgeBin:((La=no[ae])==null?void 0:La.StgeBin)||""}}),el=(aa=(Ta=s.headerData)==null?void 0:Ta.views)!=null&&aa.includes(f.CLASSIFICATION)?(se=s==null?void 0:s.payloadData)==null?void 0:se[f.CLASSIFICATION]:{};let rd=[];el&&el.basic&&el.basic.Classtype&&el.basic.Classnum&&Array.isArray(el.classification)&&rd.push({ClassificationId:((fa=el==null?void 0:el.basic)==null?void 0:fa.ClassificationId)||"",Classnum:el.basic.Classnum,Classtype:el.basic.Classtype,Object:((ll=s.headerData)==null?void 0:ll.materialNumber)||"",Tochars:(hl=el.classification)==null?void 0:hl.map(M=>({CharacteristicsId:(M==null?void 0:M.CharacteristicsId)||"",Charact:M.characteristic,CharactDescr:M.description,Tocharvalues:[{ValueChar:M.value||"",CharValueId:(M==null?void 0:M.charValuesId)||""}]}))});const si=(Rl=s==null?void 0:s.payloadData)!=null&&Rl[f.WORKSCHEDULING]?Object.entries((El=s.payloadData)==null?void 0:El[f.WORKSCHEDULING]):[],oi=(Aa=s==null?void 0:s.payloadData)!=null&&Aa[f.BOM]?Object.entries((wa=s.payloadData)==null?void 0:wa[f.BOM]):[],ri=(Ia=s==null?void 0:s.payloadData)!=null&&Ia[f.SOURCE_LIST]?Object.entries((ve=s.payloadData)==null?void 0:ve[f.SOURCE_LIST]):[],ti=(((Da=s==null?void 0:s.headerData)==null?void 0:Da.orgData)||[]).filter((M,ae,Re)=>ae===(Re==null?void 0:Re.findIndex(Le=>{var fe,Xe,A,Ve;return((Xe=(fe=Le.plant)==null?void 0:fe.value)==null?void 0:Xe.code)===((Ve=(A=M==null?void 0:M.plant)==null?void 0:A.value)==null?void 0:Ve.code)}))).map((M,ae)=>{var Us,Gs,xs,Hs,ms,Fs,ws,Ws,js,ks,$s,zs,Ys,nd,dd,id,cd,ud,hd,Td,fd,Cd,gd,pd,Rd,Ed,Ad,Id,Dd,_d,Nd;const Re=(Gs=(Us=M.plant)==null?void 0:Us.value)==null?void 0:Gs.code,Le=(xs=M.mrpProfile)==null?void 0:xs.code,fe=((Hs=Yd.find(([Ba])=>Ba===Re))==null?void 0:Hs[1])||{},Xe=((ms=ld.find(([Ba])=>Ba===Re))==null?void 0:ms[1])||{},A=((Fs=Jd.find(([Ba])=>Ba.startsWith(Re)))==null?void 0:Fs[1])||{},Ve=((ws=Xd.find(([Ba])=>Ba===Re))==null?void 0:ws[1])||{},ce=((Ws=Vd.find(([Ba])=>Ba===Re))==null?void 0:Ws[1])||{},La=((js=si.find(([Ba])=>Ba===Re))==null?void 0:js[1])||{},we=((ks=oi.find(([Ba])=>Ba===Re))==null?void 0:ks[1])||{},be=(($s=ri.find(([Ba])=>Ba===Re))==null?void 0:$s[1])||{};return{PlantId:O===h.EXTEND&&r?null:((nd=(Ys=(zs=s.payloadData)==null?void 0:zs.Purchasing)==null?void 0:Ys[Re])==null?void 0:nd.PlantId)??null,Function:"INS",Material:((dd=s.headerData)==null?void 0:dd.materialNumber)||"",Plant:Re||"",DelFlag:!1,CritPart:!1,PurGroup:(fe==null?void 0:fe.PurGroup)||"",PurStatus:(fe==null?void 0:fe.PurStatus)||"",RoundProf:(A==null?void 0:A.RoundProf)||"",IssueUnitIso:"",Mrpprofile:Le||"",MrpType:(A==null?void 0:A.MrpType)||"",MrpCtrler:(A==null?void 0:A.MrpCtrler)||"",PlndDelry:(A==null?void 0:A.PlndDelry)||"",GrPrTime:(A==null?void 0:A.GrPrTime)||"",PeriodInd:(A==null?void 0:A.PeriodInd)||"",Lotsizekey:(A==null?void 0:A.Lotsizekey)||"",ProcType:(A==null?void 0:A.ProcType)||"",Consummode:(A==null?void 0:A.Consummode)||"",FwdCons:(A==null?void 0:A.FwdCons)||"",ReorderPt:(A==null?void 0:A.ReorderPt)||"",MaxStock:(A==null?void 0:A.MaxStock)||"",SafetyStk:(A==null?void 0:A.SafetyStk)||"",Minlotsize:(A==null?void 0:A.Minlotsize)||"",PlanStrgp:(A==null?void 0:A.PlanStrgp)||"",BwdCons:(A==null?void 0:A.BwdCons)||"",Maxlotsize:(A==null?void 0:A.Maxlotsize)||"",FixedLot:(A==null?void 0:A.FixedLot)||"",RoundVal:(A==null?void 0:A.RoundVal)||"",GrpReqmts:(A==null?void 0:A.GrpReqmts)||"",MixedMrp:(A==null?void 0:A.MixedMrp)||"",SmKey:(A==null?void 0:A.SmKey)||"",Backflush:(A==null?void 0:A.Backflush)||"",AssyScarp:(A==null?void 0:A.AssyScarp)||"",Replentime:(A==null?void 0:A.Replentime)||"",PlTiFnce:(A==null?void 0:A.PlTiFnce)||"",ReplacePt:"",IndPostToInspStock:(fe==null?void 0:fe.IndPostToInspStock)===!0||(fe==null?void 0:fe.IndPostToInspStock)==="X"||(fe==null?void 0:fe.IndPostToInspStock)==="TRUE"?"X":"",HtsCode:(fe==null?void 0:fe.HtsCode)||"",CtrlKey:"",DepReqId:(A==null?void 0:A.DepReqId)||"",SaftyTId:(A==null?void 0:A.SaftyTId)||"",Safetytime:(A==null?void 0:A.Safetytime)||"",Matfrgtgrp:(Ve==null?void 0:Ve.Matfrgtgrp)||"",Availcheck:(Ve==null?void 0:Ve.Availcheck)||"",ProfitCtr:(Ve==null?void 0:Ve.ProfitCtr)||"",Loadinggrp:(Ve==null?void 0:Ve.Loadinggrp)||"",MinLotSize:(A==null?void 0:A.MinLotSize)||"",MaxLotSize:(A==null?void 0:A.MaxLotSize)||"",FixLotSize:(A==null?void 0:A.FixLotSize)||"",AssyScrap:(A==null?void 0:A.AssyScrap)||"",IssStLoc:(A==null?void 0:A.IssStLoc)||"",SalesView:((cd=s==null?void 0:s.headerData)==null?void 0:cd.views.includes((id=f)==null?void 0:id.SALES))||!1,PurchView:((hd=s==null?void 0:s.headerData)==null?void 0:hd.views.includes((ud=f)==null?void 0:ud.PURCHASING))||!1,MrpView:((fd=s==null?void 0:s.headerData)==null?void 0:fd.views.includes((Td=f)==null?void 0:Td.MRP))||!1,WorkSchedView:((gd=s==null?void 0:s.headerData)==null?void 0:gd.views.includes((Cd=f)==null?void 0:Cd.WORK_SCHEDULING_2))||!1,WarehouseView:((Rd=s==null?void 0:s.headerData)==null?void 0:Rd.views.includes((pd=f)==null?void 0:pd.WAREHOUSE))||!1,AccountView:((Ad=s==null?void 0:s.headerData)==null?void 0:Ad.views.includes((Ed=f)==null?void 0:Ed.ACCOUNTING))||!1,CostView:((Dd=s==null?void 0:s.headerData)==null?void 0:Dd.views.includes((Id=f)==null?void 0:Id.COSTING))||!1,ForecastView:!1,PrtView:!1,StorageView:((Nd=s==null?void 0:s.headerData)==null?void 0:Nd.views.includes((_d=f)==null?void 0:_d.STORAGE))||!1,QualityView:!1,GrProcTime:"",GiProcTime:"",StorageCost:"",LotSizeUom:"",LotSizeUomIso:"",Unlimited:La.Unlimited||"",ProdProf:La.ProdProf||"",VarianceKey:Xe.VarianceKey||"",PoUnit:"",Spproctype:A.Spproctype||"",CommCode:(fe==null?void 0:fe.CommCode)||"",CommCoUn:(fe==null?void 0:fe.CommCoUn)||"",Countryori:fe==null?void 0:fe.Countryori,LotSize:Xe.LotSize||"",SlocExprc:A.SlocExprc||"",Inhseprodt:Xe.Inhseprodt||"",BomUsage:(we==null?void 0:we.BomUsage)||"",AltBom:(we==null?void 0:we.AltBom)||"",Category:(we==null?void 0:we.Category)||"",Component:(we==null?void 0:we.Component)||"",Quantity:(we==null?void 0:we.Quantity)||"",CompUom:(we==null?void 0:we.CompUom)||"",Bvalidfrom:we!=null&&we.Bvalidfrom?ie(we==null?void 0:we.Bvalidfrom):ie(new Date().toISOString()),Bvalidto:we!=null&&we.Bvalidto?ie(we==null?void 0:we.Bvalidto):ie(new Date().toISOString()),Supplier:(be==null?void 0:be.Supplier)||"",PurchaseOrg:(be==null?void 0:be.PurchaseOrg)||"",ProcurementPlant:(be==null?void 0:be.ProcurementPlant)||"",SOrderUnit:(be==null?void 0:be.SOrderUnit)||"",Agreement:(be==null?void 0:be.Agreement)||"",AgreementItem:(be==null?void 0:be.AgreementItem)||"",FixedSupplySource:(be==null?void 0:be.FixedSupplySource)||"",Blocked:(be==null?void 0:be.Blocked)||"",SMrp:(be==null?void 0:be.SMrp)||"",Slvalidfrom:be!=null&&be.Slvalidfrom?ie(be==null?void 0:be.Slvalidfrom):ie(new Date().toISOString()),Slvalidto:be!=null&&be.Slvalidto?ie(be==null?void 0:be.Slvalidto):ie(new Date().toISOString()),CcPhInv:(ce==null?void 0:ce.CcPhInv)||"",CcFixed:(ce==null?void 0:ce.CcFixed)||"",StgePdUn:(ce==null?void 0:ce.StgePdUn)||"",DefaultStockSegment:(ce==null?void 0:ce.DefaultStockSegment)||"",NegStocks:(ce==null?void 0:ce.NegStocks)||"",SernoProf:(ce==null?void 0:ce.SernoProf)||"",DistrProf:(ce==null?void 0:ce.DistrProf)||"",DetermGrp:(ce==null?void 0:ce.DetermGrp)||"",IuidRelevant:(ce==null?void 0:ce.IuidRelevant)||"",UidIea:(ce==null?void 0:ce.UidIea)||"",IuidType:(ce==null?void 0:ce.IuidType)||"",SortStockBasedOnSegment:(ce==null?void 0:ce.SortStockBasedOnSegment)||"",SegmentationStrategy:(ce==null?void 0:ce.SegmentationStrategy)||"",IssueUnit:(ce==null?void 0:ce.IssueUnit)||"",BatchMgmt:(ce==null?void 0:ce.BatchMgmt)||!1}}),ds=(s==null?void 0:s.additionalData)||[],is=(s==null?void 0:s.unitsOfMeasureData)||[],cs=(s==null?void 0:s.eanData)||[],ni=ds!=null&&ds.length?ds==null?void 0:ds.map(M=>{var ae;return{MaterialDescriptionId:O===h.EXTEND&&r?null:M.MaterialDescriptionId||null,Function:"INS",Material:((ae=s.headerData)==null?void 0:ae.materialNumber)||"",Langu:M.language||"EN",LanguIso:"",MatlDesc:(M==null?void 0:M.materialDescription)||"",DelFlag:!1}}):[{MaterialDescriptionId:null,Function:"INS",Material:((ia=s.headerData)==null?void 0:ia.materialNumber)||"",Langu:"EN",LanguIso:"",MatlDesc:((sl=s.headerData)==null?void 0:sl.globalMaterialDescription)||"",DelFlag:!1}],di=is!=null&&is.length?is==null?void 0:is.map(M=>{var ae;return{UomId:O===h.EXTEND&&r?null:(M==null?void 0:M.UomId)||null,Function:"INS",Material:((ae=s.headerData)==null?void 0:ae.materialNumber)||"",AltUnit:(M==null?void 0:M.aUnit)||"",AltUnitIso:"",Numerator:(M==null?void 0:M.yValue)||"1",Denominatr:(M==null?void 0:M.xValue)||"1",EanUpc:(M==null?void 0:M.eanUpc)||"",EanCat:M.eanCategory||"",Length:M.length,NetWeight:M.netWeight||"",Width:M.width,Height:M.height,UnitDim:M.unitsOfDimension||"",UnitDimIso:"",Volume:M.volume||"",Volumeunit:M.volumeUnit||"",VolumeunitIso:"",GrossWt:M.grossWeight||"",UnitOfWt:M.weightUnit||"",UnitOfWtIso:"",DelFlag:!1,SubUom:"",SubUomIso:"",GtinVariant:"",MaterialExternal:null,MaterialGuid:null,MaterialVersion:null,NestingFactor:"",MaximumStacking:null,CapacityUsage:M.capacityUsage,EwmCwUomType:"",MaterialLong:null}}):[{UomId:null,Function:"INS",Material:((ne=s.headerData)==null?void 0:ne.materialNumber)||"",AltUnit:((ra=(qe=(E=s==null?void 0:s.payloadData)==null?void 0:E["Basic Data"])==null?void 0:qe.basic)==null?void 0:ra.BaseUom)||"",AltUnitIso:"",Numerator:"1",Denominatr:"1",EanUpc:"",EanCat:"",Length:"",Width:"",Height:"",UnitDim:"",UnitDimIso:"",Volume:((xa=(Ye=(Ca=s==null?void 0:s.payloadData)==null?void 0:Ca["Basic Data"])==null?void 0:Ye.basic)==null?void 0:xa.Volume)||"",Volumeunit:((ol=(oa=(Ze=s==null?void 0:s.payloadData)==null?void 0:Ze["Basic Data"])==null?void 0:oa.basic)==null?void 0:ol.VolumeUnit)||"",VolumeunitIso:"",GrossWt:"",UnitOfWt:((rl=(Ma=($a=s==null?void 0:s.payloadData)==null?void 0:$a["Basic Data"])==null?void 0:Ma.basic)==null?void 0:rl.UnitOfWt)||"",UnitOfWtIso:"",DelFlag:!1,SubUom:"",SubUomIso:"",GtinVariant:"",MaterialExternal:null,MaterialGuid:null,MaterialVersion:null,NestingFactor:"",MaximumStacking:null,CapacityUsage:"",EwmCwUomType:"",MaterialLong:null}],ii=cs!=null&&cs.length?cs==null?void 0:cs.map(M=>{var ae;return{EanId:O===h.EXTEND&&r?null:(M==null?void 0:M.EanId)||null,Function:"INS",Material:((ae=s.headerData)==null?void 0:ae.materialNumber)||"",Unit:(M==null?void 0:M.altunit)||"",EanUpc:(M==null?void 0:M.eanUpc)||"",EanCat:(M==null?void 0:M.eanCategory)||"",MainEan:M.MainEan||!1,Au:M.au||!1}}):null,td=new Set;(za=s==null?void 0:s.payloadData)!=null&&za.Tostroragelocationdata?(tl=s==null?void 0:s.payloadData)==null||tl.Tostroragelocationdata:(_a=(Tl=s.headerData)==null?void 0:Tl.views)!=null&&_a.includes((ke=f)==null?void 0:ke.STORAGE)&&Qs.filter(M=>{var Re,Le,fe,Xe;if(!((Le=(Re=M==null?void 0:M.plant)==null?void 0:Re.value)!=null&&Le.code)||!((Xe=(fe=M==null?void 0:M.sloc)==null?void 0:fe.value)!=null&&Xe.code))return!1;const ae=`${M.plant.value.code}-${M.sloc.value.code}`;return td.has(ae)?!1:(td.add(ae),!0)}).map(M=>{var ae,Re,Le,fe,Xe;return{Function:"INS",Material:((ae=s==null?void 0:s.headerData)==null?void 0:ae.materialNumber)||"",Plant:((Le=(Re=M==null?void 0:M.plant)==null?void 0:Re.value)==null?void 0:Le.code)||"",StgeLoc:((Xe=(fe=M==null?void 0:M.sloc)==null?void 0:fe.value)==null?void 0:Xe.code)||""}});const ci={ChildRequestId:((Ya=s==null?void 0:s.Tochildrequestheaderdata)==null?void 0:Ya.ChildRequestId)||null,MaterialGroupType:((ta=s==null?void 0:s.Tochildrequestheaderdata)==null?void 0:ta.MaterialGroupType)||null,TaskId:(l==null?void 0:l.taskId)||null,Comments:R||o,TotalIntermediateTasks:((as=s==null?void 0:s.Tochildrequestheaderdata)==null?void 0:as.TotalIntermediateTasks)||null,IntermediateTaskCount:((nl=s==null?void 0:s.Tochildrequestheaderdata)==null?void 0:nl.IntermediateTaskCount)||null,ReqCreatedBy:((ls=s==null?void 0:s.Tochildrequestheaderdata)==null?void 0:ls.ReqCreatedBy)||null,ReqCreatedOn:((dl=s==null?void 0:s.Tochildrequestheaderdata)==null?void 0:dl.ReqCreatedOn)||null,ReqUpdatedOn:((fl=s==null?void 0:s.Tochildrequestheaderdata)==null?void 0:fl.ReqUpdatedOn)||null,RequestType:((Al=s==null?void 0:s.Tochildrequestheaderdata)==null?void 0:Al.RequestType)||null,RequestPrefix:((ss=s==null?void 0:s.Tochildrequestheaderdata)==null?void 0:ss.RequestPrefix)||null,RequestDesc:((xe=s==null?void 0:s.Tochildrequestheaderdata)==null?void 0:xe.RequestDesc)||null,RequestPriority:((Il=s==null?void 0:s.Tochildrequestheaderdata)==null?void 0:Il.RequestPriority)||null,RequestStatus:((Dl=s==null?void 0:s.Tochildrequestheaderdata)==null?void 0:Dl.RequestStatus)||null,CurrentLevel:(l==null?void 0:l.ATTRIBUTE_3)||"",CurrentLevelName:(l==null?void 0:l.ATTRIBUTE_4)||"",ParticularLevel:p,TaskName:(l==null?void 0:l.taskDesc)||"",ApproverGroup:(l==null?void 0:l.ATTRIBUTE_5)||"",TaskCreatedOn:l!=null&&l.createdOn||ee!=null&&ee.createdOn?ie((l==null?void 0:l.createdOn)||(ee==null?void 0:ee.createdOn)):null,DueDate:l!=null&&l.compDeadline||ee!=null&&ee.compDeadline?ie((l==null?void 0:l.compDeadline)||(ee==null?void 0:ee.compDeadline)):null,Version:((Ha=s==null?void 0:s.Tochildrequestheaderdata)==null?void 0:Ha.Version)||us.DEFAULT_VERSION},ui={MaterialId:O===h.EXTEND&&r||O===h.CREATE&&r?null:(l!=null&&l.taskId||d||ee!=null&&ee.ATTRIBUTE_5)&&!c.includes("-")?Number(c):null,Flag:"",Function:"INS",Material:((Cl=s==null?void 0:s.headerData)==null?void 0:Cl.materialNumber)||"",MatlType:((Ja=(kl=s==null?void 0:s.headerData)==null?void 0:kl.materialType)==null?void 0:Ja.code)||((os=s==null?void 0:s.headerData)==null?void 0:os.materialType)||"",IndSector:((Xa=(_l=s==null?void 0:s.headerData)==null?void 0:_l.industrySector)==null?void 0:Xa.code)||((rs=s==null?void 0:s.headerData)==null?void 0:rs.industrySector)||"",Comments:R||o,ViewNames:$d,Description:(($l=s==null?void 0:s.headerData)==null?void 0:$l.globalMaterialDescription)||"",Uom:(Nl=(ga=s==null?void 0:s.headerData)==null?void 0:ga.Uom)!=null&&Nl.code?s.headerData.Uom.code:((Ml=s==null?void 0:s.headerData)==null?void 0:Ml.Uom)||"",Category:(Yl=(zl=s==null?void 0:s.headerData)==null?void 0:zl.Category)!=null&&Yl.code?s.headerData.Category.code:((ts=s==null?void 0:s.headerData)==null?void 0:ts.Category)||"",Relation:(Jl=(ns=s==null?void 0:s.headerData)==null?void 0:ns.Relation)!=null&&Jl.code?s.headerData.Relation.code:((Xl=s==null?void 0:s.headerData)==null?void 0:Xl.Relation)||"",Usage:((Vl=s==null?void 0:s.headerData)==null?void 0:Vl.Usage)||"",CreationDate:l!=null&&l.requestId||d||(il=n==null?void 0:n.payloadData)!=null&&il.RequestId?ie((bl=n==null?void 0:n.payloadData)==null?void 0:bl.ReqCreatedOn):`/Date(${Date.now()}+0000)/`,EditId:null,ExtendId:null,MassCreationId:O===h.CREATE||O===h.CREATE_WITH_UPLOAD?l!=null&&l.requestId||l!=null&&l.ATTRIBUTE_1||d||(Kl=n==null?void 0:n.payloadData)!=null&&Kl.RequestId?(Ol=n==null?void 0:n.payloadData)==null?void 0:Ol.RequestId:"":null,MassEditId:((S=s==null?void 0:s.payloadData)==null?void 0:S.MassEditId)||"",MassExtendId:O===h.EXTEND||O===h.EXTEND_WITH_UPLOAD?l!=null&&l.requestId||l!=null&&l.ATTRIBUTE_1||d||(Ge=n==null?void 0:n.payloadData)!=null&&Ge.RequestId?(Je=n==null?void 0:n.payloadData)==null?void 0:Je.RequestId:(pa=u==null?void 0:u.requestHeader)!=null&&pa.requestId?(Ra=u==null?void 0:u.requestHeader)==null?void 0:Ra.requestId:Oe:null,TaskId:(l==null?void 0:l.taskId)||null,TaskName:(l==null?void 0:l.taskDesc)||null,TotalIntermediateTasks:l!=null&&l.requestId||l!=null&&l.ATTRIBUTE_1||d||(qa=n==null?void 0:n.payloadData)!=null&&qa.RequestId?(Sa=n==null?void 0:n.payloadData)==null?void 0:Sa.TotalIntermediateTasks:"",IntermediateTaskCount:l!=null&&l.requestId||l!=null&&l.ATTRIBUTE_1||d||(Wa=n==null?void 0:n.payloadData)!=null&&Wa.RequestId?(Pa=n==null?void 0:n.payloadData)==null?void 0:Pa.IntermediateTaskCount:"",BasicView:((cl=s==null?void 0:s.headerData)==null?void 0:cl.views.includes((Na=f)==null?void 0:Na.BASIC_DATA))||!1,SalesView:((Va=s==null?void 0:s.headerData)==null?void 0:Va.views.includes((ul=f)==null?void 0:ul.SALES))||!1,MrpView:((yl=s==null?void 0:s.headerData)==null?void 0:yl.views.includes((Ka=f)==null?void 0:Ka.MRP))||!1,PurchaseView:((ql=s==null?void 0:s.headerData)==null?void 0:ql.views.includes((vl=f)==null?void 0:vl.PURCHASING))||!1,AccountView:((Pl=s==null?void 0:s.headerData)==null?void 0:Pl.views.includes((Sl=f)==null?void 0:Sl.ACCOUNTING))||!1,CostView:((ps=s==null?void 0:s.headerData)==null?void 0:ps.views.includes((gs=f)==null?void 0:gs.COSTING))||!1,WorkSchedView:((Es=s==null?void 0:s.headerData)==null?void 0:Es.views.includes((Rs=f)==null?void 0:Rs.WORK_SCHEDULING_2))||!1,WarehouseView:((Is=s==null?void 0:s.headerData)==null?void 0:Is.views.includes((As=f)==null?void 0:As.WAREHOUSE))||!1,ForecastView:!1,PrtView:!1,StorageView:((_s=s==null?void 0:s.headerData)==null?void 0:_s.views.includes((Ds=f)==null?void 0:Ds.STORAGE))||!1,QualityView:!1,IsFirstChangeLogCommit:!1,IsMarkedForDeletion:!1,IsFirstCreate:!(l!=null&&l.taskId),ManufacturerId:(s==null?void 0:s.ManufacturerID)||"",OrgData:Qs||[],Toclientdata:{ClientId:((Ns=s==null?void 0:s.headerData)==null?void 0:Ns.clientId)||null,Function:"INS",Material:((Ms=s==null?void 0:s.headerData)==null?void 0:Ms.materialNumber)||"",DelFlag:!0,MatlGroup:((ys=(Os=(bs=s==null?void 0:s.payloadData)==null?void 0:bs["Basic Data"])==null?void 0:Os.basic)==null?void 0:ys.MatlGroup)||"",Extmatlgrp:((Ss=(qs=(vs=s==null?void 0:s.payloadData)==null?void 0:vs["Basic Data"])==null?void 0:qs.basic)==null?void 0:Ss.Extmatlgrp)||"",OldMatNo:((ge=(I=(Ps=s==null?void 0:s.payloadData)==null?void 0:Ps["Basic Data"])==null?void 0:I.basic)==null?void 0:ge.OldMatNo)||"",BaseUom:((Te=(Me=(Ae=s==null?void 0:s.payloadData)==null?void 0:Ae["Basic Data"])==null?void 0:Me.basic)==null?void 0:Te.BaseUom)||"",Document:(H=(pe=(oe=s==null?void 0:s.payloadData)==null?void 0:oe["Basic Data"])==null?void 0:pe.basic)==null?void 0:H.Document,DocType:(_e=(la=(Fe=s==null?void 0:s.payloadData)==null?void 0:Fe["Basic Data"])==null?void 0:la.basic)==null?void 0:_e.DocType,DocVers:(Qa=(Ua=(Ue=s==null?void 0:s.payloadData)==null?void 0:Ue["Basic Data"])==null?void 0:Ua.basic)==null?void 0:Qa.DocVers,DocFormat:(Bl=(Ll=(Za=s==null?void 0:s.payloadData)==null?void 0:Za["Basic Data"])==null?void 0:Ll.basic)==null?void 0:Bl.DocFormat,DocChgNo:(xl=(Gl=(Ul=s==null?void 0:s.payloadData)==null?void 0:Ul["Basic Data"])==null?void 0:Gl.basic)==null?void 0:xl.DocChgNo,PageNo:(Fl=(ml=(Hl=s==null?void 0:s.payloadData)==null?void 0:Hl["Basic Data"])==null?void 0:ml.basic)==null?void 0:Fl.PageNo,NoSheets:(Ls=s==null?void 0:s.payloadData)==null?void 0:Ls.NoSheets,ProdMemo:(Bs=s==null?void 0:s.payloadData)==null?void 0:Bs.ProdMemo,Pageformat:(na=s==null?void 0:s.payloadData)==null?void 0:na.DocFormat,SizeDim:(Ql=s==null?void 0:s.payloadData)==null?void 0:Ql.SizeDim,BaseUomIso:"",BasicMatl:((go=(Co=(fo=s==null?void 0:s.payloadData)==null?void 0:fo["Basic Data"])==null?void 0:Co.basic)==null?void 0:go.BasicMatl)||"",StdDescr:(po=s==null?void 0:s.payloadData)==null?void 0:po.StdDescr,DsnOffice:((Ao=(Eo=(Ro=s==null?void 0:s.payloadData)==null?void 0:Ro["Basic Data"])==null?void 0:Eo.basic)==null?void 0:Ao.DsnOffice)||"",PurValkey:((_o=(Do=(Io=s==null?void 0:s.payloadData)==null?void 0:Io["Purchasing-General"])==null?void 0:Do["Purchasing-General"])==null?void 0:_o.PurValkey)||"",NetWeight:(bo=(Mo=(No=s==null?void 0:s.payloadData)==null?void 0:No["Basic Data"])==null?void 0:Mo.basic)==null?void 0:bo.NetWeight,UnitOfWt:((vo=(yo=(Oo=s==null?void 0:s.payloadData)==null?void 0:Oo["Basic Data"])==null?void 0:yo.basic)==null?void 0:vo.UnitOfWt)||"",TransGrp:(Po=(So=(qo=s==null?void 0:s.payloadData)==null?void 0:qo["Sales-General"])==null?void 0:So["Sales-General"])==null?void 0:Po.TransGrp,XSalStatus:(Uo=(Bo=(Lo=s==null?void 0:s.payloadData)==null?void 0:Lo["Sales-General"])==null?void 0:Bo["Sales-General"])==null?void 0:Uo.XSalStatus,Svalidfrom:(Ho=(xo=(Go=s==null?void 0:s.payloadData)==null?void 0:Go["Sales-General"])==null?void 0:xo["Sales-General"])!=null&&Ho.Svalidfrom?ie((wo=(Fo=(mo=s==null?void 0:s.payloadData)==null?void 0:mo["Sales-General"])==null?void 0:Fo["Sales-General"])==null?void 0:wo.Svalidfrom):null,Division:(Wo=n==null?void 0:n.payloadData)!=null&&Wo.Division?(jo=n==null?void 0:n.payloadData)==null?void 0:jo.Division:((zo=($o=(ko=s==null?void 0:s.payloadData)==null?void 0:ko["Basic Data"])==null?void 0:$o.basic)==null?void 0:zo.Division)||"",ProdHier:((Xo=(Jo=(Yo=s==null?void 0:s.payloadData)==null?void 0:Yo["Basic Data"])==null?void 0:Jo.basic)==null?void 0:Xo.ProdHier)||"",CadId:(Qo=(Ko=(Vo=s==null?void 0:s.payloadData)==null?void 0:Vo["Basic Data"])==null?void 0:Ko.basic)==null?void 0:Qo.CadId,VarOrdUn:(ar=(er=(Zo=s==null?void 0:s.payloadData)==null?void 0:Zo["Purchasing-General"])==null?void 0:er["Purchasing-General"])==null?void 0:ar.VarOrdUn,UnitOfWtIso:"",MatGrpSm:"",Authoritygroup:"",QmProcmnt:"",BatchMgmt:(or=(sr=(lr=s==null?void 0:s.payloadData)==null?void 0:lr["Sales-General"])==null?void 0:sr["Sales-General"])==null?void 0:or.BatchMgmt,SalStatus:"",Catprofile:"",Hazmatprof:((nr=(tr=(rr=s==null?void 0:s.payloadData)==null?void 0:rr["Basic Data"])==null?void 0:tr.basic)==null?void 0:nr.Hazmatprof)||"",HighVisc:(cr=(ir=(dr=s==null?void 0:s.payloadData)==null?void 0:dr["Basic Data"])==null?void 0:ir.basic)==null?void 0:cr.HighVisc,Pvalidfrom:(Tr=(hr=(ur=s==null?void 0:s.payloadData)==null?void 0:ur["Basic Data"])==null?void 0:hr.basic)!=null&&Tr.Pvalidfrom?ie((gr=(Cr=(fr=s==null?void 0:s.payloadData)==null?void 0:fr["Basic Data"])==null?void 0:Cr.basic)==null?void 0:gr.Pvalidfrom):null,EnvtRlvt:"",ProdAlloc:(Er=(Rr=(pr=s==null?void 0:s.payloadData)==null?void 0:pr["Basic Data"])==null?void 0:Rr.basic)==null?void 0:Er.ProdAlloc,ParEff:!0,Matcmpllvl:"",GItemCat:((Dr=(Ir=(Ar=s==null?void 0:s.payloadData)==null?void 0:Ar["Basic Data"])==null?void 0:Ir.basic)==null?void 0:Dr.GItemCat)||"",CSalStatus:((Mr=(Nr=(_r=s==null?void 0:s.payloadData)==null?void 0:_r["Basic Data"])==null?void 0:Nr.basic)==null?void 0:Mr.CSalStatus)||"",IntlPoPrice:((yr=(Or=(br=s==null?void 0:s.payloadData)==null?void 0:br["Basic Data"])==null?void 0:Or.basic)==null?void 0:yr.IntlPoPrice)||"",PryVendor:((Sr=(qr=(vr=s==null?void 0:s.payloadData)==null?void 0:vr["Basic Data"])==null?void 0:qr.basic)==null?void 0:Sr.PryVendor)||"",PlanningArea:((Br=(Lr=(Pr=s==null?void 0:s.payloadData)==null?void 0:Pr["Basic Data"])==null?void 0:Lr.basic)==null?void 0:Br.PlanningArea)||"",PlanningFactor:((xr=(Gr=(Ur=s==null?void 0:s.payloadData)==null?void 0:Ur["Basic Data"])==null?void 0:Gr.basic)==null?void 0:xr.PlanningFactor)||"",ReturnMatNumber:((Fr=(mr=(Hr=s==null?void 0:s.payloadData)==null?void 0:Hr["Basic Data"])==null?void 0:mr.basic)==null?void 0:Fr.ReturnMatNumber)||"",ParentMatNumber:((jr=(Wr=(wr=s==null?void 0:s.payloadData)==null?void 0:wr["Basic Data"])==null?void 0:Wr.basic)==null?void 0:jr.ParentMatNumber)||"",DiversionControlFlag:((zr=($r=(kr=s==null?void 0:s.payloadData)==null?void 0:kr["Basic Data"])==null?void 0:$r.basic)==null?void 0:zr.DiversionControlFlag)||"",MatGroupPackagingMat:((Xr=(Jr=(Yr=s==null?void 0:s.payloadData)==null?void 0:Yr["Basic Data"])==null?void 0:Jr.basic)==null?void 0:Xr.MatGroupPackagingMat)||"",HazMatNo:((Qr=(Kr=(Vr=s==null?void 0:s.payloadData)==null?void 0:Vr[f.STORAGE_GENERAL])==null?void 0:Kr[f.STORAGE_GENERAL])==null?void 0:Qr.HazMatNo)||"",QtyGrGi:((at=(et=(Zr=s==null?void 0:s.payloadData)==null?void 0:Zr[f.STORAGE_GENERAL])==null?void 0:et[f.STORAGE_GENERAL])==null?void 0:at.QtyGrGi)||"",TempConds:((ot=(st=(lt=s==null?void 0:s.payloadData)==null?void 0:lt[f.STORAGE_GENERAL])==null?void 0:st[f.STORAGE_GENERAL])==null?void 0:ot.TempConds)||"",Container:((nt=(tt=(rt=s==null?void 0:s.payloadData)==null?void 0:rt[f.STORAGE_GENERAL])==null?void 0:tt[f.STORAGE_GENERAL])==null?void 0:nt.Container)||"",LabelType:((ct=(it=(dt=s==null?void 0:s.payloadData)==null?void 0:dt[f.STORAGE_GENERAL])==null?void 0:it[f.STORAGE_GENERAL])==null?void 0:ct.LabelType)||"",LabelForm:((Tt=(ht=(ut=s==null?void 0:s.payloadData)==null?void 0:ut[f.STORAGE_GENERAL])==null?void 0:ht[f.STORAGE_GENERAL])==null?void 0:Tt.LabelForm)||"",AppdBRec:((gt=(Ct=(ft=s==null?void 0:s.payloadData)==null?void 0:ft[f.STORAGE_GENERAL])==null?void 0:Ct[f.STORAGE_GENERAL])==null?void 0:gt.AppdBRec)||"",Minremlife:((Et=(Rt=(pt=s==null?void 0:s.payloadData)==null?void 0:pt[f.STORAGE_GENERAL])==null?void 0:Rt[f.STORAGE_GENERAL])==null?void 0:Et.Minremlife)||"",ShelfLife:((Dt=(It=(At=s==null?void 0:s.payloadData)==null?void 0:At[f.STORAGE_GENERAL])==null?void 0:It[f.STORAGE_GENERAL])==null?void 0:Dt.ShelfLife)||"",PeriodIndExpirationDate:((Mt=(Nt=(_t=s==null?void 0:s.payloadData)==null?void 0:_t[f.STORAGE_GENERAL])==null?void 0:Nt[f.STORAGE_GENERAL])==null?void 0:Mt.PeriodIndExpirationDate)||"",RoundUpRuleExpirationDate:((yt=(Ot=(bt=s==null?void 0:s.payloadData)==null?void 0:bt[f.STORAGE_GENERAL])==null?void 0:Ot[f.STORAGE_GENERAL])==null?void 0:yt.RoundUpRuleExpirationDate)||"",SledBbd:((St=(qt=(vt=s==null?void 0:s.payloadData)==null?void 0:vt[f.STORAGE_GENERAL])==null?void 0:qt[f.STORAGE_GENERAL])==null?void 0:St.SledBbd)||"",SerializationLevel:((Bt=(Lt=(Pt=s==null?void 0:s.payloadData)==null?void 0:Pt[f.STORAGE_GENERAL])==null?void 0:Lt[f.STORAGE_GENERAL])==null?void 0:Bt.SerializationLevel)||"",ShelfLifeReqMax:((xt=(Gt=(Ut=s==null?void 0:s.payloadData)==null?void 0:Ut[f.STORAGE_GENERAL])==null?void 0:Gt[f.STORAGE_GENERAL])==null?void 0:xt.ShelfLifeReqMax)||"",ShelfLifeReqMin:((Ft=(mt=(Ht=s==null?void 0:s.payloadData)==null?void 0:Ht[f.STORAGE_GENERAL])==null?void 0:mt[f.STORAGE_GENERAL])==null?void 0:Ft.ShelfLifeReqMin)||"",MaturityDur:((jt=(Wt=(wt=s==null?void 0:s.payloadData)==null?void 0:wt[f.STORAGE_GENERAL])==null?void 0:Wt[f.STORAGE_GENERAL])==null?void 0:jt.MaturityDur)||"",StorPct:((zt=($t=(kt=s==null?void 0:s.payloadData)==null?void 0:kt[f.STORAGE_GENERAL])==null?void 0:$t[f.STORAGE_GENERAL])==null?void 0:zt.StorPct)||"",StorConds:((Xt=(Jt=(Yt=s==null?void 0:s.payloadData)==null?void 0:Yt[f.STORAGE_GENERAL])==null?void 0:Jt[f.STORAGE_GENERAL])==null?void 0:Xt.StorConds)||""},Toplantdata:ti,Tosalesdata:(Vt=s==null?void 0:s.headerData)!=null&&Vt.views.includes("Sales")?zd:[],Tomaterialdescription:ni,Touomdata:di,Toeandata:ii,Tostroragelocationdata:li,ToClassification:rd,Tomaterialerrordata:(s==null?void 0:s.Tomaterialerrordata)||{},Toaccountingdata:(Qt=s==null?void 0:s.headerData)!=null&&Qt.views.includes((Kt=f)==null?void 0:Kt.ACCOUNTING)?Zd:[],Tocontroldata:(Zt=s==null?void 0:s.headerData)!=null&&Zt.views.includes("Sales")?ad:[],Torequestheaderdata:{RequestId:l!=null&&l.requestId||l!=null&&l.ATTRIBUTE_1||d||(en=n==null?void 0:n.payloadData)!=null&&en.RequestId?(an=n==null?void 0:n.payloadData)==null?void 0:an.RequestId:(ln=u==null?void 0:u.requestHeader)!=null&&ln.requestId?(sn=u==null?void 0:u.requestHeader)==null?void 0:sn.requestId:Oe,ReqCreatedBy:l!=null&&l.requestId||l!=null&&l.ATTRIBUTE_1||d||(on=n==null?void 0:n.payloadData)!=null&&on.RequestId?(rn=n==null?void 0:n.payloadData)==null?void 0:rn.ReqCreatedBy:(tn=u==null?void 0:u.requestHeader)==null?void 0:tn.reqCreatedBy,ReqCreatedOn:ie(l!=null&&l.requestId||l!=null&&l.ATTRIBUTE_1||d||(nn=n==null?void 0:n.payloadData)!=null&&nn.RequestId?(dn=n==null?void 0:n.payloadData)==null?void 0:dn.ReqCreatedOn:(cn=u==null?void 0:u.requestHeader)==null?void 0:cn.reqCreatedOn),ReqUpdatedOn:ie(l!=null&&l.requestId||l!=null&&l.ATTRIBUTE_1||d||(un=n==null?void 0:n.payloadData)!=null&&un.RequestId?(hn=n==null?void 0:n.payloadData)==null?void 0:hn.ReqUpdatedOn:(Tn=u==null?void 0:u.requestHeader)==null?void 0:Tn.reqCreatedOn),RequestType:l!=null&&l.requestId||l!=null&&l.ATTRIBUTE_1||d||(fn=n==null?void 0:n.payloadData)!=null&&fn.RequestId?(Cn=n==null?void 0:n.payloadData)==null?void 0:Cn.RequestType:(gn=u==null?void 0:u.requestHeader)==null?void 0:gn.requestType,Division:l!=null&&l.requestId||l!=null&&l.ATTRIBUTE_1||d||(pn=n==null?void 0:n.payloadData)!=null&&pn.RequestId?(Rn=n==null?void 0:n.payloadData)==null?void 0:Rn.Division:(En=u==null?void 0:u.requestHeader)==null?void 0:En.Division,RequestPriority:l!=null&&l.requestId||l!=null&&l.ATTRIBUTE_1||d||(An=n==null?void 0:n.payloadData)!=null&&An.RequestId?(In=n==null?void 0:n.payloadData)==null?void 0:In.RequestPriority:(Dn=u==null?void 0:u.requestHeader)==null?void 0:Dn.requestPriority,RequestDesc:l!=null&&l.requestId||l!=null&&l.ATTRIBUTE_1||d||(_n=n==null?void 0:n.payloadData)!=null&&_n.RequestId?(Nn=n==null?void 0:n.payloadData)==null?void 0:Nn.RequestDesc:(Mn=u==null?void 0:u.requestHeader)==null?void 0:Mn.requestDesc,RequestStatus:l!=null&&l.requestId||l!=null&&l.ATTRIBUTE_1||d||(bn=n==null?void 0:n.payloadData)!=null&&bn.RequestId?(On=n==null?void 0:n.payloadData)==null?void 0:On.RequestStatus:(yn=u==null?void 0:u.requestHeader)==null?void 0:yn.requestStatus,FirstProd:l!=null&&l.requestId||l!=null&&l.ATTRIBUTE_1||d||(vn=n==null?void 0:n.payloadData)!=null&&vn.RequestId?(qn=n==null?void 0:n.payloadData)==null?void 0:qn.FirstProd:((Sn=e==null?void 0:e.payloadData)==null?void 0:Sn.FirstProductionDate)||null,LaunchDate:l!=null&&l.requestId||l!=null&&l.ATTRIBUTE_1||d||(Pn=n==null?void 0:n.payloadData)!=null&&Pn.RequestId?(Ln=n==null?void 0:n.payloadData)==null?void 0:Ln.LaunchDate:((Bn=e==null?void 0:e.payloadData)==null?void 0:Bn.LaunchDate)||null,LeadingCat:l!=null&&l.requestId||l!=null&&l.ATTRIBUTE_1||d||(Un=n==null?void 0:n.payloadData)!=null&&Un.RequestId?(Gn=n==null?void 0:n.payloadData)==null?void 0:Gn.LeadingCat:(xn=u==null?void 0:u.requestHeader)==null?void 0:xn.leadingCat,Region:l!=null&&l.requestId||l!=null&&l.ATTRIBUTE_1||d||(Hn=n==null?void 0:n.payloadData)!=null&&Hn.RequestId?(mn=n==null?void 0:n.payloadData)==null?void 0:mn.Region:(Fn=u==null?void 0:u.requestHeader)==null?void 0:Fn.region,IsBifurcated:!0,Version:((wn=n==null?void 0:n.payloadData)==null?void 0:wn.Version)||((Wn=u==null?void 0:u.requestHeader)==null?void 0:Wn.Version)||us.DEFAULT_VERSION},Tochildrequestheaderdata:l!=null&&l.requestId||l!=null&&l.ATTRIBUTE_1||d||(jn=n==null?void 0:n.payloadData)!=null&&jn.RequestId?ci:{},Towarehousedata:ai,changeLogData:m&&Object.keys(m).length>0&&(m[(kn=s.headerData)==null?void 0:kn.materialNumber]||m[($n=s.headerData)==null?void 0:$n.id])?{RequestId:(m==null?void 0:m.RequestId)||((zn=s==null?void 0:s.changeLogData)==null?void 0:zn.RequestId),ChangeLogId:((Yn=s==null?void 0:s.changeLogData)==null?void 0:Yn.ChangeLogId)??null,ChildRequestId:((m==null?void 0:m.ChildRequestId)||((Jn=s==null?void 0:s.changeLogData)==null?void 0:Jn.ChildRequestId))??null,...m[(Xn=s.headerData)==null?void 0:Xn.materialNumber],...m[(Vn=s.headerData)==null?void 0:Vn.id]}:{RequestId:(Kn=s==null?void 0:s.changeLogData)==null?void 0:Kn.RequestId,ChangeLogId:((Qn=s==null?void 0:s.changeLogData)==null?void 0:Qn.ChangeLogId)??null,ChildRequestId:((Zn=s==null?void 0:s.changeLogData)==null?void 0:Zn.ChildRequestId)??null}};W.push(ui)}}}),W}return{createPayloadFromReduxState:he}},oT=({initialReqScreen:r,isReqBench:d,remarks:R="",userInput:o="",selectedLevel:p=""})=>{var Q;const e=Y(n=>n.payload),O=(Q=e==null?void 0:e.payloadData)==null?void 0:Q.RequestType,l=Y(n=>n.userManagement.taskData),u=Y(n=>n.request),x=To(to.CURRENT_TASK),ee=typeof x=="string"?JSON.parse(x):x,m=Y(n=>n.changeLog.createChangeLogData),Ce=Vs(),Oe=new URLSearchParams(Ce.search.split("?")[1]).get("RequestId");function he(n){const W=[];return Object.keys(n).forEach(c=>{var T,U,g,L,B,b,q,P,G,F,w,j,Z,le,te,i,y,V,v,z,K,Ee,X,ye,je,$,De,de,We,ha,Ta,aa,se,fa,ll,hl,Rl,El,Aa,wa,Ia,ve,Da,ia,sl,ne,E,qe,ra,Ca,Ye,xa,Ze,oa,ol,$a,Ma,rl,za,tl,Tl,ke,_a,Ya,ta,as,nl,ls,dl,fl,Al,ss,xe,Il,Dl,Ha,Cl,kl,Ja,os,_l,Xa,rs,$l,ga,Nl,Ml,zl,Yl,ts,ns,Jl,Xl,Vl,il,bl,Kl,Ol,S,Ge,Je,pa,Ra,qa,Sa,Wa,Pa,Na,cl,ul,Va,Ka,yl,vl,ql,Sl,Pl,gs,ps,Rs,Es,As,Is,Ds,_s,Ns,Ms,bs,Os,ys,vs,qs,Ss,Ps,I,ge,Ae,Me,Te,oe,pe,H,Fe,la,_e,Ue,Ua,Qa,Za,Ll,Bl,Ul,Gl,xl,Hl,ml,Fl,Ls,Bs,na,Ql,fo,Co,go,po,Ro,Eo,Ao,Io,Do,_o,No,Mo,bo,Oo,yo,vo,qo,So,Po,Lo,Bo,Uo,Go,xo,Ho,mo,Fo,wo,Wo,jo,ko,$o,zo,Yo,Jo,Xo,Vo,Ko,Qo,Zo,er,ar,lr,sr,or,rr,tr,nr,dr,ir,cr,ur,hr,Tr,fr,Cr,gr,pr,Rr,Er,Ar,Ir,Dr,_r,Nr,Mr,br,Or,yr,vr,qr,Sr,Pr,Lr,Br,Ur,Gr,xr,Hr,mr,Fr,wr,Wr,jr,kr,$r,zr,Yr,Jr,Xr,Vr,Kr,Qr,Zr,et,at,lt,st,ot,rt,tt,nt,dt,it,ct,ut,ht,Tt,ft,Ct,gt,pt,Rt,Et,At,It,Dt,_t,Nt,Mt,bt,Ot,yt,vt,qt,St,Pt,Lt,Bt,Ut,Gt,xt,Ht,mt,Ft,wt,Wt,jt,kt,$t,zt,Yt,Jt,Xt,Vt,Kt,Qt,Zt,en,an,ln,sn,on,rn,tn,nn,dn,cn,un,hn,Tn,fn,Cn,gn,pn,Rn,En,An,In,Dn,_n,Nn,Mn,bn,On,yn,vn,qn,Sn,Pn,Ln,Bn,Un,Gn,xn,Hn,mn,Fn,wn,Wn,jn,kn,$n,zn,Yn,Jn,Xn,Vn,Kn,Qn,Zn,s,Qs,$d,ua,ed,zd,Yd,Jd,Xd,Vd,Kd,ad,Oi,ld,Qd,sd,Zd,ei,ai,no,od,li,el,rd,si,oi,ri,yi,vi,ti,ds,is,cs,ni,di,ii,td,ci,ui,M,ae,Re,Le,fe,Xe,A,Ve,ce,La,we,be,Us,Gs,xs,Hs,ms,Fs,ws,Ws,js,ks,$s,zs,Ys,nd,dd,id,cd,ud,hd,Td,fd,Cd,gd,pd,Rd,Ed,Ad,Id,Dd,_d,Nd,Ba,Zi,ec,ac,lc,sc,oc,rc,tc,nc,dc,ic,cc,uc,hc,Tc,fc,Cc,gc,pc,Rc,Ec,Ac,Ic,Dc,_c,Nc,Mc,bc,Oc,yc,vc,qc,Sc,Pc,Lc,Bc,Uc,Gc,xc,Hc,mc,Fc,wc,Wc,jc,kc,$c,zc,Yc,Jc,Xc,Vc,Kc,Qc,Zc,eu,au,lu,su,ou,ru,tu,nu,du,iu,cu,uu,hu,Tu,fu,Cu,gu,pu,Ru,Eu,Au,Iu,Du,_u,Nu,Mu,bu,Ou,yu,vu,qu,Su,Pu,Lu,Bu,Uu,Gu,xu,Hu,mu,Fu,wu,Wu,ju,ku,$u,zu,Yu,Ju,Xu,Vu;if(c.includes("-")||/\d/.test(c)){const a=n[c];if((T=a==null?void 0:a.headerData)!=null&&T.included){const hi=Yh(((U=a==null?void 0:a.headerData)==null?void 0:U.orgData)||VT||[]),RT=(((g=a==null?void 0:a.headerData)==null?void 0:g.views)||[]).join(",").trim(),ba=(L=a==null?void 0:a.payloadData)!=null&&L.Sales?Object.values(a.payloadData.Sales):[],ET=(B=a==null?void 0:a.payloadData)!=null&&B.Listing?Object.values(a.payloadData.Listing):[],AT=(b=a==null?void 0:a.payloadData)!=null&&b.POS?Object.values(a.payloadData.POS):[],IT=(q=a==null?void 0:a.payloadData)!=null&&q[f.LOGISTIC_STORE]?Object.values((P=a.payloadData)==null?void 0:P[f.LOGISTIC_STORE]):[],DT=(G=a==null?void 0:a.payloadData)!=null&&G[f.SALES_DATA]?Object.values((F=a.payloadData)==null?void 0:F[f.SALES_DATA]):[];let Ti={};Object.entries(((j=(w=a==null?void 0:a.payloadData)==null?void 0:w["Merchant Input"])==null?void 0:j.basic)||{}).forEach(([C,k])=>{Ti[C]=k||""});const Ku=new Set,_T=hi.filter(C=>{var Ie,He,ea;if(!((Ie=C.salesOrg)!=null&&Ie.code)||!((ea=(He=C.dc)==null?void 0:He.value)!=null&&ea.code))return!1;const k=`${C.salesOrg.code}-${C.dc.value.code}`;return Ku.has(k)?!1:(Ku.add(k),!0)}).map((C,k)=>{var Ie,He,ea,sa,Oa,Se,ja,_,ka,Ne,Md,$e,Pe,fi,Ci,bd,Od,yd,vd,qd,Sd,Pd,Ld,Bd,Ud,Gd,xd;return{SalesId:O===h.EXTEND&&r?null:((Ie=ba[k])==null?void 0:Ie.SalesId)||"",Function:"INS",Material:((He=a.headerData)==null?void 0:He.materialNumber)||"",SalesOrg:((ea=C.salesOrg)==null?void 0:ea.code)||"",DistrChan:((Oa=(sa=C.dc)==null?void 0:sa.value)==null?void 0:Oa.code)||"",Json249:((ja=(Se=C.plant)==null?void 0:Se.value)==null?void 0:ja.code)||"",Json79:((ka=(_=C==null?void 0:C.store)==null?void 0:_.value)==null?void 0:ka.code)||"",DelFlag:!1,MatlStats:((Ne=ba[k])==null?void 0:Ne.MatlStats)||"",RebateGrp:((Md=ba[k])==null?void 0:Md.RebateGrp)||"",CashDisc:(($e=ba[k])==null?void 0:$e.CashDisc)||!0,SalStatus:((Pe=ba[k])==null?void 0:Pe.SalStatus)||"",DelyUnit:"0.000",ValidFrom:(fi=ba[k])!=null&&fi.ValidFrom?ie((Ci=ba[k])==null?void 0:Ci.ValidFrom):null,DelyUom:((bd=ba[k])==null?void 0:bd.DelyUom)||"",DelygPlnt:((Od=ba[k])==null?void 0:Od.DelygPlnt)||"",MatPrGrp:((yd=ba[k])==null?void 0:yd.MatPrGrp)||"",AcctAssgt:((vd=ba[k])==null?void 0:vd.AcctAssgt)||"",MatlGrp4:((qd=ba[k])==null?void 0:qd.MatlGrp4)||"",MatlGrp2:((Sd=ba[k])==null?void 0:Sd.MatlGrp2)||"",MatlGrp5:((Pd=ba[k])==null?void 0:Pd.MatlGrp5)||"",BatchMgmt:((Ld=ba[k])==null?void 0:Ld.BatchMgmt)||"",Countryori:((Bd=ba[k])==null?void 0:Bd.Countryori)||"",Depcountry:((Ud=ba[k])==null?void 0:Ud.Depcountry)||"",SalesUnit:((Gd=ba[k])==null?void 0:Gd.SalesUnit)||"",ItemCat:((xd=ba[k])==null?void 0:xd.ItemCat)||"",...ET[k],...AT[k],...IT[k],...DT[k],...Ti}}),NT=(Z=a==null?void 0:a.payloadData)!=null&&Z.Purchasing?Object.entries(a.payloadData.Purchasing):[],MT=(le=a==null?void 0:a.payloadData)!=null&&le.MRP?Object.entries(a.payloadData.MRP):[],bT=(te=a==null?void 0:a.payloadData)!=null&&te[f.SALES_PLANT]?Object.entries((i=a.payloadData)==null?void 0:i[f.SALES_PLANT]):[],OT=(y=a==null?void 0:a.payloadData)!=null&&y[f.STORAGE_PLANT]?Object.entries((V=a.payloadData)==null?void 0:V[f.STORAGE_PLANT]):[],yT=(v=a==null?void 0:a.payloadData)!=null&&v.Accounting?Object.entries(a.payloadData.Accounting):[];let Qu=[];if(((X=(Ee=(K=(z=a==null?void 0:a.payloadData)==null?void 0:z.TaxData)==null?void 0:K.TaxData)==null?void 0:Ee.TaxDataSet)==null?void 0:X.length)>0){const C={};(De=($=(je=(ye=a==null?void 0:a.payloadData)==null?void 0:ye.TaxData)==null?void 0:je.TaxData)==null?void 0:$.TaxDataSet)==null||De.forEach(k=>{var ea,sa;const Ie=k.Country;C[Ie]||(C[Ie]={ControlId:k.ControlId??null,Function:"INS",Material:((ea=a.headerData)==null?void 0:ea.materialNumber)||"",Depcountry:k.Country});const He=Object.keys(C[Ie]).filter(Oa=>Oa.startsWith("TaxType")).length+1;He<=9&&(C[Ie][`TaxType${He}`]=k.TaxType,C[Ie][`Taxclass${He}`]=((sa=k.SelectedTaxClass)==null?void 0:sa.TaxClass)||"")}),Qu=Object.values(C)}const Zu=(de=a==null?void 0:a.payloadData)!=null&&de.Costing?Object.entries(a.payloadData.Costing):[],vT=((We=a==null?void 0:a.headerData)==null?void 0:We.orgData)||[],eh=new Set,qT=vT.filter(C=>{var Ie,He;if(!((He=(Ie=C.plant)==null?void 0:Ie.value)!=null&&He.code))return!1;const k=C.plant.value.code;return eh.has(k)?!1:(eh.add(k),!0)}).map(C=>{var ea,sa,Oa,Se,ja;const k=(sa=(ea=C.plant)==null?void 0:ea.value)==null?void 0:sa.code,Ie=((Oa=yT.find(([_])=>_===k))==null?void 0:Oa[1])||{},He=((Se=Zu.find(([_])=>_===k))==null?void 0:Se[1])||{};return{...Ie,AccountingId:Ie.AccountingId||He.AccountingId||null,Function:"INS",Material:((ja=a.headerData)==null?void 0:ja.materialNumber)||"",DelFlag:"",PriceCtrl:Ie.PriceCtrl||"",MovingPr:Ie.MovingPr||He.MovingPr||"",StdPrice:Ie.StdPrice||He.StdPrice||"",PriceUnit:Ie.PriceUnit||"",ValClass:Ie.ValClass||"",OrigMat:He.OrigMat===!0||He.OrigMat==="X"||He.OrigMat==="TRUE"?"X":"",ValArea:k||""}}),ST=(ha=a==null?void 0:a.payloadData)!=null&&ha.Warehouse?Object.entries(a.payloadData.Warehouse):[],PT=(se=(Ta=a.headerData)==null?void 0:Ta.views)!=null&&se.includes((aa=f)==null?void 0:aa.WAREHOUSE)?ST.map(([C,k],Ie)=>{var He;return{WarehouseId:k.WarehouseId||"",Function:k.Function||"",Material:((He=a.headerData)==null?void 0:He.materialNumber)||"",DelFlag:k.DelFlag||!0,WhseNo:C||"",SpecMvmt:k.SpecMvmt||"",LEquip1:k.LEquip1||"",LeqUnit1:k.LeqUnit1||"",Unittype1:k.Unittype1||"",Placement:k.Placement||""}}):[],qi=(ll=a==null?void 0:a.payloadData)!=null&&ll[(fa=f)==null?void 0:fa.STORAGE]?Object.values((Rl=a.payloadData)==null?void 0:Rl[(hl=f)==null?void 0:hl.STORAGE]):[],ah=new Set,LT=hi.filter(C=>{var Ie,He,ea,sa,Oa,Se,ja,_;if(!((He=(Ie=C.plant)==null?void 0:Ie.value)!=null&&He.code)||!((sa=(ea=C.sloc)==null?void 0:ea.value)!=null&&sa.code))return!1;const k=`${(Se=(Oa=C.plant)==null?void 0:Oa.value)==null?void 0:Se.code}-${(_=(ja=C.sloc)==null?void 0:ja.value)==null?void 0:_.code}`;return ah.has(k)?!1:(ah.add(k),!0)}).map((C,k)=>{var Ie,He,ea,sa,Oa,Se,ja,_;return{StorageLocationId:((Ie=qi[k])==null?void 0:Ie.StorageLocationId)||"",Plant:((ea=(He=C.plant)==null?void 0:He.value)==null?void 0:ea.code)||"",StgeLoc:((Oa=(sa=C.sloc)==null?void 0:sa.value)==null?void 0:Oa.code)||"",Material:((Se=a.headerData)==null?void 0:Se.materialNumber)||"",PickgArea:((ja=qi[k])==null?void 0:ja.PickgArea)||"",StgeBin:((_=qi[k])==null?void 0:_.StgeBin)||""}}),gl=(Aa=(El=a.headerData)==null?void 0:El.views)!=null&&Aa.includes(f.CLASSIFICATION)?(wa=a==null?void 0:a.payloadData)==null?void 0:wa[f.CLASSIFICATION]:{};let lh=[];gl&&gl.basic&&gl.basic.Classtype&&gl.basic.Classnum&&Array.isArray(gl.classification)&&lh.push({ClassificationId:(gl==null?void 0:gl.ClassificationId)||"",Classnum:gl.basic.Classnum,Classtype:gl.basic.Classtype,Object:((Ia=a.headerData)==null?void 0:Ia.materialNumber)||"",Tochars:(ve=gl.classification)==null?void 0:ve.map(C=>({CharacteristicsId:(C==null?void 0:C.CharacteristicsId)||"",Charact:C.characteristic,CharactDescr:C.description,Tocharvalues:Array.isArray(C==null?void 0:C.value)&&(C==null?void 0:C.value.length)>0?C==null?void 0:C.value.map(k=>({CharValueId:"",ValueChar:k})):[]})),Tovariants:((Da=a.headerData)==null?void 0:Da.TovariantData)&&((ia=a.headerData)==null?void 0:ia.TovariantData.filter(C=>C==null?void 0:C.item).map(C=>({VariantId:"",CharNumber:C.id,Tovariantvalues:gl.classification.map(k=>{if(k.characteristic.toLowerCase()==="size")return{VariantValueId:"",VarValue:C==null?void 0:C.size};if(k.characteristic.toLowerCase()==="color"||k.characteristic.toLowerCase()==="colour")return{VariantValueId:"",VarValue:C==null?void 0:C.color}})})))});const BT=(sl=a==null?void 0:a.payloadData)!=null&&sl[f.WORKSCHEDULING]?Object.entries((ne=a.payloadData)==null?void 0:ne[f.WORKSCHEDULING]):[],UT=(E=a==null?void 0:a.payloadData)!=null&&E[f.BOM]?Object.entries((qe=a.payloadData)==null?void 0:qe[f.BOM]):[],GT=(ra=a==null?void 0:a.payloadData)!=null&&ra[f.SOURCE_LIST]?Object.entries((Ca=a.payloadData)==null?void 0:Ca[f.SOURCE_LIST]):[],xT=(((Ye=a==null?void 0:a.headerData)==null?void 0:Ye.orgData)||[]).filter((C,k,Ie)=>k===(Ie==null?void 0:Ie.findIndex(He=>{var ea,sa,Oa,Se;return((sa=(ea=He.plant)==null?void 0:ea.value)==null?void 0:sa.code)===((Se=(Oa=C==null?void 0:C.plant)==null?void 0:Oa.value)==null?void 0:Se.code)}))),HT=(xa=a==null?void 0:a.payloadData)!=null&&xa[f.LOGISTIC_DC]?Object.entries((Ze=a.payloadData)==null?void 0:Ze[f.LOGISTIC_DC]):[],mT=(oa=a==null?void 0:a.payloadData)!=null&&oa[f.PURCHASING_DATA]?Object.entries((ol=a.payloadData)==null?void 0:ol[f.PURCHASING_DATA]):[],FT=xT.map((C,k)=>{var bd,Od,yd,vd,qd,Sd,Pd,Ld,Bd,Ud,Gd,xd,rh,th,nh,dh,ih,ch,uh,hh,Th,fh,Ch,gh,ph,Rh,Eh,Ah,Ih,Dh,_h,Nh,Mh,bh,Oh,yh,vh,qh,Sh;const Ie=(Od=(bd=C.plant)==null?void 0:bd.value)==null?void 0:Od.code,He=(yd=C.mrpProfile)==null?void 0:yd.code,ea=(qd=(vd=C==null?void 0:C.purchasingOrg)==null?void 0:vd.value)==null?void 0:qd.code,sa=(Pd=(Sd=C==null?void 0:C.supplier)==null?void 0:Sd.value)==null?void 0:Pd.code,Oa=(Bd=(Ld=C==null?void 0:C.distributionCenter)==null?void 0:Ld.value)==null?void 0:Bd.code,Se=((Ud=NT.find(([ya])=>ya===Ie))==null?void 0:Ud[1])||{},ja=((Gd=Zu.find(([ya])=>ya===Ie))==null?void 0:Gd[1])||{},_=((xd=MT.find(([ya])=>ya.startsWith(Ie)))==null?void 0:xd[1])||{},ka=((rh=bT.find(([ya])=>ya===Ie))==null?void 0:rh[1])||{},Ne=((th=OT.find(([ya])=>ya===Ie))==null?void 0:th[1])||{},Md=((nh=BT.find(([ya])=>ya===Ie))==null?void 0:nh[1])||{},$e=((dh=UT.find(([ya])=>ya===Ie))==null?void 0:dh[1])||{},Pe=((ih=GT.find(([ya])=>ya===Ie))==null?void 0:ih[1])||{},fi=((ch=HT.find(([ya])=>ya===Oa))==null?void 0:ch[1])||{},Ci=((uh=mT.find(([ya])=>ya===`${ea}-${Ie}-${sa}`))==null?void 0:uh[1])||{};return{PlantId:O===h.EXTEND&&r?null:((fh=(Th=(hh=a.payloadData)==null?void 0:hh.Purchasing)==null?void 0:Th[Ie])==null?void 0:fh.PlantId)??null,Function:"INS",Material:((Ch=a.headerData)==null?void 0:Ch.materialNumber)||"",Plant:Ie||"",Json285:ea||"",Json286:Oa||"",Json298:sa||"",DelFlag:!1,CritPart:!1,PurGroup:(Se==null?void 0:Se.PurGroup)||"",PurStatus:(Se==null?void 0:Se.PurStatus)||"",RoundProf:(_==null?void 0:_.RoundProf)||"",IssueUnitIso:"",Mrpprofile:He||"",MrpType:(_==null?void 0:_.MrpType)||"",MrpCtrler:(_==null?void 0:_.MrpCtrler)||"",PlndDelry:(_==null?void 0:_.PlndDelry)||"",GrPrTime:(_==null?void 0:_.GrPrTime)||"",PeriodInd:(_==null?void 0:_.PeriodInd)||"",Lotsizekey:(_==null?void 0:_.Lotsizekey)||"",ProcType:(_==null?void 0:_.ProcType)||"",Consummode:(_==null?void 0:_.Consummode)||"",FwdCons:(_==null?void 0:_.FwdCons)||"",ReorderPt:(_==null?void 0:_.ReorderPt)||"",MaxStock:(_==null?void 0:_.MaxStock)||"",SafetyStk:(_==null?void 0:_.SafetyStk)||"",Minlotsize:(_==null?void 0:_.Minlotsize)||"",PlanStrgp:(_==null?void 0:_.PlanStrgp)||"",BwdCons:(_==null?void 0:_.BwdCons)||"",Maxlotsize:(_==null?void 0:_.Maxlotsize)||"",FixedLot:(_==null?void 0:_.FixedLot)||"",RoundVal:(_==null?void 0:_.RoundVal)||"",GrpReqmts:(_==null?void 0:_.GrpReqmts)||"",MixedMrp:(_==null?void 0:_.MixedMrp)||"",SmKey:(_==null?void 0:_.SmKey)||"",Backflush:(_==null?void 0:_.Backflush)||"",AssyScarp:(_==null?void 0:_.AssyScarp)||"",Replentime:(_==null?void 0:_.Replentime)||"",PlTiFnce:(_==null?void 0:_.PlTiFnce)||"",ReplacePt:"",IndPostToInspStock:(Se==null?void 0:Se.IndPostToInspStock)===!0||(Se==null?void 0:Se.IndPostToInspStock)==="X"||(Se==null?void 0:Se.IndPostToInspStock)==="TRUE"?"X":"",HtsCode:(Se==null?void 0:Se.HtsCode)||"",CtrlKey:"",DepReqId:(_==null?void 0:_.DepReqId)||"",SaftyTId:(_==null?void 0:_.SaftyTId)||"",Safetytime:(_==null?void 0:_.Safetytime)||"",Matfrgtgrp:(ka==null?void 0:ka.Matfrgtgrp)||"",Availcheck:(ka==null?void 0:ka.Availcheck)||"",ProfitCtr:(ka==null?void 0:ka.ProfitCtr)||"",Loadinggrp:(ka==null?void 0:ka.Loadinggrp)||"",MinLotSize:(_==null?void 0:_.MinLotSize)||"",MaxLotSize:(_==null?void 0:_.MaxLotSize)||"",FixLotSize:(_==null?void 0:_.FixLotSize)||"",AssyScrap:(_==null?void 0:_.AssyScrap)||"",IssStLoc:(_==null?void 0:_.IssStLoc)||"",SalesView:((ph=a==null?void 0:a.headerData)==null?void 0:ph.views.includes((gh=f)==null?void 0:gh.SALES))||!1,PurchView:((Eh=a==null?void 0:a.headerData)==null?void 0:Eh.views.includes((Rh=f)==null?void 0:Rh.PURCHASING))||!1,MrpView:((Ih=a==null?void 0:a.headerData)==null?void 0:Ih.views.includes((Ah=f)==null?void 0:Ah.MRP))||!1,WorkSchedView:((_h=a==null?void 0:a.headerData)==null?void 0:_h.views.includes((Dh=f)==null?void 0:Dh.WORK_SCHEDULING_2))||!1,WarehouseView:((Mh=a==null?void 0:a.headerData)==null?void 0:Mh.views.includes((Nh=f)==null?void 0:Nh.WAREHOUSE))||!1,AccountView:((Oh=a==null?void 0:a.headerData)==null?void 0:Oh.views.includes((bh=f)==null?void 0:bh.ACCOUNTING))||!1,CostView:((vh=a==null?void 0:a.headerData)==null?void 0:vh.views.includes((yh=f)==null?void 0:yh.COSTING))||!1,ForecastView:!1,PrtView:!1,StorageView:((Sh=a==null?void 0:a.headerData)==null?void 0:Sh.views.includes((qh=f)==null?void 0:qh.STORAGE))||!1,QualityView:!1,GrProcTime:"",GiProcTime:"",StorageCost:"",LotSizeUom:"",LotSizeUomIso:"",Unlimited:Md.Unlimited||"",ProdProf:Md.ProdProf||"",VarianceKey:ja.VarianceKey||"",PoUnit:"",Spproctype:_.Spproctype||"",CommCode:(Se==null?void 0:Se.CommCode)||"",CommCoUn:(Se==null?void 0:Se.CommCoUn)||"",Countryori:Se==null?void 0:Se.Countryori,LotSize:ja.LotSize||"",SlocExprc:_.SlocExprc||"",Inhseprodt:ja.Inhseprodt||"",BomUsage:($e==null?void 0:$e.BomUsage)||"",AltBom:($e==null?void 0:$e.AltBom)||"",Category:($e==null?void 0:$e.Category)||"",Component:($e==null?void 0:$e.Component)||"",Quantity:($e==null?void 0:$e.Quantity)||"",CompUom:($e==null?void 0:$e.CompUom)||"",Bvalidfrom:$e!=null&&$e.Bvalidfrom?ie($e==null?void 0:$e.Bvalidfrom):ie(new Date().toISOString()),Bvalidto:$e!=null&&$e.Bvalidto?ie($e==null?void 0:$e.Bvalidto):ie(new Date().toISOString()),Supplier:(Pe==null?void 0:Pe.Supplier)||"",PurchaseOrg:(Pe==null?void 0:Pe.PurchaseOrg)||"",ProcurementPlant:(Pe==null?void 0:Pe.ProcurementPlant)||"",SOrderUnit:(Pe==null?void 0:Pe.SOrderUnit)||"",Agreement:(Pe==null?void 0:Pe.Agreement)||"",AgreementItem:(Pe==null?void 0:Pe.AgreementItem)||"",FixedSupplySource:(Pe==null?void 0:Pe.FixedSupplySource)||"",Blocked:(Pe==null?void 0:Pe.Blocked)||"",SMrp:(Pe==null?void 0:Pe.SMrp)||"",Slvalidfrom:Pe!=null&&Pe.Slvalidfrom?ie(Pe==null?void 0:Pe.Slvalidfrom):ie(new Date().toISOString()),Slvalidto:Pe!=null&&Pe.Slvalidto?ie(Pe==null?void 0:Pe.Slvalidto):ie(new Date().toISOString()),CcPhInv:(Ne==null?void 0:Ne.CcPhInv)||"",CcFixed:(Ne==null?void 0:Ne.CcFixed)||"",StgePdUn:(Ne==null?void 0:Ne.StgePdUn)||"",DefaultStockSegment:(Ne==null?void 0:Ne.DefaultStockSegment)||"",NegStocks:(Ne==null?void 0:Ne.NegStocks)||"",SernoProf:(Ne==null?void 0:Ne.SernoProf)||"",DistrProf:(Ne==null?void 0:Ne.DistrProf)||"",DetermGrp:(Ne==null?void 0:Ne.DetermGrp)||"",IuidRelevant:(Ne==null?void 0:Ne.IuidRelevant)||"",UidIea:(Ne==null?void 0:Ne.UidIea)||"",IuidType:(Ne==null?void 0:Ne.IuidType)||"",SortStockBasedOnSegment:(Ne==null?void 0:Ne.SortStockBasedOnSegment)||"",SegmentationStrategy:(Ne==null?void 0:Ne.SegmentationStrategy)||"",IssueUnit:(Ne==null?void 0:Ne.IssueUnit)||"",BatchMgmt:(Ne==null?void 0:Ne.BatchMgmt)||!1,...fi,...Ci,...Ti}}),io=(a==null?void 0:a.additionalData)||[],co=(a==null?void 0:a.unitsOfMeasureData)||[],uo=(a==null?void 0:a.eanData)||[],wT=io!=null&&io.length?io==null?void 0:io.map(C=>{var k;return{MaterialDescriptionId:O===h.EXTEND&&r?null:C.MaterialDescriptionId||null,Function:"INS",Material:((k=a.headerData)==null?void 0:k.materialNumber)||"",Langu:C.language||"EN",LanguIso:"",MatlDesc:(C==null?void 0:C.materialDescription)||"",DelFlag:!1}}):[{MaterialDescriptionId:null,Function:"INS",Material:(($a=a.headerData)==null?void 0:$a.materialNumber)||"",Langu:"EN",LanguIso:"",MatlDesc:((Ma=a.headerData)==null?void 0:Ma.globalMaterialDescription)||"",DelFlag:!1}],WT=co!=null&&co.length?co==null?void 0:co.map(C=>{var k;return{UomId:O===h.EXTEND&&r?null:(C==null?void 0:C.UomId)||null,Function:"INS",Material:((k=a.headerData)==null?void 0:k.materialNumber)||"",AltUnit:(C==null?void 0:C.aUnit)||"",AltUnitIso:"",Numerator:(C==null?void 0:C.yValue)||"1",Denominatr:(C==null?void 0:C.xValue)||"1",EanUpc:(C==null?void 0:C.eanUpc)||"",EanCat:C.eanCategory||"",Length:C.length,NetWeight:C.netWeight||"",Width:C.width,Height:C.height,UnitDim:C.unitsOfDimension||"",UnitDimIso:"",Volume:C.volume||"",Volumeunit:C.volumeUnit||"",VolumeunitIso:"",GrossWt:C.grossWeight||"",UnitOfWt:C.weightUnit||"",UnitOfWtIso:"",DelFlag:!1,SubUom:"",SubUomIso:"",GtinVariant:"",MaterialExternal:null,MaterialGuid:null,MaterialVersion:null,NestingFactor:"",MaximumStacking:null,CapacityUsage:C.capacityUsage,EwmCwUomType:"",MaterialLong:null}}):[{UomId:null,Function:"INS",Material:((rl=a.headerData)==null?void 0:rl.materialNumber)||"",AltUnit:((Tl=(tl=(za=a==null?void 0:a.payloadData)==null?void 0:za["Basic Data"])==null?void 0:tl.basic)==null?void 0:Tl.BaseUom)||"",AltUnitIso:"",Numerator:"1",Denominatr:"1",EanUpc:"",EanCat:"",Length:"",Width:"",Height:"",UnitDim:"",UnitDimIso:"",Volume:((Ya=(_a=(ke=a==null?void 0:a.payloadData)==null?void 0:ke["Basic Data"])==null?void 0:_a.basic)==null?void 0:Ya.Volume)||"",Volumeunit:((nl=(as=(ta=a==null?void 0:a.payloadData)==null?void 0:ta["Basic Data"])==null?void 0:as.basic)==null?void 0:nl.VolumeUnit)||"",VolumeunitIso:"",GrossWt:"",UnitOfWt:((fl=(dl=(ls=a==null?void 0:a.payloadData)==null?void 0:ls["Basic Data"])==null?void 0:dl.basic)==null?void 0:fl.UnitOfWt)||"",UnitOfWtIso:"",DelFlag:!1,SubUom:"",SubUomIso:"",GtinVariant:"",MaterialExternal:null,MaterialGuid:null,MaterialVersion:null,NestingFactor:"",MaximumStacking:null,CapacityUsage:"",EwmCwUomType:"",MaterialLong:null}],jT=uo!=null&&uo.length?uo==null?void 0:uo.map(C=>{var k;return{EanId:O===h.EXTEND&&r?null:(C==null?void 0:C.EanId)||null,Function:"INS",Material:((k=a.headerData)==null?void 0:k.materialNumber)||"",Unit:(C==null?void 0:C.altunit)||"",EanUpc:(C==null?void 0:C.eanUpc)||"",EanCat:(C==null?void 0:C.eanCategory)||"",MainEan:C.MainEan||!1,Au:C.au||!1}}):null,sh=new Set;(Al=a==null?void 0:a.payloadData)!=null&&Al.Tostroragelocationdata?(ss=a==null?void 0:a.payloadData)==null||ss.Tostroragelocationdata:(Dl=(xe=a.headerData)==null?void 0:xe.views)!=null&&Dl.includes((Il=f)==null?void 0:Il.STORAGE)&&hi.filter(C=>{var Ie,He,ea,sa;if(!((He=(Ie=C==null?void 0:C.plant)==null?void 0:Ie.value)!=null&&He.code)||!((sa=(ea=C==null?void 0:C.sloc)==null?void 0:ea.value)!=null&&sa.code))return!1;const k=`${C.plant.value.code}-${C.sloc.value.code}`;return sh.has(k)?!1:(sh.add(k),!0)}).map(C=>{var k,Ie,He,ea,sa;return{Function:"INS",Material:((k=a==null?void 0:a.headerData)==null?void 0:k.materialNumber)||"",Plant:((He=(Ie=C==null?void 0:C.plant)==null?void 0:Ie.value)==null?void 0:He.code)||"",StgeLoc:((sa=(ea=C==null?void 0:C.sloc)==null?void 0:ea.value)==null?void 0:sa.code)||""}});const kT={ChildRequestId:((Ha=a==null?void 0:a.Tochildrequestheaderdata)==null?void 0:Ha.ChildRequestId)||null,MaterialGroupType:((Cl=a==null?void 0:a.Tochildrequestheaderdata)==null?void 0:Cl.MaterialGroupType)||null,TaskId:(l==null?void 0:l.taskId)||null,Comments:R||o,TotalIntermediateTasks:((kl=a==null?void 0:a.Tochildrequestheaderdata)==null?void 0:kl.TotalIntermediateTasks)||null,IntermediateTaskCount:((Ja=a==null?void 0:a.Tochildrequestheaderdata)==null?void 0:Ja.IntermediateTaskCount)||null,ReqCreatedBy:((os=a==null?void 0:a.Tochildrequestheaderdata)==null?void 0:os.ReqCreatedBy)||null,ReqCreatedOn:((_l=a==null?void 0:a.Tochildrequestheaderdata)==null?void 0:_l.ReqCreatedOn)||null,ReqUpdatedOn:((Xa=a==null?void 0:a.Tochildrequestheaderdata)==null?void 0:Xa.ReqUpdatedOn)||null,RequestType:((rs=a==null?void 0:a.Tochildrequestheaderdata)==null?void 0:rs.RequestType)||null,RequestPrefix:(($l=a==null?void 0:a.Tochildrequestheaderdata)==null?void 0:$l.RequestPrefix)||null,RequestDesc:((ga=a==null?void 0:a.Tochildrequestheaderdata)==null?void 0:ga.RequestDesc)||null,RequestPriority:((Nl=a==null?void 0:a.Tochildrequestheaderdata)==null?void 0:Nl.RequestPriority)||null,RequestStatus:((Ml=a==null?void 0:a.Tochildrequestheaderdata)==null?void 0:Ml.RequestStatus)||null,CurrentLevel:(l==null?void 0:l.ATTRIBUTE_3)||"",CurrentLevelName:(l==null?void 0:l.ATTRIBUTE_4)||"",ParticularLevel:p,TaskName:(l==null?void 0:l.taskDesc)||"",ApproverGroup:(l==null?void 0:l.ATTRIBUTE_5)||""},Si={};Object.entries(((Yl=(zl=a==null?void 0:a.payloadData)==null?void 0:zl[f.SUPPLIER_FORM])==null?void 0:Yl.basic)||{}).forEach(([C,k])=>{C==="Json48"?Si[C]=k.join(", "):Si[C]=k||""});let oh={};(Jl=(ts=a.headerData)==null?void 0:ts.views)!=null&&Jl.includes((ns=f)==null?void 0:ns.CHARACTERISTIC)&&Object.entries(((Vl=(Xl=a==null?void 0:a.payloadData)==null?void 0:Xl[f.CHARACTERISTIC])==null?void 0:Vl.basic)||{}).forEach(([C,k])=>{oh[C]=k||""});const $T={MaterialId:O===h.EXTEND&&r||O===h.CREATE&&r?null:(l!=null&&l.taskId||d||ee!=null&&ee.ATTRIBUTE_5)&&!c.includes("-")?Number(c):null,ApparelMCat:((bl=(il=a==null?void 0:a.headerData)==null?void 0:il.apparelMcat)==null?void 0:bl.code)||"",Flag:"",Function:"INS",Material:((Kl=a==null?void 0:a.headerData)==null?void 0:Kl.materialNumber)||"",MatlType:((S=(Ol=a==null?void 0:a.headerData)==null?void 0:Ol.materialType)==null?void 0:S.code)||((Ge=a==null?void 0:a.headerData)==null?void 0:Ge.materialType)||"",IndSector:((pa=(Je=a==null?void 0:a.headerData)==null?void 0:Je.industrySector)==null?void 0:pa.code)||((Ra=a==null?void 0:a.headerData)==null?void 0:Ra.industrySector)||"",Comments:R||o,ViewNames:RT,Description:((qa=a==null?void 0:a.headerData)==null?void 0:qa.globalMaterialDescription)||"",ArticleCategory:(Wa=(Sa=a==null?void 0:a.headerData)==null?void 0:Sa.articleCategory)!=null&&Wa.code?(Na=(Pa=a==null?void 0:a.headerData)==null?void 0:Pa.articleCategory)==null?void 0:Na.code:((cl=a==null?void 0:a.headerData)==null?void 0:cl.articleCategory)||"",Toarticlecomponentsdata:(ul=a==null?void 0:a.headerData)!=null&&ul.articleComponents?(Va=a==null?void 0:a.headerData)==null?void 0:Va.articleComponents:{},Uom:(yl=(Ka=a==null?void 0:a.headerData)==null?void 0:Ka.Uom)!=null&&yl.code?a.headerData.Uom.code:((vl=a==null?void 0:a.headerData)==null?void 0:vl.Uom)||"",Category:(Sl=(ql=a==null?void 0:a.headerData)==null?void 0:ql.Category)!=null&&Sl.code?a.headerData.Category.code:((Pl=a==null?void 0:a.headerData)==null?void 0:Pl.Category)||"",Relation:(ps=(gs=a==null?void 0:a.headerData)==null?void 0:gs.Relation)!=null&&ps.code?a.headerData.Relation.code:((Rs=a==null?void 0:a.headerData)==null?void 0:Rs.Relation)||"",Usage:((Es=a==null?void 0:a.headerData)==null?void 0:Es.Usage)||"",CreationDate:l!=null&&l.requestId||d||(As=n==null?void 0:n.payloadData)!=null&&As.RequestId?ie((Is=n==null?void 0:n.payloadData)==null?void 0:Is.ReqCreatedOn):`/Date(${Date.now()}+0000)/`,EditId:null,ExtendId:null,MassCreationId:O===h.CREATE||O===h.CREATE_WITH_UPLOAD?l!=null&&l.requestId||l!=null&&l.ATTRIBUTE_1||d||(Ds=n==null?void 0:n.payloadData)!=null&&Ds.RequestId?(_s=n==null?void 0:n.payloadData)==null?void 0:_s.RequestId:"":null,MassEditId:((Ns=a==null?void 0:a.payloadData)==null?void 0:Ns.MassEditId)||"",MassExtendId:O===h.EXTEND||O===h.EXTEND_WITH_UPLOAD?l!=null&&l.requestId||l!=null&&l.ATTRIBUTE_1||d||(Ms=n==null?void 0:n.payloadData)!=null&&Ms.RequestId?(bs=n==null?void 0:n.payloadData)==null?void 0:bs.RequestId:(Os=u==null?void 0:u.requestHeader)!=null&&Os.requestId?(ys=u==null?void 0:u.requestHeader)==null?void 0:ys.requestId:Oe:null,TaskId:(l==null?void 0:l.taskId)||null,TaskName:(l==null?void 0:l.taskDesc)||null,TotalIntermediateTasks:l!=null&&l.requestId||l!=null&&l.ATTRIBUTE_1||d||(vs=n==null?void 0:n.payloadData)!=null&&vs.RequestId?(qs=n==null?void 0:n.payloadData)==null?void 0:qs.TotalIntermediateTasks:"",IntermediateTaskCount:l!=null&&l.requestId||l!=null&&l.ATTRIBUTE_1||d||(Ss=n==null?void 0:n.payloadData)!=null&&Ss.RequestId?(Ps=n==null?void 0:n.payloadData)==null?void 0:Ps.IntermediateTaskCount:"",BasicView:((ge=a==null?void 0:a.headerData)==null?void 0:ge.views.includes((I=f)==null?void 0:I.BASIC_DATA))||!1,SalesView:((Me=a==null?void 0:a.headerData)==null?void 0:Me.views.includes((Ae=f)==null?void 0:Ae.SALES))||!1,MrpView:((oe=a==null?void 0:a.headerData)==null?void 0:oe.views.includes((Te=f)==null?void 0:Te.MRP))||!1,PurchaseView:((H=a==null?void 0:a.headerData)==null?void 0:H.views.includes((pe=f)==null?void 0:pe.PURCHASING))||!1,AccountView:((la=a==null?void 0:a.headerData)==null?void 0:la.views.includes((Fe=f)==null?void 0:Fe.ACCOUNTING))||!1,CostView:((Ue=a==null?void 0:a.headerData)==null?void 0:Ue.views.includes((_e=f)==null?void 0:_e.COSTING))||!1,WorkSchedView:((Qa=a==null?void 0:a.headerData)==null?void 0:Qa.views.includes((Ua=f)==null?void 0:Ua.WORK_SCHEDULING_2))||!1,WarehouseView:((Ll=a==null?void 0:a.headerData)==null?void 0:Ll.views.includes((Za=f)==null?void 0:Za.WAREHOUSE))||!1,ForecastView:!1,PrtView:!1,StorageView:((Ul=a==null?void 0:a.headerData)==null?void 0:Ul.views.includes((Bl=f)==null?void 0:Bl.STORAGE))||!1,QualityView:!1,IsFirstChangeLogCommit:!1,IsMarkedForDeletion:!1,IsFirstCreate:!(l!=null&&l.taskId),creationTime:l!=null&&l.createdOn?ie(l==null?void 0:l.createdOn):null,dueDate:l!=null&&l.criticalDeadline?ie(l==null?void 0:l.criticalDeadline):null,ManufacturerId:(a==null?void 0:a.ManufacturerID)||"",OrgData:hi||[],Toclientdata:{ClientId:((Gl=a==null?void 0:a.headerData)==null?void 0:Gl.clientId)||null,Function:"INS",Material:((xl=a==null?void 0:a.headerData)==null?void 0:xl.materialNumber)||"",DelFlag:!0,MatlGroup:((Fl=(ml=(Hl=a==null?void 0:a.payloadData)==null?void 0:Hl["Basic Data"])==null?void 0:ml.basic)==null?void 0:Fl.MatlGroup)||"",Extmatlgrp:((na=(Bs=(Ls=a==null?void 0:a.payloadData)==null?void 0:Ls["Basic Data"])==null?void 0:Bs.basic)==null?void 0:na.Extmatlgrp)||"",OldMatNo:((Co=(fo=(Ql=a==null?void 0:a.payloadData)==null?void 0:Ql["Basic Data"])==null?void 0:fo.basic)==null?void 0:Co.OldMatNo)||"",BaseUom:((Ro=(po=(go=a==null?void 0:a.payloadData)==null?void 0:go["Basic Data"])==null?void 0:po.basic)==null?void 0:Ro.BaseUom)||"",Document:(Io=(Ao=(Eo=a==null?void 0:a.payloadData)==null?void 0:Eo["Basic Data"])==null?void 0:Ao.basic)==null?void 0:Io.Document,DocType:(No=(_o=(Do=a==null?void 0:a.payloadData)==null?void 0:Do["Basic Data"])==null?void 0:_o.basic)==null?void 0:No.DocType,DocVers:(Oo=(bo=(Mo=a==null?void 0:a.payloadData)==null?void 0:Mo["Basic Data"])==null?void 0:bo.basic)==null?void 0:Oo.DocVers,ValClass:(qo=(vo=(yo=a==null?void 0:a.payloadData)==null?void 0:yo["Basic Data"])==null?void 0:vo.basic)==null?void 0:qo.ValClass,DocFormat:(Lo=(Po=(So=a==null?void 0:a.payloadData)==null?void 0:So["Basic Data"])==null?void 0:Po.basic)==null?void 0:Lo.DocFormat,DocChgNo:(Go=(Uo=(Bo=a==null?void 0:a.payloadData)==null?void 0:Bo["Basic Data"])==null?void 0:Uo.basic)==null?void 0:Go.DocChgNo,PageNo:(mo=(Ho=(xo=a==null?void 0:a.payloadData)==null?void 0:xo["Basic Data"])==null?void 0:Ho.basic)==null?void 0:mo.PageNo,Json302:(Wo=(wo=(Fo=a==null?void 0:a.payloadData)==null?void 0:Fo["Basic Data"])==null?void 0:wo.basic)==null?void 0:Wo.Json302,Json303:($o=(ko=(jo=a==null?void 0:a.payloadData)==null?void 0:jo["Basic Data"])==null?void 0:ko.basic)==null?void 0:$o.Json303,Json304:(Jo=(Yo=(zo=a==null?void 0:a.payloadData)==null?void 0:zo["Basic Data"])==null?void 0:Yo.basic)==null?void 0:Jo.Json304,Json305:(Ko=(Vo=(Xo=a==null?void 0:a.payloadData)==null?void 0:Xo["Basic Data"])==null?void 0:Vo.basic)==null?void 0:Ko.Json305,Json306:(er=(Zo=(Qo=a==null?void 0:a.payloadData)==null?void 0:Qo["Basic Data"])==null?void 0:Zo.basic)==null?void 0:er.Json306,Json307:(sr=(lr=(ar=a==null?void 0:a.payloadData)==null?void 0:ar["Basic Data"])==null?void 0:lr.basic)==null?void 0:sr.Json307,Json308:(tr=(rr=(or=a==null?void 0:a.payloadData)==null?void 0:or["Basic Data"])==null?void 0:rr.basic)==null?void 0:tr.Json308,Json309:(ir=(dr=(nr=a==null?void 0:a.payloadData)==null?void 0:nr["Basic Data"])==null?void 0:dr.basic)==null?void 0:ir.Json309,Json310:(hr=(ur=(cr=a==null?void 0:a.payloadData)==null?void 0:cr["Basic Data"])==null?void 0:ur.basic)==null?void 0:hr.Json310,Json311:(Cr=(fr=(Tr=a==null?void 0:a.payloadData)==null?void 0:Tr["Basic Data"])==null?void 0:fr.basic)==null?void 0:Cr.Json311,Json312:(Rr=(pr=(gr=a==null?void 0:a.payloadData)==null?void 0:gr["Basic Data"])==null?void 0:pr.basic)==null?void 0:Rr.Json312,Json313:(Ir=(Ar=(Er=a==null?void 0:a.payloadData)==null?void 0:Er["Basic Data"])==null?void 0:Ar.basic)==null?void 0:Ir.Json313,Json314:(Nr=(_r=(Dr=a==null?void 0:a.payloadData)==null?void 0:Dr["Basic Data"])==null?void 0:_r.basic)==null?void 0:Nr.Json314,Json315:(Or=(br=(Mr=a==null?void 0:a.payloadData)==null?void 0:Mr["Basic Data"])==null?void 0:br.basic)==null?void 0:Or.Json315,Json316:(qr=(vr=(yr=a==null?void 0:a.payloadData)==null?void 0:yr["Basic Data"])==null?void 0:vr.basic)==null?void 0:qr.Json316,Json317:(Lr=(Pr=(Sr=a==null?void 0:a.payloadData)==null?void 0:Sr["Basic Data"])==null?void 0:Pr.basic)==null?void 0:Lr.Json317,Json318:(Gr=(Ur=(Br=a==null?void 0:a.payloadData)==null?void 0:Br["Basic Data"])==null?void 0:Ur.basic)==null?void 0:Gr.Json318,Json319:(mr=(Hr=(xr=a==null?void 0:a.payloadData)==null?void 0:xr["Basic Data"])==null?void 0:Hr.basic)==null?void 0:mr.Json319,Json320:(Wr=(wr=(Fr=a==null?void 0:a.payloadData)==null?void 0:Fr["Basic Data"])==null?void 0:wr.basic)==null?void 0:Wr.Json320,Json321:($r=(kr=(jr=a==null?void 0:a.payloadData)==null?void 0:jr["Basic Data"])==null?void 0:kr.basic)==null?void 0:$r.Json321,Json322:(Jr=(Yr=(zr=a==null?void 0:a.payloadData)==null?void 0:zr["Basic Data"])==null?void 0:Yr.basic)==null?void 0:Jr.Json322,Json323:(Kr=(Vr=(Xr=a==null?void 0:a.payloadData)==null?void 0:Xr["Basic Data"])==null?void 0:Vr.basic)==null?void 0:Kr.Json323,Json324:(et=(Zr=(Qr=a==null?void 0:a.payloadData)==null?void 0:Qr["Basic Data"])==null?void 0:Zr.basic)==null?void 0:et.Json324,Json325:(st=(lt=(at=a==null?void 0:a.payloadData)==null?void 0:at["Basic Data"])==null?void 0:lt.basic)==null?void 0:st.Json325,Json326:(tt=(rt=(ot=a==null?void 0:a.payloadData)==null?void 0:ot["Basic Data"])==null?void 0:rt.basic)==null?void 0:tt.Json326,Json327:(it=(dt=(nt=a==null?void 0:a.payloadData)==null?void 0:nt["Basic Data"])==null?void 0:dt.basic)==null?void 0:it.Json327,Json328:(ht=(ut=(ct=a==null?void 0:a.payloadData)==null?void 0:ct["Basic Data"])==null?void 0:ut.basic)==null?void 0:ht.Json328,Json329:(Ct=(ft=(Tt=a==null?void 0:a.payloadData)==null?void 0:Tt["Basic Data"])==null?void 0:ft.basic)==null?void 0:Ct.Json329,Json330:(Rt=(pt=(gt=a==null?void 0:a.payloadData)==null?void 0:gt["Basic Data"])==null?void 0:pt.basic)==null?void 0:Rt.Json330,Json331:(It=(At=(Et=a==null?void 0:a.payloadData)==null?void 0:Et["Basic Data"])==null?void 0:At.basic)==null?void 0:It.Json331,Json332:(Nt=(_t=(Dt=a==null?void 0:a.payloadData)==null?void 0:Dt["Basic Data"])==null?void 0:_t.basic)==null?void 0:Nt.Json332,Json333:(Ot=(bt=(Mt=a==null?void 0:a.payloadData)==null?void 0:Mt["Basic Data"])==null?void 0:bt.basic)==null?void 0:Ot.Json333,Json334:(qt=(vt=(yt=a==null?void 0:a.payloadData)==null?void 0:yt["Basic Data"])==null?void 0:vt.basic)==null?void 0:qt.Json334,Json335:(Lt=(Pt=(St=a==null?void 0:a.payloadData)==null?void 0:St["Basic Data"])==null?void 0:Pt.basic)==null?void 0:Lt.Json335,Json336:(Gt=(Ut=(Bt=a==null?void 0:a.payloadData)==null?void 0:Bt["Basic Data"])==null?void 0:Ut.basic)==null?void 0:Gt.Json336,Json337:(mt=(Ht=(xt=a==null?void 0:a.payloadData)==null?void 0:xt["Basic Data"])==null?void 0:Ht.basic)==null?void 0:mt.Json337,Json338:(Wt=(wt=(Ft=a==null?void 0:a.payloadData)==null?void 0:Ft["Basic Data"])==null?void 0:wt.basic)==null?void 0:Wt.Json338,Json339:($t=(kt=(jt=a==null?void 0:a.payloadData)==null?void 0:jt["Basic Data"])==null?void 0:kt.basic)==null?void 0:$t.Json339,Json340:(Jt=(Yt=(zt=a==null?void 0:a.payloadData)==null?void 0:zt["Basic Data"])==null?void 0:Yt.basic)==null?void 0:Jt.Json340,Json341:(Kt=(Vt=(Xt=a==null?void 0:a.payloadData)==null?void 0:Xt["Basic Data"])==null?void 0:Vt.basic)==null?void 0:Kt.Json341,Json342:(en=(Zt=(Qt=a==null?void 0:a.payloadData)==null?void 0:Qt["Basic Data"])==null?void 0:Zt.basic)==null?void 0:en.Json342,Json343:(sn=(ln=(an=a==null?void 0:a.payloadData)==null?void 0:an["Basic Data"])==null?void 0:ln.basic)==null?void 0:sn.Json343,Json344:(tn=(rn=(on=a==null?void 0:a.payloadData)==null?void 0:on["Basic Data"])==null?void 0:rn.basic)==null?void 0:tn.Json344,Json345:(cn=(dn=(nn=a==null?void 0:a.payloadData)==null?void 0:nn["Basic Data"])==null?void 0:dn.basic)==null?void 0:cn.Json345,Json346:(Tn=(hn=(un=a==null?void 0:a.payloadData)==null?void 0:un["Basic Data"])==null?void 0:hn.basic)==null?void 0:Tn.Json346,Json347:(gn=(Cn=(fn=a==null?void 0:a.payloadData)==null?void 0:fn["Basic Data"])==null?void 0:Cn.basic)==null?void 0:gn.Json347,Json348:(En=(Rn=(pn=a==null?void 0:a.payloadData)==null?void 0:pn["Basic Data"])==null?void 0:Rn.basic)==null?void 0:En.Json348,Json349:(Dn=(In=(An=a==null?void 0:a.payloadData)==null?void 0:An["Basic Data"])==null?void 0:In.basic)==null?void 0:Dn.Json349,Json350:(Mn=(Nn=(_n=a==null?void 0:a.payloadData)==null?void 0:_n["Basic Data"])==null?void 0:Nn.basic)==null?void 0:Mn.Json350,NoSheets:(bn=a==null?void 0:a.payloadData)==null?void 0:bn.NoSheets,ProdMemo:(On=a==null?void 0:a.payloadData)==null?void 0:On.ProdMemo,Pageformat:(yn=a==null?void 0:a.payloadData)==null?void 0:yn.DocFormat,SizeDim:(vn=a==null?void 0:a.payloadData)==null?void 0:vn.SizeDim,BaseUomIso:"",BasicMatl:((Pn=(Sn=(qn=a==null?void 0:a.payloadData)==null?void 0:qn["Basic Data"])==null?void 0:Sn.basic)==null?void 0:Pn.BasicMatl)||"",StdDescr:(Ln=a==null?void 0:a.payloadData)==null?void 0:Ln.StdDescr,DsnOffice:((Gn=(Un=(Bn=a==null?void 0:a.payloadData)==null?void 0:Bn["Basic Data"])==null?void 0:Un.basic)==null?void 0:Gn.DsnOffice)||"",PurValkey:((mn=(Hn=(xn=a==null?void 0:a.payloadData)==null?void 0:xn["Purchasing-General"])==null?void 0:Hn["Purchasing-General"])==null?void 0:mn.PurValkey)||"",NetWeight:(Wn=(wn=(Fn=a==null?void 0:a.payloadData)==null?void 0:Fn["Basic Data"])==null?void 0:wn.basic)==null?void 0:Wn.NetWeight,UnitOfWt:(($n=(kn=(jn=a==null?void 0:a.payloadData)==null?void 0:jn["Basic Data"])==null?void 0:kn.basic)==null?void 0:$n.UnitOfWt)||"",TransGrp:(Jn=(Yn=(zn=a==null?void 0:a.payloadData)==null?void 0:zn["Basic Data"])==null?void 0:Yn.basic)!=null&&Jn.TransGrp?(Kn=(Vn=(Xn=a==null?void 0:a.payloadData)==null?void 0:Xn["Basic Data"])==null?void 0:Vn.basic)==null?void 0:Kn.TransGrp:(s=(Zn=(Qn=a==null?void 0:a.payloadData)==null?void 0:Qn["Sales-General"])==null?void 0:Zn["Sales-General"])==null?void 0:s.TransGrp,XSalStatus:(ua=($d=(Qs=a==null?void 0:a.payloadData)==null?void 0:Qs["Sales-General"])==null?void 0:$d["Sales-General"])==null?void 0:ua.XSalStatus,Svalidfrom:(Yd=(zd=(ed=a==null?void 0:a.payloadData)==null?void 0:ed["Sales-General"])==null?void 0:zd["Sales-General"])!=null&&Yd.Svalidfrom?ie((Vd=(Xd=(Jd=a==null?void 0:a.payloadData)==null?void 0:Jd["Sales-General"])==null?void 0:Xd["Sales-General"])==null?void 0:Vd.Svalidfrom):null,Division:(Kd=n==null?void 0:n.payloadData)!=null&&Kd.Division?(ad=n==null?void 0:n.payloadData)==null?void 0:ad.Division:((Qd=(ld=(Oi=a==null?void 0:a.payloadData)==null?void 0:Oi["Basic Data"])==null?void 0:ld.basic)==null?void 0:Qd.Division)||"",ProdHier:((ei=(Zd=(sd=a==null?void 0:a.payloadData)==null?void 0:sd["Basic Data"])==null?void 0:Zd.basic)==null?void 0:ei.ProdHier)||"",CadId:(od=(no=(ai=a==null?void 0:a.payloadData)==null?void 0:ai["Basic Data"])==null?void 0:no.basic)==null?void 0:od.CadId,VarOrdUn:(rd=(el=(li=a==null?void 0:a.payloadData)==null?void 0:li["Purchasing-General"])==null?void 0:el["Purchasing-General"])==null?void 0:rd.VarOrdUn,UnitOfWtIso:"",MatGrpSm:"",Authoritygroup:"",QmProcmnt:"",BatchMgmt:(ri=(oi=(si=a==null?void 0:a.payloadData)==null?void 0:si["Sales-General"])==null?void 0:oi["Sales-General"])==null?void 0:ri.BatchMgmt,SalStatus:"",Catprofile:"",ShelfLife:"",StorPct:"",Hazmatprof:((ti=(vi=(yi=a==null?void 0:a.payloadData)==null?void 0:yi["Basic Data"])==null?void 0:vi.basic)==null?void 0:ti.Hazmatprof)||"",HighVisc:(cs=(is=(ds=a==null?void 0:a.payloadData)==null?void 0:ds["Basic Data"])==null?void 0:is.basic)==null?void 0:cs.HighVisc,AppdBRec:"",Pvalidfrom:(ii=(di=(ni=a==null?void 0:a.payloadData)==null?void 0:ni["Basic Data"])==null?void 0:di.basic)!=null&&ii.Pvalidfrom?ie((ui=(ci=(td=a==null?void 0:a.payloadData)==null?void 0:td["Basic Data"])==null?void 0:ci.basic)==null?void 0:ui.Pvalidfrom):null,EnvtRlvt:"",ProdAlloc:(Re=(ae=(M=a==null?void 0:a.payloadData)==null?void 0:M["Basic Data"])==null?void 0:ae.basic)==null?void 0:Re.ProdAlloc,PeriodIndExpirationDate:"",ParEff:!0,Matcmpllvl:"",GItemCat:((Xe=(fe=(Le=a==null?void 0:a.payloadData)==null?void 0:Le["Basic Data"])==null?void 0:fe.basic)==null?void 0:Xe.GItemCat)||"",CSalStatus:((ce=(Ve=(A=a==null?void 0:a.payloadData)==null?void 0:A["Basic Data"])==null?void 0:Ve.basic)==null?void 0:ce.CSalStatus)||"",IntlPoPrice:((be=(we=(La=a==null?void 0:a.payloadData)==null?void 0:La["Basic Data"])==null?void 0:we.basic)==null?void 0:be.IntlPoPrice)||"",PryVendor:((xs=(Gs=(Us=a==null?void 0:a.payloadData)==null?void 0:Us["Basic Data"])==null?void 0:Gs.basic)==null?void 0:xs.PryVendor)||"",PlanningArea:((Fs=(ms=(Hs=a==null?void 0:a.payloadData)==null?void 0:Hs["Basic Data"])==null?void 0:ms.basic)==null?void 0:Fs.PlanningArea)||"",PlanningFactor:((js=(Ws=(ws=a==null?void 0:a.payloadData)==null?void 0:ws["Basic Data"])==null?void 0:Ws.basic)==null?void 0:js.PlanningFactor)||"",ReturnMatNumber:((zs=($s=(ks=a==null?void 0:a.payloadData)==null?void 0:ks["Basic Data"])==null?void 0:$s.basic)==null?void 0:zs.ReturnMatNumber)||"",ParentMatNumber:((dd=(nd=(Ys=a==null?void 0:a.payloadData)==null?void 0:Ys["Basic Data"])==null?void 0:nd.basic)==null?void 0:dd.ParentMatNumber)||"",DiversionControlFlag:((ud=(cd=(id=a==null?void 0:a.payloadData)==null?void 0:id["Basic Data"])==null?void 0:cd.basic)==null?void 0:ud.DiversionControlFlag)||"",MatGroupPackagingMat:((fd=(Td=(hd=a==null?void 0:a.payloadData)==null?void 0:hd["Basic Data"])==null?void 0:Td.basic)==null?void 0:fd.MatGroupPackagingMat)||"",HazMatNo:((pd=(gd=(Cd=a==null?void 0:a.payloadData)==null?void 0:Cd[f.STORAGE_GENERAL])==null?void 0:gd[f.STORAGE_GENERAL])==null?void 0:pd.HazMatNo)||"",QtyGrGi:((Ad=(Ed=(Rd=a==null?void 0:a.payloadData)==null?void 0:Rd[f.STORAGE_GENERAL])==null?void 0:Ed[f.STORAGE_GENERAL])==null?void 0:Ad.QtyGrGi)||"",TempConds:((_d=(Dd=(Id=a==null?void 0:a.payloadData)==null?void 0:Id[f.STORAGE_GENERAL])==null?void 0:Dd[f.STORAGE_GENERAL])==null?void 0:_d.TempConds)||"",Container:((Zi=(Ba=(Nd=a==null?void 0:a.payloadData)==null?void 0:Nd[f.STORAGE_GENERAL])==null?void 0:Ba[f.STORAGE_GENERAL])==null?void 0:Zi.Container)||"",LabelType:((lc=(ac=(ec=a==null?void 0:a.payloadData)==null?void 0:ec[f.STORAGE_GENERAL])==null?void 0:ac[f.STORAGE_GENERAL])==null?void 0:lc.LabelType)||"",LabelForm:((rc=(oc=(sc=a==null?void 0:a.payloadData)==null?void 0:sc[f.STORAGE_GENERAL])==null?void 0:oc[f.STORAGE_GENERAL])==null?void 0:rc.LabelForm)||"",AppdBRec:((dc=(nc=(tc=a==null?void 0:a.payloadData)==null?void 0:tc[f.STORAGE_GENERAL])==null?void 0:nc[f.STORAGE_GENERAL])==null?void 0:dc.AppdBRec)||"",Minremlife:((uc=(cc=(ic=a==null?void 0:a.payloadData)==null?void 0:ic[f.STORAGE_GENERAL])==null?void 0:cc[f.STORAGE_GENERAL])==null?void 0:uc.Minremlife)||"",ShelfLife:((fc=(Tc=(hc=a==null?void 0:a.payloadData)==null?void 0:hc[f.STORAGE_GENERAL])==null?void 0:Tc[f.STORAGE_GENERAL])==null?void 0:fc.ShelfLife)||"",PeriodIndExpirationDate:((pc=(gc=(Cc=a==null?void 0:a.payloadData)==null?void 0:Cc[f.STORAGE_GENERAL])==null?void 0:gc[f.STORAGE_GENERAL])==null?void 0:pc.PeriodIndExpirationDate)||"",RoundUpRuleExpirationDate:((Ac=(Ec=(Rc=a==null?void 0:a.payloadData)==null?void 0:Rc[f.STORAGE_GENERAL])==null?void 0:Ec[f.STORAGE_GENERAL])==null?void 0:Ac.RoundUpRuleExpirationDate)||"",StorPct:((_c=(Dc=(Ic=a==null?void 0:a.payloadData)==null?void 0:Ic[f.STORAGE_GENERAL])==null?void 0:Dc[f.STORAGE_GENERAL])==null?void 0:_c.StorPct)||"",SledBbd:((bc=(Mc=(Nc=a==null?void 0:a.payloadData)==null?void 0:Nc[f.STORAGE_GENERAL])==null?void 0:Mc[f.STORAGE_GENERAL])==null?void 0:bc.SledBbd)||"",SerializationLevel:((vc=(yc=(Oc=a==null?void 0:a.payloadData)==null?void 0:Oc[f.STORAGE_GENERAL])==null?void 0:yc[f.STORAGE_GENERAL])==null?void 0:vc.SerializationLevel)||"",ShelfLifeReqMax:((Pc=(Sc=(qc=a==null?void 0:a.payloadData)==null?void 0:qc[f.STORAGE_GENERAL])==null?void 0:Sc[f.STORAGE_GENERAL])==null?void 0:Pc.ShelfLifeReqMax)||"",ShelfLifeReqMin:((Uc=(Bc=(Lc=a==null?void 0:a.payloadData)==null?void 0:Lc[f.STORAGE_GENERAL])==null?void 0:Bc[f.STORAGE_GENERAL])==null?void 0:Uc.ShelfLifeReqMin)||"",MaturityDur:((Hc=(xc=(Gc=a==null?void 0:a.payloadData)==null?void 0:Gc[f.STORAGE_GENERAL])==null?void 0:xc[f.STORAGE_GENERAL])==null?void 0:Hc.MaturityDur)||"",StorPct:((wc=(Fc=(mc=a==null?void 0:a.payloadData)==null?void 0:mc[f.STORAGE_GENERAL])==null?void 0:Fc[f.STORAGE_GENERAL])==null?void 0:wc.StorPct)||"",StorConds:((kc=(jc=(Wc=a==null?void 0:a.payloadData)==null?void 0:Wc[f.STORAGE_GENERAL])==null?void 0:jc[f.STORAGE_GENERAL])==null?void 0:kc.StorConds)||""},Togenericdata:Si||{},Tocharcreationdata:oh||{},Toplantdata:FT,Tosalesdata:($c=a==null?void 0:a.headerData)!=null&&$c.views.includes("Sales")||(zc=a==null?void 0:a.headerData)!=null&&zc.views.includes("Listing")?_T:[],Tomerchantdata:(Yc=a==null?void 0:a.headerData)!=null&&Yc.views.includes("Merchant Input")?Ti:{},Tomaterialdescription:wT,Touomdata:WT,Toeandata:jT,Tostroragelocationdata:LT,ToClassification:lh,Tomaterialerrordata:(a==null?void 0:a.Tomaterialerrordata)||{},Toaccountingdata:(Xc=a==null?void 0:a.headerData)!=null&&Xc.views.includes((Jc=f)==null?void 0:Jc.ACCOUNTING)?qT:[],Tocontroldata:(Vc=a==null?void 0:a.headerData)!=null&&Vc.views.includes("Sales")?Qu:[],Torequestheaderdata:{RequestId:l!=null&&l.requestId||l!=null&&l.ATTRIBUTE_1||d||(Kc=n==null?void 0:n.payloadData)!=null&&Kc.RequestId?(Qc=n==null?void 0:n.payloadData)==null?void 0:Qc.RequestId:(Zc=u==null?void 0:u.requestHeader)!=null&&Zc.requestId?(eu=u==null?void 0:u.requestHeader)==null?void 0:eu.requestId:Oe,ReqCreatedBy:l!=null&&l.requestId||l!=null&&l.ATTRIBUTE_1||d||(au=n==null?void 0:n.payloadData)!=null&&au.RequestId?(lu=n==null?void 0:n.payloadData)==null?void 0:lu.ReqCreatedBy:(su=u==null?void 0:u.requestHeader)==null?void 0:su.reqCreatedBy,ReqCreatedOn:ie(l!=null&&l.requestId||l!=null&&l.ATTRIBUTE_1||d||(ou=n==null?void 0:n.payloadData)!=null&&ou.RequestId?(ru=n==null?void 0:n.payloadData)==null?void 0:ru.ReqCreatedOn:(tu=u==null?void 0:u.requestHeader)==null?void 0:tu.reqCreatedOn),ReqUpdatedOn:ie(l!=null&&l.requestId||l!=null&&l.ATTRIBUTE_1||d||(nu=n==null?void 0:n.payloadData)!=null&&nu.RequestId?(du=n==null?void 0:n.payloadData)==null?void 0:du.ReqUpdatedOn:(iu=u==null?void 0:u.requestHeader)==null?void 0:iu.reqCreatedOn),RequestType:l!=null&&l.requestId||l!=null&&l.ATTRIBUTE_1||d||(cu=n==null?void 0:n.payloadData)!=null&&cu.RequestId?(uu=n==null?void 0:n.payloadData)==null?void 0:uu.RequestType:(hu=u==null?void 0:u.requestHeader)==null?void 0:hu.requestType,Division:l!=null&&l.requestId||l!=null&&l.ATTRIBUTE_1||d||(Tu=n==null?void 0:n.payloadData)!=null&&Tu.RequestId?(fu=n==null?void 0:n.payloadData)==null?void 0:fu.Division:(Cu=u==null?void 0:u.requestHeader)==null?void 0:Cu.Division,RequestPriority:l!=null&&l.requestId||l!=null&&l.ATTRIBUTE_1||d||(gu=n==null?void 0:n.payloadData)!=null&&gu.RequestId?(pu=n==null?void 0:n.payloadData)==null?void 0:pu.RequestPriority:(Ru=u==null?void 0:u.requestHeader)==null?void 0:Ru.requestPriority,RequestDesc:l!=null&&l.requestId||l!=null&&l.ATTRIBUTE_1||d||(Eu=n==null?void 0:n.payloadData)!=null&&Eu.RequestId?(Au=n==null?void 0:n.payloadData)==null?void 0:Au.RequestDesc:(Iu=u==null?void 0:u.requestHeader)==null?void 0:Iu.requestDesc,RequestStatus:l!=null&&l.requestId||l!=null&&l.ATTRIBUTE_1||d||(Du=n==null?void 0:n.payloadData)!=null&&Du.RequestId?(_u=n==null?void 0:n.payloadData)==null?void 0:_u.RequestStatus:(Nu=u==null?void 0:u.requestHeader)==null?void 0:Nu.requestStatus,FirstProd:l!=null&&l.requestId||l!=null&&l.ATTRIBUTE_1||d||(Mu=n==null?void 0:n.payloadData)!=null&&Mu.RequestId?(bu=n==null?void 0:n.payloadData)==null?void 0:bu.FirstProd:((Ou=e==null?void 0:e.payloadData)==null?void 0:Ou.FirstProductionDate)||null,LaunchDate:l!=null&&l.requestId||l!=null&&l.ATTRIBUTE_1||d||(yu=n==null?void 0:n.payloadData)!=null&&yu.RequestId?(vu=n==null?void 0:n.payloadData)==null?void 0:vu.LaunchDate:((qu=e==null?void 0:e.payloadData)==null?void 0:qu.LaunchDate)||null,LeadingCat:l!=null&&l.requestId||l!=null&&l.ATTRIBUTE_1||d||(Su=n==null?void 0:n.payloadData)!=null&&Su.RequestId?(Pu=n==null?void 0:n.payloadData)==null?void 0:Pu.LeadingCat:(Lu=u==null?void 0:u.requestHeader)==null?void 0:Lu.leadingCat,Region:l!=null&&l.requestId||l!=null&&l.ATTRIBUTE_1||d||(Bu=n==null?void 0:n.payloadData)!=null&&Bu.RequestId?(Uu=n==null?void 0:n.payloadData)==null?void 0:Uu.Region:(Gu=u==null?void 0:u.requestHeader)==null?void 0:Gu.region,Json301:l!=null&&l.requestId||l!=null&&l.ATTRIBUTE_1||d||(xu=n==null?void 0:n.payloadData)!=null&&xu.RequestId?(Hu=n==null?void 0:n.payloadData)==null?void 0:Hu.Json301:(mu=u==null?void 0:u.requestHeader)==null?void 0:mu.Json301,IsBifurcated:!0},Tochildrequestheaderdata:l!=null&&l.requestId||l!=null&&l.ATTRIBUTE_1||d||(Fu=n==null?void 0:n.payloadData)!=null&&Fu.RequestId?kT:{},Towarehousedata:PT,changeLogData:m&&Object.keys(m).length>0&&(m[(wu=a.headerData)==null?void 0:wu.materialNumber]||m[(Wu=a.headerData)==null?void 0:Wu.id])?{RequestId:(m==null?void 0:m.RequestId)||((ju=a==null?void 0:a.changeLogData)==null?void 0:ju.RequestId),ChangeLogId:((ku=a==null?void 0:a.changeLogData)==null?void 0:ku.ChangeLogId)??null,ChildRequestId:((m==null?void 0:m.ChildRequestId)||(($u=a==null?void 0:a.changeLogData)==null?void 0:$u.ChildRequestId))??null,...m[(zu=a.headerData)==null?void 0:zu.materialNumber],...m[(Yu=a.headerData)==null?void 0:Yu.id]}:{RequestId:(Ju=a==null?void 0:a.changeLogData)==null?void 0:Ju.RequestId,ChangeLogId:((Xu=a==null?void 0:a.changeLogData)==null?void 0:Xu.ChangeLogId)??null,ChildRequestId:((Vu=a==null?void 0:a.changeLogData)==null?void 0:Vu.ChildRequestId)??null}};W.push($T)}}}),W}return{createPayloadFromReduxStateArticle:he}},rT=({module:r,requestId:d,requestType:R,templateName:o,payloadData:p})=>{const e=Vs(),O=new URLSearchParams(e.search),l=Y(i=>i.userManagement.taskData),u=O.get("reqBench"),x=!(l!=null&&l.taskId)&&!u,ee=Y(i=>i.bom.bomRows),m=Y(i=>i.bom.BOMpayloadData),Ce=Y(i=>i.bom.tabFieldValues),re=Y(i=>i.bom.requestHeaderID),Oe=Y(i=>i.request.requestHeader),he=Y(i=>i==null?void 0:i.userManagement.taskData),Q=Y(i=>i.generalLedger.payload.rowsHeaderData),n=Y(i=>{var y;return(y=i==null?void 0:i.generalLedger)==null?void 0:y.selecteddropdownDataForExtendedCode}),W=Y(i=>{var y;return(y=i==null?void 0:i.generalLedger)==null?void 0:y.selectedcopyFromCompanyCode}),c=Y(i=>{var y,V,v,z,K,Ee,X;switch(r){case((y=J)==null?void 0:y.CC):return i.costCenter.payload;case((V=J)==null?void 0:V.PC):return i.profitCenter.payload;case((v=J)==null?void 0:v.GL):return i.generalLedger.payload;case((z=J)==null?void 0:z.CCG):return i.hierarchyData;case((K=J)==null?void 0:K.PCG):return i.hierarchyData;case((Ee=J)==null?void 0:Ee.CEG):return i.hierarchyData;case((X=J)==null?void 0:X.IO):return i.internalOrder;default:return null}}),T=Y(i=>i.request.requestHeader),U=Y(i=>i.profitCenter.payload.requestHeaderData),g=Y(i=>i.generalLedger.payload.requestHeaderData),L=Y(i=>i.payload.dynamicKeyValues),{fetchedProfitCenterData:B,fetchReqBenchData:b}=Y(i=>i.profitCenter),{fetchedGeneralLedgerData:q}=Y(i=>i.generalLedger),P=Y(i=>i.generalLedger.fetchReqBenchData),{fetchedCostCenterData:G,fetchReqBenchDataCC:F}=Y(i=>i.costCenter),{createPayloadFromReduxState:w}=ki({initialReqScreen:x,isReqBench:u}),{createPayloadFromReduxStateArticle:j}=oT({initialReqScreen:x,isReqBench:u}),{changePayloadForTemplate:Z}=ji(o);return({[J.MAT]:()=>R===h.CHANGE||R===h.CHANGE_WITH_UPLOAD?Z(!!d):w(p),[J.ART]:()=>j(p),[J.PC]:()=>{var i,y;return R===((i=h)==null?void 0:i.CREATE)||R===((y=h)==null?void 0:y.CREATE_WITH_UPLOAD)?KT(c,T,d,l):QT(U,l,b,B)},[J.CC]:()=>{var i,y;return R===((i=h)==null?void 0:i.CREATE)||R===((y=h)==null?void 0:y.CREATE_WITH_UPLOAD)?ZT(c,T,l,L):ef(T,Oe,he,u,F,G)},[J.GL]:()=>{var i,y,V;return R===((i=h)==null?void 0:i.CREATE)||R===((y=h)==null?void 0:y.CREATE_WITH_UPLOAD)?af(c,T,"",l,L,"",""):R===((V=h)==null?void 0:V.EXTEND)?lf(c,T,Q,he,n,L,W):sf(g,Oe,he,u,P,q)},[J.PCG]:()=>Uh(p,T),[J.CCG]:()=>of(p,T),[J.CEG]:()=>Uh(p,T),[J.BK]:()=>rf(p==null?void 0:p.payload,p==null?void 0:p.requestHeaderResponse),[J.BOM]:()=>tf(ee,Ce,m,re),[J.IO]:()=>nf(c,T,d,l),[J.ART]:()=>j(p)}[r]||(()=>null))()},TC=({open:r,onClose:d,title:R,message:o,subText:p,buttonText:e,redirectTo:O="/",icon:l=t(nC,{sx:{fontSize:40,color:me.icon.green}}),headerColor:u="#eae9ff"})=>{const x=Ni();return D(Js,{open:r,onClose:(Ce,re)=>{re!=="backdropClick"&&(d==null||d())},maxWidth:"sm",fullWidth:!0,PaperProps:{sx:{borderRadius:3,boxShadow:6}},children:[t(so,{sx:{backgroundColor:u,color:"#fff",m:0,p:2,display:"flex",alignItems:"center",justifyContent:"space-between",borderTopLeftRadius:12,borderTopRightRadius:12},children:t(Be,{variant:"h5",fontWeight:600,children:R})}),t(Xs,{children:D(Fa,{direction:"row",spacing:2,alignItems:"center",mt:1,children:[t(ue,{sx:{color:u},children:l}),D(ue,{children:[t(Be,{variant:"subtitle1",fontWeight:800,children:o}),p&&t(Be,{variant:"body2",color:"text.secondary",children:p})]})]})}),t(oo,{sx:{px:3,pb:2},children:t(da,{onClick:()=>{d==null||d(),O&&x(O)},variant:"contained",color:"success",sx:{minWidth:160},children:e})})]})},tT=N.forwardRef((r,d)=>{var te;const[R,o]=N.useState({title:"",message:"",subText:"",buttonText:"",redirectTo:""}),[p,e]=N.useState(!1),[O,l]=N.useState([]),u=Y(i=>i.payload),x=((te=u==null?void 0:u.payloadData)==null?void 0:te.TemplateName)||"",[ee,m]=N.useState(!1),[Ce,re]=N.useState(!1),{changePayloadForTemplate:Oe}=ji(x),{destination:he,approveapiCreate:Q,approveapiChange:n,approveapiExtend:W}=Mi(r.module),c=new URLSearchParams(location.search);c.get("reqBench");const T=c.get("RequestId"),U=wd[r==null?void 0:r.module]||(()=>({})),g=Y(U),L=Y(i=>i.commonFilter.RequestBench),{customError:B}=Wd();Ni(),N.useImperativeHandle(d,()=>({handlePriorityDialogClickOpen:P}));const b=Y(i=>i.paginationData),q=[{field:"id",headerName:"",editable:!1,flex:1,hide:!0},{field:"reqId",headerName:"Request ID",editable:!1,flex:2.5},{field:"module",headerName:"Module",editable:!1,flex:2},{field:"reqType",headerName:"Request Type",editable:!1,flex:1.5},{field:"scheduledBy",headerName:"Scheduled By",editable:!1,flex:4},{field:"scheduledOn",headerName:"Scheduled On",editable:!1,flex:1},{field:"totalObjects",headerName:"Total Objects",editable:!1,flex:1,renderHeader:()=>t(Wl,{title:"Objects count scheduled for SAP syndication",arrow:!0,children:t("span",{children:"Request ID"})})},{field:"pendingObjects",headerName:"Pending Objects",editable:!1,flex:1,renderHeader:()=>t(Wl,{title:"Objects count pending for SAP syndicated.",arrow:!0,children:t("span",{children:"Request ID"})})},{field:"objectSuccessCount",headerName:"Success Objects",editable:!1,flex:1,renderHeader:()=>t(Wl,{title:"Objects count syndicated in SAP",arrow:!0,children:t("span",{children:"Request ID"})})},{field:"objectFailureCount",headerName:"Failure Objects",editable:!1,flex:1,renderHeader:()=>t(Wl,{title:"Objects count failed during syndication in SAP",arrow:!0,children:t("span",{children:"Request ID"})})},{field:"retryCount",headerName:"Retry Count",editable:!1,flex:1,renderHeader:()=>t(Wl,{title:"Number of times request retriggered.(Max- 3 count, after that it wouldnt be picked & set as status- Retry Count Exceeded)",arrow:!0,children:t("span",{children:"Request ID"})})},{field:"schedulerStatus",headerName:"Scheduler Status",editable:!1,flex:1},{field:"action",headerName:"Action",editable:!1,flex:1}],P=()=>{G()},G=()=>{var z;const i=Ai().format("YYYY-MM-DDTHH:mm:ss.SSSZ"),y={modules:"Material,Cost Center,Profit Center,CC-PC Combo,General Ledger",requestTypes:"Create with Upload,Change with Upload,Extend with Upload",statuses:"Scheduler - Failed,Scheduler - Pending,Scheduler - Partially Completed",toDate:Ai(L==null?void 0:L.createdOn[1]).utc().format("YYYY-MM-DDTHH:mm:ss")??"",fromDate:Ai(L==null?void 0:L.createdOn[0]).utc().format("YYYY-MM-DDTHH:mm:ss")??""},V=K=>{var X,ye,je,$;if(K.statusCode===200){let De=(K==null?void 0:K.data)||[];re(!0);let de=De==null?void 0:De.map((se,fa)=>({id:fa,schedulerId:se==null?void 0:se.SchedulerId,reqId:se==null?void 0:se.RequestId,reqType:se==null?void 0:se.RequestType,priority:se==null?void 0:se.Priority,scheduledBy:se==null?void 0:se.ReqScheduledBy,scheduledOn:se==null?void 0:se.ReqScheduledOn,totalObjects:se==null?void 0:se.ObjectCount,pendingObjects:se==null?void 0:se.PendingObjectsCount,retryCount:se==null?void 0:se.RetryCount,module:se==null?void 0:se.Module,objectSuccessCount:se==null?void 0:se.ObjectsSuccessCount,objectFailureCount:se==null?void 0:se.ObjectsFailureCount,updatedOn:se==null?void 0:se.ReqUpdatedOn,schedulerStatus:se==null?void 0:se.SchedulerStatus}));const We=de==null?void 0:de.filter(se=>se.module===r.module&&se.reqType===r.requestType),ha=(We==null?void 0:We.length)>0?Math.max(...We.map(se=>se.priority)):0;var Ee={id:de.length+1,schedulerId:"",reqId:((X=r.taskData)==null?void 0:X.requestId)||((ye=r.taskData)==null?void 0:ye.ATTRIBUTE_1)||"",reqType:((je=r==null?void 0:r.taskData)==null?void 0:je.ATTRIBUTE_2)??"Create with Upload",priority:ha+1,scheduledBy:(($=r.userData)==null?void 0:$.emailId)??"",scheduledOn:i??"",totalObjects:(b==null?void 0:b.totalElements)||0,pendingObjects:(b==null?void 0:b.totalElements)||0,retryCount:0,module:r.module||"Material",objectSuccessCount:0,objectFailureCount:0,updatedOn:i??"",schedulerStatus:"Scheduler - Pending"};const aa=[...de,Ee].sort((se,fa)=>se.module!==fa.module?se.module.localeCompare(fa.module):se.reqType!==fa.reqType?se.reqType.localeCompare(fa.reqType):se.priority-fa.priority);l(aa)}else r.setDialogTitle("Error"),r.setSuccessMsg(!1),r.setMessageDialogMessage("Failed Fetching Scheduled Requests. Try Once more."),r.setMessageDialogSeverity("danger"),r.handleMessageDialogClickOpen()},v=K=>{B(K)};ma(`/${cf}${(z=ca.DATA)==null?void 0:z.FETCH_SCHEDULERS_IN_REQ_BENCH}`,"post",V,v,y)},F=()=>{m(!1),re(!1)},w=i=>{l(i)};let j=rT({module:r==null?void 0:r.module,requestId:T,requestType:g==null?void 0:g.RequestType,templateName:g==null?void 0:g.TemplateName,payloadData:u});const Z=i=>{var Ee;const y=r.requestType===h.CREATE_WITH_UPLOAD?`/${he}${Q}`:r.requestType===h.EXTEND_WITH_UPLOAD?`/${he}${W}`:r.requestType===h.CHANGE_WITH_UPLOAD?`/${he}${n}`:null;F(),r.setBlurLoading(!0);let V=(r==null?void 0:r.requestType)===((Ee=h)==null?void 0:Ee.CHANGE_WITH_UPLOAD)?Oe(!0):r.createPayloadFromReduxState(u);const v=r.module===J.MAT?V==null?void 0:V.map(X=>({Tochildrequestheaderdata:{...X==null?void 0:X.Tochildrequestheaderdata,IsScheduled:!0}})):j==null?void 0:j.map(X=>({...X,IsScheduled:!0}));ma(y,"post",X=>{r.setBlurLoading(!0),(X==null?void 0:X.statusCode)===200||(X==null?void 0:X.statusCode)===201?(o({title:gi.TITLE,message:X.message,subText:gi.SUBTEXT,buttonText:gi.BUTTONTEXT,redirectTo:gi.REDIRECT}),e(!0)):(X==null?void 0:X.statusCode)===500||(X==null?void 0:X.statusCode)===501?(o({title:pi.TITLE,message:X.message,subText:pi.SUBTEXT,buttonText:pi.BUTTONTEXT,redirectTo:pi.REDIRECT}),e(!0)):(setSnackbarOpen(!0),setAlertMsg("Unexpected response received."))},X=>{console.log("error")},v)};O==null||O.map((i,y)=>({SchedulerId:(i==null?void 0:i.schedulerId)??"",RequestId:i==null?void 0:i.reqId,RequestType:i==null?void 0:i.reqType,Module:i==null?void 0:i.module,Priority:i==null?void 0:i.priority,ObjectCount:i==null?void 0:i.totalObjects,ObjectsSuccessCount:i==null?void 0:i.objectSuccessCount,ObjectsFailureCount:i==null?void 0:i.objectFailureCount,PendingObjectsCount:i==null?void 0:i.pendingObjects,ReqScheduledBy:i==null?void 0:i.scheduledBy,ReqScheduledOn:i==null?void 0:i.scheduledOn,ReqUpdatedOn:i==null?void 0:i.updatedOn,SchedulerStatus:i==null?void 0:i.schedulerStatus,RetryCount:i==null?void 0:i.retryCount}));const le=Cs(({className:i,...y})=>t(Wl,{...y,classes:{popper:i}}))({[`& .${df.tooltip}`]:{maxWidth:"none"}});return D(al,{children:[t(TC,{open:p,onClose:()=>e(!1),title:R.title,message:R.message,subText:R.subText,buttonText:R.buttonText,redirectTo:R.redirectTo}),D(Js,{open:Ce,onClose:F,fullWidth:!0,maxWidth:"xl",PaperProps:{sx:{borderRadius:3,boxShadow:8,backgroundColor:"#ffffff"}},children:[D(ue,{sx:{px:3,py:2,borderBottom:"1px solid #e0e0e0",display:"flex",justifyContent:"space-between",alignItems:"center",backgroundColor:"#F4F6FA",borderTopLeftRadius:12,borderTopRightRadius:12},children:[t(Be,{variant:"h6",fontWeight:600,color:"text.primary",children:"List of Requests Scheduled"}),t(le,{arrow:!0,title:t(Be,{fontSize:12,children:"Here you can prioritize your requests"}),children:t(jl,{size:"small",children:t(uf,{fontSize:"small"})})})]}),t(Xs,{sx:{px:3,py:2},children:t(ue,{sx:{border:"1px solid #e0e0e0",borderRadius:2,overflow:"hidden"},children:t(hf,{columns:q,row:O,onRowUpdate:w,selectionType:"SAPScheduler",showDragIcon:!0})})}),D(oo,{sx:{px:3,py:2,backgroundColor:"#FAFAFA",borderTop:"1px solid #e0e0e0",borderBottomLeftRadius:12,borderBottomRightRadius:12},children:[t(da,{onClick:F,variant:"outlined",color:"primary",sx:{textTransform:"capitalize",minWidth:100},children:"Cancel"}),t(da,{onClick:()=>Z(r.currentButtonState),variant:"contained",color:"primary",sx:{textTransform:"capitalize",minWidth:100},children:"Submit"})]})]})]})}),nT=({handleSaveAsDraft:r,handleSubmitForReview:d,handleSubmitForApprove:R,handleSendBack:o,handleCorrection:p,handleRejectAndCancel:e,handleValidateAndSyndicate:O,validateAllRows:l,isSaveAsDraftEnabled:u=!0,filteredButtons:x,validateEnabled:ee=!0,moduleName:m,isHierarchy:Ce=!1,showWfLevels:re,workFlowLevels:Oe,setSelectedLevel:he,selectedLevel:Q})=>{var Jl,Xl,Vl,il,bl,Kl,Ol;const[n,W]=N.useState(!1),[c,T]=N.useState(""),[U,g]=N.useState(!1),[L,B]=N.useState(""),[b,q]=N.useState(!1),[P,G]=N.useState(""),[F,w]=N.useState(!1),[j,Z]=N.useState(""),[le,te]=N.useState(!1),[i,y]=N.useState("success"),[V,v]=N.useState(""),z=200,[K,Ee]=N.useState(!1),[X,ye]=N.useState(""),[je,$]=N.useState(""),[De,de]=N.useState("success"),[We,ha]=N.useState(""),[Ta,aa]=N.useState(!1),[se,fa]=N.useState(!1),[ll,hl]=N.useState(!1),[Rl,El]=N.useState(""),[Aa,wa]=N.useState(null),[Ia,ve]=N.useState(!1),[Da,ia]=N.useState([]),[sl,ne]=N.useState([]),[E,qe]=N.useState(!1),[ra,Ca]=N.useState(""),[Ye,xa]=N.useState(!1),[Ze,oa]=N.useState(!1),ol=bi(),$a=Vs();jd();const Ma=N.useRef(),rl=new URLSearchParams($a.search),za=rl.get("reqBench"),tl=rl.get("RequestId");let Tl=Y(S=>S.userManagement.userData),ke=Y(S=>S.userManagement.taskData);const _a=To(to.CURRENT_TASK),Ya=typeof _a=="string"?JSON.parse(_a):_a,ta=$a.state,as=!(ke!=null&&ke.taskId||Ya!=null&&Ya.ATTRIBUTE_5)&&!za,nl=Y(S=>S.CommonStepper.activeStep);let ls=Y(S=>S==null?void 0:S.userManagement.taskData);Y(S=>{var Ge,Je;return(Je=(Ge=S==null?void 0:S.costCenter)==null?void 0:Ge.payload)==null?void 0:Je.rowsBodyData}),Y(S=>{var Ge,Je;return(Je=(Ge=S==null?void 0:S.profitCenter)==null?void 0:Ge.payload)==null?void 0:Je.rowsBodyData}),Y(S=>{var Ge,Je;return(Je=(Ge=S==null?void 0:S.generalLedger)==null?void 0:Ge.payload)==null?void 0:Je.rowsBodyData}),Y(S=>S.payload.dynamicKeyValues);const dl=Y(S=>{var Ge,Je,pa,Ra,qa,Sa,Wa,Pa,Na,cl,ul,Va,Ka,yl,vl,ql,Sl,Pl;switch(m){case((Ge=J)==null?void 0:Ge.CC):return(pa=(Je=S==null?void 0:S.costCenter)==null?void 0:Je.payload)==null?void 0:pa.rowsBodyData;case((Ra=J)==null?void 0:Ra.PC):return(Sa=(qa=S==null?void 0:S.profitCenter)==null?void 0:qa.payload)==null?void 0:Sa.rowsBodyData;case((Wa=J)==null?void 0:Wa.GL):return(Na=(Pa=S==null?void 0:S.generalLedger)==null?void 0:Pa.payload)==null?void 0:Na.rowsBodyData;case((cl=J)==null?void 0:cl.CCG):return S.hierarchyData;case((ul=J)==null?void 0:ul.PCG):return S.hierarchyData;case((Va=J)==null?void 0:Va.CEG):return S.hierarchyData;case((Ka=J)==null?void 0:Ka.IO):return(yl=S.internalOrder.IOpayloadData)==null?void 0:yl.childRequestHeaderData;case((vl=J)==null?void 0:vl.BOM):return S.bom.BOMpayloadData.Tochildrequestheaderdata;case((ql=J)==null?void 0:ql.BK):return(Pl=(Sl=S==null?void 0:S.bankKey)==null?void 0:Sl.payload)==null?void 0:Pl.childRequestHeaderData;default:return null}}),fl=Y(S=>{var Ge,Je,pa,Ra,qa,Sa,Wa,Pa,Na,cl,ul,Va,Ka;switch(m){case((Ge=J)==null?void 0:Ge.CC):return(pa=(Je=S==null?void 0:S.costCenter)==null?void 0:Je.payload)==null?void 0:pa.requestHeaderData;case((Ra=J)==null?void 0:Ra.PC):return(Sa=(qa=S==null?void 0:S.profitCenter)==null?void 0:qa.payload)==null?void 0:Sa.requestHeaderData;case((Wa=J)==null?void 0:Wa.GL):return(Na=(Pa=S==null?void 0:S.generalLedger)==null?void 0:Pa.payload)==null?void 0:Na.requestHeaderData;case((cl=J)==null?void 0:cl.CCG):return S.hierarchyData;case((ul=J)==null?void 0:ul.PCG):return S.hierarchyData;case((Va=J)==null?void 0:Va.CEG):return S.hierarchyData;case((Ka=J)==null?void 0:Ka.BOM):return S.bom.BOMpayloadData;default:return null}}),Al=fl==null?void 0:fl.RequestType,ss=Al===h.CREATE_WITH_UPLOAD||Al===h.CHANGE_WITH_UPLOAD,xe=Ce||m===((Jl=J)==null?void 0:Jl.BOM)||m===((Xl=J)==null?void 0:Xl.IO)||m===((Vl=J)==null?void 0:Vl.BK)?dl==null?void 0:dl.RequestStatus:(il=Object.values(dl||{})[0])==null?void 0:il.RequestStatus,Il=(ta==null?void 0:ta.reqStatus)===((bl=pl)==null?void 0:bl.VALIDATED_REQUESTOR)||xe===((Kl=pl)==null?void 0:Kl.VALIDATED_REQUESTOR),{createPayloadFromReduxState:Dl}=ki({initialReqScreen:as,isreqBench:za,remarks:c,userInput:We,selectedLevel:Q}),Ha=()=>{W(!0)},Cl=()=>{T(""),W(!1)},kl=S=>{const Ge=S.target.value;g(Ge.length>=z),Ge.length>0&&Ge[0]===" "?T(Ge.trimStart()):T(Ge)},Ja=()=>{te(!0)},os=()=>{te(!1)},_l=()=>{q(!0)},Xa=()=>{q(!1)},rs=()=>{Xa(),L==="SAVE_AS_DRAFT"&&r("SAVE_AS_DRAFT")},$l=S=>{var Ge,Je,pa,Ra,qa,Sa;v(""),de("success"),wa(S),ye(S.MDG_DYN_BTN_COMMENT_BOX_NAME||S.MDG_MAT_DYN_BTN_COMMENT_BOX_NAME),fa(S.MDG_DYN_BTN_COMMENT_BOX_INPUT===((Ge=es)==null?void 0:Ge.MANDATORY)||S.MDG_MAT_DYN_BTN_COMMENT_BOX_INPUT===((Je=es)==null?void 0:Je.MANDATORY)),hl(S.MDG_DYN_BTN_COMMENT_BOX_INPUT===((pa=es)==null?void 0:pa.MANDATORY)||S.MDG_MAT_DYN_BTN_COMMENT_BOX_INPUT===((Ra=es)==null?void 0:Ra.MANDATORY)||S.MDG_DYN_BTN_COMMENT_BOX_INPUT==="Optional"||S.MDG_MAT_DYN_BTN_COMMENT_BOX_INPUT==="Optional"),El(S.MDG_DYN_BTN_ACTION_TYPE||S.MDG_MAT_DYN_BTN_ACTION_TYPE),S.MDG_DYN_BTN_BUTTON_NAME===va.SEND_BACK||S.MDG_DYN_BTN_BUTTON_NAME===va.CORRECTION||S.MDG_MAT_DYN_BTN_BUTTON_NAME===va.SEND_BACK||S.MDG_MAT_DYN_BTN_BUTTON_NAME===va.CORRECTION?oa(!0):oa(!1),(S.MDG_DYN_BTN_BUTTON_NAME||S.MDG_MAT_DYN_BTN_ACTION_TYPE)===va.SAP_SYNDICATE?qe(!0):qe(!1),S.MDG_DYN_BTN_COMMENT_BOX_INPUT===((qa=es)==null?void 0:qa.MANDATORY)||S.MDG_MAT_DYN_BTN_COMMENT_BOX_INPUT===((Sa=es)==null?void 0:Sa.MANDATORY)||S.MDG_DYN_BTN_COMMENT_BOX_INPUT==="Optional"||S.MDG_MAT_DYN_BTN_COMMENT_BOX_INPUT==="Optional"?Nl():ga(S.MDG_DYN_BTN_ACTION_TYPE||S.MDG_MAT_DYN_BTN_ACTION_TYPE)},ga=(S,Ge)=>{switch(S){case"handleSubmitForApproval":R("APPROVE",We);break;case"handleSubmitForReview":d("SUBMIT_FOR_REVIEW",We);break;case"handleValidate":O("VALIDATE");break;case"handleSAPSyndication":O("SYNDICATE",We);break;case"handleSendBack":o("SEND_BACK",We);break;case"handleCorrection":p("CORRECTION",We);break;case"handleReject":e("REJECT",We);break;case"handleDraft":r("SAVE_AS_DRAFT",We);break;case"handleValidate1":O("VALIDATE");break;default:console.log("Unknown action type")}},Nl=()=>{Ee(!0)},Ml=()=>{Ee(!1),ha(""),aa(!1),fa(!1),xa(!1)},zl=()=>{var S;if(se&&!We){aa(!0);return}else if(re&&Q===""&&Ze){xa(!0);return}else ra==="scheduleSyndication"?Ma!=null&&Ma.current&&((S=Ma==null?void 0:Ma.current)==null||S.handlePriorityDialogClickOpen()):ga(Rl);Ml()},Yl=S=>{var Ge,Je,pa,Ra;B(S),S==="SAVE_AS_DRAFT"?(G("Are you sure you want to save as draft?"),_l()):S==="VALIDATE"?m===((Ge=J)==null?void 0:Ge.PCG)||m===((Je=J)==null?void 0:Je.CCG)||m===((pa=J)==null?void 0:pa.BOM)||m===((Ra=J)==null?void 0:Ra.CEG)?O("VALIDATE"):l(S):Ha()},ts=()=>{Cl(),L==="SUBMIT_FOR_REVIEW"?d(L,c):L==="APPROVE"&&R(L,c)},ns=S=>{he(S.target.value),ol(Ii({keyName:"Level",data:S.target.value}))};return D(Fa,{children:[t(Fi,{sx:{position:"fixed",bottom:0,left:0,right:0,zIndex:1e3},elevation:2,children:t(Jh,{className:"container_BottomNav",showLabels:!0,sx:{display:"flex",justifyContent:"space-between",alignItems:"center",gap:1,width:"100%"},children:t(ue,{sx:{display:"flex",gap:1},children:!tl||(ta==null?void 0:ta.reqStatus)===((Ol=pl)==null?void 0:Ol.DRAFT)||(ta==null?void 0:ta.reqStatus)==="Validated-Requestor"||(ta==null?void 0:ta.reqStatus)==="Validation Failed-Requestor"||(ta==null?void 0:ta.reqStatus)==="Upload Successful"?D(al,{children:[t(da,{variant:"contained",color:"primary",onClick:()=>Yl("SAVE_AS_DRAFT"),children:"Save as Draft"}),t(Wl,{title:"Simulates in SAP",arrow:!0,children:nl===1&&t(da,{variant:"contained",color:"primary",onClick:()=>Yl("VALIDATE"),children:"Validate"})}),nl===3&&t(da,{variant:"contained",color:"primary",onClick:()=>{B("SUBMIT_FOR_REVIEW"),Ha()},disabled:!Il,children:"Submit"})]}):tl&&!ta?x==null?void 0:x.map((S,Ge)=>{var Na;const{MDG_DYN_BTN_BUTTON_NAME:Je,MDG_DYN_BTN_BUTTON_STATUS:pa,MDG_MAT_DYN_BTN_BUTTON_NAME:Ra,MDG_MAT_DYN_BTN_BUTTON_STATUS:qa}=S,Sa=xe==="Validated-MDM"||xe==="Validated-Intermediate",Wa=xe===((Na=pl)==null?void 0:Na.VALIDATED_REQUESTOR);let Pa=pa==="DISABLED"||qa==="DISABLED";return(Sa||Wa)&&(Pa=!1),t(al,{children:Object.keys(ls||{}).length>0&&nl!==2&&nl!==3&&t(da,{variant:"contained",size:"small",sx:{...Xh,mr:1},disabled:Pa,onClick:()=>$l(S),children:S.MDG_DYN_BTN_BUTTON_NAME||S.MDG_MAT_DYN_BTN_BUTTON_NAME},Ge)})}):t(al,{})})})}),D(Js,{hideBackdrop:!1,elevation:2,PaperProps:{sx:{boxShadow:"none"}},open:n,onClose:Cl,children:[D(so,{sx:{justifyContent:"space-between",alignItems:"center",height:"max-content",padding:".5rem",paddingLeft:"1rem",backgroundColor:"#EAE9FF40",display:"flex"},children:[t(Be,{variant:"h6",children:L==="SAVE"?"Save As Draft":"Remarks"}),t(jl,{sx:{width:"max-content"},onClick:Cl,children:t(Fd,{})})]}),t(Xs,{sx:{padding:".5rem 1rem"},children:L!=="SAVE"?t(Fa,{children:t(ue,{sx:{minWidth:400},children:D(mi,{sx:{height:"auto"},fullWidth:!0,children:[t(Vh,{sx:{backgroundColor:"#F5F5F5","& .MuiOutlinedInput-root":{"& fieldset":{borderColor:U?"red":"rgba(0, 0, 0, 0.23)"},"&:hover fieldset":{borderColor:U?"red":"rgba(0, 0, 0, 0.23)"},"&.Mui-focused fieldset":{borderColor:U?"red":"primary"}}},multiline:!0,rows:4,value:c,onChange:kl,placeholder:"Enter your remarks here",inputProps:{maxLength:z}}),t(Kh,{error:U,children:U?`Maximum ${z} characters allowed`:`${c.length}/${z}`})]})})}):t(Be,{children:"Are you sure you want to save as draft?"})}),D(oo,{sx:{display:"flex",justifyContent:"end"},children:[t(da,{sx:{width:"max-content",textTransform:"capitalize"},onClick:Cl,children:"Cancel"}),t(da,{className:"button_primary--normal",type:"save",onClick:ts,variant:"contained",children:L==="SAVE"?"Yes":"Submit"})]})]}),D(Js,{hideBackdrop:!1,elevation:2,PaperProps:{sx:{boxShadow:"none"}},open:b,onClose:Xa,children:[D(so,{sx:{justifyContent:"space-between",alignItems:"center",height:"max-content",padding:".5rem",paddingLeft:"1rem",backgroundColor:"#EAE9FF40",display:"flex"},children:[t(Be,{variant:"h6",children:"Confirmation"}),t(jl,{sx:{width:"max-content"},onClick:Xa,children:t(Fd,{})})]}),t(Xs,{sx:{padding:"1rem"},children:t(Be,{children:P})}),D(oo,{sx:{display:"flex",justifyContent:"end",padding:".5rem 1rem"},children:[t(da,{sx:{width:"max-content",textTransform:"capitalize"},onClick:Xa,children:"Cancel"}),t(da,{className:"button_primary--normal",type:"save",onClick:rs,variant:"contained",children:"Yes"})]})]}),t(Qh,{dialogState:K,openReusableDialog:Nl,closeReusableDialog:Ml,dialogTitle:X,dialogMessage:je,handleDialogConfirm:zl,dialogOkText:"OK",dialogSeverity:De,showCancelButton:!0,showInputText:ll,inputText:We,setInputText:ha,mandatoryTextInput:se,remarksError:Ta,isTable:Ia,tableColumns:sl,tableRows:Da,setSyndicationType:Ca,syndicationType:ra,isSyndicationBtn:E,isMassSyndication:ss,blurLoading:F,isShowWFLevel:re&&Ze,selectedLevel:Q,wfError:Ye,handleLevelChange:ns,workFlowLevels:Oe}),t(tT,{ref:Ma,dialogTitle:X,setDialogTitle:ye,messageDialogMessage:V,setMessageDialogMessage:v,messageDialogSeverity:De,setMessageDialogSeverity:de,handleMessageDialogClickOpen:Nl,blurLoading:F,setBlurLoading:w,handleMessageDialogClose:Ml,createPayloadFromReduxState:Dl,setTextInput:hl,inputText:ll,handleSnackBarOpen:Ja,taskData:ke,userData:Tl,currentButtonState:Aa,requestType:Al,module:m}),V&&t(kd,{openSnackBar:le,alertMsg:V,alertType:i,handleSnackBarClose:os}),t(Zh,{blurLoading:F,loaderMessage:j})]})},fC=({sidebyside:r=!1,artifactId:d="",artifactName:R="",poNumber:o,isAnAttachment:p,promptAction_Functions:e,attachmentType:O,module:l,requestId:u,attachments:x})=>{var sl;const ee=Y(ne=>{var E,qe;return(qe=(E=ne.request)==null?void 0:E.requestHeader)==null?void 0:qe.requestType}),m=Y(ne=>{var E,qe;return(qe=(E=ne==null?void 0:ne.userManagement)==null?void 0:E.taskData)==null?void 0:qe.ATTRIBUTE_2}),[Ce,re]=N.useState(!1),[Oe,he]=N.useState(!1),[Q,n]=N.useState([]),[W,c]=N.useState([]),[T,U]=N.useState(!1),[g,L]=N.useState(["Others"]),[B,b]=N.useState(!1),[q,P]=N.useState(!1),[G,F]=N.useState(""),[w,j]=N.useState("success"),Z=bi(),le=Vs(),i=new URLSearchParams(le.search).get("reqBench");let y=Y(ne=>ne.userManagement.userData);const V=wd[l]||(()=>({})),v=Y(V),z=Y(ne=>{var E,qe;return(qe=(E=ne==null?void 0:ne.payload)==null?void 0:E.dynamicKeyValues)==null?void 0:qe.childRequestHeaderData}),K=(z==null?void 0:z.ChildRequestId)||(v==null?void 0:v.childRequestId);let Ee=localStorage==null?void 0:localStorage.getItem(to.CURRENT_TASK);const X=JSON.parse(Ee||"{}");X&&(X==null||X.itmStatus);const{refetch:ye}=sT({requestId:u,hasAnyChildRequestId:K}),{showSnackbar:je}=wi(),[$,{error:De}]=Jf(),{t:de}=Ks();let We={other:me.attachmentDialog.other,"order change":me.attachmentDialog.orderChange},ha=ne=>We[ne]?We[ne]:me.attachmentDialog.orderChange,Ta=()=>{U(!1),n([])};const aa=()=>{P(!1),F("")};let se=()=>{U(!0)},fa=ne=>t(al,{children:ne==null?void 0:ne.map((E,qe)=>{var ra,Ca,Ye,xa;return D("div",{style:{minWidth:"5rem",minHeight:"6rem",height:"6.5rem",width:"5.4rem",borderRadius:".5rem",margin:"0 .5rem",border:`1px solid ${ha((ra=E.metaData)==null?void 0:ra.toLowerCase())}`,overflow:"hidden",boxShadow:"0px 6px 6px -3px rgba(0,0,0,0.2)",position:"relative"},children:[t(wl,{sx:{height:"1rem",backgroundColor:ha((Ca=E.metaData)==null?void 0:Ca.toLowerCase()),display:"flex",justifyContent:"center",overflow:"hidden"},children:t(Be,{sx:{fontSize:"10px",width:"100%",textAlign:"center"},children:E==null?void 0:E.metaData})}),t(wl,{sx:{display:"flex",justifyContent:"center",alignItems:"center",padding:"0.6rem"},children:t("img",{style:{width:"1.6rem",height:"1.6rem"},src:Fh[((Ye=E==null?void 0:E.name)==null?void 0:Ye.split(".")[1])||((xa=E==null?void 0:E.fileName)==null?void 0:xa.split(".")[1])]})}),t(wl,{sx:{overflow:"hidden"},children:t(Be,{variant:"body2",sx:{padding:".3rem",color:"grey",textAlign:"center",textOverflow:"ellipsis",overflow:"hidden",whiteSpace:"noWrap",height:"2rem"},children:(E==null?void 0:E.name)||(E==null?void 0:E.fileName)})})]},qe)})});const ll=ne=>{ne.preventDefault(),re(!0)},hl=()=>{re(!1)},Rl=ne=>{ne.preventDefault(),re(!1);const E=Array.from(ne.dataTransfer.files);if(Q.length+E.length>5){e.handleOpenPromptBox("Warning",{title:"Warning",message:ho.NUMBER_OF_FILES_LIMIT,severity:"warning",cancelButton:!1});return}Aa(E)},El=()=>{document.getElementById("fileButton").click()},Aa=ne=>{let E=[];ne.forEach(qe=>{qe.id=Gi(),E.push(qe)}),n(qe=>[...qe,...E])},wa=ee||m||(v==null?void 0:v.RequestType);let Ia=()=>{if(!B&&Q[0]){let ne=[...Q];(async()=>{var tl,Tl;he(!0);const qe=new FormData;let ra={};g==null||g.forEach(ke=>{ra[ke.metadataTag]=ke.id});let Ca=[];ne==null||ne.forEach(ke=>{({...ke},ke.name),qe.append("files",ke,ke.name),Ca.push(ke.name)}),Z(gf(d));const{RequestId:Ye,RequestType:xa,childRequestId:Ze}=v||{},{ChildRequestId:oa}=z||{},ol=[h.CHANGE,h.CHANGE_WITH_UPLOAD].includes(xa),$a=Ye||null,Ma=ol&&(l===J.MAT||l===J.ART)?oa||null:Ze||null,rl={artifactId:d,createdBy:y==null?void 0:y.emailId,artifactType:R,attachmentType:O,requestId:$a,childRequestId:Ma,fileName:Ca.join("$^$"),requestType:wa};p&&(rl.isAnAttachment=p),o&&R!=="Purchase Order"&&(rl.poNumber=o),qe.append("doc",JSON.stringify(rl));const za=await $(qe);((Tl=(tl=za==null?void 0:za.data)==null?void 0:tl.responseMessage)==null?void 0:Tl.code)==Zl.STATUS_200?(ye(),Z(eT(O)),Ta(),he(!1),c(ke=>[...ke,...Q]),je(`Documents for ${R} ${d} uploaded successfully `,"success")):(he(!1),je(`${de(Hd.UPLOAD_FAILED)}`,"error")),De&&(he(!1),je(`${de(Hd.UPLOAD_FAILED)}`,"error"))})();return}Q[0]||e.handleOpenPromptBox("Warning",{title:"Warning",message:de("Please add some files to upload"),severity:"warning",cancelButton:!1})},ve=()=>{Ta()},Da=ne=>{let E=Q.filter(qe=>qe.id!==ne);n(E)},ia=()=>{let ne=0;Q.forEach(E=>{ne+=E.length}),ne>5e9?(e.handleOpenPromptBox("ERROR",{title:"Warning",message:de("Files size excceded"),severity:"warning",cancelButton:!1}),b(!0)):b(!1)};return N.useEffect(()=>{ia()},[Q]),D(al,{children:[D(wl,{children:[t(wl,{sx:{display:"flex",flexDirection:"row"},children:r===!1?D(al,{children:[t(wl,{sx:{display:"flex",flexDirection:"row",margin:"1rem 0",maxWidth:"50vw",overflowX:"auto",position:"relative",minHeight:"7rem"},children:W[0]&&fa(W)}),t(wl,{onClick:se,style:{width:"5rem",maxWidth:"5rem",height:"6.5rem",borderRadius:".5rem",backgroundColor:"#eae9ff",padding:"1rem",display:"flex",justifyContent:"center",alignItems:"center",margin:"1rem .5rem",boxShadow:"0px 2px 4px -1px rgba(0,0,0,0.2),0px 4px 5px 0px rgba(0,0,0,0.14),0px 1px 10px 0px rgba(0,0,0,0.12)",cursor:"pointer"},children:t(Zf,{color:"primary",sx:{fontSize:24}})})]}):t(wl,{display:"flex",justifyContent:"center",alignItems:"center",sx:{mr:1,mb:1},children:t(da,{variant:"contained",component:"label",onClick:se,startIcon:t(Tf,{}),disabled:i&&!((sl=ro)!=null&&sl.includes(v==null?void 0:v.RequestStatus)),sx:{color:"#fff",textTransform:"capitalize",borderRadius:"5px",padding:"10px 20px","&:disabled":{backgroundColor:"#ccc"},boxShadow:"0px 4px 10px rgba(0, 0, 0, 0.2)"},children:de("Upload File")})})}),T&&D(Js,{fullWidth:!0,maxWidth:"sm",open:!0,onClose:Ta,sx:{"& .MuiDialog-paper":{borderRadius:"12px",padding:"1rem"},overflow:"hidden"},children:[D(so,{sx:{padding:"1rem 1.5rem"},children:[t(Be,{variant:"h6",sx:{fontWeight:500},children:de("Add New Attachment")}),t(jl,{"aria-label":"close",onClick:ve,sx:ne=>({position:"absolute",right:12,top:10,color:ne.palette.grey[500]}),children:t(Fd,{})})]}),D(Xs,{sx:{padding:"1.5rem",overflow:"hidden"},children:[D(wl,{className:`dropzone ${Ce?"dragover":""}`,sx:{width:"100%",border:`2px dashed ${Ce?"#3b30c8":"#d0d5dd"}`,borderRadius:"8px",padding:"2rem",backgroundColor:Ce?"#f8f9ff":"#fafbff",transition:"all 0.3s ease",cursor:"pointer",minHeight:"200px",display:"flex",alignItems:"center",justifyContent:"center"},onDragOver:ll,onDragLeave:hl,onDrop:Rl,children:[D(Fa,{alignItems:"center",spacing:1,children:[t(Qf,{sx:{fontSize:48,color:"#3b30c8"}}),t(Be,{variant:"body1",sx:{color:"#344054"},children:de("Drag and drop files")}),t(Be,{variant:"body2",color:"primary",sx:{cursor:"pointer",textDecoration:"underline","&:hover":{color:"#3b30c8"}},onClick:El,children:de("or click to upload")})]}),t("input",{id:"fileButton",multiple:!0,accept:".jpeg, .jpg, .xls, .xlsx, .docx, .pdf",type:"file",name:"files",onChange:ne=>{const E=Array.from(ne.target.files);if(Q.length+E.length+(x==null?void 0:x.length)>5){e.handleOpenPromptBox("Warning",{title:de("Warning"),message:ho.NUMBER_OF_FILES_LIMIT,severity:"warning",cancelButton:!1});return}const qe=Q.reduce((Ca,Ye)=>Ca+(Ye.size||0),0),ra=E.reduce((Ca,Ye)=>Ca+(Ye.size||0),0);if(qe+ra>52428800){e.handleOpenPromptBox("Warning",{title:de("Warning"),message:de("Total file size must not exceed 50 MB."),severity:"warning",cancelButton:!1});return}Aa(E),ne.target.value=null},style:{display:"none"}})]}),Q[0]&&t(wl,{sx:{maxHeight:"14rem",overflowY:"auto",marginTop:"1.5rem",padding:"1rem",backgroundColor:me.primary.white,borderRadius:"8px",border:"1px solid #eee","&::-webkit-scrollbar-thumb":{backgroundColor:"#888",borderRadius:"3px"}},children:Q==null?void 0:Q.map((ne,E)=>{var qe;return D(wl,{sx:{display:"flex",alignItems:"center",padding:"0.75rem",borderRadius:"6px",backgroundColor:me.primary.white,marginBottom:"0.5rem",border:"1px solid #eee","&:hover":{backgroundColor:"#f9fafb"}},children:[t("img",{style:{width:"24px",height:"24px",marginRight:"0.75rem"},src:Fh[(qe=ne.name)==null?void 0:qe.split(".")[1]]}),t(Be,{variant:"body1",sx:{flexGrow:1},children:ne.name}),B?D(Be,{variant:"body2",color:"error",sx:{marginLeft:"1rem"},children:[parseFloat(ne.size/1e6).toFixed(2)," MB"]}):D(Be,{sx:{marginLeft:"auto",marginRight:"10%"},color:"gray",children:[parseFloat(ne.size/1e6).toFixed(2)," MB"]}),t(jl,{id:`closeBtn-${ne.id}`,size:"small",onClick:ra=>{ra.stopPropagation(),Da(ne.id)},sx:{marginLeft:"0.5rem",opacity:.8},children:t(eC,{fontSize:"small",color:"error"})})]},E)})}),D(Fa,{direction:"row",spacing:2,sx:{marginTop:"1.5rem",justifyContent:"flex-end",alignItems:"center"},children:[D(Be,{variant:"body2",color:"error",children:["*",de("Max file size 50 MB")]}),t(Be,{variant:"body2",sx:{color:me.placeholder.dark},children:de("Maximum 5 files allowed")}),t(da,{variant:"contained",onClick:Ia,sx:{borderRadius:"6px",padding:"0.5rem 1.5rem"},disabled:!(Q!=null&&Q.length),children:de("Upload")})]})]})]}),t(Cf,{sx:{color:me.primary.white,zIndex:1400},open:Oe,children:t(ff,{color:"inherit"})})]}),t(kd,{openSnackBar:q,alertMsg:G,alertType:w,handleSnackBarClose:aa})]})},CC=({title:r="",module:d="",artifactName:R="",getAttachments:o=()=>{},artifactId:p="",poNumber:e="",processName:O="",isAnAttachment:l="",commentOnly:u=!0,view:x=!1,attachmentType:ee="",requestId:m="",disableCheck:Ce,attachments:re})=>{const Oe=Ni(),[he,Q]=N.useState({open:!1,type:"",redirectOnClose:!0,message:"",title:"",severity:"",okButtonText:""}),[n,W]=N.useState(""),c={handleClosePromptBox:()=>{Q(T=>({open:!1,type:"",redirectOnClose:!0,message:"",title:"",severity:""})),W("")},handleOpenPromptBox:(T,U={})=>{let g={open:!0,title:"",message:"",okButton:!0,cancelButton:!0,okText:"Ok",cancelText:"Cancel",type:"dialog"};T==="SUCCESS"&&(g.type="snackbar"),W(T),Q({...g,...U})},handleCloseAndRedirect:()=>{c.handleClosePromptBox(),Oe("/purchaseOrder/management")},getCancelFunction:()=>{switch(n){default:return()=>{c.handleClosePromptBox()}}},getCloseFunction:()=>{switch(n){case"COMMENTERROR":default:return T=>{c.handleClosePromptBox()}}},getOkFunction:()=>{switch(n){default:return()=>{c.handleClosePromptBox()}}},getCloseAndRedirectFunction:()=>he.redirectOnClose?c.handleCloseAndRedirect:c.handleClosePromptBox};return D(al,{children:[t(aC,{type:he.type,promptState:he.open,setPromptState:c.handleClosePromptBox,onCloseAction:c.getCloseFunction(),promptMessage:he.message,dialogSeverity:he.severity,dialogTitleText:he.title,handleCancelButtonAction:c.getCancelFunction(),cancelButtonText:he.cancelText,showCancelButton:he.cancelButton,handleSnackBarPromptClose:c.getCloseAndRedirectFunction(),handleOkButtonAction:c.getOkFunction(),okButtonText:he.okButtonText,showOkButton:he.okButton}),t(Fa,{children:u&&t(fC,{sidebyside:!0,view:x,useMetaData:!1,title:r,artifactName:R,artifactId:p,processName:O,poNumber:e,promptAction_Functions:c,isAnAttachment:l,attachmentType:ee,requestId:m,module:d,getAttachmentshook:o,attachments:re})})]})};var $i={},gC=Ts;Object.defineProperty($i,"__esModule",{value:!0});var dT=$i.default=void 0,pC=gC(hs()),RC=fs;dT=$i.default=(0,pC.default)((0,RC.jsx)("path",{d:"m16 5-1.42 1.42-1.59-1.59V16h-1.98V4.83L9.42 6.42 8 5l4-4zm4 5v11c0 1.1-.9 2-2 2H6c-1.11 0-2-.9-2-2V10c0-1.11.89-2 2-2h3v2H6v11h12V10h-3V8h3c1.1 0 2 .89 2 2"}),"IosShareOutlined");const EC=Cs(pf)(({theme:r})=>({padding:r.spacing(2),border:"none",backgroundColor:"rgba(179, 236, 243, 0.5)"})),AC=Cs(ue)(({theme:r})=>{var d,R;return{backgroundColor:(R=(d=me)==null?void 0:d.primary)==null?void 0:R.whiteSmoke,padding:r.spacing(1),border:"1px solid #E0E0E0",borderRadius:r.shape.borderRadius,boxShadow:"0px 8px 15px rgba(0, 0, 0, 0.08), 0px 4px 6px rgba(115, 118, 122, 0.5)",minWidth:120,textAlign:"center",fontWeight:"bold",color:r.palette.text.primary}}),IC=Cs(ue)(({theme:r})=>({display:"flex",justifyContent:"space-between",alignItems:"center",paddingLeft:r.spacing(2),backgroundColor:r.palette.grey[100],borderBottom:`1px solid ${r.palette.divider}`})),DC=Cs(Rf)(({theme:r})=>({borderRadius:r.shape.borderRadius,boxShadow:"0 4px 6px rgba(0, 0, 0, 0.1)"})),jh=4,_C=({open:r=!1,onClose:d=()=>{},handleOk:R=()=>{},message:o=""})=>{const p=Y(ee=>ee.payload.matNoList||[]),e=(p==null?void 0:p.length)||0,O=N.useMemo(()=>{const ee=[];for(let m=0;m<p.length;m+=jh)ee.push(p.slice(m,m+jh));return ee},[p]),l=()=>{const ee=p==null?void 0:p.map((m,Ce)=>({id:Ce+1,material:m}));u.convertJsonToExcel(ee)},u={convertJsonToExcel:ee=>{let m=[];m.push({header:"Material",key:"material"}),_f({fileName:"Material List",columns:m,rows:ee})}},x=()=>{d()};return D(Js,{open:r,onClose:x,maxWidth:"md",PaperProps:{sx:{borderRadius:2,minWidth:"480px",maxHeight:"80vh"}},children:[D(so,{sx:{display:"flex",alignItems:"center",gap:1,py:2},children:[t(Ef,{fontSize:"medium",sx:{color:"0px 4px 6px rgba(115, 118, 122, 0.5)"}}),D(Be,{variant:"h6",fontWeight:"bold",children:["Info: ",o]})]}),D(Xs,{sx:{p:0},children:[D(IC,{children:[D(Be,{variant:"subtitle2",color:"text.secondary",sx:{marginLeft:"15px"},children:["Total Materials: ",t("strong",{children:e})]}),t(jl,{onClick:l,color:"primary",sx:{marginRight:"10px"},title:"Export Excel",children:t(dT,{})})]}),t(ue,{sx:{pt:0,pl:2,pr:2,pb:0},children:t(DC,{component:Fi,children:t(Af,{children:t(If,{children:O.map((ee,m)=>t(Df,{sx:{"&:last-child td":{borderBottom:0}},children:t(EC,{children:t(ue,{sx:{display:"flex",flexWrap:"wrap",gap:2,justifyContent:"center"},children:ee.map(Ce=>t(AC,{children:Ce},Ce))})})},m))})})})})]}),D(oo,{sx:{p:2,borderTop:1,borderColor:"divider"},children:[t(da,{onClick:x,variant:"outlined",color:"warning",sx:{minWidth:100,textTransform:"none",fontWeight:"medium"},children:"Close"}),t(da,{onClick:R,variant:"contained",color:"primary",sx:{minWidth:100,textTransform:"none",fontWeight:"medium"},children:"Continue"})]})]})},kh={handleSubmitForApproval:6,handleSendBack:1,handleReject:3,handleValidate:5,handleSAPSyndication:8,handleValidate1:4,handleSubmitForReview:7,handleCorrection:2},$h={Approve:6,"Send Back":3,"Save As Draft":1,Reject:3,Validate:4,Forward:6,"SAP Syndication":8,Submit:7,Correction:2},md={REQUEST_HEADER:0,REQUEST_DETAILS:1,ATTACHMENT_AND_COMMENTS:2,PREVIEW:3},Ea={HANDLE_SEND_BACK:"handleSendBack",HANDLE_VALIDATE1:"handleValidate1",HANDLE_SUBMIT_FOR_APPROVAL:"handleSubmitForApproval",HANDLE_VALIDATE:"handleValidate",HANDLE_SAP_SYNDICATION:"handleSAPSyndication",HANDLE_SUBMIT_FOR_REVIEW:"handleSubmitForReview",HANDLE_CORRECTION:"handleCorrection",HANDLE_SUBMIT:"handleSubmit",HANDLE_DRAFT:"handleDraft",HANDLE_ACCEPT:"handleReview",HANDLE_REJECT:"handleReject"},NC=[Ea.HANDLE_SEND_BACK,Ea.HANDLE_CORRECTION,Ea.HANDLE_SUBMIT_FOR_APPROVAL,Ea.HANDLE_SAP_SYNDICATION,Ea.HANDLE_SUBMIT_FOR_REVIEW,Ea.HANDLE_SUBMIT,Ea.HANDLE_REJECT],MC=[Ea.HANDLE_VALIDATE,Ea.HANDLE_DRAFT],Dg={0:[],1:MC,2:[],3:NC},iT=r=>{var Va,Ka,yl,vl,ql,Sl,Pl,gs,ps,Rs,Es,As,Is,Ds,_s,Ns,Ms,bs,Os,ys,vs,qs,Ss,Ps;const d=Y(I=>I.payload),R=Y(I=>I.payload.dynamicKeyValues),o=Y(I=>I.payload.changeFieldRows),p=Y(I=>I.payload.selectedRows),e=(Va=d==null?void 0:d.payloadData)!=null&&Va.data?(yl=(Ka=d==null?void 0:d.payloadData)==null?void 0:Ka.data)==null?void 0:yl.RequestType:(vl=d==null?void 0:d.payloadData)==null?void 0:vl.RequestType,[O,l]=N.useState(!1),[u,x]=N.useState("success"),[ee,m]=N.useState(!1),[Ce,re]=N.useState(""),[Oe,he]=N.useState(""),[Q,n]=N.useState(!1),[W,c]=N.useState(""),[T,U]=N.useState(!1),[g,L]=N.useState(""),[B,b]=N.useState(""),[q,P]=N.useState(!1),[G,F]=N.useState([]),[w,j]=N.useState([]),[Z,le]=N.useState(!1),[te,i]=N.useState(!1),{t:y}=Ks(),V=jd(),[v,z]=N.useState(!1),[K,Ee]=N.useState(!1),[X,ye]=N.useState(""),[je,$]=N.useState(!1),[De,de]=N.useState(!1),[We,ha]=N.useState(""),[Ta,aa]=N.useState(""),[se,fa]=N.useState(!1),[ll,hl]=N.useState({}),[Rl,El]=N.useState(""),[Aa,wa]=N.useState("");let Ia=Y(I=>I.userManagement.userData),ve=Y(I=>I.userManagement.taskData);const Da=Ni(),ia=bi(),sl=Vs(),ne=sl.state,E=Y(I=>{var ge;return((ge=I.payload.payloadData)==null?void 0:ge.data)||I.payload.payloadData}),qe=Y(I=>I.payload.requestorPayload),ra=Y(I=>I.tabsData.changeFieldsDT),Ca=(E==null?void 0:E.TemplateName)||"",{changePayloadForTemplate:Ye}=ji(Ca),xa=new URLSearchParams(sl.search.split("?")[1]),Ze=xa.get("RequestId"),oa=xa.get("reqBench"),ol=To(to.CURRENT_TASK),$a=typeof ol=="string"?JSON.parse(ol):ol,Ma=!(ve!=null&&ve.taskId||$a!=null&&$a.ATTRIBUTE_5)&&!oa,[rl,za]=N.useState(!1),{customError:tl}=Wd(),{createFCPayload:Tl}=tC(),{createPayloadFromReduxState:ke}=ki({initialReqScreen:Ma,isReqBench:oa,remarks:g,userInput:X,selectedLevel:B}),{createPayloadFromReduxStateArticle:_a}=oT({initialReqScreen:Ma,isReqBench:oa,remarks:g,userInput:X,selectedLevel:B}),[Ya,ta]=N.useState(!1),[as,nl]=N.useState(!1),[ls,dl]=N.useState(!1),[fl,Al]=N.useState(""),ss=((ql=d==null?void 0:d.payloadData)==null?void 0:ql.RequestType)===h.CREATE_WITH_UPLOAD||((Sl=d==null?void 0:d.payloadData)==null?void 0:Sl.RequestType)===h.EXTEND_WITH_UPLOAD||((Pl=d==null?void 0:d.payloadData)==null?void 0:Pl.RequestType)===h.CHANGE_WITH_UPLOAD,{showSnackbar:xe}=wi(),Il=Y(I=>I.request.tabValue),Dl=200,Ha=N.useRef(),[Cl,kl]=N.useState(!1),Ja=()=>{m(!0)},os=()=>{m(!1)},_l=()=>{i(!0)},Xa=()=>{i(!1)},rs=()=>{var I;Aa===va.SAVE?(Xa(),zl()):Aa===((I=va)==null?void 0:I.VALIDATE)&&(Xa(),il())},$l=()=>{U(!0)},ga=()=>{L(""),U(!1)},Nl=(I,ge)=>{const Ae=I.target.value;if(ta(Ae.length>=Dl),Ae.length>0&&Ae[0]===" ")L(Ae.trimStart()),ia(Ii({keyName:"Comments",data:Ae.trimStart()}));else{let Me=Ae;L(Me),ia(Ii({keyName:"Comments",data:Me}))}},Ml=I=>{b(I.target.value),ia(Ii({keyName:"Level",data:I.target.value}))},zl=()=>{var Te,oe,pe,H,Fe,la,_e,Ue,Ua,Qa,Za,Ll,Bl,Ul,Gl,xl,Hl,ml,Fl,Ls,Bs;ga(),n(!0);var I;((Te=d==null?void 0:d.payloadData)==null?void 0:Te.RequestType)===h.CREATE||((oe=d==null?void 0:d.payloadData)==null?void 0:oe.RequestType)===h.CREATE_WITH_UPLOAD?Aa===va.SAVE?I=`/${((pe=Ke)==null?void 0:pe[r==null?void 0:r.module])??Qe}/massAction/createMaterialSaveAsDraft`:I=(Ia==null?void 0:Ia.role)==="Approver"?`/${((H=Ke)==null?void 0:H[r==null?void 0:r.module])??Qe}/massAction/createBasicMaterialsApproved`:`/${((Fe=Ke)==null?void 0:Fe[r==null?void 0:r.module])??Qe}/massAction/createMaterialSubmitForReview`:((la=d==null?void 0:d.payloadData)==null?void 0:la.RequestType)===h.EXTEND_WITH_UPLOAD?Aa===va.SAVE?I=`/${((_e=Ke)==null?void 0:_e[r==null?void 0:r.module])??Qe}${ca.MASS_ACTION.EXTEND_MATERIAL_SAVE_AS_DRAFT}`:I=`/${((Ue=Ke)==null?void 0:Ue[r==null?void 0:r.module])??Qe}${ca.MASS_ACTION.EXTEND_MATERIAL_DIRECT_APPROVED}`:(e===h.CHANGE||e===h.CHANGE_WITH_UPLOAD)&&(Aa===va.SAVE?I=`/${((Ua=Ke)==null?void 0:Ua[r==null?void 0:r.module])??Qe}/massAction/changeMaterialSaveAsDraft`:I=(Ia==null?void 0:Ia.role)==="Approver"?`/${((Qa=Ke)==null?void 0:Qa[r==null?void 0:r.module])??Qe}/massAction/changeBasicMaterialsApproved`:`/${((Za=Ke)==null?void 0:Za[r==null?void 0:r.module])??Qe}/massAction/changeMaterialSubmitForReview`);const ge=na=>{if(na.statusCode>=Zl.STATUS_200&&na.statusCode<Zl.STATUS_300){n(!1);let Ql;(Ia==null?void 0:Ia.role)==="Approver"?Ql=`${r.module} Syndicated successfully in SAP with ${r.module} ID : ${na==null?void 0:na.body.join(", ")}`:Aa===va.SAVE?Ql=na==null?void 0:na.message:Ql=`Request Submitted for Approval with Request ID ${na==null?void 0:na.body}`,xe(Ql,"success"),Ja(),Da("/workspace/MyTasks")}else n(!1),xe(na==null?void 0:na.message,"error");wa("")},Ae=na=>{xe(na==null?void 0:na.message,"error"),n(!1),wa("")};var Me;Me=((Ll=d==null?void 0:d.payloadData)==null?void 0:Ll.RequestType)===h.CREATE||((Bl=d==null?void 0:d.payloadData)==null?void 0:Bl.RequestType)===h.CREATE_WITH_UPLOAD||((Ul=d==null?void 0:d.payloadData)==null?void 0:Ul.RequestType)===h.EXTEND_WITH_UPLOAD?r.module===J.ART?_a(d):ke(d):((Gl=d==null?void 0:d.payloadData)==null?void 0:Gl.RequestType)===h.CHANGE?Ye(!!oa):(Hl=(xl=d==null?void 0:d.payloadData)==null?void 0:xl.data)!=null&&Hl.RequestType||((ml=d==null?void 0:d.payloadData)==null?void 0:ml.RequestType)===h.CHANGE_WITH_UPLOAD?Ye(!0):(Ls=(Fl=d==null?void 0:d.payloadData)==null?void 0:Fl.data)!=null&&Ls.RequestType||((Bs=d==null?void 0:d.payloadData)==null?void 0:Bs.RequestType)===h.CHANGE?r.module===J.ART?_a(d):ke(d):[],ma(I,"post",ge,Ae,Me)},Yl=async I=>{var ge,Ae,Me,Te,oe,pe,H;if(((I==null?void 0:I.MDG_MAT_DYN_BTN_ACTION_TYPE)==="handleValidate"||(I==null?void 0:I.MDG_MAT_DYN_BTN_ACTION_TYPE)==="handleValidate1")&&(((ge=d==null?void 0:d.payloadData)==null?void 0:ge.RequestType)===h.CREATE||((Ae=d==null?void 0:d.payloadData)==null?void 0:Ae.RequestType)===h.CREATE_WITH_UPLOAD||((Me=d==null?void 0:d.payloadData)==null?void 0:Me.RequestType)===h.EXTEND||((Te=d==null?void 0:d.payloadData)==null?void 0:Te.RequestType)===h.EXTEND_WITH_UPLOAD))try{const Fe=await r.validateMaterials();za(Fe)}catch(Fe){tl(Fe);return}he(""),de("success"),hl(I),aa(I.MDG_MAT_DYN_BTN_COMMENT_BOX_NAME),Ee(I.MDG_MAT_DYN_BTN_COMMENT_BOX_INPUT===((oe=es)==null?void 0:oe.MANDATORY)),$(I.MDG_MAT_DYN_BTN_COMMENT_BOX_INPUT===((pe=es)==null?void 0:pe.MANDATORY)||I.MDG_MAT_DYN_BTN_COMMENT_BOX_INPUT==="Optional"),El(I.MDG_MAT_DYN_BTN_ACTION_TYPE),I.MDG_MAT_DYN_BTN_JSON_NAME===Pi.SEND_BACK||I.MDG_MAT_DYN_BTN_JSON_NAME===Pi.CORRECTION?nl(!0):nl(!1),I.MDG_MAT_DYN_BTN_JSON_NAME===Pi.SAP_SYNDICATE?dl(!0):dl(!1),I.MDG_MAT_DYN_BTN_COMMENT_BOX_INPUT===((H=es)==null?void 0:H.MANDATORY)||I.MDG_MAT_DYN_BTN_COMMENT_BOX_INPUT==="Optional"?Na():Ol(I.MDG_MAT_DYN_BTN_ACTION_TYPE,I)},ts=()=>{var Te,oe,pe;ga(),n(!0);const I=e===h.CREATE||e===h.CREATE_WITH_UPLOAD?`/${((Te=Ke)==null?void 0:Te[r==null?void 0:r.module])??Qe}/massAction/createMaterialApprovalSubmit`:e===h.EXTEND||e===h.EXTEND_WITH_UPLOAD?`/${((oe=Ke)==null?void 0:oe[r==null?void 0:r.module])??Qe}/massAction/extendMaterialApprovalSubmit`:`/${((pe=Ke)==null?void 0:pe[r==null?void 0:r.module])??Qe}/massAction/changeMaterialApprovalSubmit`,ge=H=>{H.statusCode>=200&&H.statusCode<300?(n(!1),xe(`Request Submitted for Approval with Request ID ${H==null?void 0:H.body}`,"success"),Da(Zs.MY_TASK)):(n(!1),xe(H==null?void 0:H.message,"error"))},Ae=()=>{n(!1),xe("Failed Submitting Request.","error")};var Me;Me=e===h.CREATE||e===h.EXTEND||e===h.EXTEND_WITH_UPLOAD||e===h.CREATE_WITH_UPLOAD?r.module===J.ART?_a(d):ke(d):Ye(!0),ma(I,"post",ge,Ae,Me)},ns=I=>{var pe,H,Fe,la,_e;ga(),n(!0);var ge=e===h.CREATE||e===h.CREATE_WITH_UPLOAD?`/${((pe=Ke)==null?void 0:pe[r==null?void 0:r.module])??Qe}/massAction/createMaterialApproved`:e===h.EXTEND||e===h.EXTEND_WITH_UPLOAD?`/${((H=Ke)==null?void 0:H[r==null?void 0:r.module])??Qe}/massAction/extendMaterialApproved`:ve.ATTRIBUTE_2===h.FINANCE_COSTING?`/${((Fe=Ke)==null?void 0:Fe[r==null?void 0:r.module])??Qe}/${ca.MASS_ACTION.FINANCE_COSTING_APPROVED}`:`/${((la=Ke)==null?void 0:la[r==null?void 0:r.module])??Qe}/massAction/changeMaterialApproved`;const Ae=Ue=>{Ue.statusCode>=200&&Ue.statusCode<300?(n(!1),xe(I==null?void 0:I.MDG_MAT_DYN_BTN_SNACKBAR_SUCC_MSG,"success"),ia(xh(0)),Da("/workspace/MyTasks")):(n(!1),xe(I==null?void 0:I.MDG_MAT_DYN_BTN_SNACKBAR_FAIL_MSG,"error"))},Me=Ue=>{xe((Ue==null?void 0:Ue.message)||"Failed Submitting Request.","error"),n(!1)};var Te;const oe={requestId:(_e=d==null?void 0:d.payloadData)==null?void 0:_e.RequestId,taskId:(ve==null?void 0:ve.taskId)||"",taskName:(ve==null?void 0:ve.taskDesc)||"",comments:g||X,creationDate:ve!=null&&ve.createdOn?ie(ve==null?void 0:ve.createdOn):null,dueDate:ve!=null&&ve.criticalDeadline?ie(ve==null?void 0:ve.criticalDeadline):null};Te=e===h.CREATE||e===h.CREATE_WITH_UPLOAD||e===h.EXTEND||e===h.EXTEND_WITH_UPLOAD?r.module===J.ART?_a(d):ke(d):Ye(!0),ma(ge,"post",Ae,Me,ve.ATTRIBUTE_2===h.FINANCE_COSTING?oe:Te)},Jl=()=>{var Te,oe,pe;ga(),n(!0);const I=e===h.CREATE||e===h.CREATE_WITH_UPLOAD?`/${((Te=Ke)==null?void 0:Te[r==null?void 0:r.module])??Qe}${ca.MASS_ACTION.MATERIAL_SEND_TO_LEVEL}`:e===h.EXTEND||e===h.EXTEND_WITH_UPLOAD?`/${((oe=Ke)==null?void 0:oe[r==null?void 0:r.module])??Qe}${ca.MASS_ACTION.MATERIAL_SEND_TO_LEVEL}`:`/${((pe=Ke)==null?void 0:pe[r==null?void 0:r.module])??Qe}${ca.MASS_ACTION.MATERIAL_SEND_TO_LEVEL}`,ge=e===h.CREATE||e===h.CREATE_WITH_UPLOAD||e===h.EXTEND||e===h.EXTEND_WITH_UPLOAD?r.module===J.ART?_a(d):ke(d):Ye(!0);ma(I,"post",H=>{(H==null?void 0:H.statusCode)===Zl.STATUS_200?(xe(H.message,"success"),ia(Li({data:{}})),Da(Zs.MY_TASK)):xe(H.message||ho.WENT_WRONG,"error"),n(!1)},H=>{xe(H.error||ho.WENT_WRONG,"error"),n(!1)},ge)},Xl=()=>{var Te,oe,pe;ga(),n(!0);const I=e===h.CREATE||e===h.CREATE_WITH_UPLOAD?`/${((Te=Ke)==null?void 0:Te[r==null?void 0:r.module])??Qe}${ca.MASS_ACTION.MATERIAL_SEND_TO_LEVEL}`:e===h.EXTEND||e===h.EXTEND_WITH_UPLOAD?`/${((oe=Ke)==null?void 0:oe[r==null?void 0:r.module])??Qe}${ca.MASS_ACTION.MATERIAL_SEND_TO_LEVEL}`:`/${((pe=Ke)==null?void 0:pe[r==null?void 0:r.module])??Qe}${ca.MASS_ACTION.MATERIAL_SEND_TO_LEVEL}`,ge=e===h.CREATE||e===h.CREATE_WITH_UPLOAD||e===h.EXTEND||e===h.EXTEND_WITH_UPLOAD?r.module===J.ART?_a(d):ke(d):Ye(!0);ma(I,"post",H=>{(H==null?void 0:H.statusCode)===Zl.STATUS_200?(xe(H.message,"success"),ia(Li({data:{}})),Da(Zs.MY_TASK)):xe(H.error,"error"),n(!1)},H=>{xe(H.error,"error"),n(!1)},ge)},Vl=()=>{var Te,oe,pe;ga(),n(!0);const I=e===h.CREATE||e===h.CREATE_WITH_UPLOAD?`/${((Te=Ke)==null?void 0:Te[r==null?void 0:r.module])??Qe}${ca.MASS_ACTION.CREATE_MATERIAL_REJECTION}`:e===h.EXTEND||e===h.EXTEND_WITH_UPLOAD?`/${((oe=Ke)==null?void 0:oe[r==null?void 0:r.module])??Qe}${ca.MASS_ACTION.EXTEND_MATERIAL_REJECTION}`:`/${((pe=Ke)==null?void 0:pe[r==null?void 0:r.module])??Qe}${ca.MASS_ACTION.CHANGE_MATERIAL_REJECTION}`,ge=e===h.CREATE||e===h.CREATE_WITH_UPLOAD||e===h.EXTEND||e===h.EXTEND_WITH_UPLOAD?r.module===J.ART?_a(d):ke(d):Ye(!0);ma(I,"post",H=>{(H==null?void 0:H.statusCode)===Zl.STATUS_200?(xe(H.message,"success"),ia(Li({data:{}})),Da(Zs.MY_TASK)):xe(H.error,"error"),n(!1)},H=>{xe(H.error,"error"),n(!1)},ge)},il=I=>{var oe,pe;n(!0);const ge=(ve==null?void 0:ve.ATTRIBUTE_2)===h.FINANCE_COSTING?`/${((oe=Ke)==null?void 0:oe[r==null?void 0:r.module])??Qe}${ca.MASS_ACTION.VALIDATE_FINANCE_COSTING}?requestId=${Ze==null?void 0:Ze.slice(3)}`:`/${((pe=Ke)==null?void 0:pe[r==null?void 0:r.module])??Qe}${ca.MASS_ACTION.VALIDATE_MATERIAL}`,Ae=(ve==null?void 0:ve.ATTRIBUTE_2)===h.FINANCE_COSTING?Tl():e===h.CREATE||e===h.CREATE_WITH_UPLOAD||e===h.EXTEND||e===h.EXTEND_WITH_UPLOAD?r.module===J.ART?_a(d):ke(d):e===h.CHANGE||e===h.CHANGE_WITH_UPLOAD?oa&&Ze?Ye(!0):!oa&&!Ze?Ye(!1):!oa&&Ze?Ye(!0):[]:[];ma(ge,"post",H=>{if(n(!1),(H==null?void 0:H.statusCode)===Zl.STATUS_200){if(xe((H==null?void 0:H.message)||(I==null?void 0:I.MDG_MAT_DYN_BTN_SNACKBAR_SUCC_MSG),"success"),(Ma||oa)&&(e===h.CHANGE||e===h.CHANGE_WITH_UPLOAD||e===h.EXTEND_WITH_UPLOAD||e===h.EXTEND||e===h.CREATE_WITH_UPLOAD)){Da(Zs.REQUEST_BENCH);return}Da(Zs.MY_TASK)}else xe((H==null?void 0:H.message)||(I==null?void 0:I.MDG_MAT_DYN_BTN_SNACKBAR_FAIL_MSG)||"Validation failed.","error")},H=>{xe((H==null?void 0:H.message)||(I==null?void 0:I.MDG_MAT_DYN_BTN_SNACKBAR_FAIL_MSG),"error"),n(!1)},Ae)},bl=()=>{var Te,oe,pe;ga(),n(!0);const I=e===h.CREATE||e===h.CREATE_WITH_UPLOAD?`/${((Te=Ke)==null?void 0:Te[r==null?void 0:r.module])??Qe}/massAction/createMaterialApprovalSubmit`:e===h.EXTEND||e===h.EXTEND_WITH_UPLOAD?`/${((oe=Ke)==null?void 0:oe[r==null?void 0:r.module])??Qe}/massAction/extendMaterialSubmitForReview`:`/${((pe=Ke)==null?void 0:pe[r==null?void 0:r.module])??Qe}/massAction/changeMaterialApprovalSubmit`,ge=H=>{H.statusCode>=200&&H.statusCode<300?(n(!1),xe(`Request Submitted for Approval with Request ID ${H==null?void 0:H.body}`,"success"),Ja(),ia(xh(0)),Da("/workspace/MyTasks")):xe(H==null?void 0:H.message,"error")},Ae=H=>{xe((H==null?void 0:H.error)||"Failed Submitting Request.","error"),n(!1)};var Me;Me=e===h.CREATE||e===h.CREATE_WITH_UPLOAD||e===h.EXTEND||e===h.EXTEND_WITH_UPLOAD?r.module===J.ART?_a(d):ke(d):Ye(!0),ma(I,"post",ge,Ae,Me),l(!0),Ja()},Kl=()=>{var Te;ga(),n(!0);const I=`/${((Te=Ke)==null?void 0:Te[r==null?void 0:r.module])??Qe}${ca.MASS_ACTION.EXTEND_MATERIAL_SAVE_AS_DRAFT}`,ge=oe=>{oe.statusCode===Zl.STATUS_200?(n(!1),xe(oe==null?void 0:oe.message,"success"),Da(Zs.REQUEST_BENCH)):(n(!1),xe(oe==null?void 0:oe.message,"error"))},Ae=oe=>{xe(oe==null?void 0:oe.error,"error"),n(!1)};let Me;Me=r.module===J.ART?_a(d):ke(d),ma(I,"post",ge,Ae,Me)},Ol=(I,ge)=>{switch(I){case"handleSubmitForApproval":ts();break;case"handleSubmitForReview":bl();break;case"handleSendBack":Jl();break;case"handleCorrection":Xl();break;case"handleReject":Vl();break;case"Validate":Ge(ge);break;case"handleValidate":Ge(ge);break;case"handleSAPSyndication":ns(ge);break;case"handleDraft":Kl();break;case"handleSubmit":bl();break;case"handleReview":S();break;default:console.log("Unknown action type")}},S=()=>{ga(),r.setIsAccepted&&r.setIsAccepted(!0)},Ge=I=>{var ge,Ae;wa((ge=va)==null?void 0:ge.VALIDATE),re(y((Ae=Hd)==null?void 0:Ae.VALIDATE_MSG)),e===h.CREATE||e===h.EXTEND||e===h.CREATE_WITH_UPLOAD||e===h.EXTEND_WITH_UPLOAD||(ve==null?void 0:ve.ATTRIBUTE_2)===h.FINANCE_COSTING?il(I):Je(I)},Je=I=>{Array.isArray(o)?Sa(I):typeof o=="object"&&qa(I)},pa=()=>{const I=ra==null?void 0:ra["Config Data"],ge={};return Object.entries(I).forEach(([Ae,Me])=>{const Te=Me.filter(oe=>{var pe;return oe.visibility===((pe=es)==null?void 0:pe.MANDATORY)}).map(oe=>({jsonName:oe.jsonName,fieldName:oe.fieldName}));if(!(Te!=null&&Te.some(oe=>oe.jsonName==="Material"))){const oe=Me.find(pe=>pe.jsonName==="Material");oe&&Te.push({jsonName:oe.jsonName,fieldName:oe.fieldName})}(Te==null?void 0:Te.length)>0&&(ge[Ae]=Te)}),ge},Ra=(I,ge)=>{var Ae,Me;if(Array.isArray(o)){const Te=Ca===((Ae=Ga)==null?void 0:Ae.LOGISTIC)?[...ge,{jsonName:"AltUnit",fieldName:"Alternative Unit of Measure"}]:Ca===((Me=Ga)==null?void 0:Me.UPD_DESC)?[...ge,{jsonName:"Langu",fieldName:"Language"}]:ge,oe={};return I==null||I.forEach((pe,H)=>{var la;const Fe=(la=Te==null?void 0:Te.filter(_e=>!pe[_e==null?void 0:_e.jsonName]||pe[_e==null?void 0:_e.jsonName]===""))==null?void 0:la.map(_e=>_e==null?void 0:_e.fieldName);(Fe==null?void 0:Fe.length)>0&&(oe[H]={id:pe.id,slNo:pe.slNo,missingFields:Fe})}),oe}else if(typeof o=="object"){let Te={},oe=0;return Object.keys(I).forEach(pe=>{I[pe].forEach(H=>{var la;const Fe=(la=ge[pe])==null?void 0:la.filter(_e=>!H[_e.jsonName]||H[_e.jsonName]==="").map(_e=>_e.fieldName);Fe.length>0&&(Te[oe]={id:H.id,slNo:H.slNo,type:H.type,missingFields:Fe},oe++)})}),Te}},qa=I=>{var Te,oe,pe,H;const ge=Object.fromEntries(Object.entries(o).map(([Fe,la])=>[Fe,la.filter(_e=>{var Ue;return(Ue=p==null?void 0:p[Fe])==null?void 0:Ue.includes(_e.id)})])),Ae=pa(),Me=Ra(ge,Ae);if(ia(Gh(Me)),Object.keys(Me).length>0){const Fe=Object.keys(Me).map(Ue=>{var Ua,Qa,Za;return{"Table Name":(Ua=Me[Ue])==null?void 0:Ua.type,"Sl. No":(Qa=Me[Ue])==null?void 0:Qa.slNo,"Missing Fields":(Za=Me[Ue].missingFields)==null?void 0:Za.join(", ")}});le(!0),de("danger"),aa("Please Fill All the Mandatory Fields : ");const la=(Te=Object.keys(Fe[0]))==null?void 0:Te.map(Ue=>({field:Ue,headerName:(Ue==null?void 0:Ue.charAt(0).toUpperCase())+(Ue==null?void 0:Ue.slice(1)),flex:Ue==="Sl. No"?.5:Ue==="Missing Fields"?3:1.5,align:"center",headerAlign:"center"}));j(la);const _e=Fe==null?void 0:Fe.map(Ue=>({...Ue,id:Gi()}));F(_e),P(!0),Na(),ia(Ri(!0))}else{if(e===((oe=h)==null?void 0:oe.CHANGE)||e===((pe=h)==null?void 0:pe.CHANGE_WITH_UPLOAD)){if(!Ze||Ze&&qe&&((H=Object==null?void 0:Object.keys(qe))!=null&&H.length)){_l();return}il(I);return}x("success"),he("Data Validated Successfully"),Ja(),ia(Ri(!1)),r==null||r.setCompleted([!0,!0])}},Sa=I=>{var Me,Te,oe,pe;const ge=o==null?void 0:o.filter(H=>p==null?void 0:p.includes(H.id)),Ae=Ra(ge,ra==null?void 0:ra["Mandatory Fields"]);if(ia(Gh(Ae)),Object.keys(Ae).length>0){const H=Object.keys(Ae).map(_e=>{var Ue,Ua;return{"Sl. No":(Ue=Ae[_e])==null?void 0:Ue.slNo,"Missing Fields":(Ua=Ae[_e].missingFields)==null?void 0:Ua.join(", ")}});le(!0),de("danger"),aa("Please Fill All the Mandatory Fields : ");const Fe=(Me=Object.keys(H[0]))==null?void 0:Me.map(_e=>({field:_e,headerName:(_e==null?void 0:_e.charAt(0).toUpperCase())+(_e==null?void 0:_e.slice(1)),flex:_e==="Sl. No"?.5:_e==="Missing Fields"?3:1,align:"center",headerAlign:"center"}));j(Fe);const la=H==null?void 0:H.map(_e=>({..._e,id:Gi()}));F(la),P(!0),Na(),ia(Ri(!0))}else{if(e===((Te=h)==null?void 0:Te.CHANGE)||e===((oe=h)==null?void 0:oe.CHANGE_WITH_UPLOAD)){if(!Ze||Ze&&qe&&((pe=Object==null?void 0:Object.keys(qe))!=null&&pe.length)){_l();return}il(I);return}x("success"),he("Data Validated Successfully"),Ja(),ia(Ri(!1)),r==null||r.setCompleted([!0,!0])}},Wa=()=>{var I;if(K&&!X){z(!0);return}else if(r.showWfLevels&&B===""){kl(!0);return}else fl==="scheduleSyndication"?Ha!=null&&Ha.current&&((I=Ha==null?void 0:Ha.current)==null||I.handlePriorityDialogClickOpen()):Ol(Rl,ll);Pa()},Pa=()=>{fa(!1),ye(""),z(!1),Ee(!1),kl(!1)},Na=()=>{fa(!0)};function cl(){$l(),wa("")}const ul=()=>{cl()};return D(Fa,{children:[t(Fi,{sx:{position:"fixed",bottom:0,left:0,right:0,zIndex:1e3},elevation:2,children:D(Jh,{className:"container_BottomNav",showLabels:!0,sx:{display:"flex",justifyContent:"space-between",alignItems:"center",gap:1,width:"100%"},children:[(!Ze||oa&&(ne==null?void 0:ne.reqStatus)===((gs=pl)==null?void 0:gs.DRAFT))&&(E==null?void 0:E.RequestType)===h.CHANGE&&(E==null?void 0:E.TemplateName)===((ps=Ga)==null?void 0:ps.SET_DNU)&&t(ue,{sx:{flex:2,marginLeft:"90px"},children:D("span",{children:[t("strong",{children:"Note"}),": All default values for ",t("strong",{children:"Set To DNU"})," template will be fetched after ",t("strong",{children:"Validation"}),"."]})}),D(ue,{sx:{display:"flex",gap:1},children:[e!==h.EXTEND_WITH_UPLOAD&&e!==h.EXTEND&&t(al,{children:!Ze||oa&&((Rs=ro)!=null&&Rs.includes(ne==null?void 0:ne.reqStatus))?D(al,{children:[t(da,{variant:"contained",color:"primary",onClick:()=>{var I,ge;if(wa(va.SAVE),re(y((I=Hd)==null?void 0:I.SAVE_AS_DRAFT_MSG)),(E==null?void 0:E.RequestType)===h.CHANGE&&(!Ze||Ze&&qe&&((ge=Object==null?void 0:Object.keys(qe))!=null&&ge.length))){_l();return}$l()},children:y("Save As Draft")}),((Es=d==null?void 0:d.payloadData)==null?void 0:Es.RequestType)===h.CREATE&&Il===md.REQUEST_DETAILS&&t(Wl,{title:Nf.VALIDATE_MANDATORY,children:t(da,{variant:"contained",color:"primary",onClick:r==null?void 0:r.validateMaterials,children:y("Validate")})}),Il===md.REQUEST_DETAILS&&((E==null?void 0:E.RequestType)===h.CHANGE||(E==null?void 0:E.RequestType)===h.CHANGE_WITH_UPLOAD||(E==null?void 0:E.RequestType)===h.CREATE_WITH_UPLOAD||(E==null?void 0:E.RequestType)===h.EXTEND_WITH_UPLOAD)?t(al,{children:t(da,{variant:"contained",color:"primary",onClick:Ge,children:y("Validate")})}):t(al,{children:Il===md.PREVIEW&&t(da,{variant:"contained",color:"primary",onClick:ul,disabled:(E==null?void 0:E.RequestType)===((As=h)==null?void 0:As.CHANGE)||(E==null?void 0:E.RequestType)===((Is=h)==null?void 0:Is.CHANGE_WITH_UPLOAD)||(E==null?void 0:E.RequestType)===h.CREATE_WITH_UPLOAD||(E==null?void 0:E.RequestType)===h.EXTEND_WITH_UPLOAD?(E==null?void 0:E.RequestStatus)!==((Ds=pl)==null?void 0:Ds.VALIDATED_REQUESTOR):r==null?void 0:r.submitForApprovalDisabled,children:y("Submit")})})]}):null}),((E==null?void 0:E.RequestType)===h.EXTEND||(E==null?void 0:E.RequestType)===h.EXTEND_WITH_UPLOAD&&(!Ze||oa&&((_s=ro)==null?void 0:_s.includes(ne==null?void 0:ne.reqStatus))||!oa&&Ze)||!oa&&Ze)&&((Ns=r==null?void 0:r.filteredButtons)==null?void 0:Ns.map((I,ge)=>{var Fe,la,_e,Ue,Ua,Qa,Za,Ll,Bl,Ul,Gl,xl,Hl,ml,Fl;const{MDG_MAT_DYN_BTN_JSON_NAME:Ae,MDG_MAT_DYN_BTN_BUTTON_STATUS:Me}=I,Te=Ae==="FinalSyndication"||Ae==="SubmitRequestor"||Ae==="ArticleActivation",oe=Ae==="Forward"||Ae==="SubmitRequestor",pe=((Fe=R==null?void 0:R.requestHeaderData)==null?void 0:Fe.RequestStatus)==="Validated-MDM"||((la=R==null?void 0:R.requestHeaderData)==null?void 0:la.RequestStatus)==="Validated-Requestor"||((_e=R==null?void 0:R.requestHeaderData)==null?void 0:_e.RequestStatus)==="Validated-Intermediate"||(E==null?void 0:E.RequestStatus)==="Validated-MDM"||(E==null?void 0:E.RequestStatus)==="Validated-Requestor"||(E==null?void 0:E.RequestStatus)==="Validated-Intermediate"||((Ue=R==null?void 0:R.childRequestHeaderData)==null?void 0:Ue.RequestStatus)==="Validated-MDM"||((Ua=R==null?void 0:R.childRequestHeaderData)==null?void 0:Ua.RequestStatus)==="Validated-Requestor"||((Qa=R==null?void 0:R.childRequestHeaderData)==null?void 0:Qa.RequestStatus)==="Validated-Intermediate"||((Za=r==null?void 0:r.childRequestHeaderData)==null?void 0:Za.RequestStatus)==="Validated-MDM"||((Ll=r==null?void 0:r.childRequestHeaderData)==null?void 0:Ll.RequestStatus)==="Validated-Requestor"||((Bl=r==null?void 0:r.childRequestHeaderData)==null?void 0:Bl.RequestStatus)==="Validated-Intermediate";let H=Me==="DISABLED";return Te&&pe&&(H=!1),(oe&&((E==null?void 0:E.RequestType)===((Ul=h)==null?void 0:Ul.CREATE)||(E==null?void 0:E.RequestType)===((Gl=h)==null?void 0:Gl.CREATE_WITH_UPLOAD))&&!(r!=null&&r.submitForApprovalDisabled)||((E==null?void 0:E.RequestType)===((xl=h)==null?void 0:xl.CHANGE)||(E==null?void 0:E.RequestType)===((Hl=h)==null?void 0:Hl.CHANGE_WITH_UPLOAD)||(E==null?void 0:E.RequestType)===((ml=h)==null?void 0:ml.EXTEND)||(E==null?void 0:E.RequestType)===((Fl=h)==null?void 0:Fl.EXTEND_WITH_UPLOAD))&&oe&&pe)&&(H=!1),t(da,{variant:"contained",size:"small",sx:{...Xh,mr:1},disabled:H||Q,onClick:()=>Yl(I),children:I.MDG_MAT_DYN_BTN_BUTTON_NAME},ge)}))]})]})}),t(Qh,{dialogState:se,openReusableDialog:Na,closeReusableDialog:Pa,dialogTitle:Ta,dialogMessage:We,handleDialogConfirm:Wa,dialogOkText:"OK",dialogSeverity:De,showCancelButton:!0,showInputText:je,inputText:X,blurLoading:Q,setInputText:ye,mandatoryTextInput:K,remarksError:v,isTable:q,tableColumns:w,tableRows:G,isShowWFLevel:(r==null?void 0:r.showWfLevels)&&as,isSyndicationBtn:ls,selectedLevel:B,handleLevelChange:Ml,workFlowLevels:r.workFlowLevels,setSyndicationType:Al,syndicationType:fl,isMassSyndication:ss,wfError:Cl}),t(tT,{ref:Ha,dialogTitle:Ta,setDialogTitle:aa,messageDialogMessage:Oe,setMessageDialogMessage:he,messageDialogSeverity:De,setMessageDialogSeverity:de,handleMessageDialogClickOpen:Na,blurLoading:Q,setBlurLoading:n,handleMessageDialogClose:Pa,createPayloadFromReduxState:ke,successMsg:O,setSuccessMsg:l,setTextInput:$,inputText:je,handleSnackBarOpen:Ja,taskData:ve,userData:Ia,currentButtonState:ll,requestType:e,module:r.module===J.ART?J.ART:J.MAT}),t(_C,{open:te,onClose:Xa,handleOk:rs,message:Ce}),Oe&&t(kd,{openSnackBar:ee,alertMsg:Oe,alertType:u,handleSnackBarClose:os}),t(Zh,{blurLoading:Q,loaderMessage:W}),D(Js,{hideBackdrop:!1,elevation:2,PaperProps:{sx:{boxShadow:"none"}},open:T,onClose:ga,maxWidth:"xl",children:[D(so,{sx:{justifyContent:"space-between",alignItems:"center",height:"max-content",padding:".5rem",paddingLeft:"1rem",backgroundColor:V.palette.primary.light,display:"flex"},children:[t(Be,{variant:"h6",children:Aa===va.SAVE?"Save As Draft":"Remarks"}),t(jl,{sx:{width:"max-content"},onClick:ga,children:t(Fd,{})})]}),t(Xs,{sx:{padding:".5rem 1rem"},children:Aa!==va.SAVE?t(Fa,{sx:{marginTop:"16px"},children:t(ue,{sx:{minWidth:400},children:D(mi,{sx:{height:"auto"},fullWidth:!0,children:[t(Vh,{autoFocus:!0,sx:{backgroundColor:"#F5F5F5","& .MuiOutlinedInput-root":{"& fieldset":{borderColor:Ya?(bs=(Ms=me)==null?void 0:Ms.error)==null?void 0:bs.dark:"rgba(0, 0, 0, 0.23)"},"&:hover fieldset":{borderColor:Ya?(ys=(Os=me)==null?void 0:Os.error)==null?void 0:ys.dark:"rgba(0, 0, 0, 0.23)"},"&.Mui-focused fieldset":{borderColor:Ya?(qs=(vs=me)==null?void 0:vs.error)==null?void 0:qs.dark:V.palette.primary.dark}}},value:g,onChange:Nl,inputProps:{maxLength:Dl},multiline:!0,placeholder:"Enter Remarks"}),t(Kh,{sx:{textAlign:"right",color:Ya?(Ps=(Ss=me)==null?void 0:Ss.error)==null?void 0:Ps.dark:"rgba(0, 0, 0, 0.6)",marginTop:"4px"},children:`${(g==null?void 0:g.length)||0}/${Dl}`})]})})}):t(ue,{sx:{margin:"15px"},children:t(Be,{sx:{fontWeight:"200"},children:Hd.DRAFT_MESSAGE})})}),D(oo,{sx:{display:"flex",justifyContent:"end"},children:[t(da,{variant:"outlined",color:"warning",sx:{width:"max-content",textTransform:"capitalize"},onClick:ga,children:y("Cancel")}),t(da,{type:"save",disabled:Q,onClick:zl,variant:"contained",children:Aa===va.SAVE?"Yes":"Submit"})]})]})]})},cT=(r,d,R,o,p)=>{const[e,O]=N.useState([]),[l,u]=N.useState([]),x=Y(W=>W.payload.payloadData),m=new URLSearchParams(location.search).get("RequestType"),Ce=Y(W=>{var c,T;return(T=(c=W==null?void 0:W.userManagement)==null?void 0:c.taskData)==null?void 0:T.ATTRIBUTE_2}),[re,Oe]=N.useState(!1),{customError:he}=Wd(),Q=(x==null?void 0:x.RequestType)||Ce||m;N.useEffect(()=>{Q&&n()},[r,Q]);const n=()=>{const W={decisionTableId:null,decisionTableName:"MDG_MAT_DYN_BUTTON_CONFIG",version:"v4",rulePolicy:null,validityDate:null,conditions:[{"MDG_CONDITIONS.MDG_MAT_DYN_BTN_MODULE_NAME":p||"Material","MDG_CONDITIONS.MDG_MAT_DYN_BTN_REQUEST_TYPE":Q}],systemFilters:null,systemOrders:null,filterString:null},c=g=>{var L,B;if(g.statusCode===Zl.STATUS_200){const b=To(to.CURRENT_TASK,!0,{}),q=(r==null?void 0:r.taskDesc)||(b==null?void 0:b.taskDesc),G=(((B=(L=g==null?void 0:g.data)==null?void 0:L.result[0])==null?void 0:B.MDG_MAT_DYN_BUTTON_CONFIG)||[]).filter(w=>(w==null?void 0:w.MDG_MAT_DYN_BTN_TASK_NAME)===(q??Mf.INITIATOR));O(G),(G.find(w=>w.MDG_MAT_DYN_BTN_BUTTON_NAME===o.SEND_BACK)||G.find(w=>w.MDG_MAT_DYN_BTN_BUTTON_NAME===o.CORRECTION))&&Oe(!0)}},T=g=>{he("Dynamic Button Fetch Error:",g)},U=d.environment==="localhost"?`/${R}${ca.INVOKE_RULES.LOCAL}`:`/${R}${ca.INVOKE_RULES.PROD}`;ma(U,"post",c,T,W)};return N.useEffect(()=>{const W=[...e].sort((c,T)=>{const U=$h[c.MDG_MAT_DYN_BTN_BUTTON_NAME]??999,g=$h[T.MDG_MAT_DYN_BTN_BUTTON_NAME]??999;return U-g});u(W)},[e]),{extendFilteredButtons:l,showWfLevels:re}},bC=r=>{var o,p;const d=((p=(o=r==null?void 0:r.split("."))==null?void 0:o.pop())==null?void 0:p.toLowerCase())||"",R={fontSize:"small",sx:{mr:1}};switch(d){case"xlsx":case"xls":case"csv":return t(Yf,{sx:{color:me.icon.excel}});case"pdf":return t(xf,{...R,sx:{color:me.icon.pdf}});case"doc":case"docx":return t(mh,{...R,sx:{color:me.icon.doc}});case"ppt":case"pptx":return t(mh,{...R,sx:{color:me.icon.ppt}});default:return t(Gf,{...R,sx:{color:me.icon.file}})}},_g=({attachmentsData:r=[],requestIdHeader:d="",pcNumber:R="",disabled:o=!1,requestStatus:p,module:e,artifactName:O})=>{var ye,je;const[l,u]=N.useState({}),x=Y($=>$.appSettings),ee=Y($=>$.payload.dynamicKeyValues),[m,Ce]=N.useState([]),[re,Oe]=N.useState([]),he=bi(),{t:Q}=Ks(),n=jd(),W=Y($=>$.userManagement.taskData),c=Y($=>$.applicationConfig),T=Y($=>$.request.materialRows),[U,g]=N.useState(!1),L=wd[e]||(()=>({})),B=Y(L),b=Y($=>{var De,de;return(de=(De=$==null?void 0:$.payload)==null?void 0:De.dynamicKeyValues)==null?void 0:de.childRequestHeaderData}),[q,P]=N.useState(""),G=Vs(),w=new URLSearchParams(G.search).get("reqBench"),j=G==null?void 0:G.state,Z=(b==null?void 0:b.ChildRequestId)||(B==null?void 0:B.childRequestId),{extendFilteredButtons:le}=cT(W,c,xi,va),{data:te}=sT({requestId:d,hasAnyChildRequestId:Z});let i;const y=[Ea.HANDLE_SEND_BACK,Ea.HANDLE_VALIDATE,Ea.HANDLE_CORRECTION,Ea.HANDLE_SUBMIT_FOR_APPROVAL,Ea.HANDLE_SAP_SYNDICATION,Ea.HANDLE_SUBMIT_FOR_REVIEW,Ea.HANDLE_SUBMIT];(B==null?void 0:B.RequestType)===h.EXTEND||(B==null?void 0:B.RequestType)===h.EXTEND_WITH_UPLOAD?i=Di(le,y):i=[];const V=()=>{g(!0)},v=()=>{g(!1)},z=!ro.includes(p)||d&&!w,K={primary:me.blue.main,light:me.text.offWhite,accent:me.primary.grey,text:me.text.charcoal,secondaryText:me.text.greyBlue,background:me.background.paper},Ee=[{field:"id",headerName:"Document ID",flex:1.2,hideable:!1,hidden:!0},{field:"attachmentType",headerName:Q("Attachment Type"),flex:1.5,renderCell:$=>{var De;return t(Hh,{label:$.value,size:"small",sx:{backgroundColor:(De=me)==null?void 0:De.reportTile.lightBlue,color:me.primary.lightPlus,fontWeight:"medium"}})}},{field:"docName",headerName:Q("Document Name"),flex:2,renderCell:$=>D(Fa,{direction:"row",spacing:1,alignItems:"center",children:[bC($.value),t(Be,{variant:"body2",children:$.value})]})},{field:"uploadedOn",headerName:Q("Uploaded On"),flex:1,align:"center",headerAlign:"center"},{field:"uploadedBy",headerName:Q("Uploaded By"),sortable:!1,flex:1},{field:"view",headerName:Q("View"),sortable:!1,filterable:!1,align:"center",headerAlign:"center",renderCell:$=>{var De,de;return t(jl,{size:"small",sx:{color:me.icon.matView,"&:hover":{backgroundColor:"rgba(2, 136, 209, 0.1)"}},children:t(Xf,{index:$.row.id,name:((De=$==null?void 0:$.row)==null?void 0:De.docName)||((de=$==null?void 0:$.row)==null?void 0:de.fileName),documentViewUrl:$.row.documentViewUrl})})}},{field:"action",headerName:Q("Action"),sortable:!1,filterable:!1,align:"center",headerAlign:"center",flex:1,renderCell:$=>{var De,de,We,ha,Ta,aa;return D(Fa,{direction:"row",spacing:0,children:[t(jl,{size:"small",sx:{color:(De=me)==null?void 0:De.icon.matDownload,"&:hover":{backgroundColor:"rgba(46, 125, 50, 0.1)"}},children:t(Vf,{index:$.row.id,name:((de=$==null?void 0:$.row)==null?void 0:de.docName)||((We=$==null?void 0:$.row)==null?void 0:We.fileName)})}),t(jl,{size:"small",sx:{"&:hover":{backgroundColor:"rgba(241, 18, 18, 0.1)"}},children:t(Kf,{index:$.row.id,name:$.row.docName||((ha=$==null?void 0:$.row)==null?void 0:ha.fileName),setSnackbar:g,setopenSnackbar:g,setMessageDialogMessage:P,handleSnackbarOpen:V,setDownloadLoader:u,DownloadLoader:l,requestId:d,hasAnyChildRequestId:Z,iconColor:(aa=(Ta=me)==null?void 0:Ta.icon)==null?void 0:aa.delete})})]})}}];N.useEffect(()=>{var De,de;if(w&&(j==null?void 0:j.reqStatus)===((De=pl)==null?void 0:De.SYNDICATED_IN_SAP_DIRECT)){let We=[];var $={id:j.requestId,comment:((de=ee==null?void 0:ee.otherPayloadData)==null?void 0:de.Comments)||"",user:j.createdBy,createdAt:j.changedOnAct,taskName:"Direct Syndication Task",role:"Requestor"};We.push($),Oe(We);return}X()},[]),N.useEffect(()=>{var $;if(te){const De=te.documentDetailDtoList.map(de=>({id:de.documentId,docType:de.fileType,docName:de.fileName,uploadedOn:Ai(de.docCreationDate).format(x.dateFormat),uploadedBy:de.createdBy,attachmentType:de.attachmentType,documentViewUrl:de.documentViewUrl}));he(eT(($=De[0])==null?void 0:$.attachmentType)),Ce(De)}},[te]);const X=()=>{const{destination:$}=Mi(e);let De=d,de=ha=>{var Ta=[];ha.body.forEach(aa=>{var se={id:aa.requestId,comment:aa.comment,user:aa.createdByUser,createdAt:aa.updatedAt,taskName:aa.taskName,role:aa.createdByRole};Ta.push(se)}),Oe(Ta)},We=ha=>{console.log(ha)};ma(`/${$}/${ca.TASK_ACTION_DETAIL.TASKDETAILS_FOR_REQUESTID}?requestId=${De}`,"get",de,We)};return D("div",{children:[t(lo,{container:!0,spacing:2,sx:{padding:"10px",margin:0},children:D(lo,{item:!0,md:12,sx:{backgroundColor:me.primary.white,maxHeight:"max-content",height:"max-content",borderRadius:"8px",border:"1px solid #E0E0E0",mt:2,boxShadow:"0px 2px 14px 0px rgba(48, 38, 185, 0.10)",...aT},children:[t(lo,{container:!0,sx:{display:"flex",justifyContent:"space-between",flexDirection:"row",alignItems:"center"},children:D(ue,{sx:{display:"flex",justifyContent:"space-between",flexDirection:"row",paddingBottom:"12px",alignItems:"space-between",width:"100%"},children:[t(Be,{variant:"h6",children:t("strong",{children:Q("Attachments")})}),!o&&(r==null?void 0:r.map(($,De)=>t(CC,{title:"Material",module:e,useMetaData:!1,artifactId:`${$.MDG_ATTACHMENTS_NAME}_${R}`,artifactName:O,attachmentType:$.MDG_ATTACHMENTS_NAME,requestId:d,attachments:m})))]})}),m.length>0?t(bf,{width:"100%",rows:m,columns:Ee,hideFooter:!1,getRowIdValue:"id",autoHeight:!0,disableSelectionOnClick:!0,stopPropagation_Column:"action"}):D(Fa,{alignItems:"center",spacing:2,sx:{py:4},children:[t(zf,{sx:{fontSize:40,color:K.accent,transform:"rotate(90deg)"}}),t(Be,{variant:"body2",color:K.secondaryText,children:Q("No Attachments Found")})]}),re.length>0&&D(ue,{sx:{maxWidth:"100%",bgcolor:K.background,borderRadius:"12px",padding:"12px",marginTop:"12px",boxShadow:"0 4px 12px rgba(0,0,0,0.05)"},children:[t(Fa,{direction:"row",alignItems:"center",spacing:1,mb:3,children:t(Be,{variant:"h6",fontWeight:"bold",color:K.text,children:Q("Comments")})}),t(Of,{position:"right",sx:{p:0,m:0,"& .MuiTimelineItem-root:before":{flex:0,padding:0}},children:re.map(($,De)=>{var de;return D(yf,{sx:{},children:[D(vf,{children:[t(qf,{sx:{bgcolor:K.light,boxShadow:"0 2px 4px rgba(0,0,0,0.1)"},children:t(Sf,{size:18,sx:{color:n.palette.primary.main}})}),t(Pf,{sx:{width:"2.2px"}})]}),t(Lf,{sx:{py:"12px",px:2},children:t(eo,{variant:"outlined",sx:{borderRadius:"12px",borderColor:K.accent,background:"linear-gradient(135deg, #FFFFFF 0%, #F9FAFB 100%)",transition:"all 0.3s ease"},children:D(ao,{sx:{p:2,width:"100%",minWidth:0},children:[D(Fa,{direction:"row",justifyContent:"space-between",alignItems:"center",children:[D(Fa,{direction:"row",spacing:1.5,alignItems:"center",children:[t(Bf,{sx:{bgcolor:n.palette.primary.light,color:n.palette.primary.main,boxShadow:"none",width:32,height:32,fontSize:"14px",transition:"all 0.3s ease","&:hover":{transform:"rotate(5deg)"}},children:$.user.charAt(0).toUpperCase()}),t(Be,{fontWeight:"bold",color:K.text,children:$.user}),t(Hh,{label:$.taskName,size:"small",sx:{backgroundColor:n.palette.primary.light,color:n.palette.primary.main,fontSize:"12px",borderRadius:"16px"}})]}),t(Fa,{direction:"row",alignItems:"center",spacing:1,children:t(Be,{variant:"body2",color:K.secondaryText,sx:{fontSize:"12px"},children:new Date(((de=$.createdAt)==null?void 0:de.replace(" ","T"))+"Z").toLocaleString(void 0,{timeStyle:"short",dateStyle:"short"})})})]}),t(Uf,{sx:{my:2,borderColor:K.accent}}),t(Be,{variant:"body2",color:K.text,sx:{my:1,fontSize:"14px",lineHeight:"1.6",wordBreak:"break-word",overflowWrap:"anywhere"},children:$.comment||"-"})]})})})]},$.id)})})]})]})}),(!z||d&&!w||w&&ro.includes(p))&&t(ue,{sx:{borderTop:`1px solid ${me.placeholder.light}`,padding:"16px"},children:e===((ye=J)==null?void 0:ye.MAT)||e===((je=J)==null?void 0:je.ART)?t(iT,{activeTab:md.ATTACHMENT_AND_COMMENTS,submitForApprovalDisabled:!lT(T),filteredButtons:i,module:e}):t(nT,{filteredButtons:i,moduleName:e})}),U&&t(kd,{openSnackBar:U,alertMsg:q,handleSnackBarClose:v})]})},Bi=({number:r,label:d})=>D("div",{style:{display:"flex",flexDirection:"column",alignItems:"center",justifyContent:"center",padding:12,background:"#fff",borderRadius:8,boxShadow:"0 5px 15px rgba(0, 0, 0, 0.05)",transition:"transform 0.3s ease",animation:"fadeIn 0.5s ease-out forwards",minHeight:80},children:[t("div",{className:"stat-number",children:r}),t("div",{className:"stat-label",children:d})]}),OC=({data:r})=>{let d=0,R=Object.keys(r).length,o=0,p=0,e=0;const{t:O}=Ks();return Object.values(r).forEach(l=>{var x;const u=l.workflowDetails;e=u==null?void 0:u.totalAverageSLA,d+=2,o+=(u.requestor_sla||0)+(u.mdmApprover_sla||0),(x=u.workflowTaskDetailsByLevel)==null||x.forEach(ee=>{var Ce;const m=(Ce=Object.values(ee))==null?void 0:Ce[0];d+=m.length,p+=m.length,m.forEach(re=>{o+=re.taskSla||0})})}),D("div",{style:{display:"grid",gridTemplateColumns:"repeat(auto-fit, minmax(180px, 1fr))",gap:16,margin:"16px 0",padding:8,borderRadius:8},children:[t(Bi,{number:R,label:O("Workflow Groups")}),t(Bi,{number:d,label:O("Total Tasks")}),t(Bi,{number:e,label:O("Avg SLA (Days)")})]})};var zi={},yC=Ts;Object.defineProperty(zi,"__esModule",{value:!0});var uT=zi.default=void 0,vC=yC(hs()),qC=fs;uT=zi.default=(0,vC.default)((0,qC.jsx)("path",{d:"M7 18c-1.1 0-1.99.9-1.99 2S5.9 22 7 22s2-.9 2-2-.9-2-2-2M1 2v2h2l3.6 7.59-1.35 2.45c-.16.28-.25.61-.25.96 0 1.1.9 2 2 2h12v-2H7.42c-.14 0-.25-.11-.25-.25l.03-.12.9-1.63h7.45c.75 0 1.41-.41 1.75-1.03l3.58-6.49c.08-.14.12-.31.12-.48 0-.55-.45-1-1-1H5.21l-.94-2zm16 16c-1.1 0-1.99.9-1.99 2s.89 2 1.99 2 2-.9 2-2-.9-2-2-2"}),"ShoppingCart");var Yi={},SC=Ts;Object.defineProperty(Yi,"__esModule",{value:!0});var _i=Yi.default=void 0,PC=SC(hs()),LC=fs;_i=Yi.default=(0,PC.default)((0,LC.jsx)("path",{d:"M19 3h-4.18C14.4 1.84 13.3 1 12 1c-1.3 0-2.4.84-2.82 2H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2m-7 0c.55 0 1 .45 1 1s-.45 1-1 1-1-.45-1-1 .45-1 1-1m2 14H7v-2h7zm3-4H7v-2h10zm0-4H7V7h10z"}),"Assignment");var Ji={},BC=Ts;Object.defineProperty(Ji,"__esModule",{value:!0});var hT=Ji.default=void 0,UC=BC(hs()),GC=fs;hT=Ji.default=(0,UC.default)((0,GC.jsx)("path",{d:"m3.5 18.49 6-6.01 4 4L22 6.92l-1.41-1.41-7.09 7.97-4-4L2 16.99z"}),"ShowChart");var Xi={},xC=Ts;Object.defineProperty(Xi,"__esModule",{value:!0});var TT=Xi.default=void 0,HC=xC(hs()),mC=fs;TT=Xi.default=(0,HC.default)((0,mC.jsx)("path",{d:"M15 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4m-9-2V7H4v3H1v2h3v3h2v-3h3v-2zm9 4c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4"}),"PersonAdd");var Vi={},FC=Ts;Object.defineProperty(Vi,"__esModule",{value:!0});var fT=Vi.default=void 0,wC=FC(hs()),zh=fs;fT=Vi.default=(0,wC.default)([(0,zh.jsx)("path",{d:"M14 7h-4c-1.1 0-2 .9-2 2v6h2v7h4v-7h2V9c0-1.1-.9-2-2-2"},"0"),(0,zh.jsx)("circle",{cx:"12",cy:"4",r:"2"},"1")],"Man");const WC=r=>r.includes("Storage")?t(Hf,{}):r.includes("Buyer")?t(uT,{}):r.includes("Planner")?t(_i,{}):r.includes("Regulatory")?t(lC,{}):r.includes("Product")?t(sC,{}):r.includes("Plant")?t(hT,{}):r.includes("Data")?t(oC,{}):r.includes("Requestor")?t(TT,{}):r.includes("Steward")?t(mf,{}):r.includes("Manager")?t(fT,{}):t(_i,{}),jC=({id:r,task:d,onClick:R})=>{const o="#1976d2";return D("div",{id:r,onClick:R,className:"task",style:{borderRadius:12,marginBottom:8,background:"#fff",boxShadow:"0 4px 12px rgba(0, 0, 0, 0.08)",cursor:"pointer",transition:"transform 0.1s ease",maxWidth:230,padding:8,borderLeft:`4px solid ${o}`,position:"relative"},children:[t("div",{style:{position:"absolute",left:-12,top:12,width:16,height:16,borderRadius:"50%",background:"#fff",border:`3px solid ${o}`,boxSizing:"border-box",zIndex:2}}),D("div",{style:{display:"flex",alignItems:"start",gap:8,paddingTop:"16px",paddingBottom:"16px"},children:[t("div",{style:{color:"#1976d2",fontSize:24,display:"flex",alignItems:"center"},children:WC(d.name)}),t("div",{style:{fontWeight:600},children:d.name}),D("div",{style:{marginLeft:"auto",color:"#3498db",fontSize:14,background:"#e1f0fa",paddingLeft:"10px",paddingRight:"10px",borderRadius:"20px"},children:[d.sla,"d"]})]})]})},CT=({label:r,tasks:d,onTaskClick:R})=>D("div",{style:{display:"flex",flexDirection:"column",alignItems:"stretch",gap:0,padding:12},children:[t("div",{style:{marginBottom:8},children:t("span",{className:"level-label",style:{fontWeight:600,fontSize:16,display:"block",textAlign:"center",border:`${r=="Requestor"&&"2px solid #90EE90"}`},children:r})}),t("div",{style:{display:"flex",flexDirection:"column",gap:8,position:"relative"},children:d.map(o=>t(jC,{id:o.id,task:o,onClick:()=>R(o)},o.id))})]});CT.propTypes={label:Ei.string.isRequired,tasks:Ei.arrayOf(Ei.object).isRequired,onTaskClick:Ei.func.isRequired};const kC=({containerRef:r,sourceTargets:d})=>{const R=N.useRef(null),[o,p]=N.useState({width:0,height:0});return N.useEffect(()=>{const e=()=>{if(r.current){const O=r.current.getBoundingClientRect();p({width:O.width,height:O.height})}};return e(),window.addEventListener("resize",e),()=>window.removeEventListener("resize",e)},[r]),N.useEffect(()=>{if(!r.current||!R.current)return;const e=R.current;e.innerHTML="";const O=document.createElementNS("http://www.w3.org/2000/svg","defs"),l=document.createElementNS("http://www.w3.org/2000/svg","marker");l.setAttribute("id","arrowhead"),l.setAttribute("markerWidth","10"),l.setAttribute("markerHeight","7"),l.setAttribute("refX","9"),l.setAttribute("refY","3.5"),l.setAttribute("orient","auto");const u=document.createElementNS("http://www.w3.org/2000/svg","polygon");u.setAttribute("points","0 0, 10 3.5, 0 7"),u.setAttribute("fill","#3498db"),l.appendChild(u),O.appendChild(l),e.appendChild(O),d.forEach(({fromId:x,toId:ee})=>{const m=document.getElementById(x),Ce=document.getElementById(ee);if(!m||!Ce)return;const re=m.getBoundingClientRect(),Oe=Ce.getBoundingClientRect(),he=r.current.getBoundingClientRect(),Q=re.right-he.left,n=re.top+re.height/2-he.top,W=Oe.left-he.left,c=Oe.top+Oe.height/2-he.top,T=document.createElementNS("http://www.w3.org/2000/svg","path"),U=Math.max(50,Math.abs(W-Q)/2),g=`M ${Q},${n} C ${Q+U},${n} ${W-U},${c} ${W},${c}`;T.setAttribute("d",g),T.setAttribute("stroke","#bdc3c7"),T.setAttribute("stroke-width","2"),T.setAttribute("fill","none"),T.setAttribute("marker-end","url(#arrowhead)"),e.appendChild(T)})},[r,d,o]),t("svg",{ref:R,width:o.width,height:o.height,style:{position:"absolute",top:5,left:1.5,pointerEvents:"none",zIndex:0}})},$C=({groupName:r,groupData:d,onTaskClick:R})=>{var u;const o=N.useRef(null),[p,e]=N.useState([]),O=[],l=(x,ee,m,Ce)=>({type:x,label:ee,tasks:m==null?void 0:m.map((re,Oe)=>({...re,id:`${r}-level${Ce}-task${Oe}`}))});return O.push(l("requestor",d==null?void 0:d.requestorTaskLevelName,[{name:d==null?void 0:d.requestorTaskName,sla:d==null?void 0:d.requestor_sla,group:d==null?void 0:d.requestorTaskGroup,approver:"Requester",level:d==null?void 0:d.requestorTaskLevelName,status:"Pending"}],0)),(u=d==null?void 0:d.workflowTaskDetailsByLevel)==null||u.forEach((x,ee)=>{const m=Object.keys(x)[0],Ce=x[m];Ce.length>0&&O.push(l("workflow",`Level ${Ce[0].workflowApprovalLevel}: ${Ce[0].workflowLevelName}`,Ce.map(re=>({name:re==null?void 0:re.workflowTaskName,sla:re==null?void 0:re.taskSla,group:re==null?void 0:re.workflowTaskGroup,approver:re==null?void 0:re.taskApprover,level:re==null?void 0:re.workflowLevelName,status:"In Progress"})),ee+1))}),O.push(l("mdm",d.mdmTaskLevelName,[{name:d==null?void 0:d.mdmTaskName,sla:d==null?void 0:d.mdmApprover_sla,group:d==null?void 0:d.mdmTaskGroup,approver:d==null?void 0:d.mdmApprover_RecipientUsers,level:d==null?void 0:d.mdmTaskLevelName,status:"Not Started"}],O.length)),N.useEffect(()=>{var ee;const x=[];for(let m=0;m<(O==null?void 0:O.length)-1;m++){const Ce=O[m],re=O[m+1];(ee=Ce==null?void 0:Ce.tasks)==null||ee.forEach(Oe=>{var he;((he=re==null?void 0:re.tasks)==null?void 0:he.length)>0&&x.push({fromId:Oe==null?void 0:Oe.id,toId:re==null?void 0:re.tasks[0].id})})}e(x)},[r]),D("div",{style:{display:"flex",flexDirection:"row",alignItems:"stretch",width:"100%",borderRadius:8,padding:16,minHeight:120,position:"relative",gap:"24px"},ref:o,children:[O==null?void 0:O.map((x,ee)=>t(CT,{type:x==null?void 0:x.type,label:x==null?void 0:x.label,tasks:x==null?void 0:x.tasks,onTaskClick:R},ee)),t(kC,{containerRef:o,sourceTargets:p})]})};var Ki={},zC=Ts;Object.defineProperty(Ki,"__esModule",{value:!0});var gT=Ki.default=void 0,YC=zC(hs()),JC=fs;gT=Ki.default=(0,YC.default)((0,JC.jsx)("path",{d:"M7.41 8.59 12 13.17l4.59-4.58L18 10l-6 6-6-6z"}),"KeyboardArrowDown");var Qi={},XC=Ts;Object.defineProperty(Qi,"__esModule",{value:!0});var pT=Qi.default=void 0,VC=XC(hs()),KC=fs;pT=Qi.default=(0,VC.default)((0,KC.jsx)("path",{d:"M7.41 15.41 12 10.83l4.59 4.58L18 14l-6-6-6 6z"}),"KeyboardArrowUp");const QC=({groupName:r,groupData:d,materialTypes:R,onTaskClick:o,module:p})=>{var x;const[e,O]=N.useState(!1),l=()=>O(!e),u=jd();return N.useEffect(()=>{},[e]),D("div",{style:{border:"1px solid #e0e0e0",borderRadius:8,marginBottom:16,background:"#fff",boxShadow:"0 2px 8px rgba(0,0,0,0.04)",overflow:"hidden",transition:"box-shadow 0.2s"},children:[D("div",{style:{display:"flex",alignItems:"center",justifyContent:"space-between",padding:"16px",cursor:"pointer",backgroundImage:u.palette.gradient.pagination,border:"1px solid #E0E0E0",borderBottom:"1px solid #e0e0e0"},onClick:l,children:[D("div",{children:[p===((x=J)==null?void 0:x.MAT)&&D("div",{style:{fontWeight:600,fontSize:18,color:u.palette.primary.main},children:[r,` (${R.join(", ")})`]}),t("div",{style:{fontSize:14,color:u.palette.primary.main,marginTop:2},children:d==null?void 0:d.mdmTaskLevelName})]}),t("div",{style:{fontSize:28,color:"#fff",transition:"transform 0.2s",display:"flex",alignItems:"center",justifyContent:"center"},children:e?t(pT,{style:{color:u.palette.primary.main,fontSize:32}}):t(gT,{style:{color:u.palette.primary.main,fontSize:32}})})]}),!e&&t("div",{style:{padding:16,background:"#fff",overflowX:"auto"},children:t($C,{groupName:r,groupData:d,onTaskClick:o})})]})},ZC=r=>{const d=new Date,R=new Date(d);return R.setDate(d.getDate()+r),R.toLocaleDateString("en-US",{month:"short",day:"numeric",year:"numeric"})},eg=({task:r,onClose:d})=>t("div",{className:"modal",onClick:R=>R.target.classList.contains("modal")&&d(),children:D("div",{className:"modal-content",children:[D("div",{className:"modal-header",children:[D("div",{className:"modal-title",style:{display:"flex",alignItems:"center",gap:8},children:[t(_i,{style:{color:"#fff"}}),t("span",{children:r.name})]}),t("div",{className:"modal-close",onClick:d,style:{cursor:"pointer"},children:t(Fd,{style:{color:"#fff"}})})]}),D("div",{className:"modal-body",children:[D("div",{className:"task-detail",children:[t("div",{className:"detail-label",children:"Task Group:"}),t("div",{className:"detail-value",children:r.group})]}),D("div",{className:"task-detail",children:[t("div",{className:"detail-label",children:"Approver:"}),t(Ff,{text:r==null?void 0:r.approver,maxChars:42,variant:"body1"})]}),D("div",{className:"task-detail",children:[t("div",{className:"detail-label",children:"Level:"}),t("div",{className:"detail-value",children:r.level})]}),D("div",{className:"task-detail",children:[t("div",{className:"detail-label",children:"SLA:"}),t("div",{className:"detail-value",children:D("span",{className:"sla-badge sla-normal",children:[r.sla," days"]})})]}),D("div",{className:"task-detail",children:[t("div",{className:"detail-label",children:"Due Date:"}),t("div",{className:"detail-value",children:ZC(r.sla)})]}),D("div",{className:"task-detail",children:[t("div",{className:"detail-label",children:"Status:"}),t("div",{className:"detail-value",children:r.status})]})]})]})}),ag=({data:r,module:d})=>{var l;const[R,o]=N.useState(null),{t:p}=Ks(),e=u=>{o(u)},O=()=>{o(null)};return D("div",{children:[t(Be,{align:"left",variant:"h4",component:"h2",gutterBottom:!0,children:p("Workflow Details")}),t(OC,{data:r}),t("div",{className:"workflow-container",children:(l=Object==null?void 0:Object.entries(r))==null?void 0:l.map(([u,x])=>t(QC,{groupName:u,groupData:x==null?void 0:x.workflowDetails,materialTypes:(x==null?void 0:x.materialTypes)||[x==null?void 0:x.parentNode],onTaskClick:e},u))}),R&&t(eg,{task:R,onClose:O})]})};function lg({label:r,checked:d=!1,onChange:R,id:o}){return t(Ui,{control:t(wf,{id:o,checked:d,onChange:R,sx:{color:"primary.main","&.Mui-checked":{color:"primary.main"}}}),label:r,sx:{ml:0,".MuiFormControlLabel-label":{fontSize:14,color:"#4B5563",cursor:"pointer"}}})}const sg=({payloadForDownloadExcel:r,module:d,payloadForPreviewDownloadExcel:R})=>{var n,W;const{destination:o}=Mi(d),[p,e]=N.useState(!1),[O,l]=N.useState(),[u,x]=N.useState("success"),[ee,m]=N.useState(!1),{t:Ce}=Ks();jd();const{customError:re}=Wd(),Oe=()=>{e(!1)},he=d===((n=J)==null?void 0:n.MAT)||d===((W=J)==null?void 0:W.ART)?r:R,Q=()=>{var L,B,b;const c={[(L=J)==null?void 0:L.PCG]:ca.EXCEL.EXPORT_PCG_EXCEL,[(B=J)==null?void 0:B.CCG]:ca.EXCEL.EXPORT_CCG_EXCEL,[(b=J)==null?void 0:b.CEG]:ca.EXCEL.EXPORT_CEG_EXCEL,default:ca.EXCEL.EXPORT_PREVIEW_EXCEL};let T=c[d]||c.default;const U=q=>{if((q==null?void 0:q.size)>0){const P=URL.createObjectURL(q),G=document.createElement("a");G.href=P,G.setAttribute("download",`Request_Preview_${new Date().getTime()}.xlsx`),document.body.appendChild(G),G.click(),document.body.removeChild(G),URL.revokeObjectURL(P)}},g=q=>{re(q),l(q==null?void 0:q.message),x("error"),e(!0)};ma(`/${o}${T}`,"postandgetblob",U,g,he)};return D(lo,{item:!0,md:12,sx:{backgroundColor:"white",borderRadius:"8px",border:"1px solid #E0E0E0",boxShadow:"0px 1px 4px rgba(0, 0, 0, 0.1)",p:"10px"},children:[t(Be,{sx:{fontWeight:"bold",mb:"6px"},children:Ce("Master data details")}),t(ue,{sx:{backgroundColor:"#FAFAFA",borderRadius:"8px",boxShadow:"none"},children:D(ue,{sx:{padding:"8px"},children:[t(Be,{align:"left",variant:"h6",component:"h2",children:Ce("Please download the excel sheet to view all data.")}),D(ue,{sx:{display:"flex",flexDirection:"column",alignItems:"flex-start",gap:1,mt:2},children:[t(da,{variant:"contained",startIcon:t(rC,{sx:{fontSize:28,animation:"downloadBounce 2s ease-in-out infinite",filter:"drop-shadow(0 2px 4px rgba(255,255,255,0.3))"}}),onClick:Q,sx:{position:"relative",overflow:"hidden","&::before":{content:'""',position:"absolute",top:0,left:"-100%",width:"100%",height:"100%",background:"linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent)",transition:"left 0.5s"},"&:hover::before":{left:"100%"},"&:hover":{transform:"translateY(-3px) scale(1.02)",boxShadow:"0 12px 25px rgba(102, 126, 234, 0.4), 0 0 20px rgba(118, 75, 162, 0.3)"},"&:active":{transform:"translateY(-1px) scale(0.98)",boxShadow:"0 6px 15px rgba(102, 126, 234, 0.3)"},transition:"all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275)",borderRadius:3,py:1.8,px:3,textTransform:"none",fontSize:"1.1rem",fontWeight:600,boxShadow:"0 8px 20px rgba(102, 126, 234, 0.3), 0 0 15px rgba(118, 75, 162, 0.2)",display:"flex",alignItems:"center",gap:1.5,border:"1px solid rgba(255, 255, 255, 0.1)",backdropFilter:"blur(10px)",color:"#ffffff",letterSpacing:"0.5px",minWidth:"180px","@keyframes downloadBounce":{"0%, 100%":{transform:"translateY(0) rotate(0deg)",filter:"drop-shadow(0 2px 4px rgba(255,255,255,0.3))"},"25%":{transform:"translateY(-3px) rotate(-2deg)",filter:"drop-shadow(0 4px 8px rgba(255,255,255,0.4))"},"50%":{transform:"translateY(-6px) rotate(0deg)",filter:"drop-shadow(0 6px 12px rgba(255,255,255,0.5))"},"75%":{transform:"translateY(-3px) rotate(2deg)",filter:"drop-shadow(0 4px 8px rgba(255,255,255,0.4))"}},"&::after":{content:'""',position:"absolute",top:"50%",left:"50%",width:"0",height:"0",background:"radial-gradient(circle, rgba(255,255,255,0.3) 0%, transparent 70%)",borderRadius:"50%",transform:"translate(-50%, -50%)",transition:"width 0.6s, height 0.6s",pointerEvents:"none"},"&:active::after":{width:"300px",height:"300px"}},children:D(ue,{sx:{display:"flex",alignItems:"center",gap:1,position:"relative",zIndex:1},children:[Ce("Download Excel"),t(ue,{component:"span",sx:{fontSize:"0.8rem",opacity:.8,fontWeight:400,ml:.5},children:"(.xlsx)"})]})}),t(lg,{label:Ce("I have reviewed all request details."),checked:ee,onChange:()=>m(!ee)})]})]})}),t(kd,{openSnackBar:p,alertMsg:O,alertType:u,handleSnackBarClose:Oe})]})},og=()=>{const{t:r}=Ks();return D(ue,{sx:{p:3,maxWidth:1400,mx:"auto"},children:[t(Be,{variant:"h6",sx:{mb:3,color:"#666"},children:r("Workflow Details")}),D(ue,{sx:{display:"flex",gap:6,mb:4,justifyContent:"flex-start",maxWidth:600},children:[D(ue,{sx:{textAlign:"center"},children:[t(ze,{variant:"text",width:60,height:60,sx:{fontSize:"2.5rem",mx:"auto",bgcolor:"#e3f2fd",borderRadius:1}}),t(ze,{variant:"text",width:100,height:20,sx:{mt:1,mx:"auto"}})]}),D(ue,{sx:{textAlign:"center"},children:[t(ze,{variant:"text",width:60,height:60,sx:{fontSize:"2.5rem",mx:"auto",bgcolor:"#e3f2fd",borderRadius:1}}),t(ze,{variant:"text",width:80,height:20,sx:{mt:1,mx:"auto"}})]}),D(ue,{sx:{textAlign:"center"},children:[t(ze,{variant:"text",width:60,height:60,sx:{fontSize:"2.5rem",mx:"auto",bgcolor:"#e3f2fd",borderRadius:1}}),t(ze,{variant:"text",width:120,height:20,sx:{mt:1,mx:"auto"}})]})]}),D(Wf,{expanded:!0,sx:{bgcolor:"#37474f",color:"white","&:before":{display:"none"},borderRadius:1,overflow:"hidden"},children:[t(jf,{sx:{bgcolor:"#37474f","& .MuiAccordionSummary-content":{alignItems:"center"}},children:D(ue,{children:[t(ze,{variant:"text",width:180,height:24,sx:{bgcolor:"rgba(255,255,255,0.2)"}}),t(ze,{variant:"text",width:220,height:16,sx:{bgcolor:"rgba(255,255,255,0.1)",mt:.5}})]})}),D(kf,{sx:{bgcolor:"#f5f5f5",p:0},children:[t(ue,{sx:{display:"grid",gridTemplateColumns:"repeat(6, 1fr)",gap:2,bgcolor:"#fff",borderBottom:"1px solid #e0e0e0",p:2},children:["Requestor","Level 1: Data Entry","Level 2: Additional Master Data","Level 3: Cost","Level 4: Record Approval","Final Creation"].map((d,R)=>t(ue,{sx:{textAlign:"center"},children:t(ze,{variant:"text",width:"80%",height:20,sx:{mx:"auto"}})},R))}),t(ue,{sx:{p:3,bgcolor:"#fafafa"},children:D(ue,{sx:{display:"grid",gridTemplateColumns:"repeat(6, 1fr)",gap:2,alignItems:"flex-start"},children:[t(ue,{sx:{display:"flex",flexDirection:"column",gap:2},children:[1,2,3,4,5,6].map((d,R)=>D(ue,{sx:{position:"relative"},children:[t(eo,{sx:{bgcolor:"#e3f2fd",minHeight:60,border:"2px solid #2196f3",borderRadius:2},children:t(ao,{sx:{p:2},children:D(ue,{sx:{display:"flex",alignItems:"center",gap:1},children:[t(ze,{variant:"circular",width:24,height:24}),D(ue,{sx:{flex:1},children:[t(ze,{variant:"text",width:"70%",height:16}),t(ze,{variant:"text",width:"40%",height:12,sx:{mt:.5}})]}),t(ze,{variant:"text",width:30,height:20})]})})}),R<5&&t(ue,{sx:{position:"absolute",right:-8,top:"50%",width:16,height:2,bgcolor:"#2196f3",zIndex:1,transform:"translateY(-50%)"}})]},R))}),t(ue,{sx:{display:"flex",flexDirection:"column",gap:2},children:t(eo,{sx:{bgcolor:"#e3f2fd",minHeight:60,border:"2px solid #2196f3",borderRadius:2},children:t(ao,{sx:{p:2},children:D(ue,{sx:{display:"flex",alignItems:"center",gap:1},children:[t(ze,{variant:"circular",width:24,height:24}),D(ue,{sx:{flex:1},children:[t(ze,{variant:"text",width:"80%",height:16}),t(ze,{variant:"text",width:"50%",height:12,sx:{mt:.5}})]}),t(ze,{variant:"text",width:30,height:20})]})})})}),t(ue,{sx:{display:"flex",flexDirection:"column",gap:2},children:[1,2].map((d,R)=>t(eo,{sx:{bgcolor:"#e3f2fd",minHeight:60,border:"2px solid #2196f3",borderRadius:2},children:t(ao,{sx:{p:2},children:D(ue,{sx:{display:"flex",alignItems:"center",gap:1},children:[t(ze,{variant:"circular",width:24,height:24}),D(ue,{sx:{flex:1},children:[t(ze,{variant:"text",width:"75%",height:16}),t(ze,{variant:"text",width:"45%",height:12,sx:{mt:.5}})]}),t(ze,{variant:"text",width:30,height:20})]})})},R))}),t(ue,{sx:{display:"flex",flexDirection:"column",gap:2},children:t(eo,{sx:{bgcolor:"#e3f2fd",minHeight:60,border:"2px solid #2196f3",borderRadius:2},children:t(ao,{sx:{p:2},children:D(ue,{sx:{display:"flex",alignItems:"center",gap:1},children:[t(ze,{variant:"circular",width:24,height:24}),D(ue,{sx:{flex:1},children:[t(ze,{variant:"text",width:"85%",height:16}),t(ze,{variant:"text",width:"35%",height:12,sx:{mt:.5}})]}),t(ze,{variant:"text",width:30,height:20})]})})})}),t(ue,{sx:{display:"flex",flexDirection:"column",gap:2},children:t(eo,{sx:{bgcolor:"#e3f2fd",minHeight:60,border:"2px solid #2196f3",borderRadius:2},children:t(ao,{sx:{p:2},children:D(ue,{sx:{display:"flex",alignItems:"center",gap:1},children:[t(ze,{variant:"circular",width:24,height:24}),D(ue,{sx:{flex:1},children:[t(ze,{variant:"text",width:"90%",height:16}),t(ze,{variant:"text",width:"55%",height:12,sx:{mt:.5}})]}),t(ze,{variant:"text",width:30,height:20})]})})})}),t(ue,{sx:{display:"flex",flexDirection:"column",gap:2},children:t(eo,{sx:{bgcolor:"#f5f5f5",minHeight:60,border:"2px solid #ccc",borderRadius:2},children:t(ao,{sx:{p:2},children:D(ue,{sx:{display:"flex",alignItems:"center",gap:1},children:[t(ze,{variant:"circular",width:24,height:24,sx:{bgcolor:"#bdbdbd"}}),D(ue,{sx:{flex:1},children:[t(ze,{variant:"text",width:"75%",height:16,sx:{bgcolor:"#bdbdbd"}}),t(ze,{variant:"text",width:"40%",height:12,sx:{mt:.5,bgcolor:"#bdbdbd"}})]}),t(ze,{variant:"text",width:30,height:20,sx:{bgcolor:"#bdbdbd"}})]})})})})]})})]})]})]})},rg=(r,d,R,o,p)=>{const[e,O]=N.useState([]),[l,u]=N.useState([]),[x,ee]=N.useState(!1),m=new URLSearchParams(location.search||(location==null?void 0:location.href)),Ce=wd[p]||(()=>({})),re=Y(Ce),Oe=Y(Q=>{var n,W;return(W=(n=Q==null?void 0:Q.userManagement)==null?void 0:n.taskData)==null?void 0:W.ATTRIBUTE_2}),he=m.get("RequestType");return N.useEffect(()=>{let Q={decisionTableId:null,decisionTableName:"MDG_MAT_DYN_BUTTON_CONFIG",version:"v4",rulePolicy:null,validityDate:null,conditions:[{"MDG_CONDITIONS.MDG_MAT_DYN_BTN_MODULE_NAME":p===J.BOM?"BOM":p===J.ART?"Article":"Material","MDG_CONDITIONS.MDG_MAT_DYN_BTN_REQUEST_TYPE":he||Oe||(re==null?void 0:re.RequestType)}],systemFilters:null,systemOrders:null,filterString:null};const n=T=>{var U,g;T.statusCode===200&&O((g=(U=T==null?void 0:T.data)==null?void 0:U.result[0])==null?void 0:g.MDG_MAT_DYN_BUTTON_CONFIG)},W=T=>{console.error(T)},c=d.environment==="localhost"?`/${R}/rest/v1/invoke-rules`:`/${R}/v1/invoke-rules`;ma(c,"post",n,W,Q)},[r]),N.useEffect(()=>{const Q=To(to.CURRENT_TASK,!0,{}),n=(Q==null?void 0:Q.taskDesc)||(r==null?void 0:r.taskDesc),W=e==null?void 0:e.filter(T=>T.MDG_MAT_DYN_BTN_TASK_NAME===n),c=W==null?void 0:W.sort((T,U)=>{const g=kh[T.MDG_MAT_DYN_BTN_ACTION_TYPE]??999,L=kh[U.MDG_MAT_DYN_BTN_ACTION_TYPE]??999;return g-L});u(c),(c!=null&&c.find(T=>T.MDG_MAT_DYN_BTN_BUTTON_NAME===o.SEND_BACK)||c!=null&&c.find(T=>T.MDG_MAT_DYN_BTN_BUTTON_NAME===o.CORRECTION))&&ee(!0)},[e]),{filteredButtons:l,showWfLevels:x}},Ng=r=>{var Z,le,te,i;const d=r==null?void 0:r.payloadData,{customError:R}=Wd(),o=Vs(),p=new URLSearchParams(o.search),e=Y(y=>y.request.materialRows),O=Y(y=>y.userManagement.taskData),l=Y(y=>y.applicationConfig),u=p.get("reqBench"),x=p.get("RequestId"),ee=Y(y=>{var V,v,z,K,Ee,X,ye,je,$;switch(r.module){case((V=J)==null?void 0:V.MAT):return y.request.requestHeader;case((v=J)==null?void 0:v.ART):return y.request.requestHeader;case((z=J)==null?void 0:z.CC):return y.costCenter.payload.requestHeaderData;case((K=J)==null?void 0:K.PC):return y.profitCenter.payload.requestHeaderData;case((Ee=J)==null?void 0:Ee.GL):return y.generalLedger.payload.requestHeaderData;case((X=J)==null?void 0:X.CCG):return y.hierarchyData.requestHeaderData;case((ye=J)==null?void 0:ye.PCG):return y.hierarchyData.requestHeaderData;case((je=J)==null?void 0:je.CEG):return y.hierarchyData.requestHeaderData;case(($=J)==null?void 0:$.IO):return y.internalOrder.IOpayloadData.requestHeaderData;default:return null}}),{showSnackbar:m}=wi(),[Ce,re]=N.useState(null),[Oe,he]=N.useState(!1),Q=$f[r==null?void 0:r.module]||(()=>({})),n=Y(Q),{t:W}=Ks(),c=wd[r==null?void 0:r.module]||(()=>({})),T=Y(c),U=Y(y=>y.payload.filteredButtons),{filteredButtons:g}=rg(O,l,xi,va,r==null?void 0:r.module),{extendFilteredButtons:L}=cT(O,l,xi,va,r==null?void 0:r.module),B=T==null?void 0:T.RequestStatus,b=B===pl.DRAFT||B===pl.DRAFT_IN_CAPS||B===pl.VALIDATED_REQUESTOR||B===pl.VALIDATION_FAILED_REQUESTOR||B===pl.UPLOAD_SUCCESSFUL,{destination:q,bifurcationEndPoint:P}=Mi(r==null?void 0:r.module),G=rT({module:r==null?void 0:r.module,requestId:x,requestType:T==null?void 0:T.RequestType,templateName:T==null?void 0:T.TemplateName,payloadData:d});let F;const w=[Ea.HANDLE_SEND_BACK,Ea.HANDLE_VALIDATE,Ea.HANDLE_CORRECTION,Ea.HANDLE_ACCEPT];(T==null?void 0:T.RequestType)===h.CREATE||(T==null?void 0:T.RequestType)===h.CREATE_WITH_UPLOAD?F=Di((r==null?void 0:r.module)!==((Z=J)==null?void 0:Z.BK)?g:U,[...w,Ea.HANDLE_VALIDATE1]):(T==null?void 0:T.RequestType)===h.EXTEND||(T==null?void 0:T.RequestType)===h.EXTEND_WITH_UPLOAD?F=Di(L,w):(T==null?void 0:T.RequestType)===h.CHANGE||(T==null?void 0:T.RequestType)===h.CHANGE_WITH_UPLOAD?F=Di(U,w):F=[];const j=!ro.includes(r==null?void 0:r.requestStatus)||x&&!u;return N.useEffect(()=>{if(b){he(!0);const y=v=>{v.statusCode===Zl.STATUS_200?(re(v==null?void 0:v.body),he(!1)):(m(v==null?void 0:v.message,"error"),he(!1))},V=v=>{R(v),he(!1)};ma(`/${q}${P}`,"post",y,V,G)}},[]),D(al,{children:[D(Fa,{spacing:2,children:[Object.entries(n).map(([y,V])=>D(lo,{item:!0,md:12,sx:{backgroundColor:"white",borderRadius:"8px",border:"1px solid #E0E0E0",boxShadow:"0px 1px 4px rgba(0, 0, 0, 0.1)",...aT,pt:"10px"},children:[t(Be,{sx:{fontWeight:"bold",mb:"6px"},children:W("Request Details")}),t(ue,{sx:{backgroundColor:"#FAFAFA",padding:"10px",pl:"0px",pr:"0px",borderRadius:"8px",boxShadow:"none"},children:t(lo,{container:!0,spacing:2,children:V.filter(v=>v.visibility!=="Hidden").sort((v,z)=>v.sequenceNo-z.sequenceNo).map(v=>{let z=(ee==null?void 0:ee[v==null?void 0:v.jsonName])||(T==null?void 0:T[v==null?void 0:v.jsonName])||"",K="";return Array.isArray(z)?K=z.join(", "):z instanceof Date||typeof z=="object"&&z instanceof Object&&z.toString().includes("GMT")?K=new Date(z).toLocaleString():K=z,z=K,z&&z!==null&&z!==""&&t(lo,{item:!0,md:3,children:D("div",{style:{padding:"12px",backgroundColor:"#ffffff",borderRadius:"8px",boxShadow:"0 2px 8px rgba(0, 0, 0, 0.1)",transition:"all 0.3s ease"},children:[t(Wl,{title:W(v==null?void 0:v.fieldName)||"Field Name",children:D(Be,{variant:"body1",sx:{whiteSpace:"nowrap",overflow:"hidden",textOverflow:"ellipsis",maxWidth:"100%",fontWeight:600,fontSize:"12px",marginBottom:"4px",display:"flex",alignItems:"center"},children:[W(v==null?void 0:v.fieldName)||"Field Name",((v==null?void 0:v.visibility)==="Required"||(v==null?void 0:v.visibility)==="MANDATORY")&&t("span",{style:{color:"#d32f2f",marginLeft:"2px"},children:"*"})]})}),t(Wl,{title:z||"--",children:t("div",{style:{fontSize:"0.8rem",color:"#333333",marginTop:"4px",whiteSpace:"nowrap",overflow:"hidden",textOverflow:"ellipsis",maxWidth:"100%"},children:t("span",{style:{fontWeight:500,color:"grey",letterSpacing:"0.5px",wordSpacing:"1px"},children:z||"--"})})})]})},v==null?void 0:v.id)})})})]},y)),r.module!==J.ART&&t(sg,{payloadForDownloadExcel:r==null?void 0:r.payloadForDownloadExcel,module:r.module,payloadForPreviewDownloadExcel:r.payloadForPreviewDownloadExcel}),b&&t(al,{children:Ce&&!Oe?t(ag,{data:Ce,module:r.module}):t(og,{})})]}),(!j||x&&!u||u&&ro.includes(r==null?void 0:r.requestStatus))&&t(ue,{sx:{borderTop:"1px solid #e0e0e0",padding:"16px"},children:(r==null?void 0:r.module)===((le=J)==null?void 0:le.MAT)||(r==null?void 0:r.module)===((te=J)==null?void 0:te.ART)?t(iT,{activeTab:md.PREVIEW,submitForApprovalDisabled:!lT(e),filteredButtons:F,childRequestHeaderData:(i=d==null?void 0:d[T==null?void 0:T.selectedMaterialID])==null?void 0:i.Tochildrequestheaderdata,module:r==null?void 0:r.module}):t(nT,{filteredButtons:F,moduleName:r==null?void 0:r.module,handleSaveAsDraft:()=>{},handleSubmitForReview:()=>{},handleSubmitForApprove:()=>{},handleSendBack:()=>{},handleCorrection:()=>{},handleRejectAndCancel:()=>{},handleValidateAndSyndicate:()=>{},validateAllRows:()=>{}})})]})};export{_g as A,nT as B,kh as C,Ig as D,tT as M,Ng as P,TC as S,md as T,iT as a,Ea as b,cT as c,ki as d,ji as e,_C as f,Dg as g,rg as u};
