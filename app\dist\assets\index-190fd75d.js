import{r as y,f5 as q,fp as it,fq as jt,fr as zt,fe as Et,fh as St,fg as Nt,fi as V,f6 as ee,fc as Je,ef as G,_ as C,t as M,ff as Ft,fl as P}from"./index-f7d9b065.js";import{g as Qe,r as Ie,n as be,o as qt,p as Ze,q as $t,s as Wt,t as Vt,b as Gt,I as Ut,c as Xt,E as Yt,v as Jt,w as Qt,m as kt,x as wt,y as He,z as Zt,A as Ne,G as en,K as tn,L as nn,H as lt,a as Ot,J as rn,N as on}from"./EditOutlined-9d614b39.js";import{C as an,w as fe,f as Be,A as ge,_ as ue}from"./EyeOutlined-0bb7ab85.js";const _e=(n,t,r,e,a)=>({background:n,border:`${be(e.lineWidth)} ${e.lineType} ${t}`,[`${a}-icon`]:{color:r}}),ln=n=>{const{componentCls:t,motionDurationSlow:r,marginXS:e,marginSM:a,fontSize:l,fontSizeLG:c,lineHeight:o,borderRadiusLG:d,motionEaseInOutCirc:i,withDescriptionIconSize:u,colorText:f,colorTextHeading:v,withDescriptionPadding:p,defaultPadding:h}=n;return{[t]:Object.assign(Object.assign({},Ie(n)),{position:"relative",display:"flex",alignItems:"center",padding:h,wordWrap:"break-word",borderRadius:d,[`&${t}-rtl`]:{direction:"rtl"},[`${t}-content`]:{flex:1,minWidth:0},[`${t}-icon`]:{marginInlineEnd:e,lineHeight:0},"&-description":{display:"none",fontSize:l,lineHeight:o},"&-message":{color:v},[`&${t}-motion-leave`]:{overflow:"hidden",opacity:1,transition:`max-height ${r} ${i}, opacity ${r} ${i},
        padding-top ${r} ${i}, padding-bottom ${r} ${i},
        margin-bottom ${r} ${i}`},[`&${t}-motion-leave-active`]:{maxHeight:0,marginBottom:"0 !important",paddingTop:0,paddingBottom:0,opacity:0}}),[`${t}-with-description`]:{alignItems:"flex-start",padding:p,[`${t}-icon`]:{marginInlineEnd:a,fontSize:u,lineHeight:0},[`${t}-message`]:{display:"block",marginBottom:e,color:v,fontSize:c},[`${t}-description`]:{display:"block",color:f}},[`${t}-banner`]:{marginBottom:0,border:"0 !important",borderRadius:0}}},dn=n=>{const{componentCls:t,colorSuccess:r,colorSuccessBorder:e,colorSuccessBg:a,colorWarning:l,colorWarningBorder:c,colorWarningBg:o,colorError:d,colorErrorBorder:i,colorErrorBg:u,colorInfo:f,colorInfoBorder:v,colorInfoBg:p}=n;return{[t]:{"&-success":_e(a,e,r,n,t),"&-info":_e(p,v,f,n,t),"&-warning":_e(o,c,l,n,t),"&-error":Object.assign(Object.assign({},_e(u,i,d,n,t)),{[`${t}-description > pre`]:{margin:0,padding:0}})}}},sn=n=>{const{componentCls:t,iconCls:r,motionDurationMid:e,marginXS:a,fontSizeIcon:l,colorIcon:c,colorIconHover:o}=n;return{[t]:{"&-action":{marginInlineStart:a},[`${t}-close-icon`]:{marginInlineStart:a,padding:0,overflow:"hidden",fontSize:l,lineHeight:be(l),backgroundColor:"transparent",border:"none",outline:"none",cursor:"pointer",[`${r}-close`]:{color:c,transition:`color ${e}`,"&:hover":{color:o}}},"&-close-text":{color:c,transition:`color ${e}`,"&:hover":{color:o}}}}},cn=n=>({withDescriptionIconSize:n.fontSizeHeading3,defaultPadding:`${n.paddingContentVerticalSM}px 12px`,withDescriptionPadding:`${n.paddingMD}px ${n.paddingContentHorizontalLG}px`}),un=Qe("Alert",n=>[ln(n),dn(n),sn(n)],cn);var dt=globalThis&&globalThis.__rest||function(n,t){var r={};for(var e in n)Object.prototype.hasOwnProperty.call(n,e)&&t.indexOf(e)<0&&(r[e]=n[e]);if(n!=null&&typeof Object.getOwnPropertySymbols=="function")for(var a=0,e=Object.getOwnPropertySymbols(n);a<e.length;a++)t.indexOf(e[a])<0&&Object.prototype.propertyIsEnumerable.call(n,e[a])&&(r[e[a]]=n[e[a]]);return r};const fn={success:Gt,info:Ut,error:Xt,warning:Yt},gn=n=>{const{icon:t,prefixCls:r,type:e}=n,a=fn[e]||null;return t?Vt(t,y.createElement("span",{className:`${r}-icon`},t),()=>({className:q(`${r}-icon`,t.props.className)})):y.createElement(a,{className:`${r}-icon`})},vn=n=>{const{isClosable:t,prefixCls:r,closeIcon:e,handleClose:a,ariaProps:l}=n,c=e===!0||e===void 0?y.createElement(an,null):e;return t?y.createElement("button",Object.assign({type:"button",onClick:a,className:`${r}-close-icon`,tabIndex:0},l),c):null},hn=y.forwardRef((n,t)=>{const{description:r,prefixCls:e,message:a,banner:l,className:c,rootClassName:o,style:d,onMouseEnter:i,onMouseLeave:u,onClick:f,afterClose:v,showIcon:p,closable:h,closeText:b,closeIcon:g,action:m,id:K}=n,x=dt(n,["description","prefixCls","message","banner","className","rootClassName","style","onMouseEnter","onMouseLeave","onClick","afterClose","showIcon","closable","closeText","closeIcon","action","id"]),[E,S]=y.useState(!1),$=y.useRef(null);y.useImperativeHandle(t,()=>({nativeElement:$.current}));const{getPrefixCls:w,direction:k,closable:s,closeIcon:O,className:R,style:L}=qt("alert"),D=w("alert",e),[I,T,A]=un(D),H=W=>{var F;S(!0),(F=n.onClose)===null||F===void 0||F.call(n,W)},Y=y.useMemo(()=>n.type!==void 0?n.type:l?"warning":"info",[n.type,l]),J=y.useMemo(()=>typeof h=="object"&&h.closeIcon||b?!0:typeof h=="boolean"?h:g!==!1&&g!==null&&g!==void 0?!0:!!s,[b,g,h,s]),z=l&&p===void 0?!0:p,ae=q(D,`${D}-${Y}`,{[`${D}-with-description`]:!!r,[`${D}-no-icon`]:!z,[`${D}-banner`]:!!l,[`${D}-rtl`]:k==="rtl"},R,c,o,A,T),te=Ze(x,{aria:!0,data:!0}),X=y.useMemo(()=>typeof h=="object"&&h.closeIcon?h.closeIcon:b||(g!==void 0?g:typeof s=="object"&&s.closeIcon?s.closeIcon:O),[g,h,b,O]),ne=y.useMemo(()=>{const W=h??s;return typeof W=="object"?dt(W,["closeIcon"]):{}},[h,s]);return I(y.createElement($t,{visible:!E,motionName:`${D}-motion`,motionAppear:!1,motionEnter:!1,onLeaveStart:W=>({maxHeight:W.offsetHeight}),onLeaveEnd:v},({className:W,style:F},ve)=>y.createElement("div",Object.assign({id:K,ref:Wt($,ve),"data-show":!E,className:q(ae,W),style:Object.assign(Object.assign(Object.assign({},L),d),F),onMouseEnter:i,onMouseLeave:u,onClick:f,role:"alert"},te),z?y.createElement(gn,{description:r,icon:n.icon,prefixCls:D,type:Y}):null,y.createElement("div",{className:`${D}-content`},a?y.createElement("div",{className:`${D}-message`},a):null,r?y.createElement("div",{className:`${D}-description`},r):null),m?y.createElement("div",{className:`${D}-action`},m):null,y.createElement(vn,{isClosable:J,prefixCls:D,closeIcon:X,handleClose:H,ariaProps:ne}))))}),Dt=hn;function pn(n,t,r){return t=it(t),jt(n,zt()?Reflect.construct(t,r||[],it(n).constructor):t.apply(n,r))}let yn=function(n){function t(){var r;return Nt(this,t),r=pn(this,t,arguments),r.state={error:void 0,info:{componentStack:""}},r}return Et(t,n),St(t,[{key:"componentDidCatch",value:function(e,a){this.setState({error:e,info:a})}},{key:"render",value:function(){const{message:e,description:a,id:l,children:c}=this.props,{error:o,info:d}=this.state,i=(d==null?void 0:d.componentStack)||null,u=typeof e>"u"?(o||"").toString():e,f=typeof a>"u"?i:a;return o?y.createElement(Dt,{id:l,type:"error",message:u,description:y.createElement("pre",{style:{fontSize:"0.9em",overflowX:"auto"}},f)}):c}}])}(y.Component);const mn=yn,Pt=Dt;Pt.ErrorBoundary=mn;const Xr=Pt;function U(n,t){return n[t]}var bn=["children"];function It(n,t){return"".concat(n,"-").concat(t)}function xn(n){return n&&n.type&&n.type.isTreeNode}function Re(n,t){return n??t}function $e(n){var t=n||{},r=t.title,e=t._title,a=t.key,l=t.children,c=r||"title";return{title:c,_title:e||[c],key:a||"key",children:l||"children"}}function Tt(n){function t(r){var e=Jt(r);return e.map(function(a){if(!xn(a))return fe(!a,"Tree/TreeNode can only accept TreeNode as children."),null;var l=a.key,c=a.props,o=c.children,d=Be(c,bn),i=V({key:l},d),u=t(o);return u.length&&(i.children=u),i}).filter(function(a){return a})}return t(n)}function qe(n,t,r){var e=$e(r),a=e._title,l=e.key,c=e.children,o=new Set(t===!0?[]:t),d=[];function i(u){var f=arguments.length>1&&arguments[1]!==void 0?arguments[1]:null;return u.map(function(v,p){for(var h=It(f?f.pos:"0",p),b=Re(v[l],h),g,m=0;m<a.length;m+=1){var K=a[m];if(v[K]!==void 0){g=v[K];break}}var x=Object.assign(Qt(v,[].concat(ee(a),[l,c])),{title:g,key:b,parent:f,pos:h,children:null,data:v,isStart:[].concat(ee(f?f.isStart:[]),[p===0]),isEnd:[].concat(ee(f?f.isEnd:[]),[p===u.length-1])});return d.push(x),t===!0||o.has(b)?x.children=i(v[c]||[],x):x.children=[],x})}return i(n),d}function Cn(n,t,r){var e={};Je(r)==="object"?e=r:e={externalGetKey:r},e=e||{};var a=e,l=a.childrenPropName,c=a.externalGetKey,o=a.fieldNames,d=$e(o),i=d.key,u=d.children,f=l||u,v;c?typeof c=="string"?v=function(b){return b[c]}:typeof c=="function"&&(v=function(b){return c(b)}):v=function(b,g){return Re(b[i],g)};function p(h,b,g,m){var K=h?h[f]:n,x=h?It(g.pos,b):"0",E=h?[].concat(ee(m),[h]):[];if(h){var S=v(h,x),$={node:h,index:b,pos:x,key:S,parentPos:g.node?g.pos:null,level:g.level+1,nodes:E};t($)}K&&K.forEach(function(w,k){p(w,k,{node:h,pos:x,level:g?g.level+1:-1},E)})}p(null)}function Mt(n){var t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},r=t.initWrapper,e=t.processEntity,a=t.onProcessFinished,l=t.externalGetKey,c=t.childrenPropName,o=t.fieldNames,d=arguments.length>2?arguments[2]:void 0,i=l||d,u={},f={},v={posEntities:u,keyEntities:f};return r&&(v=r(v)||v),Cn(n,function(p){var h=p.node,b=p.index,g=p.pos,m=p.key,K=p.parentPos,x=p.level,E=p.nodes,S={node:h,nodes:E,index:b,key:m,pos:g,level:x},$=Re(m,g);u[g]=S,f[$]=S,S.parent=u[K],S.parent&&(S.parent.children=S.parent.children||[],S.parent.children.push(S)),e&&e(S,v)},{externalGetKey:i,childrenPropName:c,fieldNames:o}),a&&a(v),v}function Te(n,t){var r=t.expandedKeys,e=t.selectedKeys,a=t.loadedKeys,l=t.loadingKeys,c=t.checkedKeys,o=t.halfCheckedKeys,d=t.dragOverNodeKey,i=t.dropPosition,u=t.keyEntities,f=U(u,n),v={eventKey:n,expanded:r.indexOf(n)!==-1,selected:e.indexOf(n)!==-1,loaded:a.indexOf(n)!==-1,loading:l.indexOf(n)!==-1,checked:c.indexOf(n)!==-1,halfChecked:o.indexOf(n)!==-1,pos:String(f?f.pos:""),dragOver:d===n&&i===0,dragOverGapTop:d===n&&i===-1,dragOverGapBottom:d===n&&i===1};return v}function j(n){var t=n.data,r=n.expanded,e=n.selected,a=n.checked,l=n.loaded,c=n.loading,o=n.halfChecked,d=n.dragOver,i=n.dragOverGapTop,u=n.dragOverGapBottom,f=n.pos,v=n.active,p=n.eventKey,h=V(V({},t),{},{expanded:r,selected:e,checked:a,loaded:l,loading:c,halfChecked:o,dragOver:d,dragOverGapTop:i,dragOverGapBottom:u,pos:f,active:v,key:p});return"props"in h||Object.defineProperty(h,"props",{get:function(){return fe(!1,"Second param return from event is node data instead of TreeNode instance. Please read value directly instead of reading from `props`."),n}}),h}function Rt(n,t){var r=new Set;return n.forEach(function(e){t.has(e)||r.add(e)}),r}function Kn(n){var t=n||{},r=t.disabled,e=t.disableCheckbox,a=t.checkable;return!!(r||e)||a===!1}function En(n,t,r,e){for(var a=new Set(n),l=new Set,c=0;c<=r;c+=1){var o=t.get(c)||new Set;o.forEach(function(f){var v=f.key,p=f.node,h=f.children,b=h===void 0?[]:h;a.has(v)&&!e(p)&&b.filter(function(g){return!e(g.node)}).forEach(function(g){a.add(g.key)})})}for(var d=new Set,i=r;i>=0;i-=1){var u=t.get(i)||new Set;u.forEach(function(f){var v=f.parent,p=f.node;if(!(e(p)||!f.parent||d.has(f.parent.key))){if(e(f.parent.node)){d.add(v.key);return}var h=!0,b=!1;(v.children||[]).filter(function(g){return!e(g.node)}).forEach(function(g){var m=g.key,K=a.has(m);h&&!K&&(h=!1),!b&&(K||l.has(m))&&(b=!0)}),h&&a.add(v.key),b&&l.add(v.key),d.add(v.key)}})}return{checkedKeys:Array.from(a),halfCheckedKeys:Array.from(Rt(l,a))}}function Sn(n,t,r,e,a){for(var l=new Set(n),c=new Set(t),o=0;o<=e;o+=1){var d=r.get(o)||new Set;d.forEach(function(v){var p=v.key,h=v.node,b=v.children,g=b===void 0?[]:b;!l.has(p)&&!c.has(p)&&!a(h)&&g.filter(function(m){return!a(m.node)}).forEach(function(m){l.delete(m.key)})})}c=new Set;for(var i=new Set,u=e;u>=0;u-=1){var f=r.get(u)||new Set;f.forEach(function(v){var p=v.parent,h=v.node;if(!(a(h)||!v.parent||i.has(v.parent.key))){if(a(v.parent.node)){i.add(p.key);return}var b=!0,g=!1;(p.children||[]).filter(function(m){return!a(m.node)}).forEach(function(m){var K=m.key,x=l.has(K);b&&!x&&(b=!1),!g&&(x||c.has(K))&&(g=!0)}),b||l.delete(p.key),g&&c.add(p.key),i.add(p.key)}})}return{checkedKeys:Array.from(l),halfCheckedKeys:Array.from(Rt(c,l))}}function We(n,t,r,e){var a=[],l;e?l=e:l=Kn;var c=new Set(n.filter(function(u){var f=!!U(r,u);return f||a.push(u),f})),o=new Map,d=0;Object.keys(r).forEach(function(u){var f=r[u],v=f.level,p=o.get(v);p||(p=new Set,o.set(v,p)),p.add(f),d=Math.max(d,v)}),fe(!a.length,"Tree missing follow keys: ".concat(a.slice(0,100).map(function(u){return"'".concat(u,"'")}).join(", ")));var i;return t===!0?i=En(c,o,d,l):i=Sn(c,t.halfCheckedKeys,o,d,l),i}const Nn=n=>{const{checkboxCls:t}=n,r=`${t}-wrapper`;return[{[`${t}-group`]:Object.assign(Object.assign({},Ie(n)),{display:"inline-flex",flexWrap:"wrap",columnGap:n.marginXS,[`> ${n.antCls}-row`]:{flex:1}}),[r]:Object.assign(Object.assign({},Ie(n)),{display:"inline-flex",alignItems:"baseline",cursor:"pointer","&:after":{display:"inline-block",width:0,overflow:"hidden",content:"'\\a0'"},[`& + ${r}`]:{marginInlineStart:0},[`&${r}-in-form-item`]:{'input[type="checkbox"]':{width:14,height:14}}}),[t]:Object.assign(Object.assign({},Ie(n)),{position:"relative",whiteSpace:"nowrap",lineHeight:1,cursor:"pointer",borderRadius:n.borderRadiusSM,alignSelf:"center",[`${t}-input`]:{position:"absolute",inset:0,zIndex:1,cursor:"pointer",opacity:0,margin:0,[`&:focus-visible + ${t}-inner`]:wt(n)},[`${t}-inner`]:{boxSizing:"border-box",display:"block",width:n.checkboxSize,height:n.checkboxSize,direction:"ltr",backgroundColor:n.colorBgContainer,border:`${be(n.lineWidth)} ${n.lineType} ${n.colorBorder}`,borderRadius:n.borderRadiusSM,borderCollapse:"separate",transition:`all ${n.motionDurationSlow}`,"&:after":{boxSizing:"border-box",position:"absolute",top:"50%",insetInlineStart:"25%",display:"table",width:n.calc(n.checkboxSize).div(14).mul(5).equal(),height:n.calc(n.checkboxSize).div(14).mul(8).equal(),border:`${be(n.lineWidthBold)} solid ${n.colorWhite}`,borderTop:0,borderInlineStart:0,transform:"rotate(45deg) scale(0) translate(-50%,-50%)",opacity:0,content:'""',transition:`all ${n.motionDurationFast} ${n.motionEaseInBack}, opacity ${n.motionDurationFast}`}},"& + span":{paddingInlineStart:n.paddingXS,paddingInlineEnd:n.paddingXS}})},{[`
        ${r}:not(${r}-disabled),
        ${t}:not(${t}-disabled)
      `]:{[`&:hover ${t}-inner`]:{borderColor:n.colorPrimary}},[`${r}:not(${r}-disabled)`]:{[`&:hover ${t}-checked:not(${t}-disabled) ${t}-inner`]:{backgroundColor:n.colorPrimaryHover,borderColor:"transparent"},[`&:hover ${t}-checked:not(${t}-disabled):after`]:{borderColor:n.colorPrimaryHover}}},{[`${t}-checked`]:{[`${t}-inner`]:{backgroundColor:n.colorPrimary,borderColor:n.colorPrimary,"&:after":{opacity:1,transform:"rotate(45deg) scale(1) translate(-50%,-50%)",transition:`all ${n.motionDurationMid} ${n.motionEaseOutBack} ${n.motionDurationFast}`}}},[`
        ${r}-checked:not(${r}-disabled),
        ${t}-checked:not(${t}-disabled)
      `]:{[`&:hover ${t}-inner`]:{backgroundColor:n.colorPrimaryHover,borderColor:"transparent"}}},{[t]:{"&-indeterminate":{"&":{[`${t}-inner`]:{backgroundColor:`${n.colorBgContainer}`,borderColor:`${n.colorBorder}`,"&:after":{top:"50%",insetInlineStart:"50%",width:n.calc(n.fontSizeLG).div(2).equal(),height:n.calc(n.fontSizeLG).div(2).equal(),backgroundColor:n.colorPrimary,border:0,transform:"translate(-50%, -50%) scale(1)",opacity:1,content:'""'}},[`&:hover ${t}-inner`]:{backgroundColor:`${n.colorBgContainer}`,borderColor:`${n.colorPrimary}`}}}}},{[`${r}-disabled`]:{cursor:"not-allowed"},[`${t}-disabled`]:{[`&, ${t}-input`]:{cursor:"not-allowed",pointerEvents:"none"},[`${t}-inner`]:{background:n.colorBgContainerDisabled,borderColor:n.colorBorder,"&:after":{borderColor:n.colorTextDisabled}},"&:after":{display:"none"},"& + span":{color:n.colorTextDisabled},[`&${t}-indeterminate ${t}-inner::after`]:{background:n.colorTextDisabled}}}]};function Lt(n,t){const r=kt(t,{checkboxCls:`.${n}`,checkboxSize:t.controlInteractiveSize});return Nn(r)}const Yr=Qe("Checkbox",(n,{prefixCls:t})=>[Lt(t,n)]);var $n={icon:{tag:"svg",attrs:{viewBox:"0 0 1024 1024",focusable:"false"},children:[{tag:"path",attrs:{d:"M840.4 300H183.6c-19.7 0-30.7 20.8-18.5 35l328.4 380.8c9.4 10.9 27.5 10.9 37 0L858.9 335c12.2-14.2 1.2-35-18.5-35z"}}]},name:"caret-down",theme:"filled"};const kn=$n;var wn=function(t,r){return y.createElement(ge,G({},t,{ref:r,icon:kn}))},On=y.forwardRef(wn);const Dn=On;var Pn={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M854.6 288.6L639.4 73.4c-6-6-14.1-9.4-22.6-9.4H192c-17.7 0-32 14.3-32 32v832c0 17.7 14.3 32 32 32h640c17.7 0 32-14.3 32-32V311.3c0-8.5-3.4-16.7-9.4-22.7zM790.2 326H602V137.8L790.2 326zm1.8 562H232V136h302v216a42 42 0 0042 42h216v494z"}}]},name:"file",theme:"outlined"};const In=Pn;var Tn=function(t,r){return y.createElement(ge,G({},t,{ref:r,icon:In}))},Mn=y.forwardRef(Tn);const At=Mn;var Rn={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M928 444H820V330.4c0-17.7-14.3-32-32-32H473L355.7 186.2a8.15 8.15 0 00-5.5-2.2H96c-17.7 0-32 14.3-32 32v592c0 17.7 14.3 32 32 32h698c13 0 24.8-7.9 29.7-20l134-332c1.5-3.8 2.3-7.9 2.3-12 0-17.7-14.3-32-32-32zM136 256h188.5l119.6 114.4H748V444H238c-13 0-24.8 7.9-29.7 20L136 643.2V256zm635.3 512H159l103.3-256h612.4L771.3 768z"}}]},name:"folder-open",theme:"outlined"};const Ln=Rn;var An=function(t,r){return y.createElement(ge,G({},t,{ref:r,icon:Ln}))},_n=y.forwardRef(An);const Hn=_n;var Bn={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M880 298.4H521L403.7 186.2a8.15 8.15 0 00-5.5-2.2H144c-17.7 0-32 14.3-32 32v592c0 17.7 14.3 32 32 32h736c17.7 0 32-14.3 32-32V330.4c0-17.7-14.3-32-32-32zM840 768H184V256h188.5l119.6 114.4H840V768z"}}]},name:"folder",theme:"outlined"};const jn=Bn;var zn=function(t,r){return y.createElement(ge,G({},t,{ref:r,icon:jn}))},Fn=y.forwardRef(zn);const qn=Fn;var Wn={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M300 276.5a56 56 0 1056-97 56 56 0 00-56 97zm0 284a56 56 0 1056-97 56 56 0 00-56 97zM640 228a56 56 0 10112 0 56 56 0 00-112 0zm0 284a56 56 0 10112 0 56 56 0 00-112 0zM300 844.5a56 56 0 1056-97 56 56 0 00-56 97zM640 796a56 56 0 10112 0 56 56 0 00-112 0z"}}]},name:"holder",theme:"outlined"};const Vn=Wn;var Gn=function(t,r){return y.createElement(ge,G({},t,{ref:r,icon:Vn}))},Un=y.forwardRef(Gn);const Xn=Un;var Yn={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm0 820c-205.4 0-372-166.6-372-372s166.6-372 372-372 372 166.6 372 372-166.6 372-372 372z"}},{tag:"path",attrs:{d:"M464 336a48 48 0 1096 0 48 48 0 10-96 0zm72 112h-48c-4.4 0-8 3.6-8 8v272c0 4.4 3.6 8 8 8h48c4.4 0 8-3.6 8-8V456c0-4.4-3.6-8-8-8z"}}]},name:"info-circle",theme:"outlined"};const Jn=Yn;var Qn=function(t,r){return y.createElement(ge,G({},t,{ref:r,icon:Jn}))},Zn=y.forwardRef(Qn);const Jr=Zn;var er={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M328 544h368c4.4 0 8-3.6 8-8v-48c0-4.4-3.6-8-8-8H328c-4.4 0-8 3.6-8 8v48c0 4.4 3.6 8 8 8z"}},{tag:"path",attrs:{d:"M880 112H144c-17.7 0-32 14.3-32 32v736c0 17.7 14.3 32 32 32h736c17.7 0 32-14.3 32-32V144c0-17.7-14.3-32-32-32zm-40 728H184V184h656v656z"}}]},name:"minus-square",theme:"outlined"};const tr=er;var nr=function(t,r){return y.createElement(ge,G({},t,{ref:r,icon:tr}))},rr=y.forwardRef(nr);const or=rr;var ar={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M328 544h152v152c0 4.4 3.6 8 8 8h48c4.4 0 8-3.6 8-8V544h152c4.4 0 8-3.6 8-8v-48c0-4.4-3.6-8-8-8H544V328c0-4.4-3.6-8-8-8h-48c-4.4 0-8 3.6-8 8v152H328c-4.4 0-8 3.6-8 8v48c0 4.4 3.6 8 8 8z"}},{tag:"path",attrs:{d:"M880 112H144c-17.7 0-32 14.3-32 32v736c0 17.7 14.3 32 32 32h736c17.7 0 32-14.3 32-32V144c0-17.7-14.3-32-32-32zm-40 728H184V184h656v656z"}}]},name:"plus-square",theme:"outlined"};const ir=ar;var lr=function(t,r){return y.createElement(ge,G({},t,{ref:r,icon:ir}))},dr=y.forwardRef(lr);const sr=dr;var et=y.createContext(null),cr=y.createContext({}),ur=function(t){for(var r=t.prefixCls,e=t.level,a=t.isStart,l=t.isEnd,c="".concat(r,"-indent-unit"),o=[],d=0;d<e;d+=1)o.push(y.createElement("span",{key:d,className:q(c,C(C({},"".concat(c,"-start"),a[d]),"".concat(c,"-end"),l[d]))}));return y.createElement("span",{"aria-hidden":"true",className:"".concat(r,"-indent")},o)};const fr=y.memo(ur);var gr=["eventKey","className","style","dragOver","dragOverGapTop","dragOverGapBottom","isLeaf","isStart","isEnd","expanded","selected","checked","halfChecked","loading","domRef","active","data","onMouseMove","selectable"],st="open",ct="close",vr="---",Me=function(t){var r,e,a,l=t.eventKey,c=t.className,o=t.style,d=t.dragOver,i=t.dragOverGapTop,u=t.dragOverGapBottom,f=t.isLeaf,v=t.isStart,p=t.isEnd,h=t.expanded,b=t.selected,g=t.checked,m=t.halfChecked,K=t.loading,x=t.domRef,E=t.active,S=t.data,$=t.onMouseMove,w=t.selectable,k=Be(t,gr),s=M.useContext(et),O=M.useContext(cr),R=M.useRef(null),L=M.useState(!1),D=ue(L,2),I=D[0],T=D[1],A=!!(s.disabled||t.disabled||(r=O.nodeDisabled)!==null&&r!==void 0&&r.call(O,S)),H=M.useMemo(function(){return!s.checkable||t.checkable===!1?!1:s.checkable},[s.checkable,t.checkable]),Y=function(N){A||s.onNodeSelect(N,j(t))},J=function(N){A||!H||t.disableCheckbox||s.onNodeCheck(N,j(t),!g)},z=M.useMemo(function(){return typeof w=="boolean"?w:s.selectable},[w,s.selectable]),ae=function(N){s.onNodeClick(N,j(t)),z?Y(N):J(N)},te=function(N){s.onNodeDoubleClick(N,j(t))},X=function(N){s.onNodeMouseEnter(N,j(t))},ne=function(N){s.onNodeMouseLeave(N,j(t))},W=function(N){s.onNodeContextMenu(N,j(t))},F=M.useMemo(function(){return!!(s.draggable&&(!s.draggable.nodeDraggable||s.draggable.nodeDraggable(S)))},[s.draggable,S]),ve=function(N){N.stopPropagation(),T(!0),s.onNodeDragStart(N,t);try{N.dataTransfer.setData("text/plain","")}catch{}},ie=function(N){N.preventDefault(),N.stopPropagation(),s.onNodeDragEnter(N,t)},ke=function(N){N.preventDefault(),N.stopPropagation(),s.onNodeDragOver(N,t)},Ce=function(N){N.stopPropagation(),s.onNodeDragLeave(N,t)},le=function(N){N.stopPropagation(),T(!1),s.onNodeDragEnd(N,t)},he=function(N){N.preventDefault(),N.stopPropagation(),T(!1),s.onNodeDrop(N,t)},je=function(N){K||s.onNodeExpand(N,j(t))},Ke=M.useMemo(function(){var _=U(s.keyEntities,l)||{},N=_.children;return!!(N||[]).length},[s.keyEntities,l]),de=M.useMemo(function(){return f===!1?!1:f||!s.loadData&&!Ke||s.loadData&&t.loaded&&!Ke},[f,s.loadData,Ke,t.loaded]);M.useEffect(function(){K||typeof s.loadData=="function"&&h&&!de&&!t.loaded&&s.onNodeLoad(j(t))},[K,s.loadData,s.onNodeLoad,h,de,t]);var we=M.useMemo(function(){var _;return(_=s.draggable)!==null&&_!==void 0&&_.icon?M.createElement("span",{className:"".concat(s.prefixCls,"-draggable-icon")},s.draggable.icon):null},[s.draggable]),Oe=function(N){var Z=t.switcherIcon||s.switcherIcon;return typeof Z=="function"?Z(V(V({},t),{},{isLeaf:N})):Z},De=function(){if(de){var N=Oe(!0);return N!==!1?M.createElement("span",{className:q("".concat(s.prefixCls,"-switcher"),"".concat(s.prefixCls,"-switcher-noop"))},N):null}var Z=Oe(!1);return Z!==!1?M.createElement("span",{onClick:je,className:q("".concat(s.prefixCls,"-switcher"),"".concat(s.prefixCls,"-switcher_").concat(h?st:ct))},Z):null},ze=M.useMemo(function(){if(!H)return null;var _=typeof H!="boolean"?H:null;return M.createElement("span",{className:q("".concat(s.prefixCls,"-checkbox"),C(C(C({},"".concat(s.prefixCls,"-checkbox-checked"),g),"".concat(s.prefixCls,"-checkbox-indeterminate"),!g&&m),"".concat(s.prefixCls,"-checkbox-disabled"),A||t.disableCheckbox)),onClick:J,role:"checkbox","aria-checked":m?"mixed":g,"aria-disabled":A||t.disableCheckbox,"aria-label":"Select ".concat(typeof t.title=="string"?t.title:"tree node")},_)},[H,g,m,A,t.disableCheckbox,t.title]),pe=M.useMemo(function(){return de?null:h?st:ct},[de,h]),B=M.useMemo(function(){return M.createElement("span",{className:q("".concat(s.prefixCls,"-iconEle"),"".concat(s.prefixCls,"-icon__").concat(pe||"docu"),C({},"".concat(s.prefixCls,"-icon_loading"),K))})},[s.prefixCls,pe,K]),Q=M.useMemo(function(){var _=!!s.draggable,N=!t.disabled&&_&&s.dragOverNodeKey===l;return N?s.dropIndicatorRender({dropPosition:s.dropPosition,dropLevelOffset:s.dropLevelOffset,indent:s.indent,prefixCls:s.prefixCls,direction:s.direction}):null},[s.dropPosition,s.dropLevelOffset,s.indent,s.prefixCls,s.direction,s.draggable,s.dragOverNodeKey,s.dropIndicatorRender]),re=M.useMemo(function(){var _=t.title,N=_===void 0?vr:_,Z="".concat(s.prefixCls,"-node-content-wrapper"),Fe;if(s.showIcon){var Le=t.icon||s.icon;Fe=Le?M.createElement("span",{className:q("".concat(s.prefixCls,"-iconEle"),"".concat(s.prefixCls,"-icon__customize"))},typeof Le=="function"?Le(t):Le):B}else s.loadData&&K&&(Fe=B);var Ae;return typeof N=="function"?Ae=N(S):s.titleRender?Ae=s.titleRender(S):Ae=N,M.createElement("span",{ref:R,title:typeof N=="string"?N:"",className:q(Z,"".concat(Z,"-").concat(pe||"normal"),C({},"".concat(s.prefixCls,"-node-selected"),!A&&(b||I))),onMouseEnter:X,onMouseLeave:ne,onContextMenu:W,onClick:ae,onDoubleClick:te},Fe,M.createElement("span",{className:"".concat(s.prefixCls,"-title")},Ae),Q)},[s.prefixCls,s.showIcon,t,s.icon,B,s.titleRender,S,pe,X,ne,W,ae,te]),Ee=Ze(k,{aria:!0,data:!0}),ye=U(s.keyEntities,l)||{},Pe=ye.level,Se=p[p.length-1],se=!A&&F,me=s.draggingNodeKey===l,at=w!==void 0?{"aria-selected":!!w}:void 0;return M.createElement("div",G({ref:x,role:"treeitem","aria-expanded":f?void 0:h,className:q(c,"".concat(s.prefixCls,"-treenode"),(a={},C(C(C(C(C(C(C(C(C(C(a,"".concat(s.prefixCls,"-treenode-disabled"),A),"".concat(s.prefixCls,"-treenode-switcher-").concat(h?"open":"close"),!f),"".concat(s.prefixCls,"-treenode-checkbox-checked"),g),"".concat(s.prefixCls,"-treenode-checkbox-indeterminate"),m),"".concat(s.prefixCls,"-treenode-selected"),b),"".concat(s.prefixCls,"-treenode-loading"),K),"".concat(s.prefixCls,"-treenode-active"),E),"".concat(s.prefixCls,"-treenode-leaf-last"),Se),"".concat(s.prefixCls,"-treenode-draggable"),F),"dragging",me),C(C(C(C(C(C(C(a,"drop-target",s.dropTargetKey===l),"drop-container",s.dropContainerKey===l),"drag-over",!A&&d),"drag-over-gap-top",!A&&i),"drag-over-gap-bottom",!A&&u),"filter-node",(e=s.filterTreeNode)===null||e===void 0?void 0:e.call(s,j(t))),"".concat(s.prefixCls,"-treenode-leaf"),de))),style:o,draggable:se,onDragStart:se?ve:void 0,onDragEnter:F?ie:void 0,onDragOver:F?ke:void 0,onDragLeave:F?Ce:void 0,onDrop:F?he:void 0,onDragEnd:F?le:void 0,onMouseMove:$},at,Ee),M.createElement(fr,{prefixCls:s.prefixCls,level:Pe,isStart:v,isEnd:p}),we,De(),ze,re)};Me.isTreeNode=1;function oe(n,t){if(!n)return[];var r=n.slice(),e=r.indexOf(t);return e>=0&&r.splice(e,1),r}function ce(n,t){var r=(n||[]).slice();return r.indexOf(t)===-1&&r.push(t),r}function tt(n){return n.split("-")}function hr(n,t){var r=[],e=U(t,n);function a(){var l=arguments.length>0&&arguments[0]!==void 0?arguments[0]:[];l.forEach(function(c){var o=c.key,d=c.children;r.push(o),a(d)})}return a(e.children),r}function pr(n){if(n.parent){var t=tt(n.pos);return Number(t[t.length-1])===n.parent.children.length-1}return!1}function yr(n){var t=tt(n.pos);return Number(t[t.length-1])===0}function ut(n,t,r,e,a,l,c,o,d,i){var u,f=n.clientX,v=n.clientY,p=n.target.getBoundingClientRect(),h=p.top,b=p.height,g=(i==="rtl"?-1:1)*(((a==null?void 0:a.x)||0)-f),m=(g-12)/e,K=d.filter(function(A){var H;return(H=o[A])===null||H===void 0||(H=H.children)===null||H===void 0?void 0:H.length}),x=U(o,r.eventKey);if(v<h+b/2){var E=c.findIndex(function(A){return A.key===x.key}),S=E<=0?0:E-1,$=c[S].key;x=U(o,$)}var w=x.key,k=x,s=x.key,O=0,R=0;if(!K.includes(w))for(var L=0;L<m&&pr(x);L+=1)x=x.parent,R+=1;var D=t.data,I=x.node,T=!0;return yr(x)&&x.level===0&&v<h+b/2&&l({dragNode:D,dropNode:I,dropPosition:-1})&&x.key===r.eventKey?O=-1:(k.children||[]).length&&K.includes(s)?l({dragNode:D,dropNode:I,dropPosition:0})?O=0:T=!1:R===0?m>-1.5?l({dragNode:D,dropNode:I,dropPosition:1})?O=1:T=!1:l({dragNode:D,dropNode:I,dropPosition:0})?O=0:l({dragNode:D,dropNode:I,dropPosition:1})?O=1:T=!1:l({dragNode:D,dropNode:I,dropPosition:1})?O=1:T=!1,{dropPosition:O,dropLevelOffset:R,dropTargetKey:x.key,dropTargetPos:x.pos,dragOverNodeKey:s,dropContainerKey:O===0?null:((u=x.parent)===null||u===void 0?void 0:u.key)||null,dropAllowed:T}}function ft(n,t){if(n){var r=t.multiple;return r?n.slice():n.length?[n[0]]:n}}function Ve(n){if(!n)return null;var t;if(Array.isArray(n))t={checkedKeys:n,halfCheckedKeys:void 0};else if(Je(n)==="object")t={checkedKeys:n.checked||void 0,halfCheckedKeys:n.halfChecked||void 0};else return fe(!1,"`checkedKeys` is not an array or an object"),null;return t}function Xe(n,t){var r=new Set;function e(a){if(!r.has(a)){var l=U(t,a);if(l){r.add(a);var c=l.parent,o=l.node;o.disabled||c&&e(c.key)}}}return(n||[]).forEach(function(a){e(a)}),ee(r)}var mr=function(t){var r=t.dropPosition,e=t.dropLevelOffset,a=t.indent,l={pointerEvents:"none",position:"absolute",right:0,backgroundColor:"red",height:2};switch(r){case-1:l.top=0,l.left=-e*a;break;case 1:l.bottom=0,l.left=-e*a;break;case 0:l.bottom=0,l.left=a;break}return M.createElement("div",{style:l})};function _t(n){if(n==null)throw new TypeError("Cannot destructure "+n)}function br(n,t){var r=y.useState(!1),e=ue(r,2),a=e[0],l=e[1];He(function(){if(a)return n(),function(){t()}},[a]),He(function(){return l(!0),function(){l(!1)}},[])}var xr=["className","style","motion","motionNodes","motionType","onMotionStart","onMotionEnd","active","treeNodeRequiredProps"],Cr=y.forwardRef(function(n,t){var r=n.className,e=n.style,a=n.motion,l=n.motionNodes,c=n.motionType,o=n.onMotionStart,d=n.onMotionEnd,i=n.active,u=n.treeNodeRequiredProps,f=Be(n,xr),v=y.useState(!0),p=ue(v,2),h=p[0],b=p[1],g=y.useContext(et),m=g.prefixCls,K=l&&c!=="hide";He(function(){l&&K!==h&&b(K)},[l]);var x=function(){l&&o()},E=y.useRef(!1),S=function(){l&&!E.current&&(E.current=!0,d())};br(x,S);var $=function(k){K===k&&S()};return l?y.createElement($t,G({ref:t,visible:h},a,{motionAppear:c==="show",onVisibleChanged:$}),function(w,k){var s=w.className,O=w.style;return y.createElement("div",{ref:k,className:q("".concat(m,"-treenode-motion"),s),style:O},l.map(function(R){var L=Object.assign({},(_t(R.data),R.data)),D=R.title,I=R.key,T=R.isStart,A=R.isEnd;delete L.children;var H=Te(I,u);return y.createElement(Me,G({},L,H,{title:D,active:i,data:R.data,key:I,isStart:T,isEnd:A}))}))}):y.createElement(Me,G({domRef:t,className:r,style:e},f,{active:i}))});function Kr(){var n=arguments.length>0&&arguments[0]!==void 0?arguments[0]:[],t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:[],r=n.length,e=t.length;if(Math.abs(r-e)!==1)return{add:!1,key:null};function a(l,c){var o=new Map;l.forEach(function(i){o.set(i,!0)});var d=c.filter(function(i){return!o.has(i)});return d.length===1?d[0]:null}return r<e?{add:!0,key:a(n,t)}:{add:!1,key:a(t,n)}}function gt(n,t,r){var e=n.findIndex(function(o){return o.key===r}),a=n[e+1],l=t.findIndex(function(o){return o.key===r});if(a){var c=t.findIndex(function(o){return o.key===a.key});return t.slice(l+1,c)}return t.slice(l+1)}var Er=["prefixCls","data","selectable","checkable","expandedKeys","selectedKeys","checkedKeys","loadedKeys","loadingKeys","halfCheckedKeys","keyEntities","disabled","dragging","dragOverNodeKey","dropPosition","motion","height","itemHeight","virtual","scrollWidth","focusable","activeItem","focused","tabIndex","onKeyDown","onFocus","onBlur","onActiveChange","onListChangeStart","onListChangeEnd"],vt={width:0,height:0,display:"flex",overflow:"hidden",opacity:0,border:0,padding:0,margin:0},Sr=function(){},xe="RC_TREE_MOTION_".concat(Math.random()),Ye={key:xe},Ht={key:xe,level:0,index:0,pos:"0",node:Ye,nodes:[Ye]},ht={parent:null,children:[],pos:Ht.pos,data:Ye,title:null,key:xe,isStart:[],isEnd:[]};function pt(n,t,r,e){return t===!1||!r?n:n.slice(0,Math.ceil(r/e)+1)}function yt(n){var t=n.key,r=n.pos;return Re(t,r)}function Nr(n){for(var t=String(n.data.key),r=n;r.parent;)r=r.parent,t="".concat(r.data.key," > ").concat(t);return t}var $r=y.forwardRef(function(n,t){var r=n.prefixCls,e=n.data;n.selectable,n.checkable;var a=n.expandedKeys,l=n.selectedKeys,c=n.checkedKeys,o=n.loadedKeys,d=n.loadingKeys,i=n.halfCheckedKeys,u=n.keyEntities,f=n.disabled,v=n.dragging,p=n.dragOverNodeKey,h=n.dropPosition,b=n.motion,g=n.height,m=n.itemHeight,K=n.virtual,x=n.scrollWidth,E=n.focusable,S=n.activeItem,$=n.focused,w=n.tabIndex,k=n.onKeyDown,s=n.onFocus,O=n.onBlur,R=n.onActiveChange,L=n.onListChangeStart,D=n.onListChangeEnd,I=Be(n,Er),T=y.useRef(null),A=y.useRef(null);y.useImperativeHandle(t,function(){return{scrollTo:function(Q){T.current.scrollTo(Q)},getIndentWidth:function(){return A.current.offsetWidth}}});var H=y.useState(a),Y=ue(H,2),J=Y[0],z=Y[1],ae=y.useState(e),te=ue(ae,2),X=te[0],ne=te[1],W=y.useState(e),F=ue(W,2),ve=F[0],ie=F[1],ke=y.useState([]),Ce=ue(ke,2),le=Ce[0],he=Ce[1],je=y.useState(null),Ke=ue(je,2),de=Ke[0],we=Ke[1],Oe=y.useRef(e);Oe.current=e;function De(){var B=Oe.current;ne(B),ie(B),he([]),we(null),D()}He(function(){z(a);var B=Kr(J,a);if(B.key!==null)if(B.add){var Q=X.findIndex(function(se){var me=se.key;return me===B.key}),re=pt(gt(X,e,B.key),K,g,m),Ee=X.slice();Ee.splice(Q+1,0,ht),ie(Ee),he(re),we("show")}else{var ye=e.findIndex(function(se){var me=se.key;return me===B.key}),Pe=pt(gt(e,X,B.key),K,g,m),Se=e.slice();Se.splice(ye+1,0,ht),ie(Se),he(Pe),we("hide")}else X!==e&&(ne(e),ie(e))},[a,e]),y.useEffect(function(){v||De()},[v]);var ze=b?ve:e,pe={expandedKeys:a,selectedKeys:l,loadedKeys:o,loadingKeys:d,checkedKeys:c,halfCheckedKeys:i,dragOverNodeKey:p,dropPosition:h,keyEntities:u};return y.createElement(y.Fragment,null,$&&S&&y.createElement("span",{style:vt,"aria-live":"assertive"},Nr(S)),y.createElement("div",null,y.createElement("input",{style:vt,disabled:E===!1||f,tabIndex:E!==!1?w:null,onKeyDown:k,onFocus:s,onBlur:O,value:"",onChange:Sr,"aria-label":"for screen reader"})),y.createElement("div",{className:"".concat(r,"-treenode"),"aria-hidden":!0,style:{position:"absolute",pointerEvents:"none",visibility:"hidden",height:0,overflow:"hidden",border:0,padding:0}},y.createElement("div",{className:"".concat(r,"-indent")},y.createElement("div",{ref:A,className:"".concat(r,"-indent-unit")}))),y.createElement(Zt,G({},I,{data:ze,itemKey:yt,height:g,fullHeight:!1,virtual:K,itemHeight:m,scrollWidth:x,prefixCls:"".concat(r,"-list"),ref:T,role:"tree",onVisibleChange:function(Q){Q.every(function(re){return yt(re)!==xe})&&De()}}),function(B){var Q=B.pos,re=Object.assign({},(_t(B.data),B.data)),Ee=B.title,ye=B.key,Pe=B.isStart,Se=B.isEnd,se=Re(ye,Q);delete re.key,delete re.children;var me=Te(se,pe);return y.createElement(Cr,G({},re,me,{title:Ee,active:!!S&&ye===S.key,pos:Q,data:B.data,isStart:Pe,isEnd:Se,motion:b,motionNodes:ye===xe?le:null,motionType:de,onMotionStart:L,onMotionEnd:De,treeNodeRequiredProps:pe,onMouseMove:function(){R(null)}}))}))}),kr=10,nt=function(n){Et(r,n);var t=Ft(r);function r(){var e;Nt(this,r);for(var a=arguments.length,l=new Array(a),c=0;c<a;c++)l[c]=arguments[c];return e=t.call.apply(t,[this].concat(l)),C(P(e),"destroyed",!1),C(P(e),"delayedDragEnterLogic",void 0),C(P(e),"loadingRetryTimes",{}),C(P(e),"state",{keyEntities:{},indent:null,selectedKeys:[],checkedKeys:[],halfCheckedKeys:[],loadedKeys:[],loadingKeys:[],expandedKeys:[],draggingNodeKey:null,dragChildrenKeys:[],dropTargetKey:null,dropPosition:null,dropContainerKey:null,dropLevelOffset:null,dropTargetPos:null,dropAllowed:!0,dragOverNodeKey:null,treeData:[],flattenNodes:[],focused:!1,activeKey:null,listChanging:!1,prevProps:null,fieldNames:$e()}),C(P(e),"dragStartMousePosition",null),C(P(e),"dragNodeProps",null),C(P(e),"currentMouseOverDroppableNodeKey",null),C(P(e),"listRef",y.createRef()),C(P(e),"onNodeDragStart",function(o,d){var i=e.state,u=i.expandedKeys,f=i.keyEntities,v=e.props.onDragStart,p=d.eventKey;e.dragNodeProps=d,e.dragStartMousePosition={x:o.clientX,y:o.clientY};var h=oe(u,p);e.setState({draggingNodeKey:p,dragChildrenKeys:hr(p,f),indent:e.listRef.current.getIndentWidth()}),e.setExpandedKeys(h),window.addEventListener("dragend",e.onWindowDragEnd),v==null||v({event:o,node:j(d)})}),C(P(e),"onNodeDragEnter",function(o,d){var i=e.state,u=i.expandedKeys,f=i.keyEntities,v=i.dragChildrenKeys,p=i.flattenNodes,h=i.indent,b=e.props,g=b.onDragEnter,m=b.onExpand,K=b.allowDrop,x=b.direction,E=d.pos,S=d.eventKey;if(e.currentMouseOverDroppableNodeKey!==S&&(e.currentMouseOverDroppableNodeKey=S),!e.dragNodeProps){e.resetDragState();return}var $=ut(o,e.dragNodeProps,d,h,e.dragStartMousePosition,K,p,f,u,x),w=$.dropPosition,k=$.dropLevelOffset,s=$.dropTargetKey,O=$.dropContainerKey,R=$.dropTargetPos,L=$.dropAllowed,D=$.dragOverNodeKey;if(v.includes(s)||!L){e.resetDragState();return}if(e.delayedDragEnterLogic||(e.delayedDragEnterLogic={}),Object.keys(e.delayedDragEnterLogic).forEach(function(I){clearTimeout(e.delayedDragEnterLogic[I])}),e.dragNodeProps.eventKey!==d.eventKey&&(o.persist(),e.delayedDragEnterLogic[E]=window.setTimeout(function(){if(e.state.draggingNodeKey!==null){var I=ee(u),T=U(f,d.eventKey);T&&(T.children||[]).length&&(I=ce(u,d.eventKey)),e.props.hasOwnProperty("expandedKeys")||e.setExpandedKeys(I),m==null||m(I,{node:j(d),expanded:!0,nativeEvent:o.nativeEvent})}},800)),e.dragNodeProps.eventKey===s&&k===0){e.resetDragState();return}e.setState({dragOverNodeKey:D,dropPosition:w,dropLevelOffset:k,dropTargetKey:s,dropContainerKey:O,dropTargetPos:R,dropAllowed:L}),g==null||g({event:o,node:j(d),expandedKeys:u})}),C(P(e),"onNodeDragOver",function(o,d){var i=e.state,u=i.dragChildrenKeys,f=i.flattenNodes,v=i.keyEntities,p=i.expandedKeys,h=i.indent,b=e.props,g=b.onDragOver,m=b.allowDrop,K=b.direction;if(e.dragNodeProps){var x=ut(o,e.dragNodeProps,d,h,e.dragStartMousePosition,m,f,v,p,K),E=x.dropPosition,S=x.dropLevelOffset,$=x.dropTargetKey,w=x.dropContainerKey,k=x.dropTargetPos,s=x.dropAllowed,O=x.dragOverNodeKey;u.includes($)||!s||(e.dragNodeProps.eventKey===$&&S===0?e.state.dropPosition===null&&e.state.dropLevelOffset===null&&e.state.dropTargetKey===null&&e.state.dropContainerKey===null&&e.state.dropTargetPos===null&&e.state.dropAllowed===!1&&e.state.dragOverNodeKey===null||e.resetDragState():E===e.state.dropPosition&&S===e.state.dropLevelOffset&&$===e.state.dropTargetKey&&w===e.state.dropContainerKey&&k===e.state.dropTargetPos&&s===e.state.dropAllowed&&O===e.state.dragOverNodeKey||e.setState({dropPosition:E,dropLevelOffset:S,dropTargetKey:$,dropContainerKey:w,dropTargetPos:k,dropAllowed:s,dragOverNodeKey:O}),g==null||g({event:o,node:j(d)}))}}),C(P(e),"onNodeDragLeave",function(o,d){e.currentMouseOverDroppableNodeKey===d.eventKey&&!o.currentTarget.contains(o.relatedTarget)&&(e.resetDragState(),e.currentMouseOverDroppableNodeKey=null);var i=e.props.onDragLeave;i==null||i({event:o,node:j(d)})}),C(P(e),"onWindowDragEnd",function(o){e.onNodeDragEnd(o,null,!0),window.removeEventListener("dragend",e.onWindowDragEnd)}),C(P(e),"onNodeDragEnd",function(o,d){var i=e.props.onDragEnd;e.setState({dragOverNodeKey:null}),e.cleanDragState(),i==null||i({event:o,node:j(d)}),e.dragNodeProps=null,window.removeEventListener("dragend",e.onWindowDragEnd)}),C(P(e),"onNodeDrop",function(o,d){var i,u=arguments.length>2&&arguments[2]!==void 0?arguments[2]:!1,f=e.state,v=f.dragChildrenKeys,p=f.dropPosition,h=f.dropTargetKey,b=f.dropTargetPos,g=f.dropAllowed;if(g){var m=e.props.onDrop;if(e.setState({dragOverNodeKey:null}),e.cleanDragState(),h!==null){var K=V(V({},Te(h,e.getTreeNodeRequiredProps())),{},{active:((i=e.getActiveItem())===null||i===void 0?void 0:i.key)===h,data:U(e.state.keyEntities,h).node}),x=v.includes(h);fe(!x,"Can not drop to dragNode's children node. This is a bug of rc-tree. Please report an issue.");var E=tt(b),S={event:o,node:j(K),dragNode:e.dragNodeProps?j(e.dragNodeProps):null,dragNodesKeys:[e.dragNodeProps.eventKey].concat(v),dropToGap:p!==0,dropPosition:p+Number(E[E.length-1])};u||m==null||m(S),e.dragNodeProps=null}}}),C(P(e),"cleanDragState",function(){var o=e.state.draggingNodeKey;o!==null&&e.setState({draggingNodeKey:null,dropPosition:null,dropContainerKey:null,dropTargetKey:null,dropLevelOffset:null,dropAllowed:!0,dragOverNodeKey:null}),e.dragStartMousePosition=null,e.currentMouseOverDroppableNodeKey=null}),C(P(e),"triggerExpandActionExpand",function(o,d){var i=e.state,u=i.expandedKeys,f=i.flattenNodes,v=d.expanded,p=d.key,h=d.isLeaf;if(!(h||o.shiftKey||o.metaKey||o.ctrlKey)){var b=f.filter(function(m){return m.key===p})[0],g=j(V(V({},Te(p,e.getTreeNodeRequiredProps())),{},{data:b.data}));e.setExpandedKeys(v?oe(u,p):ce(u,p)),e.onNodeExpand(o,g)}}),C(P(e),"onNodeClick",function(o,d){var i=e.props,u=i.onClick,f=i.expandAction;f==="click"&&e.triggerExpandActionExpand(o,d),u==null||u(o,d)}),C(P(e),"onNodeDoubleClick",function(o,d){var i=e.props,u=i.onDoubleClick,f=i.expandAction;f==="doubleClick"&&e.triggerExpandActionExpand(o,d),u==null||u(o,d)}),C(P(e),"onNodeSelect",function(o,d){var i=e.state.selectedKeys,u=e.state,f=u.keyEntities,v=u.fieldNames,p=e.props,h=p.onSelect,b=p.multiple,g=d.selected,m=d[v.key],K=!g;K?b?i=ce(i,m):i=[m]:i=oe(i,m);var x=i.map(function(E){var S=U(f,E);return S?S.node:null}).filter(Boolean);e.setUncontrolledState({selectedKeys:i}),h==null||h(i,{event:"select",selected:K,node:d,selectedNodes:x,nativeEvent:o.nativeEvent})}),C(P(e),"onNodeCheck",function(o,d,i){var u=e.state,f=u.keyEntities,v=u.checkedKeys,p=u.halfCheckedKeys,h=e.props,b=h.checkStrictly,g=h.onCheck,m=d.key,K,x={event:"check",node:d,checked:i,nativeEvent:o.nativeEvent};if(b){var E=i?ce(v,m):oe(v,m),S=oe(p,m);K={checked:E,halfChecked:S},x.checkedNodes=E.map(function(R){return U(f,R)}).filter(Boolean).map(function(R){return R.node}),e.setUncontrolledState({checkedKeys:E})}else{var $=We([].concat(ee(v),[m]),!0,f),w=$.checkedKeys,k=$.halfCheckedKeys;if(!i){var s=new Set(w);s.delete(m);var O=We(Array.from(s),{checked:!1,halfCheckedKeys:k},f);w=O.checkedKeys,k=O.halfCheckedKeys}K=w,x.checkedNodes=[],x.checkedNodesPositions=[],x.halfCheckedKeys=k,w.forEach(function(R){var L=U(f,R);if(L){var D=L.node,I=L.pos;x.checkedNodes.push(D),x.checkedNodesPositions.push({node:D,pos:I})}}),e.setUncontrolledState({checkedKeys:w},!1,{halfCheckedKeys:k})}g==null||g(K,x)}),C(P(e),"onNodeLoad",function(o){var d,i=o.key,u=e.state.keyEntities,f=U(u,i);if(!(f!=null&&(d=f.children)!==null&&d!==void 0&&d.length)){var v=new Promise(function(p,h){e.setState(function(b){var g=b.loadedKeys,m=g===void 0?[]:g,K=b.loadingKeys,x=K===void 0?[]:K,E=e.props,S=E.loadData,$=E.onLoad;if(!S||m.includes(i)||x.includes(i))return null;var w=S(o);return w.then(function(){var k=e.state.loadedKeys,s=ce(k,i);$==null||$(s,{event:"load",node:o}),e.setUncontrolledState({loadedKeys:s}),e.setState(function(O){return{loadingKeys:oe(O.loadingKeys,i)}}),p()}).catch(function(k){if(e.setState(function(O){return{loadingKeys:oe(O.loadingKeys,i)}}),e.loadingRetryTimes[i]=(e.loadingRetryTimes[i]||0)+1,e.loadingRetryTimes[i]>=kr){var s=e.state.loadedKeys;fe(!1,"Retry for `loadData` many times but still failed. No more retry."),e.setUncontrolledState({loadedKeys:ce(s,i)}),p()}h(k)}),{loadingKeys:ce(x,i)}})});return v.catch(function(){}),v}}),C(P(e),"onNodeMouseEnter",function(o,d){var i=e.props.onMouseEnter;i==null||i({event:o,node:d})}),C(P(e),"onNodeMouseLeave",function(o,d){var i=e.props.onMouseLeave;i==null||i({event:o,node:d})}),C(P(e),"onNodeContextMenu",function(o,d){var i=e.props.onRightClick;i&&(o.preventDefault(),i({event:o,node:d}))}),C(P(e),"onFocus",function(){var o=e.props.onFocus;e.setState({focused:!0});for(var d=arguments.length,i=new Array(d),u=0;u<d;u++)i[u]=arguments[u];o==null||o.apply(void 0,i)}),C(P(e),"onBlur",function(){var o=e.props.onBlur;e.setState({focused:!1}),e.onActiveChange(null);for(var d=arguments.length,i=new Array(d),u=0;u<d;u++)i[u]=arguments[u];o==null||o.apply(void 0,i)}),C(P(e),"getTreeNodeRequiredProps",function(){var o=e.state,d=o.expandedKeys,i=o.selectedKeys,u=o.loadedKeys,f=o.loadingKeys,v=o.checkedKeys,p=o.halfCheckedKeys,h=o.dragOverNodeKey,b=o.dropPosition,g=o.keyEntities;return{expandedKeys:d||[],selectedKeys:i||[],loadedKeys:u||[],loadingKeys:f||[],checkedKeys:v||[],halfCheckedKeys:p||[],dragOverNodeKey:h,dropPosition:b,keyEntities:g}}),C(P(e),"setExpandedKeys",function(o){var d=e.state,i=d.treeData,u=d.fieldNames,f=qe(i,o,u);e.setUncontrolledState({expandedKeys:o,flattenNodes:f},!0)}),C(P(e),"onNodeExpand",function(o,d){var i=e.state.expandedKeys,u=e.state,f=u.listChanging,v=u.fieldNames,p=e.props,h=p.onExpand,b=p.loadData,g=d.expanded,m=d[v.key];if(!f){var K=i.includes(m),x=!g;if(fe(g&&K||!g&&!K,"Expand state not sync with index check"),i=x?ce(i,m):oe(i,m),e.setExpandedKeys(i),h==null||h(i,{node:d,expanded:x,nativeEvent:o.nativeEvent}),x&&b){var E=e.onNodeLoad(d);E&&E.then(function(){var S=qe(e.state.treeData,i,v);e.setUncontrolledState({flattenNodes:S})}).catch(function(){var S=e.state.expandedKeys,$=oe(S,m);e.setExpandedKeys($)})}}}),C(P(e),"onListChangeStart",function(){e.setUncontrolledState({listChanging:!0})}),C(P(e),"onListChangeEnd",function(){setTimeout(function(){e.setUncontrolledState({listChanging:!1})})}),C(P(e),"onActiveChange",function(o){var d=e.state.activeKey,i=e.props,u=i.onActiveChange,f=i.itemScrollOffset,v=f===void 0?0:f;d!==o&&(e.setState({activeKey:o}),o!==null&&e.scrollTo({key:o,offset:v}),u==null||u(o))}),C(P(e),"getActiveItem",function(){var o=e.state,d=o.activeKey,i=o.flattenNodes;return d===null?null:i.find(function(u){var f=u.key;return f===d})||null}),C(P(e),"offsetActiveKey",function(o){var d=e.state,i=d.flattenNodes,u=d.activeKey,f=i.findIndex(function(h){var b=h.key;return b===u});f===-1&&o<0&&(f=i.length),f=(f+o+i.length)%i.length;var v=i[f];if(v){var p=v.key;e.onActiveChange(p)}else e.onActiveChange(null)}),C(P(e),"onKeyDown",function(o){var d=e.state,i=d.activeKey,u=d.expandedKeys,f=d.checkedKeys,v=d.fieldNames,p=e.props,h=p.onKeyDown,b=p.checkable,g=p.selectable;switch(o.which){case Ne.UP:{e.offsetActiveKey(-1),o.preventDefault();break}case Ne.DOWN:{e.offsetActiveKey(1),o.preventDefault();break}}var m=e.getActiveItem();if(m&&m.data){var K=e.getTreeNodeRequiredProps(),x=m.data.isLeaf===!1||!!(m.data[v.children]||[]).length,E=j(V(V({},Te(i,K)),{},{data:m.data,active:!0}));switch(o.which){case Ne.LEFT:{x&&u.includes(i)?e.onNodeExpand({},E):m.parent&&e.onActiveChange(m.parent.key),o.preventDefault();break}case Ne.RIGHT:{x&&!u.includes(i)?e.onNodeExpand({},E):m.children&&m.children.length&&e.onActiveChange(m.children[0].key),o.preventDefault();break}case Ne.ENTER:case Ne.SPACE:{b&&!E.disabled&&E.checkable!==!1&&!E.disableCheckbox?e.onNodeCheck({},E,!f.includes(i)):!b&&g&&!E.disabled&&E.selectable!==!1&&e.onNodeSelect({},E);break}}}h==null||h(o)}),C(P(e),"setUncontrolledState",function(o){var d=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1,i=arguments.length>2&&arguments[2]!==void 0?arguments[2]:null;if(!e.destroyed){var u=!1,f=!0,v={};Object.keys(o).forEach(function(p){if(e.props.hasOwnProperty(p)){f=!1;return}u=!0,v[p]=o[p]}),u&&(!d||f)&&e.setState(V(V({},v),i))}}),C(P(e),"scrollTo",function(o){e.listRef.current.scrollTo(o)}),e}return St(r,[{key:"componentDidMount",value:function(){this.destroyed=!1,this.onUpdated()}},{key:"componentDidUpdate",value:function(){this.onUpdated()}},{key:"onUpdated",value:function(){var a=this.props,l=a.activeKey,c=a.itemScrollOffset,o=c===void 0?0:c;l!==void 0&&l!==this.state.activeKey&&(this.setState({activeKey:l}),l!==null&&this.scrollTo({key:l,offset:o}))}},{key:"componentWillUnmount",value:function(){window.removeEventListener("dragend",this.onWindowDragEnd),this.destroyed=!0}},{key:"resetDragState",value:function(){this.setState({dragOverNodeKey:null,dropPosition:null,dropLevelOffset:null,dropTargetKey:null,dropContainerKey:null,dropTargetPos:null,dropAllowed:!1})}},{key:"render",value:function(){var a=this.state,l=a.focused,c=a.flattenNodes,o=a.keyEntities,d=a.draggingNodeKey,i=a.activeKey,u=a.dropLevelOffset,f=a.dropContainerKey,v=a.dropTargetKey,p=a.dropPosition,h=a.dragOverNodeKey,b=a.indent,g=this.props,m=g.prefixCls,K=g.className,x=g.style,E=g.showLine,S=g.focusable,$=g.tabIndex,w=$===void 0?0:$,k=g.selectable,s=g.showIcon,O=g.icon,R=g.switcherIcon,L=g.draggable,D=g.checkable,I=g.checkStrictly,T=g.disabled,A=g.motion,H=g.loadData,Y=g.filterTreeNode,J=g.height,z=g.itemHeight,ae=g.scrollWidth,te=g.virtual,X=g.titleRender,ne=g.dropIndicatorRender,W=g.onContextMenu,F=g.onScroll,ve=g.direction,ie=g.rootClassName,ke=g.rootStyle,Ce=Ze(this.props,{aria:!0,data:!0}),le;L&&(Je(L)==="object"?le=L:typeof L=="function"?le={nodeDraggable:L}:le={});var he={prefixCls:m,selectable:k,showIcon:s,icon:O,switcherIcon:R,draggable:le,draggingNodeKey:d,checkable:D,checkStrictly:I,disabled:T,keyEntities:o,dropLevelOffset:u,dropContainerKey:f,dropTargetKey:v,dropPosition:p,dragOverNodeKey:h,indent:b,direction:ve,dropIndicatorRender:ne,loadData:H,filterTreeNode:Y,titleRender:X,onNodeClick:this.onNodeClick,onNodeDoubleClick:this.onNodeDoubleClick,onNodeExpand:this.onNodeExpand,onNodeSelect:this.onNodeSelect,onNodeCheck:this.onNodeCheck,onNodeLoad:this.onNodeLoad,onNodeMouseEnter:this.onNodeMouseEnter,onNodeMouseLeave:this.onNodeMouseLeave,onNodeContextMenu:this.onNodeContextMenu,onNodeDragStart:this.onNodeDragStart,onNodeDragEnter:this.onNodeDragEnter,onNodeDragOver:this.onNodeDragOver,onNodeDragLeave:this.onNodeDragLeave,onNodeDragEnd:this.onNodeDragEnd,onNodeDrop:this.onNodeDrop};return y.createElement(et.Provider,{value:he},y.createElement("div",{className:q(m,K,ie,C(C(C({},"".concat(m,"-show-line"),E),"".concat(m,"-focused"),l),"".concat(m,"-active-focused"),i!==null)),style:ke},y.createElement($r,G({ref:this.listRef,prefixCls:m,style:x,data:c,disabled:T,selectable:k,checkable:!!D,motion:A,dragging:d!==null,height:J,itemHeight:z,virtual:te,focusable:S,focused:l,tabIndex:w,activeItem:this.getActiveItem(),onFocus:this.onFocus,onBlur:this.onBlur,onKeyDown:this.onKeyDown,onActiveChange:this.onActiveChange,onListChangeStart:this.onListChangeStart,onListChangeEnd:this.onListChangeEnd,onContextMenu:W,onScroll:F,scrollWidth:ae},this.getTreeNodeRequiredProps(),Ce))))}}],[{key:"getDerivedStateFromProps",value:function(a,l){var c=l.prevProps,o={prevProps:a};function d(w){return!c&&a.hasOwnProperty(w)||c&&c[w]!==a[w]}var i,u=l.fieldNames;if(d("fieldNames")&&(u=$e(a.fieldNames),o.fieldNames=u),d("treeData")?i=a.treeData:d("children")&&(fe(!1,"`children` of Tree is deprecated. Please use `treeData` instead."),i=Tt(a.children)),i){o.treeData=i;var f=Mt(i,{fieldNames:u});o.keyEntities=V(C({},xe,Ht),f.keyEntities)}var v=o.keyEntities||l.keyEntities;if(d("expandedKeys")||c&&d("autoExpandParent"))o.expandedKeys=a.autoExpandParent||!c&&a.defaultExpandParent?Xe(a.expandedKeys,v):a.expandedKeys;else if(!c&&a.defaultExpandAll){var p=V({},v);delete p[xe];var h=[];Object.keys(p).forEach(function(w){var k=p[w];k.children&&k.children.length&&h.push(k.key)}),o.expandedKeys=h}else!c&&a.defaultExpandedKeys&&(o.expandedKeys=a.autoExpandParent||a.defaultExpandParent?Xe(a.defaultExpandedKeys,v):a.defaultExpandedKeys);if(o.expandedKeys||delete o.expandedKeys,i||o.expandedKeys){var b=qe(i||l.treeData,o.expandedKeys||l.expandedKeys,u);o.flattenNodes=b}if(a.selectable&&(d("selectedKeys")?o.selectedKeys=ft(a.selectedKeys,a):!c&&a.defaultSelectedKeys&&(o.selectedKeys=ft(a.defaultSelectedKeys,a))),a.checkable){var g;if(d("checkedKeys")?g=Ve(a.checkedKeys)||{}:!c&&a.defaultCheckedKeys?g=Ve(a.defaultCheckedKeys)||{}:i&&(g=Ve(a.checkedKeys)||{checkedKeys:l.checkedKeys,halfCheckedKeys:l.halfCheckedKeys}),g){var m=g,K=m.checkedKeys,x=K===void 0?[]:K,E=m.halfCheckedKeys,S=E===void 0?[]:E;if(!a.checkStrictly){var $=We(x,!0,v);x=$.checkedKeys,S=$.halfCheckedKeys}o.checkedKeys=x,o.halfCheckedKeys=S}}return d("loadedKeys")&&(o.loadedKeys=a.loadedKeys),o}}]),r}(y.Component);C(nt,"defaultProps",{prefixCls:"rc-tree",showLine:!1,showIcon:!0,selectable:!0,multiple:!1,checkable:!1,disabled:!1,checkStrictly:!1,draggable:!1,defaultExpandParent:!0,autoExpandParent:!1,defaultExpandAll:!1,defaultExpandedKeys:[],defaultCheckedKeys:[],defaultSelectedKeys:[],dropIndicatorRender:mr,allowDrop:function(){return!0},expandAction:!1});C(nt,"TreeNode",Me);const wr=({treeCls:n,treeNodeCls:t,directoryNodeSelectedBg:r,directoryNodeSelectedColor:e,motionDurationMid:a,borderRadius:l,controlItemBgHover:c})=>({[`${n}${n}-directory ${t}`]:{[`${n}-node-content-wrapper`]:{position:"static",[`&:has(${n}-drop-indicator)`]:{position:"relative"},[`> *:not(${n}-drop-indicator)`]:{position:"relative"},"&:hover":{background:"transparent"},"&:before":{position:"absolute",inset:0,transition:`background-color ${a}`,content:'""',borderRadius:l},"&:hover:before":{background:c}},[`${n}-switcher, ${n}-checkbox, ${n}-draggable-icon`]:{zIndex:1},"&-selected":{background:r,borderRadius:l,[`${n}-switcher, ${n}-draggable-icon`]:{color:e},[`${n}-node-content-wrapper`]:{color:e,background:"transparent","&:before, &:hover:before":{background:r}}}}}),Or=new tn("ant-tree-node-fx-do-not-use",{"0%":{opacity:0},"100%":{opacity:1}}),Dr=(n,t)=>({[`.${n}-switcher-icon`]:{display:"inline-block",fontSize:10,verticalAlign:"baseline",svg:{transition:`transform ${t.motionDurationSlow}`}}}),Pr=(n,t)=>({[`.${n}-drop-indicator`]:{position:"absolute",zIndex:1,height:2,backgroundColor:t.colorPrimary,borderRadius:1,pointerEvents:"none","&:after":{position:"absolute",top:-3,insetInlineStart:-6,width:8,height:8,backgroundColor:"transparent",border:`${be(t.lineWidthBold)} solid ${t.colorPrimary}`,borderRadius:"50%",content:'""'}}}),Ir=(n,t)=>{const{treeCls:r,treeNodeCls:e,treeNodePadding:a,titleHeight:l,indentSize:c,nodeSelectedBg:o,nodeHoverBg:d,colorTextQuaternary:i,controlItemBgActiveDisabled:u}=t;return{[r]:Object.assign(Object.assign({},Ie(t)),{"--rc-virtual-list-scrollbar-bg":t.colorSplit,background:t.colorBgContainer,borderRadius:t.borderRadius,transition:`background-color ${t.motionDurationSlow}`,"&-rtl":{direction:"rtl"},[`&${r}-rtl ${r}-switcher_close ${r}-switcher-icon svg`]:{transform:"rotate(90deg)"},[`&-focused:not(:hover):not(${r}-active-focused)`]:wt(t),[`${r}-list-holder-inner`]:{alignItems:"flex-start"},[`&${r}-block-node`]:{[`${r}-list-holder-inner`]:{alignItems:"stretch",[`${r}-node-content-wrapper`]:{flex:"auto"},[`${e}.dragging:after`]:{position:"absolute",inset:0,border:`1px solid ${t.colorPrimary}`,opacity:0,animationName:Or,animationDuration:t.motionDurationSlow,animationPlayState:"running",animationFillMode:"forwards",content:'""',pointerEvents:"none",borderRadius:t.borderRadius}}},[e]:{display:"flex",alignItems:"flex-start",marginBottom:a,lineHeight:be(l),position:"relative","&:before":{content:'""',position:"absolute",zIndex:1,insetInlineStart:0,width:"100%",top:"100%",height:a},[`&-disabled ${r}-node-content-wrapper`]:{color:t.colorTextDisabled,cursor:"not-allowed","&:hover":{background:"transparent"}},[`${r}-checkbox-disabled + ${r}-node-selected,&${e}-disabled${e}-selected ${r}-node-content-wrapper`]:{backgroundColor:u},[`${r}-checkbox-disabled`]:{pointerEvents:"unset"},[`&:not(${e}-disabled)`]:{[`${r}-node-content-wrapper`]:{"&:hover":{color:t.nodeHoverColor}}},[`&-active ${r}-node-content-wrapper`]:{background:t.controlItemBgHover},[`&:not(${e}-disabled).filter-node ${r}-title`]:{color:t.colorPrimary,fontWeight:t.fontWeightStrong},"&-draggable":{cursor:"grab",[`${r}-draggable-icon`]:{flexShrink:0,width:l,textAlign:"center",visibility:"visible",color:i},[`&${e}-disabled ${r}-draggable-icon`]:{visibility:"hidden"}}},[`${r}-indent`]:{alignSelf:"stretch",whiteSpace:"nowrap",userSelect:"none","&-unit":{display:"inline-block",width:c}},[`${r}-draggable-icon`]:{visibility:"hidden"},[`${r}-switcher, ${r}-checkbox`]:{marginInlineEnd:t.calc(t.calc(l).sub(t.controlInteractiveSize)).div(2).equal()},[`${r}-switcher`]:Object.assign(Object.assign({},Dr(n,t)),{position:"relative",flex:"none",alignSelf:"stretch",width:l,textAlign:"center",cursor:"pointer",userSelect:"none",transition:`all ${t.motionDurationSlow}`,"&-noop":{cursor:"unset"},"&:before":{pointerEvents:"none",content:'""',width:l,height:l,position:"absolute",left:{_skip_check_:!0,value:0},top:0,borderRadius:t.borderRadius,transition:`all ${t.motionDurationSlow}`},[`&:not(${r}-switcher-noop):hover:before`]:{backgroundColor:t.colorBgTextHover},[`&_close ${r}-switcher-icon svg`]:{transform:"rotate(-90deg)"},"&-loading-icon":{color:t.colorPrimary},"&-leaf-line":{position:"relative",zIndex:1,display:"inline-block",width:"100%",height:"100%","&:before":{position:"absolute",top:0,insetInlineEnd:t.calc(l).div(2).equal(),bottom:t.calc(a).mul(-1).equal(),marginInlineStart:-1,borderInlineEnd:`1px solid ${t.colorBorder}`,content:'""'},"&:after":{position:"absolute",width:t.calc(t.calc(l).div(2).equal()).mul(.8).equal(),height:t.calc(l).div(2).equal(),borderBottom:`1px solid ${t.colorBorder}`,content:'""'}}}),[`${r}-node-content-wrapper`]:Object.assign(Object.assign({position:"relative",minHeight:l,paddingBlock:0,paddingInline:t.paddingXS,background:"transparent",borderRadius:t.borderRadius,cursor:"pointer",transition:`all ${t.motionDurationMid}, border 0s, line-height 0s, box-shadow 0s`},Pr(n,t)),{"&:hover":{backgroundColor:d},[`&${r}-node-selected`]:{color:t.nodeSelectedColor,backgroundColor:o},[`${r}-iconEle`]:{display:"inline-block",width:l,height:l,textAlign:"center",verticalAlign:"top","&:empty":{display:"none"}}}),[`${r}-unselectable ${r}-node-content-wrapper:hover`]:{backgroundColor:"transparent"},[`${e}.drop-container > [draggable]`]:{boxShadow:`0 0 0 2px ${t.colorPrimary}`},"&-show-line":{[`${r}-indent-unit`]:{position:"relative",height:"100%","&:before":{position:"absolute",top:0,insetInlineEnd:t.calc(l).div(2).equal(),bottom:t.calc(a).mul(-1).equal(),borderInlineEnd:`1px solid ${t.colorBorder}`,content:'""'},"&-end:before":{display:"none"}},[`${r}-switcher`]:{background:"transparent","&-line-icon":{verticalAlign:"-0.15em"}}},[`${e}-leaf-last ${r}-switcher-leaf-line:before`]:{top:"auto !important",bottom:"auto !important",height:`${be(t.calc(l).div(2).equal())} !important`}})}},Tr=(n,t,r=!0)=>{const e=`.${n}`,a=`${e}-treenode`,l=t.calc(t.paddingXS).div(2).equal(),c=kt(t,{treeCls:e,treeNodeCls:a,treeNodePadding:l});return[Ir(n,c),r&&wr(c)].filter(Boolean)},Mr=n=>{const{controlHeightSM:t,controlItemBgHover:r,controlItemBgActive:e}=n,a=t;return{titleHeight:a,indentSize:a,nodeHoverBg:r,nodeHoverColor:n.colorText,nodeSelectedBg:e,nodeSelectedColor:n.colorText}},Rr=n=>{const{colorTextLightSolid:t,colorPrimary:r}=n;return Object.assign(Object.assign({},Mr(n)),{directoryNodeSelectedColor:t,directoryNodeSelectedBg:r})},Lr=Qe("Tree",(n,{prefixCls:t})=>[{[n.componentCls]:Lt(`${t}-checkbox`,n)},Tr(t,n),en(n)],Rr),mt=4;function Ar(n){const{dropPosition:t,dropLevelOffset:r,prefixCls:e,indent:a,direction:l="ltr"}=n,c=l==="ltr"?"left":"right",o=l==="ltr"?"right":"left",d={[c]:-r*a+mt,[o]:0};switch(t){case-1:d.top=-3;break;case 1:d.bottom=-3;break;default:d.bottom=-3,d[c]=a+mt;break}return M.createElement("div",{style:d,className:`${e}-drop-indicator`})}const _r=n=>{var t,r;const{prefixCls:e,switcherIcon:a,treeNodeProps:l,showLine:c,switcherLoadingIcon:o}=n,{isLeaf:d,expanded:i,loading:u}=l;if(u)return y.isValidElement(o)?o:y.createElement(nn,{className:`${e}-switcher-loading-icon`});let f;if(c&&typeof c=="object"&&(f=c.showLeafIcon),d){if(!c)return null;if(typeof f!="boolean"&&f){const h=typeof f=="function"?f(l):f,b=`${e}-switcher-line-custom-icon`;return y.isValidElement(h)?lt(h,{className:q((t=h.props)===null||t===void 0?void 0:t.className,b)}):h}return f?y.createElement(At,{className:`${e}-switcher-line-icon`}):y.createElement("span",{className:`${e}-switcher-leaf-line`})}const v=`${e}-switcher-icon`,p=typeof a=="function"?a(l):a;return y.isValidElement(p)?lt(p,{className:q((r=p.props)===null||r===void 0?void 0:r.className,v)}):p!==void 0?p:c?i?y.createElement(or,{className:`${e}-switcher-line-icon`}):y.createElement(sr,{className:`${e}-switcher-line-icon`}):y.createElement(Dn,{className:v})},Hr=_r,Br=M.forwardRef((n,t)=>{var r;const{getPrefixCls:e,direction:a,virtual:l,tree:c}=M.useContext(Ot),{prefixCls:o,className:d,showIcon:i=!1,showLine:u,switcherIcon:f,switcherLoadingIcon:v,blockNode:p=!1,children:h,checkable:b=!1,selectable:g=!0,draggable:m,motion:K,style:x}=n,E=e("tree",o),S=e(),$=K??Object.assign(Object.assign({},rn(S)),{motionAppear:!1}),w=Object.assign(Object.assign({},n),{checkable:b,selectable:g,showIcon:i,motion:$,blockNode:p,showLine:!!u,dropIndicatorRender:Ar}),[k,s,O]=Lr(E),[,R]=on(),L=R.paddingXS/2+(((r=R.Tree)===null||r===void 0?void 0:r.titleHeight)||R.controlHeightSM),D=M.useMemo(()=>{if(!m)return!1;let T={};switch(typeof m){case"function":T.nodeDraggable=m;break;case"object":T=Object.assign({},m);break}return T.icon!==!1&&(T.icon=T.icon||M.createElement(Xn,null)),T},[m]),I=T=>M.createElement(Hr,{prefixCls:E,switcherIcon:f,switcherLoadingIcon:v,treeNodeProps:T,showLine:u});return k(M.createElement(nt,Object.assign({itemHeight:L,ref:t,virtual:l},w,{style:Object.assign(Object.assign({},c==null?void 0:c.style),x),prefixCls:E,className:q({[`${E}-icon-hide`]:!i,[`${E}-block-node`]:p,[`${E}-unselectable`]:!g,[`${E}-rtl`]:a==="rtl"},c==null?void 0:c.className,d,s,O),direction:a,checkable:b&&M.createElement("span",{className:`${E}-checkbox-inner`}),selectable:g,switcherIcon:I,draggable:D}),h))}),Bt=Br,bt=0,Ge=1,xt=2;function rt(n,t,r){const{key:e,children:a}=r;function l(c){const o=c[e],d=c[a];t(o,c)!==!1&&rt(d||[],t,r)}n.forEach(l)}function jr({treeData:n,expandedKeys:t,startKey:r,endKey:e,fieldNames:a}){const l=[];let c=bt;if(r&&r===e)return[r];if(!r||!e)return[];function o(d){return d===r||d===e}return rt(n,d=>{if(c===xt)return!1;if(o(d)){if(l.push(d),c===bt)c=Ge;else if(c===Ge)return c=xt,!1}else c===Ge&&l.push(d);return t.includes(d)},$e(a)),l}function Ue(n,t,r){const e=ee(t),a=[];return rt(n,(l,c)=>{const o=e.indexOf(l);return o!==-1&&(a.push(c),e.splice(o,1)),!!e.length},$e(r)),a}var Ct=globalThis&&globalThis.__rest||function(n,t){var r={};for(var e in n)Object.prototype.hasOwnProperty.call(n,e)&&t.indexOf(e)<0&&(r[e]=n[e]);if(n!=null&&typeof Object.getOwnPropertySymbols=="function")for(var a=0,e=Object.getOwnPropertySymbols(n);a<e.length;a++)t.indexOf(e[a])<0&&Object.prototype.propertyIsEnumerable.call(n,e[a])&&(r[e[a]]=n[e[a]]);return r};function zr(n){const{isLeaf:t,expanded:r}=n;return t?y.createElement(At,null):r?y.createElement(Hn,null):y.createElement(qn,null)}function Kt({treeData:n,children:t}){return n||Tt(t)}const Fr=(n,t)=>{var{defaultExpandAll:r,defaultExpandParent:e,defaultExpandedKeys:a}=n,l=Ct(n,["defaultExpandAll","defaultExpandParent","defaultExpandedKeys"]);const c=y.useRef(null),o=y.useRef(null),d=()=>{const{keyEntities:k}=Mt(Kt(l));let s;return r?s=Object.keys(k):e?s=Xe(l.expandedKeys||a||[],k):s=l.expandedKeys||a||[],s},[i,u]=y.useState(l.selectedKeys||l.defaultSelectedKeys||[]),[f,v]=y.useState(()=>d());y.useEffect(()=>{"selectedKeys"in l&&u(l.selectedKeys)},[l.selectedKeys]),y.useEffect(()=>{"expandedKeys"in l&&v(l.expandedKeys)},[l.expandedKeys]);const p=(k,s)=>{var O;return"expandedKeys"in l||v(k),(O=l.onExpand)===null||O===void 0?void 0:O.call(l,k,s)},h=(k,s)=>{var O;const{multiple:R,fieldNames:L}=l,{node:D,nativeEvent:I}=s,{key:T=""}=D,A=Kt(l),H=Object.assign(Object.assign({},s),{selected:!0}),Y=(I==null?void 0:I.ctrlKey)||(I==null?void 0:I.metaKey),J=I==null?void 0:I.shiftKey;let z;R&&Y?(z=k,c.current=T,o.current=z,H.selectedNodes=Ue(A,z,L)):R&&J?(z=Array.from(new Set([].concat(ee(o.current||[]),ee(jr({treeData:A,expandedKeys:f,startKey:T,endKey:c.current,fieldNames:L}))))),H.selectedNodes=Ue(A,z,L)):(z=[T],c.current=T,o.current=z,H.selectedNodes=Ue(A,z,L)),(O=l.onSelect)===null||O===void 0||O.call(l,z,H),"selectedKeys"in l||u(z)},{getPrefixCls:b,direction:g}=y.useContext(Ot),{prefixCls:m,className:K,showIcon:x=!0,expandAction:E="click"}=l,S=Ct(l,["prefixCls","className","showIcon","expandAction"]),$=b("tree",m),w=q(`${$}-directory`,{[`${$}-directory-rtl`]:g==="rtl"},K);return y.createElement(Bt,Object.assign({icon:zr,ref:t,blockNode:!0},S,{showIcon:x,expandAction:E,prefixCls:$,className:w,expandedKeys:f,selectedKeys:i,onSelect:h,onExpand:p}))},qr=y.forwardRef(Fr),Wr=qr,ot=Bt;ot.DirectoryTree=Wr;ot.TreeNode=Me;const Qr=ot;export{Xr as A,Hn as F,Jr as I,Qr as T,qn as a,We as b,Mt as c,oe as d,ce as e,Yr as u};
