import{n as B,s as sS,aP as SS,r as x,wP as W,fw as I,xA as o,C as g,bJ as F,au as L,aT as w,aJ as Q,wQ as iS,wR as Ws,wS as ES,aA as Fs,aB as Qs,wY as js,fY as TS,aK as C,wZ as H,w_ as hS,w$ as tS}from"./index-f7d9b065.js";const AS=()=>{const i=B(S=>S.paginationData),j=B(S=>S.payload.changeFieldRows),z=B(S=>S.payload.whseList),$=B(S=>S.payload.matNoList),b=B(S=>S.payload.plantList),K=B(S=>S.payload.changeFieldRowsDisplay),q=B(S=>S.payload.selectedRows),A=sS(),{customError:U}=SS(),[Ks,fS]=x.useState({errorText:!1,errorTextMessage:""}),qs=async(S,s)=>{var T,t,y,_,f,D,O,P,c,Y,G,J,k,r,X,Z,a,n,u,d,V,v,e,ss,Ss,is,Es,Ts,hs,ts,fs,Ns,ys,os,As,_s,Ds,Rs,Is,Ps,Cs,Ms,Os,ps,Ls,ws,Us,cs;A(W(!0));let h,N;return S===((T=I)==null?void 0:T.LOGISTIC)?(N={materialNo:(s==null?void 0:s[(t=o)==null?void 0:t.MATERIAL_NUM])||"",division:(s==null?void 0:s[(y=o)==null?void 0:y.DIVISION])||"",plant:(s==null?void 0:s[(_=o)==null?void 0:_.PLANT])||"",page:i==null?void 0:i.page,size:i==null?void 0:i.size},h=`/${L}/${(D=(f=w)==null?void 0:f.CHG_DISPLAY_REQUESTOR)==null?void 0:D.LOGISTIC}`):S===((O=I)==null?void 0:O.ITEM_CAT)?(N={materialNo:(s==null?void 0:s[(P=o)==null?void 0:P.MATERIAL_NUM])||"",salesOrg:(s==null?void 0:s[(c=o)==null?void 0:c.SALES_ORG])||"",distrChan:(s==null?void 0:s[(Y=o)==null?void 0:Y.DIST_CHNL])||"",division:(s==null?void 0:s[(G=o)==null?void 0:G.DIVISION])||"",page:i==null?void 0:i.page,size:i==null?void 0:i.size},h=`/${L}/${(k=(J=w)==null?void 0:J.CHG_DISPLAY_REQUESTOR)==null?void 0:k.SALES}`):S===((r=I)==null?void 0:r.MRP)?(N={materialNo:(s==null?void 0:s[(X=o)==null?void 0:X.MATERIAL_NUM])||"",mrpCtrler:(s==null?void 0:s[(Z=o)==null?void 0:Z.MRP_CTRLER])||"",plant:(s==null?void 0:s[(a=o)==null?void 0:a.PLANT])||"",division:(s==null?void 0:s[(n=o)==null?void 0:n.DIVISION])||"",page:i==null?void 0:i.page,size:i==null?void 0:i.size},h=`/${L}/${(d=(u=w)==null?void 0:u.CHG_DISPLAY_REQUESTOR)==null?void 0:d.MRP}`):S===((V=I)==null?void 0:V.UPD_DESC)?(N={materialNo:(s==null?void 0:s[(v=o)==null?void 0:v.MATERIAL_NUM])||"",division:(s==null?void 0:s[(e=o)==null?void 0:e.DIVISION])||"",plant:(s==null?void 0:s[(ss=o)==null?void 0:ss.PLANT])||"",page:i==null?void 0:i.page,size:i==null?void 0:i.size},h=`/${L}/${(is=(Ss=w)==null?void 0:Ss.CHG_DISPLAY_REQUESTOR)==null?void 0:is.DESC}`):S===((Es=I)==null?void 0:Es.WARE_VIEW_2)?(N={materialNo:(s==null?void 0:s[(Ts=o)==null?void 0:Ts.MATERIAL_NUM])||"",whseNo:(s==null?void 0:s[(hs=o)==null?void 0:hs.WAREHOUSE])||"",plant:(s==null?void 0:s[(ts=o)==null?void 0:ts.PLANT])||"",division:(s==null?void 0:s[(fs=o)==null?void 0:fs.DIVISION])||"",page:i==null?void 0:i.page,size:i==null?void 0:i.size},h=`/${L}/${(ys=(Ns=w)==null?void 0:Ns.CHG_DISPLAY_REQUESTOR)==null?void 0:ys.WAREHOUSE}`):S===((os=I)==null?void 0:os.CHG_STAT)?(N={materialNo:(s==null?void 0:s[(As=o)==null?void 0:As.MATERIAL_NUM])||"",salesOrg:(s==null?void 0:s[(_s=o)==null?void 0:_s.SALES_ORG])||"",distrChan:(s==null?void 0:s[(Ds=o)==null?void 0:Ds.DIST_CHNL])||"",division:(s==null?void 0:s[(Rs=o)==null?void 0:Rs.DIVISION])||"",page:i==null?void 0:i.page,size:i==null?void 0:i.size},h=`/${L}/${(Ps=(Is=w)==null?void 0:Is.CHG_DISPLAY_REQUESTOR)==null?void 0:Ps.CHG_STATUS}`):S===((Cs=I)==null?void 0:Cs.SET_DNU)&&(N={materialNo:(s==null?void 0:s[(Ms=o)==null?void 0:Ms.MATERIAL_NUM])||"",salesOrg:(s==null?void 0:s[(Os=o)==null?void 0:Os.SALES_ORG])||"",distrChan:(s==null?void 0:s[(ps=o)==null?void 0:ps.DIST_CHNL])||"",division:(s==null?void 0:s[(Ls=o)==null?void 0:Ls.DIVISION])||"",plant:(s==null?void 0:s[(ws=o)==null?void 0:ws.PLANT])||"",page:i==null?void 0:i.page,size:i==null?void 0:i.size},h=`/${L}/${(cs=(Us=w)==null?void 0:Us.CHG_DISPLAY_REQUESTOR)==null?void 0:cs.SET_DNU}`),new Promise((vs,es)=>{g(h,"post",E=>{var Gs,$s,bs,Bs,Hs,zs,ms,Ys,gs;A(iS(E==null?void 0:E.totalElements)),(E==null?void 0:E.totalPages)===1||(E==null?void 0:E.currentPage)+1===(E==null?void 0:E.totalPages)?(A(Ws(E==null?void 0:E.totalElements)),A(ES(!0))):A(Ws(((E==null?void 0:E.currentPage)+1)*(E==null?void 0:E.pageSize)));const R=S===((Gs=I)==null?void 0:Gs.LOGISTIC)?Js(E==null?void 0:E.body):S===(($s=I)==null?void 0:$s.ITEM_CAT)?ks(E==null?void 0:E.body):S===((bs=I)==null?void 0:bs.MRP)?rs(E==null?void 0:E.body):S===((Bs=I)==null?void 0:Bs.UPD_DESC)?ns(E==null?void 0:E.body):S===((Hs=I)==null?void 0:Hs.WARE_VIEW_2)?as(E==null?void 0:E.body):S===((zs=I)==null?void 0:zs.CHG_STAT)?Xs(E==null?void 0:E.body):S===((ms=I)==null?void 0:ms.SET_DNU)?Zs(E==null?void 0:E.body):[];if(Array.isArray(R))A(Fs([...j,...R])),A(Qs({...K,[i==null?void 0:i.page]:R}));else if(typeof R=="object"&&R!==null){const M={...j};(Ys=Object==null?void 0:Object.keys(R))==null||Ys.forEach(p=>{M[p]=[...M[p]||[],...R[p]]}),A(Fs(M)),A(Qs({...K,[i==null?void 0:i.page]:R}))}A(W(!1));let m;if(Array.isArray(R))m=R==null?void 0:R.map(M=>M==null?void 0:M.id),A(js([...q,...m]));else if(typeof R=="object"&&R!==null){m=Object.keys(R).reduce((p,ls)=>{var xs;return p[ls]=((xs=R[ls])==null?void 0:xs.map(l=>l==null?void 0:l.id))||[],p},{});const M={...q};(gs=Object==null?void 0:Object.keys(m))==null||gs.forEach(p=>{M[p]=[...M[p]||[],...m[p]]}),A(js(M))}vs(E==null?void 0:E.body)},()=>{var E;A(W(!1)),es(new Error((E=TS)==null?void 0:E.ERROR_MSG))},N)})},Js=S=>{const s=[];let h=1;const N=new Set;return S.forEach(T=>{T.ToLogisticdata.forEach(t=>{N.add(t.Material);const y={...t,id:C(),slNo:h++,MatlType:(T==null?void 0:T.MatlType)||""};s.push(y)})}),A(H([...$,...N])),s},ks=S=>{const s=[];let h=1;const N=new Set;return S.forEach(T=>{T.Tosalesdata.forEach(t=>{N.add(t.Material);const y={...t,id:C(),slNo:h++,MatlType:(T==null?void 0:T.MatlType)||""};s.push(y)})}),A(H([...$,...N])),s},rs=S=>{const s={"Basic Data":[],"Plant Data":[]};let h=1,N=1;const T=new Set,t=new Set;return S.forEach(y=>{const{Tomrpupdate:_,ToBasicdata:f,Material:D,MatlType:O}=y;T.add(D),s["Basic Data"].push({...f,id:C(),slNo:h++,type:"Basic Data",MatlType:O}),_.forEach(P=>{t.add(P==null?void 0:P.Plant),s["Plant Data"].push({...P,id:C(),slNo:N++,type:"Plant Data"})})}),A(H([...$,...T])),A(hS([...b,...t])),s},Xs=S=>{const s={"Basic Data":[],"Plant Data":[],"Sales Data":[]};let h=1,N=1,T=1;const t=new Set;return S.forEach(y=>{const{Tosalesdata:_,ToBasicdata:f,Toplantdata:D,Material:O,MatlType:P}=y;t.add(O),s["Basic Data"].push({...f,id:C(),slNo:h++,type:"Basic Data",MatlType:P}),D==null||D.forEach(c=>{s["Plant Data"].push({...c,id:C(),slNo:N++,type:"Plant Data"})}),_==null||_.forEach(c=>{s["Sales Data"].push({...c,id:C(),slNo:T++,type:"Sales Data"})})}),A(H([...$,...t])),s},Zs=S=>{const s={"Basic Data":[],"Plant Data":[],"Sales Data":[],Description:[]};let h=1,N=1,T=1,t=1;const y=new Set;return S.forEach(_=>{const{Tosalesdata:f,ToBasicdata:D,Toplantdata:O,Tomaterialdescription:P,Material:c,MatlType:Y}=_;y.add(c),s["Basic Data"].push({...D,id:C(),slNo:h++,type:"Basic Data",MatlType:Y}),O==null||O.forEach(G=>{s["Plant Data"].push({...G,id:C(),slNo:N++,type:"Plant Data"})}),f==null||f.forEach(G=>{s["Sales Data"].push({...G,id:C(),slNo:T++,type:"Sales Data"})}),P==null||P.forEach(G=>{s.Description.push({...G,id:C(),slNo:t++,type:"Description"})})}),A(H([...$,...y])),s},as=S=>{const s=[],h=new Set;let N=1;const T=new Set;S.forEach(y=>{y.ToWarehousedata.forEach(_=>{h.add(_.WhseNo),T.add(_.Material);const f={..._,id:C(),slNo:N++,MatlType:(y==null?void 0:y.MatlType)||""};s.push(f)})});const t=[...h];return A(tS(t)),A(H([...$,...T])),s},ns=S=>{const s=[];let h=1;const N=new Set;return S.forEach(T=>{T.Tomaterialdescription.forEach(t=>{N.add(t.Material);const y={...t,id:C(),slNo:h++,MatlType:(T==null?void 0:T.MatlType)||""};s.push(y)})}),A(H([...$,...N])),s};x.useEffect(()=>{(async()=>{if((z==null?void 0:z.length)>0){const s=await us(z);A(F({keyName:"Unittype1",data:s}))}})()},[z]);const us=async S=>{const s={};for(const h of S){let N={whseNo:h};try{const T=await new Promise(t=>{var y,_;g(`/${L}${(_=(y=w)==null?void 0:y.DEPENDENT_LOOKUPS)==null?void 0:_.UNITTYPE}`,"post",f=>{var D;f.statusCode===((D=Q)==null?void 0:D.STATUS_200)?t(f==null?void 0:f.body):(U("Failed to fetch data"),t([]))},f=>{U(f),t([])},N)});s[h]=T}catch(T){U(T),s[h]=[]}}return s};x.useEffect(()=>{(async()=>{if((b==null?void 0:b.length)>0){const s=await ds(b);A(F({keyName:"Spproctype",data:s}));const h=await Vs(b);A(F({keyName:"MrpCtrler",data:h}))}})()},[b]);const ds=async S=>{const s={};for(const h of S){let N={plant:h};try{const T=await new Promise(t=>{var y,_;g(`/${L}${(_=(y=w)==null?void 0:y.DATA)==null?void 0:_.GET_SPPROC_TYPE}`,"post",f=>{var D;f.statusCode===((D=Q)==null?void 0:D.STATUS_200)?t(f==null?void 0:f.body):(U("Failed to fetch data"),t([]))},f=>{U(f),t([])},N)});s[h]=T}catch(T){U(T),s[h]=[]}}return s},Vs=async S=>{const s={};for(const h of S){let N={plant:h};try{const T=await new Promise(t=>{var y,_;g(`/${L}${(_=(y=w)==null?void 0:y.DATA)==null?void 0:_.GET_MRP_CONTROLLER_BASED_ON_PLANT}`,"post",f=>{var D;f.statusCode===((D=Q)==null?void 0:D.STATUS_200)?t(f==null?void 0:f.body):(U("Failed to fetch data"),t([]))},f=>{U(f),t([])},N)});s[h]=T}catch(T){U(T),s[h]=[]}}return s};return{fetchDisplayDataRequestor:qs,errorState:Ks}};export{AS as u};
