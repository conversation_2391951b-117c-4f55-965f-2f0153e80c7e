import{qm as n,cb as e,qo as a,qC as t,q6 as m,qD as s,qE as c}from"./index-f7d9b065.js";import{i as d}from"./Dropdown-7dddbb05.js";const l=n({components:{MuiAccordion:{styleOverrides:{root:{"&.Mui-disabled":{backgroundColor:"var(--background-disabled)"}}}}}});function b(r){return e.jsx(a,{theme:l,children:e.jsx(t,{...r})})}const u={small:{minHeight:"2rem "},medium:{minHeight:"2.5rem "},large:{minHeight:"3.25rem"},xlarge:{minHeight:"3.5rem"}},g=m(s)(({size:r="medium",variant:o,disabled:i})=>({display:"flex",...o=="leftIcon"&&{flexDirection:"row-reverse"},gap:"0.5rem","& .MuiAccordionSummary-content":{margin:"0.5rem 0rem",...r=="small"&&{margin:"0.25rem 0rem"}},padding:"0rem 1rem",fontFamily:"inherit",fontSize:"1rem",fontWeight:"500",backgroundColor:"var(--background-default)","& .MuiPaper-root":{"& .MuiAccordion-root":{...i&&{backgroundColor:"var(--background-disabled)"}}},...u[r]})),p=({variant:r="leftIcon",...o})=>e.jsx(g,{variant:r,...o,expandIcon:r==="noIcon"?null:e.jsx(d,{})});function h(r){return e.jsx(c,{...r})}export{h as c,p as g,b as t};
