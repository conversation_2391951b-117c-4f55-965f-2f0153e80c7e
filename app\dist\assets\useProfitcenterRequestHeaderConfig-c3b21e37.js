import{n as E,s as u,C as M,bI as T,aT as i,dG as A,xi as d}from"./index-f7d9b065.js";const C=()=>{const a=E(s=>s.profitCenter.payload.requestHeaderData),c=E(s=>s.applicationConfig),o=u();return{getRequestHeaderTemplatePc:()=>{let s={decisionTableId:null,decisionTableName:"MDG_FMD_REQUEST_HEADER_CONFIG",version:"v2",rulePolicy:null,validityDate:null,conditions:[{"MDG_CONDITIONS.MDG_MAT_SCENARIO":(a==null?void 0:a.RequestType)||"Create","MDG_CONDITIONS.MDG_MAT_MODULE_NAME":"Profit Center"}],systemFilters:null,systemOrders:null,filterString:null};const _=t=>{var r,l;if(t.statusCode===200){const D={"Header Data":((l=(r=t==null?void 0:t.data)==null?void 0:r.result[0])==null?void 0:l.MDG_MAT_REQUEST_HEADER_CONFIG).sort((e,N)=>e.MDG_MAT_SEQUENCE_NO-N.MDG_MAT_SEQUENCE_NO).map(e=>({fieldName:e.MDG_MAT_UI_FIELD_NAME,sequenceNo:e.MDG_MAT_SEQUENCE_NO,fieldType:e.MDG_MAT_FIELD_TYPE,maxLength:e.MDG_MAT_MAX_LENGTH,value:e.MDG_MAT_DEFAULT_VALUE,visibility:e.MDG_MAT_VISIBILITY,jsonName:e.MDG_MAT_JSON_FIELD_NAME}))};o(A({tab:"Request Header",data:D})),o(d(D))}},n=t=>{console.log(t)};c.environment==="localhost"?M(`/${T}${i.INVOKE_RULES.LOCAL}`,"post",_,n,s):M(`/${T}${i.INVOKE_RULES.PROD}`,"post",_,n,s)}}};export{C as u};
