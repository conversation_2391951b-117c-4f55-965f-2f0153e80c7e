import{cs as G,A as Ge,k as Se,O as s,an as ee,d as b,r as l,a as Te,b as Oe,t as Ne,n as S,s as ve,g as De,E as Le,cc as Me,ct as Re,bA as _e,cu as ke,c as i,j as e,N as Ie,aZ as He,Q as Pe,U as We,V as $e,W as Be,i as ze,Y as D,Z as L,$ as M,a7 as we,ab as V,ac as je,ad as Ue,F as Fe,b1 as qe,ag as J,ah as Ke,bT as Ye,C as g,cd as T,x as R,bH as Ze,bI as Q,w as Ve,aE as Je,aG as Qe,bJ as x}from"./index-f7d9b065.js";import{S as _}from"./SingleSelectDropdown-aee403d4.js";import{R as Xe}from"./ReusableHIerarchyTree-7527be6c.js";import{R as et}from"./ReusablePresetFilter-36c3fb2e.js";import"./context-4d14404f.js";import"./EyeOutlined-0bb7ab85.js";import"./EditOutlined-9d614b39.js";import"./asyncToGenerator-88583e02.js";import"./ArrowLeftOutlined-a31a51d5.js";import"./index-190fd75d.js";const tt=G(Ge)(({theme:m})=>({minHeight:"2rem !important",margin:"0px !important",backgroundColor:m.palette.primary.light,borderRadius:"8px 8px 0 0",transition:"all 0.2s ease-in-out","&:hover":{backgroundColor:`${m.palette.primary.light}20`}})),ot=G(Se)(({theme:m})=>({marginTop:"0px !important",border:`1px solid ${m.palette.primary.main}`,borderRadius:"8px",boxShadow:"0 2px 4px rgba(0,0,0,0.05)","&:not(:last-child)":{borderBottom:0},"&:before":{display:"none"}})),rt=G(s)({display:"flex",justifyContent:"flex-end",paddingRight:"0.75rem",paddingBottom:"0.75rem",paddingTop:"0rem",gap:"0.5rem"}),X=G(ee)({borderRadius:"4px",padding:"4px 12px",textTransform:"none",fontSize:"0.875rem"}),k=G(b)(({theme:m})=>({fontSize:"0.75rem",color:m.palette.primary.dark,marginBottom:"0.25rem",fontWeight:500})),Tt=()=>{var w,j,U,F,q,K,Y,Z;let m=l.useRef(null);const{t:d}=Te(),O=Oe(),[te,at]=l.useState(!1),[oe,nt]=l.useState(""),[st,re]=l.useState(!1),[lt,I]=l.useState(!1),[it,ae]=l.useState({code:"ETCA",desc:"ET FAMILY CO AREA"}),[ct,ne]=l.useState(null),[dt,se]=l.useState(null),le=Ne.useRef(null),[ie,H]=l.useState(!1),[ce,ut]=l.useState(""),[de,pt]=l.useState(),[ue,mt]=l.useState(""),[C,P]=l.useState([{id:"1",title:"",child:[],tags:[],description:""}]);let N=S(t=>{var r;return(r=t.userManagement.entitiesAndActivities)==null?void 0:r.Material});const[ht,pe]=l.useState(C.length===0||!((w=C[0])!=null&&w.title)),me=S(t=>t.applicationConfig),W=()=>{var a,E,y,f,A,p;pe(!1),I(!0);var t={coa:((a=o==null?void 0:o.chartOfAccount)==null?void 0:a.code)===""?"ETCN":(E=o==null?void 0:o.chartOfAccount)==null?void 0:E.code,node:((y=o==null?void 0:o.costElementGroup)==null?void 0:y.code)===""?"AL-ALL":(f=o==null?void 0:o.costElementGroup)==null?void 0:f.code,controllingArea:((A=o==null?void 0:o.controllingArea)==null?void 0:A.code)===""?"ETCA":(p=o==null?void 0:o.controllingArea)==null?void 0:p.code,classValue:"0102",id:"",screenName:"Display"};const r=h=>{let v=[];h.statusCode===200&&(v.push(h.body.HierarchyTree),P(v)),P(v),I(!1)},n=h=>{};g(`/${T}/data/displayHierarchyTreeNodeStructure`,"post",r,n,t)},c=ve(),he=De(),u=S(t=>{var r;return(r=t==null?void 0:t.AllDropDown)==null?void 0:r.dropDown}),o=S(t=>t.commonFilter.HierarchyNodeGeneralLedger),ge=t=>{{var r=t;let n={...o,chartOfAccount:r};c(R({module:"HierarchyNodeGeneralLedger",filterData:n}))}$(t.code)},Ce=t=>{var r=t;let n={...o,controllingArea:r};c(R({module:"HierarchyNodeGeneralLedger",filterData:n}))},fe=t=>{var r=t;let n={...o,costElementGroup:r};c(R({module:"HierarchyNodeGeneralLedger",filterData:n}))},Ee=t=>{const r=a=>{c(x({keyName:"COA",data:a.body}))},n=a=>{console.log(a)};g(`/${T}/data/getChartOfAccounts`,"get",r,n)},ye=t=>{const r=a=>{c(x({keyName:"COAREA",data:a.body}))},n=a=>{};g(`/${Ze}/data/getControllingArea`,"get",r,n)},Ae=()=>{let t={decisionTableId:null,decisionTableName:"MDG_CUSTOM_DROPDOWN_LIST",version:"v1",rulePolicy:null,validityDate:null,conditions:[{"MDG_CONDITIONS.MDG_MODULE":"CCG","MDG_CONDITIONS.MDG_FIELD_NAME":"Controlling Area"}],systemFilters:null,systemOrders:null,filterString:null};const r=a=>{var E,y;if(a.statusCode===200){const f=((y=(E=a==null?void 0:a.data)==null?void 0:E.result[0])==null?void 0:y.MDG_CUSTOM_LOOKUP_ACTION_TYPE)||[];let A=[];f==null||f.map(p=>{let h={};h.code=p==null?void 0:p.MDG_LOOKUP_CODE,h.desc=p==null?void 0:p.MDG_LOOKUP_DESC,A.push(h)}),c(x({keyName:"NewControllingArea",data:A}))}},n=a=>{};me.environment==="localhost"?g(`/${Q}/rest/v1/invoke-rules`,"post",r,n,t):g(`/${Q}/v1/invoke-rules`,"post",r,n,t)},xe=t=>{const r=a=>{c(x({keyName:"PRCTRGroup",data:a.body}))},n=a=>{};g(`/${T}/node/getZeroLevelNodes?chartOfAccount=${t}`,"get",r,n)},$=t=>{const r=a=>{c(x({keyName:"CostElementGroupFilter",data:a.body}))},n=a=>{};g(`/${T}/data/getCostEleGroup?coa=${t}`,"get",r,n)},B=()=>{c(Ve({module:"HierarchyNodeGeneralLedger"}))};l.useEffect(()=>{ye(),Ee(),xe("ETCN"),$("ETCN"),Ae(),c(Le()),c(Me()),c(Re()),c(_e()),c(ke({module:"HierarchyNodeGeneralLedger",groupName:"costElementGroup"}))},[]);const be=()=>{H(!0)},z=()=>{H(!1)};return l.useEffect(()=>{ae({code:"ETCA",desc:"ET FAMILY CO AREA"}),ne(null),se(null)},[]),i("div",{ref:m,children:[e(Je,{dialogState:ie,openReusableDialog:be,closeReusableDialog:z,dialogTitle:ce,dialogMessage:de,handleDialogConfirm:z,dialogOkText:"OK",dialogSeverity:ue}),e(Qe,{blurLoading:te,loaderMessage:oe}),e("div",{style:{...Ie,backgroundColor:"#FAFCFF"},children:i(He,{spacing:1,children:[e(s,{container:!0,mt:0,sx:Pe,children:i(s,{item:!0,md:5,children:[e(b,{variant:"h3",children:e("strong",{children:d("Cost Element Group")})}),e(b,{variant:"body2",color:"#777",children:d("This view displays the selected Cost Element Hierarchy")})]})}),e(s,{container:!0,sx:We,children:e(s,{item:!0,md:12,children:i(ot,{defaultExpanded:!0,className:"filterGLH",children:[i(tt,{expandIcon:e($e,{sx:{fontSize:"1.25rem",color:O.palette.primary.dark}}),"aria-controls":"panel1a-content",id:"panel1a-header",children:[e(Be,{sx:{fontSize:"1.25rem",marginRight:1,color:O.palette.primary.dark}}),e(b,{sx:{fontSize:"0.875rem",fontWeight:600,color:O.palette.primary.dark},children:d("Filter Cost Element Group")})]}),i(ze,{sx:{padding:"1rem 1rem 0.5rem"},children:[e(s,{container:!0,rowSpacing:1,spacing:2,justifyContent:"space-between",alignItems:"center",children:i(s,{container:!0,spacing:1,sx:{padding:"0rem 1rem 0.5rem"},children:[i(s,{item:!0,md:2,children:[i(k,{sx:D,children:[d("Chart of Account")," ",e("span",{style:{color:(U=(j=L)==null?void 0:j.error)==null?void 0:U.dark},children:"*"})]}),e(M,{size:"small",fullWidth:!0,children:e(_,{options:(u==null?void 0:u.COA)??[],value:o==null?void 0:o.chartOfAccount,onChange:t=>{ge(t)},placeholder:d("SELECT CHART OF ACCOUNT"),disabled:!1,minWidth:"90%",listWidth:210})})]}),i(s,{item:!0,md:2,children:[i(k,{sx:D,children:[d("Controlling Area")," ",e("span",{style:{color:(q=(F=L)==null?void 0:F.error)==null?void 0:q.dark},children:"*"})]}),e(M,{size:"small",fullWidth:!0,children:e(_,{options:(u==null?void 0:u.COAREA)??[],value:o==null?void 0:o.controllingArea,onChange:t=>{Ce(t)},placeholder:d("SELECT CONTROLLING AREA"),disabled:!1,minWidth:"90%",listWidth:210})})]}),i(s,{item:!0,md:2,children:[i(k,{sx:D,children:[d("Cost Element Group")," ",e("span",{style:{color:(Y=(K=L)==null?void 0:K.error)==null?void 0:Y.dark},children:"*"})]}),e(M,{size:"small",fullWidth:!0,children:e(_,{options:(u==null?void 0:u.CostElementGroupFilter)??[],value:o==null?void 0:o.costElementGroup,onChange:t=>{fe(t)},placeholder:d("SELECT COST CENTER GROUP"),disabled:!1,minWidth:"90%",listWidth:210})})]})]})}),i(rt,{children:[e(X,{variant:"outlined",size:"small",startIcon:e(we,{sx:{fontSize:"1rem"}}),onClick:()=>{B()},children:d("Clear")}),e(s,{sx:{...V},children:e(et,{moduleName:"MaterialMaster",handleSearch:W,onPresetActiveChange:t=>re(t),onClearPreset:B})}),e(X,{variant:"contained",size:"small",startIcon:e(je,{sx:{fontSize:"1rem"}}),sx:{...Ue,...V},onClick:()=>{W()},children:d("Search")})]})]})]})})}),e(Fe,{children:C.length>0&&((Z=C[0])==null?void 0:Z.label)&&e(s,{container:!0,children:e(s,{item:!0,md:12,sx:{backgroundColor:"white",maxHeight:"max-content",height:"max-content",borderRadius:"8px",border:"1px solid #E0E0E0",mt:.25,boxShadow:"0px 2px 14px 0px rgba(48, 38, 185, 0.10)",...qe},children:e(s,{container:!0,display:"flex",flexDirection:"row",flexWrap:"nowrap",children:i(s,{item:!0,md:12,children:[C[0]?e(s,{item:!0,sx:{display:"flex",justifyContent:"space-between"}}):"",e(s,{children:e(b,{variant:"body1",fontWeight:"bold",justifyContent:"flex-start",marginBottom:2,children:d("Existing Structure in SAP")})}),e(Xe,{initialRawTreeData:C,editmode:!1,object:"General Ledger"})]})})})})}),e(J,{sx:{position:"fixed",bottom:0,left:0,right:0},elevation:2,children:(N==null?void 0:N.length)>0&&e(J,{sx:{position:"fixed",bottom:0,left:0,right:0},elevation:2,children:i(Ke,{className:"container_BottomNav",showLabels:!0,sx:{display:"flex",justifyContent:"flex-end",gap:1},children:[e(ee,{size:"small",variant:"contained",onClick:()=>{he("/requestBench/CostElementGroupRequestTab")},className:"createRequestButtonGLH",children:d("Create Request")}),e(Ye,{variant:"contained",ref:le,"aria-label":"split button"})]})})})]})})]})};export{Tt as default};
