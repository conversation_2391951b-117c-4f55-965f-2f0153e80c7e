import{c9 as K,ca as Q,cb as X,r as b,c as t,j as e,O as l,Bm as c,gg as ne,yA as H,Bo as ie,dy as A,bs as w,B as i,gd as oe,d as r,aa as Y,a5 as F,B7 as $,a6 as Z,qQ as le,aZ as T,ag as E,bE as W,D as ae,hZ as ce,bS as se,yB as de,Bp as he,dj as o,Bq as ue,B1 as xe,a as ge,cw as fe,g as be,u as pe,C as V,a_ as q,a$ as me,an as ve,au as G,aT as J,aG as ye}from"./index-f7d9b065.js";import{d as Se}from"./Download-52c4427b.js";import{d as Ce}from"./PermIdentityOutlined-0746a749.js";var z={},Ae=Q;Object.defineProperty(z,"__esModule",{value:!0});var ee=z.default=void 0,we=Ae(K()),Ee=X;ee=z.default=(0,we.default)((0,Ee.jsx)("path",{d:"M16.54 11 13 7.46l1.41-1.41 2.12 2.12 4.24-4.24 1.41 1.41zM11 7H2v2h9zm10 6.41L19.59 12 17 14.59 14.41 12 13 13.41 15.59 16 13 18.59 14.41 20 17 17.41 19.59 20 21 18.59 18.41 16zM11 15H2v2h9z"}),"Rule");var P={},Ie=Q;Object.defineProperty(P,"__esModule",{value:!0});var te=P.default=void 0,_e=Ie(K()),Le=X;te=P.default=(0,_e.default)((0,Le.jsx)("path",{d:"M3 5v14h18V5zm16 6h-3.33V7H19zm-5.33 0h-3.33V7h3.33zM8.33 7v4H5V7zM5 17v-4h3.33v4zm5.33 0v-4h3.33v4zm5.34 0v-4H19v4z"}),"ViewModuleOutlined");const Re=({apiData:h})=>{var m,_,L;const[j,U]=b.useState(!0),[x,B]=b.useState(""),[k,M]=b.useState(!1),p=Object.keys(h).map((n,s)=>({id:s+1,materialNumber:n,score:Math.round(h[n][0].MAT_MATERIAL_FIELD_CONFIG.totalScore),configCount:h[n].length})),[g,O]=b.useState(((m=p[0])==null?void 0:m.id)||null),v=b.useMemo(()=>{if(!x.trim())return p;const n=x.toLowerCase();return p.filter(s=>{const u=s.materialNumber.toLowerCase().includes(n),a=s.score.toString().includes(n);return u||a})},[p,x]);b.useEffect(()=>{x&&v.length>0&&!g&&O(v[0].id)},[x,v,g]);const y=n=>n>=80?"success":n>=50?"warning":"error",d=()=>{B("")},I=p.find(n=>n.id===g),S=I?h[I.materialNumber]:[],N=n=>{if(!n||n.length===0)return 0;const s=n.reduce((u,a)=>{const R=Object.keys(a)[0];return u+a[R].totalScore},0);return Math.round(s/n.length)},D=n=>{const s=n.filter(a=>a.value.pass===!0).length,u=n.filter(a=>a.value.pass===!1).length;return{passCount:s,failCount:u}};return t(i,{sx:{display:"flex",flexDirection:"column",height:"100vh",bgcolor:"#f5f5f5"},children:[e(i,{sx:{p:3,bgcolor:"white",borderBottom:1,borderColor:"divider"},children:e(l,{container:!0,spacing:3,justifyContent:"center",children:[{label:c.MODULE,value:"Material",color:"#FF6B6B",icon:e(te,{})},{label:c.NO_OBJECTS,value:Object.keys(h).length,color:"#FF9800",icon:e(ne,{})},{label:c.BUSINESS_RULES,value:(L=h==null?void 0:h[(_=Object.keys(h))==null?void 0:_[0]])==null?void 0:L.length,color:"#9C27B0",icon:e(ee,{})},{label:c.AGGREGATE_SCORE,value:`${Math.round(p.reduce((n,s)=>n+s.score,0)/p.length)}%`,color:"#2196F3",icon:e(H,{})},{label:c.CREATEDBY,value:"<EMAIL>",color:"#4CAF50",icon:e(ie,{})}].map((n,s)=>e(l,{item:!0,xs:12,sm:6,md:2.4,children:e(A,{sx:{background:`linear-gradient(135deg, ${n.color}15 0%, ${n.color}08 100%)`,border:`1px solid ${n.color}30`,transition:"all 0.3s ease",borderRadius:"10px"},children:t(w,{sx:{textAlign:"center",py:2},children:[e(i,{sx:{display:"flex",justifyContent:"center",mb:1},children:e(oe,{sx:{bgcolor:n.color,width:40,height:40},children:n.icon})}),e(r,{variant:"body2",color:"text.secondary",gutterBottom:!0,children:n.label}),e(r,{variant:"h5",fontWeight:"bold",color:n.color,children:n.value})]})})},s))})}),t(i,{sx:{display:"flex",flex:1,overflow:"hidden",zIndex:"1"},children:[e(ae,{variant:"persistent",open:j,sx:{width:320,flexShrink:0,"& .MuiDrawer-paper":{width:320,boxSizing:"border-box",position:"relative",height:"auto",bgcolor:"white",borderRight:1,borderColor:"divider"}},children:t(i,{sx:{height:"100%",display:"flex",flexDirection:"column"},children:[e(i,{sx:{p:2,borderBottom:1,borderColor:"divider"},children:e(Y,{fullWidth:!0,variant:"outlined",placeholder:c.SEARCH,value:x,onChange:n=>B(n.target.value),onFocus:()=>M(!0),onBlur:()=>M(!1),size:"small",InputProps:{startAdornment:e(F,{position:"start",children:e($,{color:"action"})}),endAdornment:x&&e(F,{position:"end",children:e(Z,{onClick:d,size:"small",children:e(le,{})})})},sx:{"& .MuiOutlinedInput-root":{bgcolor:"grey.50","&:hover":{bgcolor:"grey.100"},"&.Mui-focused":{bgcolor:"white"}}}})}),x&&e(i,{sx:{px:2,py:1,bgcolor:"grey.50",borderBottom:1,borderColor:"divider"},children:t(r,{variant:"caption",color:"text.secondary",children:[v.length," of ",p.length," materials"]})}),e(i,{sx:{flex:1,overflow:"auto",p:1,maxHeight:"500px"},children:t(T,{spacing:1,children:[v.map(n=>t(E,{elevation:g===n.id?8:1,sx:{p:2,cursor:"pointer",border:g===n.id?2:1,borderColor:g===n.id?"primary.main":"divider",bgcolor:g===n.id?"primary.50":"white",transition:"all 0.2s ease","&:hover":{bgcolor:g===n.id?"primary.100":"grey.50"},borderRadius:"10px"},onClick:()=>O(n.id),children:[t(i,{sx:{display:"flex",justifyContent:"space-between",alignItems:"center",mb:1},children:[e(r,{variant:"caption",color:"text.secondary",children:c.MATERIAL}),e(r,{variant:"caption",color:"text.secondary",paddingRight:"5px",children:c.SCORE})]}),t(i,{sx:{display:"flex",justifyContent:"space-between",alignItems:"center",mb:1},children:[e(r,{variant:"subtitle2",fontWeight:g===n.id?600:500,color:g===n.id?"primary.main":"text.primary",children:n.materialNumber}),e(W,{label:`${n.score}%`,size:"small",color:y(n.score),variant:"outlined"})]})]},n.id)),v.length===0&&t(i,{sx:{textAlign:"center",py:6},children:[e($,{sx:{fontSize:48,color:"grey.300",mb:2}}),e(r,{variant:"body2",color:"text.secondary",children:c.NO_MATERIAL_FOUND}),e(r,{variant:"caption",color:"text.secondary",children:c.ADJUST})]})]})})]})}),e(i,{sx:{flex:1,overflow:"auto",p:3},children:I&&S.length>0?t(T,{spacing:3,children:[e(E,{elevation:2,sx:{p:3,background:"linear-gradient(135deg,rgb(137, 155, 233) 0%,rgb(171, 114, 228) 100%)",color:"white",borderRadius:"10px"},children:t(l,{container:!0,alignItems:"center",justifyContent:"space-between",children:[t(l,{item:!0,children:[e(r,{variant:"h3",fontWeight:"bold",gutterBottom:!0,color:"white",children:I.materialNumber}),t(r,{variant:"body2",sx:{opacity:.9},children:[c.ANALYSIS_DETAILS," - ",S.length," Business Rule",S.length!==1?"s":""]})]}),e(l,{item:!0,children:t(i,{sx:{textAlign:"right"},children:[t(r,{variant:"h4",fontWeight:"bold",color:"white",children:[N(S),"%"]}),e(r,{variant:"body2",sx:{opacity:.9},children:c.OVERALL_SCORE})]})})]})}),e(l,{container:!0,spacing:3,sx:{marginLeft:"200px"},children:S.map((n,s)=>{const u=Object.keys(n)[0],a=n[u],{passCount:R,failCount:C}=D(a.fieldDetails);return e(l,{item:!0,xs:12,lg:6,children:e(A,{elevation:3,sx:{ml:-3,mr:3,height:"100%",borderRadius:"10px",transition:"all 0.3s ease","&:hover":{boxShadow:6}},children:t(w,{sx:{p:3},children:[t(i,{sx:{display:"flex",justifyContent:"space-between",alignItems:"center",mb:2},children:[t(r,{variant:"h6",fontWeight:"bold",color:"primary",children:[t(i,{component:"span",sx:{fontWeight:"bold"},children:[c.BUSINESS_RULE,": "]}),e(i,{component:"span",sx:{fontWeight:"medium"},children:u})]}),e(W,{label:`${Math.round(a.totalScore)}%`,color:y(a.totalScore),sx:{fontWeight:"bold"}})]}),e(i,{sx:{mb:2},children:e(ce,{variant:"determinate",value:a.totalScore,color:y(a.totalScore),sx:{height:8,borderRadius:4}})}),t(l,{container:!0,spacing:2,sx:{mb:2},children:[e(l,{item:!0,xs:4,children:t(i,{sx:{textAlign:"center"},children:[e(r,{variant:"h6",fontWeight:"bold",children:a.fieldDetails.length}),e(r,{variant:"caption",color:"text.secondary",children:c.FIELDS})]})}),e(l,{item:!0,xs:4,children:t(i,{sx:{textAlign:"center"},children:[e(r,{variant:"h6",fontWeight:"bold",color:"success.main",children:R}),e(r,{variant:"caption",color:"text.secondary",children:c.PASS})]})}),e(l,{item:!0,xs:4,children:t(i,{sx:{textAlign:"center"},children:[e(r,{variant:"h6",fontWeight:"bold",color:"error.main",children:C}),e(r,{variant:"caption",color:"text.secondary",children:c.FAIL})]})})]}),e(se,{sx:{my:2}}),e(T,{spacing:1.5,children:a.fieldDetails.map((f,re)=>e(E,{variant:"outlined",sx:{p:2,bgcolor:f.value.pass?"success.50":"error.50",borderColor:f.value.pass?"success.200":"error.200"},children:t(l,{container:!0,alignItems:"center",spacing:2,paddingLeft:"-500px",children:[e(l,{item:!0,children:f.value.pass?e(H,{color:"success"}):e(de,{color:"error"})}),t(l,{item:!0,xs:!0,children:[t(r,{variant:"h5",children:[e(i,{component:"span",sx:{fontWeight:"bold"},children:"Field Name: "}),e(i,{component:"span",sx:{fontWeight:"normal"},children:f.fieldName})]}),t(r,{variant:"h6",color:"text.secondary",children:[c.SCORE,": ",f.value.givenScore,"/",f.value.allotedScore]})]}),e(l,{item:!0,children:t(i,{sx:{textAlign:"right"},children:[t(r,{variant:"h6",color:"text.secondary",children:[c.PRESENT_VALUE,": ",f.value.dataPresentValue||"N/A"]}),e("br",{}),t(r,{variant:"h6",color:"text.secondary",children:[c.EXPECTED_VALUE,": ",f.value.dataDefaultValue]})]})})]})},re))})]})})},s)})})]}):t(E,{elevation:2,sx:{p:6,textAlign:"center",bgcolor:"grey.50",minHeight:400,display:"flex",flexDirection:"column",justifyContent:"center",alignItems:"center"},children:[e(he,{sx:{fontSize:64,color:"grey.300",mb:2}}),e(r,{variant:"h5",color:"text.secondary",gutterBottom:!0,children:c.NO_MATERIAL}),e(r,{variant:"body2",color:"text.secondary",children:c.VALIDATIONS_DETAILS})]})})]})]})},je=()=>t(xe,{maxWidth:"xl",sx:{py:3,bgcolor:"#f5f5f5",minHeight:"100vh"},children:[t(l,{container:!0,spacing:3,sx:{mb:4},children:[e(l,{item:!0,xs:12,sm:6,md:2.4,children:e(A,{sx:{textAlign:"center",p:2,bgcolor:"#fff5f5"},children:t(w,{children:[e(i,{sx:{display:"flex",justifyContent:"center",mb:2},children:e(o,{variant:"circular",width:48,height:48})}),e(r,{variant:"body2",color:"text.secondary",gutterBottom:!0,children:e(o,{width:80})}),e(r,{variant:"h6",color:"#dc3545",fontWeight:"bold",children:e(o,{width:60})})]})})}),e(l,{item:!0,xs:12,sm:6,md:2.4,children:e(A,{sx:{textAlign:"center",p:2,bgcolor:"#fff8f0"},children:t(w,{children:[e(i,{sx:{display:"flex",justifyContent:"center",mb:2},children:e(o,{variant:"circular",width:48,height:48})}),e(r,{variant:"body2",color:"text.secondary",gutterBottom:!0,children:e(o,{width:100})}),e(r,{variant:"h6",color:"#fd7e14",fontWeight:"bold",children:e(o,{width:40})})]})})}),e(l,{item:!0,xs:12,sm:6,md:2.4,children:e(A,{sx:{textAlign:"center",p:2,bgcolor:"#f8f0ff"},children:t(w,{children:[e(i,{sx:{display:"flex",justifyContent:"center",mb:2},children:e(o,{variant:"circular",width:48,height:48})}),e(r,{variant:"body2",color:"text.secondary",gutterBottom:!0,children:e(o,{width:120})}),e(r,{variant:"h6",color:"#6f42c1",fontWeight:"bold",children:e(o,{width:20})})]})})}),e(l,{item:!0,xs:12,sm:6,md:2.4,children:e(A,{sx:{textAlign:"center",p:2,bgcolor:"#f0f8ff"},children:t(w,{children:[e(i,{sx:{display:"flex",justifyContent:"center",mb:2},children:e(o,{variant:"circular",width:48,height:48})}),e(r,{variant:"body2",color:"text.secondary",gutterBottom:!0,children:e(o,{width:90})}),e(r,{variant:"h6",color:"#007bff",fontWeight:"bold",children:e(o,{width:30})})]})})}),e(l,{item:!0,xs:12,sm:6,md:2.4,children:e(A,{sx:{textAlign:"center",p:2,bgcolor:"#f0fff4"},children:t(w,{children:[e(i,{sx:{display:"flex",justifyContent:"center",mb:2},children:e(o,{variant:"circular",width:48,height:48})}),e(r,{variant:"body2",color:"text.secondary",gutterBottom:!0,children:e(o,{width:70})}),e(r,{variant:"h6",color:"#28a745",fontWeight:"bold",children:e(o,{width:180})})]})})})]}),t(l,{container:!0,spacing:3,children:[t(l,{item:!0,xs:12,md:4,children:[e(i,{sx:{mb:2},children:e(Y,{fullWidth:!0,placeholder:"Search materials...",InputProps:{startAdornment:e(F,{position:"start",children:e($,{color:"action"})})},disabled:!0})}),e(E,{sx:{p:2,maxHeight:400,overflow:"auto"},children:[1,2,3,4].map(h=>t(i,{sx:{display:"flex",justifyContent:"space-between",alignItems:"center",p:2,mb:1,border:"1px solid #e0e0e0",borderRadius:1,bgcolor:h===1?"#e3f2fd":"white"},children:[t(i,{children:[e(r,{variant:"body2",color:"text.secondary",children:e(o,{width:50})}),e(r,{variant:"h6",children:e(o,{width:60})})]}),t(i,{children:[e(r,{variant:"body2",color:"text.secondary",children:e(o,{width:40})}),e(W,{label:e(o,{width:20}),size:"small",sx:{bgcolor:"#ffebee",color:"#d32f2f"}})]})]},h))})]}),t(l,{item:!0,xs:12,md:8,children:[e(E,{sx:{p:3,mb:3,bgcolor:"linear-gradient(135deg, #667eea 0%, #764ba2 100%)"},children:t(i,{sx:{display:"flex",justifyContent:"space-between",alignItems:"center"},children:[t(i,{children:[e(r,{variant:"h4",sx:{color:"white",fontWeight:"bold"},children:e(o,{width:60,sx:{bgcolor:"rgba(255,255,255,0.3)"}})}),e(r,{variant:"body1",sx:{color:"white",opacity:.9},children:e(o,{width:200,sx:{bgcolor:"rgba(255,255,255,0.3)"}})})]}),t(i,{sx:{textAlign:"right"},children:[e(r,{variant:"h4",sx:{color:"white",fontWeight:"bold"},children:e(o,{width:40,sx:{bgcolor:"rgba(255,255,255,0.3)"}})}),e(r,{variant:"body1",sx:{color:"white",opacity:.9},children:e(o,{width:80,sx:{bgcolor:"rgba(255,255,255,0.3)"}})})]})]})}),t(E,{sx:{p:3},children:[t(i,{sx:{display:"flex",alignItems:"center",gap:2,mb:3},children:[e(r,{variant:"h6",sx:{color:"#1976d2"},children:e(o,{width:100})}),e(o,{width:200}),e(W,{label:e(o,{width:20}),size:"small",sx:{bgcolor:"#ffebee",color:"#d32f2f"}})]}),e(i,{sx:{mb:3},children:e(o,{variant:"rectangular",width:"100%",height:8,sx:{borderRadius:1}})}),t(l,{container:!0,spacing:3,sx:{mb:3},children:[e(l,{item:!0,xs:4,children:t(i,{sx:{textAlign:"center"},children:[e(r,{variant:"h5",fontWeight:"bold",children:e(o,{width:20})}),e(r,{variant:"body2",color:"text.secondary",children:e(o,{width:40})})]})}),e(l,{item:!0,xs:4,children:t(i,{sx:{textAlign:"center"},children:[e(r,{variant:"h5",fontWeight:"bold",sx:{color:"#4caf50"},children:e(o,{width:20})}),e(r,{variant:"body2",color:"text.secondary",children:e(o,{width:30})})]})}),e(l,{item:!0,xs:4,children:t(i,{sx:{textAlign:"center"},children:[e(r,{variant:"h5",fontWeight:"bold",sx:{color:"#f44336"},children:e(o,{width:20})}),e(r,{variant:"body2",color:"text.secondary",children:e(o,{width:25})})]})})]}),e(i,{sx:{border:"1px solid #e0e0e0",borderRadius:1,p:2},children:t(i,{sx:{display:"flex",alignItems:"center",gap:2,mb:2},children:[e(ue,{sx:{color:"#f44336"}}),t(i,{sx:{flex:1},children:[e(r,{variant:"body1",fontWeight:"bold",children:e(o,{width:150})}),e(r,{variant:"body2",color:"text.secondary",children:e(o,{width:80})})]}),t(i,{sx:{textAlign:"right"},children:[e(r,{variant:"body2",color:"text.secondary",children:e(o,{width:100})}),e(r,{variant:"body2",color:"text.secondary",children:e(o,{width:120})})]})]})})]})]})]})]}),De=()=>{var D;const[h,j]=b.useState(!1),[U,x]=b.useState(!1),[B,k]=b.useState(""),[M,p]=b.useState({}),{t:g}=ge(),{showSnackbar:O}=fe(),v=be(),y=pe(),d=y==null?void 0:y.state;b.useEffect(()=>{d!=null&&d.requestId&&S(d==null?void 0:d.requestId)},[d]);const I={padding:"1rem",minHeight:"100vh"},S=m=>{var s,u;x(!0);let _={requestId:m};const L=a=>{p(a==null?void 0:a.body),x(!1)},n=a=>{x(!1)};V(`/${G}${(u=(s=J)==null?void 0:s.DATA_CLEANSE_APIS)==null?void 0:u.CLEANSING_REQ_DETAILS}`,"post",L,n,_)},N=m=>{var s,u;j(!0);let _={requestId:m};const L=a=>{var f;const R=URL.createObjectURL(a),C=document.createElement("a");C.href=R,C.setAttribute("download",`${m}_Data Cleanse.pdf`),document.body.appendChild(C),C.click(),document.body.removeChild(C),URL.revokeObjectURL(R),j(!1),k(""),O(`${m}${(f=c)==null?void 0:f.EXPORT_SUCCESS}`,"success")},n=()=>{j(!1),k(""),O(`Failed exporting ${m}_Data Cleanse.pdf`,"error")};V(`/${G}${(u=(s=J)==null?void 0:s.DATA_CLEANSE_APIS)==null?void 0:u.DOWNLOAD_PDF}`,"postandgetblob",L,n,_)};return t("div",{style:{maxHeight:"50vh"},children:[e(l,{container:!0,sx:{padding:"16px"},children:t(l,{item:!0,md:12,sx:{padding:"16px",display:"flex",mb:-6},children:[t(l,{md:9,sx:{display:"flex"},children:[e(Z,{color:"primary",sx:q,onClick:()=>v(-1),children:e(me,{sx:{fontSize:"25px",color:"#000000"}})}),t(l,{item:!0,md:12,paddingTop:.5,children:[t(r,{variant:"h5",sx:{mb:1,textAlign:"left",display:"flex",alignItems:"center",gap:1},children:[e(Ce,{sx:{fontSize:"1.5rem"}}),g("Request Header ID"),":"," ",e("span",{children:d==null?void 0:d.requestId})]}),e(r,{variant:"body2",color:"#777"})]})]}),e(l,{md:3,sx:{display:"flex",justifyContent:"flex-end",alignItems:"flex-start"},children:e(ve,{variant:"outlined",color:"primary",startIcon:e(Se,{}),sx:q,onClick:()=>{N((d==null?void 0:d.requestId)||"")},children:c.PDF})})]})}),e(ye,{blurLoading:h,loaderMessage:B}),e("div",{style:{...I,backgroundColor:"#FAFCFF"},children:e(T,{spacing:1,children:U||!((D=Object==null?void 0:Object.keys(M))!=null&&D.length)?e(je,{}):e(Re,{apiData:M})})})]})};export{De as default};
