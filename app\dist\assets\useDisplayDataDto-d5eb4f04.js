import{s as re,r as S,aP as ue,cw as Ie,wP as O,cZ as _e,aX as e,C as ce,bf as oe,aO as c,f$ as V,au as le,aJ as Re,wQ as Se,wR as Z,wS as Oe,fS as de,g4 as te,r0 as ge,wT as Ge,ao as he,er as me,ae as D}from"./index-f7d9b065.js";import{u as pe,a as He}from"./useChangeMaterialRows-cefb1340.js";import{u as Le}from"./useFinanceCostingRows-ffbb569f.js";const Ue=()=>{const n=re(),[a,I]=S.useState(!1),[w,_]=S.useState(null),{getChangeTemplate:k}=pe(),{fetchDisplayDataRows:ee}=He(),{createFCRows:se}=Le(),{customError:d}=ue(),{showSnackbar:Ee}=Ie();return{getDisplayData:S.useCallback(async(Te,u,Ce,i,T,t)=>new Promise((o,g)=>{var G,h;I(!0),_(null),n(O(!0));const A=Te,l=_e(oe.CURRENT_TASK,!0,{}),s=u||(i==null?void 0:i.ATTRIBUTE_2)||(l==null?void 0:l.ATTRIBUTE_2);let ie=Ce?{massCreationId:T!=null&&T.isBifurcated?"":s===e.CREATE||s===e.CREATE_WITH_UPLOAD?A:"",massChildCreationId:T!=null&&T.isBifurcated&&(s===e.CREATE||s===e.CREATE_WITH_UPLOAD)?A:"",massChangeId:T!=null&&T.isBifurcated?"":s===e.CHANGE||s===e.CHANGE_WITH_UPLOAD?A:"",massExtendId:T!=null&&T.isBifurcated?"":s===e.EXTEND||s===e.EXTEND_WITH_UPLOAD?A:"",massSchedulingId:T!=null&&T.isBifurcated?"":s===e.FINANCE_COSTING?A:"",screenName:s===e.FINANCE_COSTING?"":s,dtName:s===e.FINANCE_COSTING?"":"MDG_MAT_MATERIAL_FIELD_CONFIG",version:s===e.FINANCE_COSTING?"":"v2",page:0,size:s===e.FINANCE_COSTING?100:s===e.CHANGE||s===e.CHANGE_WITH_UPLOAD?10:50,sort:"",ApproverGroup:i==null?void 0:i.ATTRIBUTE_5,Region:"",massChildSchedulingId:T!=null&&T.isBifurcated&&s===e.FINANCE_COSTING?A:"",massChildExtendId:T!=null&&T.isBifurcated&&(s===e.EXTEND||s===e.EXTEND_WITH_UPLOAD)?A:"",massChildChangeId:T!=null&&T.isBifurcated&&(s===e.CHANGE||s===e.CHANGE_WITH_UPLOAD)?A:""}:{massCreationId:"",massChangeId:"",massSchedulingId:s===e.FINANCE_COSTING||s==="Finance Costing"?A:"",massExtendId:"",screenName:s==="MASS_CREATE"||s==="Mass Create"||s===e.CREATE?e.CREATE:s===e.FINANCE_COSTING?"":e.CHANGE,dtName:s===e.FINANCE_COSTING?"":"MDG_MAT_MATERIAL_FIELD_CONFIG",version:s===e.FINANCE_COSTING?"":"v2",page:0,size:s===e.FINANCE_COSTING||u===e.FINANCE_COSTING?100:u===e.CHANGE||u===e.CHANGE_WITH_UPLOAD||s===e.CHANGE||s===e.CHANGE_WITH_UPLOAD?10:50,sort:"",ApproverGroup:i==null?void 0:i.ATTRIBUTE_5,Region:"",massChildCreationId:s==="MASS_CREATE"||s==="Mass Create"||s===e.CREATE||s===e.CREATE_WITH_UPLOAD?A:"",massChildSchedulingId:"",massChildExtendId:s===e.EXTEND||s===e.EXTEND_WITH_UPLOAD?A:"",massChildChangeId:s==="MASS_CHANGE"||s==="Mass Change"||s===e.CHANGE||s===e.CHANGE_WITH_UPLOAD?A:""};const ne=async E=>{var m,p,H,L,U,f,M,P,F,q,y,B,W,b,x,X,v,K,z,J,Y,$,j;try{if((E==null?void 0:E.statusCode)===Re.STATUS_200){n(O(!1)),I(!1);const C=E.body;if(n(Se(E==null?void 0:E.totalElements)),(E==null?void 0:E.totalPages)===1||(E==null?void 0:E.currentPage)+1===(E==null?void 0:E.totalPages)?(n(Z(E==null?void 0:E.totalElements)),n(Oe(!0))):n(Z(((E==null?void 0:E.currentPage)+1)*(E==null?void 0:E.pageSize))),(i==null?void 0:i.ATTRIBUTE_2)===e.CHANGE||(i==null?void 0:i.ATTRIBUTE_2)===e.CHANGE_WITH_UPLOAD||u===e.CHANGE_WITH_UPLOAD||u===e.CHANGE){n(de({keyName:"requestHeaderData",data:(m=C[0])==null?void 0:m.Torequestheaderdata})),k(((p=C[0])==null?void 0:p.Torequestheaderdata)||"",C[0]||{}),ee(C),o(E);return}if(u===e.FINANCE_COSTING||(i==null?void 0:i.ATTRIBUTE_2)===e.FINANCE_COSTING){const N={ReqCreatedBy:(L=(H=C[0])==null?void 0:H.Torequestheaderdata)==null?void 0:L.ReqCreatedBy,RequestStatus:(f=(U=C[0])==null?void 0:U.Torequestheaderdata)==null?void 0:f.RequestStatus,Region:(P=(M=C[0])==null?void 0:M.Torequestheaderdata)==null?void 0:P.Region,ReqCreatedOn:new Date().toISOString(),ReqUpdatedOn:new Date().toISOString(),RequestType:(q=(F=C[0])==null?void 0:F.Torequestheaderdata)==null?void 0:q.RequestType,RequestDesc:(B=(y=C[0])==null?void 0:y.Torequestheaderdata)==null?void 0:B.RequestDesc,RequestPriority:(b=(W=C[0])==null?void 0:W.Torequestheaderdata)==null?void 0:b.RequestPriority,LeadingCat:(X=(x=C[0])==null?void 0:x.Torequestheaderdata)==null?void 0:X.LeadingCat,RequestId:(K=(v=C[0])==null?void 0:v.Torequestheaderdata)==null?void 0:K.RequestId,TemplateName:(J=(z=C[0])==null?void 0:z.Torequestheaderdata)==null?void 0:J.TemplateName,Json301:($=(Y=C[0])==null?void 0:Y.Torequestheaderdata)==null?void 0:$.Json301};n(te({data:N}));const R=await se(C);n(ge(R)),o(E);return}const r=await Ge(C);await n(he({data:r==null?void 0:r.payload}));const Ne=Object.keys(r==null?void 0:r.payload).filter(N=>!isNaN(Number(N))),Q={};Ne.forEach(N=>{Q[N]=r==null?void 0:r.payload[N]}),n(me((j=Object.values(Q))==null?void 0:j.map(N=>N.headerData).sort((N,R)=>N.lineNumber-R.lineNumber))),o(E)}else Ee(E==null?void 0:E.message,"error")}catch(C){d(D.ERROR_GET_DISPLAY_DATA),_(C),I(!1),g(C)}},Ae=E=>{d(D.ERROR_FETCHING_DATA),_(E),I(!1),n(O(!1)),g(E)};ce(`/${t===c.MAT?(G=V)==null?void 0:G[c.MAT]:t===c.ART?(h=V)==null?void 0:h[c.ART]:le}/data/displayMassMaterialDTO`,"post",ne,Ae,ie)}),[n]),loading:a,error:w,clearError:()=>_(null)}},qe=Ue;export{qe as u};
