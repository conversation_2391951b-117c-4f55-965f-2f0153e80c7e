import{r as s,f5 as ee,t as te,ef as Ne,fh as Ia,fg as Ca,_ as ne,fc as Ka,f6 as Xa,b as mt,c as p,j as r,ag as Ya,ah as Ja,B as Qa,F as ft,aG as Za,aZ as ei,d2 as ti,a6 as ga,qY as c,u as ni,g as ri,cw as ai,qZ as ii,C as Ct,aO as oi,dm as gt,aT as ht,q_ as si,aJ as li}from"./index-f7d9b065.js";import{C as ci}from"./ChangeLogWF-b8efdaaf.js";import{K as ct,g as Oa,n as Z,a1 as di,r as bt,C as ui,m as $a,aE as fi,a as rt,u as At,b as pi,I as mi,c as gi,E as hi,d as Si,N as bi,y as vi,aX as yi,Q as wt,ba as xi,a9 as Ot,s as wi,bb as Ei,aj as <PERSON>,ao as <PERSON>,ap as Ii,a<PERSON> as Ta,ak as Ci,am as Oi,al as $i,an as Ti,bc as _i,bd as <PERSON>,be as Ri,aq as Mi,_ as ki,as as Bi,ac as Fi,ai as Di,ad as Pi,at as Hi,W as ha,aw as Sa,f as _a,k as Wi,ax as ji,v as Vi,w as zi,o as qi,e as Gi,h as Ui,B as de,T as Xe,aU as Y,aT as _,i as Ki,D as Xi,S as vt,F as G,l as it,M as ot}from"./EditOutlined-9d614b39.js";import{d as Yi}from"./TrackChangesTwoTone-7a2ab513.js";import{x as Aa,y as Ra,z as Ji,S as Qi,C as ut,T as Lt,q as Zi,r as Tt,w as eo,P as ba,m as _e,v as va}from"./index-b53128b6.js";import{C as to,A as Je,w as no,_ as dt,f as Ma,F as ro}from"./EyeOutlined-0bb7ab85.js";import{N as ao,u as io,a as oo,A as so}from"./context-4d14404f.js";import"./asyncToGenerator-88583e02.js";const lo=e=>{const{componentCls:t,notificationMarginEdge:n,animationMaxHeight:a}=e,o=`${t}-notice`,l=new ct("antNotificationFadeIn",{"0%":{transform:"translate3d(100%, 0, 0)",opacity:0},"100%":{transform:"translate3d(0, 0, 0)",opacity:1}}),d=new ct("antNotificationTopFadeIn",{"0%":{top:-a,opacity:0},"100%":{top:0,opacity:1}}),u=new ct("antNotificationBottomFadeIn",{"0%":{bottom:e.calc(a).mul(-1).equal(),opacity:0},"100%":{bottom:0,opacity:1}}),f=new ct("antNotificationLeftFadeIn",{"0%":{transform:"translate3d(-100%, 0, 0)",opacity:0},"100%":{transform:"translate3d(0, 0, 0)",opacity:1}});return{[t]:{[`&${t}-top, &${t}-bottom`]:{marginInline:0,[o]:{marginInline:"auto auto"}},[`&${t}-top`]:{[`${t}-fade-enter${t}-fade-enter-active, ${t}-fade-appear${t}-fade-appear-active`]:{animationName:d}},[`&${t}-bottom`]:{[`${t}-fade-enter${t}-fade-enter-active, ${t}-fade-appear${t}-fade-appear-active`]:{animationName:u}},[`&${t}-topRight, &${t}-bottomRight`]:{[`${t}-fade-enter${t}-fade-enter-active, ${t}-fade-appear${t}-fade-appear-active`]:{animationName:l}},[`&${t}-topLeft, &${t}-bottomLeft`]:{marginRight:{value:0,_skip_check_:!0},marginLeft:{value:n,_skip_check_:!0},[o]:{marginInlineEnd:"auto",marginInlineStart:0},[`${t}-fade-enter${t}-fade-enter-active, ${t}-fade-appear${t}-fade-appear-active`]:{animationName:f}}}}},co=lo,uo=["top","topLeft","topRight","bottom","bottomLeft","bottomRight"],fo={topLeft:"left",topRight:"right",bottomLeft:"left",bottomRight:"right",top:"left",bottom:"left"},po=(e,t)=>{const{componentCls:n}=e;return{[`${n}-${t}`]:{[`&${n}-stack > ${n}-notice-wrapper`]:{[t.startsWith("top")?"top":"bottom"]:0,[fo[t]]:{value:0,_skip_check_:!0}}}}},mo=e=>{const t={};for(let n=1;n<e.notificationStackLayer;n++)t[`&:nth-last-child(${n+1})`]={overflow:"hidden",[`& > ${e.componentCls}-notice`]:{opacity:0,transition:`opacity ${e.motionDurationMid}`}};return Object.assign({[`&:not(:nth-last-child(-n+${e.notificationStackLayer}))`]:{opacity:0,overflow:"hidden",color:"transparent",pointerEvents:"none"}},t)},go=e=>{const t={};for(let n=1;n<e.notificationStackLayer;n++)t[`&:nth-last-child(${n+1})`]={background:e.colorBgBlur,backdropFilter:"blur(10px)","-webkit-backdrop-filter":"blur(10px)"};return Object.assign({},t)},ho=e=>{const{componentCls:t}=e;return Object.assign({[`${t}-stack`]:{[`& > ${t}-notice-wrapper`]:Object.assign({transition:`transform ${e.motionDurationSlow}, backdrop-filter 0s`,willChange:"transform, opacity",position:"absolute"},mo(e))},[`${t}-stack:not(${t}-stack-expanded)`]:{[`& > ${t}-notice-wrapper`]:Object.assign({},go(e))},[`${t}-stack${t}-stack-expanded`]:{[`& > ${t}-notice-wrapper`]:{"&:not(:nth-last-child(-n + 1))":{opacity:1,overflow:"unset",color:"inherit",pointerEvents:"auto",[`& > ${e.componentCls}-notice`]:{opacity:1}},"&:after":{content:'""',position:"absolute",height:e.margin,width:"100%",insetInline:0,bottom:e.calc(e.margin).mul(-1).equal(),background:"transparent",pointerEvents:"auto"}}}},uo.map(n=>po(e,n)).reduce((n,a)=>Object.assign(Object.assign({},n),a),{}))},So=ho,ka=e=>{const{iconCls:t,componentCls:n,boxShadow:a,fontSizeLG:o,notificationMarginBottom:l,borderRadiusLG:d,colorSuccess:u,colorInfo:f,colorWarning:S,colorError:x,colorTextHeading:N,notificationBg:E,notificationPadding:C,notificationMarginEdge:F,notificationProgressBg:O,notificationProgressHeight:D,fontSize:A,lineHeight:L,width:P,notificationIconSize:g,colorText:H}=e,M=`${n}-notice`;return{position:"relative",marginBottom:l,marginInlineStart:"auto",background:E,borderRadius:d,boxShadow:a,[M]:{padding:C,width:P,maxWidth:`calc(100vw - ${Z(e.calc(F).mul(2).equal())})`,overflow:"hidden",lineHeight:L,wordWrap:"break-word"},[`${M}-message`]:{color:N,fontSize:o,lineHeight:e.lineHeightLG},[`${M}-description`]:{fontSize:A,color:H,marginTop:e.marginXS},[`${M}-closable ${M}-message`]:{paddingInlineEnd:e.paddingLG},[`${M}-with-icon ${M}-message`]:{marginInlineStart:e.calc(e.marginSM).add(g).equal(),fontSize:o},[`${M}-with-icon ${M}-description`]:{marginInlineStart:e.calc(e.marginSM).add(g).equal(),fontSize:A},[`${M}-icon`]:{position:"absolute",fontSize:g,lineHeight:1,[`&-success${t}`]:{color:u},[`&-info${t}`]:{color:f},[`&-warning${t}`]:{color:S},[`&-error${t}`]:{color:x}},[`${M}-close`]:Object.assign({position:"absolute",top:e.notificationPaddingVertical,insetInlineEnd:e.notificationPaddingHorizontal,color:e.colorIcon,outline:"none",width:e.notificationCloseButtonSize,height:e.notificationCloseButtonSize,borderRadius:e.borderRadiusSM,transition:`background-color ${e.motionDurationMid}, color ${e.motionDurationMid}`,display:"flex",alignItems:"center",justifyContent:"center",background:"none",border:"none","&:hover":{color:e.colorIconHover,backgroundColor:e.colorBgTextHover},"&:active":{backgroundColor:e.colorBgTextActive}},di(e)),[`${M}-progress`]:{position:"absolute",display:"block",appearance:"none",inlineSize:`calc(100% - ${Z(d)} * 2)`,left:{_skip_check_:!0,value:d},right:{_skip_check_:!0,value:d},bottom:0,blockSize:D,border:0,"&, &::-webkit-progress-bar":{borderRadius:d,backgroundColor:"rgba(0, 0, 0, 0.04)"},"&::-moz-progress-bar":{background:O},"&::-webkit-progress-value":{borderRadius:d,background:O}},[`${M}-actions`]:{float:"right",marginTop:e.marginSM}}},bo=e=>{const{componentCls:t,notificationMarginBottom:n,notificationMarginEdge:a,motionDurationMid:o,motionEaseInOut:l}=e,d=`${t}-notice`,u=new ct("antNotificationFadeOut",{"0%":{maxHeight:e.animationMaxHeight,marginBottom:n},"100%":{maxHeight:0,marginBottom:0,paddingTop:0,paddingBottom:0,opacity:0}});return[{[t]:Object.assign(Object.assign({},bt(e)),{position:"fixed",zIndex:e.zIndexPopup,marginRight:{value:a,_skip_check_:!0},[`${t}-hook-holder`]:{position:"relative"},[`${t}-fade-appear-prepare`]:{opacity:"0 !important"},[`${t}-fade-enter, ${t}-fade-appear`]:{animationDuration:e.motionDurationMid,animationTimingFunction:l,animationFillMode:"both",opacity:0,animationPlayState:"paused"},[`${t}-fade-leave`]:{animationTimingFunction:l,animationFillMode:"both",animationDuration:o,animationPlayState:"paused"},[`${t}-fade-enter${t}-fade-enter-active, ${t}-fade-appear${t}-fade-appear-active`]:{animationPlayState:"running"},[`${t}-fade-leave${t}-fade-leave-active`]:{animationName:u,animationPlayState:"running"},"&-rtl":{direction:"rtl",[`${d}-actions`]:{float:"left"}}})},{[t]:{[`${d}-wrapper`]:Object.assign({},ka(e))}}]},Ba=e=>({zIndexPopup:e.zIndexPopupBase+ui+50,width:384}),Fa=e=>{const t=e.paddingMD,n=e.paddingLG;return $a(e,{notificationBg:e.colorBgElevated,notificationPaddingVertical:t,notificationPaddingHorizontal:n,notificationIconSize:e.calc(e.fontSizeLG).mul(e.lineHeightLG).equal(),notificationCloseButtonSize:e.calc(e.controlHeightLG).mul(.55).equal(),notificationMarginBottom:e.margin,notificationPadding:`${Z(e.paddingMD)} ${Z(e.paddingContentHorizontalLG)}`,notificationMarginEdge:e.marginLG,animationMaxHeight:150,notificationStackLayer:3,notificationProgressHeight:2,notificationProgressBg:`linear-gradient(90deg, ${e.colorPrimaryBorderHover}, ${e.colorPrimary})`})},Da=Oa("Notification",e=>{const t=Fa(e);return[bo(t),co(t),So(t)]},Ba),vo=fi(["Notification","PurePanel"],e=>{const t=`${e.componentCls}-notice`,n=Fa(e);return{[`${t}-pure-panel`]:Object.assign(Object.assign({},ka(n)),{width:n.width,maxWidth:`calc(100vw - ${Z(e.calc(n.notificationMarginEdge).mul(2).equal())})`,margin:0})}},Ba);var yo=globalThis&&globalThis.__rest||function(e,t){var n={};for(var a in e)Object.prototype.hasOwnProperty.call(e,a)&&t.indexOf(a)<0&&(n[a]=e[a]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var o=0,a=Object.getOwnPropertySymbols(e);o<a.length;o++)t.indexOf(a[o])<0&&Object.prototype.propertyIsEnumerable.call(e,a[o])&&(n[a[o]]=e[a[o]]);return n};function Rt(e,t){return t===null||t===!1?null:t||s.createElement(to,{className:`${e}-close-icon`})}const xo={success:pi,info:mi,error:gi,warning:hi},Pa=e=>{const{prefixCls:t,icon:n,type:a,message:o,description:l,actions:d,role:u="alert"}=e;let f=null;return n?f=s.createElement("span",{className:`${t}-icon`},n):a&&(f=s.createElement(xo[a]||null,{className:ee(`${t}-icon`,`${t}-icon-${a}`)})),s.createElement("div",{className:ee({[`${t}-with-icon`]:f}),role:u},f,s.createElement("div",{className:`${t}-message`},o),l&&s.createElement("div",{className:`${t}-description`},l),d&&s.createElement("div",{className:`${t}-actions`},d))},wo=e=>{const{prefixCls:t,className:n,icon:a,type:o,message:l,description:d,btn:u,actions:f,closable:S=!0,closeIcon:x,className:N}=e,E=yo(e,["prefixCls","className","icon","type","message","description","btn","actions","closable","closeIcon","className"]),{getPrefixCls:C}=s.useContext(rt),F=f??u,O=t||C("notification"),D=`${O}-notice`,A=At(O),[L,P,g]=Da(O,A);return L(s.createElement("div",{className:ee(`${D}-pure-panel`,P,n,g,A)},s.createElement(vo,{prefixCls:O}),s.createElement(ao,Object.assign({},E,{prefixCls:O,eventKey:"pure",duration:null,closable:S,className:ee({notificationClassName:N}),closeIcon:Rt(O,x),content:s.createElement(Pa,{prefixCls:D,icon:a,type:o,message:l,description:d,actions:F})}))))},Eo=wo;function Lo(e,t,n){let a;switch(e){case"top":a={left:"50%",transform:"translateX(-50%)",right:"auto",top:t,bottom:"auto"};break;case"topLeft":a={left:0,top:t,bottom:"auto"};break;case"topRight":a={right:0,top:t,bottom:"auto"};break;case"bottom":a={left:"50%",transform:"translateX(-50%)",right:"auto",top:"auto",bottom:n};break;case"bottomLeft":a={left:0,top:"auto",bottom:n};break;default:a={right:0,top:"auto",bottom:n};break}return a}function No(e){return{motionName:`${e}-fade`}}function Io(e,t,n){return typeof e<"u"?e:typeof(t==null?void 0:t.closeIcon)<"u"?t.closeIcon:n==null?void 0:n.closeIcon}var Co=globalThis&&globalThis.__rest||function(e,t){var n={};for(var a in e)Object.prototype.hasOwnProperty.call(e,a)&&t.indexOf(a)<0&&(n[a]=e[a]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var o=0,a=Object.getOwnPropertySymbols(e);o<a.length;o++)t.indexOf(a[o])<0&&Object.prototype.propertyIsEnumerable.call(e,a[o])&&(n[a[o]]=e[a[o]]);return n};const ya=24,Oo=4.5,$o="topRight",To=({children:e,prefixCls:t})=>{const n=At(t),[a,o,l]=Da(t,n);return a(te.createElement(oo,{classNames:{list:ee(o,l,n)}},e))},_o=(e,{prefixCls:t,key:n})=>te.createElement(To,{prefixCls:t,key:n},e),Ao=te.forwardRef((e,t)=>{const{top:n,bottom:a,prefixCls:o,getContainer:l,maxCount:d,rtl:u,onAllRemoved:f,stack:S,duration:x,pauseOnHover:N=!0,showProgress:E}=e,{getPrefixCls:C,getPopupContainer:F,notification:O,direction:D}=s.useContext(rt),[,A]=bi(),L=o||C("notification"),P=z=>Lo(z,n??ya,a??ya),g=()=>ee({[`${L}-rtl`]:u??D==="rtl"}),H=()=>No(L),[M,V]=io({prefixCls:L,style:P,className:g,motion:H,closable:!0,closeIcon:Rt(L),duration:x??Oo,getContainer:()=>(l==null?void 0:l())||(F==null?void 0:F())||document.body,maxCount:d,pauseOnHover:N,showProgress:E,onAllRemoved:f,renderNotifications:_o,stack:S===!1?!1:{threshold:typeof S=="object"?S==null?void 0:S.threshold:void 0,offset:8,gap:A.margin}});return te.useImperativeHandle(t,()=>Object.assign(Object.assign({},M),{prefixCls:L,notification:O})),V});function Ha(e){const t=te.useRef(null);return Si(),[te.useMemo(()=>{const a=u=>{var f;if(!t.current)return;const{open:S,prefixCls:x,notification:N}=t.current,E=`${x}-notice`,{message:C,description:F,icon:O,type:D,btn:A,actions:L,className:P,style:g,role:H="alert",closeIcon:M,closable:V}=u,z=Co(u,["message","description","icon","type","btn","actions","className","style","role","closeIcon","closable"]),K=L??A,J=Rt(E,Io(M,e,N));return S(Object.assign(Object.assign({placement:(f=e==null?void 0:e.placement)!==null&&f!==void 0?f:$o},z),{content:te.createElement(Pa,{prefixCls:E,icon:O,type:D,message:C,description:F,actions:K,role:H}),className:ee(D&&`${E}-${D}`,P,N==null?void 0:N.className),style:Object.assign(Object.assign({},N==null?void 0:N.style),g),closeIcon:J,closable:V??!!J}))},l={open:a,destroy:u=>{var f,S;u!==void 0?(f=t.current)===null||f===void 0||f.close(u):(S=t.current)===null||S===void 0||S.destroy()}};return["success","info","warning","error"].forEach(u=>{l[u]=f=>a(Object.assign(Object.assign({},f),{type:u}))}),l},[]),te.createElement(Ao,Object.assign({key:"notification-holder"},e,{ref:t}))]}function Ro(e){return Ha(e)}var Mo={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M890.5 755.3L537.9 269.2c-12.8-17.6-39-17.6-51.7 0L133.5 755.3A8 8 0 00140 768h75c5.1 0 9.9-2.5 12.9-6.6L512 369.8l284.1 391.6c3 4.1 7.8 6.6 12.9 6.6h75c6.5 0 10.3-7.4 6.5-12.7z"}}]},name:"up",theme:"outlined"};const ko=Mo;var Bo=function(t,n){return s.createElement(Je,Ne({},t,{ref:n,icon:ko}))},Fo=s.forwardRef(Bo);const Do=Fo;function _t(){return typeof BigInt=="function"}function Wa(e){return!e&&e!==0&&!Number.isNaN(e)||!String(e).trim()}function Ye(e){var t=e.trim(),n=t.startsWith("-");n&&(t=t.slice(1)),t=t.replace(/(\.\d*[^0])0*$/,"$1").replace(/\.0*$/,"").replace(/^0+/,""),t.startsWith(".")&&(t="0".concat(t));var a=t||"0",o=a.split("."),l=o[0]||"0",d=o[1]||"0";l==="0"&&d==="0"&&(n=!1);var u=n?"-":"";return{negative:n,negativeStr:u,trimStr:a,integerStr:l,decimalStr:d,fullStr:"".concat(u).concat(a)}}function Mt(e){var t=String(e);return!Number.isNaN(Number(t))&&t.includes("e")}function Ke(e){var t=String(e);if(Mt(e)){var n=Number(t.slice(t.indexOf("e-")+2)),a=t.match(/\.(\d+)/);return a!=null&&a[1]&&(n+=a[1].length),n}return t.includes(".")&&kt(t)?t.length-t.indexOf(".")-1:0}function Nt(e){var t=String(e);if(Mt(e)){if(e>Number.MAX_SAFE_INTEGER)return String(_t()?BigInt(e).toString():Number.MAX_SAFE_INTEGER);if(e<Number.MIN_SAFE_INTEGER)return String(_t()?BigInt(e).toString():Number.MIN_SAFE_INTEGER);t=e.toFixed(Ke(t))}return Ye(t).fullStr}function kt(e){return typeof e=="number"?!Number.isNaN(e):e?/^\s*-?\d+(\.\d+)?\s*$/.test(e)||/^\s*-?\d+\.\s*$/.test(e)||/^\s*-?\.\d+\s*$/.test(e):!1}var Po=function(){function e(t){if(Ca(this,e),ne(this,"origin",""),ne(this,"negative",void 0),ne(this,"integer",void 0),ne(this,"decimal",void 0),ne(this,"decimalLen",void 0),ne(this,"empty",void 0),ne(this,"nan",void 0),Wa(t)){this.empty=!0;return}if(this.origin=String(t),t==="-"||Number.isNaN(t)){this.nan=!0;return}var n=t;if(Mt(n)&&(n=Number(n)),n=typeof n=="string"?n:Nt(n),kt(n)){var a=Ye(n);this.negative=a.negative;var o=a.trimStr.split(".");this.integer=BigInt(o[0]);var l=o[1]||"0";this.decimal=BigInt(l),this.decimalLen=l.length}else this.nan=!0}return Ia(e,[{key:"getMark",value:function(){return this.negative?"-":""}},{key:"getIntegerStr",value:function(){return this.integer.toString()}},{key:"getDecimalStr",value:function(){return this.decimal.toString().padStart(this.decimalLen,"0")}},{key:"alignDecimal",value:function(n){var a="".concat(this.getMark()).concat(this.getIntegerStr()).concat(this.getDecimalStr().padEnd(n,"0"));return BigInt(a)}},{key:"negate",value:function(){var n=new e(this.toString());return n.negative=!n.negative,n}},{key:"cal",value:function(n,a,o){var l=Math.max(this.getDecimalStr().length,n.getDecimalStr().length),d=this.alignDecimal(l),u=n.alignDecimal(l),f=a(d,u).toString(),S=o(l),x=Ye(f),N=x.negativeStr,E=x.trimStr,C="".concat(N).concat(E.padStart(S+1,"0"));return new e("".concat(C.slice(0,-S),".").concat(C.slice(-S)))}},{key:"add",value:function(n){if(this.isInvalidate())return new e(n);var a=new e(n);return a.isInvalidate()?this:this.cal(a,function(o,l){return o+l},function(o){return o})}},{key:"multi",value:function(n){var a=new e(n);return this.isInvalidate()||a.isInvalidate()?new e(NaN):this.cal(a,function(o,l){return o*l},function(o){return o*2})}},{key:"isEmpty",value:function(){return this.empty}},{key:"isNaN",value:function(){return this.nan}},{key:"isInvalidate",value:function(){return this.isEmpty()||this.isNaN()}},{key:"equals",value:function(n){return this.toString()===(n==null?void 0:n.toString())}},{key:"lessEquals",value:function(n){return this.add(n.negate().toString()).toNumber()<=0}},{key:"toNumber",value:function(){return this.isNaN()?NaN:Number(this.toString())}},{key:"toString",value:function(){var n=arguments.length>0&&arguments[0]!==void 0?arguments[0]:!0;return n?this.isInvalidate()?"":Ye("".concat(this.getMark()).concat(this.getIntegerStr(),".").concat(this.getDecimalStr())).fullStr:this.origin}}]),e}(),Ho=function(){function e(t){if(Ca(this,e),ne(this,"origin",""),ne(this,"number",void 0),ne(this,"empty",void 0),Wa(t)){this.empty=!0;return}this.origin=String(t),this.number=Number(t)}return Ia(e,[{key:"negate",value:function(){return new e(-this.toNumber())}},{key:"add",value:function(n){if(this.isInvalidate())return new e(n);var a=Number(n);if(Number.isNaN(a))return this;var o=this.number+a;if(o>Number.MAX_SAFE_INTEGER)return new e(Number.MAX_SAFE_INTEGER);if(o<Number.MIN_SAFE_INTEGER)return new e(Number.MIN_SAFE_INTEGER);var l=Math.max(Ke(this.number),Ke(a));return new e(o.toFixed(l))}},{key:"multi",value:function(n){var a=Number(n);if(this.isInvalidate()||Number.isNaN(a))return new e(NaN);var o=this.number*a;if(o>Number.MAX_SAFE_INTEGER)return new e(Number.MAX_SAFE_INTEGER);if(o<Number.MIN_SAFE_INTEGER)return new e(Number.MIN_SAFE_INTEGER);var l=Math.max(Ke(this.number),Ke(a));return new e(o.toFixed(l))}},{key:"isEmpty",value:function(){return this.empty}},{key:"isNaN",value:function(){return Number.isNaN(this.number)}},{key:"isInvalidate",value:function(){return this.isEmpty()||this.isNaN()}},{key:"equals",value:function(n){return this.toNumber()===(n==null?void 0:n.toNumber())}},{key:"lessEquals",value:function(n){return this.add(n.negate().toString()).toNumber()<=0}},{key:"toNumber",value:function(){return this.number}},{key:"toString",value:function(){var n=arguments.length>0&&arguments[0]!==void 0?arguments[0]:!0;return n?this.isInvalidate()?"":Nt(this.number):this.origin}}]),e}();function Ee(e){return _t()?new Po(e):new Ho(e)}function yt(e,t,n){var a=arguments.length>3&&arguments[3]!==void 0?arguments[3]:!1;if(e==="")return"";var o=Ye(e),l=o.negativeStr,d=o.integerStr,u=o.decimalStr,f="".concat(t).concat(u),S="".concat(l).concat(d);if(n>=0){var x=Number(u[n]);if(x>=5&&!a){var N=Ee(e).add("".concat(l,"0.").concat("0".repeat(n)).concat(10-x));return yt(N.toString(),t,n,a)}return n===0?S:"".concat(S).concat(t).concat(u.padEnd(n,"0").slice(0,n))}return f===".0"?S:"".concat(S).concat(f)}function Wo(e,t){return typeof Proxy<"u"&&e?new Proxy(e,{get:function(a,o){if(t[o])return t[o];var l=a[o];return typeof l=="function"?l.bind(a):l}}):e}function jo(e,t){var n=s.useRef(null);function a(){try{var l=e.selectionStart,d=e.selectionEnd,u=e.value,f=u.substring(0,l),S=u.substring(d);n.current={start:l,end:d,value:u,beforeTxt:f,afterTxt:S}}catch{}}function o(){if(e&&n.current&&t)try{var l=e.value,d=n.current,u=d.beforeTxt,f=d.afterTxt,S=d.start,x=l.length;if(l.startsWith(u))x=u.length;else if(l.endsWith(f))x=l.length-n.current.afterTxt.length;else{var N=u[S-1],E=l.indexOf(N,S-1);E!==-1&&(x=E+1)}e.setSelectionRange(x,x)}catch(C){no(!1,"Something warning of cursor restore. Please fire issue about this: ".concat(C.message))}}return[a,o]}var Vo=function(){var t=s.useState(!1),n=dt(t,2),a=n[0],o=n[1];return vi(function(){o(yi())},[]),a},zo=200,qo=600;function Go(e){var t=e.prefixCls,n=e.upNode,a=e.downNode,o=e.upDisabled,l=e.downDisabled,d=e.onStep,u=s.useRef(),f=s.useRef([]),S=s.useRef();S.current=d;var x=function(){clearTimeout(u.current)},N=function(P,g){P.preventDefault(),x(),S.current(g);function H(){S.current(g),u.current=setTimeout(H,zo)}u.current=setTimeout(H,qo)};s.useEffect(function(){return function(){x(),f.current.forEach(function(L){return wt.cancel(L)})}},[]);var E=Vo();if(E)return null;var C="".concat(t,"-handler"),F=ee(C,"".concat(C,"-up"),ne({},"".concat(C,"-up-disabled"),o)),O=ee(C,"".concat(C,"-down"),ne({},"".concat(C,"-down-disabled"),l)),D=function(){return f.current.push(wt(x))},A={unselectable:"on",role:"button",onMouseUp:D,onMouseLeave:D};return s.createElement("div",{className:"".concat(C,"-wrap")},s.createElement("span",Ne({},A,{onMouseDown:function(P){N(P,!0)},"aria-label":"Increase Value","aria-disabled":o,className:F}),n||s.createElement("span",{unselectable:"on",className:"".concat(t,"-handler-up-inner")})),s.createElement("span",Ne({},A,{onMouseDown:function(P){N(P,!1)},"aria-label":"Decrease Value","aria-disabled":l,className:O}),a||s.createElement("span",{unselectable:"on",className:"".concat(t,"-handler-down-inner")})))}function xa(e){var t=typeof e=="number"?Nt(e):Ye(e).fullStr,n=t.includes(".");return n?Ye(t.replace(/(\d)\.(\d)/g,"$1$2.")).fullStr:e+"0"}const Uo=function(){var e=s.useRef(0),t=function(){wt.cancel(e.current)};return s.useEffect(function(){return t},[]),function(n){t(),e.current=wt(function(){n()})}};var Ko=["prefixCls","className","style","min","max","step","defaultValue","value","disabled","readOnly","upHandler","downHandler","keyboard","changeOnWheel","controls","classNames","stringMode","parser","formatter","precision","decimalSeparator","onChange","onInput","onPressEnter","onStep","changeOnBlur","domRef"],Xo=["disabled","style","prefixCls","value","prefix","suffix","addonBefore","addonAfter","className","classNames"],wa=function(t,n){return t||n.isEmpty()?n.toString():n.toNumber()},Ea=function(t){var n=Ee(t);return n.isInvalidate()?null:n},Yo=s.forwardRef(function(e,t){var n=e.prefixCls,a=e.className,o=e.style,l=e.min,d=e.max,u=e.step,f=u===void 0?1:u,S=e.defaultValue,x=e.value,N=e.disabled,E=e.readOnly,C=e.upHandler,F=e.downHandler,O=e.keyboard,D=e.changeOnWheel,A=D===void 0?!1:D,L=e.controls,P=L===void 0?!0:L;e.classNames;var g=e.stringMode,H=e.parser,M=e.formatter,V=e.precision,z=e.decimalSeparator,K=e.onChange,J=e.onInput,ae=e.onPressEnter,ue=e.onStep,le=e.changeOnBlur,ce=le===void 0?!0:le,Be=e.domRef,Fe=Ma(e,Ko),ie="".concat(n,"-input"),fe=s.useRef(null),me=s.useState(!1),oe=dt(me,2),be=oe[0],ve=oe[1],Q=s.useRef(!1),ge=s.useRef(!1),he=s.useRef(!1),Ce=s.useState(function(){return Ee(x??S)}),U=dt(Ce,2),W=U[0],se=U[1];function Qe(h){x===void 0&&se(h)}var Oe=s.useCallback(function(h,v){if(!v)return V>=0?V:Math.max(Ke(h),Ke(f))},[V,f]),Ae=s.useCallback(function(h){var v=String(h);if(H)return H(v);var R=v;return z&&(R=R.replace(z,".")),R.replace(/[^\w.-]+/g,"")},[H,z]),Re=s.useRef(""),De=s.useCallback(function(h,v){if(M)return M(h,{userTyping:v,input:String(Re.current)});var R=typeof h=="number"?Nt(h):h;if(!v){var T=Oe(R,v);if(kt(R)&&(z||T>=0)){var re=z||".";R=yt(R,re,T)}}return R},[M,Oe,z]),Se=s.useState(function(){var h=S??x;return W.isInvalidate()&&["string","number"].includes(Ka(h))?Number.isNaN(h)?"":h:De(W.toString(),!1)}),Pe=dt(Se,2),ye=Pe[0],He=Pe[1];Re.current=ye;function Ie(h,v){He(De(h.isInvalidate()?h.toString(!1):h.toString(!v),v))}var xe=s.useMemo(function(){return Ea(d)},[d,V]),we=s.useMemo(function(){return Ea(l)},[l,V]),We=s.useMemo(function(){return!xe||!W||W.isInvalidate()?!1:xe.lessEquals(W)},[xe,W]),je=s.useMemo(function(){return!we||!W||W.isInvalidate()?!1:W.lessEquals(we)},[we,W]),Ze=jo(fe.current,be),Ve=dt(Ze,2),et=Ve[0],tt=Ve[1],ze=function(v){return xe&&!v.lessEquals(xe)?xe:we&&!we.lessEquals(v)?we:null},Me=function(v){return!ze(v)},$e=function(v,R){var T=v,re=Me(T)||T.isEmpty();if(!T.isEmpty()&&!R&&(T=ze(T)||T,re=!0),!E&&!N&&re){var pe=T.toString(),Te=Oe(pe,R);return Te>=0&&(T=Ee(yt(pe,".",Te)),Me(T)||(T=Ee(yt(pe,".",Te,!0)))),T.equals(W)||(Qe(T),K==null||K(T.isEmpty()?null:wa(g,T)),x===void 0&&Ie(T,R)),T}return W},qe=Uo(),i=function h(v){if(et(),Re.current=v,He(v),!ge.current){var R=Ae(v),T=Ee(R);T.isNaN()||$e(T,!0)}J==null||J(v),qe(function(){var re=v;H||(re=v.replace(/。/g,".")),re!==v&&h(re)})},m=function(){ge.current=!0},b=function(){ge.current=!1,i(fe.current.value)},w=function(v){i(v.target.value)},I=function(v){var R;if(!(v&&We||!v&&je)){Q.current=!1;var T=Ee(he.current?xa(f):f);v||(T=T.negate());var re=(W||Ee(0)).add(T.toString()),pe=$e(re,!1);ue==null||ue(wa(g,pe),{offset:he.current?xa(f):f,type:v?"up":"down"}),(R=fe.current)===null||R===void 0||R.focus()}},y=function(v){var R=Ee(Ae(ye)),T;R.isNaN()?T=$e(W,v):T=$e(R,v),x!==void 0?Ie(W,!1):T.isNaN()||Ie(T,!1)},$=function(){Q.current=!0},j=function(v){var R=v.key,T=v.shiftKey;Q.current=!0,he.current=T,R==="Enter"&&(ge.current||(Q.current=!1),y(!1),ae==null||ae(v)),O!==!1&&!ge.current&&["Up","ArrowUp","Down","ArrowDown"].includes(R)&&(I(R==="Up"||R==="ArrowUp"),v.preventDefault())},k=function(){Q.current=!1,he.current=!1};s.useEffect(function(){if(A&&be){var h=function(T){I(T.deltaY<0),T.preventDefault()},v=fe.current;if(v)return v.addEventListener("wheel",h,{passive:!1}),function(){return v.removeEventListener("wheel",h)}}});var q=function(){ce&&y(!1),ve(!1),Q.current=!1};return Ot(function(){W.isInvalidate()||Ie(W,!1)},[V,M]),Ot(function(){var h=Ee(x);se(h);var v=Ee(Ae(ye));(!h.equals(v)||!Q.current||M)&&Ie(h,Q.current)},[x]),Ot(function(){M&&tt()},[ye]),s.createElement("div",{ref:Be,className:ee(n,a,ne(ne(ne(ne(ne({},"".concat(n,"-focused"),be),"".concat(n,"-disabled"),N),"".concat(n,"-readonly"),E),"".concat(n,"-not-a-number"),W.isNaN()),"".concat(n,"-out-of-range"),!W.isInvalidate()&&!Me(W))),style:o,onFocus:function(){ve(!0)},onBlur:q,onKeyDown:j,onKeyUp:k,onCompositionStart:m,onCompositionEnd:b,onBeforeInput:$},P&&s.createElement(Go,{prefixCls:n,upNode:C,downNode:F,upDisabled:We,downDisabled:je,onStep:I}),s.createElement("div",{className:"".concat(ie,"-wrap")},s.createElement("input",Ne({autoComplete:"off",role:"spinbutton","aria-valuemin":l,"aria-valuemax":d,"aria-valuenow":W.isInvalidate()?null:W.toString(),step:f},Fe,{ref:wi(fe,t),className:ie,value:ye,onChange:w,disabled:N,readOnly:E}))))}),Jo=s.forwardRef(function(e,t){var n=e.disabled,a=e.style,o=e.prefixCls,l=o===void 0?"rc-input-number":o,d=e.value,u=e.prefix,f=e.suffix,S=e.addonBefore,x=e.addonAfter,N=e.className,E=e.classNames,C=Ma(e,Xo),F=s.useRef(null),O=s.useRef(null),D=s.useRef(null),A=function(P){D.current&&Ei(D.current,P)};return s.useImperativeHandle(t,function(){return Wo(D.current,{focus:A,nativeElement:F.current.nativeElement||O.current})}),s.createElement(xi,{className:N,triggerFocus:A,prefixCls:l,value:d,disabled:n,style:a,prefix:u,suffix:f,addonAfter:x,addonBefore:S,classNames:E,components:{affixWrapper:"div",groupWrapper:"div",wrapper:"div",groupAddon:"div"},ref:F},s.createElement(Yo,Ne({prefixCls:l,disabled:n,ref:D,domRef:O,className:E==null?void 0:E.input},C)))});const Qo=e=>{var t;const n=(t=e.handleVisible)!==null&&t!==void 0?t:"auto",a=e.controlHeightSM-e.lineWidth*2;return Object.assign(Object.assign({},Li(e)),{controlWidth:90,handleWidth:a,handleFontSize:e.fontSize/2,handleVisible:n,handleActiveBg:e.colorFillAlter,handleBg:e.colorBgContainer,filledHandleBg:new ro(e.colorFillSecondary).onBackground(e.colorBgContainer).toHexString(),handleHoverColor:e.colorPrimary,handleBorderColor:e.colorBorder,handleOpacity:n===!0?1:0,handleVisibleWidth:n===!0?a:0})},La=({componentCls:e,borderRadiusSM:t,borderRadiusLG:n},a)=>{const o=a==="lg"?n:t;return{[`&-${a}`]:{[`${e}-handler-wrap`]:{borderStartEndRadius:o,borderEndEndRadius:o},[`${e}-handler-up`]:{borderStartEndRadius:o},[`${e}-handler-down`]:{borderEndEndRadius:o}}}},Zo=e=>{const{componentCls:t,lineWidth:n,lineType:a,borderRadius:o,inputFontSizeSM:l,inputFontSizeLG:d,controlHeightLG:u,controlHeightSM:f,colorError:S,paddingInlineSM:x,paddingBlockSM:N,paddingBlockLG:E,paddingInlineLG:C,colorIcon:F,motionDurationMid:O,handleHoverColor:D,handleOpacity:A,paddingInline:L,paddingBlock:P,handleBg:g,handleActiveBg:H,colorTextDisabled:M,borderRadiusSM:V,borderRadiusLG:z,controlWidth:K,handleBorderColor:J,filledHandleBg:ae,lineHeightLG:ue,calc:le}=e;return[{[t]:Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({},bt(e)),Ta(e)),{display:"inline-block",width:K,margin:0,padding:0,borderRadius:o}),Ci(e,{[`${t}-handler-wrap`]:{background:g,[`${t}-handler-down`]:{borderBlockStart:`${Z(n)} ${a} ${J}`}}})),Oi(e,{[`${t}-handler-wrap`]:{background:ae,[`${t}-handler-down`]:{borderBlockStart:`${Z(n)} ${a} ${J}`}},"&:focus-within":{[`${t}-handler-wrap`]:{background:g}}})),$i(e,{[`${t}-handler-wrap`]:{background:g,[`${t}-handler-down`]:{borderBlockStart:`${Z(n)} ${a} ${J}`}}})),Ti(e)),{"&-rtl":{direction:"rtl",[`${t}-input`]:{direction:"rtl"}},"&-lg":{padding:0,fontSize:d,lineHeight:ue,borderRadius:z,[`input${t}-input`]:{height:le(u).sub(le(n).mul(2)).equal(),padding:`${Z(E)} ${Z(C)}`}},"&-sm":{padding:0,fontSize:l,borderRadius:V,[`input${t}-input`]:{height:le(f).sub(le(n).mul(2)).equal(),padding:`${Z(N)} ${Z(x)}`}},"&-out-of-range":{[`${t}-input-wrap`]:{input:{color:S}}},"&-group":Object.assign(Object.assign(Object.assign({},bt(e)),_i(e)),{"&-wrapper":Object.assign(Object.assign(Object.assign({display:"inline-block",textAlign:"start",verticalAlign:"top",[`${t}-affix-wrapper`]:{width:"100%"},"&-lg":{[`${t}-group-addon`]:{borderRadius:z,fontSize:e.fontSizeLG}},"&-sm":{[`${t}-group-addon`]:{borderRadius:V}}},Ai(e)),Ri(e)),{[`&:not(${t}-compact-first-item):not(${t}-compact-last-item)${t}-compact-item`]:{[`${t}, ${t}-group-addon`]:{borderRadius:0}},[`&:not(${t}-compact-last-item)${t}-compact-first-item`]:{[`${t}, ${t}-group-addon`]:{borderStartEndRadius:0,borderEndEndRadius:0}},[`&:not(${t}-compact-first-item)${t}-compact-last-item`]:{[`${t}, ${t}-group-addon`]:{borderStartStartRadius:0,borderEndStartRadius:0}}})}),[`&-disabled ${t}-input`]:{cursor:"not-allowed"},[t]:{"&-input":Object.assign(Object.assign(Object.assign(Object.assign({},bt(e)),{width:"100%",padding:`${Z(P)} ${Z(L)}`,textAlign:"start",backgroundColor:"transparent",border:0,borderRadius:o,outline:0,transition:`all ${O} linear`,appearance:"textfield",fontSize:"inherit"}),Mi(e.colorTextPlaceholder)),{'&[type="number"]::-webkit-inner-spin-button, &[type="number"]::-webkit-outer-spin-button':{margin:0,appearance:"none"}})},[`&:hover ${t}-handler-wrap, &-focused ${t}-handler-wrap`]:{width:e.handleWidth,opacity:1}})},{[t]:Object.assign(Object.assign(Object.assign({[`${t}-handler-wrap`]:{position:"absolute",insetBlockStart:0,insetInlineEnd:0,width:e.handleVisibleWidth,opacity:A,height:"100%",borderStartStartRadius:0,borderStartEndRadius:o,borderEndEndRadius:o,borderEndStartRadius:0,display:"flex",flexDirection:"column",alignItems:"stretch",transition:`all ${O}`,overflow:"hidden",[`${t}-handler`]:{display:"flex",alignItems:"center",justifyContent:"center",flex:"auto",height:"40%",[`
              ${t}-handler-up-inner,
              ${t}-handler-down-inner
            `]:{marginInlineEnd:0,fontSize:e.handleFontSize}}},[`${t}-handler`]:{height:"50%",overflow:"hidden",color:F,fontWeight:"bold",lineHeight:0,textAlign:"center",cursor:"pointer",borderInlineStart:`${Z(n)} ${a} ${J}`,transition:`all ${O} linear`,"&:active":{background:H},"&:hover":{height:"60%",[`
              ${t}-handler-up-inner,
              ${t}-handler-down-inner
            `]:{color:D}},"&-up-inner, &-down-inner":Object.assign(Object.assign({},ki()),{color:F,transition:`all ${O} linear`,userSelect:"none"})},[`${t}-handler-up`]:{borderStartEndRadius:o},[`${t}-handler-down`]:{borderEndEndRadius:o}},La(e,"lg")),La(e,"sm")),{"&-disabled, &-readonly":{[`${t}-handler-wrap`]:{display:"none"},[`${t}-input`]:{color:"inherit"}},[`
          ${t}-handler-up-disabled,
          ${t}-handler-down-disabled
        `]:{cursor:"not-allowed"},[`
          ${t}-handler-up-disabled:hover &-handler-up-inner,
          ${t}-handler-down-disabled:hover &-handler-down-inner
        `]:{color:M}})}]},es=e=>{const{componentCls:t,paddingBlock:n,paddingInline:a,inputAffixPadding:o,controlWidth:l,borderRadiusLG:d,borderRadiusSM:u,paddingInlineLG:f,paddingInlineSM:S,paddingBlockLG:x,paddingBlockSM:N,motionDurationMid:E}=e;return{[`${t}-affix-wrapper`]:Object.assign(Object.assign({[`input${t}-input`]:{padding:`${Z(n)} 0`}},Ta(e)),{position:"relative",display:"inline-flex",alignItems:"center",width:l,padding:0,paddingInlineStart:a,"&-lg":{borderRadius:d,paddingInlineStart:f,[`input${t}-input`]:{padding:`${Z(x)} 0`}},"&-sm":{borderRadius:u,paddingInlineStart:S,[`input${t}-input`]:{padding:`${Z(N)} 0`}},[`&:not(${t}-disabled):hover`]:{zIndex:1},"&-focused, &:focus":{zIndex:1},[`&-disabled > ${t}-disabled`]:{background:"transparent"},[`> div${t}`]:{width:"100%",border:"none",outline:"none",[`&${t}-focused`]:{boxShadow:"none !important"}},"&::before":{display:"inline-block",width:0,visibility:"hidden",content:'"\\a0"'},[`${t}-handler-wrap`]:{zIndex:2},[t]:{position:"static",color:"inherit","&-prefix, &-suffix":{display:"flex",flex:"none",alignItems:"center",pointerEvents:"none"},"&-prefix":{marginInlineEnd:o},"&-suffix":{insetBlockStart:0,insetInlineEnd:0,height:"100%",marginInlineEnd:a,marginInlineStart:o,transition:`margin ${E}`}},[`&:hover ${t}-handler-wrap, &-focused ${t}-handler-wrap`]:{width:e.handleWidth,opacity:1},[`&:not(${t}-affix-wrapper-without-controls):hover ${t}-suffix`]:{marginInlineEnd:e.calc(e.handleWidth).add(a).equal()}}),[`${t}-underlined`]:{borderRadius:0}}},ts=Oa("InputNumber",e=>{const t=$a(e,Ni(e));return[Zo(t),es(t),Ii(t)]},Qo,{unitless:{handleOpacity:!0},resetFont:!1});var ns=globalThis&&globalThis.__rest||function(e,t){var n={};for(var a in e)Object.prototype.hasOwnProperty.call(e,a)&&t.indexOf(a)<0&&(n[a]=e[a]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var o=0,a=Object.getOwnPropertySymbols(e);o<a.length;o++)t.indexOf(a[o])<0&&Object.prototype.propertyIsEnumerable.call(e,a[o])&&(n[a[o]]=e[a[o]]);return n};const ja=s.forwardRef((e,t)=>{const{getPrefixCls:n,direction:a}=s.useContext(rt),o=s.useRef(null);s.useImperativeHandle(t,()=>o.current);const{className:l,rootClassName:d,size:u,disabled:f,prefixCls:S,addonBefore:x,addonAfter:N,prefix:E,suffix:C,bordered:F,readOnly:O,status:D,controls:A,variant:L}=e,P=ns(e,["className","rootClassName","size","disabled","prefixCls","addonBefore","addonAfter","prefix","suffix","bordered","readOnly","status","controls","variant"]),g=n("input-number",S),H=At(g),[M,V,z]=ts(g,H),{compactSize:K,compactItemClassnames:J}=Bi(g,a);let ae=s.createElement(Do,{className:`${g}-handler-up-inner`}),ue=s.createElement(Wi,{className:`${g}-handler-down-inner`});const le=typeof A=="boolean"?A:void 0;typeof A=="object"&&(ae=typeof A.upIcon>"u"?ae:s.createElement("span",{className:`${g}-handler-up-inner`},A.upIcon),ue=typeof A.downIcon>"u"?ue:s.createElement("span",{className:`${g}-handler-down-inner`},A.downIcon));const{hasFeedback:ce,status:Be,isFormItemInput:Fe,feedbackIcon:ie}=s.useContext(Fi),fe=ji(Be,D),me=Di(W=>{var se;return(se=u??K)!==null&&se!==void 0?se:W}),oe=s.useContext(Pi),be=f??oe,[ve,Q]=Hi("inputNumber",L,F),ge=ce&&s.createElement(s.Fragment,null,ie),he=ee({[`${g}-lg`]:me==="large",[`${g}-sm`]:me==="small",[`${g}-rtl`]:a==="rtl",[`${g}-in-form-item`]:Fe},V),Ce=`${g}-group`,U=s.createElement(Jo,Object.assign({ref:o,disabled:be,className:ee(z,H,l,d,J),upHandler:ae,downHandler:ue,prefixCls:g,readOnly:O,controls:le,prefix:E,suffix:ge||C,addonBefore:x&&s.createElement(ha,{form:!0,space:!0},x),addonAfter:N&&s.createElement(ha,{form:!0,space:!0},N),classNames:{input:he,variant:ee({[`${g}-${ve}`]:Q},Sa(g,fe,ce)),affixWrapper:ee({[`${g}-affix-wrapper-sm`]:me==="small",[`${g}-affix-wrapper-lg`]:me==="large",[`${g}-affix-wrapper-rtl`]:a==="rtl",[`${g}-affix-wrapper-without-controls`]:A===!1||be||O},V),wrapper:ee({[`${Ce}-rtl`]:a==="rtl"},V),groupWrapper:ee({[`${g}-group-wrapper-sm`]:me==="small",[`${g}-group-wrapper-lg`]:me==="large",[`${g}-group-wrapper-rtl`]:a==="rtl",[`${g}-group-wrapper-${ve}`]:Q},Sa(`${g}-group-wrapper`,fe,ce),V)}},P));return M(U)}),Va=ja,rs=e=>s.createElement(_a,{theme:{components:{InputNumber:{handleVisible:!0}}}},s.createElement(ja,Object.assign({},e)));Va._InternalPanelDoNotUseOrYouWillBeFired=rs;const nt=Va;function as(e,t,n){return typeof n=="boolean"?n:e.length?!0:Vi(t).some(o=>o.type===Aa)}var za=globalThis&&globalThis.__rest||function(e,t){var n={};for(var a in e)Object.prototype.hasOwnProperty.call(e,a)&&t.indexOf(a)<0&&(n[a]=e[a]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var o=0,a=Object.getOwnPropertySymbols(e);o<a.length;o++)t.indexOf(a[o])<0&&Object.prototype.propertyIsEnumerable.call(e,a[o])&&(n[a[o]]=e[a[o]]);return n};function It({suffixCls:e,tagName:t,displayName:n}){return a=>s.forwardRef((l,d)=>s.createElement(a,Object.assign({ref:d,suffixCls:e,tagName:t},l)))}const Bt=s.forwardRef((e,t)=>{const{prefixCls:n,suffixCls:a,className:o,tagName:l}=e,d=za(e,["prefixCls","suffixCls","className","tagName"]),{getPrefixCls:u}=s.useContext(rt),f=u("layout",n),[S,x,N]=Ra(f),E=a?`${f}-${a}`:f;return S(s.createElement(l,Object.assign({className:ee(n||E,o,x,N),ref:t},d)))}),is=s.forwardRef((e,t)=>{const{direction:n}=s.useContext(rt),[a,o]=s.useState([]),{prefixCls:l,className:d,rootClassName:u,children:f,hasSider:S,tagName:x,style:N}=e,E=za(e,["prefixCls","className","rootClassName","children","hasSider","tagName","style"]),C=zi(E,["suffixCls"]),{getPrefixCls:F,className:O,style:D}=qi("layout"),A=F("layout",l),L=as(a,f,S),[P,g,H]=Ra(A),M=ee(A,{[`${A}-has-sider`]:L,[`${A}-rtl`]:n==="rtl"},O,d,u,g,H),V=s.useMemo(()=>({siderHook:{addSider:z=>{o(K=>[].concat(Xa(K),[z]))},removeSider:z=>{o(K=>K.filter(J=>J!==z))}}}),[]);return P(s.createElement(Ji.Provider,{value:V},s.createElement(x,Object.assign({ref:t,className:M,style:Object.assign(Object.assign({},D),N)},C),f)))}),os=It({tagName:"div",displayName:"Layout"})(is),ss=It({suffixCls:"header",tagName:"header",displayName:"Header"})(Bt),ls=It({suffixCls:"footer",tagName:"footer",displayName:"Footer"})(Bt),cs=It({suffixCls:"content",tagName:"main",displayName:"Content"})(Bt),ds=os,at=ds;at.Header=ss;at.Footer=ls;at.Content=cs;at.Sider=Aa;at._InternalSiderContext=Qi;const qa=at;let Le=null,xt=e=>e(),Et=[],pt={};function Na(){const{getContainer:e,rtl:t,maxCount:n,top:a,bottom:o,showProgress:l,pauseOnHover:d}=pt,u=(e==null?void 0:e())||document.body;return{getContainer:()=>u,rtl:t,maxCount:n,top:a,bottom:o,showProgress:l,pauseOnHover:d}}const us=te.forwardRef((e,t)=>{const{notificationConfig:n,sync:a}=e,{getPrefixCls:o}=s.useContext(rt),l=pt.prefixCls||o("notification"),d=s.useContext(so),[u,f]=Ha(Object.assign(Object.assign(Object.assign({},n),{prefixCls:l}),d.notification));return te.useEffect(a,[]),te.useImperativeHandle(t,()=>{const S=Object.assign({},u);return Object.keys(S).forEach(x=>{S[x]=(...N)=>(a(),u[x].apply(u,N))}),{instance:S,sync:a}}),f}),fs=te.forwardRef((e,t)=>{const[n,a]=te.useState(Na),o=()=>{a(Na)};te.useEffect(o,[]);const l=Ui(),d=l.getRootPrefixCls(),u=l.getIconPrefixCls(),f=l.getTheme(),S=te.createElement(us,{ref:t,sync:o,notificationConfig:n});return te.createElement(_a,{prefixCls:d,iconPrefixCls:u,theme:f},l.holderRender?l.holderRender(S):S)}),Ft=()=>{if(!Le){const e=document.createDocumentFragment(),t={fragment:e};Le=t,xt(()=>{Gi()(te.createElement(fs,{ref:a=>{const{instance:o,sync:l}=a||{};Promise.resolve().then(()=>{!t.instance&&o&&(t.instance=o,t.sync=l,Ft())})}}),e)});return}Le.instance&&(Et.forEach(e=>{switch(e.type){case"open":{xt(()=>{Le.instance.open(Object.assign(Object.assign({},pt),e.config))});break}case"destroy":xt(()=>{var t;(t=Le==null?void 0:Le.instance)===null||t===void 0||t.destroy(e.key)});break}}),Et=[])};function ps(e){pt=Object.assign(Object.assign({},pt),e),xt(()=>{var t;(t=Le==null?void 0:Le.sync)===null||t===void 0||t.call(Le)})}function Ga(e){Et.push({type:"open",config:e}),Ft()}const ms=e=>{Et.push({type:"destroy",key:e}),Ft()},gs=["success","info","warning","error"],hs={open:Ga,destroy:ms,config:ps,useNotification:Ro,_InternalPanelDoNotUseOrYouWillBeFired:Eo},Ua=hs;gs.forEach(e=>{Ua[e]=t=>Ga(Object.assign(Object.assign({},t),{type:e}))});const st=Ua;var Ss={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M869 487.8L491.2 159.9c-2.9-2.5-6.6-3.9-10.5-3.9h-88.5c-7.4 0-10.8 9.2-5.2 14l350.2 304H152c-4.4 0-8 3.6-8 8v60c0 4.4 3.6 8 8 8h585.1L386.9 854c-5.6 4.9-2.2 14 5.2 14h91.5c1.9 0 3.8-.7 5.2-2L869 536.2a32.07 32.07 0 000-48.4z"}}]},name:"arrow-right",theme:"outlined"};const bs=Ss;var vs=function(t,n){return s.createElement(Je,Ne({},t,{ref:n,icon:bs}))},ys=s.forwardRef(vs);const xs=ys;var ws={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M699 353h-46.9c-10.2 0-19.9 4.9-25.9 13.3L469 584.3l-71.2-98.8c-6-8.3-15.6-13.3-25.9-13.3H325c-6.5 0-10.3 7.4-6.5 12.7l124.6 172.8a31.8 31.8 0 0051.7 0l210.6-292c3.9-5.3.1-12.7-6.4-12.7z"}},{tag:"path",attrs:{d:"M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm0 820c-205.4 0-372-166.6-372-372s166.6-372 372-372 372 166.6 372 372-166.6 372-372 372z"}}]},name:"check-circle",theme:"outlined"};const Es=ws;var Ls=function(t,n){return s.createElement(Je,Ne({},t,{ref:n,icon:Es}))},Ns=s.forwardRef(Ls);const $t=Ns;var Is={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M909.3 506.3L781.7 405.6a7.23 7.23 0 00-11.7 5.7V476H548V254h64.8c6 0 9.4-7 5.7-11.7L517.7 114.7a7.14 7.14 0 00-11.3 0L405.6 242.3a7.23 7.23 0 005.7 11.7H476v222H254v-64.8c0-6-7-9.4-11.7-5.7L114.7 506.3a7.14 7.14 0 000 11.3l127.5 100.8c4.7 3.7 11.7.4 11.7-5.7V548h222v222h-64.8c-6 0-9.4 7-5.7 11.7l100.8 127.5c2.9 3.7 8.5 3.7 11.3 0l100.8-127.5c3.7-4.7.4-11.7-5.7-11.7H548V548h222v64.8c0 6 7 9.4 11.7 5.7l127.5-100.8a7.3 7.3 0 00.1-11.4z"}}]},name:"drag",theme:"outlined"};const Cs=Is;var Os=function(t,n){return s.createElement(Je,Ne({},t,{ref:n,icon:Cs}))},$s=s.forwardRef(Os);const Ts=$s;var _s={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M408 442h480c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8H408c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8zm-8 204c0 4.4 3.6 8 8 8h480c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8H408c-4.4 0-8 3.6-8 8v56zm504-486H120c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h784c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8zm0 632H120c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h784c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8zM115.4 518.9L271.7 642c5.8 4.6 14.4.5 14.4-6.9V388.9c0-7.4-8.5-11.5-14.4-6.9L115.4 505.1a8.74 8.74 0 000 13.8z"}}]},name:"menu-fold",theme:"outlined"};const As=_s;var Rs=function(t,n){return s.createElement(Je,Ne({},t,{ref:n,icon:As}))},Ms=s.forwardRef(Rs);const ks=Ms;var Bs={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M408 442h480c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8H408c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8zm-8 204c0 4.4 3.6 8 8 8h480c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8H408c-4.4 0-8 3.6-8 8v56zm504-486H120c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h784c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8zm0 632H120c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h784c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8zM142.4 642.1L298.7 519a8.84 8.84 0 000-13.9L142.4 381.9c-5.8-4.6-14.4-.5-14.4 6.9v246.3a8.9 8.9 0 0014.4 7z"}}]},name:"menu-unfold",theme:"outlined"};const Fs=Bs;var Ds=function(t,n){return s.createElement(Je,Ne({},t,{ref:n,icon:Fs}))},Ps=s.forwardRef(Ds);const Hs=Ps;var Ws={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M893.3 293.3L730.7 130.7c-7.5-7.5-16.7-13-26.7-16V112H144c-17.7 0-32 14.3-32 32v736c0 17.7 14.3 32 32 32h736c17.7 0 32-14.3 32-32V338.5c0-17-6.7-33.2-18.7-45.2zM384 184h256v104H384V184zm456 656H184V184h136v136c0 17.7 14.3 32 32 32h320c17.7 0 32-14.3 32-32V205.8l136 136V840zM512 442c-79.5 0-144 64.5-144 144s64.5 144 144 144 144-64.5 144-144-64.5-144-144-144zm0 224c-44.2 0-80-35.8-80-80s35.8-80 80-80 80 35.8 80 80-35.8 80-80 80z"}}]},name:"save",theme:"outlined"};const js=Ws;var Vs=function(t,n){return s.createElement(Je,Ne({},t,{ref:n,icon:js}))},zs=s.forwardRef(Vs);const qs=zs,Gs=({children:e,handleSubmitWorkflow:t,blurLoading:n,loaderMessage:a,openChangeLog:o,handleChangeLogClose:l,changeLogData:d,mode:u})=>{const f=mt();return p(ft,{children:[e,r(ei,{children:p(Ya,{sx:{position:"fixed",bottom:0,left:0,right:0},elevation:2,children:[r(Ja,{className:"container_BottomNav",showLabels:!0,sx:{display:"flex",justifyContent:"space-between",alignItems:"center",gap:1,width:"100%"},children:r(Qa,{sx:{display:"flex",gap:1},children:r(ft,{children:u!=="view"&&r(de,{type:"primary",style:{backgroundColor:f.palette.primary.main,borderRadius:"5px"},onClick:t,children:"Submit"})})})}),n&&r(Za,{blurLoading:n,loaderMessage:a}),o&&r(ci,{open:o,onClose:l,data:d})]})})]})},{Title:Us,Text:Ge}=Lt,Ks=({mode:e,navigate:t,headerData:n,module:a,fetchChangeLogDetails:o})=>{var d,u,f,S,x,N,E,C,F,O;const l=mt();return p(ft,{children:[p("div",{style:{marginBottom:32},children:[p("div",{style:{display:"flex",alignItems:"center",justifyContent:"space-between",width:"100%"},children:[p("div",{style:{display:"flex",alignItems:"center",gap:8},children:[r(ga,{onClick:()=>t(-1),color:"primary","aria-label":"go back",title:"Back",sx:{padding:0},children:r(ti,{sx:{fontSize:"25px",color:"#000000"}})}),p(Us,{level:3,style:{marginBottom:2,fontWeight:700},children:[e===((u=(d=c)==null?void 0:d.MODES)==null?void 0:u.CREATE)?(S=(f=c)==null?void 0:f.TITLE_MODES)==null?void 0:S.CREATE:e===((N=(x=c)==null?void 0:x.MODES)==null?void 0:N.EDIT)?(C=(E=c)==null?void 0:E.TITLE_MODES)==null?void 0:C.EDIT:(O=(F=c)==null?void 0:F.TITLE_MODES)==null?void 0:O.VIEW," - ",a]})]}),r(Xe,{title:"View Changelog History",placement:"left",children:r(ga,{onClick:o,color:"primary","aria-label":"view changelog",sx:{padding:"8px",border:`2px solid ${l.palette.primary.main}`,borderRadius:"10px","&:hover":{backgroundColor:"rgba(24, 144, 255, 0.1)"}},children:r(Yi,{style:{fontSize:"24px",color:l.palette.primary.main}})})})]}),r(Ge,{type:"secondary",children:"Design your workflows with simplicity"})]}),r(ut,{title:r("div",{style:{display:"flex",alignItems:"center",gap:8},children:r("span",{style:{fontSize:"17px",fontWeight:600,color:l.palette.primary.main},children:"Workflow Information"})}),size:"small",style:{marginTop:-20,marginBottom:24,borderRadius:"8px",backgroundColor:"#fafafa",border:`1px solid ${l.palette.primary.main}60`,boxShadow:"0 1px 4px rgba(0,0,0,0.1)"},bodyStyle:{padding:"16px 24px"},children:p("div",{style:{fontSize:"12px",lineHeight:"20px"},children:[p(Y,{gutter:[24,8],children:[r(_,{span:8,children:p(Y,{gutter:[8,4],children:[r(_,{span:10,children:r(Ge,{strong:!0,children:"Workflow ID:"})}),r(_,{span:14,children:(n==null?void 0:n.workflowId)||"N/A"})]})}),r(_,{span:8,children:p(Y,{gutter:[8,4],children:[r(_,{span:10,children:r(Ge,{strong:!0,children:"Workflow Name:"})}),r(_,{span:14,children:(n==null?void 0:n.workflowName)||"N/A"})]})}),r(_,{span:8,children:p(Y,{gutter:[8,4],children:[r(_,{span:10,children:r(Ge,{strong:!0,children:"Region:"})}),r(_,{span:14,children:(n==null?void 0:n.region)||"N/A"})]})})]}),p(Y,{gutter:[24,8],style:{marginTop:8},children:[r(_,{span:8,children:p(Y,{gutter:[8,4],children:[r(_,{span:10,children:r(Ge,{strong:!0,children:"Scenario:"})}),r(_,{span:14,children:(n==null?void 0:n.scenario)||"N/A"})]})}),r(_,{span:8,children:p(Y,{gutter:[8,4],children:[r(_,{span:10,children:r(Ge,{strong:!0,children:"Template:"})}),r(_,{span:14,children:(n==null?void 0:n.template)||"N/A"})]})}),r(_,{span:8,children:p(Y,{gutter:[8,4],children:[r(_,{span:10,children:r(Ge,{strong:!0,children:"Bifurcation Group:"})}),r(_,{span:14,children:(n==null?void 0:n.bifurcationGroup)||"N/A"})]})})]})]})})]})},{Text:Xs}=Lt,Ys=({workflowData:e,selectedCard:t,handleCardClick:n,mode:a,handleEditTask:o,setCardToMove:l,setIsMoveModalVisible:d,handleDeleteTask:u,getSortedLevels:f})=>{const S=E=>{const C=["linear-gradient(135deg, #ffebee 0%, #ffcdd2 100%)","linear-gradient(135deg, #e8f5e8 0%, #c8e6c9 100%)","linear-gradient(135deg, #fff3e0 0%, #ffe0b2 100%)","linear-gradient(135deg, #f3e5f5 0%, #e1bee7 100%)","linear-gradient(135deg, #e0f2f1 0%, #b2dfdb 100%)"];if(E===-1||E==="-1")return"linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%)";const O=parseInt(E,10)%C.length;return C[O]||"linear-gradient(135deg, #f5f5f5 0%, #e0e0e0 100%)"},x=f(),N=mt();return p("div",{style:{width:"100%",minHeight:"400px",maxHeight:"80vh",height:"auto",background:"white",borderRadius:"10px",boxShadow:"0 1px 4px rgba(0,0,0,0.1)",border:`1px solid ${N.palette.primary.main}60`,padding:"15px",position:"relative",display:"flex",flexDirection:"column"},children:[p("div",{style:{flexShrink:0},children:[r("div",{style:{fontSize:"17px",fontWeight:"600",color:N.palette.primary.main,textAlign:"left"},children:"Workflow Design"}),r(Zi,{style:{opacity:1.5,marginTop:"10px",marginBottom:"10px"}})]}),r("div",{style:{flex:1,display:"flex",alignItems:"flex-start",justifyContent:"flex-start",gap:"16px",overflowX:"auto",overflowY:"auto",padding:"10px 0",scrollbarWidth:"thin",scrollbarColor:"#d1d5db #f3f4f6"},children:x==null?void 0:x.map((E,C)=>{var F,O,D,A;return p(te.Fragment,{children:[p("div",{style:{display:"flex",flexDirection:"column",alignItems:"center",minWidth:"200px",flexShrink:0,height:"fit-content"},children:[p("div",{style:{marginBottom:10,textAlign:"center"},children:[r(Tt,{color:E===-1?"blue":E===0?"red":"green",style:{fontSize:"10px",padding:"2px 8px",fontWeight:"bold"},children:E===-1?"REQUESTOR":E===0?"FINAL":`LEVEL ${E}`}),r("div",{style:{fontSize:"13px",color:"#666",marginTop:2,fontWeight:"500",marginLeft:"-7.5px"},children:(F=e[E])==null?void 0:F.levelName}),p("div",{style:{fontSize:"11px",color:"#666",marginTop:2,fontWeight:"500",marginLeft:"-7.5px"},children:["No. of Tasks: ",((D=(O=e[E])==null?void 0:O.tasks)==null?void 0:D.length)||0]})]}),r("div",{style:{display:"flex",flexDirection:"column",gap:"10px",width:"100%",flex:1,overflowY:"visible",overflowX:"hidden",paddingLeft:"4px",paddingTop:"10px",paddingBottom:"10px",marginTop:"5px",minHeight:"fit-content"},children:(A=e[E])==null?void 0:A.tasks.map(L=>{const P=(t==null?void 0:t.id)===L.id;return p(ut,{size:"small",hoverable:!0,onClick:()=>n(L),style:{width:"100%",minHeight:"100px",cursor:"pointer",border:P?"2px solid #1890ff":"none",background:S(E),borderRadius:"12px",transition:"all 0.2s ease",position:"relative",boxShadow:P?"0 4px 10px rgba(24, 144, 255, 0.3), 0 2px 2px rgba(0,0,0,0.1)":"0 2px 5px rgba(0,0,0,0.1), 0 2px 2px rgba(0,0,0,0.05)",transform:P?"translateY(-1px)":"translateY(0)",marginBottom:"20px"},bodyStyle:{padding:"10px",background:"transparent"},onMouseEnter:g=>{if(!L.isFixed){const H=g.currentTarget.querySelector(".hover-buttons");H&&(H.style.display="flex")}g.currentTarget.style.transform="translateY(-2px)"},onMouseLeave:g=>{const H=g.currentTarget.querySelector(".hover-buttons");H&&(H.style.display="none"),P||(g.currentTarget.style.transform="translateY(0)")},children:[r("div",{style:{position:"absolute",top:0,right:0,width:"24px",height:"24px",background:"linear-gradient(135deg, rgba(255,255,255,0.3) 0%, rgba(255,255,255,0.1) 100%)",borderTopRightRadius:"12px",borderBottomLeftRadius:"12px"}}),a!=="view"&&p("div",{className:"hover-buttons",style:{position:"absolute",top:"6px",right:"6px",display:"none",gap:"3px",zIndex:10},children:[!L.isFixed&&r(Xe,{title:"Edit Task",children:r(de,{size:"small",shape:"circle",icon:r(Ki,{style:{fontSize:"13px"}}),onClick:g=>{g.stopPropagation(),o(L)},style:{color:"blue",backgroundColor:"rgba(255,255,255,0.9)",border:"none",boxShadow:"0 1px 4px rgba(0,0,0,0.15)",width:"23px",height:"23px",minWidth:"20px"}})}),!L.isFixed&&E!==0&&p(ft,{children:[r(Xe,{title:"Move Task",children:r(de,{size:"small",shape:"circle",icon:r(Ts,{style:{fontSize:"13px"}}),onClick:g=>{g.stopPropagation(),l(L),d(!0)},style:{color:"#722ed1",backgroundColor:"rgba(255,255,255,0.9)",border:"none",boxShadow:"0 1px 4px rgba(0,0,0,0.15)",width:"23px",height:"23px",minWidth:"20px"}})}),r(Xe,{title:"Delete Task",children:r(eo,{title:"Delete Task",description:c.POPCONFIRM_DESC,onConfirm:g=>{g==null||g.stopPropagation(),u(E,L.id)},okText:"Delete",cancelText:"Cancel",okType:"danger",children:r(de,{size:"small",shape:"circle",icon:r(Xi,{style:{fontSize:"13px"}}),onClick:g=>g.stopPropagation(),danger:!0,style:{backgroundColor:"rgba(255,255,255,0.9)",border:"none",boxShadow:"0 1px 4px rgba(0,0,0,0.15)",width:"23px",height:"23px",minWidth:"20px"}})})})]})]}),p("div",{style:{position:"relative",zIndex:1},children:[r("div",{style:{display:"flex",alignItems:"flex-start",justifyContent:"space-between",marginBottom:8},children:r(Xs,{style:{fontSize:"12px",fontWeight:"600",color:"#1a1a1a",lineHeight:"1.2",letterSpacing:"-0.01em",textShadow:"0 1px 1px rgba(0,0,0,0.1)",flex:1,marginRight:"6px"},children:L.workflowTaskName})}),r("div",{style:{fontSize:"10px",fontWeight:"500",color:"#2c3e50",marginBottom:6,padding:"4px 8px",backgroundColor:"rgba(255,255,255,0.7)",borderRadius:"6px",border:"1px solid rgba(255,255,255,0.8)",textAlign:"center",boxShadow:"0 1px 2px rgba(0,0,0,0.1)",marginTop:"12px"},children:L.workflowGroup})]})]},L.id)})})]}),C<x.length-1&&r("div",{style:{display:"flex",alignItems:"center",height:"auto",paddingTop:"115px",flexShrink:0,justifyContent:"center"},children:r(xs,{style:{fontSize:"18px",color:"#1890ff",padding:"8px",backgroundColor:"rgba(255,255,255,0.9)",borderRadius:"50%",boxShadow:"0 1px 4px rgba(24, 144, 255, 0.3)"}})})]},E)})}),r("style",{jsx:!0,children:`
        div::-webkit-scrollbar {
          width: 8px;
          height: 8px;
        }
        
        div::-webkit-scrollbar-track {
          background: #f3f4f6;
          border-radius: 4px;
        }
        
        div::-webkit-scrollbar-thumb {
          background: #d1d5db;
          border-radius: 4px;
        }
        
        div::-webkit-scrollbar-thumb:hover {
          background: #9ca3af;
        }
      `})]})},{Title:Js,Text:Ue}=Lt,Qs=({selectedCard:e,findTaskLevel:t,mode:n,setIsAddModalVisible:a,isCollapsed:o,onToggleCollapse:l})=>{const d=mt();return o?p("div",{style:{padding:"24px 8px",display:"flex",flexDirection:"column",alignItems:"center",height:"100%"},children:[r(Xe,{title:"Expand Task Details",placement:"left",children:r(de,{type:"text",icon:r(ks,{}),onClick:l,style:{color:d.palette.primary.main,fontSize:"18px",width:"40px",height:"40px",display:"flex",alignItems:"center",justifyContent:"center",marginBottom:"20px"}})}),n!=="view"&&r(Xe,{title:"Add Task",placement:"left",children:r(de,{type:"primary",icon:r(ba,{}),onClick:()=>a(!0),style:{backgroundColor:d.palette.primary.main,borderRadius:"6px",boxShadow:`0 2px 4px ${d.palette.primary.main}60`,width:"40px",height:"40px",display:"flex",alignItems:"center",justifyContent:"center"}})}),e&&p("div",{style:{marginTop:"30px",display:"flex",flexDirection:"column",alignItems:"center",gap:"8px"},children:[r("div",{style:{width:"40px",height:"40px",borderRadius:"8px",backgroundColor:"#fafafa",border:"1px solid #e8e8e8",display:"flex",alignItems:"center",justifyContent:"center"},children:r($t,{style:{color:d.palette.primary.main}})}),r("div",{style:{fontSize:"20px",fontWeight:"bold",marginTop:"100px",paddingLeft:"4px",color:"#666",textAlign:"center",writingMode:"vertical-rl",textOrientation:"mixed",maxHeight:"500px",overflow:"hidden"},children:e.workflowTaskName})]})]}):p("div",{style:{padding:"24px"},children:[p("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center",marginBottom:20},children:[r(Js,{level:4,style:{margin:0,color:d.palette.primary.main},children:"Task Details"}),p("div",{style:{display:"flex",gap:"8px",alignItems:"center"},children:[n!=="view"&&r(de,{type:"primary",icon:r(ba,{}),onClick:()=>a(!0),style:{backgroundColor:d.palette.primary.main,borderRadius:"6px",boxShadow:`0 2px 4px ${d.palette.primary.main}60`},children:"Add Task"}),r(Xe,{title:"Collapse Panel",children:r(de,{type:"text",icon:r(Hs,{}),onClick:l,style:{color:d.palette.primary.main,fontSize:"16px"}})})]})]}),e?p("div",{children:[r(ut,{title:p(vt,{children:[r("span",{style:{fontSize:"14px"},children:e.workflowTaskName}),e.isFixed&&r(Tt,{size:"small",color:"gold",children:"FIXED"})]}),size:"small",style:{marginBottom:16,borderRadius:"8px",backgroundColor:"#fafafa"},children:p("div",{style:{fontSize:"12px",lineHeight:"20px"},children:[p("div",{children:[p(Y,{gutter:[8,4],style:{marginBottom:"8px"},children:[r(_,{span:8,children:r(Ue,{strong:!0,children:"Level:"})}),r(_,{span:16,children:t(e.id)})]}),p(Y,{gutter:[8,4],style:{marginBottom:"8px"},children:[r(_,{span:8,children:r(Ue,{strong:!0,children:"Group:"})}),r(_,{span:16,children:e.workflowGroup})]}),p(Y,{gutter:[8,4],style:{marginBottom:"8px"},children:[r(_,{span:8,children:r(Ue,{strong:!0,children:"High Priority SLA:"})}),p(_,{span:16,children:[e.slaHigh," hours"]})]}),p(Y,{gutter:[8,4],style:{marginBottom:"8px"},children:[r(_,{span:8,children:r(Ue,{strong:!0,children:"Medium Priority SLA:"})}),p(_,{span:16,children:[e.slaMedium," hours"]})]}),p(Y,{gutter:[8,4],style:{marginBottom:"8px"},children:[r(_,{span:8,children:r(Ue,{strong:!0,children:"Low Priority SLA:"})}),p(_,{span:16,children:[e.slaLow," hours"]})]})]}),e.sendBackAllowedTo&&e.sendBackAllowedTo.length>0&&p("div",{style:{marginTop:12},children:[r(Ue,{strong:!0,children:"Send Back Allowed To:"}),r("div",{style:{marginTop:4},children:e.sendBackAllowedTo.map((u,f)=>p(Tt,{size:"small",style:{marginBottom:4},children:["Level ",u]},f))})]}),e.requestBenchStatus&&p("div",{style:{marginTop:12},children:[r(Ue,{strong:!0,children:"Request Bench Status:"}),r("div",{style:{marginTop:4,fontStyle:"italic",padding:"8px",backgroundColor:"white",borderRadius:"4px",border:"1px solid #f0f0f0"},children:e.requestBenchStatus})]})]})}),r(ut,{size:"small",style:{borderRadius:"8px"},children:p("div",{style:{textAlign:"center",color:"#666",padding:"20px 0"},children:[r($t,{style:{fontSize:"24px",marginBottom:8}}),r("div",{children:"Task details are displayed above"}),r("div",{style:{fontSize:"12px",marginTop:4},children:"Use the edit icon on task cards to modify"})]})})]}):r(ut,{size:"small",style:{borderRadius:"8px"},children:p("div",{style:{textAlign:"center",color:"#999",padding:"40px 0"},children:[r($t,{style:{fontSize:"48px",marginBottom:16}}),r("div",{children:"Select a task card to view its details"})]})})]})},{Text:lt}=Lt,{Option:St}=_e,Zs=({workflowData:e,isAddModalVisible:t,setIsAddModalVisible:n,addForm:a,handleAddTask:o,getAvailableLevels:l,handleLevelNumberChange:d,selectedLevel:u,workflowGroups:f,getSendBackAllowedLevels:S,isEditModalVisible:x,setIsEditModalVisible:N,cardToEdit:E,setCardToEdit:C,editDialogForm:F,handleSaveEditedTask:O,findTaskLevel:D,isMoveModalVisible:A,setIsMoveModalVisible:L,cardToMove:P,setCardToMove:g,moveForm:H,handleMoveTask:M,isLevelNameChangeModalVisible:V,setIsLevelNameChangeModalVisible:z,levelNameChangeData:K,setLevelNameChangeData:J,handleLevelNameChangeConfirm:ae,isBenchStatusChangeModalVisible:ue,setIsBenchStatusChangeModalVisible:le,benchStatusChangeData:ce,setBenchStatusChangeData:Be,handleBenchStatusChangeConfirm:Fe})=>{var fe,me,oe,be,ve,Q,ge,he,Ce,U,W,se,Qe,Oe,Ae,Re,De,Se,Pe,ye,He,Ie,xe,we,We,je,Ze,Ve,et,tt,ze,Me,$e,qe,i,m,b,w,I,y,$,j,k,q,h,v,R,T,re,pe,Te,Dt,Pt,Ht,Wt,jt,Vt,zt,qt,Gt,Ut,Kt,Xt,Yt,Jt,Qt,Zt,en,tn,nn,rn,an,on,sn,ln,cn,dn,un,fn,pn,mn,gn,hn,Sn,bn,vn,yn,xn,wn,En,Ln,Nn,In,Cn,On,$n,Tn,_n,An,Rn,Mn,kn,Bn,Fn,Dn,Pn,Hn,Wn,jn,Vn,zn,qn,Gn,Un,Kn,Xn,Yn,Jn,Qn,Zn,er,tr,nr,rr,ar,ir,or,sr,lr,cr,dr,ur,fr,pr,mr,gr,hr,Sr,br,vr,yr,xr,wr,Er,Lr,Nr,Ir,Cr,Or,$r,Tr,_r,Ar,Rr,Mr,kr,Br,Fr,Dr,Pr,Hr,Wr,jr,Vr,zr,qr,Gr,Ur,Kr,Xr,Yr,Jr,Qr,Zr,ea,ta,na,ra,aa,ia,oa,sa,la,ca,da,ua,fa,pa,ma;const ie=mt();return p(ft,{children:[r(ot,{title:"Add New Task",open:t,onCancel:()=>{n(!1),a.resetFields()},footer:null,width:600,children:p(G,{form:a,layout:"vertical",onFinish:o,children:[p(Y,{gutter:16,children:[r(_,{span:12,children:r(G.Item,{name:(oe=(me=(fe=c)==null?void 0:fe.MODAL_FILTERS)==null?void 0:me.LVL_NUM)==null?void 0:oe.name,label:(Q=(ve=(be=c)==null?void 0:be.MODAL_FILTERS)==null?void 0:ve.LVL_NUM)==null?void 0:Q.label,rules:[{required:!0,message:(he=(ge=c)==null?void 0:ge.MODAL_FILTERS)==null?void 0:he.LVL_NUM}],children:r(_e,{showSearch:!0,placeholder:(W=(U=(Ce=c)==null?void 0:Ce.MODAL_FILTERS)==null?void 0:U.LVL_NUM)==null?void 0:W.placeholder,optionFilterProp:"children",onChange:d,filterOption:(B,X)=>{var ke;return(ke=X==null?void 0:X.children)==null?void 0:ke.toLowerCase().includes(B.toLowerCase())},children:l().map(B=>r(St,{value:B,children:B===-1?"Requestor Level (-1)":`Level ${B}`},B))})})}),r(_,{span:12,children:r(G.Item,{name:(Oe=(Qe=(se=c)==null?void 0:se.MODAL_FILTERS)==null?void 0:Qe.LVL_NAME)==null?void 0:Oe.name,label:(De=(Re=(Ae=c)==null?void 0:Ae.MODAL_FILTERS)==null?void 0:Re.LVL_NAME)==null?void 0:De.label,rules:[{required:!0,message:(ye=(Pe=(Se=c)==null?void 0:Se.MODAL_FILTERS)==null?void 0:Pe.LVL_NAME)==null?void 0:ye.message}],children:r(it,{placeholder:(xe=(Ie=(He=c)==null?void 0:He.MODAL_FILTERS)==null?void 0:Ie.LVL_NAME)==null?void 0:xe.placeholder})})})]}),p(Y,{gutter:16,children:[r(_,{span:12,children:r(G.Item,{name:(je=(We=(we=c)==null?void 0:we.MODAL_FILTERS)==null?void 0:We.WORKFLOW_GROUP)==null?void 0:je.name,label:(et=(Ve=(Ze=c)==null?void 0:Ze.MODAL_FILTERS)==null?void 0:Ve.WORKFLOW_GROUP)==null?void 0:et.label,rules:[{required:!0,message:(Me=(ze=(tt=c)==null?void 0:tt.MODAL_FILTERS)==null?void 0:ze.WORKFLOW_GROUP)==null?void 0:Me.message}],children:r(_e,{showSearch:!0,placeholder:(i=(qe=($e=c)==null?void 0:$e.MODAL_FILTERS)==null?void 0:qe.WORKFLOW_GROUP)==null?void 0:i.placeholder,optionFilterProp:"children",filterOption:(B,X)=>{var ke;return(ke=X==null?void 0:X.children)==null?void 0:ke.toLowerCase().includes(B.toLowerCase())},children:f.map(B=>r(_e.Option,{value:B,children:B},B))})})}),r(_,{span:12,children:r(G.Item,{name:(w=(b=(m=c)==null?void 0:m.MODAL_FILTERS)==null?void 0:b.TASK_NAME)==null?void 0:w.name,label:($=(y=(I=c)==null?void 0:I.MODAL_FILTERS)==null?void 0:y.TASK_NAME)==null?void 0:$.label,rules:[{required:!0,message:(q=(k=(j=c)==null?void 0:j.MODAL_FILTERS)==null?void 0:k.TASK_NAME)==null?void 0:q.message}],children:r(it,{placeholder:(R=(v=(h=c)==null?void 0:h.MODAL_FILTERS)==null?void 0:v.TASK_NAME)==null?void 0:R.placeholder})})})]}),p(Y,{gutter:16,children:[r(_,{span:12,children:r(G.Item,{name:(pe=(re=(T=c)==null?void 0:T.MODAL_FILTERS)==null?void 0:re.SEND_BACK_ALLOWED_TO)==null?void 0:pe.name,label:(Pt=(Dt=(Te=c)==null?void 0:Te.MODAL_FILTERS)==null?void 0:Dt.SEND_BACK_ALLOWED_TO)==null?void 0:Pt.label,children:r(_e,{mode:"multiple",placeholder:(jt=(Wt=(Ht=c)==null?void 0:Ht.MODAL_FILTERS)==null?void 0:Wt.SEND_BACK_ALLOWED_TO)==null?void 0:jt.placeholder,allowClear:!0,filterOption:(B,X)=>((X==null?void 0:X.label)??"").toLowerCase().includes(B.toLowerCase()),children:(()=>{const B=a.getFieldValue("levelNumber")||u;return B!=null?S(B).filter(X=>X!==0).map(X=>r(St,{value:X,children:X===-1?"Requestor Level (-1)":`Level ${X}`},X)):[]})()})})}),r(_,{span:12,children:r(G.Item,{name:(qt=(zt=(Vt=c)==null?void 0:Vt.MODAL_FILTERS)==null?void 0:zt.TASK_DESC)==null?void 0:qt.name,label:(Kt=(Ut=(Gt=c)==null?void 0:Gt.MODAL_FILTERS)==null?void 0:Ut.TASK_DESC)==null?void 0:Kt.label,rules:[{required:!0,message:(Jt=(Yt=(Xt=c)==null?void 0:Xt.MODAL_FILTERS)==null?void 0:Yt.TASK_DESC)==null?void 0:Jt.message}],children:r(it,{placeholder:(en=(Zt=(Qt=c)==null?void 0:Qt.MODAL_FILTERS)==null?void 0:Zt.TASK_DESC)==null?void 0:en.message})})})]}),p(Y,{gutter:16,children:[r(_,{span:8,children:r(G.Item,{name:(rn=(nn=(tn=c)==null?void 0:tn.MODAL_FILTERS)==null?void 0:nn.SLA_HIGH)==null?void 0:rn.name,label:(sn=(on=(an=c)==null?void 0:an.MODAL_FILTERS)==null?void 0:on.SLA_HIGH)==null?void 0:sn.label,rules:[{required:!0,message:(dn=(cn=(ln=c)==null?void 0:ln.MODAL_FILTERS)==null?void 0:cn.SLA_HIGH)==null?void 0:dn.message}],children:r(nt,{min:1,max:168,style:{width:"100%"},placeholder:(pn=(fn=(un=c)==null?void 0:un.MODAL_FILTERS)==null?void 0:fn.SLA_HIGH)==null?void 0:pn.placeholder})})}),r(_,{span:8,children:r(G.Item,{name:(hn=(gn=(mn=c)==null?void 0:mn.MODAL_FILTERS)==null?void 0:gn.SLA_MEDIUM)==null?void 0:hn.name,label:(vn=(bn=(Sn=c)==null?void 0:Sn.MODAL_FILTERS)==null?void 0:bn.SLA_MEDIUM)==null?void 0:vn.label,rules:[{required:!0,message:(wn=(xn=(yn=c)==null?void 0:yn.MODAL_FILTERS)==null?void 0:xn.SLA_MEDIUM)==null?void 0:wn.message}],children:r(nt,{min:1,max:168,style:{width:"100%"},placeholder:(Nn=(Ln=(En=c)==null?void 0:En.MODAL_FILTERS)==null?void 0:Ln.SLA_MEDIUM)==null?void 0:Nn.placeholder})})}),r(_,{span:8,children:r(G.Item,{name:(On=(Cn=(In=c)==null?void 0:In.MODAL_FILTERS)==null?void 0:Cn.SLA_LOW)==null?void 0:On.name,label:(_n=(Tn=($n=c)==null?void 0:$n.MODAL_FILTERS)==null?void 0:Tn.SLA_LOW)==null?void 0:_n.label,rules:[{required:!0,message:(Mn=(Rn=(An=c)==null?void 0:An.MODAL_FILTERS)==null?void 0:Rn.SLA_LOW)==null?void 0:Mn.message}],children:r(nt,{min:1,max:168,style:{width:"100%"},placeholder:(Fn=(Bn=(kn=c)==null?void 0:kn.MODAL_FILTERS)==null?void 0:Bn.SLA_LOW)==null?void 0:Fn.placeholder})})})]}),r(G.Item,{style:{marginBottom:0,textAlign:"right"},children:p(vt,{children:[r(de,{onClick:()=>{n(!1),a.resetFields()},children:"Cancel"}),r(de,{type:"primary",htmlType:"submit",style:{backgroundColor:ie.palette.primary.main},children:"Add Task"})]})})]})}),r(ot,{title:"Edit Task",open:x,onCancel:()=>{N(!1),C(null),F.resetFields()},footer:null,width:600,children:E&&p("div",{children:[p("div",{style:{marginBottom:16,padding:12,backgroundColor:"#f5f5f5",borderRadius:6},children:[r(lt,{strong:!0,children:"Editing: "}),r(lt,{children:E.workflowTaskName}),p(lt,{type:"secondary",style:{marginLeft:8},children:["(Level ",D(E.id),")"]})]}),p(G,{form:F,layout:"vertical",onFinish:O,children:[p(Y,{gutter:16,children:[r(_,{span:12,children:r(G.Item,{name:(Hn=(Pn=(Dn=c)==null?void 0:Dn.MODAL_FILTERS)==null?void 0:Pn.WORKFLOW_GROUP)==null?void 0:Hn.name,label:(Vn=(jn=(Wn=c)==null?void 0:Wn.MODAL_FILTERS)==null?void 0:jn.WORKFLOW_GROUP)==null?void 0:Vn.label,rules:[{required:!0,message:(Gn=(qn=(zn=c)==null?void 0:zn.MODAL_FILTERS)==null?void 0:qn.WORKFLOW_GROUP)==null?void 0:Gn.message}],children:r(_e,{showSearch:!0,placeholder:(Xn=(Kn=(Un=c)==null?void 0:Un.MODAL_FILTERS)==null?void 0:Kn.WORKFLOW_GROUP)==null?void 0:Xn.placeholder,optionFilterProp:"children",filterOption:(B,X)=>{var ke;return(ke=X==null?void 0:X.children)==null?void 0:ke.toLowerCase().includes(B.toLowerCase())},children:f.map(B=>r(_e.Option,{value:B,children:B},B))})})}),r(_,{span:12,children:r(G.Item,{name:(Qn=(Jn=(Yn=c)==null?void 0:Yn.MODAL_FILTERS)==null?void 0:Jn.TASK_NAME)==null?void 0:Qn.name,label:(tr=(er=(Zn=c)==null?void 0:Zn.MODAL_FILTERS)==null?void 0:er.TASK_NAME)==null?void 0:tr.label,rules:[{required:!0,message:(ar=(rr=(nr=c)==null?void 0:nr.MODAL_FILTERS)==null?void 0:rr.TASK_NAME)==null?void 0:ar.message}],children:r(it,{placeholder:(sr=(or=(ir=c)==null?void 0:ir.MODAL_FILTERS)==null?void 0:or.TASK_NAME)==null?void 0:sr.placeholder})})})]}),p(Y,{gutter:16,children:[r(_,{span:12,children:r(G.Item,{name:(dr=(cr=(lr=c)==null?void 0:lr.MODAL_FILTERS)==null?void 0:cr.SEND_BACK_ALLOWED_TO)==null?void 0:dr.name,label:(pr=(fr=(ur=c)==null?void 0:ur.MODAL_FILTERS)==null?void 0:fr.SEND_BACK_ALLOWED_TO)==null?void 0:pr.label,children:r(_e,{mode:"multiple",placeholder:(hr=(gr=(mr=c)==null?void 0:mr.MODAL_FILTERS)==null?void 0:gr.SEND_BACK_ALLOWED_TO)==null?void 0:hr.placeholder,allowClear:!0,children:(br=(Sr=S(D(E.id)))==null?void 0:Sr.filter(B=>B!==0))==null?void 0:br.map(B=>r(St,{value:B,children:B===-1?"Requestor Level (-1)":`Level ${B}`},B))})})}),r(_,{span:12,children:r(G.Item,{name:(xr=(yr=(vr=c)==null?void 0:vr.MODAL_FILTERS)==null?void 0:yr.TASK_DESC)==null?void 0:xr.name,label:(Lr=(Er=(wr=c)==null?void 0:wr.MODAL_FILTERS)==null?void 0:Er.TASK_DESC)==null?void 0:Lr.label,rules:[{required:!0,message:(Cr=(Ir=(Nr=c)==null?void 0:Nr.MODAL_FILTERS)==null?void 0:Ir.TASK_DESC)==null?void 0:Cr.message}],children:r(it,{placeholder:(Tr=($r=(Or=c)==null?void 0:Or.MODAL_FILTERS)==null?void 0:$r.TASK_DESC)==null?void 0:Tr.message})})})]}),p(Y,{gutter:16,children:[r(_,{span:8,children:r(G.Item,{name:(Rr=(Ar=(_r=c)==null?void 0:_r.MODAL_FILTERS)==null?void 0:Ar.SLA_HIGH)==null?void 0:Rr.name,label:(Br=(kr=(Mr=c)==null?void 0:Mr.MODAL_FILTERS)==null?void 0:kr.SLA_HIGH)==null?void 0:Br.label,rules:[{required:!0,message:(Pr=(Dr=(Fr=c)==null?void 0:Fr.MODAL_FILTERS)==null?void 0:Dr.SLA_HIGH)==null?void 0:Pr.message}],children:r(nt,{min:1,max:168,style:{width:"100%"},placeholder:(jr=(Wr=(Hr=c)==null?void 0:Hr.MODAL_FILTERS)==null?void 0:Wr.SLA_HIGH)==null?void 0:jr.placeholder})})}),r(_,{span:8,children:r(G.Item,{name:(qr=(zr=(Vr=c)==null?void 0:Vr.MODAL_FILTERS)==null?void 0:zr.SLA_MEDIUM)==null?void 0:qr.name,label:(Kr=(Ur=(Gr=c)==null?void 0:Gr.MODAL_FILTERS)==null?void 0:Ur.SLA_MEDIUM)==null?void 0:Kr.label,rules:[{required:!0,message:(Jr=(Yr=(Xr=c)==null?void 0:Xr.MODAL_FILTERS)==null?void 0:Yr.SLA_MEDIUM)==null?void 0:Jr.message}],children:r(nt,{min:1,max:168,style:{width:"100%"},placeholder:(ea=(Zr=(Qr=c)==null?void 0:Qr.MODAL_FILTERS)==null?void 0:Zr.SLA_MEDIUM)==null?void 0:ea.placeholder})})}),r(_,{span:8,children:r(G.Item,{name:(ra=(na=(ta=c)==null?void 0:ta.MODAL_FILTERS)==null?void 0:na.SLA_LOW)==null?void 0:ra.name,label:(oa=(ia=(aa=c)==null?void 0:aa.MODAL_FILTERS)==null?void 0:ia.SLA_LOW)==null?void 0:oa.label,rules:[{required:!0,message:(ca=(la=(sa=c)==null?void 0:sa.MODAL_FILTERS)==null?void 0:la.SLA_LOW)==null?void 0:ca.message}],children:r(nt,{min:1,max:168,style:{width:"100%"},placeholder:(fa=(ua=(da=c)==null?void 0:da.MODAL_FILTERS)==null?void 0:ua.SLA_LOW)==null?void 0:fa.placeholder})})})]}),r(G.Item,{style:{marginBottom:0,textAlign:"right"},children:p(vt,{children:[r(de,{onClick:()=>{N(!1),C(null),F.resetFields()},children:"Cancel"}),r(de,{type:"primary",htmlType:"submit",icon:r(qs,{}),style:{backgroundColor:ie.palette.primary.main},children:"Save Changes"})]})})]})]})}),r(ot,{title:"Move Task",open:A,onCancel:()=>{L(!1),g(null),H.resetFields()},footer:null,width:400,children:P&&p("div",{children:[p("div",{style:{marginBottom:16,padding:12,backgroundColor:"#f5f5f5",borderRadius:6},children:[r(lt,{strong:!0,children:"Moving: "}),r(lt,{children:P.workflowTaskName})]}),p(G,{form:H,layout:"vertical",onFinish:M,children:[r(G.Item,{name:"newLevel",label:"Select New Level",rules:[{required:!0}],children:r(_e,{placeholder:"Select destination level",children:l().filter(B=>B.toString()!==D(P.id)).map(B=>r(St,{value:B,children:B===-1?"Requestor Level (-1)":`Level ${B}`},B))})}),r(G.Item,{style:{marginBottom:0,textAlign:"right"},children:p(vt,{children:[r(de,{onClick:()=>{L(!1),g(null),H.resetFields()},children:"Cancel"}),r(de,{type:"primary",htmlType:"submit",style:{backgroundColor:ie.palette.primary.main},children:"Move Task"})]})})]})]})}),r(ot,{title:"Confirm Level Name Change",open:V,onOk:ae,onCancel:()=>{z(!1),J(null)},okText:"Confirm Change",cancelText:"Cancel",okType:"primary",children:K&&p("div",{children:[p("p",{children:["Level ",K.levelNumber,' already has tasks with the name "',K.currentLevelName,'".']}),p("p",{children:['Are you sure you want to change it to "',K.newLevelName,'"?']}),p("p",{style:{color:"#fa8c16",fontSize:"12px"},children:[r(va,{style:{marginRight:4}}),"This will affect all tasks in this level."]})]})}),r(ot,{title:"Confirm Request Bench Status Change",open:ue,onOk:Fe,onCancel:()=>{le(!1),Be(null)},okText:"Confirm Change",cancelText:"Cancel",okType:"primary",children:ce&&p("div",{children:[p("p",{children:["Level ",ce.levelNumber," currently has ",((ma=(pa=e[ce.levelNumber])==null?void 0:pa.tasks)==null?void 0:ma.length)||0,' task(s) with Request Bench Status "',ce.currentBenchStatus,'".']}),p("p",{children:['Are you sure you want to change it to "',ce.newBenchStatus,'"?']}),p("p",{style:{color:"#fa8c16",fontSize:"12px"},children:[r(va,{style:{marginRight:4}}),"This will update the Request Bench Status for ALL tasks in Level ",ce.levelNumber,"."]})]})})]})},{Content:el,Sider:tl}=qa,dl=()=>{var qe;const[e,t]=s.useState({}),[n,a]=s.useState(null),[o,l]=s.useState(null),[d,u]=s.useState(!1),[f,S]=s.useState(!1),[x,N]=s.useState(!1),[E,C]=s.useState(!1),[F,O]=s.useState(!1),[D,A]=s.useState(!1),[L,P]=s.useState(null),[g,H]=s.useState(null),[M,V]=s.useState(null),[z,K]=s.useState(null),[J,ae]=s.useState(!1),[ue,le]=s.useState([]),[ce,Be]=s.useState([]),[Fe,ie]=s.useState(!1),[fe,me]=s.useState(""),[oe]=G.useForm(),[be]=G.useForm(),[ve]=G.useForm(),[Q]=G.useForm(),ge=ni(),he=ri(),{showSnackbar:Ce}=ai(),U=ge.state||{},W=(U==null?void 0:U.mode)||c.MODES.CREATE,se=(U==null?void 0:U.headerData)||{};s.useEffect(()=>{var m;let i=W===c.MODES.CREATE?ii:U==null?void 0:U.workflowData;t(i),a((m=i[-1])==null?void 0:m.tasks[0]),et()},[]);const Qe=()=>{u(!d)},Oe=i=>{const m=Object.keys(i).map(y=>parseInt(y)),b=m.filter(y=>y===-1||y===0),w=m.filter(y=>y>0).sort((y,$)=>y-$),I={};return b.forEach(y=>{i[y]&&(I[y]={...i[y]})}),w.forEach((y,$)=>{var k,q,h,v,R;const j=$+1;i[y]&&((k=i[y].tasks)==null?void 0:k.length)>0&&(I[j]={...i[y],levelName:(h=i[y].levelName)!=null&&h.includes(`${(q=c)==null?void 0:q.LEVEL} ${y}`)?i[y].levelName.replace(`${(v=c)==null?void 0:v.LEVEL} ${y}`,`${(R=c)==null?void 0:R.LEVEL} ${j}`):i[y].levelName,requestBenchStatus:i[y].requestBenchStatus},I[j].tasks=I[j].tasks.map(T=>{var re;return{...T,sendBackAllowedTo:((re=T.sendBackAllowedTo)==null?void 0:re.map(pe=>{if(pe>0){const Te=w.indexOf(pe);return Te!==-1?Te+1:pe}return pe}))||[]}}))}),Object.keys(I).forEach(y=>{var $;($=I[y])!=null&&$.tasks&&(I[y].tasks=I[y].tasks.map(j=>{var k;return{...j,sendBackAllowedTo:((k=j.sendBackAllowedTo)==null?void 0:k.map(q=>{if(q>0){const h=w.indexOf(q);return h!==-1?h+1:q}return q}))||[]}}))}),I},Ae=()=>{const i=Object.keys(e).map(w=>parseInt(w)).sort((w,I)=>w-I),m=[];i.forEach(w=>{w>0&&m.push(w)});const b=Math.max(...i.filter(w=>w>0),0);return m.push(b+1),m.filter(w=>w!==0)},Re=i=>{const m=Object.keys(e).map(b=>parseInt(b)).sort((b,w)=>b-w);return i==="0"||i===0?m.filter(b=>b!==0):m.filter(b=>b<parseInt(i))},De=()=>{const i=Object.keys(e).map(w=>parseInt(w)),m=i.filter(w=>w===0);return[...i.filter(w=>w!==0).sort((w,I)=>w-I),...m]},Se=i=>{var m,b;for(const w in e)if((b=(m=e[w])==null?void 0:m.tasks)!=null&&b.find(I=>I.id===i))return w;return null},Pe=(i,m,b)=>{var I,y,$;const w=(I=e[i])==null?void 0:I.levelName;w&&w!==m&&(($=(y=e[i])==null?void 0:y.tasks)==null?void 0:$.length)>0?(K({levelNumber:i,currentLevelName:w,newLevelName:m,callback:b}),O(!0)):b()},ye=(i,m,b)=>{var I;const w=e[i];if(((I=w==null?void 0:w.tasks)==null?void 0:I.length)>0){const y=w.requestBenchStatus;y&&y!==m?(P({levelNumber:i,currentBenchStatus:y,newBenchStatus:m,callback:b,updateAllTasks:!0}),A(!0)):b()}else b()},He=()=>{z!=null&&z.callback&&z.callback(),O(!1),K(null)},Ie=()=>{L!=null&&L.callback&&(L.updateAllTasks&&t(i=>{const m={...i},b=L.levelNumber;return m[b]&&(m[b].tasks=m[b].tasks.map(w=>({...w,requestBenchStatus:L.newBenchStatus})),n&&Se(n.id)===b.toString()&&a(w=>({...w,requestBenchStatus:L.newBenchStatus}))),m}),L.callback()),A(!1),P(null)},xe=i=>{const m=()=>{var w;const b={id:`task-${Date.now()}`,workflowTaskName:i.workflowTaskName,requestBenchStatus:i.requestBenchStatus||((w=c)==null?void 0:w.TASK_DESC),isFixed:!1,workflowGroup:i.workflowGroup,slaHigh:i.slaHigh||24,slaMedium:i.slaMedium||48,slaLow:i.slaLow||72,sendBackAllowedTo:i.sendBackAllowedTo||[]};t(I=>{var j,k,q,h;const y={...I},$=i.levelNumber;return y[$]?(i.levelName&&(y[$].levelName=i.levelName),i.requestBenchStatus&&(y[$].requestBenchStatus=i.requestBenchStatus,y[$].tasks=y[$].tasks.map(v=>({...v,requestBenchStatus:i.requestBenchStatus})))):y[$]={levelName:i.levelName||`${(j=c)==null?void 0:j.LEVEL} ${$}`,requestBenchStatus:i.requestBenchStatus||((k=c)==null?void 0:k.TASK_DESC),tasks:[]},(h=(q=y[$])==null?void 0:q.tasks)==null||h.push(b),y}),n&&Se(n.id)===i.levelNumber.toString()&&a(I=>{var y;return{...I,requestBenchStatus:i.requestBenchStatus||((y=c)==null?void 0:y.TASK_DESC)}}),S(!1),oe.resetFields(),st.success({message:c.SUCCESS_MSG,description:`"${i.workflowTaskName}" ${c.SUCCESS_DESC} ${i.levelNumber}`,duration:3})};if(i.levelName||i.requestBenchStatus){const b=()=>{i.requestBenchStatus?ye(i.levelNumber,i.requestBenchStatus,m):m()};i.levelName?Pe(i.levelNumber,i.levelName,b):b()}else m()},we=(i,m)=>{var y,$,j,k;const b=(y=e[i])==null?void 0:y.tasks.find(q=>q.id===m),w=((j=($=e[i])==null?void 0:$.tasks)==null?void 0:j.length)||0;t(q=>{const h={...q};return h[i]&&(h[i].tasks=h[i].tasks.filter(v=>v.id!==m),h[i].tasks.length===0&&i!=="-1"&&i!==0&&(delete h[i],parseInt(i)>0))?Oe(h):h}),(n==null?void 0:n.id)===m&&a((k=e[-1])==null?void 0:k.tasks[0]);const I=w===1&&parseInt(i)>0;st.success({message:I?c.LEVELWILLBEREMOVED_MSG:c.LEVMOV_MSG_TASK_DEL,description:I?`"${b==null?void 0:b.workflowTaskName}" ${c.LELMOV_DESC}`:`"${b==null?void 0:b.workflowTaskName}" ${c.LELMOV_DESC_DEL}`})},We=i=>{var y,$;const{newLevel:m}=i,b=Se(g.id),w=(($=(y=e[b])==null?void 0:y.tasks)==null?void 0:$.length)||0;if(b===m.toString()){st.warning({message:c.HANDLE_MSG,description:c.HANDLE_DESC});return}t(j=>{var h;const k={...j},q=w===1&&parseInt(b)>0;return k[b]&&(k[b].tasks=k[b].tasks.filter(v=>v.id!==g.id),k[b].tasks.length===0&&b!=="-1"&&b!==0&&delete k[b]),k[m]||(k[m]={levelName:`${(h=c)==null?void 0:h.LEVEL} ${m}`,tasks:[]}),k[m].tasks.push(g),q?Oe(k):k}),N(!1),H(null),ve.resetFields();const I=w===1&&parseInt(b)>0;st.success({message:I?c.LEVELADJUST_MSG:c.LEVELADJUST_MSG_MSGS,description:I?`"${g.workflowTaskName}" ${c.LEVELADJUST_DESC_MOV}${m} ${c.LEVELADJUST_DESC_LEV}`:`"${g.workflowTaskName}"${c.LEVELADJUST_DESC_MOV} ${m}`})},je=i=>{V(i),Q.setFieldsValue({workflowTaskName:i.workflowTaskName,requestBenchStatus:i.requestBenchStatus,workflowGroup:i.workflowGroup,slaHigh:i.slaHigh,slaMedium:i.slaMedium,slaLow:i.slaLow,sendBackAllowedTo:i.sendBackAllowedTo}),C(!0)},Ze=i=>{var I;if(!M)return;const m=Se(M.id),b=()=>{var j,k,q;if(t(h=>{const v={...h};if(v[m])if(i.requestBenchStatus)v[m].requestBenchStatus=i.requestBenchStatus,v[m].tasks=v[m].tasks.map(R=>({...R,requestBenchStatus:i.requestBenchStatus,...R.id===M.id?{workflowTaskName:i.workflowTaskName,workflowGroup:i.workflowGroup,slaHigh:i.slaHigh,slaMedium:i.slaMedium,slaLow:i.slaLow,sendBackAllowedTo:i.sendBackAllowedTo}:{}}));else{const R=v[m].tasks.findIndex(T=>T.id===M.id);R!==-1&&(v[m].tasks[R]={...v[m].tasks[R],workflowTaskName:i.workflowTaskName,workflowGroup:i.workflowGroup,slaHigh:i.slaHigh,slaMedium:i.slaMedium,slaLow:i.slaLow,sendBackAllowedTo:i.sendBackAllowedTo})}return v}),(n==null?void 0:n.id)===M.id){const h={...M,workflowTaskName:i.workflowTaskName,requestBenchStatus:i.requestBenchStatus,workflowGroup:i.workflowGroup,slaHigh:i.slaHigh,slaMedium:i.slaMedium,slaLow:i.slaLow,sendBackAllowedTo:i.sendBackAllowedTo};a(h)}else n&&Se(n.id)===m&&i.requestBenchStatus&&a(h=>({...h,requestBenchStatus:i.requestBenchStatus}));C(!1),V(null),Q.resetFields();const y=((k=(j=e[m])==null?void 0:j.tasks)==null?void 0:k.length)||0,$=i.requestBenchStatus!==((q=e[m])==null?void 0:q.requestBenchStatus);st.success({message:c.HANDLE_TASK_MSG,description:$?`"${i.workflowTaskName}" updated and Request Bench Status changed for all ${y} tasks in Level ${m}`:`"${i.workflowTaskName}"${c.HANDLE_TASK_MSG_DESC}`})},w=(I=e[m])==null?void 0:I.requestBenchStatus;i.requestBenchStatus!==w?ye(m,i.requestBenchStatus,b):b()},Ve=i=>{a(i),i.isFixed||be.setFieldsValue({workflowTaskName:i.workflowTaskName,requestBenchStatus:i.requestBenchStatus,workflowGroup:i.workflowGroup,slaHigh:i.slaHigh,slaMedium:i.slaMedium,slaLow:i.slaLow,sendBackAllowedTo:i.sendBackAllowedTo})},et=()=>{var b,w;const i=I=>{var $;const y=($=I==null?void 0:I.data)==null?void 0:$.map(j=>j.groupName);Be(y)},m=()=>{};Ct(`/${gt}${(w=(b=ht)==null?void 0:b.WORKFLOW_APIS)==null?void 0:w.GET_GRP_NAMES}`,"get",i,m)},tt=i=>{var w,I;const m=(w=e[i])==null?void 0:w.levelName,b=(I=e[i])==null?void 0:I.requestBenchStatus;m?oe.setFieldsValue({levelName:m}):oe.setFieldsValue({levelName:""}),b?oe.setFieldsValue({requestBenchStatus:b}):oe.setFieldsValue({requestBenchStatus:""}),oe.setFieldsValue({sendBackAllowedTo:[]}),l(m||"")},ze=()=>{var I,y,$,j;ie(!0);const i=si(se,e,U==null?void 0:U.module),m=k=>{ie(!1),Ce(k==null?void 0:k.data,"success"),he(-1)},b=k=>{ie(!1),Ce(k==null?void 0:k.data,"error")},w=W===c.MODES.EDIT?`/${gt}${(y=(I=ht)==null?void 0:I.WORKFLOW_APIS)==null?void 0:y.CHANGE_SUBMIT_WF}`:`/${gt}${(j=($=ht)==null?void 0:$.WORKFLOW_APIS)==null?void 0:j.CREATE_SUBMIT_WF}`;Ct(w,"post",m,b,i)},Me=()=>{var I,y;ie(!0);const i={workflowId:se==null?void 0:se.workflowId},m=`/${gt}${(y=(I=ht)==null?void 0:I.WORKFLOW_APIS)==null?void 0:y.FETCH_CHANGELOG}`;Ct(m,"post",$=>{var j;if(ie(!1),($==null?void 0:$.statusCode)===li.STATUS_200){const k=(j=$==null?void 0:$.data)==null?void 0:j.map((q,h)=>({...q,id:q.id||h+1}));le(k),ae(!0)}else le([]),ae(!0)},()=>{ie(!1)},i)},$e=()=>{ae(!1)};return p(qa,{style:{minHeight:"80vh"},children:[p(el,{style:{padding:"25px",overflow:"auto",backgroundColor:"white",transition:"all 0.3s ease"},children:[r(Ks,{mode:W,navigate:he,headerData:se,module:(U==null?void 0:U.module)||((qe=oi)==null?void 0:qe.MAT),fetchChangeLogDetails:Me}),r(Ys,{workflowData:e,selectedCard:n,handleCardClick:Ve,mode:W,handleEditTask:je,setCardToMove:H,setIsMoveModalVisible:N,handleDeleteTask:we,getSortedLevels:De})]}),r(tl,{width:d?60:380,collapsedWidth:60,theme:"light",style:{borderLeft:"1px solid #e8e8e8",backgroundColor:"white",transition:"all 0.3s ease",overflow:"hidden"},children:r(Qs,{selectedCard:n,findTaskLevel:Se,mode:W,setIsAddModalVisible:S,isCollapsed:d,onToggleCollapse:Qe})}),r(Zs,{workflowData:e,isAddModalVisible:f,setIsAddModalVisible:S,addForm:oe,handleAddTask:xe,getAvailableLevels:Ae,handleLevelNumberChange:tt,selectedLevel:o,workflowGroups:ce,getSendBackAllowedLevels:Re,isEditModalVisible:E,setIsEditModalVisible:C,cardToEdit:M,setCardToEdit:V,editDialogForm:Q,handleSaveEditedTask:Ze,findTaskLevel:Se,isMoveModalVisible:x,setIsMoveModalVisible:N,cardToMove:g,setCardToMove:H,moveForm:ve,handleMoveTask:We,isLevelNameChangeModalVisible:F,setIsLevelNameChangeModalVisible:O,levelNameChangeData:z,setLevelNameChangeData:K,handleLevelNameChangeConfirm:He,isBenchStatusChangeModalVisible:D,setIsBenchStatusChangeModalVisible:A,benchStatusChangeData:L,setBenchStatusChangeData:P,handleBenchStatusChangeConfirm:Ie}),r(Gs,{handleSubmitWorkflow:ze,blurLoading:Fe,loaderMessage:fe,openChangeLog:J,handleChangeLogClose:$e,changeLogData:ue,mode:W})]})};export{dl as default};
