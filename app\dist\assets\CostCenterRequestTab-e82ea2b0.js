import{n as $,s as Qt,u as Yo,C as G,bI as co,aT as Ye,zt as zs,aJ as R,aO as Ve,r as i,j as s,aR as js,g as ds,cw as Bs,c as A,aa as rs,B as X,bH as z,bU as Gs,zu as _s,zv as Ho,zw as Is,zx as Vs,aF as ls,F as So,ai as Vo,d as qe,al as bo,an as Me,aX as ee,am as Ao,aG as Ko,af as ns,ag as Do,zy as Ts,a9 as cs,zz as xs,zA as Rs,fE as Vt,bK as Ke,fP as v,fQ as M,aM as Ls,ae as Es,d0 as qs,zB as Ks,aP as ys,da as Ys,zs as Qs,bJ as Pt,zC as Js,dG as Zs,xi as en,a as Ns,eR as Ct,O as Dt,b1 as tn,aZ as on,aD as sn,z9 as nn,ap as Ss,zD as rn,cz as ln,zb as zo,aY as cn,zE as ts,bf as Yt,zF as gs,T as os,a6 as jo,aj as hs,$ as an,bO as dn,bP as un,bQ as Os,bG as Us,gq as ps,L as Cn,gr as Tn,h as gn,f as hn,bl as pn,bm as fn,bn as mn,bo as vs,bp as ss,bq as En,Z as et,b6 as Ms,b5 as ks,zG as lo,xH as Sn,zH as mo,fD as wt,c7 as bs,zI as bn,aK as ws,zJ as An,c6 as as,eZ as Dn,dn as yn,xY as Nn,zn as _n,zK as In,bc as xn,y3 as Rn,bd as Ln,g4 as On,zL as Un,d3 as vn,d4 as Mn,d5 as kn,cI as Xo,d6 as wn,d7 as Pn,br as Bn,ad as Gn,d8 as qn,a$ as $n,d9 as Wn,fY as Fn,zM as Hn,zN as Xn,zO as zn,zP as jn,c8 as Vn}from"./index-f7d9b065.js";import{u as Kn,F as Yn}from"./FilterFieldGlobal-f8e8f75f.js";import{S as $s,D as As,B as is,A as Qn,P as Jn}from"./PreviewPage-634057fa.js";import{d as Ds}from"./FeedOutlined-41109ec9.js";import{F as Eo}from"./FilterChangeDropdown-22e24089.js";import{u as Ws,E as Zn}from"./ErrorReportDialog-cb66d1ed.js";import{S as Kt}from"./SingleSelectDropdown-aee403d4.js";import{O as er,C as tr}from"./ChangeLogGL-8c860c66.js";import{g as or}from"./fieldHelper-36011162.js";import{u as sr}from"./UseCostCenterFieldConfig-b068a69c.js";import{d as nr}from"./TaskAlt-0afc1812.js";import{G as Ps}from"./GenericTabsGlobal-613ace00.js";import{d as fs,a as ms}from"./CloseFullscreen-2870eb3e.js";import{d as rr}from"./PermIdentityOutlined-0746a749.js";import{E as lr}from"./ExcelOperationsCard-49e9ffd2.js";import{d as cr}from"./TrackChangesTwoTone-7a2ab513.js";import{d as ar}from"./FileUploadOutlined-4a68a28a.js";import"./AdapterDayjs-2a9281df.js";import"./advancedFormat-4f6292d4.js";import"./customParseFormat-1bc1aa07.js";import"./isBetween-c1c6beb9.js";import"./DatePicker-a8e9bd4a.js";import"./useMobilePicker-9b56b5b6.js";import"./CSSTransition-cd337b47.js";import"./AttachFile-8d552da8.js";import"./UtilDoc-6f590135.js";import"./FileDownloadOutlined-59854a55.js";import"./VisibilityOutlined-b6cd6d28.js";import"./DeleteOutlined-e668453f.js";import"./CloudUpload-0ba6431e.js";import"./utilityImages-067c3dc2.js";import"./Delete-5278579a.js";import"./ReusablePromptBox-e1871d49.js";import"./featureConfig-652a9f8d.js";import"./Description-ab582559.js";import"./DataObject-52409c14.js";import"./Download-52c4427b.js";import"./useFinanceCostingRows-ffbb569f.js";import"./CheckCircleOutline-e186af3e.js";import"./lz-string-0665f106.js";import"./ErrorHistory-ef441d1f.js";import"./CloudDownload-9a7605e9.js";import"./AttachmentUploadDialog-43cc9099.js";const Fs=()=>{$(E=>E.payload.payloadData);const oe=$(E=>E.applicationConfig);$(E=>{var y;return(y=E.userManagement)==null?void 0:y.taskData}),Qt();const he=Yo();return new URLSearchParams(he.search).get("RequestType"),{getDynamicWorkflowDT:(E,y,u=1,N,k,F)=>new Promise((K,be)=>{var j;let g={decisionTableId:null,decisionTableName:k,version:N,conditions:[{"MDG_CONDITIONS.MDG_CC_REQUEST_TYPE":(j=zs)==null?void 0:j.reqType,"MDG_CONDITIONS.MDG_CC_CCCATEGORY":y||"NA"}]};const C=ie=>{var we,L,se,Le,$e;if(ie.statusCode===R.STATUS_200){let d=((L=(we=ie==null?void 0:ie.data)==null?void 0:we.result[0])==null?void 0:L.MDG_MAT_DYNAMIC_WF_DT)||[];if(F===((se=Ve)==null?void 0:se.BK)){d=(($e=(Le=ie==null?void 0:ie.data)==null?void 0:Le.result[0])==null?void 0:$e.MDG_BNKY_DYNAMIC_WORKFLOW_DT_ACTION_TYPE)||[];const ae=new Map;d.forEach(te=>{if(te.MDG_DYNAMIC_WF_APPROVAL_LEVEL===parseInt(u)){const _e=te.MDG_MAT_SENDBACK_ALLOWED;typeof _e=="string"&&_e.trim().length>0&&_e.split(",").map(We=>{const fe=We.trim().match(/^(-?\d+)-(.*)$/);return fe?{key:parseInt(fe[1],10),Name:fe[2].trim()}:null}).filter(Boolean).forEach(({key:We,Name:fe})=>{ae.has(We)||ae.set(We,fe)})}});const Qe=Array.from(ae,([te,_e])=>({key:te,Name:_e}));K(Qe);return}let Oe=[];d==null||d.forEach(ae=>{ae.MDG_DYNAMIC_WF_APPROVAL_LEVEL===parseInt(u)&&ae.MDG_MAT_SENDBACK_ALLOWED.split(",").map(te=>parseInt(te)).forEach(te=>Oe.push(te))}),Oe=[...new Set(Oe)],K(Oe)}else be(new Error("Failed to fetch workflow levels"))},pe=ie=>{be(ie)};oe.environment==="localhost"?G(`/${co}${Ye.INVOKE_RULES.LOCAL}`,"post",C,pe,g):G(`/${co}${Ye.INVOKE_RULES.PROD}`,"post",C,pe,g)})}},ir=i.forwardRef(function(he,W){return s(js,{direction:"down",ref:W,...he})}),Hs=({reqBench:oe,requestId:he,apiResponses:W,downloadClicked:ke,setDownloadClicked:E,module:y,isDisabled:u})=>{var es,yo,No,_o,Io,xo,Ro,Lo,Oo;const N=ds(),k=Qt(),{fetchedCostCenterData:F,fetchReqBenchDataCC:K,changedFieldsMap:be}=$(e=>e.costCenter),g=$(e=>e.costCenter.payload.requestHeaderData),C=$(e=>e.request.requestHeader),pe=$(e=>e.payload.filteredButtons),j=$(e=>e==null?void 0:e.userManagement.taskData),ie=Yo(),we=new URLSearchParams(ie.search),L=we.get("RequestId"),se=we.get("reqBench"),[Le,$e]=i.useState(!0),[d,Oe]=i.useState(""),[ae,Qe]=i.useState(null),[te,_e]=i.useState([]),[ht,We]=i.useState(!1),[fe,Pe]=i.useState([]),[Be,me]=i.useState([]),[Y,Fe]=i.useState([]),[_t,at]=i.useState(!1),[It,Je]=i.useState(!1),[Jt,Ie]=i.useState(""),[Bt,de]=i.useState(""),[tt,Xe]=i.useState(""),[xt,Q]=i.useState("systemGenerated"),[pt,yt]=i.useState([]),[Gt,qt]=i.useState([]),[Tt,Nt]=i.useState("success"),[$t,Ze]=i.useState(""),[Rt,ft]=i.useState([]),[Ue,Lt]=i.useState(!1),[mt,ve]=i.useState({title:"",message:"",subText:"",buttonText:"",redirectTo:""}),[it,Ee]=i.useState(!1),{getButtonsDisplayGlobal:I,showWfLevels:V}=Ws(),le=$(e=>e.payload.lockIndicatorData),Te=ie.state,Ae=(Te==null?void 0:Te.reqStatus)==="Validated-Requestor",Se=$(e=>e.request.requestHeader),Z=$(e=>e.costCenter.payload),[ot,Ge]=i.useState(!1),{showSnackbar:ze}=Bs(),xe=$(e=>{var t;return(t=e==null?void 0:e.payload)==null?void 0:t.changeFieldSelectiondata}),_=Array.isArray(xe)?xe:[],O=g==null?void 0:g.FieldName,De=(yo=(es=W==null?void 0:W[0])==null?void 0:es.Torequestheaderdata)==null?void 0:yo.FieldName,ye=oe?De:O,[Ne,ue]=i.useState([]),[J,ce]=i.useState(""),{updateChangeLogGlForChange:Et}=Kn(),st=$(e=>e.changeLog.createChangeLogDataGL),{getDynamicWorkflowDT:dt}=Fs();i.useEffect(()=>{var t;const e=async()=>{var f,T;try{const b=await dt(g==null?void 0:g.TemplateName,(f=K==null?void 0:K[0])==null?void 0:f.CostcenterType,j==null?void 0:j.ATTRIBUTE_3,"v4","MDG_DYNAMIC_WF_CC_DT",(T=Ve)==null?void 0:T.CC);ue(b)}catch(b){customError(b)}};g!=null&&g.TemplateName&&((t=K==null?void 0:K[0])!=null&&t.CostcenterType)&&(j!=null&&j.ATTRIBUTE_3)&&e()},[g==null?void 0:g.TemplateName,(No=K==null?void 0:K[0])==null?void 0:No.CostcenterType,j==null?void 0:j.ATTRIBUTE_3]);const He=Array.isArray(ye)?ye.map(e=>e.trim()):typeof ye=="string"?ye.split(",").map(e=>e.trim()):[],{allFields:St,mandatoryFields:ut,fieldMetaMap:ge}=or(_,He);i.useEffect(()=>{(j!=null&&j.ATTRIBUTE_1||L)&&I("Cost Center","MDG_DYN_BTN_DT","v3")},[j]);const Zt=(e,t,f)=>{var D;const T=e==null?void 0:e.code;Et({uniqueId:t.row.id,filedName:(D=t==null?void 0:t.colDef)==null?void 0:D.headerName,jsonName:t==null?void 0:t.field,currentValue:T,requestId:C==null?void 0:C.RequestId,childRequestId:he,accountNumber:t.row.costCenter});const b={...t.row,[t.field]:T};k(oe?Rs(b):xs(b))},B=()=>{$e(!1),E(!1),N("/requestbench")},nt=e=>{go(e)},rt=[{field:"included",width:120,align:"center",headerAlign:"center",disableColumnMenu:!0,sortable:!1,renderHeader:()=>{const e=oe?K:F,t=e.every(T=>T.included),f=e.some(T=>T.included);return s(cs,{checked:t,indeterminate:!t&&f,disabled:u,onChange:T=>{const b=e.map(D=>({...D,included:T.target.checked}));k(oe?Ho(b):Ts(b))}})},renderCell:e=>{var T;const t=oe?K:F,f=t.findIndex(b=>b.id===e.row.id);return s(cs,{checked:((T=t[f])==null?void 0:T.included)||!1,disabled:u,onChange:b=>{const D=[...t];D[f]={...D[f],included:b.target.checked},k(oe?Ho(D):Ts(D))}})}},{field:"lineNumber",headerName:"Sl No",width:100,align:"center",headerAlign:"center",renderCell:e=>{const t=(oe?K:F).findIndex(f=>f.id===e.row.id);return s("div",{children:t+1})}},{field:"controllingArea",headerName:"Controlling Area",width:150,editable:!1},{field:"compCode",headerName:"Company Codes",width:150,editable:!1,renderCell:e=>s("span",{style:{color:"#9e9e9e",pointerEvents:"none",cursor:"default"},children:e.value||""})},{field:"costCenter",headerName:"Cost Center",width:150,editable:!1,renderCell:e=>s("span",{style:{color:"#9e9e9e",pointerEvents:"none",cursor:"default"},children:e.value||""})},{field:"ProfitCtr",headerName:"Profit Center",width:250,editable:!1,renderCell:e=>{const t=e.field,f=Gt||[],T=f.find(b=>b.code===e.row[t]||b.desc===e.row[t])||null;return s(Kt,{options:f,value:T,onChange:b=>Zt(b,e),placeholder:`Select ${e.colDef.headerName}`,disabled:!1,minWidth:"90%",listWidth:235})}},{field:"FuncAreaLong",headerName:"Functional Area",width:250,editable:!1,renderCell:e=>{const t=e.field,f=pt||[],T=f.find(b=>b.code===e.row[t]||b.desc===e.row[t])||null;return s(Kt,{options:f,value:T,onChange:b=>Zt(b,e),placeholder:`Select ${e.colDef.headerName}`,disabled:!1,minWidth:"90%",listWidth:235})}},{field:"Name",headerName:"Short Description",width:200,editable:!1},{field:"Descript",headerName:"Long Description",width:250,editable:!1},{field:"PersonInChargeUser",headerName:"CC User Responsible",width:250,editable:!1},{field:"PersonInCharge",headerName:"CC Person Responsible",width:250,editable:!1},{field:"blockingStatus",headerName:"Blocking Status",width:250,editable:!1,renderCell:()=>{if((g==null?void 0:g.TemplateName)==="All Other Fields")return"Unblock";if((g==null?void 0:g.TemplateName)==="Block")return"Block"}},{field:"createdBy",headerName:"Created By",width:150},{field:"validFrom",headerName:"Valid From",width:150},{field:"validTo",headerName:"Valid To",width:150},{field:"AddrCity",headerName:"City",width:150,editable:!0},{field:"AddrCountry",headerName:"Country/Reg.",width:150,editable:!0},{field:"AddrStreet",headerName:"Street",width:150,editable:!0},{field:"AddrPostlCode",headerName:"Postal Code",width:150,editable:!0},{field:"AddrRegion",headerName:"Region",width:150,editable:!0},{field:"AddrName1",headerName:"Name 1",width:150,editable:!0},{field:"AddrName2",headerName:"Name 2",width:150,editable:!0},{field:"AddrName3",headerName:"Name 3",width:150,editable:!0},{field:"AddrName4",headerName:"Name 4",width:150,editable:!0}],Wt=e=>t=>{var T;const f=t.target.value.toUpperCase();e.api.setEditCellValue({id:e.id,field:e.field,value:f}),Et({uniqueId:e.row.id,filedName:(T=e==null?void 0:e.colDef)==null?void 0:T.headerName,jsonName:e==null?void 0:e.field,currentValue:f,requestId:C==null?void 0:C.RequestId,childRequestId:he,accountNumber:e.row.costCenter})},ao=rt.slice(0,4),gt=rt.slice(4).filter(e=>{var t;return St.includes((t=e.headerName)==null?void 0:t.trim())}).map(e=>{var D;const t=(D=e.headerName)==null?void 0:D.trim(),f=ut.includes(t),T=["Descript","Name","PersonInChargeUser","costCenterName","PersonInCharge","AddrCity","AddrStreet","AddrPostlCode","AddrName1","AddrName2","AddrName3","AddrName4"].includes(e.field),b=40;return{...e,headerName:t,editable:!0,renderHeader:()=>f?A("span",{children:[t," ",s("span",{style:{color:"red"},children:"*"})]}):t,...T&&{renderCell:n=>{var U;return A(X,{sx:{position:"relative",width:"100%"},children:[s(rs,{value:n.value||"",variant:"outlined",size:"small",disabled:u,onChange:Wt(n),fullWidth:!0,InputProps:{style:{maxLength:b,paddingBottom:"5px"}}}),A(X,{sx:{position:"absolute",bottom:0,left:14,fontSize:"8px",color:"blue",pointerEvents:"none"},children:[((U=n.value)==null?void 0:U.length)||0,"/",b]})]})},renderEditCell:n=>{const U=n.value||"",w=U.length,H=w>=b;return A(X,{sx:{position:"relative",width:"100%"},children:[s(rs,{value:U,autoFocus:!0,onFocus:ne=>{ne.target.setSelectionRange(0,ne.target.value.length)},onChange:Wt(n),variant:"outlined",size:"small",fullWidth:!0,placeholder:`Enter ${t}`,inputProps:{maxLength:b+1,style:{paddingBottom:"15px"}}}),s(X,{sx:{position:"absolute",bottom:0,left:14,fontSize:"9px",color:H?"red":"blue",pointerEvents:"none"},children:H?"Max length reached":`${w}/${b}`})]})}}}}),Ft=[...ao,...gt],io=e=>(k(xs(e)),e),Ot=e=>(k(Rs(e)),e),Ht=e=>{Qe(e.row)};i.useEffect(()=>{d&&fe.length>0&&Co()},[d,fe]),i.useEffect(()=>{Xt(),Ut(),uo()},[]),i.useEffect(()=>{eo()},[]);const Xt=()=>{const e=f=>{_e(f.body),k({type:"SET_DROPDOWN",payload:{keyName:"CompanyCode",data:f.body}})},t=f=>{console.log(f)};G(`/${z}/data/getCompanyCodeBasedOnControllingArea?controllingArea=ETCA&rolePrefix=ETP`,"get",e,t)},eo=(e,t)=>{const f={controllingArea:e,companyCode:t,top:"100",skip:"0"},T=D=>{var n,U;qt((n=D.body)==null?void 0:n.list),k({type:"SET_DROPDOWN",payload:{keyName:"ProfitCtr",data:(U=D.body)==null?void 0:U.list}})},b=D=>{console.log(D)};G(`/${Gs}/data/getProfitCentersNo`,"post",T,b,f)},Ut=()=>{const e=f=>{k({type:"SET_DROPDOWN",payload:{keyName:"CostcenterType",data:f.body}})},t=f=>{console.error(f)};G(`/${z}/data/getCostCenterCategory`,"get",e,t)},uo=()=>{const e=f=>{yt(f.body),k({type:"SET_DROPDOWN",payload:{keyName:"FuncAreaLong",data:f.body}})},t=f=>{console.error(f)};G(`/${z}/data/getFunctionalArea`,"get",e,t)},to=()=>{const e=f=>{k({type:"SET_DROPDOWN",payload:{keyName:"Country",data:f.body}})},t=f=>{console.log(f)};G(`/${z}/data/getCountryOrReg`,"get",e,t)};i.useEffect(()=>{to()},[]);const Co=()=>{const e=`${d}`;fe.forEach(t=>{const f={controllingArea:e,companyCode:t,top:"100",skip:"0"};G(`/${z}/data/getCostCentersNo`,"post",T=>{var b;if(Array.isArray((b=T.body)==null?void 0:b.list)){const D=T.body.list.map(n=>({code:n.code,desc:n.desc,companyCode:t}));me(n=>{const U=new Set(n.map(H=>H.code)),w=D.filter(H=>!U.has(H.code));return[...n,...w]})}},T=>console.error("Cost Center fetch failed",T),f)})},To=e=>e.map(t=>{var b,D,n,U,w,H,ne,o,r,l,c,p,x,a,m,h,S,q,re,P,Re,je,lt,ct,jt,Uo,vo,Mo,ko,wo,Po,Bo,Go,qo,$o,Wo,fo,Fo;const T=((b=t==null?void 0:t.controlTabDto)==null?void 0:b.LockIndActualPrimaryCosts)||((D=t==null?void 0:t.controlTabDto)==null?void 0:D.LockIndPlanPrimaryCosts)||((n=t==null?void 0:t.controlTabDto)==null?void 0:n.LockIndActSecondaryCosts)||((U=t==null?void 0:t.controlTabDto)==null?void 0:U.LockIndPlanSecondaryCosts)||((w=t==null?void 0:t.controlTabDto)==null?void 0:w.LockIndActualRevenues)||((H=t==null?void 0:t.controlTabDto)==null?void 0:H.LockIndPlanRevenues)?"Block":"Unblock";return{included:!0,id:t.costCenter,costCenterHeaderID:t==null?void 0:t.costCenterHeaderId,costCenterErrorId:t==null?void 0:t.costCenterErrorId,costCenter:t.costCenter,controllingArea:t.controllingArea,CostcenterType:(ne=t==null?void 0:t.basicDataTabDto)==null?void 0:ne.CostcenterType,FuncAreaLong:(o=t==null?void 0:t.basicDataTabDto)==null?void 0:o.FuncAreaLong,currency:(r=t==null?void 0:t.basicDataTabDto)==null?void 0:r.Currency,profitCtr:(l=t==null?void 0:t.basicDataTabDto)==null?void 0:l.ProfitCtr,compCode:(c=t==null?void 0:t.basicDataTabDto)==null?void 0:c.CompCode,Name:(p=t==null?void 0:t.basicDataTabDto)==null?void 0:p.Name,Descript:((x=t.basicDataTabDto)==null?void 0:x.Descript)||"",PersonInChargeUser:((a=t.basicDataTabDto)==null?void 0:a.PersonInCharge)||"",PersonInCharge:((m=t.basicDataTabDto)==null?void 0:m.PersonInChargeUser)||"",createdBy:((h=t.historyTabDto)==null?void 0:h.ReqCreatedBy)||"",validFrom:(t==null?void 0:t.fromValid)||"",validTo:(t==null?void 0:t.toValid)||"",AddrCity:((S=t.addressTabDto)==null?void 0:S.City)||"",AddrStreet:((q=t.addressTabDto)==null?void 0:q.Street)||"",AddrCountry:((re=t.addressTabDto)==null?void 0:re.Country)||"",AddrRegion:((P=t.addressTabDto)==null?void 0:P.AddrRegion)||"",AddrPostlCode:((Re=t.addressTabDto)==null?void 0:Re.AddrPostlCode)||"",regio:((je=t.addressTabDto)==null?void 0:je.Regio)||"",AddrName1:((lt=t.addressTabDto)==null?void 0:lt.Name1)||"",AddrName2:((ct=t.addressTabDto)==null?void 0:ct.Name2)||"",AddrName3:((jt=t.addressTabDto)==null?void 0:jt.Name3)||"",AddrName4:((Uo=t.addressTabDto)==null?void 0:Uo.Name4)||"",lockIndActualPrimaryCosts:bt((vo=t==null?void 0:t.basicDataTabDto)==null?void 0:vo.CostcenterType,g==null?void 0:g.TemplateName,T,"Actual Primary Costs",(Mo=t==null?void 0:t.controlTabDto)==null?void 0:Mo.LockIndActualPrimaryCosts),lockIndPlanPrimaryCosts:bt((ko=t==null?void 0:t.basicDataTabDto)==null?void 0:ko.CostcenterType,g==null?void 0:g.TemplateName,T,"Plan Primary Costs",(wo=t==null?void 0:t.controlTabDto)==null?void 0:wo.LockIndPlanPrimaryCosts),lockIndActSecondaryCosts:bt((Po=t==null?void 0:t.basicDataTabDto)==null?void 0:Po.CostcenterType,g==null?void 0:g.TemplateName,T,"Actual Secondary Costs",(Bo=t==null?void 0:t.controlTabDto)==null?void 0:Bo.LockIndActSecondaryCosts),lockIndPlanSecondaryCosts:bt((Go=t==null?void 0:t.basicDataTabDto)==null?void 0:Go.CostcenterType,g==null?void 0:g.TemplateName,T,"Plan Secondary Costs",(qo=t==null?void 0:t.controlTabDto)==null?void 0:qo.LockIndPlanSecondaryCosts),lockIndActualRevenues:bt(($o=t==null?void 0:t.basicDataTabDto)==null?void 0:$o.CostcenterType,g==null?void 0:g.TemplateName,T,"Actual Revenue",(Wo=t==null?void 0:t.controlTabDto)==null?void 0:Wo.LockIndActualRevenues),lockIndPlanRevenues:bt((fo=t==null?void 0:t.basicDataTabDto)==null?void 0:fo.CostcenterType,g==null?void 0:g.TemplateName,T,"Plan Revenue",(Fo=t==null?void 0:t.controlTabDto)==null?void 0:Fo.LockIndPlanRevenues)}}),go=e=>{if(!Y.length||!d)return;const t=Y==null?void 0:Y.map(b=>({controllingArea:d,costCenter:b,changedFieldsToCheck:(C==null?void 0:C.fieldName)??(C==null?void 0:C.FieldName)})),f=b=>{if(!(b==null?void 0:b.some(n=>(n==null?void 0:n.statusCode)!==200)))e==="OK"?($e(!1),oe!=="true"&&Ge(!0),vt()):e==="Download"&&Qo();else{const n=b.filter(w=>w.statusCode===400);let U=[];n==null||n.forEach((w,H)=>{var o,r,l,c,p,x,a,m,h,S,q;const ne={id:`${(o=w==null?void 0:w.body)==null?void 0:o.costCenter}_${H}`,objectNo:(r=w==null?void 0:w.body)==null?void 0:r.costCenter,reqId:(p=(c=(l=w==null?void 0:w.body)==null?void 0:l.matchingRequests)==null?void 0:c.map(re=>re==null?void 0:re.matchingRequestHeaderId))==null?void 0:p.filter(Boolean),childReqId:(m=(a=(x=w==null?void 0:w.body)==null?void 0:x.matchingRequests)==null?void 0:a.map(re=>re==null?void 0:re.matchingChildHeaderIdsSet))==null?void 0:m.filter(Boolean),requestedBy:(q=(S=(h=w==null?void 0:w.body)==null?void 0:h.matchingRequests)==null?void 0:S.map(re=>re==null?void 0:re.RequestCreatedBy))==null?void 0:q.filter(Boolean)};U.push(ne)}),ft(U),Lt(!0)}},T=b=>{console.error("Failed to fetch cost center details",b)};G(`/${z}/alter/checkDuplicateCCRequest`,"post",f,T,t)},vt=()=>{if(!Y.length||!d)return;const e={coAreaCCs:Y.map(T=>({costCenter:T,controllingArea:d}))},t=T=>{var ne,o;const b=(T==null?void 0:T.body)||[],D=To(b);k(oe==="true"?Ho(D):Ts(D));let n={};D==null||D.forEach(r=>{const l=(r==null?void 0:r.id)??"";n[l]={...r}});const H={requestHeaderData:{},rowsHeaderData:{},rowsBodyData:n};k(qs(H)),k(Ks(H)),eo((ne=D==null?void 0:D[0])==null?void 0:ne.controllingArea,(o=D==null?void 0:D[0])==null?void 0:o.compCode)},f=T=>{console.error("Failed to fetch cost center details",T)};G(`/${z}/data/getCostCentersData`,"post",t,f,e)};i.useEffect(()=>{if(oe==="true"&&Array.isArray(W)&&W.length>0&&K.length===0){let e="";(W==null?void 0:W.CostCenterID)!==null&&(e=_s(W)),k(Ho(e)),k(Is(e))}},[W,oe]),i.useEffect(()=>{if(j&&Object.keys(j).length!==0&&Array.isArray(W)&&W.length>0){const e=_s(W);k(Ho(e)),k(Is(e))}},[W,j]),i.useEffect(()=>{ke&&$e(!0)},[ke]);const zt=(W??[]).map(e=>{let t={};if(typeof e.changedFields=="object"&&e.changedFields!==null)t=e.changedFields;else if(typeof e.ChangedFields=="string")try{t=JSON.parse(e.ChangedFields)}catch{t={}}const{changedFields:f,ChangedFields:T,...b}=e;return{...b,changedFields:t}});i.useEffect(()=>{if(!zt||zt.length===0)return;const e={};zt.forEach(t=>{e[t.CostCenterID]=t.changedFields||{}}),k(Vs(e))},[W]);const bt=(e,t,f,T,b)=>{if(t==="Block")return"X";if(t==="All Other Fields"){if(f==="Unblock")return b===!0?"X":"";if(f==="Block"){const D=le.find(n=>n.hasOwnProperty(e));if(D&&D[e].includes(T))return"X"}}else if(t==="CC Category & FERC Indicator Change"){if(f==="Unblock"){const D=le.find(n=>n.hasOwnProperty(e));if(D&&D[e].includes(T))return"X"}else if(f==="Block")return"X"}return""},Mt=()=>{const e=Vt(Se,C,Z,j,se,K,F,J),t=T=>{var b,D,n,U;de(!1),(T==null?void 0:T.statusCode)===((b=R)==null?void 0:b.STATUS_200)||(T==null?void 0:T.statusCode)===((D=R)==null?void 0:D.STATUS_201)?(ve({title:v.TITLE,message:T.message,subText:v.SUBTEXT,buttonText:v.BUTTONTEXT,redirectTo:v.REDIRECT}),Ee(!0)):(T==null?void 0:T.statusCode)===((n=R)==null?void 0:n.STATUS_500)||(T==null?void 0:T.statusCode)===((U=R)==null?void 0:U.STATUS_501)?(ve({title:M.TITLE,message:T.message,subText:M.SUBTEXT,buttonText:M.BUTTONTEXT,redirectTo:M.REDIRECT}),Ee(!0)):Ze("Unexpected response received.")},f=T=>{ze(T==null?void 0:T.message,"error"),de(!1)};G(`/${z}/massAction/changeCostCentersSaveAsDraft`,"POST",t,f,e)},oo=(e,t,f)=>{const T=Vt(Se,C,Z,j,se,K,F,t,f,J,st),b=n=>{var U,w,H,ne;de(!1),(n==null?void 0:n.statusCode)===((U=R)==null?void 0:U.STATUS_200)||(n==null?void 0:n.statusCode)===((w=R)==null?void 0:w.STATUS_201)?(ve({title:v.TITLE,message:n.message,subText:v.SUBTEXT,buttonText:v.BUTTONTEXT,redirectTo:v.REDIRECT}),Ee(!0)):(n==null?void 0:n.statusCode)===((H=R)==null?void 0:H.STATUS_500)||(n==null?void 0:n.statusCode)===((ne=R)==null?void 0:ne.STATUS_501)?(ve({title:M.TITLE,message:n.message,subText:M.SUBTEXT,buttonText:M.BUTTONTEXT,redirectTo:M.REDIRECT}),Ee(!0)):Ze("Unexpected response received.")},D=n=>{ze(n==null?void 0:n.message,"error"),de(!1)};G(`/${z}/massAction/costCentersSendToLevel`,"POST",b,D,T)},so=(e,t,f)=>{const T=Vt(Se,C,Z,j,se,K,F,t,f,J,st),b=n=>{var U,w,H,ne;de(!1),(n==null?void 0:n.statusCode)===((U=R)==null?void 0:U.STATUS_200)||(n==null?void 0:n.statusCode)===((w=R)==null?void 0:w.STATUS_200)?(ve({title:v.TITLE,message:n.message,subText:v.SUBTEXT,buttonText:v.BUTTONTEXT,redirectTo:v.REDIRECT}),Ee(!0)):(n==null?void 0:n.statusCode)===((H=R)==null?void 0:H.STATUS_500)||(n==null?void 0:n.statusCode)===((ne=R)==null?void 0:ne.STATUS_501)?(ve({title:M.TITLE,message:n.message,subText:M.SUBTEXT,buttonText:M.BUTTONTEXT,redirectTo:M.REDIRECT}),Ee(!0)):Ze("Unexpected response received.")},D=n=>{ze(n==null?void 0:n.message,"error"),de(!1)};G(`/${z}/massAction/changeCostCentersSaveAsDraft`,"POST",b,D,T)},At=(e,t,f)=>{const T=Vt(Se,C,Z,j,se,K,F,t,f,J,st),b=n=>{var U,w,H,ne;de(!1),(n==null?void 0:n.statusCode)===((U=R)==null?void 0:U.STATUS_200)||(n==null?void 0:n.statusCode)===((w=R)==null?void 0:w.STATUS_201)?(ve({title:v.TITLE,message:n.message,subText:v.SUBTEXT,buttonText:v.BUTTONTEXT,redirectTo:v.REDIRECT}),Ee(!0)):(n==null?void 0:n.statusCode)===((H=R)==null?void 0:H.STATUS_500)||(n==null?void 0:n.statusCode)===((ne=R)==null?void 0:ne.STATUS_501)?(ve({title:M.TITLE,message:n.message,subText:M.SUBTEXT,buttonText:M.BUTTONTEXT,redirectTo:M.REDIRECT}),Ee(!0)):Ze("Unexpected response received.")},D=n=>{ze(n==null?void 0:n.message,"error"),de(!1)};G(`/${z}/massAction/changeCostCentersSubmitForReview`,"POST",b,D,T)},ho=(e,t,f)=>{const T=Vt(Se,C,Z,j,se,K,F,t,f,J,st),b=n=>{var U,w,H,ne;de(!1),(n==null?void 0:n.statusCode)===((U=R)==null?void 0:U.STATUS_200)||(n==null?void 0:n.statusCode)===((w=R)==null?void 0:w.STATUS_201)?(ve({title:v.TITLE,message:n.message,subText:v.SUBTEXT,buttonText:v.BUTTONTEXT,redirectTo:v.REDIRECT}),Ee(!0)):(n==null?void 0:n.statusCode)===((H=R)==null?void 0:H.STATUS_500)||(n==null?void 0:n.statusCode)===((ne=R)==null?void 0:ne.STATUS_501)?(ve({title:M.TITLE,message:n.message,subText:M.SUBTEXT,buttonText:M.BUTTONTEXT,redirectTo:M.REDIRECT}),Ee(!0)):Ze("Unexpected response received.")},D=n=>{ze(n==null?void 0:n.message,"error"),de(!1)};G(`/${z}/massAction/changeCostCentersApprovalSubmit`,"POST",b,D,T)},Ce=(e,t,f)=>{const T=Vt(Se,C,Z,j,se,K,F,t,f,J,st),b=n=>{var U,w,H,ne;de(!1),(n==null?void 0:n.statusCode)===((U=R)==null?void 0:U.STATUS_200)||(n==null?void 0:n.statusCode)===((w=R)==null?void 0:w.STATUS_201)?(ve({title:v.TITLE,message:n.message,subText:v.SUBTEXT,buttonText:v.BUTTONTEXT,redirectTo:v.REDIRECT}),Ee(!0)):(n==null?void 0:n.statusCode)===((H=R)==null?void 0:H.STATUS_500)||(n==null?void 0:n.statusCode)===((ne=R)==null?void 0:ne.STATUS_501)?(ve({title:M.TITLE,message:n.message,subText:M.SUBTEXT,buttonText:M.BUTTONTEXT,redirectTo:M.REDIRECT}),Ee(!0)):Ze("Unexpected response received.")},D=n=>{ze(n==null?void 0:n.message,"error"),de(!1)};G(`/${z}/massAction/validateMassCostCenter`,"POST",b,D,T)},kt=(e,t,f)=>{const T=Vt(Se,C,Z,j,se,K,F,t,f,J,st),b=n=>{var U,w,H,ne;de(!1),(n==null?void 0:n.statusCode)===((U=R)==null?void 0:U.STATUS_200)||(n==null?void 0:n.statusCode)===((w=R)==null?void 0:w.STATUS_201)?(ve({title:v.TITLE,message:n.message,subText:v.SUBTEXT,buttonText:v.BUTTONTEXT,redirectTo:v.REDIRECT}),Ee(!0)):(n==null?void 0:n.statusCode)===((H=R)==null?void 0:H.STATUS_500)||(n==null?void 0:n.statusCode)===((ne=R)==null?void 0:ne.STATUS_501)?(ve({title:M.TITLE,message:n.message,subText:M.SUBTEXT,buttonText:M.BUTTONTEXT,redirectTo:M.REDIRECT}),Ee(!0)):Ze("Unexpected response received.")},D=n=>{ze(n==null?void 0:n.message,"error"),de(!1)};G(e==="VALIDATE"?`/${z}/massAction/validateMassCostCenter`:`/${z}/massAction/changeCostCentersApproved`,"POST",b,D,T)},no=()=>{Je(!0)},ro=()=>{Je(!1)},Qo=()=>{We(!0)},po=()=>{We(!1),Q("systemGenerated")},Jo=e=>{var t;Q((t=e==null?void 0:e.target)==null?void 0:t.value)},Zo=()=>{xt==="systemGenerated"&&(us(),po()),xt==="mailGenerated"&&(Cs(),po())},us=()=>{var T,b;Xe("Please wait 1-2 minutes while real-time data are downloaded into the template. Thank you for your patience."),de(!0),$e(!1),E(!1),L||N((T=Ke)==null?void 0:T.REQUEST_BENCH);let e={coAreaCCs:Y.map(D=>({costCenter:D,controllingArea:d})),requestId:(g==null?void 0:g.RequestId)||(C==null?void 0:C.requestId)||"",templateHeaders:g!=null&&g.FieldName?(b=g.FieldName)==null?void 0:b.join("$^$"):"",templateName:g!=null&&g.TemplateName?g.TemplateName:"",dtName:"MDG_CHANGE_TEMPLATE_DT",version:"v6"};const t=D=>{var w;if((D==null?void 0:D.size)==0){de(!1),Xe(""),Ls((w=Es)==null?void 0:w.NO_DATA_FOUND,"error",{position:"top-center",largeWidth:!0}),setTimeout(()=>{var H;N((H=Ke)==null?void 0:H.REQUEST_BENCH)},2600);return}const n=URL.createObjectURL(D),U=document.createElement("a");U.href=n,U.setAttribute("download",`${g==null?void 0:g.TemplateName}_Mass Change.xlsx`),document.body.appendChild(U),U.click(),document.body.removeChild(U),URL.revokeObjectURL(n),de(!1),Xe(""),at(!0),Ie(`${g==null?void 0:g.TemplateName}_Mass Change.xlsx has been downloaded successfully.`),Nt("success"),no(),setTimeout(()=>{var H;N((H=Ke)==null?void 0:H.REQUEST_BENCH)},2600)},f=()=>{var D;de(!1),Xe(""),Ls((D=Es)==null?void 0:D.ERR_DOWNLOADING_EXCEL,"error",{position:"top-center"}),setTimeout(()=>{var n;N((n=Ke)==null?void 0:n.REQUEST_BENCH)},2600)};G(`/${z}/excel/downloadExcelWithData`,"postandgetblob",t,f,e)},Cs=()=>{var b,D,n;de(!0);let e=((b=Templates[C==null?void 0:C.TemplateName])==null?void 0:b.map(U=>U.key))||[],t={};activeTab===0?t={materialDetails:[e.reduce((U,w)=>(U[w]=convertedValues!=null&&convertedValues[w]?convertedValues==null?void 0:convertedValues[w]:"",U),{})],templateHeaders:C!=null&&C.FieldName?(D=C.FieldName)==null?void 0:D.join("$^$"):"",requestId:L||(C==null?void 0:C.RequestId)||"",templateName:C!=null&&C.TemplateName?C.TemplateName:"",dtName:"MDG_MAT_CHANGE_TEMPLATE",version:"v4",rolePrefix:""}:t={materialDetails:[e.reduce((U,w)=>(U[w]=rowsOfMaterialData.map(H=>{var ne;return(ne=H[w])==null?void 0:ne.trim()}).filter(H=>H!=="").join(",")||"",U),{})],templateHeaders:C!=null&&C.FieldName?(n=C.FieldName)==null?void 0:n.join("$^$"):"",requestId:L||(C==null?void 0:C.RequestId)||"",templateName:C!=null&&C.TemplateName?C.TemplateName:"",dtName:"MDG_MAT_CHANGE_TEMPLATE",version:"v4",rolePrefix:""};const f=()=>{de(!1),Xe(""),at(!0),Ie("Download has been started. You will get the Excel file via email."),Nt("success"),no(),setTimeout(()=>{var U;N((U=Ke)==null?void 0:U.REQUEST_BENCH)},2600)},T=()=>{de(!1),at(!0),Ie("Oops! Something went wrong. Please try again later."),Nt("danger"),no(),setTimeout(()=>{var U;N((U=Ke)==null?void 0:U.REQUEST_BENCH)},2600)};G(`/${z}/excel/downloadExcelWithDataInMail`,"postandgetblob",f,T,t)};return A("div",{children:[_t&&s(ls,{openSnackBar:It,alertMsg:Jt,alertType:Tt,handleSnackBarClose:ro}),s($s,{open:it,onClose:()=>Ee(!1),title:mt.title,message:mt.message,subText:mt.subText,buttonText:mt.buttonText,redirectTo:mt.redirectTo}),s(ls,{openSnackBar:It,alertMsg:$t,handleSnackBarClose:ro,alertType:Tt}),((g==null?void 0:g.TemplateName)||ke)&&A(So,{children:[F.length===0&&!oe&&Object.keys(j||{}).length===0&&A(So,{children:[A(Vo,{open:Le,TransitionComponent:ir,keepMounted:!0,onClose:(e,t)=>{t==="backdropClick"||t==="escapeKeyDown"||B()},maxWidth:"sm",fullWidth:!0,children:[A(X,{sx:{backgroundColor:"#e3f2fd",padding:"1rem 1.5rem",display:"flex",alignItems:"center"},children:[s(Ds,{color:"primary",sx:{marginRight:"0.5rem"}}),s(qe,{variant:"h6",component:"div",color:"primary",children:"Select Cost Center for Change"})]}),A(bo,{sx:{padding:"1.5rem 1.5rem 1rem"},children:[s(X,{sx:{marginBottom:"1rem"},children:s(Eo,{param:{key:"controllingArea",label:"Controlling Area"},dropDownData:{controllingArea:[{code:"ETCA",desc:"ETCA"}]},selectedValues:{controllingArea:d?[{code:d}]:[]},handleSelectionChange:(e,t)=>{Oe(t.length>0?t[0].code||t[0]:"")},formatOptionLabel:e=>e.code&&e.desc?`${e.code} - ${e.desc}`:e.code||"",singleSelect:!0,errors:{}})}),s(X,{sx:{marginBottom:"1rem"},children:s(Eo,{param:{key:"companyCode",label:"Company Code"},dropDownData:{companyCode:te||[]},selectedValues:{companyCode:fe.map(e=>(te==null?void 0:te.find(t=>t.code===e))||{code:e})},handleSelectAll:()=>{fe.length===(te==null?void 0:te.length)?Pe([]):Pe((te==null?void 0:te.map(e=>e.code))||[])},handleSelectionChange:(e,t)=>{Pe(t.map(f=>typeof f=="string"?f:f.code||f))},formatOptionLabel:e=>e.code&&e.desc?`${e.code} - ${e.desc}`:e.code||"",isSelectAll:!0,errors:{}})}),s(X,{sx:{marginBottom:"1rem"},children:s(Eo,{param:{key:"costCenter",label:"Cost Center"},dropDownData:{costCenter:Be.filter(e=>fe.includes(e.companyCode)).map(e=>({code:e.code,desc:e.desc}))},selectedValues:{costCenter:Y.filter(e=>Be.some(t=>t.code===e&&fe.includes(t.companyCode))).map(e=>({code:e}))},handleSelectAll:()=>{const e=Be.filter(t=>fe.includes(t.companyCode));Y.length===e.length?Fe([]):Fe(e.map(t=>t.code))},handleSelectionChange:(e,t)=>{const f=t.map(T=>typeof T=="string"?T:T.code||T);Fe(f)},formatOptionLabel:e=>typeof e=="string"?e:e.code||e,isSelectAll:!0,errors:{}})})]}),s(Ao,{sx:{padding:"0.5rem 1.5rem",display:"flex",alignItems:"center"},children:A(X,{sx:{display:"flex",gap:1},children:[s(Me,{onClick:B,color:"error",variant:"outlined",sx:{height:36,minWidth:"3.5rem",textTransform:"none",borderColor:"#cc3300",fontWeight:500},children:"Cancel"}),(g==null?void 0:g.RequestType)!==((_o=ee)==null?void 0:_o.CHANGE_WITH_UPLOAD)&&s(Me,{onClick:()=>{nt("OK")},variant:"contained",sx:{height:36,minWidth:"3.5rem",backgroundColor:"#3B30C8",textTransform:"none",fontWeight:500,"&:hover":{backgroundColor:"#2c278f"}},children:"OK"}),(g==null?void 0:g.RequestType)===((Io=ee)==null?void 0:Io.CHANGE_WITH_UPLOAD)&&s(Me,{onClick:()=>{nt("Download")},variant:"contained",sx:{height:36,minWidth:"3.5rem",backgroundColor:"#3B30C8",textTransform:"none",fontWeight:500,"&:hover":{backgroundColor:"#2c278f"}},children:"Download"})]})})]}),s(As,{onDownloadTypeChange:Zo,open:ht,downloadType:xt,handleDownloadTypeChange:Jo,onClose:po}),s(Ko,{blurLoading:Bt,loaderMessage:tt})]}),ot&&A(X,{sx:{mt:4,px:4},children:[s(qe,{variant:"h5",sx:{fontWeight:600,mb:2},children:"Cost Center List"}),s(Do,{elevation:3,sx:{borderRadius:3,overflow:"hidden",border:"1px solid #e0e0e0",backgroundColor:"#fafbff"},children:s(X,{sx:{p:2},children:s(ns,{rows:F,columns:Ft,pageSize:10,tempheight:"50vh",getRowIdValue:"id",editMode:"row",status_onRowSingleClick:!0,callback_onRowSingleClick:Ht,processRowUpdate:e=>io(e),experimentalFeatures:{newEditingApi:!0},isCellEditable:e=>!["costCenter","companyCode"].includes(e.field),getRowClassName:e=>(ae==null?void 0:ae.id)===e.row.id?"Mui-selected":""})})}),A(X,{sx:{display:"flex",justifyContent:"right",mt:3,gap:2},children:[s(Me,{variant:"contained",color:"primary",onClick:Mt,children:"Save as draft"}),s(Me,{variant:"contained",color:"primary",onClick:Ce,children:"Validate"}),s(Me,{variant:"contained",color:"secondary",onClick:At,disabled:!Ae,children:"Submit"})]})]})]}),Ue&&s(er,{duplicateFieldsArr:Rt,moduleName:(xo=Ve)==null?void 0:xo.CC,open:Ue,onClose:()=>Lt(!1)}),A(So,{children:[((Ro=K==null?void 0:K[0])==null?void 0:Ro.costCenterHeaderID)===null&&oe==="true"&&A(So,{children:[A(Vo,{open:Le,onClose:(e,t)=>{t==="backdropClick"||t==="escapeKeyDown"||B()},maxWidth:"sm",fullWidth:!0,children:[A(X,{sx:{backgroundColor:"#e3f2fd",padding:"1rem 1.5rem",display:"flex",alignItems:"center"},children:[s(Ds,{color:"primary",sx:{marginRight:"0.5rem"}}),s(qe,{variant:"h6",component:"div",color:"primary",children:"Select Cost Center for Change"})]}),A(bo,{sx:{padding:"1.5rem 1.5rem 1rem"},children:[s(X,{sx:{marginBottom:"1rem"},children:s(Eo,{param:{key:"controllingArea",label:"Controlling Area"},dropDownData:{controllingArea:[{code:"ETCA",desc:"ETCA"}]},selectedValues:{controllingArea:d?[{code:d}]:[]},handleSelectionChange:(e,t)=>{Oe(t.length>0?t[0].code||t[0]:"")},formatOptionLabel:e=>e.code&&e.desc?`${e.code} - ${e.desc}`:e.code||"",singleSelect:!0,errors:{}})}),s(X,{sx:{marginBottom:"1rem"},children:s(Eo,{param:{key:"companyCode",label:"Company Code"},dropDownData:{companyCode:te||[]},selectedValues:{companyCode:fe.map(e=>(te==null?void 0:te.find(t=>t.code===e))||{code:e})},handleSelectAll:()=>{fe.length===(te==null?void 0:te.length)?Pe([]):Pe((te==null?void 0:te.map(e=>e.code))||[])},handleSelectionChange:(e,t)=>{Pe(t.map(f=>typeof f=="string"?f:f.code||f))},formatOptionLabel:e=>e.code&&e.desc?`${e.code} - ${e.desc}`:e.code||"",isSelectAll:!0,errors:{}})}),s(X,{sx:{marginBottom:"1rem"},children:s(Eo,{param:{key:"costCenter",label:"Cost Center"},dropDownData:{costCenter:Be.filter(e=>fe.includes(e.companyCode)).map(e=>({code:e.code,desc:e.desc}))},selectedValues:{costCenter:Y.filter(e=>Be.some(t=>t.code===e&&fe.includes(t.companyCode))).map(e=>({code:e}))},handleSelectAll:()=>{const e=Be.filter(t=>fe.includes(t.companyCode));Y.length===e.length?Fe([]):Fe(e.map(t=>t.code))},handleSelectionChange:(e,t)=>{const f=t.map(T=>typeof T=="string"?T:T.code||T);Fe(f)},formatOptionLabel:e=>typeof e=="string"?e:e.code||e,isSelectAll:!0,errors:{}})})]}),s(Ao,{sx:{padding:"0.5rem 1.5rem",display:"flex",alignItems:"center"},children:A(X,{sx:{display:"flex",gap:1},children:[s(Me,{onClick:B,color:"error",variant:"outlined",sx:{height:36,minWidth:"3.5rem",textTransform:"none",borderColor:"#cc3300",fontWeight:500},children:"Cancel"}),(g==null?void 0:g.RequestType)!==((Lo=ee)==null?void 0:Lo.CHANGE_WITH_UPLOAD)&&s(Me,{onClick:()=>nt("OK"),variant:"contained",sx:{height:36,minWidth:"3.5rem",backgroundColor:"#3B30C8",textTransform:"none",fontWeight:500,"&:hover":{backgroundColor:"#2c278f"}},children:"OK"}),(g==null?void 0:g.RequestType)===((Oo=ee)==null?void 0:Oo.CHANGE_WITH_UPLOAD)&&s(Me,{onClick:()=>{Qo()},variant:"contained",sx:{height:36,minWidth:"3.5rem",backgroundColor:"#3B30C8",textTransform:"none",fontWeight:500,"&:hover":{backgroundColor:"#2c278f"}},children:"Download"})]})})]}),s(As,{onDownloadTypeChange:Zo,open:ht,downloadType:xt,handleDownloadTypeChange:Jo,onClose:po}),s(Ko,{blurLoading:Bt,loaderMessage:tt})]}),oe==="true"&&A(X,{sx:{marginTop:"20px",padding:"16px"},children:[s(qe,{variant:"h5",gutterBottom:!0,children:"Cost Center Lists"}),s(Do,{elevation:4,sx:{p:0,borderRadius:2,overflow:"hidden",mt:"50px"},children:s("div",{children:s(ns,{rows:K,columns:Ft,pageSize:10,tempheight:"50vh",getRowIdValue:"id",status_onRowSingleClick:!0,editMode:"cell",callback_onRowSingleClick:Ht,processRowUpdate:e=>Ot(e),experimentalFeatures:{newEditingApi:!0},isCellEditable:e=>!["costCenter","companyCode"].includes(e.field),getRowClassName:e=>(ae==null?void 0:ae.id)===e.row.id?"Mui-selected":""})})}),s(X,{sx:{display:"flex",justifyContent:"right",mt:3,gap:2},children:s(is,{handleSaveAsDraft:Mt,handleSubmitForReview:At,handleSubmitForApprove:ho,handleSendBack:oo,handleRejectAndCancel:so,handleValidateAndSyndicate:kt,validateAllRows:Ce,filteredButtons:pe,moduleName:y,showWfLevels:V,selectedLevel:J,workFlowLevels:Ne,setSelectedLevel:ce})})]}),j&&Object.keys(j).length>0&&A(X,{sx:{marginTop:"20px",padding:"16px"},children:[s(qe,{variant:"h5",gutterBottom:!0,children:"Cost Center Lists"}),s(Do,{elevation:4,sx:{p:0,borderRadius:2,overflow:"hidden",mt:"50px"},children:s("div",{children:s(ns,{rows:K,columns:Ft,pageSize:10,tempheight:"50vh",getRowIdValue:"id",status_onRowSingleClick:!0,editMode:"cell",callback_onRowSingleClick:Ht,processRowUpdate:e=>Ot(e),experimentalFeatures:{newEditingApi:!0},isCellEditable:e=>!["costCenter","companyCode"].includes(e.field),getRowClassName:e=>(ae==null?void 0:ae.id)===e.row.id?"Mui-selected":""})})}),s(X,{sx:{display:"flex",justifyContent:"right",mt:3,gap:2},children:s(is,{handleSaveAsDraft:Mt,handleSubmitForReview:At,handleSubmitForApprove:ho,handleSendBack:oo,handleRejectAndCancel:so,handleValidateAndSyndicate:kt,validateAllRows:Ce,filteredButtons:pe,moduleName:y,showWfLevels:V,selectedLevel:J,workFlowLevels:Ne,setSelectedLevel:ce})})]})]})]})},Xs=oe=>{window.location.hash.split("/");const{customError:W}=ys(),ke=$(N=>N.applicationConfig),E=Qt(),y=$(N=>N==null?void 0:N.userManagement.taskData);return{getChangeTemplate:N=>{const k={decisionTableId:null,decisionTableName:"MDG_CHANGE_TEMPLATE_DT",version:"v6",rulePolicy:null,validityDate:null,conditions:[{"MDG_CONDITIONS.MDG_MODULE":oe,"MDG_CONDITIONS.MDG_MAT_ROLE":y!=null&&y.ATTRIBUTE_5?y==null?void 0:y.ATTRIBUTE_5:Ys.REQ_INITIATE_FIN}],systemFilters:null,systemOrders:null,filterString:null},F=g=>{var C,pe,j;if(g.statusCode===R.STATUS_200){const ie=((j=(pe=(C=g==null?void 0:g.data)==null?void 0:C.result)==null?void 0:pe[0])==null?void 0:j.MDG_CHANGE_TEMPLATE_ACTION_TYPE)||[];E(Qs(ie));const we=[...new Set(ie.map(L=>L==null?void 0:L.MDG_CHANGE_TEMPLATE_NAME).filter(Boolean))].map(L=>({code:L}));E(Pt({keyName:"TemplateName",data:we}))}else return W("Failed to fetch data"),[]},K=g=>{W(g)},be=ke.environment==="localhost"?Ye.INVOKE_RULES.LOCAL:Ye.INVOKE_RULES.PROD;G(`/${co}${be}`,"post",F,K,k)}}},dr=()=>{const he=window.location.hash.split("/"),W=he[he.length-1];console.log("activeLocation",W);const{customError:ke}=ys();$(N=>{var k,F;return(F=(k=N==null?void 0:N.costCenter)==null?void 0:k.payload)==null?void 0:F.requestHeaderData});const E=$(N=>N.applicationConfig),y=Qt();return{getLockIndicator:()=>{const N={decisionTableId:null,decisionTableName:"MDG_CC_CONTROL_TAB_FIELDS_DT",version:"v1",rulePolicy:null,validityDate:null,conditions:[{}],systemFilters:null,systemOrders:null,filterString:null},k=be=>{var g,C,pe;if(be.statusCode===R.STATUS_200){console.log("lockIndicatorList",be);const j=(pe=(C=(g=be==null?void 0:be.data)==null?void 0:g.result)==null?void 0:C[0])==null?void 0:pe.conditions.map((ie,we)=>{var Le,$e,d,Oe,ae;console.log("conditionnn",ie);const L=ie==null?void 0:ie["MDG_CONDITIONS.MDG_CC_COST_CENTER_CATEGORY"],se=((ae=(Oe=(d=($e=(Le=be==null?void 0:be.data)==null?void 0:Le.result)==null?void 0:$e[0])==null?void 0:d.MDG_CC_CONTROL_TAB_FIELDS_ACTION_TYPE)==null?void 0:Oe[we])==null?void 0:ae.MDG_CC_CONTROL_TAB_FIELDS.split(",").map(Qe=>Qe.trim()))||"";return{[L]:se}});console.log("transformedDataLI",j),y(Js(j))}else return ke("Failed to fetch data"),[]},F=be=>{ke(be)},K=E.environment==="localhost"?Ye.INVOKE_RULES.LOCAL:Ye.INVOKE_RULES.PROD;G(`/${co}${K}`,"post",k,F,N)}}},ur=()=>{const oe=$(E=>{var y,u;return(u=(y=E==null?void 0:E.costCenter)==null?void 0:y.payload)==null?void 0:u.requestHeaderData}),he=$(E=>E.applicationConfig),W=Qt();return{getRequestHeaderTemplateCc:()=>{let E={decisionTableId:null,decisionTableName:"MDG_FMD_REQUEST_HEADER_CONFIG",version:"v2",rulePolicy:null,validityDate:null,conditions:[{"MDG_CONDITIONS.MDG_MAT_SCENARIO":(oe==null?void 0:oe.RequestType)||"Create","MDG_CONDITIONS.MDG_MAT_MODULE_NAME":"Cost Center"}],systemFilters:null,systemOrders:null,filterString:null};const y=N=>{var k,F;if(N.statusCode===200){const g={"Header Data":((F=(k=N==null?void 0:N.data)==null?void 0:k.result[0])==null?void 0:F.MDG_MAT_REQUEST_HEADER_CONFIG).sort((C,pe)=>C.MDG_MAT_SEQUENCE_NO-pe.MDG_MAT_SEQUENCE_NO).map(C=>({fieldName:C.MDG_MAT_UI_FIELD_NAME,sequenceNo:C.MDG_MAT_SEQUENCE_NO,fieldType:C.MDG_MAT_FIELD_TYPE,maxLength:C.MDG_MAT_MAX_LENGTH,value:C.MDG_MAT_DEFAULT_VALUE,visibility:C.MDG_MAT_VISIBILITY,jsonName:C.MDG_MAT_JSON_FIELD_NAME}))};W(Zs({tab:"Request Header",data:g})),W(en(g))}},u=N=>{console.log(N)};he.environment==="localhost"?G(`/${co}${Ye.INVOKE_RULES.LOCAL}`,"post",y,u,E):G(`/${co}${Ye.INVOKE_RULES.PROD}`,"post",y,u,E)}}},Cr=({downloadClicked:oe,setDownloadClicked:he,setIsSecondTabEnabled:W,setIsAttachmentTabEnabled:ke})=>{const{t:E}=Ns(),y=Qt(),u=$(I=>{var V,le;return(le=(V=I==null?void 0:I.costCenter)==null?void 0:V.payload)==null?void 0:le.requestHeaderData}),N=$(I=>{var V;return(V=I==null?void 0:I.request)==null?void 0:V.requestHeader}),k=$(I=>{var V;return(V=I==null?void 0:I.userManagement)==null?void 0:V.userData}),F=$(I=>{var V;return(V=I==null?void 0:I.tabsData)==null?void 0:V.requestHeaderData}),K=$(I=>{var V,le;return((le=(V=I==null?void 0:I.AllDropDown)==null?void 0:V.dropDown)==null?void 0:le.FieldName)||[]}),be=$(I=>{var V,le;return((le=(V=I==null?void 0:I.AllDropDown)==null?void 0:V.dropDown)==null?void 0:le.TemplateName)||[]});let g=[{code:"US",desc:"USA"},{code:"EU",desc:"Europe"}];const C=Yo(),j=new URLSearchParams(C.search).get("RequestId"),ie=`/Date(${Date.now()})/`,[we,L]=i.useState(""),[se,Le]=i.useState(""),[$e,d]=i.useState(""),[Oe,ae]=i.useState(""),[Qe,te]=i.useState([]),_e=ds(),[ht,We]=i.useState(!1),[fe,Pe]=i.useState("success"),[Be,me]=i.useState(!1),[Y,Fe]=i.useState("systemGenerated"),[_t,at]=i.useState(),[It,Je]=i.useState(!1),[Jt,Ie]=i.useState(!1),[Bt,de]=i.useState(!1),[tt,Xe]=i.useState(""),[xt,Q]=i.useState(""),[pt,yt]=i.useState(!1),{getChangeTemplate:Gt}=Xs("Cost Center"),{getRequestHeaderTemplateCc:qt}=ur(),{getLockIndicator:Tt}=dr(),Nt=[{code:"Create",tooltip:"Create New CostCenter Directly in Application"},{code:"Change",tooltip:"Modify Existing CostCenter Directly in Application"},{code:"Create with Upload",tooltip:"Create New CostCenter with Excel Upload"},{code:"Change with Upload",tooltip:"Modify Existing CostCenter with Excel Upload"}],$t=[{code:"High",desc:""},{code:"Medium",desc:""},{code:"Low",desc:""}];y(Ct({keyName:"RequestStatus",data:"DRAFT"})),y(Ct({keyName:"ReqCreatedBy",data:k==null?void 0:k.user_id})),i.useEffect(()=>{var I;if(oe){if((u==null?void 0:u.RequestType)===ee.CREATE_WITH_UPLOAD){yt(!0);return}if((u==null?void 0:u.RequestType)===((I=ee)==null?void 0:I.CHANGE_WITH_UPLOAD)){Ie(!0);return}}},[oe]);const Ze=()=>{var V,le;let I=!0;return u&&((V=F[Object.keys(F)])!=null&&V.length)?(le=F[Object.keys(F)[0]])==null||le.forEach(Te=>{var Ae;(u[Te.jsonName]===void 0||u[Te.jsonName]===null||u[Te.jsonName]==="")&&Te.visibility===((Ae=sn)==null?void 0:Ae.MANDATORY)&&(I=!1)}):I=!1,I};i.useEffect(()=>{((u==null?void 0:u.RequestType)==="Change"||(u==null?void 0:u.RequestType)==="Change with Upload")&&(Gt(),Tt())},[u==null?void 0:u.RequestType]),i.useEffect(()=>{qt()},[u==null?void 0:u.RequestType]),i.useEffect(()=>{const I=we&&se&&$e.trim()!=="",V=we!=="Change"||we==="Change with Upload"||Oe&&Qe.length>0;We(I&&V)},[we,se,$e,Oe,Qe]);const Rt=()=>{const I=new Date(u==null?void 0:u.ReqCreatedOn).getTime(),V=K.map(Z=>Z.code||"").join(", ")||"";Ie(!1);const le={RequestId:"",ReqCreatedBy:"<EMAIL>",ReqCreatedOn:I?`/Date(${I})/`:ie,ReqUpdatedOn:I?`/Date(${I})/`:ie,RequestType:(u==null?void 0:u.RequestType)||"",RequestDesc:(u==null?void 0:u.RequestDesc)||"",RequestStatus:"DRAFT",RequestPriority:(u==null?void 0:u.RequestPriority)||"",FieldName:V,TemplateName:(u==null?void 0:u.TemplateName)||"",ChangeCategory:"",IsHierarchyGroup:!1,Region:u==null?void 0:u.Region},Te=Z=>{var ot,Ge,ze;if(Je(!0),at(`Request Header Created Successfully! Request ID: ${(ot=Z==null?void 0:Z.body)==null?void 0:ot.requestId}`),me(!1),Pe("success"),Ue(),ke(!0),y(nn(Z==null?void 0:Z.body)),y(Ss(Z.body)),y(rn({keyName:ln.REQUEST_ID,data:(Ge=Z==null?void 0:Z.body)==null?void 0:Ge.requestId})),(u==null?void 0:u.RequestType)===ee.CREATE_WITH_UPLOAD||(u==null?void 0:u.RequestType)===ee.EXTEND_WITH_UPLOAD){yt(!0);return}if((u==null?void 0:u.RequestType)===((ze=ee)==null?void 0:ze.CHANGE_WITH_UPLOAD)){Ie(!0);return}setTimeout(()=>{y(zo(1)),W(!0)},2500)},Ae=Z=>{console.error("APIError",Z),Je(!0),Pe("error"),at("Error occured while saving Request Header"),Ue()};G(`/${z}/massAction/createRequestHeader`,"post",Te,Ae,le)},ft=()=>{var I;he(!1),yt(!1),Fe("systemGenerated"),j||_e((I=Ke)==null?void 0:I.REQUEST_BENCH)},Ue=()=>{de(!0)},Lt=()=>{de(!1)},mt=I=>{var V;Fe((V=I==null?void 0:I.target)==null?void 0:V.value)},ve=()=>{Y==="systemGenerated"&&(it(),ft()),Y==="mailGenerated"&&(Ee(),ft())},it=()=>{Xe("Please wait 1-2 minutes while real-time data are downloaded into the template. Thank you for your patience."),Q(!0);let I={scenario:(u==null?void 0:u.RequestType)||(N==null?void 0:N.requestType)||"",rolePrefix:"ETP",dtName:"MDG_CC_FIELD_CONFIG",version:"v3",requestId:(u==null?void 0:u.RequestId)||(N==null?void 0:N.requestId)||""};const V=Ae=>{if((Ae==null?void 0:Ae.size)==0){Q(!1),Xe(""),Je(!0),at("No data found for the selected criteria."),Pe("danger"),Ue();return}const Se=URL.createObjectURL(Ae),Z=document.createElement("a");Z.href=Se,Z.setAttribute("download","Mass_Create.xlsx"),document.body.appendChild(Z),Z.click(),document.body.removeChild(Z),URL.revokeObjectURL(Se),Q(!1),Xe(""),Je(!0),at(`${u!=null&&u.TemplateName?`${u==null?void 0:u.TemplateName}_Mass Change`:"Mass_Create"}.xlsx has been downloaded successfully.`),Pe("success"),Ue(),setTimeout(()=>{_e("/requestBench")},2600)},le=()=>{Q(!1)},Te=`/${z}${(u==null?void 0:u.RequestType)===ee.EXTEND_WITH_UPLOAD?Ye.EXCEL.DOWNLOAD_EXCEL_FOR_EXTEND:Ye.EXCEL.DOWNLOAD_EXCEL}`;G(Te,"postandgetblob",V,le,I)},Ee=()=>{Q(!0);let I={region:u==null?void 0:u.Region,scenario:u==null?void 0:u.RequestType,matlType:"ALL",dtName:"MDG_MAT_MASS_CREATE_EXTEND_FIELD_CONFIG",version:"v1",rolePrefix:"",requestId:u!=null&&u.RequestId?u==null?void 0:u.RequestId:""};const V=()=>{var Ae;Q(!1),Xe(""),Je(!0),at((Ae=cn)==null?void 0:Ae.DOWNLOAD_MAIL_INITIATED),Pe("success"),Ue(),setTimeout(()=>{var Se;_e((Se=Ke)==null?void 0:Se.REQUEST_BENCH)},2600)},le=()=>{var Ae;Q(!1),Je(!0),at((Ae=Es)==null?void 0:Ae.ERR_DOWNLOADING_EXCEL),Pe("danger"),Ue(),setTimeout(()=>{var Se;_e((Se=Ke)==null?void 0:Se.REQUEST_BENCH)},2600)},Te=`/${destination_MaterialMgmt}${(u==null?void 0:u.RequestType)===ee.EXTEND_WITH_UPLOAD?Ye.EXCEL.DOWNLOAD_EXCEL_FOR_EXTEND_MAIL:Ye.EXCEL.DOWNLOAD_EXCEL_MAIL}`;G(Te,"post",V,le,I)};return s("div",{children:A(on,{spacing:2,children:[Object.entries(F).map(([I,V])=>A(Dt,{item:!0,md:12,sx:{backgroundColor:"white",maxHeight:"max-content",height:"max-content",borderRadius:"8px",border:"1px solid #E0E0E0",mt:.25,boxShadow:"0px 2px 14px 0px rgba(48, 38, 185, 0.10)",...tn},children:[s(qe,{sx:{fontSize:"12px",fontWeight:"700",paddingBottom:"10px"},children:I}),s(X,{children:s(Dt,{container:!0,spacing:1,children:V.filter(le=>le.visibility!=="Hidden").sort((le,Te)=>le.sequenceNo-Te.sequenceNo).map(le=>s(Yn,{isHeader:!0,field:le,dropDownData:{RequestType:Nt,RequestPriority:$t,TemplateName:be,FieldName:K,Region:g},disabled:j,requestHeader:!0,module:"CostCenter"},le.id))})}),!j&&!(N!=null&&N.requestId)&&s(X,{sx:{display:"flex",justifyContent:"flex-end",marginTop:"20px"},children:s(Me,{variant:"contained",color:"primary",disabled:!Ze(),onClick:Rt,children:E("Save Request Header")})})]},I)),s(Ko,{blurLoading:xt,loaderMessage:tt}),It&&s(ls,{openSnackBar:Bt,alertMsg:_t,alertType:fe,handleSnackBarClose:Lt}),Jt&&s(Hs,{downloadClicked:oe,setDownloadClicked:he}),s(As,{onDownloadTypeChange:ve,open:pt,downloadType:Y,handleDownloadTypeChange:mt,onClose:ft})]})})},Tr=({reqBench:oe,module:he,isDisabled:W,fieldDisable:ke})=>{var t,f,T,b,D,n,U,w,H,ne;const E=Qt();ds();const{t:y}=Ns(),{customError:u}=ys();let N=$(o=>o==null?void 0:o.userManagement.taskData);const k=$(o=>o.costCenter.selectedRowId),F=$(o=>(o.costCenter.costCenterTabs||[]).filter(l=>l.tab!=="Initial Screen")),K=Yo(),g=new URLSearchParams(K.search).get("RequestId"),C=$(o=>o.request.requestHeader),pe=$(o=>{var r;return((r=o.AllDropDown)==null?void 0:r.dropDown)||{}}),{loading:j,error:ie,fetchCostCenterFieldConfig:we}=sr(),L=$(o=>o.costCenter.payload.rowsHeaderData),se=$(o=>o.costCenter.payload),Le=$(o=>o.payload.dynamicKeyValues),$e=$(o=>o.payload.filteredButtons),[d,Oe]=i.useState(null),[ae,Qe]=i.useState(0),[te,_e]=i.useState([]),[ht,We]=i.useState([]),[fe,Pe]=i.useState(!1),[Be,me]=i.useState([]),[Y,Fe]=i.useState(!1),[_t,at]=i.useState(!1),[It,Je]=i.useState(!1),[Jt,Ie]=i.useState(""),[Bt,de]=i.useState("success"),[tt,Xe]=i.useState(!1),[xt,Q]=i.useState(!1),[pt,yt]=i.useState(""),{getButtonsDisplayGlobal:Gt,showWfLevels:qt}=Ws(),[Tt,Nt]=i.useState(!1),[$t,Ze]=i.useState({}),[Rt,ft]=i.useState({}),[Ue,Lt]=i.useState("yes"),[mt,ve]=i.useState(!1),[it,Ee]=i.useState(""),[I,V]=i.useState(""),[le,Te]=i.useState(""),[Ae,Se]=i.useState([]),[Z,ot]=i.useState([]),Ge=$(o=>{var r;return((r=o.costCenter.payload)==null?void 0:r.rowsBodyData)||{}}),ze=$(o=>{var r;return(r=o.costCenterDropDownData)==null?void 0:r.dropDown}),[xe,_]=i.useState(!1),[O,De]=i.useState(!1),[ye,Ne]=i.useState({}),[ue,J]=i.useState(!1),[ce,Et]=i.useState([]),[st,dt]=i.useState([]),[He,St]=i.useState("");i.useState(!1);const[ut,ge]=i.useState({title:"",message:"",subText:"",buttonText:"",redirectTo:""}),[Zt,B]=i.useState(!1),nt=$(o=>{var r;return(r=o==null?void 0:o.costCenter)==null?void 0:r.validatedRowsStatus}),rt=$(o=>o.changeLog.createChangeLogDataGL),Wt=$(o=>o.costCenter.isOpenDialog),ao=$(o=>o.costCenter.validatedRows);d!=null&&d.profitCenterNumber&&(nt==null||nt[d.profitCenterNumber]);const{showSnackbar:gt}=Bs(),{getDynamicWorkflowDT:Ft}=Fs();i.useEffect(()=>{let o=Object.keys(se==null?void 0:se.rowsBodyData)[0];const r=async()=>{var l,c;try{const p=await Ft(C==null?void 0:C.RequestType,(l=se==null?void 0:se.rowsBodyData[o])==null?void 0:l.CostcenterType,N==null?void 0:N.ATTRIBUTE_3,"v4","MDG_DYNAMIC_WF_CC_DT",(c=Ve)==null?void 0:c.CC);dt(p)}catch(p){u(p)}};C!=null&&C.RequestType&&(C!=null&&C.Region)&&(N!=null&&N.ATTRIBUTE_3)&&r()},[C==null?void 0:C.RequestType,C==null?void 0:C.Region,N==null?void 0:N.ATTRIBUTE_3]),i.useEffect(()=>{F!=null&&F.length||we()},[]),i.useEffect(()=>{it&&I&&io()},[it,I]);const io=()=>{if(!it||!I)return;const o={controllingArea:it,companyCode:I,top:"100",skip:"0"};G(`/${z}/data/getCostCentersNo`,"post",r=>{var l;if(Array.isArray((l=r.body)==null?void 0:l.list)){const c=r.body.list.map(p=>p.code).filter((p,x,a)=>p&&a.indexOf(p)===x);Se(c.map(p=>({code:p,desc:p})))}},r=>{console.error("Cost Center fetch failed",r)},o)},Ot=o=>{const r=c=>{_e(c.body),E(as({keyName:"CompCode",data:(c==null?void 0:c.body)||[]})),E(Pt({keyName:"CompCode",data:c.body}))},l=c=>{console.log(c)};G(`/${z}/data/getCompanyCodeBasedOnControllingArea?controllingArea=${o}&rolePrefix=ETP`,"get",r,l)};i.useEffect(()=>{var o,r,l;L.length>=1&&((o=L[0])!=null&&o.costCenterNumber||(r=L[0])!=null&&r.controllingArea||(l=L[0])!=null&&l.companyCode)&&E(ts(!1))},[]),i.useEffect(()=>{(N!=null&&N.ATTRIBUTE_1||g)&&Gt("Cost Center","MDG_DYN_BTN_DT","v3")},[N]);const Ht=[{field:"included",headerName:"",flex:.3,align:"center",headerAlign:"center",sortable:!1,disableColumnMenu:!0,renderHeader:()=>{const o=L.length>0&&L.every(l=>l.included),r=L.some(l=>l.included);return s(cs,{indeterminate:!o&&r,checked:o,disabled:W,onChange:l=>{const c=l.target.checked,p=L.map(x=>({...x,included:c}));E(lo(p))}})},renderCell:o=>s(cs,{checked:o.row.included,disabled:W,onChange:r=>At(r.target.checked,o.row.id,"included")})},{field:"lineNumber",headerName:"Sl No.",flex:.25,align:"center",headerAlign:"center",renderCell:o=>{const r=L.findIndex(l=>l.id===o.row.id);return s("div",{children:(r+1)*10})}},{field:"controllingArea",headerName:"Controlling Area",align:"left",headerAlign:"left",flex:1,renderHeader:()=>A("span",{children:[y("Controlling Area"),vt("controllingArea")&&s("span",{style:{color:"red"},children:" *"})]}),renderCell:o=>s(Kt,{options:(pe==null?void 0:pe.ControllingArea)||[],value:o.row.controllingArea,onChange:r=>At(r,o.row.id,"controllingArea"),placeholder:y("Select Controlling Area"),disabled:W,minWidth:"90%",listWidth:235})},{field:"CompCode",headerName:"Company Code",align:"left",headerAlign:"left",flex:1,renderHeader:()=>A("span",{children:[y("Company Code"),vt("CompCode")&&s("span",{style:{color:"red"},children:" *"})]}),renderCell:o=>{var r,l;return s(Kt,{options:(pe==null?void 0:pe.CompCode)||[],value:(r=o.row.CompCode)!=null&&r.code?(l=o.row.CompCode)==null?void 0:l.code:o.row.CompCode,onChange:c=>At(c,o.row.id,"CompCode"),placeholder:y("Select Company Code"),disabled:W,minWidth:"90%",listWidth:235})}},{field:"costCenterNumber",headerName:"Cost Center Number",align:"left",headerAlign:"left",flex:1,renderHeader:()=>A("span",{children:[y("Cost Center Number"),vt("costCenterNumber")&&s("span",{style:{color:"red"},children:" *"})]}),renderCell:o=>{var S,q,re;const r=(S=o.row.CompCode)!=null&&S.code?(q=o.row.CompCode)==null?void 0:q.code:(re=o==null?void 0:o.row)==null?void 0:re.CompCode,l=typeof r=="string"?r:typeof r=="object"&&r!==null?r.value??"":"",c=l||"",p=o.row.costCenterNumber||"",x=p.startsWith(c)?p.slice(c.length):"",a=p.length>0&&(p.length!==10||!/^[a-zA-Z0-9]+$/.test(p));return A(X,{sx:{position:"relative",width:"100%"},children:[s(rs,{value:x,onChange:P=>{const Re=P.target.value.replace(/[^0-9]/g,"").slice(0,10-c.length);At(c+Re,o.row.id,"costCenterNumber")},onKeyDown:P=>{["Backspace","Delete","Tab","Escape","Enter","ArrowLeft","ArrowRight"].includes(P.key)||/^[0-9]$/.test(P.key)||P.preventDefault()},onPaste:P=>{const Re=P.clipboardData.getData("text");/^[0-9]+$/.test(Re)||P.preventDefault()},variant:"outlined",size:"small",fullWidth:!0,disabled:W,inputProps:{maxLength:10-c.length,style:{paddingLeft:`${c.length+2}ch`,fontSize:"0.875rem",height:"35px",boxSizing:"border-box",display:"flex",alignItems:"center"}},sx:{"& .MuiOutlinedInput-root":{height:"35px"},"& .MuiInputBase-input":{fontFamily:"inherit",fontWeight:500}}}),s(X,{sx:{position:"absolute",top:"50%",left:"14px",transform:"translateY(-50%)",pointerEvents:"none",fontSize:"0.875rem",fontWeight:500,fontFamily:"inherit",color:"rgba(0, 0, 0, 0.7)"},children:c}),a&&s(X,{sx:{position:"absolute",bottom:-1,left:14,color:"red",fontSize:"10px",pointerEvents:"none"},children:"Must be 10 alphanumeric"})]})}},{field:"longDescription",headerName:"Long Description",align:"left",headerAlign:"left",flex:1.5,renderHeader:()=>A("span",{children:[y("Long Description"),vt("longDescription")&&s("span",{style:{color:"red"},children:" *"})]}),renderCell:o=>{const r=o.row.longDescription||"",l=40;return A(X,{sx:{position:"relative",width:"100%"},children:[s(rs,{value:r,onChange:c=>{const p=c.target.value.toUpperCase();At(p.slice(0,l),o.row.id,"longDescription")},onKeyDown:c=>{c.key===" "&&c.stopPropagation()},disabled:W,variant:"outlined",size:"small",placeholder:"Enter Long Description",fullWidth:!0,inputProps:{maxLength:l,style:{paddingBottom:"12px"}},sx:{"& .MuiInputBase-root.Mui-disabled":{"& > input":{WebkitTextFillColor:et.black.dark,color:et.black.dark}}}}),A(X,{sx:{position:"absolute",bottom:-1,left:14,color:"blue",fontSize:"10px",pointerEvents:"none"},children:[r.length,"/",l]})]})}},{field:"action",headerName:"Action",align:"left",headerAlign:"left",width:150,renderHeader:()=>s("span",{style:{fontWeight:"bold"},children:y("Action")}),renderCell:o=>{const r=o.row.id,l={id:r,...Ge[r]},c=o.row.costCenterNumber,p=c&&nt[c]||"idle";return s(X,{children:s(os,{title:p==="success"?"Validated Successfully":p==="error"?"Validation Failed":"Click to Validate",children:s(jo,{onClick:a=>{a.stopPropagation(),go(o.row,l,F)},color:p,disabled:W,children:p==="error"?s(Sn,{}):s(nr,{})})})})}}],Xt=["controllingArea","costCenterNumber","CompCode","longDescription"],eo=()=>{Je(!1)},Ut=()=>{E(ts(!1))},uo=()=>{_(!xe),O&&De(!1)},to=()=>{De(!O),xe&&_(!1)},Co=()=>{const o=l=>{E(Pt({keyName:"CostcenterType",data:l.body}))},r=l=>{console.error(l)};G(`/${z}/data/getCostCenterCategory`,"get",o,r)},To=()=>{const o=l=>{E(Pt({keyName:"FuncAreaLong",data:l.body}))},r=l=>{console.error(l)};G(`/${z}/data/getFunctionalArea`,"get",o,r)};i.useEffect(()=>{Co(),To()},[]),i.useEffect(()=>{if(!Array.isArray(L)||L.length===0){Fe(!0),Nt(!1);return}const o=L.every(r=>{const l=(r==null?void 0:r.costCenterNumber)??(r==null?void 0:r.id);return(r==null?void 0:r.id)?(nt==null?void 0:nt[String(l)])==="success":!1});Fe(o),Nt(o)},[L,Ge,ao,$t,Rt]),i.useEffect(()=>{try{const o=JSON.parse(localStorage.getItem(Yt.STATUS_VALIDATE))||{};Object.entries(o).forEach(([r,l])=>{E(gs({rowId:r,status:l}))})}catch{u(err)}},[E]);const go=async(o,r,l)=>{const c=[],p=[],x=(o==null?void 0:o.lineNumber)||L.findIndex(S=>S.id===o.id)+1;l.flatMap(S=>Object.values(S.data).flat()).forEach(S=>{if(S.visibility==="Mandatory"){const q=r[S.jsonName];(q==null||typeof q=="string"&&q.trim()==="")&&(p.push(S.jsonName),c.push(`Line ${x} - ${S.fieldName}`))}});const m={CompCode:"Company Code",costCenterNumber:"Cost Center Number",controllingArea:"Controlling Area",longDescription:"Long Description"};Xt.forEach(S=>{const q=o[S],re=m[S]||S;q==null||typeof q=="string"&&q.trim()===""?c.push(`Line ${x} - ${re}`):S==="costCenterNumber"&&(q.length!==10||!/^[a-zA-Z0-9]+$/.test(q))&&c.push(`Line ${x} - ${re}`)});const h=c.length===0?"success":"error";E(gs({rowId:o.costCenterNumber,status:h})),E(mo({rowId:o.id}));try{const S=JSON.parse(localStorage.getItem(Yt.STATUS_VALIDATE))||{},q=String(o.costCenterNumber);S[q]=h,localStorage.setItem(Yt.STATUS_VALIDATE,JSON.stringify(S))}catch{u(err)}if(h==="error"){const S=[...new Set(c)];me(S),Pe(!0),Ne(q=>({...q,[o.id]:p}))}else Ze(S=>({...S,[o.id]:JSON.parse(JSON.stringify(o))})),ft(S=>{const{id:q,...re}=r;return{...S,[o.id]:JSON.parse(JSON.stringify(re))}}),de("success"),Ie(`Line ${x} - Validation Successful`),Je(!0)};i.useEffect(()=>{if(L.length&&Object.keys(Ge).length){const o={},r={};L.forEach(l=>{const c=l.id;if($t[c]||(o[c]=JSON.parse(JSON.stringify(l))),!Rt[c]&&Ge[c]){const{id:p,...x}=Ge[c];r[c]=JSON.parse(JSON.stringify(x))}}),Ze(l=>({...l,...o})),ft(l=>({...l,...r}))}},[L,Ge]);const vt=o=>Xt.includes(o),zt=(o,r,l)=>{const c=[],p=(o==null?void 0:o.lineNumber)||L.findIndex(m=>m.id===o.id)+1;l.flatMap(m=>Object.values(m.data).flat()).forEach(m=>{if(m.visibility==="Mandatory"){const h=r[m.jsonName];(h==null||typeof h=="string"&&h.trim()==="")&&c.push(`Line ${p} - ${m.fieldName}`)}});const a={CompCode:"Company Code",costCenterNumber:"Cost Center Number",controllingArea:"Controlling Area",longDescription:"Long Description"};return Xt.forEach(m=>{const h=o[m],S=a[m]||m;h==null||typeof h=="string"&&h.trim()===""?c.push(`Line ${p} - ${S}`):m==="costCenterNumber"&&(h.length!==10||!/^[a-zA-Z0-9]+$/.test(h))&&c.push(`Line ${p} - ${S}`)}),{missing:c,status:c.length>0?"error":"success"}},bt=(o,r)=>{const l={},c={},p={};o.forEach((h,S)=>{var lt,ct,jt;const q=r[h.id],re=h.lineNumber||S+1,P=(lt=q==null?void 0:q.CostCenterName)==null?void 0:lt.trim(),Re=(ct=q==null?void 0:q.Descript)==null?void 0:ct.trim(),je=(jt=h==null?void 0:h.costCenterNumber)==null?void 0:jt.trim();P&&(l[P]||(l[P]=[]),l[P].push(re)),Re&&(c[Re]||(c[Re]=[]),c[Re].push(re)),je&&(p[je]||(p[je]=[]),p[je].push(re))});const x=Object.entries(l).filter(([h,S])=>S.length>1).map(([h,S])=>({type:"Short Description",value:h,lines:S})),a=Object.entries(c).filter(([h,S])=>S.length>1).map(([h,S])=>({type:"Long Description",value:h,lines:S})),m=Object.entries(p).filter(([h,S])=>S.length>1).map(([h,S])=>({type:"Cost Center Number",value:h,lines:S}));return[...x,...a,...m]},Mt=()=>{let o=[],r="";if(L.forEach(l=>{const c={id:l.id,...Ge[l.id]},{missing:p,status:x}=zt(l,c,F);r=bt(L,Ge),E(gs({rowId:l.id,status:x}));try{const a=JSON.parse(localStorage.getItem(Yt.STATUS_VALIDATE))||{},m=String(l.costCenterNumber||l.id);a[m]=x,localStorage.setItem(Yt.STATUS_VALIDATE,JSON.stringify(a))}catch(a){u("Failed to save validation status to localStorage",a)}x==="error"?o.push(...p):(Ze(a=>({...a,[l.id]:JSON.parse(JSON.stringify(l))})),ft(a=>{const{id:m,...h}=c;return{...a,[l.id]:JSON.parse(JSON.stringify(h))}}),E(mo({rowId:l.id})))}),o.length>0){const l=[...new Set(o)];me(l),Pe(!0)}else{if(r.length>0){Et(r),J(!0);return}oo(),de("success"),Ie("All Rows Validated Successfully"),Je(!0)}},oo=()=>{Q(!0);const o=wt(se,C,g,N,Le,rt,He),r=c=>{var p,x,a,m;Q(!1),(c==null?void 0:c.statusCode)===((p=R)==null?void 0:p.STATUS_200)||(c==null?void 0:c.statusCode)===((x=R)==null?void 0:x.STATUS_201)?(ge({title:v.TITLE,message:c.message,subText:v.SUBTEXT,buttonText:v.BUTTONTEXT,redirectTo:v.REDIRECT}),B(!0)):(c==null?void 0:c.statusCode)===((a=R)==null?void 0:a.STATUS_500)||(c==null?void 0:c.statusCode)===((m=R)==null?void 0:m.STATUS_501)?(ge({title:M.TITLE,message:c.message,subText:M.SUBTEXT,buttonText:M.BUTTONTEXT,redirectTo:M.REDIRECT}),B(!0)):Ie("Unexpected response received.")},l=c=>{Q(!1),Ie("Error occurred while validating the request"),console.error("Error saving draft:",c)};G(`/${z}/massAction/validateMassCostCenter`,"POST",r,l,o)},so=()=>{Pe(!1)},At=(o,r,l)=>{if(l==="controllingArea"){const p=(o==null?void 0:o.code)||o;Ot(p),E(Ct({uniqueId:k||(d==null?void 0:d.id),keyName:"controllingArea",data:p,viewID:null}));const x=L.map(a=>a.id===r?{...a,controllingArea:p}:a);E(lo(x));return}l==="CompCode"&&(ro(o==null?void 0:o.code),E(Ct({uniqueId:k||(d==null?void 0:d.id),keyName:"CompCode",data:o==null?void 0:o.code,viewID:"CompCode"})),bs(o==null?void 0:o.code,E,k||(d==null?void 0:d.id))),l==="longDescription"&&E(Ct({uniqueId:k||(d==null?void 0:d.id),keyName:"Descript",data:o,viewID:"Basic Data"}));const c=L.map(p=>p.id===r?{...p,[l]:o}:p);E(lo(c))},ho=o=>{const r=o.row;Oe(r),E(bn(r==null?void 0:r.CostCenterID))},Ce=()=>{ws(),ve(!0),Lt("yes"),Ee(""),V(""),Te(""),ot([]),E(ts(!0))},kt=(o,r)=>{Qe(r)};i.useEffect(()=>{Oe(L[0])},[]);const no=o=>{const r=c=>{E(Dn({keyName:"PrctrHierGrp",data:(c==null?void 0:c.body)||[],keyName2:k||(d==null?void 0:d.id)}))},l=c=>{console.log(c)};G(`/${z}/data/getCostCtrGroup?controllingArea=${o}`,"get",r,l)},ro=o=>{const r={controllingArea:"ETCA",companyCode:o,top:"100",skip:"0"},l=p=>{var x;E(as({keyName:"ProfitCtr",data:((x=p==null?void 0:p.body)==null?void 0:x.list)||[]}))},c=p=>{console.log(p)};G(`/${Gs}/data/getProfitCentersNo`,"post",l,c,r)},Qo=()=>{const o=l=>{E({type:"SET_DROPDOWN",payload:{keyName:"Template",data:l.body}})},r=l=>{console.log(l)};G(`/${z}/data/getFormPlanningTemp`,"get",o,r)},po=()=>{const o=l=>{We(l.body),E(Pt({keyName:"ControllingArea",data:l.body}))},r=l=>{console.log(l)};G(`/${z}/data/getControllingArea`,"get",o,r)},Jo=()=>{const o=l=>{E({type:"SET_DROPDOWN",payload:{keyName:"TaxJurisdiction",data:l.body}})},r=l=>{console.log(l)};G(`/${z}/data/getJurisdiction`,"get",o,r)},Zo=()=>{const o=l=>{We(l.body)},r=l=>{console.log(l)};G(`/${z}/data/getBusinessSegment`,"get",o,r)};i.useEffect(()=>{us(),no(),Qo(),po(),Jo(),Zo()},[]);const us=()=>{const o=l=>{E(Pt({keyName:"AddrCountry",data:l.body}))},r=l=>{console.log(l)};G(`/${z}/data/getCountry`,"get",o,r)},Cs=o=>{const r=c=>{E({type:"SET_DROPDOWN",payload:{keyName:"AddrRegion",data:c.body}})},l=c=>{console.log(c)};G(`/${z}/data/getRegionBasedOnCountry?country=${o}`,"get",r,l)},es=()=>{const o=l=>{E({type:"SET_DROPDOWN",payload:{keyName:"Language",data:l.body}})},r=l=>{console.log(l)};G(`/${z}/data/getLanguageKey`,"get",o,r)};i.useEffect(()=>{es()},[]);const yo=()=>{Q(!0);const o=wt(se,C,g,N,Le,rt,He),r=c=>{var p,x,a,m;Q(!1),(c==null?void 0:c.statusCode)===((p=R)==null?void 0:p.STATUS_200)||(c==null?void 0:c.statusCode)===((x=R)==null?void 0:x.STATUS_201)?(ge({title:v.TITLE,message:c.message,subText:v.SUBTEXT,buttonText:v.BUTTONTEXT,redirectTo:v.REDIRECT}),B(!0)):(c==null?void 0:c.statusCode)===((a=R)==null?void 0:a.STATUS_500)||(c==null?void 0:c.statusCode)===((m=R)==null?void 0:m.STATUS_501)?(ge({title:M.TITLE,message:c.message,subText:M.SUBTEXT,buttonText:M.BUTTONTEXT,redirectTo:M.REDIRECT}),B(!0)):Ie("Unexpected response received.")},l=c=>{gt(c==null?void 0:c.message,"error"),Q(!1)};G(`/${z}/massAction/costCentersSaveAsDraft`,"POST",r,l,o)},No=(o,r,l)=>{yt(""),Q(!0);const c=wt(se,C,g,N,Le,rt,r,l,He),p=a=>{var m,h,S,q;Q(!1),(a==null?void 0:a.statusCode)===((m=R)==null?void 0:m.STATUS_200)||(a==null?void 0:a.statusCode)===((h=R)==null?void 0:h.STATUS_201)?(ge({title:v.TITLE,message:a.message,subText:v.SUBTEXT,buttonText:v.BUTTONTEXT,redirectTo:v.REDIRECT}),B(!0)):(a==null?void 0:a.statusCode)===((S=R)==null?void 0:S.STATUS_500)||(a==null?void 0:a.statusCode)===((q=R)==null?void 0:q.STATUS_501)?(ge({title:M.TITLE,message:a.message,subText:M.SUBTEXT,buttonText:M.BUTTONTEXT,redirectTo:M.REDIRECT}),B(!0)):Ie("Unexpected response received.")},x=a=>{gt(a==null?void 0:a.message,"error"),Q(!1)};G(`/${z}/massAction/costCentersSubmitForReview`,"POST",p,x,c)},_o=(o,r,l)=>{Q(!0);const c=wt(se,C,g,N,Le,rt,r,l,He),p=a=>{var m,h,S,q;Q(!1),(a==null?void 0:a.statusCode)===((m=R)==null?void 0:m.STATUS_200)||(a==null?void 0:a.statusCode)===((h=R)==null?void 0:h.STATUS_201)?(ge({title:v.TITLE,message:a.message,subText:v.SUBTEXT,buttonText:v.BUTTONTEXT,redirectTo:v.REDIRECT}),B(!0)):(a==null?void 0:a.statusCode)===((S=R)==null?void 0:S.STATUS_500)||(a==null?void 0:a.statusCode)===((q=R)==null?void 0:q.STATUS_501)?(ge({title:M.TITLE,message:a.message,subText:M.SUBTEXT,buttonText:M.BUTTONTEXT,redirectTo:M.REDIRECT}),B(!0)):Ie("Unexpected response received.")},x=a=>{gt(a==null?void 0:a.message,"error"),Q(!1)};G(o==="VALIDATE"?`/${z}/massAction/validateMassCostCenter`:`/${z}/massAction/createCostCentersApproved`,"POST",p,x,c)},Io=(o,r,l)=>{Q(!0);const c=wt(se,C,g,N,Le,rt,r,l,He),p=a=>{var m,h,S,q;Q(!1),(a==null?void 0:a.statusCode)===((m=R)==null?void 0:m.STATUS_200)||(a==null?void 0:a.statusCode)===((h=R)==null?void 0:h.STATUS_201)?(ge({title:v.TITLE,message:a.message,subText:v.SUBTEXT,buttonText:v.BUTTONTEXT,redirectTo:v.REDIRECT}),B(!0)):(a==null?void 0:a.statusCode)===((S=R)==null?void 0:S.STATUS_500)||(a==null?void 0:a.statusCode)===((q=R)==null?void 0:q.STATUS_501)?(ge({title:M.TITLE,message:a.message,subText:M.SUBTEXT,buttonText:M.BUTTONTEXT,redirectTo:M.REDIRECT}),B(!0)):Ie("Unexpected response received.")},x=a=>{gt(a==null?void 0:a.message,"error"),Q(!1)};G(`/${z}/massAction/costCentersApprovalSubmit`,"POST",p,x,c)},xo=(o,r,l)=>{var m,h,S,q,re;Q(!0);let c=(re=(S=(m=Ye)==null?void 0:m.MASTER_BUTTON_APIS)==null?void 0:S[(h=Ve)==null?void 0:h.CC])==null?void 0:re[(q=ee)==null?void 0:q.CREATE].SEND_BACK;const p=wt(se,C,g,N,Le,rt,r,l,He),x=P=>{var Re,je,lt,ct;Q(!1),(P==null?void 0:P.statusCode)===((Re=R)==null?void 0:Re.STATUS_200)||(P==null?void 0:P.statusCode)===((je=R)==null?void 0:je.STATUS_201)?(ge({title:v.TITLE,message:P.message,subText:v.SUBTEXT,buttonText:v.BUTTONTEXT,redirectTo:v.REDIRECT}),B(!0)):(P==null?void 0:P.statusCode)===((lt=R)==null?void 0:lt.STATUS_500)||(P==null?void 0:P.statusCode)===((ct=R)==null?void 0:ct.STATUS_501)?(ge({title:M.TITLE,message:P.message,subText:M.SUBTEXT,buttonText:M.BUTTONTEXT,redirectTo:M.REDIRECT}),B(!0)):Ie("Unexpected response received.")},a=P=>{gt(P==null?void 0:P.message,"error"),Q(!1)};G(c.URL,"POST",x,a,p)},Ro=(o,r,l)=>{var m,h,S,q,re;let c=(re=(S=(m=Ye)==null?void 0:m.MASTER_BUTTON_APIS)==null?void 0:S[(h=Ve)==null?void 0:h.CC])==null?void 0:re[(q=ee)==null?void 0:q.CREATE].SEND_BACK;Q(!0);const p=wt(se,C,g,N,Le,rt,r,l,He),x=P=>{var Re,je,lt,ct;Q(!1),(P==null?void 0:P.statusCode)===((Re=R)==null?void 0:Re.STATUS_200)||(P==null?void 0:P.statusCode)===((je=R)==null?void 0:je.STATUS_201)?(ge({title:v.TITLE,message:P.message,subText:v.SUBTEXT,buttonText:v.BUTTONTEXT,redirectTo:v.REDIRECT}),B(!0)):(P==null?void 0:P.statusCode)===((lt=R)==null?void 0:lt.STATUS_500)||(P==null?void 0:P.statusCode)===((ct=R)==null?void 0:ct.STATUS_501)?(ge({title:M.TITLE,message:P.message,subText:M.SUBTEXT,buttonText:M.BUTTONTEXT,redirectTo:M.REDIRECT}),B(!0)):Ie("Unexpected response received.")},a=P=>{gt(P==null?void 0:P.message,"error"),Q(!1)};G(c.URL,"POST",x,a,p)},Lo=(o,r,l)=>{Q(!0);const c=wt(se,C,g,N,Le,rt,r,l,He),p=a=>{var m,h,S,q;Q(!1),(a==null?void 0:a.statusCode)===((m=R)==null?void 0:m.STATUS_200)||(a==null?void 0:a.statusCode)===((h=R)==null?void 0:h.STATUS_201)?(ge({title:v.TITLE,message:a.message,subText:v.SUBTEXT,buttonText:v.BUTTONTEXT,redirectTo:v.REDIRECT}),B(!0)):(a==null?void 0:a.statusCode)===((S=R)==null?void 0:S.STATUS_500)||(a==null?void 0:a.statusCode)===((q=R)==null?void 0:q.STATUS_501)?(ge({title:M.TITLE,message:a.message,subText:M.SUBTEXT,buttonText:M.BUTTONTEXT,redirectTo:M.REDIRECT}),B(!0)):Ie("Unexpected response received.")},x=()=>{gt(ie==null?void 0:ie.message,"error"),Q(!1)};G(`/${z}/massAction/costCentersRejected`,"POST",p,x,c)},Oo=(o,r)=>{Q(!0);const l={coAreaCCs:[{controllingArea:it,costCenter:le}]},c=x=>{var P,Re,je,lt,ct,jt,Uo,vo,Mo,ko,wo,Po,Bo,Go,qo,$o,Wo;const m=((x==null?void 0:x.body)||[])[0];if(!m){console.warn("No response data received for Cost Center copy."),Q(!1),Ut();return}const h=((P=m==null?void 0:m.addressTabDto)==null?void 0:P.AddrCountry)||"",S=((Re=m==null?void 0:m.addressTabDto)==null?void 0:Re.AddrRegion)||"",q=r.map(fo=>{var Fo;return fo.id===o?{...fo,controllingArea:m.controllingArea||"",CompCode:((Fo=m.basicDataTabDto)==null?void 0:Fo.CompCode)||""}:fo}),re={[o]:{PersonInCharge:((je=m.basicDataTabDto)==null?void 0:je.PersonInCharge)||"",PersonInChargeUser:((lt=m.basicDataTabDto)==null?void 0:lt.PersonInChargeUser)||"",CostcenterType:((ct=m.basicDataTabDto)==null?void 0:ct.CostcenterType)||"",FuncAreaLong:((jt=m.basicDataTabDto)==null?void 0:jt.FuncAreaLong)||"",Currency:((Uo=m.basicDataTabDto)==null?void 0:Uo.Currency)||"",ProfitCtr:((vo=m.basicDataTabDto)==null?void 0:vo.ProfitCtr)||"",CompCode:((Mo=m.basicDataTabDto)==null?void 0:Mo.CompCode)||[],AddrCountry:(ko=m==null?void 0:m.addressTabDto)==null?void 0:ko.AddrCountry,AddrStreet:((wo=m.addressTabDto)==null?void 0:wo.AddrStreet)||"",AddrCity:((Po=m.addressTabDto)==null?void 0:Po.AddrCity)||"",AddrRegion:(Bo=m==null?void 0:m.addressTabDto)==null?void 0:Bo.AddrRegion,AddrPostlCode:(Go=m==null?void 0:m.addressTabDto)==null?void 0:Go.AddrPostlCode}};E({type:"costCenter/setCostCenterRows",payload:q}),E({type:"costCenter/setCostCenterTab",payload:re}),E(Ct({uniqueId:o,keyName:"CompCode",data:((qo=m.basicDataTabDto)==null?void 0:qo.CompCode)||"",viewID:"Comp Codes"})),E(Ct({uniqueId:o,keyName:"costCenterNumber",data:(m==null?void 0:m.costCenter)||"",viewID:"Comp Codes"})),E(Ct({uniqueId:o,keyName:"AddrCountry",data:h,viewID:"Address"})),E(Ct({uniqueId:o,keyName:"AddrRegion",data:S,viewID:"Address"})),h&&Cs(h),Ot(m==null?void 0:m.controllingArea),ro(($o=m.basicDataTabDto)==null?void 0:$o.CompCode),bs((Wo=m.basicDataTabDto)==null?void 0:Wo.CompCode,E,k||(d==null?void 0:d.id)),Q(!1),Ut()},p=x=>{console.error("Error fetching profit center data",x),Q(!1)};G(`/${z}/data/getCostCentersData`,"post",c,p,l)},e=()=>{var c;const r=Math.max(0,...L.map(p=>p.lineNumber||0))+10;E(ts(!1));const l=ws();if(Ue==="yes"){if(Z!=null&&Z.length){const p=Z[0],x=Number(p.code/10-1),a=L[x];if(!a){console.warn("Invalid row selected.");return}const m={id:l,controllingArea:(a==null?void 0:a.controllingArea)||"",costCenterNumber:"",longDescription:"",companyCode:((c=a==null?void 0:a.CompCode)==null?void 0:c.code)||"",included:!0,isNew:!0,lineNumber:r};E(lo([...L,m])),E(mo({rowId:l})),Fe(!1);const h=Ge==null?void 0:Ge[a.id];if(h){const S={PersonInCharge:(h==null?void 0:h.PersonInCharge)||"",PersonInChargeUser:(h==null?void 0:h.PersonInChargeUser)||"",CostcenterType:(h==null?void 0:h.CostcenterType)||"",FuncAreaLong:(h==null?void 0:h.FuncAreaLong)||"",Currency:(h==null?void 0:h.Currency)||"",ProfitCtr:(h==null?void 0:h.ProfitCtr)||"",CompCode:(h==null?void 0:h.CompCode)||[],AddrCountry:(h==null?void 0:h.AddrCountry)||[],AddrStreet:(h==null?void 0:h.AddrStreet)||"",AddrCity:(h==null?void 0:h.AddrCity)||"",AddrRegion:(h==null?void 0:h.AddrRegion)||"",AddrPostlCode:(h==null?void 0:h.AddrPostlCode)||""};E(An({[l]:S})),E(Ct({uniqueId:l,keyName:"CompCode",data:(S==null?void 0:S.CompCode)||"",viewID:"Comp Codes"})),E(Ct({uniqueId:l,keyName:"costCenterNumber",data:"",viewID:"Comp Codes"})),E(Ct({uniqueId:l,keyName:"AddrCountry",data:S==null?void 0:S.AddrCountry,viewID:"Address"})),E(Ct({uniqueId:l,keyName:"AddrRegion",data:S==null?void 0:S.AddrRegion,viewID:"Address"}))}return}if(mt){const p={id:l,controllingArea:"",costCenterNumber:"",longDescription:"",companyCode:"",included:!0,isNew:!0,lineNumber:r},x=[...L,p];E(lo(x)),E(mo({rowId:l})),Oo(l,x),Fe(!1)}else{const p={id:l,controllingArea:"",costCenterNumber:"",longDescription:"",companyCode:"",included:!0,isNew:!0,lineNumber:r},x=[...L,p];E(lo(x)),E(mo({rowId:l})),Fe(!1),Oo(l,x)}}else if(L.length>=0){const p={id:l,controllingArea:"",costCenterNumber:"",longDescription:"",companyCode:"",included:!0,isNew:!0,lineNumber:r};E(lo([...L,p])),E(mo({rowId:l})),Fe(!1)}else console.warn("No existing valid data in rows. Skipping row addition.")};return A(X,{children:[s(ls,{openSnackBar:It,alertMsg:Jt,handleSnackBarClose:eo,alertType:Bt,isLoading:tt}),s($s,{open:Zt,onClose:()=>B(!1),title:ut.title,message:ut.message,subText:ut.subText,buttonText:ut.buttonText,redirectTo:ut.redirectTo}),ie&&s(qe,{color:"error",children:y("Error loading data")}),s("div",{style:{padding:"0",width:"100%",margin:"0",marginTop:"20px"},children:A(X,{sx:{position:xe?"fixed":"relative",top:xe?0:"auto",left:xe?0:"auto",right:xe?0:"auto",bottom:xe?0:"auto",width:xe?"100vw":"100%",height:xe?"100vh":"auto",zIndex:xe?1004:1,backgroundColor:xe?"white":"transparent",padding:xe?"20px":"0",display:"flex",flexDirection:"column",boxShadow:xe?"0px 0px 15px rgba(0, 0, 0, 0.2)":"none",transition:"all 0.3s ease",borderRadius:"8px",border:"1px solid #e0e0e0"},children:[A(X,{sx:{display:"flex",justifyContent:"space-between",alignItems:"center",padding:"8px 16px",backgroundColor:"#f5f5f5",borderRadius:"8px 8px 0 0"},children:[s(qe,{variant:"h6",children:y("List of Cost Centers")}),A(X,{sx:{display:"flex",alignItems:"center",gap:1},children:[A(Me,{variant:"contained",color:"primary",size:"small",onClick:Ce,disabled:!Y||W||ke,children:["+ ",y("Add")]}),s(os,{title:xe?"Exit Zoom":"Zoom In",sx:{zIndex:"1009"},children:s(jo,{onClick:uo,color:"primary",sx:{backgroundColor:"rgba(0, 0, 0, 0.05)","&:hover":{backgroundColor:"rgba(0, 0, 0, 0.1)"}},children:xe?s(fs,{}):s(ms,{})})})]})]}),s("div",{style:{width:"100%",height:"100%",overflowX:"auto"},children:s("div",{style:{height:"100%"},children:s("div",{children:s(ns,{isLoading:j,rows:L,columns:Ht,pageSize:10,tempheight:"50vh",getRowIdValue:"id",status_onRowSingleClick:!0,callback_onRowSingleClick:ho,getRowClassName:o=>(d==null?void 0:d.id)===o.row.id?"Mui-selected":""})})})})]})}),Wt&&A(Vo,{fullWidth:!0,open:Wt,maxWidth:"lg",onClose:Ut,sx:{"&::webkit-scrollbar":{width:"1px"}},children:[s(hs,{sx:{justifyContent:"space-between",alignItems:"center",height:"max-content",padding:".5rem",paddingLeft:"1rem",backgroundColor:"#EAE9FF",display:"flex"},children:s(qe,{variant:"h6",children:"Add New Cost Center"})}),A(bo,{sx:{padding:".5rem 1rem",alignItems:"center",justifyContent:"center",margin:"0px 25px"},children:[A(an,{component:"fieldset",sx:{paddingBottom:"2%"},children:[s(dn,{component:"legend",sx:{padding:"15px 0px",fontWeight:"600",fontSize:"15px"},children:"Do You Want To Continue"}),A(un,{row:!0,"aria-label":"cost-center-number",name:"cost-center-number",value:Ue,onChange:o=>{const r=o.target.value;Lt(r),r==="no"&&(Ee(""),V(""),Te(""),ot([]))},children:[s(Us,{value:"yes",control:s(Os,{}),label:"With Reference"}),s(Us,{value:"no",control:s(Os,{}),label:"Without Reference"})]})]}),Ue==="yes"?s(Dt,{container:!0,spacing:2,children:s(Dt,{item:!0,xs:12,children:A(Dt,{container:!0,spacing:2,children:[A(Dt,{item:!0,xs:3,children:[A(qe,{variant:"subtitle2",gutterBottom:!0,children:["Controlling Area",s("span",{style:{color:"red"},children:"*"})]}),s(Kt,{options:ht||[],value:(ht==null?void 0:ht.find(o=>o.code===it))||null,onChange:o=>{_e([]),Se([]),V(""),Te(""),Ot(o==null?void 0:o.code),Ee((o==null?void 0:o.code)||""),ot([])},placeholder:"Select Controlling Area",minWidth:"90%",listWidth:235,disabled:Ue==="no"})]}),A(Dt,{item:!0,xs:3,children:[A(qe,{variant:"subtitle2",gutterBottom:!0,children:["Company Code",s("span",{style:{color:"red"},children:"*"})]}),s(Kt,{options:te||[],value:(te==null?void 0:te.find(o=>o.code===I))||null,onChange:o=>{Se([]),Te(""),V((o==null?void 0:o.code)||""),ot([])},placeholder:"Select Company Code",disabled:Ue==="no",minWidth:"90%",listWidth:235})]}),A(Dt,{item:!0,xs:3,children:[A(qe,{variant:"subtitle2",gutterBottom:!0,children:["Cost Center",s("span",{style:{color:"red"},children:"*"})]}),s(Kt,{options:Ae,value:Ae.find(o=>o.code===le)||null,onChange:o=>{Te((o==null?void 0:o.code)||""),ot([])},placeholder:"Select Cost Center",minWidth:"90%",listWidth:235,disabled:Ue==="no"})]}),L.length>0?A(So,{children:[s(Dt,{item:!0,xs:1,sx:{textAlign:"center"},children:s(qe,{variant:"body1",sx:{fontWeight:"bold",color:"gray"},children:"OR"})}),s(Dt,{item:!0,xs:3,children:s(Kt,{options:L.map((o,r)=>({code:o.lineNumber})),value:Z[0],onChange:o=>{ot(o?[o]:[]),Ee(""),V(""),Te("")},minWidth:180,listWidth:266,placeholder:y("Select Cost Center Number"),disabled:Ue==="no",getOptionLabel:o=>o!=null&&o.desc?`${o.code} - ${o.desc}`:(o==null?void 0:o.code)||"",renderOption:(o,r)=>A("li",{...o,children:[s("strong",{children:r==null?void 0:r.code}),r!=null&&r.desc?` - ${r==null?void 0:r.desc}`:""]}),sx:{minWidth:270,"& .MuiAutocomplete-popper":{minWidth:306}}})})]}):null]})})}):""]}),A(Ao,{sx:{display:"flex",justifyContent:"end"},children:[s(Me,{sx:{width:"max-content",textTransform:"capitalize"},onClick:Ut,variant:"outlined",children:"Cancel"}),s(Me,{className:"button_primary--normal",type:"save",onClick:e,variant:"contained",disabled:Ue==="yes"&&!(it&&I&&le||(Z==null?void 0:Z.length)>0),children:"Proceed"})]})]}),A(Vo,{open:fe,onClose:so,"aria-labelledby":"missing-fields-dialog-title",maxWidth:"sm",fullWidth:!0,children:[A(hs,{id:"missing-fields-dialog-title",sx:{backgroundColor:"#fff3e0",color:"#e65100",display:"flex",alignItems:"center",gap:1,fontWeight:"bold"},children:[s(ps,{fontSize:"medium"}),y("Missing Mandatory Fields")]}),A(bo,{sx:{pt:2},children:[s(qe,{variant:"body1",gutterBottom:!0,children:y("Please complete the following mandatory fields:")}),s(hn,{dense:!0,children:Be.map((o,r)=>A(Cn,{disablePadding:!0,children:[s(Tn,{sx:{minWidth:30},children:s(ps,{fontSize:"small",color:"warning"})}),s(gn,{primary:o})]},r))})]}),s(Ao,{sx:{pr:3,pb:2},children:s(Me,{onClick:so,variant:"contained",color:"warning",sx:{textTransform:"none",fontWeight:500},children:y("Close")})})]}),A(Vo,{open:ue,onClose:()=>J(!1),maxWidth:"sm",fullWidth:!0,children:[A(hs,{id:"missing-fields-dialog-title",sx:{backgroundColor:"#fff3e0",color:"#e65100",display:"flex",alignItems:"center",gap:1,fontWeight:"bold"},children:[s(ps,{fontSize:"medium"}),y("Duplicate Description")]}),s(bo,{dividers:!0,children:ce.length===0?s(qe,{children:"No duplicates found."}):s(pn,{component:Do,elevation:0,children:A(fn,{size:"small",children:[s(mn,{children:A(vs,{children:[s(ss,{children:s("strong",{children:"Type"})}),s(ss,{children:s("strong",{children:"Remarks"})})]})}),s(En,{children:ce.map((o,r)=>A(vs,{children:[s(ss,{children:o.type}),A(ss,{children:[s("strong",{children:o.value})," found in Line(s):"," ",o.lines.join(", ")]})]},r))})]})})}),s(Ao,{children:s(Me,{onClick:()=>J(!1),children:"Close"})})]}),d&&(oe==="true"&&k?A(X,{sx:{position:O?"fixed":"relative",top:O?0:"auto",left:O?0:"auto",right:O?0:"auto",bottom:O?0:"auto",width:O?"100vw":"100%",height:O?"100vh":"auto",zIndex:O?1004:1,backgroundColor:O?"white":"transparent",padding:O?"20px":"0",marginTop:"20px",display:"flex",flexDirection:"column",boxShadow:O?"0px 0px 15px rgba(0, 0, 0, 0.2)":"none",transition:"all 0.3s ease",borderRadius:"8px",border:"1px solid #e0e0e0"},children:[A(X,{sx:{display:"flex",justifyContent:"space-between",alignItems:"center",padding:"8px 16px",borderRadius:"8px 8px 0 0"},children:[s(qe,{variant:"h6",children:y(`${Ve.CC} Details`)}),s(os,{title:O?"Exit Zoom":"Zoom In",sx:{zIndex:"1009"},children:s(jo,{onClick:to,color:"primary",sx:{backgroundColor:"rgba(0, 0, 0, 0.05)","&:hover":{backgroundColor:"rgba(0, 0, 0, 0.1)"}},children:O?s(fs,{}):s(ms,{})})})]}),A(X,{sx:{mt:3},children:[s(ks,{value:ae,onChange:kt,indicatorColor:"primary",textColor:"primary","aria-label":"Request tabs",variant:"scrollable",scrollButtons:"auto",sx:{top:0,position:"sticky",zIndex:1e3,backgroundColor:et.background.container,borderBottom:`1px solid ${et.border.light}`,"& .MuiTab-root":{minHeight:"48px",textTransform:"none",fontSize:"14px",fontWeight:600,color:et.black.graphite,"&.Mui-selected":{color:et.primary.main,fontWeight:700},"&:hover":{color:et.primary.main,opacity:.8}},"& .MuiTabs-indicator":{backgroundColor:et.primary.main,height:"3px"}},children:F.map((o,r)=>s(Ms,{label:o.tab},r))}),s(Do,{elevation:2,sx:{p:3,borderRadius:4},children:F[ae]&&s(Ps,{disabled:W,basicDataTabDetails:F[ae].data,dropDownData:ze,activeViewTab:F[ae].tab,uniqueId:(d==null?void 0:d.id)||k||((t=L[0])==null?void 0:t.id),selectedRow:d||{},module:"CostCenter",fieldErrors:ye[(d==null?void 0:d.id)||k||((f=L[0])==null?void 0:f.id)]||[]},((d==null?void 0:d.id)||k||((T=L[0])==null?void 0:T.id))+(((D=ye[(d==null?void 0:d.id)||k||((b=L[0])==null?void 0:b.id)])==null?void 0:D.join(","))||""))})]})]}):A(X,{sx:{position:O?"fixed":"relative",top:O?0:"auto",left:O?0:"auto",right:O?0:"auto",bottom:O?0:"auto",width:O?"100vw":"100%",height:O?"100vh":"auto",zIndex:O?1004:1,backgroundColor:O?"white":"transparent",padding:O?"20px":"0",marginTop:"20px",display:"flex",flexDirection:"column",boxShadow:O?"0px 0px 15px rgba(0, 0, 0, 0.2)":"none",transition:"all 0.3s ease",borderRadius:"8px",border:"1px solid #e0e0e0"},children:[A(X,{sx:{display:"flex",justifyContent:"space-between",alignItems:"center",padding:"8px 16px",borderRadius:"8px 8px 0 0"},children:[s(qe,{variant:"h6",children:y(`${Ve.CC} Details`)}),s(os,{title:O?"Exit Zoom":"Zoom In",sx:{zIndex:"1009"},children:s(jo,{onClick:to,color:"primary",sx:{backgroundColor:"rgba(0, 0, 0, 0.05)","&:hover":{backgroundColor:"rgba(0, 0, 0, 0.1)"}},children:O?s(fs,{}):s(ms,{})})})]}),A(X,{sx:{flexGrow:1,display:"flex",flexDirection:"column"},children:[s(ks,{value:ae,onChange:kt,indicatorColor:"primary",textColor:"primary","aria-label":"Request tabs",variant:"scrollable",scrollButtons:"auto",sx:{top:0,position:"sticky",zIndex:1e3,backgroundColor:et.background.container,borderBottom:`1px solid ${et.border.light}`,"& .MuiTab-root":{minHeight:"48px",textTransform:"none",fontSize:"14px",fontWeight:600,color:et.black.graphite,"&.Mui-selected":{color:et.primary.main,fontWeight:700},"&:hover":{color:et.primary.main,opacity:.8}},"& .MuiTabs-indicator":{backgroundColor:et.primary.main,height:"3px"}},children:F.map((o,r)=>s(Ms,{label:o.tab},r))}),s(Do,{elevation:2,sx:{p:3,borderRadius:4},children:F[ae]&&s(Ps,{disabled:W,basicDataTabDetails:F[ae].data,dropDownData:ze,activeViewTab:F[ae].tab,uniqueId:(d==null?void 0:d.id)||k||((n=L[0])==null?void 0:n.id),selectedRow:d||{},module:"CostCenter",fieldErrors:ye[(d==null?void 0:d.id)||k||((U=L[0])==null?void 0:U.id)]||[]},((d==null?void 0:d.id)||k||((w=L[0])==null?void 0:w.id))+(((ne=ye[(d==null?void 0:d.id)||k||((H=L[0])==null?void 0:H.id)])==null?void 0:ne.join(","))||""))}),s(X,{sx:{borderTop:"1px solid #e0e0e0",padding:"16px"},children:s(is,{handleSaveAsDraft:yo,handleSubmitForReview:No,handleSubmitForApprove:Io,handleSendBack:xo,handleCorrection:Ro,handleRejectAndCancel:Lo,handleValidateAndSyndicate:_o,validateAllRows:Mt,isSaveAsDraftEnabled:_t,validateEnabled:Tt,filteredButtons:$e,moduleName:he,showWfLevels:qt,selectedLevel:He,workFlowLevels:st,setSelectedLevel:St})})]})]})),s(is,{handleSaveAsDraft:yo,handleSubmitForReview:No,handleSubmitForApprove:Io,handleSendBack:xo,handleCorrection:Ro,handleRejectAndCancel:Lo,handleValidateAndSyndicate:_o,validateAllRows:Mt,isSaveAsDraftEnabled:_t,validateEnabled:Tt,filteredButtons:$e,moduleName:he,showWfLevels:qt,selectedLevel:He,workFlowLevels:st,setSelectedLevel:St}),s(Ko,{blurLoading:xt,loaderMessage:pt})]})},gr=["Request Header","CostCenter List","Attachments & Comments","Preview"],nl=()=>{var Te,Ae,Se,Z,ot,Ge,ze,xe;const{t:oe}=Ns(),he=$(_=>_.CommonStepper.activeStep),W=$(_=>_.costCenter.payload.requestHeaderData),ke=$(_=>{var O;return(O=_.request.requestHeader)==null?void 0:O.requestId}),E=$(_=>_.request.requestHeader),y=Qt(),[u,N]=i.useState(!0),[k,F]=i.useState(!1),[K,be]=i.useState(!1),g=Yo(),C=ds(),[pe,j]=i.useState([]),[ie,we]=i.useState([]),[L,se]=i.useState(!1),[Le,$e]=i.useState(""),d=g.state,Oe=new URLSearchParams(g.search),[ae,Qe]=i.useState(!1),[te,_e]=i.useState(!1),[ht,We]=i.useState(""),[fe,Pe]=i.useState([]),Be=Oe.get("reqBench"),me=Oe.get("RequestId"),Y=Oe.get("RequestType"),[Fe,_t]=i.useState(!1),[at,It]=i.useState(!1),{getChangeTemplate:Je}=Xs(Ve.CC),Jt=$(_=>{var O;return((O=_==null?void 0:_.payload)==null?void 0:O.changeFieldSelectiondata)||[]}),Ie=$(_=>_.applicationConfig),Bt=$(_=>{var O;return(O=_.costCenterDropDownData)==null?void 0:O.isOdataApiCalled}),{fetchAllDropdownFMD:de}=yn(z,as);let tt=$(_=>_==null?void 0:_.userManagement.taskData);const Xe=$(_=>_.costCenter.payload);$(_=>_.payload.dynamicKeyValues);const{fetchedCostCenterData:xt,fetchReqBenchDataCC:Q}=$(_=>_.costCenter),pt=$(_=>_.request.requestHeader.requestType),yt=(Te=Xe==null?void 0:Xe.requestHeaderData)==null?void 0:Te.TemplateName,Gt=(d==null?void 0:d.childRequestIds)!=="Not Available"&&typeof tt=="object"&&tt!==null&&Object.keys(tt).length===0&&Be==="true";let qt=localStorage==null?void 0:localStorage.getItem(Yt.CURRENT_TASK);const Tt=JSON.parse(qt||"{}");let Nt=Tt?Tt==null?void 0:Tt.itmStatus:"";const $t=_=>{let O="";O="getAllCostCenterFromExcel",We("Initiating Excel Upload"),_e(!0);const De=new FormData;[..._].forEach(ue=>De.append("files",ue)),De.append("dtName",Y===ee.CREATE_WITH_UPLOAD||Y===ee.EXTEND_WITH_UPLOAD?"MDG_CC_FIELD_CONFIG":"MDG_CHANGE_TEMPLATE_DT"),De.append("version",Y===ee.CREATE_WITH_UPLOAD||Y===ee.EXTEND_WITH_UPLOAD?"v3":"v6"),De.append("requestId",me||""),De.append("IsSunoco","false"),De.append("screenName",Y||"");const ye=ue=>{var J,ce;ue.statusCode===200?(Qe(!1),_e(!1),We(""),C((J=Ke)==null?void 0:J.REQUEST_BENCH)):(Qe(!1),_e(!1),We(""),C((ce=Ke)==null?void 0:ce.REQUEST_BENCH))},Ne=()=>{var ue;_e(!1),We(""),C((ue=Ke)==null?void 0:ue.REQUEST_BENCH)};G(`/${z}/massAction/${O}`,"postformdata",ye,Ne,De)};i.useEffect(()=>{Rt()},[]);const Ze=_=>{const O=ye=>{y(as({keyName:"CompCode",data:(ye==null?void 0:ye.body)||[]})),y(Pt({keyName:"CompCode",data:ye.body}))},De=()=>{};G(`/${z}/data/getCompanyCodeBasedOnControllingArea?controllingArea=${_}&rolePrefix=ETP`,"get",O,De)},Rt=()=>{const _={decisionTableId:null,decisionTableName:"MDG_ATTACHMENTS_LIST_DT",version:"v1",rulePolicy:null,validityDate:null,conditions:[{"MDG_CONDITIONS.MDG_ATTACHMENTS_OBJECT_TYPE":"ET Cost Center","MDG_CONDITIONS.MDG_ATTACHMENTS_SCENARIO":ee.CREATE,"MDG_CONDITIONS.MDG_ATTACHMENTS_REG_COMP_CODE":1}],systemFilters:null,systemOrders:null,filterString:null},O=Ne=>{var ue,J,ce;if((Ne==null?void 0:Ne.statusCode)===200){const Et=((ce=(J=(ue=Ne==null?void 0:Ne.data)==null?void 0:ue.result)==null?void 0:J[0])==null?void 0:ce.MDG_ATTACHMENTS_ACTION_TYPE)??[];Pe(Et)}else console.warn("Unexpected statusCode:",Ne==null?void 0:Ne.statusCode)},De=Ne=>{console.error("Attachment fetch error:",Ne)},ye=Ie.environment==="localhost"?Ye.INVOKE_RULES.LOCAL:Ye.INVOKE_RULES.PROD;G(`/${co}${ye}`,"post",O,De,_)},ft=_=>{y(zo(_))},Ue=()=>{se(!0)},Lt=()=>{It(!0)},mt=_=>{It(_)},ve=()=>{var ye,Ne,ue,J,ce,Et,st,dt,He;const _={dtName:Y===((ye=ee)==null?void 0:ye.CREATE)||Y===((Ne=ee)==null?void 0:Ne.CREATE_WITH_UPLOAD)||pt===((ue=ee)==null?void 0:ue.CREATE)||pt===((J=ee)==null?void 0:J.CREATE_WITH_UPLOAD)?"MDG_CC_FIELD_CONFIG":"MDG_CHANGE_TEMPLATE_DT",version:Y===((ce=ee)==null?void 0:ce.CREATE)||Y===((Et=ee)==null?void 0:Et.CREATE_WITH_UPLOAD)||pt===((st=ee)==null?void 0:st.CREATE)||pt===((dt=ee)==null?void 0:dt.CREATE_WITH_UPLOAD)?"v3":"v6",requestId:me,scenario:pt||Y||((He=ee)==null?void 0:He.CREATE_WITH_UPLOAD),isChild:!!(tt&&Object.keys(tt).length!==0||d.isChildRequest)},O=St=>{const ut=URL.createObjectURL(St),ge=document.createElement("a");ge.href=ut,ge.setAttribute("download",`${_!=null&&_.scenario?_==null?void 0:_.scenario:"Mass_Create"}_Data Export.xlsx`),document.body.appendChild(ge),ge.click(),document.body.removeChild(ge),URL.revokeObjectURL(ut),_e(!1),We("")},De=St=>{console.error("Attachment fetch error:",St)};G(`/${z}/excel/exportCCExcel`,"postandgetblob",O,De,_)};i.useEffect(()=>{y(Nn()),y(_n())},[]),i.useEffect(()=>(Bt||(de("costCenter"),y(In(!0))),xn(Yt.MODULE,Ve.CC),Rt(),$e(Rn("CC")),()=>{Ln(Yt.MODULE)}),[]),i.useEffect(()=>{u&&we([!0])},[u]);const it=_=>{const O=(d==null?void 0:d.childRequestIds)!=="Not Available",De={sort:"id,asc",parentId:O?"":_,massCreationId:O&&(Y===ee.CREATE||Y===ee.CREATE_WITH_UPLOAD)?_:"",massChangeId:O&&(Y===ee.CHANGE||Y===ee.CHANGE_WITH_UPLOAD)?_:"",page:0,size:10},ye=ue=>{var He,St,ut,ge,Zt;const J=(ue==null?void 0:ue.body)||[];let ce=(He=ue==null?void 0:ue.body[0])==null?void 0:He.Torequestheaderdata,Et=(St=ue==null?void 0:ue.body[0])==null?void 0:St.TotalIntermediateTasks,st={RequestId:ce.RequestId,RequestPrefix:ce.RequestPrefix,ReqCreatedBy:ce.ReqCreatedBy,ReqCreatedOn:ce.ReqCreatedOn,ReqUpdatedOn:ce.ReqUpdatedOn,RequestType:ce.RequestType,RequestDesc:ce.RequestDesc,RequestStatus:(ut=J==null?void 0:J[0])==null?void 0:ut.RequestStatus,RequestPriority:ce.RequestPriority,FieldName:ce.FieldName,TemplateName:ce.TemplateName,Division:ce.Division,region:ce.region,leadingCat:ce.leadingCat,firstProd:ce.firstProd,launchDate:ce.launchDate,isBifurcated:ce.isBifurcated,screenName:ce.screenName,TotalIntermediateTasks:Et,childRequestId:(Zt=(ge=J==null?void 0:J[0])==null?void 0:ge.ToChildHeaderdata)==null?void 0:Zt.RequestId};y(Hn(st)),y(Ss(ce)),j(J);const dt=Xn(J);J.forEach(B=>{var nt,rt,Wt,ao,gt,Ft,io,Ot,Ht,Xt,eo,Ut,uo,to,Co,To,go,vt,zt,bt,Mt,oo;if((rt=(nt=B==null?void 0:B.ToCostCenterData)==null?void 0:nt[0])!=null&&rt.AddrCountry&&zn((ao=(Wt=B==null?void 0:B.ToCostCenterData)==null?void 0:Wt[0])==null?void 0:ao.AddrCountry,y,(Ft=(gt=B==null?void 0:B.ToCostCenterData)==null?void 0:gt[0])==null?void 0:Ft.CostCenterID),(io=J==null?void 0:J[0])!=null&&io.ControllingArea&&Ze((Ot=J==null?void 0:J[0])==null?void 0:Ot.ControllingArea),(Xt=(Ht=B==null?void 0:B.ToCostCenterData)==null?void 0:Ht[0])!=null&&Xt.CompCode&&(bs((Ut=(eo=B==null?void 0:B.ToCostCenterData)==null?void 0:eo[0])==null?void 0:Ut.CompCode,y,(to=(uo=B==null?void 0:B.ToCostCenterData)==null?void 0:uo[0])==null?void 0:to.CostCenterID),jn((Co=J==null?void 0:J[0])==null?void 0:Co.ControllingArea,(go=(To=B==null?void 0:B.ToCostCenterData)==null?void 0:To[0])==null?void 0:go.CompCode,y,(zt=(vt=B==null?void 0:B.ToCostCenterData)==null?void 0:vt[0])==null?void 0:zt.CostCenterID)),(bt=B==null?void 0:B.Torequestheaderdata)!=null&&bt.RequestType&&Je(),(Mt=B==null?void 0:B.Torequestheaderdata)!=null&&Mt.TemplateName){let so=(oo=B==null?void 0:B.Torequestheaderdata)==null?void 0:oo.TemplateName;const At=Jt.filter(Ce=>(Ce==null?void 0:Ce.MDG_CHANGE_TEMPLATE_NAME)===so&&(Ce==null?void 0:Ce.MDG_MAT_CHANGE_TYPE)==="Item"&&(Ce==null?void 0:Ce.MDG_MAT_FIELD_VISIBILITY)!=="Hidden"&&(Ce==null?void 0:Ce.MDG_MAT_FIELD_VISIBILITY)!=="Display").sort((Ce,kt)=>{const no=Number(Ce==null?void 0:Ce.MDG_MAT_FIELD_SEQUENCE)||0,ro=Number(kt==null?void 0:kt.MDG_MAT_FIELD_SEQUENCE)||0;return no-ro}),ho=[...new Set(At.map(Ce=>Ce==null?void 0:Ce.MDG_MAT_FIELD_NAME).filter(Boolean))].map(Ce=>({code:Ce}));y(Pt({keyName:"FieldName",data:ho||[]}))}}),y(Vn(dt==null?void 0:dt.payload)),y(qs(dt==null?void 0:dt.payload))},Ne=ue=>{console.error("Error fetching CC Create data:",ue)};G(`/${z}/data/displayMassCostCenterDto`,"post",ye,Ne,De)};i.useEffect(()=>((async()=>{me?(await it(me),(Y===ee.CHANGE_WITH_UPLOAD&&!(d!=null&&d.length)||Y===ee.CREATE_WITH_UPLOAD||Y===ee.EXTEND_WITH_UPLOAD)&&((d==null?void 0:d.reqStatus)===Xo.DRAFT||(d==null?void 0:d.reqStatus)===Xo.UPLOAD_FAILED)?(y(zo(0)),N(!1),F(!1)):(y(zo(1)),N(!0),F(!0))):y(zo(0))})(),()=>{y(On()),y(Un()),y(Ss({})),y(Pt({keyName:"FieldName",data:[]}))}),[me,y]);const Ee=()=>{var _,O,De;me&&!Be?C((_=Ke)==null?void 0:_.MY_TASK):Be?C((O=Ke)==null?void 0:O.REQUEST_BENCH):!me&&!Be&&C((De=Ke)==null?void 0:De.MASTER_DATA_CC)},I=()=>{be(!1)},le={costCenterDetails:Y===((Ae=ee)==null?void 0:Ae.CREATE)||Y===((Se=ee)==null?void 0:Se.CREATE_WITH_UPLOAD)?wt(Xe,E,me,tt):Vt(W,tt,Be,Q),dtName:"MDG_CC_FIELD_CONFIG",version:"v3",requestId:me||"",scenario:"Create",templateName:"",region:"US"};return A("div",{children:[A(X,{sx:{padding:2},children:[A(Dt,{sx:{display:"flex",justifyContent:"space-between",alignItems:"center"},children:[me||ke?A(X,{children:[A(qe,{variant:"h6",sx:{mb:1,textAlign:"left",display:"flex",alignItems:"center",gap:1},children:[s(rr,{sx:{fontSize:"1.5rem"}}),oe("Request Header ID"),":"," ",s("span",{children:ke?E==null?void 0:E.requestId:`${me}`})]}),yt&&A(qe,{variant:"h6",sx:{mb:1,textAlign:"left",display:"flex",alignItems:"center",gap:1},children:[s(Ds,{sx:{fontSize:"1.5rem"}}),"Template Name: ",s("span",{children:yt})]})]}):s("div",{style:{flex:1}}),at&&s(tr,{module:Ve.CC,open:!0,closeModal:mt,requestId:(W==null?void 0:W.RequestId)||me,requestType:"create"}),he===1&&A(X,{sx:{display:"flex",justifyContent:"flex-end",gap:"1rem"},children:[s(Me,{variant:"outlined",size:"small",title:"Error Report",disabled:!me,onClick:()=>_t(!0),color:"primary",children:s(qn,{sx:{padding:"2px"}})}),s(Me,{variant:"outlined",disabled:!me,size:"small",onClick:Lt,title:"Change Log",children:s(cr,{sx:{padding:"2px"}})}),s(Me,{variant:"outlined",disabled:!me,size:"small",onClick:ve,title:"Export Excel",children:s(ar,{sx:{padding:"2px"}})})]})]}),s(jo,{onClick:()=>{var _;if(Be==="true"){C((_=Ke)==null?void 0:_.REQUEST_BENCH);return}be(!0)},color:"primary","aria-label":"upload picture",component:"label",sx:{left:"-10px"},title:"Back",children:s($n,{sx:{fontSize:"25px",color:"#000000"}})}),s(kn,{nonLinear:!0,activeStep:he,sx:{display:"flex",alignItems:"center",justifyContent:"center",margin:"-35px 14% 10px",marginTop:"-35px"},children:gr.map((_,O)=>s(vn,{completed:ie[O],children:s(Mn,{color:"error",disabled:O===1&&!u||O===2&&!k||O===3&&!u&&!k,onClick:()=>ft(O),sx:{fontSize:"50px",fontWeight:"bold"},children:s("span",{style:{fontSize:"15px",fontWeight:"bold"},children:_})})},_))}),he===0&&A(So,{children:[s(Cr,{apiResponse:pe,reqBench:Be,downloadClicked:L,setDownloadClicked:se,setIsSecondTabEnabled:N,setIsAttachmentTabEnabled:F}),(Y===ee.CHANGE_WITH_UPLOAD||Y===ee.CREATE_WITH_UPLOAD||Y===ee.EXTEND_WITH_UPLOAD)&&((d==null?void 0:d.reqStatus)==Xo.DRAFT&&!((Z=d==null?void 0:d.material)!=null&&Z.length)||(d==null?void 0:d.reqStatus)==Xo.UPLOAD_FAILED)&&s(lr,{handleDownload:Ue,setEnableDocumentUpload:Qe,enableDocumentUpload:ae,handleUploadMaterial:$t})]}),he===1&&W.RequestType&&(W.RequestType==="Change"||W.RequestType==="Change with Upload"?s(Hs,{reqBench:Be,requestId:me,apiResponses:pe,setIsAttachmentTabEnabled:!0,setCompleted:we,downloadClicked:L,setDownloadClicked:se,module:Ve.CC,isDisabled:Gt}):s(Tr,{reqBench:Be,apiResponses:pe,module:Ve.CC,isDisabled:Gt,fieldDisable:Nt})),he===2&&s(Qn,{requestStatus:d!=null&&d.reqStatus?d==null?void 0:d.reqStatus:Xo.ENABLE_FOR_FIRST_TIME,attachmentsData:fe,requestIdHeader:ke||me,pcNumber:Le,module:Ve.CC,artifactName:wn.CC}),he===3&&s(X,{sx:{width:"100%",overflow:"auto"},children:s(Jn,{module:(ot=Ve)==null?void 0:ot.CC,payloadForPreviewDownloadExcel:le})})]}),s(Ko,{blurLoading:te,loaderMessage:ht}),s(Zn,{dialogState:Fe,closeReusableDialog:()=>_t(!1),module:(Ge=Ve)==null?void 0:Ge.CC,isHierarchyCheck:!1}),K&&A(Pn,{isOpen:K,titleIcon:s(Wn,{size:"small",sx:{color:(xe=(ze=et)==null?void 0:ze.secondary)==null?void 0:xe.amber,fontSize:"20px"}}),Title:"Warning",handleClose:I,children:[s(bo,{sx:{mt:2},children:Fn.LEAVE_PAGE_MESSAGE}),A(Ao,{children:[s(Me,{variant:"outlined",size:"small",sx:{...Bn},onClick:I,children:oe("No")}),s(Me,{variant:"contained",size:"small",sx:{...Gn},onClick:Ee,children:oe("Yes")})]})]})]})};export{nl as default};
