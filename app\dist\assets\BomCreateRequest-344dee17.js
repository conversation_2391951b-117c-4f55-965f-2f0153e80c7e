import{g as tt,cw as Fe,u as Ge,n as v,r as i,da as kt,C as he,ae as pe,bK as Ce,cp as te,aT as me,o as ve,a as ht,s as Le,yN as ke,cy as _o,yO as Eo,cz as xt,yP as fo,eT as lt,yQ as Ht,aX as k,J as Ze,c as I,aZ as _t,O as Ie,b1 as To,j as e,d as re,B as ne,an as se,aH as go,aG as Ft,aD as Mo,aY as Wt,yR as Ao,f1 as yt,yS as Et,yT as we,c9 as Do,ca as Co,cb as Io,cs as ft,ag as bo,Z as g,a6 as ye,T as Be,bE as Oo,ch as $t,cH as Yt,yU as ee,a9 as Vt,bu as Xe,a1 as ct,aa as dt,yV as Bt,b6 as No,b5 as Ro,aj as jt,al as Tt,$ as So,bO as xo,bP as yo,bQ as vt,bG as Gt,am as gt,ai as zt,fL as Bo,aJ as Mt,yW as ut,eb as vo,yX as Qt,c5 as Go,cM as Lo,cN as Uo,yY as He,cO as Po,cZ as At,bf as et,g5 as qo,fR as Lt,aO as xe,xR as wo,aK as ko,aP as Ho,yZ as Fo,y3 as Wo,cA as $o,bc as Yo,y_ as Vo,d8 as jo,d2 as zo,d3 as Qo,d4 as Ko,d5 as Jo,F as Ut,cI as De,d7 as Xo,fY as Zo,br as en,ad as tn,d6 as on,d9 as nn}from"./index-f7d9b065.js";import{d as an}from"./PermIdentityOutlined-0746a749.js";import{d as sn}from"./FeedOutlined-41109ec9.js";import{E as rn}from"./ExcelOperationsCard-49e9ffd2.js";import{F as ln}from"./FilterFieldGlobal-f8e8f75f.js";import{D as cn,C as Pt,B as dn,b as it,A as un,P as mn}from"./PreviewPage-634057fa.js";import{d as Kt,a as Jt}from"./CloseFullscreen-2870eb3e.js";import{u as pn}from"./useDynamicWorkflowDT-955b7628.js";import{S as ge}from"./SingleSelectDropdown-aee403d4.js";import{a as hn,d as _n}from"./FileUploadOutlined-4a68a28a.js";import{d as En}from"./Delete-5278579a.js";import{A as mt}from"./AdapterDayjs-2a9281df.js";import{D as pt}from"./DatePicker-a8e9bd4a.js";import{d as qt}from"./TrackChangesTwoTone-7a2ab513.js";import"./CloudDownload-9a7605e9.js";import"./CloudUpload-0ba6431e.js";import"./AttachmentUploadDialog-43cc9099.js";import"./utilityImages-067c3dc2.js";import"./AttachFile-8d552da8.js";import"./UtilDoc-6f590135.js";import"./FileDownloadOutlined-59854a55.js";import"./VisibilityOutlined-b6cd6d28.js";import"./DeleteOutlined-e668453f.js";import"./ReusablePromptBox-e1871d49.js";import"./featureConfig-652a9f8d.js";import"./Description-ab582559.js";import"./DataObject-52409c14.js";import"./Download-52c4427b.js";import"./useFinanceCostingRows-ffbb569f.js";import"./CheckCircleOutline-e186af3e.js";import"./advancedFormat-4f6292d4.js";import"./customParseFormat-1bc1aa07.js";import"./isBetween-c1c6beb9.js";import"./useMobilePicker-9b56b5b6.js";import"./CSSTransition-cd337b47.js";const fn=(c,a)=>{const n=tt(),{showSnackbar:x}=Fe(),f=Ge(),p=new URLSearchParams(f.search),G=p.get("RequestId"),h=p.get("RequestType"),b=v(T=>T.bom.requestHeaderID),o=v(T=>T.bom.BOMpayloadData);return{handleDownloadBOM:i.useCallback(()=>{var A;c("Please wait 1-2 minutes while real-time data are downloaded into the template. Thank you for your patience."),a(!0);let T={region:(o==null?void 0:o.Region)||"US",scenario:(o==null?void 0:o.RequestType)||h,dtName:"MDG_BOM_MATERIAL_FIELD_CONFIG",version:"v3",role:(A=kt)==null?void 0:A.REQ_INITIATE_DOWNLOAD_MAT,requestId:o!=null&&o.RequestId?o==null?void 0:o.RequestId:G||b};const O=_=>{var l,y;if((_==null?void 0:_.size)==0){a(!1),c(""),x((l=pe)==null?void 0:l.NO_DATA_FOUND,"error");return}const w=URL.createObjectURL(_),D=document.createElement("a");D.href=w,D.setAttribute("download","Mass_Create.xlsx"),document.body.appendChild(D),D.click(),document.body.removeChild(D),URL.revokeObjectURL(w),a(!1),c(""),x(`${o!=null&&o.TemplateName?`${o.TemplateName}_Mass Change`:"Mass_Create"}.xlsx has been downloaded successfully.`,"success"),n((y=Ce)==null?void 0:y.REQUEST_BENCH)},r=()=>{var _;a(!1),x((_=pe)==null?void 0:_.ERR_DOWNLOADING_EXCEL,"error")},R=`/${te}${me.EXCEL.DOWNLOAD_EXCEL_BOM}`;he(R,"postandgetblob",O,r,T)},[a,c,o])}},Tn=({setIsSecondTabEnabled:c,setIsAttachmentTabEnabled:a,downloadClicked:n,setDownloadClicked:x})=>{var j;const{getDtCall:f,dtData:p}=ve();ve();const{t:G}=ht(),{showSnackbar:h}=Fe(),b=v(u=>u.bom.headerFieldsBOM),o=Ge(),W=new URLSearchParams(o.search),T=W.get("reqBench"),O=W.get("RequestId"),r=v(u=>u.bom.requestHeaderID),[R,A]=i.useState(""),[_,w]=i.useState(!1),D=v(u=>u.userManagement.userData),l=v(u=>u.bom.BOMpayloadData),[y,N]=i.useState(!1),[M,X]=i.useState("systemGenerated"),{handleDownloadBOM:Z}=fn(A,w),P=Le();P(ke({keyName:"RequestPriority",data:_o})),P(ke({keyName:"TemplateName",data:Eo})),P(ke({keyName:(j=xt)==null?void 0:j.REQUEST_TYPE,data:fo})),!O&&!T&&(P(lt({keyName:"ReqCreatedBy",data:D==null?void 0:D.user_id})),P(lt({keyName:"RequestStatus",data:"DRAFT"}))),i.useEffect(()=>{C()},[l==null?void 0:l.RequestType]),i.useEffect(()=>{L()},[l==null?void 0:l.TemplateName]),i.useEffect(()=>{var u;if(p){const H={"Header Data":((u=p==null?void 0:p.result[0])==null?void 0:u.MDG_MAT_REQUEST_HEADER_CONFIG).sort((z,de)=>z.MDG_MAT_SEQUENCE_NO-de.MDG_MAT_SEQUENCE_NO).map(z=>({fieldName:z.MDG_MAT_UI_FIELD_NAME,sequenceNo:z.MDG_MAT_SEQUENCE_NO,fieldType:z.MDG_MAT_FIELD_TYPE,maxLength:z.MDG_MAT_MAX_LENGTH,value:z.MDG_MAT_DEFAULT_VALUE,visibility:z.MDG_MAT_VISIBILITY,jsonName:z.MDG_MAT_JSON_FIELD_NAME}))};P(Ht(H))}},[p]),i.useEffect(()=>{var u;if(n){if((l==null?void 0:l.RequestType)===k.CREATE_WITH_UPLOAD){N(!0);return}if((l==null?void 0:l.RequestType)===((u=k)==null?void 0:u.CHANGE_WITH_UPLOAD))return}},[n]);const Q=()=>{var u;x(!1),N(!1),X("systemGenerated"),O||navigate((u=Ce)==null?void 0:u.REQUEST_BENCH)},ae=u=>{var q;X((q=u==null?void 0:u.target)==null?void 0:q.value)},ce=()=>{M==="systemGenerated"&&(Z(),Q()),M==="mailGenerated"&&Q()},d=()=>{var q,B;let u=!0;return l&&((q=b[Object.keys(b)])!=null&&q.length)?(B=b[Object.keys(b)[0]])==null||B.forEach(H=>{var z;!l[H.jsonName]&&H.visibility===((z=Mo)==null?void 0:z.MANDATORY)&&(u=!1)}):u=!1,u},C=()=>{let u={decisionTableId:null,decisionTableName:Ze.MDG_MAT_REQUEST_HEADER_CONFIG,version:"v2",conditions:[{"MDG_CONDITIONS.MDG_MAT_SCENARIO":(l==null?void 0:l.RequestType)||"Create","MDG_CONDITIONS.MDG_MODULE":"BOM"}]};f(u)},L=()=>{},S=()=>{var de;const u=new Date(l==null?void 0:l.ReqCreatedOn).getTime(),q=`/Date(${Date.now()})/`,B={RequestId:r||"",ReqCreatedBy:(D==null?void 0:D.user_id)||"",ReqCreatedOn:u?`/Date(${u})/`:q,ReqUpdatedOn:u?`/Date(${u})/`:q,RequestType:(l==null?void 0:l.RequestType)||"",RequestPrefix:"",RequestPriority:(l==null?void 0:l.RequestPriority)||"",RequestDesc:(l==null?void 0:l.RequestDesc)||"",RequestStatus:"DRAFT",TemplateName:(l==null?void 0:l.TemplateName)||"",FieldName:(de=l==null?void 0:l.FieldName)!=null&&de.join?l.FieldName.join("$^$"):l.FieldName||"",Region:(l==null?void 0:l.Region)||"",filterDetails:"",SapSystem:"",IsBifurcated:!0,IncompleteChildTasks:""},H=J=>{var be,Oe,fe;if(h(`${G(Wt.REQUEST_HEADER_CREATED)} ${(be=J==null?void 0:J.body)==null?void 0:be.requestId}`,"success"),P(Ao((Oe=J==null?void 0:J.body)==null?void 0:Oe.requestId)),P(yt({keyName:xt.REQUEST_ID,data:(fe=J==null?void 0:J.body)==null?void 0:fe.requestId})),Object.entries(l).forEach(([_e,Ne])=>{P(yt({keyName:_e,data:Ne.code||Ne}))}),a(!0),P(Et()),(l==null?void 0:l.RequestType)===k.CREATE_WITH_UPLOAD){N(!0);return}P(we(1)),c(!0)},z=()=>{h("Error occured while saving Request Header","error")};he(`/${te}${me.MASS_ACTION.CREATE_BOM_REQUEST}`,"post",H,z,B)};return I("div",{children:[I(_t,{spacing:2,children:[Object.entries(b).map(([u,q])=>I(Ie,{item:!0,md:12,sx:{backgroundColor:"white",maxHeight:"max-content",height:"max-content",borderRadius:"8px",border:"1px solid #E0E0E0",mt:.25,boxShadow:"0px 2px 14px 0px rgba(48, 38, 185, 0.10)",...To},children:[e(re,{sx:{fontSize:"12px",fontWeight:"700",paddingBottom:"10px"},children:G(u)}),e(ne,{children:e(Ie,{container:!0,spacing:1,children:q.filter(B=>B.visibility!=="Hidden").sort((B,H)=>B.sequenceNo-H.sequenceNo).map(B=>e(ln,{isHeader:!0,field:B,dropDownData:{},module:"BOM",disabled:O||r,requestHeader:!0},B.id))})}),!O&&!r&&e(ne,{sx:{display:"flex",justifyContent:"flex-end",marginTop:"20px"},children:e(se,{variant:"contained",color:"primary",onClick:S,disabled:!d(),children:G("Save Request Header")})}),e(go,{})]},u)),e(cn,{onDownloadTypeChange:ce,open:y,downloadType:M,handleDownloadTypeChange:ae,onClose:Q})]}),e(Ft,{blurLoading:_,loaderMessage:R})]})};var Dt={},gn=Co;Object.defineProperty(Dt,"__esModule",{value:!0});var Ct=Dt.default=void 0,Mn=gn(Do()),An=Io;Ct=Dt.default=(0,Mn.default)((0,An.jsx)("path",{d:"M21 8H3V4h18zm0 2H3v4h18zm0 6H3v4h18z"}),"TableRows");const wt=ft(bo)(({theme:c})=>({boxShadow:"0 4px 20px rgba(0, 0, 0, 0.08)",borderRadius:"16px",overflow:"hidden",border:`1px solid ${g.primary.border}`,"& .MuiDataGrid-root":{border:"none","& .MuiDataGrid-cell":{borderBottom:`1px solid ${g.primary.pale}`,padding:"8px 16px","&:focus":{outline:"none"},"&:focus-within":{outline:`2px solid ${g.primary.main}`,outlineOffset:"-2px"}},"& .MuiDataGrid-columnHeaders":{backgroundColor:g.primary.ultraLight,borderBottom:`2px solid ${g.primary.border}`,"& .MuiDataGrid-columnHeader":{padding:"12px 16px","&:focus":{outline:"none"},"&:focus-within":{outline:`2px solid ${g.primary.main}`,outlineOffset:"-2px"}}},"& .MuiDataGrid-row":{"&:nth-of-type(even)":{backgroundColor:g.primary.veryLight},"&:hover":{backgroundColor:`${g.primary.light}40`},"&.Mui-selected":{backgroundColor:`${g.primary.main}20`,"&:hover":{backgroundColor:`${g.primary.main}30`}}},"& .MuiDataGrid-virtualScroller":{"&::-webkit-scrollbar":{width:"8px",height:"8px"},"&::-webkit-scrollbar-track":{backgroundColor:g.primary.veryLight},"&::-webkit-scrollbar-thumb":{backgroundColor:g.primary.pale,borderRadius:"4px","&:hover":{backgroundColor:g.primary.border}}}}})),Dn=ft(ne)(({theme:c})=>({padding:"16px 20px",display:"flex",justifyContent:"space-between",alignItems:"center",borderBottom:`1px solid ${g.primary.border}`,backgroundColor:g.primary.ultraLight,gap:"16px"})),Xt=ft("div")(({theme:c})=>({display:"flex",flexDirection:"column",alignItems:"center",justifyContent:"center",height:"100%",padding:"32px",backgroundColor:g.primary.veryLight,"& svg":{color:g.secondary.grey,fontSize:"48px",marginBottom:"16px"}}));function Cn(){return I(Xt,{children:[e(Ct,{}),e(re,{variant:"h6",sx:{color:g.secondary.grey,fontWeight:500,marginBottom:"8px"},children:pe.NO_DATA_AVAILABLE}),e(re,{variant:"body2",sx:{color:g.secondary.grey,textAlign:"center"},children:pe.NO_RECORDS})]})}const In=({viewsDt:c,activeViewName:a,rows:n,onRowsUpdate:x})=>{var P,Q,ae,ce;const f=(Q=(P=c==null?void 0:c.flatMap(d=>d.MDG_BOM_MATERIAL_FIELD_CONFIG_ACTION_TYPE))==null?void 0:P.filter(d=>d.MDG_VIEW_NAME===a))==null?void 0:Q.sort((d,C)=>(d.MDG_SEQUENCE_NO??0)-(C.MDG_SEQUENCE_NO??0)),p=v(d=>d.bom.dropDownData||{}),G=(ce=(ae=c==null?void 0:c.flatMap(d=>d.MDG_BOM_MATERIAL_FIELD_CONFIG_ACTION_TYPE))==null?void 0:ae.find(d=>d.MDG_VIEW_NAME===a))==null?void 0:ce.MDG_CARD_NAME,h=v(d=>d.bom.selectedRowID),o=v(d=>d.bom.bomRows).find(d=>d.id===h),W=Ge(),T=new URLSearchParams(W.search),O=T.get("reqBench"),r=T.get("RequestId"),R=v(d=>{var C;return(C=d.bom.BOMpayloadData)==null?void 0:C.RequestStatus});console.log(R,"requestStatus");const[A,_]=i.useState({}),w=Le(),D=i.useRef({}),l=!Yt.includes(R)||r&&!O,y=(d,C)=>{if(!d){_(L=>({...L,[C]:[]}));return}he(`/${te}/data/getBOMComponentBasedOnPlant?plant=${encodeURIComponent(d)}`,"get",L=>{const S=(L.body||[]).map(j=>({code:j.code,desc:j.desc||j.code,...j}));_(j=>({...j,[C]:S}))},L=>{_(S=>({...S,[C]:[]})),console.error(L)})};if(i.useEffect(()=>{n.forEach(d=>{const C=(o==null?void 0:o.plant)||(o==null?void 0:o.Plant),L=`${h}_${d.id}`;D.current[L]!==C&&(D.current[L]=C,y(C,L))})},[n,o,h]),i.useEffect(()=>{let d=!1;const C=n.map(L=>{const S=(o==null?void 0:o.plant)||(o==null?void 0:o.Plant);return(!S||S==="")&&L.Component&&L.Component!==""?(d=!0,{...L,Component:""}):L});d&&x(C)},[n,o]),!f||f.length===0)return e(wt,{children:I(Xt,{children:[e(Ct,{}),e(re,{variant:"h6",sx:{color:g.secondary.grey,fontWeight:500,marginBottom:"8px"},children:pe.NO_FIELDS_CONFIGURED}),e(re,{variant:"body2",sx:{color:g.secondary.grey,textAlign:"center"},children:pe.NO_DATA_CONFIGURED})]})});const N=()=>{const d={id:Date.now(),...f.reduce((C,L)=>(C[L.MDG_JSON_FIELD_NAME||L.MDG_UI_FIELD_NAME]="",C),{})};x([...n,d])},M=d=>{const C=n.filter(L=>L.id!==d);x(C)},X=(d,C,L)=>{const S=d.MDG_JSON_FIELD_NAME||d.MDG_UI_FIELD_NAME,j=d.MDG_VISIBILITY==="Display";if(d.MDG_FIELD_TYPE==="Drop Down"&&(S==="Component"||S==="component")){const u=`${h}_${L}`,q=(A[u]||[]).find(H=>H.code===C)||(C?{code:C,desc:""}:""),B=q&&q.code&&!(A[u]||[]).some(H=>H.code===q.code)?[...A[u]||[],q]:A[u]||[];return e(ge,{options:B,value:q,onChange:H=>{const z=(H==null?void 0:H.code)||"",de=n.map(J=>J.id===L?{...J,[S]:z}:J);x(de),w(ee({rowId:h,status:!1}))},placeholder:`SELECT ${d.MDG_UI_FIELD_NAME.toUpperCase()}`,disabled:j})}switch(d.MDG_FIELD_TYPE){case"Input":return e(dt,{size:"small",value:C||"",onChange:u=>{const q=n.map(B=>B.id===L?{...B,[S]:u.target.value}:B);x(q),w(ee({rowId:h,status:!1}))},fullWidth:!0,variant:"outlined",placeholder:`ENTER ${d.MDG_UI_FIELD_NAME.toUpperCase()}`,disabled:j||l,sx:{"& .MuiOutlinedInput-root":{"& fieldset":{borderColor:g.primary.pale},"&:hover fieldset":{borderColor:g.primary.border},"&.Mui-focused fieldset":{borderColor:g.primary.main}}}});case"Calendar":return e(ct,{dateAdapter:mt,children:e(pt,{value:C?Xe(C):null,onChange:u=>{const q=n.map(B=>B.id===L?{...B,[S]:u}:B);x(q),w(ee({rowId:h,status:!1}))},disabled:j,format:"DD/MM/YYYY",slotProps:{textField:{size:"small",fullWidth:!0,placeholder:`SELECT ${d.MDG_UI_FIELD_NAME.toUpperCase()}`,sx:{"& .MuiOutlinedInput-root":{"& fieldset":{borderColor:g.primary.pale},"&:hover fieldset":{borderColor:g.primary.border},"&.Mui-focused fieldset":{borderColor:g.primary.main}}}}}})});case"Drop Down":return e(ge,{options:p[S]||[],value:C||"",onChange:u=>{const q=(u==null?void 0:u.code)||"",B=n.map(H=>H.id===L?{...H,[S]:q}:H);x(B),w(ee({rowId:h,status:!1}))},placeholder:`SELECT ${d.MDG_UI_FIELD_NAME.toUpperCase()}`,disabled:j||l});case"Check Box":return e(Vt,{checked:C||!1,onChange:u=>{const q=n.map(B=>B.id===L?{...B,[S]:u.target.checked}:B);x(q),w(ee({rowId:h,status:!1}))},sx:{color:g.primary.main,"&.Mui-checked":{color:g.primary.main}},disabled:j||l});default:return e(re,{variant:"body2",sx:{color:C?g.text.primary:g.text.disabled},children:C||"-"})}},Z=[...f.map(d=>({field:d.MDG_JSON_FIELD_NAME||d.MDG_UI_FIELD_NAME,headerName:d.MDG_VISIBILITY==="Mandatory"?I("span",{children:[d.MDG_UI_FIELD_NAME,e("span",{style:{color:"red",marginLeft:2},children:"*"})]}):d.MDG_UI_FIELD_NAME,flex:1,minWidth:150,renderCell:C=>X(d,C.value,C.row.id)})),...a!=="General"?[{field:"actions",headerName:"Actions",width:100,sortable:!1,filterable:!1,renderCell:d=>e(Be,{title:"Delete Row",children:e(ye,{onClick:()=>M(d.row.id),color:"error",size:"small",disabled:l,sx:{"&:hover":{backgroundColor:`${g.error.light}40`}},children:e(En,{})})})}]:[]];return I(wt,{children:[I(Dn,{children:[I(_t,{direction:"row",spacing:2,alignItems:"center",children:[G&&e(re,{variant:"h6",sx:{color:g.primary.main},children:G}),e(Oo,{label:`${n.length} records`,size:"small",sx:{backgroundColor:g.primary.light,color:g.primary.main,height:"24px",fontSize:"0.75rem"}})]}),a!=="General"&&e(Be,{title:"Add New Row",children:e(se,{variant:"contained",startIcon:e(hn,{}),onClick:N,size:"small",sx:{backgroundColor:g.primary.main,"&:hover":{backgroundColor:g.primary.dark},borderRadius:"8px",textTransform:"none",fontWeight:500},disabled:l,children:"Add Row"})})]}),e("div",{style:{width:"100%",height:"100%",overflowX:"auto"},children:e("div",{style:{height:"100%"},children:e($t,{columns:Z,rows:n,autoHeight:!1,disableSelectionOnClick:!0,hideFooter:!0,components:{NoRowsOverlay:Cn},style:{border:"1px solid #ccc",borderRadius:"8px",width:"100%",height:n.length===0?"180px":`${Math.min(n.length*50+100,300)}px`,overflow:"auto"}})})})]})},bn=({isTabsZoomed:c,toggleTabsZoom:a,t:n,viewNames:x,viewsDt:f,selectedRowID:p})=>{const[G,h]=i.useState(0),b=Le(),o=v(R=>R.bom.tabFieldValues),W=o[p]||{};i.useEffect(()=>{var l,y,N;if(!p)return;const R=Array.isArray((l=o[p])==null?void 0:l.Document)?o[p].Document:[],_=(Array.isArray((y=o[p])==null?void 0:y.Material)?o[p].Material:[]).length+R.length;let w=[];if(f&&(w=f.flatMap(M=>M.MDG_BOM_MATERIAL_FIELD_CONFIG_ACTION_TYPE).filter(M=>M.MDG_VIEW_NAME==="General").map(M=>M.MDG_JSON_FIELD_NAME||M.MDG_UI_FIELD_NAME)),(((N=o[p])==null?void 0:N.General)||[]).length!==_){const M=Array.from({length:_},(X,Z)=>{const P={id:Z+1};return w.forEach(Q=>P[Q]=""),P});b(Bt({rowId:p,viewName:"General",rows:M}))}},[o,p,f,b]);const T=(R,A)=>{h(A)},O=R=>{const A=x[G];b(Bt({rowId:p,viewName:A,rows:R}))},r=()=>{const R=x[G],A=W[R]||[];return e(In,{viewsDt:f,activeViewName:R,rows:A,onRowsUpdate:O})};return I(ne,{sx:{position:c?"fixed":"relative",top:c?0:"auto",left:c?0:"auto",right:c?0:"auto",bottom:c?0:"auto",width:c?"100vw":"100%",height:c?"100vh":"auto",zIndex:c?1004:void 0,backgroundColor:c?"white":"transparent",padding:c?"20px":"0",marginTop:"20px",display:"flex",flexDirection:"column",boxShadow:c?"0px 0px 15px rgba(0, 0, 0, 0.2)":"none",transition:"all 0.3s ease",borderRadius:"8px",border:"1px solid #e0e0e0"},children:[I(ne,{sx:{display:"flex",justifyContent:"space-between",alignItems:"center",padding:"8px 16px",borderRadius:"8px 8px 0 0"},children:[e(re,{variant:"h6",children:n("BOM Details")}),e(Be,{title:n(c?"Exit Zoom":"Zoom In"),sx:{zIndex:"1009"},children:e(ye,{onClick:a,color:"primary",sx:{backgroundColor:"rgba(0, 0, 0, 0.05)","&:hover":{backgroundColor:"rgba(0, 0, 0, 0.1)"}},children:c?e(Kt,{}):e(Jt,{})})})]}),I(ne,{sx:{display:"flex",flexDirection:"column"},children:[e(Ro,{value:G,onChange:T,sx:{top:0,position:"sticky",zIndex:1e3,backgroundColor:g.background.container,borderBottom:`1px solid ${g.border.light}`,"& .MuiTab-root":{minHeight:"48px",textTransform:"none",fontSize:"14px",fontWeight:600,color:g.black.graphite,"&.Mui-selected":{color:g.primary.main,fontWeight:700},"&:hover":{color:g.primary.main,opacity:.8}},"& .MuiTabs-indicator":{backgroundColor:g.primary.main,height:"3px"}},children:x.map((R,A)=>e(No,{label:R},A))}),e(ne,{sx:{padding:2,marginTop:2,flexGrow:1,overflow:"auto",height:c?"calc(100vh - 180px)":"auto"},children:r()})]})]})},On=({open:c,onClose:a,withReference:n,setWithReference:x,onProceed:f,t:p})=>{const G=v(r=>r.bom.dropDownData||{}),[h,b]=i.useState(""),[o,W]=i.useState(""),[T,O]=i.useState("");return I(zt,{fullWidth:!0,open:c,maxWidth:"lg",sx:{"&::webkit-scrollbar":{width:"1px"}},children:[e(jt,{sx:{justifyContent:"space-between",alignItems:"center",height:"max-content",padding:".5rem",paddingLeft:"1rem",backgroundColor:"#EAE9FF",display:"flex"},children:e(re,{variant:"h6",children:p("Add New BOM")})}),I(Tt,{sx:{padding:".5rem 1rem",alignItems:"center",justifyContent:"center",margin:"0px 25px"},children:[I(So,{component:"fieldset",sx:{paddingBottom:"2%"},children:[e(xo,{component:"legend",sx:{padding:"15px 0px",fontWeight:"600",fontSize:"15px"},children:p("Do You Want To Continue")}),I(yo,{row:!0,"aria-label":"profit-center-number",name:"profit-center-number",value:n,onChange:r=>x(r.target.value),children:[e(Gt,{value:"yes",control:e(vt,{}),label:p("With Reference")}),e(Gt,{value:"no",control:e(vt,{}),label:p("Without Reference")})]})]}),I(Ie,{container:!0,spacing:2,sx:{marginTop:1},children:[e(Ie,{item:!0,xs:4,children:e(ge,{options:G.Material||[],value:h,onChange:b,disabled:n==="no",placeholder:p("SELECT MATERIAL"),minWidth:180,listWidth:266})}),e(Ie,{item:!0,xs:4,children:e(ge,{options:G.Plant||[],value:o,disabled:n==="no",onChange:W,placeholder:p("SELECT PLANT"),minWidth:180,listWidth:266})}),e(Ie,{item:!0,xs:4,children:e(ge,{options:G.BOM||[],value:T,disabled:n==="no",onChange:O,placeholder:p("SELECT BOM"),minWidth:180,listWidth:266})})]})]}),I(gt,{sx:{display:"flex",justifyContent:"end"},children:[e(se,{sx:{width:"max-content",textTransform:"capitalize"},onClick:a,variant:"outlined",children:p("Cancel")}),e(se,{className:"button_primary--normal",type:"save",disabled:n==="yes",onClick:f,variant:"contained",children:p("Proceed")})]})]})},Nn={SAVE_AS_DRAFT:{endpoint:`/${te}${me.MASS_ACTION.CREATE_BOM_SAVE_AS_DRAFT}`},SUBMIT_FOR_REVIEW:{endpoint:`/${te}${me.MASS_ACTION.CREATE_BOM_SUBMIT_FOR_REVIEW}`},APPROVE:{endpoint:`/${te}${me.MASS_ACTION.CREATE_BOM_SUBMIT_FOR_APPROVE}`},VALIDATE:{endpoint:`/${te}${me.MASS_ACTION.VALIDATE_BOM}`},SYNDICATE:{endpoint:`/${te}${me.MASS_ACTION.BOM_SYNDICATE}`}};function Rn(){const c=v(b=>b.bom.bomRows),a=v(b=>b.bom.tabFieldValues),n=v(b=>b.bom.BOMpayloadData),x=v(b=>b.bom.requestHeaderID),f=v(b=>b.userManagement.taskData),{showSnackbar:p}=Fe(),G=tt();return{handleBottomButton:i.useCallback(b=>{const o=Bo(c,a,n,x,f),W=Nn[b];if(!W){p("Invalid action type","error");return}G(-1),he(W.endpoint,"post",T=>{T.statusCode!==Mt.STATUS_200?p(T.message,"error"):p(T.message,"success")},T=>{p(W.errorMessage,"error")},o)},[c,a,n,x,f,p])}}const Sn=()=>{const[c,a]=i.useState(null),[n,x]=i.useState(null),[f,p]=i.useState(!1),G=v(o=>o.bom.requestHeaderID),h=v(o=>o.bom.BOMpayloadData);return{checkBomDuplicates:o=>{p(!0),x(null),a(null);const W=[{material:typeof o.material=="object"&&o.material.code||o.material,plant:typeof o.plant=="object"&&o.plant.code||o.plant,alternativeBom:typeof o.altBom=="object"&&o.altBom.code||o.altBom,bomUsage:typeof o.bomUsage=="object"&&o.bomUsage.code||o.bomUsage,requestNo:G||(h==null?void 0:h.RequestId)}];return new Promise((T,O)=>{he(`/${te}${me.MASS_ACTION.FETCH_BOM_NO_DUPLICATE_CHECK}`,"post",r=>{a(r),p(!1),T(r)},r=>{x(r),p(!1),O(r)},W)})},result:c,error:n,loading:f}},xn=c=>{var Nt,Rt;const{t:a}=ht(),n=Le(),{getDtCall:x,dtData:f}=ve(),{getDtCall:p,dtData:G}=ve(),{showSnackbar:h}=Fe(),{handleBottomButton:b}=Rn(),{checkBomDuplicates:o,result:W,error:T,loading:O}=Sn(),r=v(t=>t.bom.BOMpayloadData),R=v(t=>t.bom.bomRows),A=v(t=>t.bom.selectedRowID),_=v(t=>t.paginationData),w=v(t=>t.bom.dropDownData||{}),D=v(t=>t.userManagement.taskData),l=v(t=>t.bom.buttonDTData),y=v(t=>t.bom.validatedRows||{}),N=v(t=>t.bom.tabFieldValues),[M,X]=i.useState(R||[]),[Z,P]=i.useState(!1),[Q,ae]=i.useState([]),[ce,d]=i.useState(null),[C,L]=i.useState(!1),[S,j]=i.useState(!1),[u,q]=i.useState([]),[B,H]=i.useState(!1),[z,de]=i.useState("yes"),[J,be]=i.useState(0),[Oe,fe]=i.useState(!1),[_e,Ne]=i.useState(""),[Re,Ue]=i.useState({}),[We,ot]=i.useState(!0),[nt,$e]=i.useState([]),[Ye,Ve]=i.useState(!1),[je,ze]=i.useState(""),{getDynamicWorkflowDT:Qe}=pn();i.useEffect(()=>{const t=async()=>{var s,m;try{const E=await Qe(r==null?void 0:r.RequestType,r==null?void 0:r.Region,"",(s=r==null?void 0:r.Tochildrequestheaderdata)==null?void 0:s.BOMGroupType,D==null?void 0:D.ATTRIBUTE_3,"v1","MDG_BOM_DYNAMIC_WORKFLOW_DT",(m=xe)==null?void 0:m.BOM);$e(E)}catch(E){h(E==null?void 0:E.message,"error")}};r!=null&&r.RequestType&&(r!=null&&r.Region)&&(D!=null&&D.ATTRIBUTE_3)&&t()},[r==null?void 0:r.RequestType,r==null?void 0:r.Region,D==null?void 0:D.ATTRIBUTE_3]),i.useEffect(()=>{X(R||[])},[R]),i.useEffect(()=>{M.length>0&&!A&&n(ut(M[0].id))},[M,A,n]);const U=Ge(),$=new URLSearchParams(U.search),ie=$.get("reqBench"),Me=$.get("RequestId"),F=!Yt.includes(c==null?void 0:c.requestStatus)||Me&&!ie;i.useEffect(()=>{(R==null?void 0:R.length)===0&&((r==null?void 0:r.RequestType)===k.CREATE||(r==null?void 0:r.RequestType)===k.CREATE_WITH_UPLOAD)&&P(!0);const t={decisionTableId:null,decisionTableName:Ze.MDG_MAT_BOM_CONFIG,version:"v3",conditions:[{"MDG_CONDITIONS.MDG_REGION":vo.US,"MDG_CONDITIONS.MDG_GROUP_ROLE":(D==null?void 0:D.ATTRIBUTE_5)||"Z_MAT_REQ_INITIATE","MDG_CONDITIONS.MDG_SCENARIO":k.CREATE}]};x(t),at()},[]),i.useEffect(()=>{var t;if((t=f==null?void 0:f.result)!=null&&t.length){const s=new Map;f.result.forEach(E=>{var oe;(oe=E.MDG_BOM_MATERIAL_FIELD_CONFIG_ACTION_TYPE)==null||oe.forEach(le=>{const K=le.MDG_VIEW_NAME,Y=le.MDG_VIEW_SEQUENCE;K&&K!=="Header"&&Y!==void 0&&(!s.has(K)||s.get(K)>Y)&&s.set(K,Y)})});const m=Array.from(s.entries()).sort(([,E],[,oe])=>E-oe).map(([E])=>E);q(m)}},[f]),i.useEffect(()=>{G&&n(Qt(G))},[G,n]);const at=()=>{const t={decisionTableId:null,decisionTableName:Ze.MDG_MAT_BOM_BUTTONS,version:"v3",conditions:[{"MDG_CONDITIONS.MDG_MAT_DYN_BTN_MODULE_NAME":Go.BOM,"MDG_CONDITIONS.MDG_MAT_DYN_BTN_REQUEST_TYPE":r==null?void 0:r.RequestType}]};p(t)};i.useEffect(()=>{if(M.length>0){const t={};M.forEach(s=>{s.id in y||(t[s.id]=void 0)}),n(ee(t))}},[M,y,n]);const Ee=t=>{const{id:s,field:m,value:E}=t,oe=["material","plant","bomUsage"];let le=E;oe.includes(m)&&typeof E=="object"&&E!==null&&(le=E.code||"");let K=M.map(Ae=>Ae.id===s?{...Ae,[m]:(m==="validFrom"||m==="validTo")&&le?Xe(le):le,...m==="material"?{plant:""}:{}}:Ae);X(K),n(He(K)),m==="material"&&to(E,s),["material","plant","bomUsage","altBom","bomDescription"].includes(m)&&n(ee({rowId:s,status:void 0}))},Zt=t=>{const s=t.target.value;ce&&clearTimeout(ce);const m=setTimeout(()=>{eo(s)},500);d(m)},eo=(t="")=>{L(!0);let s={materialNo:t,top:200,skip:0};const m=oe=>{L(!1);const le=M.map(Y=>Y.material).filter(Y=>Y&&typeof Y=="object"&&Y.code),K=oe.body||[];ae(Y=>{const Ae=[...Y,...le.filter(ue=>!Y.some(V=>V.code===ue.code)),...K.filter(ue=>!Y.some(V=>V.code===ue.code))],qe=[],Se=new Set;for(const ue of Ae)Se.has(ue.code)||(qe.push(ue),Se.add(ue.code));return qe})},E=oe=>{console.log(oe)};he(`/${te}/data/getMaterialBasedOnMaterialNo`,"post",m,E,s)},to=(t,s)=>{if(!t){Ue(m=>({...m,[s]:[]}));return}he(`/${te}/data/getPlantBasedOnMaterial?material=${encodeURIComponent(typeof t=="object"?t.code:t)}`,"get",m=>{Ue(E=>({...E,[s]:m.body||[]}))},m=>{Ue(E=>({...E,[s]:[]})),console.error(m)})},oo=t=>{n(wo(t)),be(t)},no=t=>{n(ut(t.row.id))},ao=()=>{P(!1),z!=="yes"&&so()},so=()=>{const t=ko(),s={id:t,included:!0,material:"",plant:"",bomUsage:"",altBom:"",bomDescription:"",validFrom:Xe(),validTo:Xe()};n(He([...M,s])),X([...M,s]),n(ee({rowId:t,status:void 0}))},ro=t=>{const{material:s,plant:m,bomUsage:E,altBom:oe,bomDescription:le}=t,K=V=>V?typeof V=="object"?V.code||V.desc||"":V:"",Y=[];if(s||Y.push(a("Material")),m||Y.push(a("Plant")),E||Y.push(a("BOM Usage")),oe||Y.push(a("Alternative BOM")),le||Y.push(a("BOM Description")),Y.length>0)return{isValid:!1,message:a("Please fill the following mandatory field(s): ")+Y.join(", ")};const Ae=N[A]||{},qe=f==null?void 0:f.result,Se=[];if(qe&&qe.forEach(V=>{const Te=V.MDG_VIEW_NAME;if(Te==="Material"||Te==="Document"){const st=V.MDG_BOM_MATERIAL_FIELD_CONFIG_ACTION_TYPE||[],rt=[];st.forEach(Ke=>{var St;if(Ke.MDG_VISIBILITY==="Mandatory"){const ho=Ke.MDG_JSON_FIELD_NAME||Ke.MDG_UI_FIELD_NAME,Je=(St=Ae[Te])==null?void 0:St[ho];(Je==null||typeof Je=="string"&&Je.trim()==="")&&rt.push(a(Ke.MDG_UI_FIELD_NAME))}}),rt.length>0&&Se.push(`${a(Te)}: ${rt.join(", ")}`)}}),Se.length>0)return{isValid:!1,message:a("Please fill the following mandatory field(s) in tab(s): ")+Se.join("; ")};const ue=M.map((V,Te)=>({row:V,idx:Te})).filter(({row:V})=>V.id!==t.id&&K(V.material)===K(s)&&K(V.plant)===K(m)&&K(V.bomUsage)===K(E)&&K(V.altBom)===K(oe));if(ue.length>0){const V=M.findIndex(st=>st.id===t.id),Te=ue[0].idx;return{isValid:!1,message:`${a(pe.DUPLICATE_COMBINATION)}: Row ${V+1} and Row ${Te+1} have the same combination (Material, Plant, BOM Usage, Alternative BOM)`}}return{isValid:!0,message:a(Wt.DUPLICATE_COMBINATION)}},io=async t=>{const s=ro(t);if(!s.isValid){h(s.message,"error"),n(ee({rowId:t.id,status:!1}));return}try{const m=await o(t);m&&m.statusCode===200?(h(m.message,"success"),n(ee({rowId:t.id,status:!0}))):m&&m.statusCode===500?(Ne(m.message),fe(!0),n(ee({rowId:t.id,status:!1}))):(h("Unexpected response from duplicate check.","error"),n(ee({rowId:t.id,status:!1})))}catch(m){h("Error checking duplicates: "+(m.message||m),"error"),n(ee({rowId:t.id,status:!1}))}};i.useEffect(()=>{const t=M.length===0||M.length>0&&M.every(s=>y[s.id]===!0);ot(t)},[M,y]);const lo=[{field:"included",headerName:a("Included"),flex:.4,align:"center",headerAlign:"center",renderCell:t=>e(Vt,{checked:t.row.included,disabled:F,onChange:s=>Ee({id:t.row.id,field:"included",value:s.target.checked})})},{field:"sno",headerName:"S.No",flex:.2,align:"center",headerAlign:"center",sortable:!1,filterable:!1,renderCell:t=>{const s=M.findIndex(m=>m.id===t.row.id);return s!==-1?s+1:""}},{field:"material",headerName:I("span",{children:[a("Material"),e("span",{style:{color:"red"},children:"*"})]}),flex:.8,align:"center",headerAlign:"center",renderCell:t=>{const s=Q.find(E=>E.code===t.row.material)||(t.row.material?{code:t.row.material,desc:""}:""),m=s&&s.code&&!Q.some(E=>E.code===s.code)?[...Q,s]:Q;return e("div",{style:{width:"100%"},children:e(ge,{options:m||[],value:s,onChange:E=>Ee({id:t.row.id,field:"material",value:E||""}),handleInputChange:Zt,isLoading:C,placeholder:a("Select Material"),disabled:F,minWidth:"90%",listWidth:235})})}},{field:"plant",headerName:I("span",{children:[a("Plant"),e("span",{style:{color:"red"},children:"*"})]}),flex:.8,align:"center",headerAlign:"center",renderCell:t=>{const s=(Re[t.row.id]||[]).find(E=>E.code===t.row.plant)||(t.row.plant?{code:t.row.plant,desc:""}:""),m=s&&s.code&&!(Re[t.row.id]||[]).some(E=>E.code===s.code)?[...Re[t.row.id]||[],s]:Re[t.row.id]||[];return e(ge,{options:m,value:s,onChange:E=>Ee({id:t.row.id,field:"plant",value:E||""}),placeholder:a("Select Plant"),disabled:F,minWidth:"90%",listWidth:235})}},{field:"bomUsage",headerName:I("span",{children:[a("BOM Usage"),e("span",{style:{color:"red"},children:"*"})]}),flex:.8,align:"center",headerAlign:"center",renderCell:t=>{var s;return e(ge,{options:(w==null?void 0:w.BOMUsage)||[],value:((s=w==null?void 0:w.BOMUsage)==null?void 0:s.find(m=>m.code===t.row.bomUsage))||"",onChange:m=>Ee({id:t.row.id,field:"bomUsage",value:m||""}),placeholder:a("Select BOM Usage"),disabled:F,minWidth:"90%",listWidth:235})}},{field:"altBom",headerName:I("span",{children:[a("Alternative BOM"),e("span",{style:{color:"red"},children:"*"})]}),flex:.8,align:"center",headerAlign:"center",renderCell:t=>e(dt,{value:t.row.altBom||"",onChange:s=>{const E=s.target.value.toUpperCase().replace(/[^A-Z0-9-]/g,"").slice(0,40);Ee({id:t.row.id,field:"altBom",value:E})},placeholder:a("ENTER ALTERNATIVE BOM"),disabled:F,inputProps:{maxLength:40},size:"small",variant:"outlined",sx:{width:"90%"}})},{field:"bomDescription",headerName:I("span",{children:[a("BOM Description"),e("span",{style:{color:"red"},children:"*"})]}),flex:1,align:"center",headerAlign:"center",renderCell:t=>e(dt,{value:t.row.bomDescription||"",onChange:s=>{const E=s.target.value.toUpperCase().replace(/[^A-Z0-9-]/g,"").slice(0,40);Ee({id:t.row.id,field:"bomDescription",value:E})},placeholder:a("ENTER BOM DESCRIPTION"),disabled:F,inputProps:{maxLength:40},size:"small",variant:"outlined",sx:{width:"90%"}})},{field:"validFrom",headerName:a("Valid From"),flex:1,align:"center",headerAlign:"center",renderCell:t=>e(ct,{dateAdapter:mt,children:e(pt,{value:t.row.validFrom,onChange:s=>Ee({id:t.row.id,field:"validFrom",value:s}),disabled:F,format:"DD/MM/YYYY",slotProps:{textField:{sx:{width:"90%","& .MuiInputBase-root":{height:36},"& .MuiInputBase-input":{padding:"8.5px 14px"}},size:"small"}}})})},{field:"validTo",headerName:a("Valid To"),flex:1,align:"center",headerAlign:"center",renderCell:t=>e(ct,{dateAdapter:mt,children:e(pt,{value:t.row.validTo,onChange:s=>Ee({id:t.row.id,field:"validTo",value:s}),disabled:F,format:"DD/MM/YYYY",slotProps:{textField:{sx:{width:"90%","& .MuiInputBase-root":{height:36},"& .MuiInputBase-input":{padding:"8.5px 14px"}},size:"small"}}})})},{field:"Actions",headerName:a("Actions"),flex:.6,align:"center",headerAlign:"center",renderCell:t=>{const s=y[t.row.id]||void 0;return I(_t,{direction:"row",alignItems:"center",sx:{marginLeft:"0.5rem",magrinRight:"0.5rem"},spacing:.5,children:[e(Be,{title:s===!0?"Validated Successfully":a(s===!1?"Validation Failed":"Click to Validate"),children:e(ye,{onClick:()=>io(t.row),disabled:F,color:s===!0?"success":s===!1?"error":"default",children:s===!1?e(Lo,{}):e(Uo,{})})}),!F&&e(Be,{title:"Delete Row",children:e(ye,{onClick:()=>{const m=M.filter(E=>E.id!==t.row.id);X(m),n(He(m)),n(Et(t.row.id)),n(ee({rowId:t.row.id,status:void 0}))},color:"error",children:e(Po,{})})})]})}}],co=()=>{j(!S),B&&H(!1)},uo=()=>{H(!B),S&&j(!1)},It=At(et.CURRENT_TASK,!0,{}),mo=It&&It.taskDesc||D&&D.taskDesc,bt=(((Rt=(Nt=l==null?void 0:l.result)==null?void 0:Nt[0])==null?void 0:Rt.MDG_MAT_DYN_BUTTON_CONFIG)||[]).filter(t=>t.MDG_MAT_DYN_BTN_TASK_NAME===mo);qo(bt,[it.HANDLE_SUBMIT_FOR_APPROVAL,it.HANDLE_SAP_SYNDICATION,it.HANDLE_SUBMIT_FOR_REVIEW]);const po=[...bt].sort((t,s)=>{const m=Pt[t.MDG_MAT_DYN_BTN_ACTION_TYPE]??999,E=Pt[s.MDG_MAT_DYN_BTN_ACTION_TYPE]??999;return m-E}),Pe=[],Ot=new Set;for(const t of po)Ot.has(t.MDG_MAT_DYN_BTN_ACTION_TYPE)||(Pe.push(t),Ot.add(t.MDG_MAT_DYN_BTN_ACTION_TYPE));return i.useEffect(()=>{(Pe.find(t=>t.MDG_MAT_DYN_BTN_BUTTON_NAME===Lt.SEND_BACK)||Pe.find(t=>t.MDG_MAT_DYN_BTN_BUTTON_NAME===Lt.CORRECTION))&&Ve(!0)},[Pe]),I("div",{children:[e("div",{style:{padding:"0",width:"100%",margin:"0",marginTop:"20px"},children:I(ne,{sx:{position:S?"fixed":"relative",top:S?0:"auto",left:S?0:"auto",right:S?0:"auto",bottom:S?0:"auto",width:S?"100vw":"100%",height:S?"100vh":"auto",zIndex:S?1004:void 0,backgroundColor:S?"white":"transparent",padding:S?"20px":"0",display:"flex",flexDirection:"column",boxShadow:S?"0px 0px 15px rgba(0, 0, 0, 0.2)":"none",transition:"all 0.3s ease",borderRadius:"8px",border:"1px solid #e0e0e0"},children:[I(ne,{sx:{display:"flex",justifyContent:"space-between",alignItems:"center",padding:"8px 16px",backgroundColor:"#f5f5f5",borderRadius:"8px 8px 0 0"},children:[e(re,{variant:"h6",children:a("List of BOM")}),I(ne,{sx:{display:"flex",alignItems:"center",gap:1},children:[I(se,{variant:"contained",color:"primary",size:"small",disabled:F||!We,onClick:()=>{(r==null?void 0:r.RequestType)===k.CREATE&&P(!0)},children:["+ ",a("Add")]}),e(Be,{title:a(S?"Exit Zoom":"Zoom In"),sx:{zIndex:"1009"},children:e(ye,{onClick:co,color:"primary",sx:{backgroundColor:"rgba(0, 0, 0, 0.05)","&:hover":{backgroundColor:"rgba(0, 0, 0, 0.1)"}},children:S?e(Kt,{}):e(Jt,{})})})]})]}),e("div",{style:{width:"100%",height:"100%",overflowX:"auto"},children:e("div",{style:{height:"100%"},children:e($t,{rows:M,columns:lo,pageSize:50,autoHeight:!1,page:J,rowCount:(_==null?void 0:_.totalElements)||0,rowsPerPageOptions:[50],onRowClick:no,onCellEditCommit:Ee,onPageChange:t=>oo(t),pagination:!0,disableSelectionOnClick:!0,getRowClassName:t=>t.id===A?"selected-row":"",style:{border:"1px solid #ccc",borderRadius:"8px",width:"100%",height:S?"calc(100vh - 150px)":`${Math.min(M.length*50+130,300)}px`,overflow:"auto"},sx:{"& .selected-row":{backgroundColor:"rgb(234 233 255)"}}})})})]})}),A&&M.find(t=>t.id===A)&&e(bn,{isTabsZoomed:B,toggleTabsZoom:uo,viewNames:u,selectedRowID:A,viewsDt:f==null?void 0:f.result,t:a}),Z&&e(On,{open:Z,onClose:()=>P(!1),withReference:z,setWithReference:de,onProceed:ao,t:a}),e(dn,{filteredButtons:Pe,moduleName:xe.BOM,handleSaveAsDraft:b,handleSubmitForReview:b,handleSubmitForApprove:b,handleSendBack:()=>{},handleCorrection:()=>{},handleRejectAndCancel:()=>{},handleValidateAndSyndicate:b,validateAllRows:()=>{},showWfLevels:Ye,selectedLevel:je,workFlowLevels:nt,setSelectedLevel:ze}),I(zt,{open:Oe,onClose:()=>fe(!1),children:[e(jt,{children:"Duplicate BOM Found"}),e(Tt,{children:e("div",{style:{whiteSpace:"pre-line"},children:_e})}),e(gt,{children:e(se,{onClick:()=>fe(!1),color:"primary",autoFocus:!0,children:"Close"})})]})]})},yn=()=>{const c=Le(),[a,n]=i.useState(!1),[x,f]=i.useState(null),{customError:p}=Ho();return{getDisplayBomData:i.useCallback(async(h,b,o,W,T)=>new Promise((O,r)=>{n(!0),f(null);const R=h,A=At(et.CURRENT_TASK,!0,{}),_=b||(W==null?void 0:W.ATTRIBUTE_2)||(A==null?void 0:A.ATTRIBUTE_2);let w=o?{massCreationId:T!=null&&T.isBifurcated?"":_===k.CREATE||_===k.CREATE_WITH_UPLOAD?R:"",massChildCreationId:T!=null&&T.isBifurcated&&(_===k.CREATE||_===k.CREATE_WITH_UPLOAD)?R:"",massChangeId:T!=null&&T.isBifurcated?"":_===k.CHANGE||_===k.CHANGE_WITH_UPLOAD?R:"",massChildChangeId:T!=null&&T.isBifurcated&&(_===k.CHANGE||_===k.CHANGE_WITH_UPLOAD)?R:"",page:0,size:10,sort:""}:{massCreationId:"",massChangeId:"",massChildCreationId:_===k.CREATE||_===k.CREATE_WITH_UPLOAD?R:"",massChildChangeId:_===k.CHANGE||_===k.CHANGE_WITH_UPLOAD?R:"",page:0,size:10,sort:""};const D=y=>{if(n(!1),(y==null?void 0:y.statusCode)===Mt.STATUS_200){const{bomPayload:N,bomRows:M,tabFieldValues:X}=Fo(y);Object.entries(N).forEach(([Z,P])=>{c(lt({keyName:Z,data:P}))}),c(He(M)),Object.entries(X).forEach(([Z,P])=>{Object.entries(P).forEach(([Q,ae])=>{c({type:"bom/setTabRows",payload:{rowId:Z,viewName:Q,rows:ae}})})}),O(y)}else f((y==null?void 0:y.message)||pe.ERROR_GET_DISPLAY_DATA),r((y==null?void 0:y.message)||pe.ERROR_GET_DISPLAY_DATA)},l=y=>{p(pe.ERROR_FETCHING_DATA),f(y),n(!1),r(y)};he(`/${te}/${me.CHG_DISPLAY_REQUESTOR.DISPLAY_DTO}`,"post",D,l,w)}),[c]),loading:a,error:x,clearError:()=>f(null)}},Bn=({setBlurLoading:c,setLoaderMessage:a,setEnableDocumentUpload:n})=>{const x=tt(),f=Ge(),{showSnackbar:p}=Fe(),G=new URLSearchParams(f.search),h=G.get("RequestId"),b=G.get("RequestType"),o=v(T=>T.bom.BOMpayloadData);return{handleUploadBOM:i.useCallback(T=>{var A;a("Initiating Excel Upload"),c(!0);const O=new FormData;[...T].forEach(_=>O.append("files",_)),O.append("dtName",b===k.CREATE_WITH_UPLOAD?"MDG_BOM_MATERIAL_FIELD_CONFIG":""),O.append("version",b===k.CREATE_WITH_UPLOAD?"v3":""),O.append("requestId",h||""),O.append("region",o!=null&&o.Region?o==null?void 0:o.Region:"US"),O.append("role",(A=kt)==null?void 0:A.REQ_INITIATE_DOWNLOAD_MAT);const r=_=>{var w;(_==null?void 0:_.statusCode)===Mt.STATUS_200?(n(!1),c(!1),a(""),x((w=Ce)==null?void 0:w.REQUEST_BENCH)):(n(!1),c(!1),a(""),p(_==null?void 0:_.message,"error"))},R=_=>{c(!1),a(""),p(_==null?void 0:_.message,"error")};he(`/${te}${me.MASS_ACTION.UPLOAD_BOM_FILE}`,"postformdata",r,R,O)},[c,a,n,x,o])}},_a=()=>{var $e,Ye,Ve,je,ze,Qe;const c=tt(),a=Ge(),n=Le(),{getDtCall:x,dtData:f}=ve(),{getDtCall:p,dtData:G}=ve(),{t:h}=ht(),o=new URLSearchParams(a.search.split("?")[1]).get("RequestId"),W=[h("Request Header"),h("BOM List"),h("Attachments & Remarks"),h("Preview")],T=v(U=>U.bom.requestHeaderID),O=v(U=>U.bom.BOMpayloadData),r=v(U=>U.bom.tabValue),R=new URLSearchParams(a.search),A=R.get("reqBench"),[_,w]=i.useState(!1),D=R.get("RequestType"),l=(($e=a.state)==null?void 0:$e.isChildRequest)??(o&&!A)??!1,y=v(U=>{var $;return($=U.userManagement)==null?void 0:$.taskData}),N=a.state,[M,X]=i.useState(!1),[Z,P]=i.useState([!1]),[Q,ae]=i.useState(!1),[ce,d]=i.useState(!1),[C,L]=i.useState(""),[S,j]=i.useState(!1),[u,q]=i.useState([]),[B,H]=i.useState(!1),[z,de]=i.useState(""),{handleUploadBOM:J}=Bn({setBlurLoading:H,setLoaderMessage:de,setEnableDocumentUpload:w}),{getDisplayBomData:be}=yn(),Oe={},fe=v(U=>U.bom);i.useEffect(()=>{Q&&P([!0])},[Q]),i.useEffect(()=>{var $;const U=(($=G==null?void 0:G.result[0])==null?void 0:$.MDG_ATTACHMENTS_ACTION_TYPE)||[];q(U)},[G]),i.useEffect(()=>((async()=>{var $;if(o){const ie=At(et.CURRENT_TASK,!0,{}),Me=D||(y==null?void 0:y.ATTRIBUTE_2)||(ie==null?void 0:ie.ATTRIBUTE_2);await be(o,Me,A,y,N),(D===k.CHANGE_WITH_UPLOAD&&!(($=N==null?void 0:N.material)!=null&&$.length)||D===k.CREATE_WITH_UPLOAD)&&((N==null?void 0:N.reqStatus)===De.DRAFT||(N==null?void 0:N.reqStatus)===De.UPLOAD_FAILED)?(n(we(0)),ae(!1),d(!1)):(n(we(1)),ae(!0),d(!0))}else n(we(0))})(),ot(),nt(),_e("getBomUsage","BOMUsage"),_e("getBomItemCategory","Category"),_e("getBOMDocument","Document"),_e("getBOMDocumentType","DocType"),_e("getBOMComponentUOM","CompUom"),L(Wo("BOM")),n(ke({keyName:"Region",data:$o})),Yo(et.MODULE,xe.BOM),()=>{n(Vo()),n(Et()),n(Qt(null)),n(ut(null)),n(He([]))}),[n]),i.useEffect(()=>{var U;if(f){const Me={"Header Data":((U=f==null?void 0:f.result[0])==null?void 0:U.MDG_MAT_REQUEST_HEADER_CONFIG).sort((F,at)=>F.MDG_MAT_SEQUENCE_NO-at.MDG_MAT_SEQUENCE_NO).map(F=>({fieldName:F.MDG_MAT_UI_FIELD_NAME,sequenceNo:F.MDG_MAT_SEQUENCE_NO,fieldType:F.MDG_MAT_FIELD_TYPE,maxLength:F.MDG_MAT_MAX_LENGTH,value:F.MDG_MAT_DEFAULT_VALUE,visibility:F.MDG_MAT_VISIBILITY,jsonName:F.MDG_MAT_JSON_FIELD_NAME}))};n(Ht(Me))}},[f]);const _e=(U,$)=>{const ie=F=>{n(ke({keyName:$,data:F.body}))},Me=F=>{console.log(F)};he(`/${te}/data/${U}`,"get",ie,Me)},Ne=U=>{n(we(U))},Re=()=>{j(!0)},Ue=()=>{var U,$,ie;o&&!A?c((U=Ce)==null?void 0:U.MY_TASK):A?c(($=Ce)==null?void 0:$.REQUEST_BENCH):!o&&!A&&c((ie=Ce)==null?void 0:ie.BOM)},We=()=>{X(!1)},ot=()=>{let U={decisionTableId:null,decisionTableName:Ze.MDG_MAT_REQUEST_HEADER_CONFIG,version:"v2",conditions:[{"MDG_CONDITIONS.MDG_MAT_SCENARIO":"Create","MDG_CONDITIONS.MDG_MODULE":"BOM"}]};x(U)},nt=()=>{p({decisionTableId:null,decisionTableName:"MDG_ATTACHMENTS_LIST_DT",version:"v1",rulePolicy:null,validityDate:null,conditions:[{"MDG_CONDITIONS.MDG_ATTACHMENTS_OBJECT_TYPE":"Material","MDG_CONDITIONS.MDG_ATTACHMENTS_SCENARIO":"Create","MDG_CONDITIONS.MDG_ATTACHMENTS_REG_COMP_CODE":1}],systemFilters:null,systemOrders:null,filterString:null})};return I(Ut,{children:[I(ne,{sx:{padding:2},children:[I(Ie,{sx:{display:"flex",justifyContent:"space-between",alignItems:"center"},children:[T||o?I(re,{variant:"h6",sx:{mb:1,textAlign:"left",display:"flex",alignItems:"center",gap:1},children:[e(an,{sx:{fontSize:"1.5rem"}}),h("Request Header ID"),":"," ",e("span",{children:T||o})]}):e("div",{style:{flex:1}}),r===1&&I(ne,{sx:{display:"flex",justifyContent:"flex-end",gap:"1rem"},children:[e(se,{variant:"outlined",size:"small",title:h("Download Error Report"),disabled:!o,onClick:()=>{c(`/requestBench/errorHistory?RequestId=${o||""}`,{state:{display:!0,childRequest:l,module:xe.BOM}})},color:"primary",children:e(jo,{sx:{padding:"2px"}})}),(O==null?void 0:O.RequestType)===k.CREATE||(O==null?void 0:O.RequestType)===k.CREATE_WITH_UPLOAD?e(se,{variant:"outlined",disabled:!o,size:"small",onClick:()=>{},title:h("Create Change Log"),children:e(qt,{sx:{padding:"2px"}})}):e(se,{variant:"outlined",disabled:!o,size:"small",onClick:()=>{},title:h("Change Log"),children:e(qt,{sx:{padding:"2px"}})}),e(se,{variant:"outlined",disabled:!o,size:"small",onClick:()=>{},title:h("Export Excel"),children:e(_n,{sx:{padding:"2px"}})})]})]}),(O==null?void 0:O.TemplateName)&&I(re,{variant:"h6",sx:{mb:1,textAlign:"left",display:"flex",alignItems:"center",gap:1},children:[e(sn,{sx:{fontSize:"1.5rem"}}),h("Template Name"),": ",e("span",{children:O==null?void 0:O.TemplateName})]}),e(ye,{onClick:()=>{var U;if(A){c((U=Ce)==null?void 0:U.REQUEST_BENCH);return}X(!0)},color:"primary","aria-label":"upload picture",component:"label",sx:{left:"-10px"},title:h("Back"),children:e(zo,{sx:{fontSize:"25px",color:"#000000"}})}),e(Jo,{nonLinear:!0,activeStep:r,sx:{display:"flex",alignItems:"center",justifyContent:"center",margin:"25px 14%",marginTop:"-35px"},children:W.map((U,$)=>e(Qo,{completed:Z[$],children:e(Ko,{color:"error",disabled:$===1&&!Q||$===2&&!ce||$===3&&!ce,onClick:()=>Ne($),sx:{fontSize:"50px",fontWeight:"bold"},children:e("span",{style:{fontSize:"15px",fontWeight:"bold"},children:U})})},U))}),r===0&&I(Ut,{children:[e(Tn,{setIsSecondTabEnabled:ae,setIsAttachmentTabEnabled:d,downloadClicked:S,setDownloadClicked:j}),(D===k.CHANGE_WITH_UPLOAD||D===k.CREATE_WITH_UPLOAD)&&((N==null?void 0:N.reqStatus)==De.DRAFT&&!((Ye=N==null?void 0:N.material)!=null&&Ye.length)||(N==null?void 0:N.reqStatus)==De.UPLOAD_FAILED)&&e(rn,{handleDownload:Re,setEnableDocumentUpload:w,enableDocumentUpload:_,handleUploadMaterial:J}),e(Ft,{blurLoading:B,loaderMessage:z})]}),r===1&&e(xn,{requestStatus:N!=null&&N.reqStatus?N==null?void 0:N.reqStatus:De.ENABLE_FOR_FIRST_TIME,setIsAttachmentTabEnabled:d,setCompleted:P}),r===2&&e(un,{requestStatus:De.ENABLE_FOR_FIRST_TIME,attachmentsData:u,requestIdHeader:T||o,pcNumber:C,module:(Ve=xe)==null?void 0:Ve.BOM,artifactName:on.BOM}),r===3&&e(ne,{sx:{width:"100%",overflow:"auto"},children:e(mn,{requestStatus:De.ENABLE_FOR_FIRST_TIME,module:(je=xe)==null?void 0:je.BOM,payloadData:fe,payloadForDownloadExcel:Oe})})]}),M&&I(Xo,{isOpen:M,titleIcon:e(nn,{size:"small",sx:{color:(Qe=(ze=g)==null?void 0:ze.secondary)==null?void 0:Qe.amber,fontSize:"20px"}}),Title:h("Warning"),handleClose:We,children:[e(Tt,{sx:{mt:2},children:h(Zo.LEAVE_PAGE_MESSAGE)}),I(gt,{children:[e(se,{variant:"outlined",size:"small",sx:{...en},onClick:We,children:h("No")}),e(se,{variant:"contained",size:"small",sx:{...tn},onClick:Ue,children:h("Yes")})]})]})]})};export{_a as default};
