import{s as U,aP as b,n as I,r as O,da as R,bI as y,aT as p,C as j,aJ as V,gI as w,gJ as Q,dI as q,gK as P,dc as h,dd as Y,aD as W}from"./index-f7d9b065.js";const x=n=>{let E={},N=n==null?void 0:n.sort((_,o)=>_.MDG_GL_VIEW_SEQUENCE-o.MDG_GL_VIEW_SEQUENCE);const M=h(N,"MDG_GL_VIEW_NAME");let G=[];Object.entries(M).forEach(([_,o])=>{let D=h(o,"MDG_GL_CARD_NAME"),s=[];Object.entries(D).forEach(([a,r])=>{r.sort((e,L)=>e.MDG_GL_SEQUENCE_NO-L.MDG_GL_SEQUENCE_NO);let c=new Set,i=[];for(let e of r)c.has(e.MDG_GL_JSON_FIELD_NAME)||(c.add(e.MDG_GL_JSON_FIELD_NAME),i.push({fieldName:e.MDG_GL_UI_FIELD_NAME,sequenceNo:e.MDG_GL_SEQUENCE_NO,fieldType:e.MDG_GL_FIELD_TYPE,maxLength:e.MDG_GL_MAX_LENGTH,dataType:e.MDG_GL_DATA_TYPE,viewName:e.MDG_GL_VIEW_NAME,cardName:e.MDG_GL_CARD_NAME,cardSeq:e.MDG_GL_CARD_SEQUENCE,viewSeq:e.MDG_GL_VIEW_SEQUENCE,value:e.MDG_GL_DEFAULT_VALUE,visibility:e.MDG_GL_VISIBILITY,jsonName:e.MDG_GL_JSON_FIELD_NAME}));s.push({cardName:a,cardSeq:r[0].MDG_GL_CARD_SEQUENCE,viewSeq:r[0].MDG_GL_VIEW_SEQUENCE,cardDetails:i})}),s.sort((a,r)=>a.cardSeq-r.cardSeq),G.push({viewName:_,cards:s})});let d=Y(G),l={};return d.forEach(_=>{let o={};_.cards.forEach(D=>{o[D.cardName]=D.cardDetails,_.viewName!=="Request Header"&&D.cardDetails.forEach(s=>{s.visibility===W.MANDATORY&&(E[s.viewName]||(E[s.viewName]=[]),E[s.viewName].some(r=>r.jsonName===s.jsonName)||E[s.viewName].push({jsonName:s==null?void 0:s.jsonName,fieldName:s==null?void 0:s.fieldName}))})}),l[_.viewName]=o}),{transformedData:l,mandatoryFields:E}},H=()=>{const n=U(),{customError:E}=b(),N=I(a=>{var r;return(r=a.payload)==null?void 0:r.payloadData}),M=I(a=>a.applicationConfig),G=I(a=>a.generalLedger.payload||{});I(a=>a.userManagement.userData);const[d,l]=O.useState(!1),[_,o]=O.useState(null),D=async()=>{var e,L;l(!0);const a={decisionTableId:null,decisionTableName:"MDG_GL_FIELD_CONFIG",version:"v3",rulePolicy:null,validityDate:null,conditions:[{"MDG_CONDITIONS.MDG_GL_SCENARIO":(N==null?void 0:N.RequestType)||"Create","MDG_CONDITIONS.MDG_MAT_ROLE":(e=R)==null?void 0:e.REQ_INITIATE_FIN,"MDG_CONDITIONS.MDG_GL_ACCOUNT_TYPE":(L=G==null?void 0:G.rowsHeaderData)==null?void 0:L.accountType}],systemFilters:null,systemOrders:null,filterString:null},r=t=>{var f,m,S,T;if(t.statusCode===V.STATUS_200){if(Array.isArray((f=t==null?void 0:t.data)==null?void 0:f.result)&&((m=t==null?void 0:t.data)!=null&&m.result.every(u=>Object.keys(u).length!==0))){let u=(T=(S=t==null?void 0:t.data)==null?void 0:S.result[0])==null?void 0:T.MDG_GL_FIELD_DETAILS_ACTION_TYPE;const{transformedData:A,mandatoryFields:v}=x(u);let g=Object.keys(A);const F=g.map(C=>({tab:C,data:A[C]}));n(w(F)),n(Q({GeneralLedger:{allfields:q(g),mandatoryFields:v}}))}else n(P({GeneralLedger:{}}));l(!1)}},c=t=>{E(t),o(t),l(!1)},i=M.environment==="localhost"?`/${y}${p.INVOKE_RULES.LOCAL}`:`/${y}${p.INVOKE_RULES.PROD}`;j(i,"post",r,c,a)};return{loading:d,error:_,fetchGeneralLedgerFieldConfig:()=>{try{D()}catch(a){o(a),l(!1)}}}};export{H as u};
