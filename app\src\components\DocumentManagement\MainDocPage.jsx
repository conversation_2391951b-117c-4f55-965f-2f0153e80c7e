import * as React from "react";
import Box from "@mui/material/Box";
import moment from "moment/moment";
import useLogger from "@hooks/useLogger";
import {
  outermostContainer,
  outermostContainer_Information,
} from "../common/commonStyles.jsx";
import "./doc.css";
import {
  Tooltip,
  Typography,
} from "@mui/material";
import PictureAsPdfOutlinedIcon from '@mui/icons-material/PictureAsPdfOutlined';
import DescriptionOutlinedIcon from '@mui/icons-material/DescriptionOutlined';
import ImageOutlinedIcon from '@mui/icons-material/ImageOutlined';

import { Grid, Stack, Button } from "@mui/material";
import {
  destination_DocumentManagement,
} from "../../destinationVariables.jsx";
import {
  checkIwaAccess,
} from "../../functions";
import { useState, useEffect } from "react";

import {
  dateFormatter,
  DropdownNames,
  MatDownload,
  MatView,
  MultiDownloadButton,
} from "./UtilDoc.jsx";
import { useDispatch, useSelector } from "react-redux";
import ReusableDialog from "../common/ReusableDialog.jsx";
import DocumentFilter from "./DocumentFilter.jsx";
import styled from "@emotion/styled";
import ReusableSnackBar from "../common/ReusableSnackBar.jsx";
import { doAjax } from "../common/fetchService.jsx";
import { initialDataUpdate } from "../../app/initialDataSlice.jsx";
import { commonFilterClear, commonFilterUpdate } from "../../app/commonFilterSlice.jsx";
import NoDataDialog from "../common/NoDataDialog.jsx";
import ReusableDataTable from "@components/Common/ReusableTable.jsx";
import ReusableBackDrop from "@components/Common/ReusableBackDrop.jsx";
import { colors } from "@constant/colors.js";
import { COLUMN_FIELD_TYPES, DECISION_TABLE_NAME, ROLES, VISIBILITY_TYPE } from "@constant/enum.js";
import { END_POINTS } from "@constant/apiEndPoints.js";
import xlsx from "json-as-xlsx";
import InfoIcon from '@mui/icons-material/Info';
import { commonSearchBarClear } from "@app/commonSearchBarSlice.jsx";
import useLang  from "@hooks/useLang";
import useGenericDtCall from "@hooks/useGenericDtCall";
import { formatDateTime } from "@helper/helper.js";

function MainDocPage() {
  const { customError } = useLogger();
  let iwaAccessData = useSelector(
    (state) =>
      state.userManagement.entitiesAndActivities?.["Document Management"]
  );
  const FilterSearchForm = useSelector(
    (state) => state.commonFilter["DocumentManagement"]
  );
  const [GenerateDynamicColumn,setGenerateDynamicColumn] = useState([])
  const { getDtCall, dtData } = useGenericDtCall();
  const [page, setPage] = useState(0);
  const { t } = useLang();
  const dispatch = useDispatch();

  const fetchColumnsFromDt = () => {
    let payload = {
          decisionTableId: null,
          decisionTableName: DECISION_TABLE_NAME.MDG_MAT_SEARCHSCREEN_COLUMN,
          version: "v2",
          conditions: [
            {
              "MDG_CONDITIONS.MDG_MAT_REGION":"US",
              "MDG_CONDITIONS.MDG_MODULE":"Material",
              "MDG_CONDITIONS.MDG_MAT_SEARCHTYPE":"Document Management",
              "MDG_CONDITIONS.MDG_MAT_VIEW_TYPE": "NA"
            },
          ],
        };
        getDtCall(payload);
  }

  const iconMap = {
    pdf: <PictureAsPdfOutlinedIcon sx={{ fontSize: 20, color: `${colors.icon.red}` }} />,
    image: <ImageOutlinedIcon sx={{ fontSize: 20, color: `${colors.icon.image}` }} />,
    txt: <DescriptionOutlinedIcon sx={{ fontSize: 20, color: `${colors.icon.green}` }} />,
  };

  const userData = useSelector((state) => state.userManagement.userData);
  const userRoles = useSelector((state) => state.userManagement.roles);
  const names = DropdownNames;
  const presentDate = new Date();
  const backDate = new Date();
  backDate.setDate(backDate.getDate() - 3);
  const [date, setDate] = React.useState([backDate, presentDate]);
  const [rmDataRows, setRmDataRows] = useState([]);
  const [roCount, setroCount] = useState(0);
  const [tableData, setTableData] = useState([...rmDataRows]);
  const [documentCount, setDocumentCount] = useState(0);
  const [blurLoading, setBlurLoading] = useState(false);
  const [loaderMessage, setLoaderMessage] = useState("");
  const [snackbar, setSnackbar] = useState(false);
  const [openSnackbar, setopenSnackbar] = useState(false);
  const handleSnackbarClose = () => {
    setopenSnackbar(false);
  };
  const handleSnackbarOpen = () => {
    setopenSnackbar(true);
  };
  const [downloadError, setdownloadError] = useState(false);
  const [openMessageDialog, setOpenMessageDialog] = useState(false);
  const [messageDialogTitle, setMessageDialogTitle] = useState("");
  const [messageDialogMessage, setMessageDialogMessage] = useState("");
  const [messageDialogSeverity, setMessageDialogSeverity] = useState("");
  const handleMessageDialogClickOpen = () => {
    setOpenMessageDialog(true);
  };
  const handleMessageDialogClose = () => {
    setOpenMessageDialog(false);
  };
  const [pageSize, setPageSize] = React.useState(10);
  const [PrimaryData, setPrimaryData] = React.useState([]);

  const DocumentsRow = useSelector((state) => state.initialData?.["Document"]);
  const [showDownloadButton, setshowDownloadButton] = React.useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [DownloadArray, setDownloadArray] = React.useState([]);
  const [DownloadNumber, setDownloadNumber] = React.useState(0);

  const [value, setValue] = React.useState(null);
  const [getDocName, setgetDocName] = React.useState("");
  const appSettings = useSelector((state) => state.appSettings);

  const StyledGridOverlay = styled("div")(({ theme }) => ({
    display: "flex",
    flexDirection: "column",
    alignItems: "center",
    justifyContent: "center",
    height: "100%",
  }));
  function CustomNoRowsOverlay() {
    return (
      <StyledGridOverlay>
        <Box sx={{ mt: 1 }}>No Data Available</Box>
      </StyledGridOverlay>
    );
  }

  const handleSearchBar = (value) => {
    if (!value) {
      setTableData([...rmDataRows]);
      setroCount(documentCount);
      return;
    }
    const selected = rmDataRows.filter((row) => {
      let rowMatched = false;
      let keys = Object.keys(row);

      for (let k = 0; k < keys.length; k++) {
        rowMatched = !row[keys[k]]
          ? false
          : row?.[keys?.[k]] &&
            row?.[keys?.[k]]
              .toString()
              .toLowerCase()
              ?.indexOf(value?.toLowerCase()) != -1;

        if (rowMatched) break;
      }
      return rowMatched;
    });

    setTableData([...selected]);
    setroCount(selected?.length);
  };

  const handlePageSizeChange = (event) => {
    const newPageSize = event.target.value;
    setPageSize(newPageSize);
    setPage(0);
  };
  const handlePageChange = (event, newPage) => {
    setPage(isNaN(newPage) ? 0 : newPage);
  };

  const getFilter = () => {
    setIsLoading(true);
    let data = {
      documentId: "",
      fileName: "",
      fileType: "",
      artifactType: "",
      attachmentType: FilterSearchForm?.attType ?? "",
      documentUrl: "",
      createdBy: userRoles.includes(`${ROLES.SUPER_USER}`) ? FilterSearchForm?.createdBy : userData?.emailId,
      artifactId: "",
      material: FilterSearchForm?.number ?? "",
      docCreationDate: "",
      docCreatedBy: "",
      fromDate: moment(
        FilterSearchForm?.uploadedDate[0]
          ? FilterSearchForm?.uploadedDate[0]
          : ""
      ),
      toDate: moment(
        FilterSearchForm?.uploadedDate[1]
          ? FilterSearchForm?.uploadedDate[1]
          : ""
      ),
      childRequestId: FilterSearchForm?.childRequestId ?? "",
      requestId: "",
      documentType: FilterSearchForm?.docType ?? "",
      loginUser: "",
      role: "",
      requestType: FilterSearchForm?.requestType ?? "",
      top: pageSize,
      skip: 0,
    };
    let hSuccess = (data) => {
      var rows = [];
      for (
        let index = 0;
        index < data?.documentDetailDtoList?.length;
        index++
      ) {
        var tempObj = data?.documentDetailDtoList[index];
        var tempRow = {
          id: tempObj["documentId"],
          documentId: tempObj["documentId"],
          requestId:
            tempObj["requestId"] != null ? `${tempObj["requestId"]}` : "-",
          requestType:
            tempObj["requestType"] != null ? `${tempObj["requestType"]}` : "-",
          attachmentType:
            tempObj["attachmentType"] != null ? `${tempObj["attachmentType"]}` : "-",
          documentType:
            tempObj["documentType"] != null
              ? `${tempObj["documentType"]}`
              : "-",
          material: tempObj["material"].length > 0 ? `${tempObj["material"]}` : "-",
          childRequestId:
            tempObj["childRequestId"] != "" || null ? `${tempObj["childRequestId"]}` : "-",
          createdBy:
            tempObj["createdBy"] != null ? `${tempObj["createdBy"]}` : "-",
          docCreationDate:
            tempObj["docCreationDate"] != null
              ? `${tempObj["docCreationDate"]}`
              : "-",
          fileName: tempObj["fileName"] != null ? `${tempObj["fileName"]}` : "",
        };
        rows.push(tempRow);
      }
      setDocumentCount(data?.responseMessage?.count);
      setRmDataRows(rows);
      setroCount(data?.responseMessage?.count);
      setIsLoading(false);
      dispatch(commonSearchBarClear({ module: "DocumentMgmt" }));
    };

    let hError = (error) => {
      customError(error);
    };
    doAjax(
      `/${destination_DocumentManagement}/${END_POINTS.DMS_API.FETCH_DOCUMENTS}`,
      "post",
      hSuccess,
      hError,
      data
    );
  };
  const getFilterBasedOnPagination = () => {
    setIsLoading(true);
    let data = {
      documentId: "",
      fileName: "",
      fileType: "",
      artifactType: "",
      attachmentType: FilterSearchForm?.attType ?? "",
      documentUrl: "",
      createdBy: userRoles.includes(`${ROLES.SUPER_USER}`) ? FilterSearchForm?.createdBy : userData?.emailId,
      artifactId: "",
      material: FilterSearchForm?.number ?? "",
      docCreationDate: "",
      docCreatedBy: "",
      fromDate: moment(
        FilterSearchForm?.uploadedDate[0]
          ? FilterSearchForm?.uploadedDate[0]
          : ""
      ),
      toDate: moment(
        FilterSearchForm?.uploadedDate[1]
          ? FilterSearchForm?.uploadedDate[1]
          : ""
      ),
      childRequestId: FilterSearchForm?.childRequestId ?? "",
      requestId: "",
      documentType: FilterSearchForm?.docType ?? "",
      loginUser: "",
      role: "",
      requestType: FilterSearchForm?.requestType ?? "",
      top: pageSize,
      skip: pageSize * (page) ?? 0,
    };
    let hSuccess = (data) => {
      var rows = [];
      for (
        let index = 0;
        index < data?.documentDetailDtoList?.length;
        index++
      ) {
        var tempObj = data?.documentDetailDtoList[index];
        var tempRow = {
          id: tempObj["documentId"],
          documentId: tempObj["documentId"],
          requestId:
            tempObj["requestId"] != null ? `${tempObj["requestId"]}` : "-",
          requestType:
            tempObj["requestType"] != null ? `${tempObj["requestType"]}` : "-",
          documentType:
            tempObj["documentType"] != null
              ? `${tempObj["documentType"]}`
              : "-",
          uploadedBy:
            tempObj["createdBy"] != null ? `${tempObj["createdBy"]}` : "-",
          childRequestId:
            tempObj["childRequestId"] != "" ? `${tempObj["childRequestId"]}` : "-",
          attachmentType:
            tempObj["attachmentType"] != null ? `${tempObj["attachmentType"]}` : "-",
          docCreationDate:
            tempObj["docCreationDate"] != null
              ? tempObj["docCreationDate"]
              : "-",
          material: tempObj["material"].length > 0 ? `${tempObj["material"]}` : "-",
          fileName: tempObj["fileName"] != null ? `${tempObj["fileName"]}` : "",
        };
        rows.push(tempRow);
      }
      setDocumentCount(data?.responseMessage?.count);
      setRmDataRows((prevRows) => [...prevRows, ...rows]);
      setroCount(data?.responseMessage?.count);
      setIsLoading(false);
    };

    let hError = (error) => {
      customError(error);
    };
    doAjax(
      `/${destination_DocumentManagement}/${END_POINTS.DMS_API.FETCH_DOCUMENTS}`,
      "post",
      hSuccess,
      hError,
      data
    );
  };

  const handleSearch = () => {
    getFilter();
  };
  const clearSearchBar = () => {
    setgetDocName("");
  };
  useEffect(() => setTableData([...rmDataRows]), [rmDataRows]);

  useEffect(() => {
     
      if(page!==0){
        const requiredDataCount = pageSize * (page + 1);
        if (requiredDataCount > rmDataRows.length && rmDataRows.length % pageSize === 0) {
          getFilterBasedOnPagination();
        }
      }   
    }, [page]);

  useEffect(() => {
    getFilter();
  }, [pageSize]);

  const createMultiValueCell = (fieldName, displayName) => ({
    field: fieldName,
    headerName: t(displayName),
    editable: false,
    flex: 1,
    renderCell: (params) => {
        const materials = params.value ? params.value.split(",").map(m => m.trim()) : [];
        const displayCount = materials.length - 1;

        if (materials.length === 0) return "-";

        return (
          <Box sx={{ 
            display: "flex", 
            alignItems: "center",
            width: "100%",
            minWidth: 0 
          }}>
            <Tooltip 
              title={materials[0]}
              placement="top"
              arrow
            >
              <Typography 
                variant="body2" 
                sx={{ 
                  overflow: "hidden",
                  textOverflow: "ellipsis",
                  whiteSpace: "nowrap",
                  flex: 1,
                  minWidth: 0,
                }}
              >
                {materials[0]}
              </Typography>
            </Tooltip>
            {displayCount > 0 && (
              <Box sx={{ 
                display: "flex",
                alignItems: "center",
                ml: 1,
                flexShrink: 0 
              }}>
                <Tooltip
                  arrow
                  placement="right"
                  title={
                    <Box sx={{ p: 1, maxHeight: 200, overflowY: "auto" }}>
                      <Typography variant="subtitle2" sx={{ fontWeight: 600, mb: 1 }}>
                        {t("Additional Materials")} ({displayCount})
                      </Typography>
                      {materials.slice(1).map((material, idx) => (
                        <Typography key={idx} variant="body2" sx={{ mb: 0.5 }}>
                          {material}
                        </Typography>
                      ))}
                    </Box>
                  }
                >
                  <Box sx={{ 
                    display: "flex", 
                    alignItems: "center",
                    cursor: "pointer"
                  }}>
                    <InfoIcon 
                      sx={{ 
                        fontSize: "1rem",
                        color: "primary.main",
                        "&:hover": { color: "primary.dark" }
                      }} 
                    />
                    <Typography 
                      variant="caption" 
                      sx={{ 
                        ml: 0.5,
                        color: "primary.main",
                        fontSize: "11px"
                      }}
                    >
                      +{displayCount}
                    </Typography>
                  </Box>
                </Tooltip>
              </Box>
            )}
          </Box>
        );
      },

  })
  const createSingleValueCell = (fieldName, displayName) => ({
    field: fieldName,
    headerName: t(displayName),
    editable: false,
    flex: 1.4,
  })

  const createDocValueCell = (fieldName,displayName) => ({
    field: fieldName,
    headerName: t(displayName),
    editable: false,
    flex: 1,
    renderCell: (cellValues) => {
        const docType = cellValues.row.documentType?.toLowerCase();
        return (
          <>
            {iconMap[docType] || (
              <DescriptionOutlinedIcon sx={{ fontSize: 20, color: `${colors.icon.green}` }} />
            )}
            <span style={{ marginLeft: 8 }}>{cellValues.row.documentType}</span>
          </>
        );
      },
  })

  const createDateValueCell = (fieldName, displayName) => ({
    field: fieldName,
    headerName: t(displayName),
    editable: false,
    flex: 1,
    renderCell: (params) => {
          return <Typography sx={{ fontSize: "12px" }}>{formatDateTime(params.row[fieldName],appSettings?.dateFormat) }</Typography>;
        },
  })

  const createDocumentsColums = (data) => {
    const columns = [];
    let sortedData = data?.sort(
    (a, b) => a.MDG_MAT_SEQUENCE_NO - b.MDG_MAT_SEQUENCE_NO
    )|| [];
    if (sortedData) {
      sortedData?.forEach((item) => {
        if(item?.MDG_MAT_VISIBILITY === VISIBILITY_TYPE.DISPLAY){
              if (item?.MDG_MAT_UI_FIELD_NAME) {
                const fieldName = item.MDG_MAT_JSON_FIELD_NAME;
                const headerName = item.MDG_MAT_UI_FIELD_NAME;
                if(item.MDG_MAT_FIELD_TYPE === COLUMN_FIELD_TYPES.MULTIPLE) {
                  columns.push(createMultiValueCell(fieldName, headerName));
                }
                else if(item.MDG_MAT_FIELD_TYPE === COLUMN_FIELD_TYPES.SINGLE){
                  columns.push(createSingleValueCell(fieldName,headerName))
                }
                else if(item.MDG_MAT_FIELD_TYPE === COLUMN_FIELD_TYPES.DOCUMENTTYPE){
                  columns.push(createDocValueCell(fieldName,headerName))
                }
                 else if (item.MDG_MAT_FIELD_TYPE === COLUMN_FIELD_TYPES.DATE) {
                    columns.push(createDateValueCell(fieldName, headerName));
                  }
              }
            }
      })
    }
    columns.push({
      field: "actions",
      headerName: t("Actions"),
      editable: false,
      flex: 1,
      sortable: false,
      filterable: false,
      align: "center",
      headerAlign: "center",
      renderCell: (cellValues) => {
        return (
          <>
            <MatDownload
              index={cellValues.row.documentId}
              name={cellValues.row.fileName}
            />
            <MatView
              index={cellValues.row.documentId}
              name={cellValues.row.fileName}
            />
          </>
        );
      },
    },)
    return columns;
  }

  useEffect(() => {
      fetchColumnsFromDt()
      return () => {
        dispatch(
          initialDataUpdate({
            module: "DocumentManagement",
            initialData: [],
          })
        );
        dispatch(commonFilterClear({ module: "DocumentManagement", days: 7 }));
      };
    }, []);

    useEffect(() => {
        if (dtData) {
          const columnsGlobal = createDocumentsColums(dtData?.result?.[0]?.MDG_MAT_SEARCHSCREEN_COLUMN_ACTION_TYPE);
          setGenerateDynamicColumn(columnsGlobal);
        }
      }, [dtData]);

  /*********PRESET ***************************/


  const PresetObj = [
    { name: "transactionType", value: FilterSearchForm.transactionType },
    { name: "transactionId", value: FilterSearchForm.transactionId },
    {
      name: "fromDate",
      value: dateFormatter(FilterSearchForm.uploadedDate[0].toString()),
    },
    {
      name: "toDate",
      value: dateFormatter(FilterSearchForm.uploadedDate[1].toString()),
    },
    { name: "fileType", value: FilterSearchForm.docType },
    { name: "createdBy", value: FilterSearchForm.uploadedBy },
  ];
  const PresetMethod = (preset) => {
    dispatch(
      commonFilterUpdate({
        module: "DocumentManagement",
        filterData: {
          transactionId: preset.transactionId || "",
          transactionType: preset.transactionType || [],
          docType: preset.fileType || "",
          uploadedBy: preset.createdBy || "",
          uploadedDate: [
            moment(preset.startDate.split("T")[0], "YYYY-MM-DD").toDate(),
            moment(preset.endDate.split("T")[0], "YYYY-MM-DD").toDate(),
          ],
        },
      })
    );
  };

  let excelColumns = [];
  GenerateDynamicColumn.forEach((item) => {
    if (item.headerName.toLowerCase() !== "action" && !item.hide) {
      excelColumns.push({ label: item.headerName, value: item.field });
    }
  });
  const functions_ExportAsExcel = {
    data: [
      {
        sheet: "Documents Management",
        columns: GenerateDynamicColumn.map(({ field, headerName }) => ({
          label: headerName,
          value: field,
        })),
        content: rmDataRows,
      },
    ],
    settings: {
      fileName: `Documents Mgmt Datasheet-${moment(presentDate).format(
        "DD-MMM-YYYY"
      )}`, // Name of the resulting spreadsheet
      extraLength: 3, // A bigger number means that columns will be wider
      writeMode: "writeFile", // The available parameters are 'WriteFile' and 'write'. This setting is optional.
      writeOptions: {}, // Style options from https://docs.sheetjs.com/docs/api/write-options
      RTL: false, // Display the columns from right-to-left (the default value is false)
    },

    convertJsonToExcel: () => {
      xlsx(functions_ExportAsExcel.data, functions_ExportAsExcel.settings);
    },
    button: () => {
      return (
        <Button
          sx={{
            textTransform: "capitalize",
            position: "absolute",
            right: 0,
            top: 0,
          }}
          onClick={() => functions_ExportAsExcel.convertJsonToExcel()}
        >
          Download
        </Button>
      );
    },
  };

  return (
    <>
      <div className="printScreen" id={"container_outermost"}>
        <Box
          className="DocumentManagement "
          sx={{
            ...outermostContainer,
            margin: "0rem 0rem",
            backgroundColor: "#FAFCFF",
          }}
        >
          <NoDataDialog DataRows={DocumentsRow} />
          {downloadError && (
            <ReusableDialog
              dialogState={openMessageDialog}
              openReusableDialog={handleMessageDialogClickOpen}
              closeReusableDialog={handleMessageDialogClose}
              dialogTitle={messageDialogTitle}
              dialogMessage={messageDialogMessage}
              handleDialogConfirm={handleMessageDialogClose}
              dialogOkText={"OK"}
              dialogSeverity={messageDialogSeverity}
              handleDialogReject={handleMessageDialogClose}
            />
          )}
          {snackbar && (
            <ReusableSnackBar
              openSnackBar={openSnackbar}
              alertMsg={messageDialogMessage}
              handleSnackBarClose={handleSnackbarClose}
            />
          )}
          <Stack spacing={1}>
            {/* Information */}

              <Grid container mt={0} sx={outermostContainer_Information}>
                <Grid item md={4} xs={12}>
                  <Typography variant="h3">
                    <strong>{t("Document Management")}</strong>
                  </Typography>
                  <Typography sx={{ mb: '0.5rem' }}  variant="body2" color={colors.secondary.grey}>
                    {t("This view displays the list of Documents")}
                  </Typography>
                </Grid>
                <Grid
                  item
                  md={7}
                  xs={12}
                  sx={{
                    display: "flex",
                  }}
                >
                  <Grid
                    container
                    direction="row"
                    justifyContent="flex-end"
                    alignItems="center"
                    spacing={0}
                    mt={0}
                  >

                    
                    {checkIwaAccess(
                      iwaAccessData,
                      "Document Management",
                      "Create Service Request - DM"
                    ) && serviceRequestForm_Component.button()}
                  </Grid>
                </Grid>
              </Grid>

            <DocumentFilter
              names={names}
              PresetObj={PresetObj}
              PresetMethod={PresetMethod}
              handleSearch={handleSearch}
              getFilter={getFilter}
            />

            <ReusableDataTable
              title={t("List of Documents")}
              width="100%"
              isLoading={isLoading}
              paginationLoading={isLoading}
              columns={GenerateDynamicColumn || []}
              rows={tableData ?? []}
              pageSize={pageSize}
              onPageSizeChange={handlePageSizeChange}
              rowCount={roCount ?? rmDataRows?.length ?? 0}
              onPageChange={handlePageChange}
              getRowIdValue={"id"}
              hideFooter={true}
              showSearch={true}
              showRefresh={true}
              showExport={true}
              onSearch={handleSearchBar}
              onRefresh={getFilter}
              disableSelectionOnClick={true}
              status_onRowSingleClick={true}
              showCustomNavigation={true}
              status_onRowDoubleClick={true}
              page={page}
              module={"DocumentMgmt"}
            />
          </Stack>
          <ReusableBackDrop
            blurLoading={blurLoading}
            loaderMessage={loaderMessage}
          />

          {showDownloadButton && (
            <MultiDownloadButton
              value={value}
              setValue={setValue}
              DownloadNumber={DownloadNumber}
              DownloadArray={DownloadArray}
            />
          )}
        </Box>
      </div>
    </>
  );
}

export default MainDocPage;
