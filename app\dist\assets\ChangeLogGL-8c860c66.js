import{j as e,c as d,aj as P,d as N,bW as F,al as q,af as L,am as M,an as O,ai as W,F as _,fZ as z,T as G,B as p,aP as $,r as f,n as H,fM as J,aK as K,yn as Z,gv as U,ae as Q,aW as X,aG as Y,aZ as T,O as ee,gs as ae,a6 as I,a_ as te,R as le,ak as oe,ds as re,C as ne,c2 as se,aT as ie,aJ as de}from"./index-f7d9b065.js";function ge({duplicateFieldsArr:x,moduleName:h,open:C,onClose:c}){const u=t=>{const s=Array.isArray(t)?t:(t==null?void 0:t.split(",").map(b=>b.trim()))||[],i=s[0]||"";return d(p,{display:"flex",alignItems:"center",children:[e(N,{variant:"body2",noWrap:!0,children:i}),s.length>1&&e(G,{title:s.join(", "),arrow:!0,children:e(z,{sx:{ml:.5,fontSize:"18px",color:"#888"}})})]})};return e(_,{children:d(W,{open:C,onClose:()=>c(),maxWidth:"xl",fullWidth:!0,children:[e(P,{sx:{bgcolor:"#FFDAB9",color:"warning.contrastText"},children:e(N,{variant:"h6",gutterBottom:!0,sx:{display:"flex",alignItems:"center",justifyContent:"space-between"},children:d("span",{children:[e(F,{sx:{mr:1}})," Duplicate Requests Alert"]})})}),e(q,{children:e("div",{style:{marginTop:"20px"},children:e(L,{height:400,rows:x,columns:[{field:"objectNo",headerName:h,editable:!1,flex:1,width:150},{field:"reqId",headerName:"Req Id",editable:!1,flex:1,width:200,renderCell:t=>u(t.row.reqId)},{field:"childReqId",headerName:"Child Req Id",editable:!1,flex:1,width:200,renderCell:t=>u(t.row.childReqId)},{field:"requestedBy",headerName:"Requested By",editable:!1,flex:1,width:250,renderCell:t=>u(t.row.requestedBy)}],rowCount:x.length??0,getRowIdValue:"id"})})}),e(M,{children:e(O,{variant:"contained",onClick:c,children:"OK"})})]})})}const fe=({module:x,open:h,closeModal:C,requestId:c,requestType:u})=>{const{customError:m}=$(),[t,s]=f.useState(!0),[i,b]=f.useState(null),y=H(r=>r.payload.payloadData),[g,V]=f.useState([]);y==null||y.TemplateName;const{destination:B}=J(x),R={position:"absolute",top:"50%",left:"52%",transform:"translate(-50%, -50%)",width:"80%",height:"auto",bgcolor:"#fff",boxShadow:4,p:2},S=()=>{C(!1)};let E=[{field:"objectNumber",headerName:"Object Number",flex:1},{field:"FieldName",headerName:"Field Name",flex:1},{field:"PreviousValue",headerName:"Previous Value",flex:1},{field:"CurrentValue",headerName:"Current Value",flex:1},{field:"SAPValue",headerName:"SAP Value",flex:1},{field:"ChangedBy",headerName:"Changed By",flex:1},{field:"ChangedOn",headerName:"Changed On",flex:1}];f.useEffect(()=>{(async()=>{if(h&&!i)try{const a=await j(c,u);b(a)}catch(a){m("Error fetching changelog data:",a)}})()},[h,c]),f.useEffect(()=>{var r;if(i!=null&&i.length)try{let a=[];(r=i[0].ChangeLogData)==null||r.map(n=>{let l={};l.id=K(),l.objectNumber=Z(n.ObjectNo,1),l.SAPValue=n.SAPValue,l.PreviousValue=n.PreviousValue,l.CurrentValue=n.CurrentValue,l.ChangedOn=U(n.ChangedOn),l.ChangedBy=n.ChangedBy,l.FieldName=n.FieldName,a==null||a.push(l)}),V(a)}catch(a){m(Q.CHANGE_LOG_MESSAGE,a)}},[i]);const j=r=>{s(!0);const a=`/${B}${ie.DATA.GET_CHANGELOG_DATA}`;let n={requestId:"",childRequestId:r};return new Promise((l,k)=>{ne(a,"post",o=>{var w;if((o==null?void 0:o.statusCode)===de.STATUS_200&&((w=o==null?void 0:o.body)==null?void 0:w.length)>0){const v=o==null?void 0:o.body;s(!1),l(v)}else s(!1),l([])},o=>{s(!1),m(o),k(o)},n)})},A=new Date;A.setDate(A.getDate()-15);const D={convertJsonToExcel:()=>{let r=[];E.forEach(a=>{a.headerName.toLowerCase()!=="action"&&!a.hide&&r.push({header:a.headerName,key:a.field})}),se({fileName:`${c}_ChangeLog`,columns:r,rows:g})},button:()=>e(O,{sx:{textTransform:"capitalize",position:"absolute",right:0,top:0},onClick:()=>D.convertJsonToExcel(),children:"Download"})};return d(_,{children:[t&&e(Y,{blurLoading:t,loaderMessage:X.CHANGELOG_LOADING}),e(re,{open:h,onClose:S,"aria-labelledby":"modal-modal-title","aria-describedby":"modal-modal-description",children:d(p,{sx:R,children:[e(T,{children:d(ee,{item:!0,md:12,sx:{display:"flex",alignItems:"center",justifyContent:"space-between"},children:[d(p,{sx:{display:"flex",alignItems:"center",gap:"8px"},children:[e(ae,{sx:{color:"black",fontSize:"20px","&:hover":{transform:"rotate(360deg)",transition:"0.9s"},textAlign:"center",marginTop:"4px"}}),e(N,{id:"modal-modal-title",variant:"subtitle1",fontSize:"16px",fontWeight:"bold",sx:{color:"black"},children:"Change Log"})]}),d(p,{sx:{display:"flex",alignItems:"center",gap:"8px"},children:[e(G,{title:"Export Table",children:e(I,{sx:te,onClick:D.convertJsonToExcel,children:e(le,{iconName:"IosShare"})})}),e(I,{sx:{padding:"0 0 0 5px"},onClick:S,children:e(oe,{})})]})]})}),e("div",{className:"tab-content",style:{position:"relative",height:"100%",marginTop:16},children:e(T,{children:e(L,{rows:g,columns:E,getRowIdValue:"id",pageSize:g==null?void 0:g.length,autoHeight:!0,scrollbarSize:10,sx:{"& .MuiDataGrid-row:hover":{backgroundColor:"#EAE9FF40"},backgroundColor:"#fff"}})})})]})})]})};export{fe as C,ge as O};
