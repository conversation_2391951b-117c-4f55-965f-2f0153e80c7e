import{n as T,aP as I,s as d,da as M,aT as E,C as m,bI as h,aJ as O,zs as g,bJ as S}from"./index-f7d9b065.js";const f=_=>{window.location.hash.split("/");const e=T(n=>n==null?void 0:n.userManagement.taskData),{customError:a}=I(),D=T(n=>n.applicationConfig),t=d();return{getChangeTemplate:n=>{const p={decisionTableId:null,decisionTableName:"MDG_CHANGE_TEMPLATE_DT",version:"v6",rulePolicy:null,validityDate:null,conditions:[{"MDG_CONDITIONS.MDG_MODULE":_,"MDG_CONDITIONS.MDG_MAT_ROLE":e!=null&&e.ATTRIBUTE_5?e==null?void 0:e.ATTRIBUTE_5:M.REQ_INITIATE_FIN}],systemFilters:null,systemOrders:null,filterString:null},u=s=>{var l,r,i;if(s.statusCode===O.STATUS_200){const c=((i=(r=(l=s==null?void 0:s.data)==null?void 0:l.result)==null?void 0:r[0])==null?void 0:i.MDG_CHANGE_TEMPLATE_ACTION_TYPE)||[];t(g(c));const C=[...new Set(c.map(o=>o==null?void 0:o.MDG_CHANGE_TEMPLATE_NAME).filter(Boolean))].map(o=>({code:o}));t(S({keyName:"TemplateName",data:C}))}else return a("Failed to fetch data"),[]},N=s=>{a(s)},A=D.environment==="localhost"?E.INVOKE_RULES.LOCAL:E.INVOKE_RULES.PROD;m(`/${h}${A}`,"post",u,N,p)}}};export{f as u};
