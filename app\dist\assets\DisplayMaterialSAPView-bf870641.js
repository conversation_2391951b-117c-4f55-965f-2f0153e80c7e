import{b9 as Go,n as oo,ba as Uo,a as _o,b as ao,g as mo,u as Wo,s as vo,aP as wo,r as _,bb as Vo,bc as Ho,bd as ko,C as Z,be as i,c as A,j as n,Q as Bo,O as y,a6 as Fo,a_ as jo,a$ as zo,d as m,Z as k,aZ as no,B as Q,b5 as Ko,b6 as qo,F as lo,k as Yo,A as Zo,V as Qo,i as Xo,ae as Jo,aO as Do,bf as to,au as a,aG as co,bg as eo,bh as M,bi as ol,b2 as ll,aT as so}from"./index-f7d9b065.js";import{d as el,a as sl,b as nl}from"./Category-81eb2509.js";import{d as rl}from"./Description-ab582559.js";import{G as go}from"./GenericTabsForChange-c448c967.js";import{u as il}from"./useMaterialFieldConfig-3bde8f21.js";import{G as xo}from"./GenericViewGeneral-e6209433.js";import pl from"./AdditionalData-148835c9.js";import{T as dl}from"./TaxDataSAP-07618524.js";import"./FilterField-ed1f5dc1.js";import"./useChangeLogUpdate-1ba6b2dd.js";import"./AdapterDayjs-2a9281df.js";import"./advancedFormat-4f6292d4.js";import"./customParseFormat-1bc1aa07.js";import"./isBetween-c1c6beb9.js";import"./AutoCompleteType-13f5746b.js";import"./useMobilePicker-9b56b5b6.js";import"./CSSTransition-cd337b47.js";import"./DeleteOutline-584dc929.js";import"./useCustomDtCall-0fd16760.js";import"./SingleSelectDropdown-aee403d4.js";const X=({label:J,value:D,labelWidth:O="25%",centerWidth:t="5%",icon:B})=>A(no,{flexDirection:"row",alignItems:"center",children:[B&&n("div",{style:{marginRight:"10px"},children:B}),n(m,{variant:"body2",color:k.secondary.grey,style:{width:O},children:J}),n(m,{variant:"body2",fontWeight:"bold",sx:{width:t,textAlign:"center"},children:":"}),n(m,{variant:"body2",fontWeight:"bold",justifyContent:"flex-start",children:D||""})]}),$l=()=>{var io,po,ho;const{fetchMaterialFieldConfig:J}=il(),D=Go(()=>({customTabs:{"& .MuiTabs-scroller":{overflowX:"auto !important",overflowY:"hidden !important"}}})),O=oo(s=>s.payload),t=oo(s=>s.tabsData.allTabsData),B=oo(s=>{var o;return(o=s.materialDropDownData)==null?void 0:o.isOdataApiCalled}),{fetchAllDropdownMasterData:To}=Uo(),{t:R}=_o(),F=ao(),uo=mo();D();const Ao=Wo(),x=vo(),r=Ao.state,{customError:fo}=wo(),j=[],[h,yo]=_.useState([]),[Co,Io]=_.useState({}),[No,ro]=_.useState(!1),[Eo,Lo]=_.useState(""),[b,Mo]=_.useState(0),[Po,c]=_.useState(null);_.useEffect(()=>(B||(To(),x(Vo(!0))),Ho(to.MODULE,Do.MAT),Oo(),J(r==null?void 0:r.materialType.split(" - ")[0]),()=>{ko(to.MODULE)}),[]);const Oo=()=>{ro(!0);const s={materialNo:r==null?void 0:r.Number},o={"Basic Data":"Toclientdata",Sales:"Tosalesdata",Purchasing:"Toplantdata",Costing:"Toplantdata",Accounting:"Toaccountingdata",MRP:"Toplantdata",Warehouse:"Towarehousedata","Sales-Plant":"Tosalesdata","Work Scheduling":"Toplantdata"};function d(e){if(!Array.isArray(e))return{UniqueTaxDataSet:[]};const g=new Map;return e.forEach(S=>{const $=S==null?void 0:S.Depcountry;for(let C=1;C<=9;C++){const G=S[`TaxType${C}`],T=S[`Taxclass${C}`];if(G&&T!==void 0){const I=`${$}-${G}`,N=T==="1"?"Taxable":"Exempt";g.has(I)||g.set(I,{Country:$,TaxType:G,TaxClasses:[],SelectedTaxClass:{TaxClass:T,TaxClassDesc:N}});const p=g.get(I);p.TaxClasses.some(E=>E.TaxClass===T)||p.TaxClasses.push({TaxClass:T,TaxClassDesc:N}),p.SelectedTaxClass={TaxClass:T,TaxClassDesc:N}}}}),{UniqueTaxDataSet:Array.from(g.values())}}const bo=e=>{var g,S,$,C,G,T,I,N,p,P,E,U,W,v;if(e!=null&&e.body){const L=((g=e.body[0].ViewNames)==null?void 0:g.split(",").map(l=>l.trim()))||[],w=d((S=e==null?void 0:e.body[0])==null?void 0:S.Tocontroldata),V=[],Y={};L.forEach(l=>{const f=o[l]??"";f!==""&&e.body[0][f]&&(V.push(l),Y[l]=e.body[0][f])}),L.includes("Sales")&&(V.push("Sales-Plant"),Y["Sales-Plant"]=e.body[0].Toplantdata),V.push("Additional Data"),yo(V),Io(Y);const u=($=e==null?void 0:e.body[0])==null?void 0:$.Toclientdata;if(u){if(u.Pvalidfrom){const l=eo(u.Pvalidfrom);u.Pvalidfrom=l?l.toISOString().split("T")[0]:""}if(u.Svalidfrom){const l=eo(u.Svalidfrom);u.Svalidfrom=l?l.toISOString().split("T")[0]:""}}x(M({materialID:(C=e==null?void 0:e.body[0])==null?void 0:C.Material,viewID:"Basic Data",itemID:"basic",data:u})),x(M({materialID:(G=e==null?void 0:e.body[0])==null?void 0:G.Material,viewID:i.PURCHASING_GENERAL,itemID:i.PURCHASING_GENERAL,data:(T=e==null?void 0:e.body[0])==null?void 0:T.Toclientdata})),x(M({materialID:(I=e==null?void 0:e.body[0])==null?void 0:I.Material,viewID:i.SALES_GENERAL,itemID:i.SALES_GENERAL,data:(N=e==null?void 0:e.body[0])==null?void 0:N.Toclientdata})),x(ol({materialID:(p=e==null?void 0:e.body[0])==null?void 0:p.Material,data:(E=(P=e==null?void 0:e.body[0])==null?void 0:P.Touomdata)==null?void 0:E.map((l,f)=>{var H,So;return{...l,id:`${l.Material}-${f}`,uomId:`${l.Material}-${f}`,xValue:(l==null?void 0:l.Denominatr)||"",aUnit:(l==null?void 0:l.AltUnit)||"",measureUnitText:"",yValue:(l==null?void 0:l.Numerator)||"",eanUpc:(l==null?void 0:l.EanUpc)||"",eanCategory:(l==null?void 0:l.EanCat)||"",length:l==null?void 0:l.Length,width:l==null?void 0:l.Width,height:l==null?void 0:l.Height,unitsOfDimension:(l==null?void 0:l.UnitDim)||"",volume:(l==null?void 0:l.Volume)||"",volumeUnit:(l==null?void 0:l.Volumeunit)||"",grossWeight:(l==null?void 0:l.GrossWt)||"",netWeight:f===0&&(((So=(H=e==null?void 0:e.body[0])==null?void 0:H.Toclientdata)==null?void 0:So.NetWeight)||(l==null?void 0:l.NetWeight))||"",weightUnit:(l==null?void 0:l.UnitOfWt)||""}})})),x(ll({materialID:(U=e==null?void 0:e.body[0])==null?void 0:U.Material,data:(W=e==null?void 0:e.body[0])==null?void 0:W.Tomaterialdescription.map((l,f)=>({id:f+1,language:l==null?void 0:l.Langu,materialDescription:l==null?void 0:l.MatlDesc}))})),x(M({materialID:(v=e==null?void 0:e.body[0])==null?void 0:v.Material,viewID:"TaxData",itemID:"TaxData",data:w||{TaxData:{UniqueTaxDataSet:[]}}})),ro(!1),Lo("")}},q=()=>{};Z(`/${a}/data/displayLimitedMaterialData`,"post",bo,q,s)},Ro=(s,o)=>{Mo(o),c(null)},$o=(s,o,d)=>(bo,q)=>{var T,I,N;if(d===i.COSTING){let p={materialNo:o==null?void 0:o.Material,plant:o==null?void 0:o.Plant};o==null||o.Plant;let P={materialNo:o==null?void 0:o.Material,valArea:o==null?void 0:o.Plant},E=`/${a}/${so.ACCORDION_API.PLANT}`,U=`/${a}/${so.ACCORDION_API.ACCOUNTING}`;Z(E,"post",L=>{const w=L==null?void 0:L.body[0].Toplantdata[0];Z(U,"post",u=>{var H;const l=(H=u==null?void 0:u.body[0])==null?void 0:H.Toaccountingdata[0],f={...w,...l};x(M({materialID:o==null?void 0:o.Material,viewID:d,itemID:o==null?void 0:o.Plant,data:f}))},()=>{},P)},()=>{},p),c(q?s:null);return}let e={},g="",S="";d===i.PURCHASING||d===i.MRP||d===i.WORKSCHEDULING||d===i.SALES_PLANT?(e={materialNo:o==null?void 0:o.Material,plant:o==null?void 0:o.Plant},S=o==null?void 0:o.Plant,g=`/${a}/data/displayLimitedPlantData`):d===i.WAREHOUSE?(e={materialNo:o==null?void 0:o.Material,whNumber:o==null?void 0:o.WhseNo},S=o==null?void 0:o.WhseNo,g=`/${a}/${so.ACCORDION_API.WAREHOUSE}`):d===i.ACCOUNTING?(e={materialNo:o==null?void 0:o.Material,valArea:o==null?void 0:o.ValArea},S=o==null?void 0:o.ValArea,g=`/${a}/data/displayLimitedAccountingData`):d===i.SALES&&(e={materialNo:o==null?void 0:o.Material,salesOrg:o==null?void 0:o.SalesOrg,distChnl:o==null?void 0:o.DistrChan},S=`${o==null?void 0:o.SalesOrg}-${o==null?void 0:o.DistrChan}`,g=`/${a}/data/displayLimitedSalesData`);const $=p=>{var P,E,U,W,v,L;if(d===i.PURCHASING||d===i.MRP||d===i.WORKSCHEDULING||d===i.SALES_PLANT)x(M({materialID:o==null?void 0:o.Material,viewID:d,itemID:o==null?void 0:o.Plant,data:{...p==null?void 0:p.body[0].Toplantdata[0],ProdProf:((P=p==null?void 0:p.body[0].Toplantdata[0])==null?void 0:P.Prodprof)||""}}));else if(d===i.ACCOUNTING)x(M({materialID:o==null?void 0:o.Material,viewID:d,itemID:o==null?void 0:o.ValArea,data:(E=p==null?void 0:p.body[0])==null?void 0:E.Toaccountingdata[0]}));else if(d===i.WAREHOUSE)x(M({materialID:o==null?void 0:o.Material,viewID:d,itemID:o==null?void 0:o.WhseNo,data:(U=p==null?void 0:p.body[0])==null?void 0:U.Towarehousedata[0]}));else if(d===i.SALES){if((v=(W=p==null?void 0:p.body[0])==null?void 0:W.Tosalesdata[0])!=null&&v.ValidFrom){const w=eo(p.body[0].Tosalesdata[0].ValidFrom);p.body[0].Tosalesdata[0].ValidFrom=w?w.toISOString().split("T")[0]:""}x(M({materialID:o==null?void 0:o.Material,viewID:d,itemID:`${o==null?void 0:o.SalesOrg}-${o==null?void 0:o.DistrChan}`,data:(L=p==null?void 0:p.body[0])==null?void 0:L.Tosalesdata[0]}))}},C=p=>{fo(p)};!((N=(I=(T=O==null?void 0:O[o==null?void 0:o.Material])==null?void 0:T.payloadData)==null?void 0:I[d])!=null&&N[S])&&Z(g,"post",$,C,e),c(q?s:null)},z=t!=null&&t.hasOwnProperty(i.SALES_GENERAL)?Object.entries(t[i.SALES_GENERAL]):[],K=t!=null&&t.hasOwnProperty(i.PURCHASING_GENERAL)?Object.entries(t[i.PURCHASING_GENERAL]):[];return A("div",{style:{backgroundColor:"#FAFCFF"},children:[n(y,{container:!0,sx:Bo,children:n(y,{item:!0,md:12,sx:{padding:"16px",display:"flex"},children:A(y,{md:9,sx:{display:"flex"},children:[n(Fo,{color:"primary",sx:jo,onClick:()=>uo(-1),children:n(zo,{sx:{fontSize:"25px",color:"#000000"}})}),A(y,{item:!0,md:12,children:[n(m,{variant:"h3",children:n("strong",{children:R("Display Material")})}),n(m,{variant:"body2",color:"#777",children:R("This view displays the details of the materials")})]})]})})}),A(y,{container:!0,display:"flex",flexDirection:"row",flexWrap:"nowrap",sx:{justifyContent:"space-between",alignItems:"center",paddingLeft:"29px",backgroundColor:k.basic.lighterGrey,borderRadius:"10px",boxShadow:"0 2px 8px rgba(0,0,0,0.06)"},children:[A(no,{width:"48%",spacing:1,sx:{padding:"10px 15px",borderRight:"1px solid #eaedf0"},children:[n(y,{item:!0,children:n(X,{label:R("Material"),value:(r==null?void 0:r.Number)||"",labelWidth:"35%",icon:n(el,{sx:{color:F.palette.primary.main,fontSize:"20px"}})})}),n(y,{item:!0,children:n(X,{label:R("Industry Sector"),value:(r==null?void 0:r.indSector)||"",labelWidth:"35%",icon:n(sl,{sx:{color:F.palette.primary.main,fontSize:"20px"}})})})]}),A(no,{width:"48%",spacing:1,marginRight:"-10%",sx:{padding:"10px 15px"},children:[n(y,{item:!0,children:n(X,{label:R("Material Type"),value:(r==null?void 0:r.materialType)||"",labelWidth:"35%",icon:n(nl,{sx:{color:F.palette.primary.main,fontSize:"20px"}})})}),n(y,{item:!0,children:n(X,{label:R("Material Description"),value:(r==null?void 0:r.materialDesc)||"",labelWidth:"35%",icon:n(rl,{sx:{color:F.palette.primary.main,fontSize:"20px"}})})})]})]}),n(y,{children:t&&h.length>0?n(Q,{sx:{marginTop:"30px",border:"1px solid #e0e0e0",padding:"16px",background:k.primary.white},children:A(Q,{sx:{marginTop:"-10px",marginLeft:"5px"},children:[n(Ko,{value:b,onChange:Ro,"aria-label":"material tabs",sx:{top:100,zIndex:1e3,background:"#fafcff",marginLeft:"20px",marginBottom:"-20px"},children:h.map((s,o)=>n(qo,{label:R(s)},o))}),n(Q,{sx:{padding:2,marginTop:2},children:h[b]==="Basic Data"&&O?n(go,{disabled:!0,materialID:r==null?void 0:r.Number,dropDownData:j,basicDataTabDetails:t["Basic Data"],activeViewTab:"Basic Data",plantData:"basic"}):h[b]==="Additional Data"?n(pl,{disableCheck:!0,materialID:r==null?void 0:r.Number}):A(lo,{children:[h[b]===i.SALES&&A(lo,{children:[n(dl,{materialID:r==null?void 0:r.Number}),(z==null?void 0:z.length)>0&&n(xo,{materialID:r==null?void 0:r.Number,GeneralFields:z,disabled:!0,dropDownData:j,viewName:(io=i)==null?void 0:io.SALES_GENERAL})]}),h[b]===i.PURCHASING&&A(lo,{children:[" ",(K==null?void 0:K.length)>0&&n(xo,{materialID:r==null?void 0:r.Number,GeneralFields:K,disabled:!0,dropDownData:j,viewName:(po=i)==null?void 0:po.PURCHASING_GENERAL})]}),(ho=Co[h[b]])==null?void 0:ho.map((s,o)=>A(Yo,{sx:{marginBottom:"20px",boxShadow:3},expanded:Po===o,onChange:$o(o,s,h[b]),children:[n(Zo,{expandIcon:n(Qo,{}),sx:{backgroundColor:"#f5f5f5",borderRadius:"10px",padding:"8px 16px","&:hover":{backgroundColor:"#e0e0e0"}},children:n(m,{variant:"h6",sx:{fontWeight:"bold"},children:h[b]===i.PURCHASING||h[b]===i.COSTING||h[b]===i.MRP||h[b]===i.WORKSCHEDULING||h[b]===i.SALES_PLANT?`Plant - ${s==null?void 0:s.Plant}`:h[b]===i.SALES?`Sales Org - ${s==null?void 0:s.SalesOrg} ,  Distribution Channel - ${s==null?void 0:s.DistrChan}`:h[b]===i.ACCOUNTING?`Plant - ${s==null?void 0:s.ValArea}`:h[b]===i.WAREHOUSE?`Warehouse - ${s==null?void 0:s.WhseNo}`:`${s==null?void 0:s.Material}`})}),O&&n(Xo,{sx:{padding:"16px"},children:n(m,{sx:{fontSize:"0.875rem",color:"#555"},children:n(go,{disabled:!0,materialID:r==null?void 0:r.Number,dropDownData:j,basicDataTabDetails:t[h[b]],activeViewTab:h[b],plantData:h[b]==="Sales"?`${s==null?void 0:s.SalesOrg}-${s==null?void 0:s.DistrChan}`:h[b]==="Purchasing"?`${s==null?void 0:s.Plant}`:h[b]==="Accounting"?`${s==null?void 0:s.ValArea}`:h[b]==="Warehouse"?`${s==null?void 0:s.WhseNo}`:`${s==null?void 0:s.Plant}`})})})]},o))]})})]})}):n(Q,{sx:{marginTop:"30px",border:`1px solid ${k.secondary.grey}`,padding:"16px",background:`${k.primary.white}`,textAlign:"center"},children:n("span",{children:Jo.NO_DATA_AVAILABLE})})}),n(co,{blurLoading:No,loaderMessage:Eo})]})};export{$l as default};
