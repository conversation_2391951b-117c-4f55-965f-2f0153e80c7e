import{c9 as Ze,ca as Xe,cb as je,c as l,dr as eo,aj as ye,j as e,d as O,ak as Me,a6 as K,al as pe,B as le,ag as ge,bS as Ee,ai as Ae,$ as we,dA as To,a4 as Io,a8 as H,cs as he,k as Do,A as So,O as A,an as z,r as h,a as ke,b as wo,C as te,dm as ae,aT as ne,V as Wo,W as yo,i as Mo,t as vo,Y as ie,qN as me,qO as De,a1 as No,a2 as Lo,a3 as Ro,a0 as Go,a7 as Oo,ac as Bo,U as Uo,qP as zo,cw as ve,o as $o,ft as Po,aa as Ye,aX as ce,fs as Fo,am as Ne,qQ as qo,bD as Yo,J as Ho,aJ as _e,b3 as Vo,pO as <PERSON>,Z as ue,bW as <PERSON>,qR as Qo,qS as <PERSON>,bl as <PERSON>o,bm as Xo,bn as jo,bo as Se,bp as V,bq as et,d7 as oo,br as to,qT as We,ad as ao,F as Le,T as re,qU as ot,af as tt,qV as at,fY as nt,g as rt,gn as Q,aO as Ve,cq as be,N as lt,aZ as st,Q as it,a_ as Ke,gD as dt,qW as Je,gE as ct,b5 as ut,b6 as pt,ah as ht,aG as ft,qX as gt}from"./index-f7d9b065.js";import{u as xt}from"./useDownloadExcel-cd596a31.js";import{d as Ct}from"./CloudUpload-0ba6431e.js";import{E as mt}from"./ExcelOperationsCard-49e9ffd2.js";import{A as bt}from"./ArrowLeftOutlined-a31a51d5.js";import{d as Et}from"./AddOutlined-9a9caebd.js";import{C as _t}from"./ChangeLogWF-b8efdaaf.js";import{d as At}from"./Download-52c4427b.js";import{A as kt}from"./AttachmentUploadDialog-43cc9099.js";import{d as Tt}from"./DeleteOutlined-e668453f.js";import{d as It}from"./TrackChangesTwoTone-7a2ab513.js";import"./CloudDownload-9a7605e9.js";import"./EyeOutlined-0bb7ab85.js";import"./Delete-5278579a.js";import"./utilityImages-067c3dc2.js";var Re={},Dt=Xe;Object.defineProperty(Re,"__esModule",{value:!0});var no=Re.default=void 0,St=Dt(Ze()),wt=je;no=Re.default=(0,St.default)((0,wt.jsx)("path",{d:"M13 7h-2v4H7v2h4v4h2v-4h4v-2h-4zm-1-5C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2m0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8"}),"AddCircleOutline");const Wt=({open:C,onClose:s,setDialogState:u,handleOpenDialog:B})=>l(Ae,{open:C,onClose:s,TransitionComponent:eo,maxWidth:"md",fullWidth:!0,PaperProps:{sx:{borderRadius:3,boxShadow:10,overflow:"hidden"}},children:[l(ye,{sx:{display:"flex",justifyContent:"space-between",alignItems:"center",px:3,py:2,backgroundColor:a=>a.palette.grey[100],borderBottom:"1px solid",borderColor:"divider"},children:[e(O,{variant:"h6",fontWeight:"bold",children:"Which scenario do you want?"}),e(K,{onClick:s,sx:{color:"text.secondary","&:hover":{color:"error.main"}},children:e(Me,{})})]}),l(pe,{sx:{px:3,py:4,mb:-7},children:[l(le,{sx:{display:"flex",flexDirection:{xs:"column",sm:"row"},gap:4,mt:4},children:[l(ge,{elevation:5,onClick:()=>{u(a=>({...a,createWorkflow:!1})),B()},sx:{flex:1,p:7,cursor:"pointer",borderRadius:3,border:"2px solid transparent",background:a=>`linear-gradient(145deg, ${a.palette.background.paper}, ${a.palette.grey[100]})`,display:"flex",flexDirection:"column",alignItems:"center",textAlign:"center",transition:"all 0.3s ease","&:hover":{borderColor:"primary.main",boxShadow:8,transform:"scale(1.02)"}},children:[e(no,{sx:{fontSize:48,color:"primary.main",mb:3}}),e(O,{variant:"h6",fontWeight:"bold",gutterBottom:!0,children:"Create Workflow Directly in Application"}),e(O,{variant:"body2",color:"text.secondary",sx:{flexGrow:1,lineHeight:1.6},children:"Use our visual builder to design approval flows, notifications, and logic steps tailored to your needs."})]}),l(ge,{elevation:5,onClick:()=>u(a=>({...a,createWorkflow:!1,excelOperations:!0})),sx:{flex:1,p:7,cursor:"pointer",borderRadius:3,border:"2px solid transparent",background:a=>`linear-gradient(145deg, ${a.palette.background.paper}, ${a.palette.grey[100]})`,display:"flex",flexDirection:"column",alignItems:"center",textAlign:"center",transition:"all 0.3s ease","&:hover":{borderColor:"primary.main",boxShadow:8,transform:"scale(1.02)"}},children:[e(Ct,{sx:{fontSize:48,color:"primary.main",mb:2}}),e(O,{variant:"h6",fontWeight:"bold",gutterBottom:!0,children:"Create Workflow with Upload"}),e(O,{variant:"body2",color:"text.secondary",sx:{flexGrow:1,lineHeight:1.6},children:"Upload a configuration file to instantly build a workflow without manual setup."})]})]}),e(Ee,{sx:{my:4,borderStyle:"dashed"}}),e(le,{sx:{display:"flex",justifyContent:"flex-end"}})]})]}),yt=({open:C,handleClose:s,setDialogState:u,selectedOption:B,handleChanged:a,MODULE_MAP:m,handleDownload:b,setEnableDocumentUpload:D,enableDocumentUpload:M,handleUpload:w})=>l(Ae,{open:C,maxWidth:"md",fullWidth:!0,PaperProps:{sx:{borderRadius:"12px",padding:"4px",position:"relative"}},children:[e(K,{onClick:()=>u({createWorkflow:!0,excelOperations:!1}),sx:{position:"absolute",left:8,top:8,color:p=>p.palette.grey[500],zIndex:1,"&:hover":{color:p=>p.palette.primary.main,backgroundColor:p=>p.palette.action.hover}},children:e(bt,{})}),l(pe,{sx:{pt:6},children:[e(K,{"aria-label":"close",onClick:s,sx:{position:"absolute",right:8,top:8,color:p=>p.palette.grey[500],"&:hover":{color:p=>p.palette.error.main,backgroundColor:p=>p.palette.action.hover}},children:e(Me,{})}),l(we,{sx:{mb:2,mt:2,minWidth:200,maxWidth:300},children:[e(To,{id:"dropdown-label",children:"Module Type"}),l(Io,{labelId:"dropdown-label",value:B,label:"Module Type",onChange:a,MenuProps:{PaperProps:{sx:{maxHeight:200}}},children:[e(H,{value:m.MAT,children:"MATERIAL"}),e(H,{value:m.ART,children:"ARTICLE"}),e(H,{value:m.PC,children:"PROFIT CENTER"}),e(H,{value:m.CC,children:"COST CENTER"}),e(H,{value:m.BK,children:"BANK KEY"}),e(H,{value:m.GL,children:"GENERAL LEDGER"}),e(H,{value:m.CCG,children:"COST CENTER GROUP"}),e(H,{value:m.PCG,children:"PROFIT CENTER GROUP"}),e(H,{value:m.CEG,children:"COST ELEMENT GROUP"}),e(H,{value:m.BOM,children:"BILL OF MATERIAL"}),e(H,{value:m.IO,children:"INTERNAL ORDER"})]})]}),e(mt,{handleDownload:b,setEnableDocumentUpload:D,enableDocumentUpload:M,handleUploadMaterial:w})]})]}),Mt=he(Do)(({theme:C})=>({marginTop:"0px !important",border:`1px solid ${C.palette.primary.main}`,borderRadius:"8px",boxShadow:"0 2px 4px rgba(0,0,0,0.05)","&:not(:last-child)":{borderBottom:0},"&:before":{display:"none"}})),vt=he(So)(({theme:C})=>({minHeight:"2rem !important",margin:"0px !important",backgroundColor:C.palette.primary.light,borderRadius:"8px 8px 0 0",transition:"all 0.2s ease-in-out","&:hover":{backgroundColor:C.palette.primary.light}})),Nt=he(A)({padding:"0.75rem",gap:"0.5rem"}),Lt=he(A)({display:"flex",justifyContent:"flex-end",paddingRight:"0.75rem",paddingBottom:"0.75rem",paddingTop:"0rem",gap:"0.5rem"}),Qe=he(z)({borderRadius:"4px",padding:"4px 12px",textTransform:"none",fontSize:"0.875rem"}),de=he(O)(({theme:C})=>({fontSize:"0.75rem",color:C.palette.primary.dark,marginBottom:"0.25rem",fontWeight:500})),Rt=({searchParameters:C,setWfSearchForm:s,wfSearchForm:u,activeTab:B,getAllWorflows:a})=>{const[m,b]=h.useState([]),[D,M]=h.useState([]),[w,p]=h.useState(null),[R,N]=h.useState([]),[f,_]=h.useState(!1),{t:E}=ke(),W=wo(),k=(o,c)=>{let t;t=c||"";let n={...u,[o]:t};s(n)},j=o=>{if(o!==null){var c=o;s(t=>({...t,createdOn:c}))}};h.useEffect(()=>{R!=null&&R.length?k("workflowName",R==null?void 0:R.map(o=>o.code)):k("workflowName",[])},[R]),h.useEffect(()=>{J()},[]);const Z=o=>{var i,g;_(!0);let c={workflowName:o};const t=x=>{var d;_(!1),b(((d=x==null?void 0:x.data)==null?void 0:d.map(Y=>({code:Y,desc:""})))||[])},n=x=>{_(!1),console.log(x)};te(`/${ae}${(g=(i=ne)==null?void 0:i.WORKFLOW_APIS)==null?void 0:g.WF_NAMES}`,"post",t,n,c)},J=()=>{var t,n;_(!0);const o=i=>{_(!1),M((i==null?void 0:i.data)||[])},c=i=>{_(!1)};te(`/${ae}${(n=(t=ne)==null?void 0:t.WORKFLOW_APIS)==null?void 0:n.WF_CREATEDBY}`,"get",o,c)},$=o=>{const c=o.target.value;if(w&&clearTimeout(w),c.length>=4){const t=setTimeout(()=>{Z(c)},500);p(t)}};return e(A,{container:!0,children:e(A,{item:!0,md:12,children:l(Mt,{defaultExpanded:!1,children:[l(vt,{expandIcon:e(Wo,{sx:{fontSize:"1.25rem",color:W.palette.primary.dark}}),"aria-controls":"panel1a-content",id:"panel1a-header",className:"filterWorkFlow",children:[e(yo,{sx:{fontSize:"1.25rem",marginRight:1,color:W.palette.primary.dark}}),e(O,{sx:{fontSize:"0.875rem",fontWeight:600,color:W.palette.primary.dark},children:E("Filter Workflows ")})]}),l(Mo,{sx:{padding:0},children:[e(Nt,{container:!0,children:e(A,{container:!0,rowSpacing:1,spacing:2,alignItems:"center",sx:{padding:"0rem 1rem 0.5rem"},children:C==null?void 0:C.filter(o=>o.MDG_MAT_VISIBILITY!=="Hidden").sort((o,c)=>o.MDG_MAT_SEQUENCE_NO-c.MDG_MAT_SEQUENCE_NO).map((o,c)=>{var t,n,i,g,x,d,Y;return l(vo.Fragment,{children:[(o==null?void 0:o.MDG_MAT_JSON_FIELD_NAME)==="region"&&l(A,{item:!0,md:2,children:[e(de,{sx:ie,children:E(o==null?void 0:o.MDG_MAT_UI_FIELD_NAME)}),e(me,{options:(t=De)==null?void 0:t.REGION,value:(n=u==null?void 0:u.region)!=null&&n.length?u.region:[],onChange:S=>{k("region",S)},placeholder:E(`SELECT ${o==null?void 0:o.MDG_MAT_UI_FIELD_NAME}`).toUpperCase()})]}),(o==null?void 0:o.MDG_MAT_JSON_FIELD_NAME)==="scenario"&&l(A,{item:!0,md:2,children:[e(de,{sx:ie,children:E(o==null?void 0:o.MDG_MAT_UI_FIELD_NAME)}),e(me,{options:(i=De)==null?void 0:i.SCENARIO,value:(g=u==null?void 0:u.scenario)!=null&&g.length?u.scenario:[],onChange:S=>{k("scenario",S)},placeholder:E(`SELECT ${o==null?void 0:o.MDG_MAT_UI_FIELD_NAME}`).toUpperCase()})]}),(o==null?void 0:o.MDG_MAT_JSON_FIELD_NAME)==="createdBy"&&l(A,{item:!0,md:2,children:[e(de,{sx:ie,children:E(o==null?void 0:o.MDG_MAT_UI_FIELD_NAME)}),e(me,{options:D||null,value:(x=u==null?void 0:u.createdBy)!=null&&x.length?u.createdBy:[],onChange:S=>{k("createdBy",S)},placeholder:E(`SELECT ${o==null?void 0:o.MDG_MAT_UI_FIELD_NAME}`).toUpperCase()})]}),(o==null?void 0:o.MDG_MAT_JSON_FIELD_NAME)==="bifurcationGroup"&&l(A,{item:!0,md:2,children:[e(de,{sx:ie,children:E(o==null?void 0:o.MDG_MAT_UI_FIELD_NAME)}),e(me,{options:(d=De)==null?void 0:d.BIFURCATION_GROUP,value:(Y=u==null?void 0:u.bifurcationGroup)!=null&&Y.length?u.bifurcationGroup:[],onChange:S=>{k("bifurcationGroup",S)},placeholder:E(`SELECT ${o==null?void 0:o.MDG_MAT_UI_FIELD_NAME}`).toUpperCase()})]}),(o==null?void 0:o.MDG_MAT_JSON_FIELD_NAME)==="dateRange"&&l(A,{item:!0,md:2,children:[e(de,{sx:ie,children:E(o==null?void 0:o.MDG_MAT_UI_FIELD_NAME)}),e(we,{fullWidth:!0,sx:{padding:0,height:"37px"},children:e(No,{dateAdapter:Lo,children:e(Ro,{handleDate:j,date:u==null?void 0:u.createdOn})})})]}),(o==null?void 0:o.MDG_MAT_JSON_FIELD_NAME)==="workflowName"&&l(A,{item:!0,md:2,children:[e(de,{sx:ie,children:E(o==null?void 0:o.MDG_MAT_UI_FIELD_NAME)}),e(we,{size:"small",fullWidth:!0,children:e(Go,{matGroup:m,selectedMaterialGroup:R,setSelectedMaterialGroup:N,isDropDownLoading:f,placeholder:E(`ENTER ${o==null?void 0:o.MDG_MAT_UI_FIELD_NAME}`).toUpperCase(),onInputChange:$,minCharacters:4})})]})]},c)})})}),l(Lt,{children:[e(Qe,{variant:"outlined",size:"small",startIcon:e(Oo,{sx:{fontSize:"1rem"}}),onClick:()=>s({}),children:E("Clear")}),e(Qe,{variant:"contained",size:"small",startIcon:e(Bo,{sx:{fontSize:"1rem"}}),onClick:()=>{a(B),N([])},children:E("Search")})]})]})]})})})},Gt=({setWfSearchForm:C,wfSearchForm:s,activeTab:u,getAllWorflows:B})=>e(A,{container:!0,sx:Uo,children:e(A,{item:!0,md:12,children:e(Rt,{searchParameters:zo,setWfSearchForm:C,wfSearchForm:s,activeTab:u,getAllWorflows:B})})});var Ge={},Ot=Xe;Object.defineProperty(Ge,"__esModule",{value:!0});var Oe=Ge.default=void 0,Bt=Ot(Ze()),Ut=je;Oe=Ge.default=(0,Bt.default)((0,Ut.jsx)("path",{d:"M3 21h3.75L17.81 9.94l-3.75-3.75L3 17.25zm2-2.92 9.06-9.06.92.92L5.92 19H5zM18.37 3.29a.9959.9959 0 0 0-1.41 0l-1.83 1.83 3.75 3.75 1.83-1.83c.39-.39.39-1.02 0-1.41z"}),"ModeEditOutlineOutlined");const zt=({open:C,onClose:s,onApplyFilters:u,setFiltersObj:B})=>{const[a,m]=h.useState({region:"",workflowName:"",module:"",scenario:"",template:"",validityDateRange:null,bifurcationGroup:""}),[b,D]=h.useState(""),[M,w]=h.useState(!1),{showSnackbar:p}=ve(),{getDtCall:R,dtData:N}=$o(),[f,_]=h.useState({region:[{code:"US",desc:"USA"},{code:"EUR",desc:"Europe"}],module:[{code:"Material",desc:"Material"},{code:"Article",desc:"Article"},{code:"Profit Center",desc:"Profit Center"},{code:"Cost Center",desc:"Cost Center"},{code:"Bank Key",desc:"Bank Key"},{code:"General Ledger",desc:"General Ledger"},{code:"Cost Center Group",desc:"Cost Center Group"},{code:"Profit Center Group",desc:"Profit Center Group"},{code:"Cost Element Group",desc:"Cost Element Group"},{code:"Bill Of Material",desc:"Bill Of Material"},{code:"Internal Order",desc:"Internal Order"}],scenario:[{code:"Create",desc:"Create"},{code:"Change",desc:"Change"},{code:"Create with Upload",desc:"Create"},{code:"Change with Upload",desc:"Change"}],template:[{code:"Logistic Data",desc:"Logistic Data"},{code:"MRP Data",desc:"MRP Data"},{code:"Item Cat Group",desc:"Item Cat Group"},{code:"Set to DNU",desc:"Set to DNU"},{code:"Warehouse View 2",desc:"Custom Template"},{code:"Change Status",desc:"Advanced Template"},{code:"Change Descriptions",desc:"Advanced Template"}],bifurcationGroup:[]}),E=()=>((a==null?void 0:a.scenario)===ce.CREATE?["region","workflowName","module","scenario"]:["region","workflowName","module","scenario","bifurcationGroup","template"]).every(n=>n==="workflowName"?a[n].trim()!=="":a[n]!==""),W=()=>{let t={decisionTableId:null,decisionTableName:Ho.DICISIONTABLENAME,version:"v3",conditions:[{"MDG_CONDITIONS.MDG_MAT_REGION":(a==null?void 0:a.region)||"US","MDG_CONDITIONS.MDG_MAT_MODULE_NAME":(a==null?void 0:a.scenario)||ce.CREATE}]};R(t)},k=async t=>new Promise((n,i)=>{var S,se;w(!0),D("");const g={workflowName:t.workflowName,region:t.region,module:t.module,scenario:t.scenario,template:(t==null?void 0:t.scenario)===ce.CREATE?"NA":t.template,bifurcationGroup:t.bifurcationGroup||"GROUP-1"},x=P=>{w(!1),n(P)},d=P=>{w(!1);const Te=(P==null?void 0:P.message)||WORKFLOW_DATA_CONSTANTS.CHECKDUP_ERR_MSG;D(Te),i(P)},Y=`/${ae}${(se=(S=ne)==null?void 0:S.WORKFLOW_APIS)==null?void 0:se.DUPLICACY_CHECK}`;te(Y,"post",x,d,g)});h.useEffect(()=>{a!=null&&a.scenario&&(a!=null&&a.region)&&W()},[a==null?void 0:a.scenario,a==null?void 0:a.region]),h.useEffect(()=>{var t;if(N){let n=((t=N==null?void 0:N.result[0])==null?void 0:t.MDG_MAT_MATERIAL_BIFURCATION_ACTION_TYPE)||[];const i=[...new Set(n.map(g=>g.MDG_MATERIAL_GROUPS))].map(g=>({code:g,desc:g}));_({...f,bifurcationGroup:i})}},[N]);const j=(t,n)=>{b&&D(""),m(t==="workflowName"?i=>({...i,[t]:n}):i=>({...i,[t]:n?n.code:""}))},Z=async()=>{var t;try{const n={...a,validFrom:a.validityDateRange?a.validityDateRange[0]:"",validTo:a.validityDateRange?a.validityDateRange[1]:""};delete n.validityDateRange;const i=await k(n);(i==null?void 0:i.statusCode)===((t=_e)==null?void 0:t.STATUS_200)?(p(i==null?void 0:i.data,"success"),u(n),B(n),s()):p(i==null?void 0:i.data,"error")}catch{}},J=()=>{m({region:"",workflowName:"",module:"",scenario:"",template:"",validityDateRange:null,bifurcationGroup:""}),D("")},$=()=>{D(""),s()},o=(t,n)=>{var g;return n&&((g=f[t])==null?void 0:g.find(x=>x.code===n))||null},c=(t,n,i,g=!1)=>e(Vo,{value:o(t,a[t]),onChange:(x,d)=>j(t,d),options:i,disabled:g,getOptionLabel:x=>x?x.desc:"",isOptionEqualToValue:(x,d)=>(x==null?void 0:x.code)===(d==null?void 0:d.code),renderInput:x=>e(Ye,{...x,label:l("span",{children:[n,e("span",{style:{color:"red",marginLeft:"2px"},children:"*"})]}),sx:{"& .MuiOutlinedInput-root":{borderRadius:2}}}),renderOption:(x,d)=>e(O,{...x,component:"li",style:{fontSize:12,padding:"8px 16px",width:"100%",cursor:"pointer",display:"flex",alignItems:"start"},children:e("span",{style:{whiteSpace:"nowrap",overflow:"hidden",textOverflow:"ellipsis"},title:`${d==null?void 0:d.code}${d!=null&&d.desc?` - ${d==null?void 0:d.desc}`:""}`,children:e("strong",{children:d==null?void 0:d.code})})}),fullWidth:!0});return l(Ae,{open:C,onClose:$,maxWidth:"md",fullWidth:!0,TransitionComponent:eo,TransitionProps:{timeout:300},PaperProps:{sx:{borderRadius:3,background:"linear-gradient(145deg, #ffffff 0%, #f8f9ff 100%)",boxShadow:"0 20px 60px rgba(102, 126, 234, 0.15)"}},children:[l(ye,{sx:{pb:1},children:[l(le,{display:"flex",alignItems:"center",justifyContent:"space-between",children:[e(le,{display:"flex",alignItems:"center",gap:1,children:e(O,{variant:"h5",fontWeight:600,children:"Create Request"})}),e(K,{onClick:$,size:"small",children:e(Po,{})})]}),e(O,{variant:"body2",color:"text.secondary",sx:{mt:.5},children:"Configure your parameters to begin with the workflow creation"})]}),l(pe,{sx:{pt:2},children:[l(A,{container:!0,spacing:3,sx:{pt:2},children:[e(A,{item:!0,xs:12,sm:6,children:e(Ye,{fullWidth:!0,label:l("span",{children:["Workflow Name",e("span",{style:{color:"red",marginLeft:"2px"},children:"*"})]}),value:a.workflowName,onChange:t=>{const n=/^[a-zA-Z0-9._\- ]*$/,i=t.target.value;n.test(i)&&j("workflowName",i)},onKeyPress:t=>{/[a-zA-Z0-9._\- ]/.test(t.key)||t.preventDefault()},inputProps:{pattern:"[a-zA-Z0-9._\\- ]*",title:"Only letters, numbers, periods, underscores, hyphens, and spaces are allowed"},sx:{"& .MuiOutlinedInput-root":{borderRadius:2}}})}),e(A,{item:!0,xs:12,sm:6,children:c("region","Region",f.region)}),e(A,{item:!0,xs:12,sm:6,children:c("module","Module",f.module)}),e(A,{item:!0,xs:12,sm:6,children:c("scenario","Scenario",f.scenario)}),e(A,{item:!0,xs:12,sm:6,children:c("template","Template",f.template,(a==null?void 0:a.scenario)===ce.CREATE)}),e(A,{item:!0,xs:12,sm:6,children:c("bifurcationGroup","Bifurcation Group",f.bifurcationGroup)})]}),b&&e(le,{sx:{mt:3},children:e(Fo,{severity:"error",onClose:()=>D(""),children:b})})]}),l(Ne,{sx:{px:3,pb:3,pt:2,gap:1},children:[e(z,{onClick:J,startIcon:e(qo,{}),color:"inherit",variant:"outlined",sx:{textTransform:"none",px:3},children:"Clear All"}),e(le,{flex:1}),e(z,{onClick:$,color:"warning",variant:"outlined",sx:{textTransform:"none",px:4},children:"Cancel"}),e(z,{onClick:Z,variant:"contained",disabled:!E()||M,startIcon:M?e(Yo,{size:16}):null,sx:{textTransform:"none",px:4},children:M?"Checking...":"Proceed"})]})]})},$t=({selectedRows:C,count:s,tableData:u,handleMassCancel:B,handleMassDownload:a,maxLimit:m=10})=>{const[b,D]=h.useState(!1),[M,w]=h.useState(!1),{t:p}=ke(),R=Ko();if(C.length===0)return null;const N=u.filter(k=>C.includes(k.id)),f=C.length>m,_=()=>{w(!0)},E=()=>{w(!1),B(C)},W=()=>{a(C)};return l(Le,{children:[l(ge,{elevation:3,sx:{position:"fixed",bottom:20,left:"50%",transform:"translateX(-50%)",zIndex:2e3,padding:"8px 16px",backgroundColor:f?ue.error.deepRed:R.palette.primary.main,color:"white",borderRadius:"24px",display:"flex",alignItems:"center",gap:1},children:[l(O,{sx:{display:"flex",alignItems:"center",gap:1},children:[f&&e(Jo,{fontSize:"small"}),C.length," out of ",s," selected",f&&l(O,{component:"span",sx:{ml:1,fontSize:"0.875rem"},children:["(Maximum ",m," workflows allowed)"]})]}),e(Ee,{orientation:"vertical",flexItem:!0,sx:{backgroundColor:"rgba(255, 255, 255, 0.3)"}}),e(z,{variant:"text",onClick:()=>D(!0),sx:{color:"white",textTransform:"none",fontSize:"0.875rem",fontWeight:500,padding:"4px 8px",minWidth:"auto","&:hover":{backgroundColor:"rgba(255, 255, 255, 0.1)"}},startIcon:e(Qo,{}),children:p("View Details")}),e(Ee,{orientation:"vertical",flexItem:!0,sx:{backgroundColor:"rgba(255, 255, 255, 0.3)"}}),e(z,{variant:"text",onClick:W,disabled:f,sx:{color:ue.primary.white,textTransform:"none",fontSize:"0.875rem",fontWeight:500,padding:"4px 8px",minWidth:"auto","&:hover":{backgroundColor:"rgba(255, 255, 255, 0.1)"}},startIcon:e(At,{}),children:p("Download Workflow(s)")}),e(Ee,{orientation:"vertical",flexItem:!0,sx:{backgroundColor:"rgba(255, 255, 255, 0.3)"}}),e(z,{variant:"text",onClick:_,disabled:f,sx:{color:ue.primary.white,textTransform:"none",fontSize:"0.875rem",fontWeight:500,padding:"4px 8px",minWidth:"auto","&:hover":{backgroundColor:"rgba(255, 255, 255, 0.1)"}},startIcon:e(He,{}),children:p("Delete Workflow(s)")})]}),l(Ae,{open:b,onClose:()=>D(!1),maxWidth:"md",fullWidth:!0,children:[l(ye,{children:[p("Selected Workflows"),e(K,{"aria-label":"close",onClick:()=>D(!1),sx:{position:"absolute",right:8,top:8},children:e(Me,{})})]}),e(pe,{children:e(Zo,{component:ge,sx:{maxHeight:400,boxShadow:"0 4px 6px -1px rgb(0 0 0 / 0.1)",borderRadius:"8px",border:"1px solid #e5e7eb"},children:l(Xo,{size:"small",stickyHeader:!0,sx:{"& .MuiTableCell-root":{borderBottom:"1px solid #f3f4f6"}},children:[e(jo,{children:l(Se,{children:[e(V,{sx:{backgroundColor:"#f8fafc",fontWeight:600,fontSize:"0.875rem",color:"#374151",borderBottom:"2px solid #e5e7eb",textTransform:"uppercase",letterSpacing:"0.025em",padding:"12px 16px"},children:p("Workflow ID")}),e(V,{sx:{backgroundColor:"#f8fafc",fontWeight:600,fontSize:"0.875rem",color:"#374151",borderBottom:"2px solid #e5e7eb",textTransform:"uppercase",letterSpacing:"0.025em",padding:"12px 16px"},children:p("Workflow Name")}),e(V,{sx:{backgroundColor:"#f8fafc",fontWeight:600,fontSize:"0.875rem",color:"#374151",borderBottom:"2px solid #e5e7eb",textTransform:"uppercase",letterSpacing:"0.025em",padding:"12px 16px"},children:p("Scenario")}),e(V,{sx:{backgroundColor:"#f8fafc",fontWeight:600,fontSize:"0.875rem",color:"#374151",borderBottom:"2px solid #e5e7eb",textTransform:"uppercase",letterSpacing:"0.025em",padding:"12px 16px"},children:p("Created By")}),e(V,{sx:{backgroundColor:"#f8fafc",fontWeight:600,fontSize:"0.875rem",color:"#374151",borderBottom:"2px solid #e5e7eb",textTransform:"uppercase",letterSpacing:"0.025em",padding:"12px 16px"},children:p("Created On")})]})}),l(et,{children:[N.map((k,j)=>l(Se,{sx:{"&:nth-of-type(odd)":{backgroundColor:"#f9fafb"},"&:nth-of-type(even)":{backgroundColor:"#ffffff"}},children:[e(V,{sx:{padding:"12px 16px",fontSize:"0.875rem",color:"#1f2937",fontWeight:500},children:k.workflowId}),e(V,{sx:{padding:"12px 16px",fontSize:"0.875rem",color:"#1f2937",fontWeight:400},children:k.workflowName}),e(V,{sx:{padding:"12px 16px",fontSize:"0.875rem",color:"#6b7280"},children:k.scenario}),e(V,{sx:{padding:"12px 16px",fontSize:"0.875rem",color:"#6b7280"},children:k.createdBy}),e(V,{sx:{padding:"12px 16px",fontSize:"0.875rem",color:"#6b7280"},children:k.createdAt})]},k.id)),N.length===0&&e(Se,{children:e(V,{colSpan:5,align:"center",sx:{padding:"24px",color:"#9ca3af",fontStyle:"italic",fontSize:"0.875rem"},children:p("No workflows found")})})]})]})})})]}),M&&l(oo,{isOpen:M,titleIcon:e(He,{size:"small",color:"error",sx:{fontSize:"20px"}}),Title:p("Mass Delete Workflows"),handleClose:()=>w(!1),children:[e(pe,{sx:{mt:2},children:l(O,{children:[p("Are you sure you want to delete")," ",C.length," ",p("selected workflows? This action cannot be undone.")]})}),l(Ne,{children:[e(z,{variant:"outlined",size:"small",sx:{...to},onClick:()=>w(!1),children:p(We.CANCEL)}),e(z,{variant:"contained",size:"small",sx:{...ao},onClick:E,children:p("Confirm")})]})]})]})};function Pt({isLoading:C,tableData:s,selectedRows:u,activeTab:B,onSelectionChange:a,onSearch:m,onChangeLogExport:b,onDownloadAllData:D,onDelete:M,onView:w,onEdit:p,onViewChangelog:R,rmDataRows:N,setTableData:f}){const{t:_}=ke(),{showSnackbar:E}=ve(),[W,k]=h.useState({data:{},isVisible:!1}),[j,Z]=h.useState(!1),J=[{field:"workflowId",headerName:_("Workflow Id"),flex:.5,editable:!1,align:"center"},{field:"workflowName",headerName:_("Workflow Name"),flex:1,editable:!1},{field:"scenario",headerName:_("Scenario"),flex:1},{field:"createdAt",headerName:_("Created At"),flex:1},{field:"updatedAt",headerName:_("Updated At"),flex:1},{field:"createdBy",headerName:_("Created By"),flex:1.5,align:"left"},{field:"actions",headerName:_("Action"),sortable:!1,flex:1,align:"center",headerAlign:"center",disableClickEventBubbling:!0,renderCell:c=>l("div",{children:[e(re,{title:_("Cancel"),children:e(K,{disabled:!1,"aria-label":"View Metadata",onClick:t=>{t.stopPropagation(),k({data:c,isVisible:!0})},children:e(Tt,{sx:{color:t=>"#cc3300",padding:"1px"}})})}),e(re,{title:_("View Workflow"),children:e(K,{onClick:t=>{var n;t.stopPropagation(),w((n=c.row)==null?void 0:n.workflowId)},disabled:!1,children:e(ot,{sx:{color:`${ue.blue.indigo}`,padding:"1px"}})})}),e(re,{title:_("Edit Workflow"),children:e(K,{onClick:t=>{var n;t.stopPropagation(),p((n=c.row)==null?void 0:n.workflowId)},disabled:!1,children:e(Oe,{sx:{color:`${ue.warning.amber}`,padding:"1px"}})})}),e(re,{title:_("View Changelog"),children:e(K,{onClick:t=>{var n;t.stopPropagation(),R((n=c.row)==null?void 0:n.workflowId)},disabled:!1,children:e(It,{sx:{color:`${ue.icon.green}`,padding:"1px"}})})})]})}],$=()=>{var g,x,d,Y;Z(!0);const c={workflowId:(x=(g=W==null?void 0:W.data)==null?void 0:g.row)==null?void 0:x.workflowId},t=S=>{Z(!1),E(S==null?void 0:S.data,"success"),k({data:{},isVisible:!1}),M(null)},n=S=>{Z(!1),E(S==null?void 0:S.message,"error")},i=`/${ae}${(Y=(d=ne)==null?void 0:d.WORKFLOW_APIS)==null?void 0:Y.DELETE_WF}`;te(i,"post",t,n,c)},o=c=>{if(!c){f([...N]);return}const t=N.filter(n=>{var x;let i=!1,g=Object.keys(n);for(let d=0;d<g.length&&(i=n[g[d]]?(n==null?void 0:n[g==null?void 0:g[d]])&&((x=n==null?void 0:n[g==null?void 0:g[d]].toString().toLowerCase())==null?void 0:x.indexOf(c==null?void 0:c.toLowerCase()))!=-1:!1,!i);d++);return i});f([...t]),m&&m(c)};return l(Le,{children:[e(tt,{module:"WorkflowBench",isLoading:C,width:"100%",title:_("Workflow Details List"),rows:s??[],columns:J,getRowIdValue:"id",showSearch:!0,showChangeLogExport:!0,showAllWFDataExport:!0,onSearch:o,handleChangeLogExport:b,handleDownloadWFAllData:D,checkboxSelection:!0,onRowsSelectionHandler:a,selectionModel:u,disableSelectionOnClick:!0}),(W==null?void 0:W.isVisible)&&l(oo,{isOpen:W==null?void 0:W.isVisible,titleIcon:e(at,{size:"small",color:"error",sx:{fontSize:"20px"}}),Title:"Delete Workflow!",handleClose:()=>k({data:{},isVisible:!1}),children:[e(pe,{sx:{mt:2},children:nt.CANCEL_MESSAGE}),l(Ne,{children:[e(z,{variant:"outlined",size:"small",sx:{...to},onClick:()=>k({data:{},isVisible:!1}),children:_(We.CANCEL)}),e(z,{variant:"contained",size:"small",sx:{...ao},onClick:$,children:_(We.DELETE)})]})]})]})}function na(){const C=rt(),[s,u]=h.useState({}),[B,a]=h.useState(!0),{t:m}=ke(),[b,D]=h.useState(0),[M,w]=h.useState([]),[p,R]=h.useState([]),[N,f]=h.useState(!1),[_,E]=h.useState(""),[W,k]=h.useState(!1),[j,Z]=h.useState([]);ve();const[J,$]=h.useState([]),[o,c]=h.useState(!1),[t,n]=h.useState("Material"),[i,g]=h.useState([]),{handleDownloadWF:x,handleUploadWF:d,handleMassCancelWF:Y,handleMassDownloadWF:S}=xt(),[se,P]=h.useState({createWorkflow:!1,excelOperations:!1}),[Te,xe]=h.useState(!1),[ro,Be]=h.useState(!1),Ue=r=>{n(r.target.value)},lo=()=>{Be(!0)},so=()=>{Be(!1)},io=r=>{var T;C("createWorkflow",{state:{mode:"create",headerData:r,workflowData:{},module:(r==null?void 0:r.module)||((T=Q)==null?void 0:T[b])}})};h.useEffect(()=>$([...M]),[M]);const co=()=>{var r;x(E,f,t||((r=Q)==null?void 0:r[b]),"create")},uo=r=>{var T,v;d(r,E,f,t||((T=Q)==null?void 0:T[b]),c,ze,D,(v=ce)==null?void 0:v.CREATE_WITH_UPLOAD)},po=r=>{Y(r,E,f,g,b,Ce)},ho=r=>{var T;xe(!1),d(r,E,f,t,xe,()=>{},D,(T=ce)==null?void 0:T.CHANGE_WITH_UPLOAD)},fo=r=>{S(r,E,f,g,b)},go=()=>{var r;x(E,f,(r=Q)==null?void 0:r[b],"")},xo=(r,T)=>{D(T)},Co=()=>{k(!1)},mo=()=>{var U,F,L;const r={module:(U=Q)==null?void 0:U[b]};f(!0);const T=`/${ae}${(L=(F=ne)==null?void 0:F.WORKFLOW_APIS)==null?void 0:L.CONSOLIDATED_CHANGELOG}`;te(T,"post",y=>{var q;if(f(!1),(y==null?void 0:y.statusCode)===_e.STATUS_200){const X=(y==null?void 0:y.data)||[];Je(X,gt,{fileName:`Change Log - ${(q=Q)==null?void 0:q[b]}`})}},()=>{f(!1)},r)},ze=()=>{P(r=>({...r,excelOperations:!1}))},bo=r=>{g(r)};h.useEffect(()=>{Ce(b)},[b]);const Ce=(r=0)=>{var F,L,y,q,X,oe;a(!0);var T={page:0,size:100,orderBy:"createdOn",module:((F=Q)==null?void 0:F[r])||((L=Ve)==null?void 0:L.MAT),region:(s==null?void 0:s.region)||[],scenario:(s==null?void 0:s.scenario)||[],bifurcationGroups:(s==null?void 0:s.bifurcationGroup)||[],createdBy:(s==null?void 0:s.createdBy)||[],workflowName:(s==null?void 0:s.workflowName)||[],fromDate:(y=s==null?void 0:s.createdOn)!=null&&y[0]?be(s==null?void 0:s.createdOn[0]).format("YYYY-MM-DDThh:mm:ss"):"",toDate:(q=s==null?void 0:s.createdOn)!=null&&q[1]?be(s==null?void 0:s.createdOn[1]).format("YYYY-MM-DDThh:mm:ss"):""};const v=`/${ae}${(oe=(X=ne)==null?void 0:X.WORKFLOW_APIS)==null?void 0:oe.FETCH_WF_LIST}`;te(v,"post",G=>{var Pe,Fe,qe;if((G==null?void 0:G.statusCode)===((Pe=_e)==null?void 0:Pe.STATUS_200)){var fe=[];for(let Ie=0;Ie<((Fe=G==null?void 0:G.data)==null?void 0:Fe.length);Ie++){var I=(qe=G==null?void 0:G.data)==null?void 0:qe[Ie],ko={id:(I==null?void 0:I.Workflow_Id)||(I==null?void 0:I.id)||"",workflowId:(I==null?void 0:I.Workflow_Id)||(I==null?void 0:I.id)||"",workflowName:I==null?void 0:I.Workflow_Name,scenario:I==null?void 0:I.Scenario,createdAt:be(I.Created_At).format("DD MMM YYYY")??"",createdBy:I==null?void 0:I.Created_By,updatedAt:be(I.Last_Updated_At).format("DD MMM YYYY")??""};fe.push(ko)}w(fe),a(!1)}else w([]),a(!1)},G=>{w([]),a(!1)},T)};let Eo=[m("Material"),"Article","Profit Center","Cost Center","Bank Key","General Ledger","Cost Center Group","Profit Center Group","Cost Element Group","Bill Of Material","Internal Order"];return e(Le,{children:l("div",{className:"printScreen",id:"container_outermost",children:[e(Wt,{open:se.createWorkflow,onClose:()=>P(r=>({...r,createWorkflow:!1})),setDialogState:P,handleOpenDialog:lo}),e("div",{className:"ServiceRequest",style:{...lt,margin:"0rem 0rem",backgroundColor:"#FAFCFF"},children:l(st,{children:[l(A,{container:!0,mt:0,sx:it,children:[l(A,{item:!0,md:5,xs:12,children:[e(O,{variant:"h3",children:e("strong",{children:m("Workflow Management")})}),e(O,{variant:"body2",color:"#777",children:m("This view displays the list of Workflows")})]}),e(A,{item:!0,md:7,xs:12,sx:{display:"flex"},children:l(A,{container:!0,direction:"row",justifyContent:"flex-end",alignItems:"center",spacing:0,mt:0,children:[e(re,{title:"Reload",children:e(K,{sx:{...Ke},onClick:()=>Ce(b),children:e(dt,{})})}),e(re,{title:"Export Table",children:e(K,{sx:{...Ke},onClick:()=>{var r;return Je(J,[],{fileName:`Workflow List - ${(r=Q)==null?void 0:r[b]}`})},children:e(ct,{})})})]})})]}),e(Gt,{handleSearch:()=>{},setWfSearchForm:u,wfSearchForm:s,activeTab:b,getAllWorflows:Ce}),e(A,{container:!0,children:e(ut,{className:"module-WF",value:b,onChange:xo,variant:"scrollable",sx:{background:"#FFF",borderBottom:"1px solid #BDBDBD",width:"100%"},"aria-label":"mui tabs example",children:Eo.map((r,T)=>e(pt,{disabled:!1,sx:{fontSize:"12px",fontWeight:"700"},label:r},T))})}),e(yt,{open:se.excelOperations,handleClose:ze,setDialogState:P,selectedOption:t,handleChanged:Ue,MODULE_MAP:Ve,handleDownload:co,setEnableDocumentUpload:c,enableDocumentUpload:o,handleUpload:uo}),e(A,{container:!0,children:e(le,{sx:{width:"100%"},children:e(Pt,{isLoading:B,tableData:J,selectedRows:i,activeTab:b,onSelectionChange:bo,onSearch:r=>Ao(r),onChangeLogExport:mo,onDownloadAllData:go,onDelete:r=>setIsDeleteDialogVisible({data:r,isVisible:!0}),onView:r=>$e(r,"view"),onEdit:r=>$e(r,"edit"),onViewChangelog:r=>_o(r),rmDataRows:M,setTableData:$})})}),e(zt,{open:ro,onClose:so,onApplyFilters:io,setFiltersObj:R}),Te&&e(kt,{artifactId:"",artifactName:"",setOpen:xe,handleUpload:ho,showModuleDropdown:!0,selectedOption:t,handleChanged:Ue,dialogTitle:"Change Workflow"}),e(_t,{open:W,onClose:Co,data:j}),e(ge,{sx:{position:"fixed",bottom:0,left:0,right:0,zIndex:1},elevation:2,children:l(ht,{className:"container_BottomNav",showLabels:!0,sx:{display:"flex",justifyContent:"flex-end"},children:[e(re,{title:"Edit Single or Multiple Workflows through upload",children:e(z,{className:"buttonChangeWorkFlow",size:"small",variant:"contained",sx:{m:1},onClick:()=>xe(!0),startIcon:e(Oe,{}),children:m("Change Workflow")})}),e(re,{title:"Create New Workflows",children:e(z,{className:"buttonCreateWorkFlow",size:"small",variant:"contained",onClick:()=>P({...se,createWorkflow:!0}),startIcon:e(Et,{}),children:m("Create Workflow")})})]})}),N&&e(ft,{blurLoading:N,loaderMessage:_}),e($t,{selectedRows:i,count:J.length,tableData:J,handleMassCancel:po,handleMassDownload:fo,maxLimit:10})]})})]})});function $e(r,T){var L,y;f(!0);const v={workflowId:r},ee=`/${ae}${(y=(L=ne)==null?void 0:L.WORKFLOW_APIS)==null?void 0:y.FETCH_WF_DETAILS}`;te(ee,"post",q=>{var G,fe;f(!1);const{headerData:X,...oe}=(q==null?void 0:q.data)||{};T==="view"?C("viewWorkflow",{state:{mode:T,headerData:X,workflowData:oe,module:(G=Q)==null?void 0:G[b]}}):T==="edit"&&C("editWorkflow",{state:{mode:T,headerData:X,workflowData:oe,module:(fe=Q)==null?void 0:fe[b]}})},()=>{f(!1)},v)}function _o(r){var F,L;f(!0);const T={workflowId:r},v=`/${ae}${(L=(F=ne)==null?void 0:F.WORKFLOW_APIS)==null?void 0:L.FETCH_CHANGELOG}`;te(v,"post",y=>{var q;if(f(!1),(y==null?void 0:y.statusCode)===_e.STATUS_200){const X=(q=y==null?void 0:y.data)==null?void 0:q.map((oe,G)=>({...oe,id:oe.id||G+1}));Z(X),k(!0)}},()=>{f(!1)},T)}function Ao(r){if(!r){$([...M]);return}const T=M.filter(v=>{var F;let ee=!1,U=Object.keys(v);for(let L=0;L<U.length&&(ee=v[U[L]]?(v==null?void 0:v[U==null?void 0:U[L]])&&((F=v==null?void 0:v[U==null?void 0:U[L]].toString().toLowerCase())==null?void 0:F.indexOf(r==null?void 0:r.toLowerCase()))!=-1:!1,!ee);L++);return ee});$([...T])}}export{na as default};
