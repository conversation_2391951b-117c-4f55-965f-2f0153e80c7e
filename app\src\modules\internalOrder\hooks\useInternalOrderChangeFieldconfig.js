import { changeTemplateDT } from "@app/tabsDetailsSlice";
import { doAjax } from "@components/Common/fetchService";
import React from "react";
import { useSelector, useDispatch } from "react-redux";
import { destination_IDM } from "@destinations";
import { END_POINTS } from "@constant/apiEndPoints";
import { API_CODE, TASK_NAME } from "@constant/enum";
import useLogger from "@hooks/useLogger";
import { setChangeFieldSelectionData } from "@app/payloadSlice";
import { setDropDownDataIO } from "@InternalOrder/slice/InternalOrderSlice";

const useInternalOrderChangeFieldconfig = (props) => {

  const currentHash = window.location.hash;
  const parts = currentHash.split("/");

  const task = useSelector((state) => state?.userManagement.taskData);

  const { customError } = useLogger();
  const applicationConfig = useSelector((state) => state.applicationConfig);
  const dispatch = useDispatch();

  const getChangeTemplate = () => {
    const payload = {
      decisionTableId: null,
      decisionTableName: "MDG_INTORD_CHANGE_TEMPLATE",
      version: "v2",
      rulePolicy: null,
      validityDate: null,
      conditions: [
        {
          "MDG_CONDITIONS.MDG_GROUP_ROLE": task?.ATTRIBUTE_5?task?.ATTRIBUTE_5:TASK_NAME.REQ_INITIATE_FIN,
          
        },
      ],
      systemFilters: null,
      systemOrders: null,
      filterString: null,
    };
    const hSuccess = (data) => {
      if (data.statusCode === API_CODE.STATUS_200) {
        const responseData =
          data?.data?.result?.[0]?.MDG_CHANGE_TEMPLATE_ACTION_TYPE || [];
        dispatch(setChangeFieldSelectionData(responseData));
        // Get unique template names
        const templateNames = [
          ...new Set(
            responseData
              .map((item) => item?.MDG_CHANGE_TEMPLATE_NAME)
              .filter(Boolean)
          ),
        ].map((name) => ({ code: name }));        
        // dispatch(setDropDownDataIO({ keyName: "TemplateName", data: templateNames }));
        // return templateNames;
      } else {
        customError("Failed to fetch data");
        return [];
      }
    };
    const hError = (error) => {
      customError(error);
    };

    const endpoint =
      applicationConfig.environment === "localhost"
        ? END_POINTS.INVOKE_RULES.LOCAL
        : END_POINTS.INVOKE_RULES.PROD;

    doAjax(`/${destination_IDM}${endpoint}`, "post", hSuccess, hError, payload);
  };

  return { getChangeTemplate };
};

export default useInternalOrderChangeFieldconfig;