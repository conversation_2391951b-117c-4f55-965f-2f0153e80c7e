import{bw as R,dR as A,aP as E,C as D,cY as t,M as T,dS as F,dT as I,dU as C}from"./index-f7d9b065.js";const m={dropDown:{VarPurOrderUnitActive:[{code:"0",desc:"Not Active"},{code:"1",desc:"Active"},{code:"2",desc:"Active with Own Price"}],PriceCtrl:A,QualFFreeGoodDisc:[{code:"0",desc:"Not eligible for Discount in Kind"},{code:"1",desc:"Eligible for Discount in Kind for Purchasing And Sales"},{code:"2",desc:"Eligible for Discount in Kind only for Purchasing"},{code:"3",desc:"Eligible for Discount in Kind only for Sales"}],VarTransportationGroup:[{code:"NSTG",desc:"No Spec Trans Grp"},{code:"55555",desc:"test"},{code:"1600",desc:"Etoposide"},{code:"0001",desc:"PFZR - On pallets"},{code:"0002",desc:"SPC Air"},{code:"0009",desc:"Narcotics"},{code:"1000",desc:"Refrigerated 150lbs"},{code:"1010",desc:"Refrigerated 12 lbs"},{code:"1050",desc:"Refrigerated 60lbs"},{code:"1060",desc:"FREEZE (-25C-15C)"},{code:"1080",desc:"Refrigerated"},{code:"1081",desc:"Biologic Cold Storage"},{code:"1100",desc:"Vault Item 150lbs"},{code:"1150",desc:"Vault Item 60lbs"},{code:"1200",desc:"Aerosol 150lbs"},{code:"1250",desc:"Aerosol 60lbs"}],VarMolecule:[{code:"TIO",desc:"Tiotropium - Bronchodilator"},{code:"TOF",desc:"Tofacitinib - JAK Inhibitor"},{code:"TOC",desc:"Tocopheryl - Vitamin E"},{code:"TIZ",desc:"Tizanidine - Muscle Relaxant"},{code:"TOB",desc:"Tobramycin - Antibiotic"},{code:"TOL",desc:"Tolazamide - Anti-diabetic Agent"}],VarSalt:[{code:"ACETATE",desc:"Acetate"},{code:"LACTATE",desc:"Lactate"},{code:"LYSINE",desc:"Lysine"},{code:"MAGNESIUM",desc:"Magnesium"},{code:"MALEATE",desc:"Maleate"},{code:"MEGLUMINE",desc:"Meglumine"},{code:"MESYLATE",desc:"Mesylate"},{code:"MONOFUMARATE",desc:"Monofumarate"},{code:"MONONITRATE",desc:"Mononitrate"},{code:"NAPISILATE",desc:"Napsilate"},{code:"NITRATE",desc:"Nitrate"},{code:"NONE_SPECIFIED",desc:"None specified"},{code:"OCTASULFATE",desc:"Octasulfate"},{code:"OROTATE",desc:"Orotate"},{code:"OXALATE",desc:"Oxalate"}],pharmacopoeiaData:[{code:"BP",desc:"British Pharmacopoeia"},{code:"CHP",desc:"Chinese Pharmacopoeia"},{code:"EP",desc:"European Pharmacopoeia"},{code:"IH",desc:"In House Specification"},{code:"IP",desc:"Indian Specification"},{code:"JP",desc:"Japanese Pharmacopoeia"},{code:"PH_FR",desc:"French Pharmacopoeia"},{code:"PH.EUR",desc:"European Pharmacopoeia"}],polymorphicFormData:[{code:"FORM_XIV",desc:"FORM XIV"},{code:"FORM_Y",desc:"FORM Y"},{code:"FORM_B",desc:"FORM-B"},{code:"FORM_B1",desc:"FORM-B1"},{code:"FORM_D",desc:"FORM-D"},{code:"FORM_E",desc:"FORM-E"},{code:"FORM_VII",desc:"FORM VII"},{code:"FORM_VI",desc:"FORM VI"},{code:"FORM_V2",desc:"FORM V2"},{code:"FORM_V",desc:"FORM V"},{code:"FORM_RACEMIC",desc:"FORM RACEMIC"},{code:"FORM_POLYCRYSTALLINE",desc:"FORM POLYCRYSTALLINE"},{code:"FORM_P",desc:"FORM P"},{code:"FORM_F",desc:"FORM-F"},{code:"FORM_IV",desc:"FORM IV"}],stageFormData:[{code:"BL",desc:"BL - BLENDED"},{code:"CO",desc:"CO - COMPACTED"},{code:"GR",desc:"GR - GRANULATION"},{code:"MI",desc:"MI - MICRONIZED"},{code:"MM",desc:"MM - MULTI-MILLED"},{code:"PU",desc:"PU - PULVERIZED"},{code:"OTHER",desc:"Other - OTHER"}]},isOdataApiCalled:!1},r=R({name:"materialAllDropDown",initialState:m,reducers:{setDropDown:(c,e)=>{c.dropDown[e.payload.keyName]=e.payload.data},setDependentDropdown:(c,e)=>{c.dropDown[e.payload.keyName]||(c.dropDown[e.payload.keyName]={}),c.dropDown[e.payload.keyName][e.payload.keyName2]=e.payload.data},setOdataApiCall:(c,e)=>{c.isOdataApiCalled=e.payload}}}),{setDropDown:P,setDependentDropdown:u,setOdataApiCall:L}=r.actions;r.reducer;const N=()=>{const{customError:c}=E();return{fetchDataAndDispatch:(s,a,n="get",d={},l=!1)=>{const O=o=>{if(l?t.dispatch(F({keyName:a,data:o.body,keyName2:(d==null?void 0:d.plant)||`${d.salesOrg}-${d.distChnl}`||d.salesOrg})):t.dispatch(T({keyName:a,data:o.body})),a===I.VAL_CLASS&&s.includes("getValuationClass")){const i=s.match(/matlType=([^&]+)/),p=i?i[1]:null;t.dispatch(C({materialType:p,data:o.body}))}},M=o=>{c(o)};D(s,n.toLowerCase(),O,M,d)}}};export{P as s,N as u};
