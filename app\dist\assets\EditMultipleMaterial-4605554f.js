import{r as h,n as x,s as O,u as _,b7 as $,j as e,aZ as b,c as i,d as r,b3 as H,aa as W,a1 as Q,a2 as Z,a9 as q,F,O as o,aI as J,g as K,Q as U,a6 as X,a_ as Y,a$ as G,an as I,br as ee,B as C,b5 as te,b6 as ae,b1 as ie,bs as le,bt as ne,ah as oe,ag as re}from"./index-f7d9b065.js";import{d as de}from"./EditOutlined-a6f382b7.js";import{D as se}from"./DatePicker-a8e9bd4a.js";import"./useMobilePicker-9b56b5b6.js";import"./CSSTransition-cd337b47.js";function ce(d,s){return Array.isArray(s)&&s.find(u=>u.code===d)||""}const pe=({label:d,value:s,fieldGroup:g,units:u,onSave:D,isEditMode:S,isExtendMode:T,selectedRowData:k,options:E=[],type:c})=>{var z;const[n,f]=h.useState(s),[v,l]=h.useState(!1),p=x(t=>t.AllDropDown.dropDown),w=O(),N=ce(n,p);console.log("dropdownData",n),console.log("value e",s),console.log("label",d),console.log("units",u),console.log("transformedValue",N),_();const M=x(t=>t.initialData.MultipleMaterial),P=x(t=>t.edit.payload);x(t=>t.initialData.MultipleMaterial[0].Description);let j=-1;for(let t=0;t<M.length;t++)if(M[t].Description===k){M[t],j=t;break}console.log("editField",P),console.log("fieldData",{label:d,value:n,units:u,type:c});let y=d.replaceAll("(","").replaceAll(")","").replaceAll("/","").replaceAll("-","").replaceAll(".","").split(" ").join("");h.useEffect(()=>{f(s)},[s]);const A=(t,a)=>{w(J(M.map((m,L)=>{if(L==j){let R=m["Basic Data"],V=m["Basic Data"][g];return{...m,"Basic Data":{...R,[g]:V.map(B=>B.fieldName===t?{...B,value:a}:B)}}}else return m})))};return h.useEffect(()=>{console.log("lkey",y),console.log("data",s),w($({keyname:y.replaceAll("(","").replaceAll(")","").replaceAll("/","").replaceAll("-","").replaceAll(".","").split(" ").join(""),data:s||""}))},[]),console.log("editedValue[key] ",p[y]),console.log("editedValue[key] ",n),e(o,{item:!0,children:e(b,{children:S||T?i(F,{children:[e(r,{variant:"body2",color:"#777",children:d}),c==="Drop Down"?e(H,{options:p[y]??[],value:n&&((z=p[y])==null?void 0:z.filter(t=>t.code===n))||"",onChange:(t,a)=>{A(d,a.code),console.log("newValue",a),f(a.code),l(!0),console.log("keys",y)},getOptionLabel:t=>{var a,m;return console.log("optionoptionoption",t),t===""?"":`${t&&((a=t[0])==null?void 0:a.code)} - ${t&&((m=t[0])==null?void 0:m.desc)}`},renderOption:(t,a)=>(console.log("option vakue",a),e("li",{...t,children:e(r,{style:{fontSize:12},children:`${a==null?void 0:a.code} - ${a==null?void 0:a.desc}`})})),renderInput:t=>e(W,{...t,variant:"outlined",size:"small",label:null})}):c==="Input"?e(W,{variant:"outlined",size:"small",value:n,onChange:t=>{const a=t.target.value;A(d,a),f(a)}}):c==="Calendar"?e(Q,{dateAdapter:Z,children:e(se,{slotProps:{textField:{size:"small"}},placeholder:"Select Date Range"})}):c==="Radio Button"?e(q,{sx:{borderRadius:"0 !important"},checked:n,onChange:(t,a)=>{A(d,a),f(a)}}):""]}):e(F,{children:i(F,{children:[e(r,{variant:"body2",color:"#777",children:d}),i(r,{variant:"body2",fontWeight:"bold",children:[n," ",u]})]})})})})},ye=()=>{const d=K(),s=O();h.useState({});const[g,u]=h.useState(0),[D,S]=h.useState(!1),[T,k]=h.useState(!0),E=_();h.useState(!1),x(l=>l.initialData.EditMultipleMaterial);const c=x(l=>l.initialData.MultipleMaterial),n=E.state;x(l=>l.payload);const f=()=>{S(!0),ne(),k(!1)};for(let l=0;l<c.length;l++)if(c[l].Description===n.description){c[l];break}const v=c.filter(l=>l.Description===n.description)[0]["Basic Data"];return console.log(v,"lololol"),i("div",{children:[e(o,{container:!0,style:{...U,backgroundColor:"#FAFCFF"},children:i(o,{sx:{width:"inherit"},children:[i(o,{item:!0,md:12,style:{padding:"16px",display:"flex"},children:[e(o,{item:!0,style:{display:"flex",justifyContent:"flex-end"},children:e(X,{color:"primary","aria-label":"upload picture",component:"label",sx:Y,children:e(G,{style:{height:"1em",width:"1em",color:"#000000"},onClick:()=>{setTimeout(()=>{d(-1)},1e3),s(clearPayload()),s(clearOrgData())}})})}),i(o,{md:8,children:[e(r,{variant:"h3",children:i("strong",{children:["Multiple Material : ",n.description," "]})}),e(r,{variant:"body2",color:"#777",children:"This view displays details of uploaded material"})]}),D?"":e(o,{md:4,sx:{display:"flex",justifyContent:"flex-end"},children:e(o,{item:!0,children:i(I,{variant:"outlined",size:"small",sx:ee,onClick:f,children:["Change",e(de,{sx:{padding:"2px"},fontSize:"small"})]})})})]}),i(o,{container:!0,display:"flex",flexDirection:"row",flexWrap:"nowrap",children:[i(C,{width:"70%",sx:{marginLeft:"40px"},children:[e(o,{item:!0,sx:{paddingTop:"2px !important"},children:i(b,{flexDirection:"row",children:[e("div",{style:{width:"15%"},children:e(r,{variant:"body2",color:"#777",children:"Material"})}),i(r,{variant:"body2",fontWeight:"bold",justifyContent:"flex-start",children:[": ",n.material]})]})}),e(o,{item:!0,sx:{paddingTop:"2px !important"},children:i(b,{flexDirection:"row",children:[e("div",{style:{width:"15%"},children:e(r,{variant:"body2",color:"#777",children:"Material Type"})}),i(r,{variant:"body2",fontWeight:"bold",children:[": ",n.materialType]})]})}),e(o,{item:!0,sx:{paddingTop:"2px !important"},children:i(b,{flexDirection:"row",children:[e("div",{style:{width:"15%"},children:e(r,{variant:"body2",color:"#777",children:"Description"})}),i(r,{variant:"body2",fontWeight:"bold",children:[": ",n.description]})]})}),e(o,{item:!0,sx:{paddingTop:"2px !important"},children:i(b,{flexDirection:"row",children:[e("div",{style:{width:"15%"},children:e(r,{variant:"body2",color:"#777",children:"Industry Sector"})}),i(r,{variant:"body2",fontWeight:"bold",children:[": ",n.industrySector]})]})})]}),e(C,{width:"30%",sx:{marginLeft:"40px"},children:e(o,{item:!0,children:i(b,{flexDirection:"row",children:[e(r,{variant:"body2",color:"#777",style:{width:"30%"}}),e(r,{variant:"body2",fontWeight:"bold",sx:{width:"8%",textAlign:"center"}}),e(r,{variant:"body2",fontWeight:"bold",justifyContent:"flex-start"})]})})})]}),i(o,{container:!0,style:{padding:"16px"},children:[e(C,{sx:{borderBottom:1,borderColor:"divider"},children:e(te,{value:g,variant:"scrollable",sx:{background:"#FFF",borderBottom:"1px solid #BDBDBD",width:"100%"},"aria-label":"mui tabs example",children:e(ae,{sx:{fontSize:"12px",fontWeight:"700"},label:"Basic Data"},0)})}),e(o,{container:!0,item:!0,md:12,sx:{backgroundColor:"white",maxHeight:"max-content",height:"max-content",mt:1,mb:1},children:Object.keys(v).map(l=>i(o,{item:!0,md:12,sx:{backgroundColor:"white",maxHeight:"max-content",height:"max-content",borderRadius:"8px",border:"1px solid #E0E0E0",mt:.25,boxShadow:"0px 2px 14px 0px rgba(48, 38, 185, 0.10)",...ie},children:[e(r,{sx:{fontSize:"12px",fontWeight:"700",margin:"0px !important"},children:l}),e(C,{sx:{width:"100%"},children:e(le,{sx:{padding:"0",paddingBottom:"0 !important",paddingTop:"10px !important"},children:e(o,{container:!0,style:{display:"grid",gridTemplateColumns:"repeat(6,1fr)",gap:"15px"},justifyContent:"space-between",alignItems:"flex-start",md:12,children:v[l].map(p=>e(pe,{fieldGroup:l,selectedRowData:n.description,label:p.fieldName,value:p.value,onSave:w=>handleFieldSave(p.fieldName,w),isEditMode:D,type:p.fieldType,field:p}))})})})]},l))},v)]})]})}),D?e(re,{sx:{position:"fixed",bottom:0,left:0,right:0},elevation:2,children:e(oe,{className:"container_BottomNav",showLabels:!0,sx:{display:"flex",justifyContent:"flex-end",gap:1},value:g,onChange:l=>{u(l)},children:e(I,{size:"small",variant:"contained",onClick:()=>{d("/masterDataCockpit/materialMaster/massMaterialTable")},children:"Save"})})}):""]})};export{ye as default};
