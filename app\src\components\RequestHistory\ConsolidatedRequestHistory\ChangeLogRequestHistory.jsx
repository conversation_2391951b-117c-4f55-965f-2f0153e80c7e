import React, { useState, useEffect } from "react";
import {
  Box,
  Button,
  Typography,
  Modal,
  Grid,
  Stack,
  IconButton,
  Tooltip,
  <PERSON><PERSON>,
  Tab
} from "@mui/material";
import ChangeCircleOutlinedIcon from "@mui/icons-material/ChangeCircleOutlined";
import CloseIcon from "@mui/icons-material/Close";
import { v4 as uuidv4 } from "uuid";
import moment from "moment";

// Import your custom components (make sure paths are correct)
import { doAjax } from "../../common/fetchService";
import { destination_MaterialMgmt } from "../../../destinationVariables";
import ReusableDataTable from "../../Common/ReusableTable";
import ReusableIcon from "../../Common/ReusableIcon";
import { iconButton_SpacingSmall } from "../../Common/commonStyles";
import { saveExcelMultiSheets } from "../../../functions";
import { END_POINTS } from "../../../constant/apiEndPoints";
import useLogger from "../../../hooks/useLogger";
import { API_CODE, LOADING_MESSAGE } from "../../../constant/enum";
import ReusableBackDrop from "../../../components/Common/ReusableBackDrop";
import { CHANGE_LOG_TEMPS } from "@constant/changeLogTemplates";
import { useSelector } from "react-redux";

const ChangeLogRequestHistory = ({ open, closeModal, request }) => {
    const { customError } = useLogger();
    // Initialize loading as false instead of true
    const [loading, setLoading] = useState(false);
    const [apiResponse, setApiResponse] = useState(null);
    const templateKey = request?.template;
    const appSettings = useSelector((state) => state.appSettings);
    const [tabData, setTabData] = useState(() => {
        const tabConfig = CHANGE_LOG_TEMPS[templateKey] || {};
        return Object.keys(tabConfig).map((tabName) => ({
            label: tabName,
            columns: tabConfig[tabName],
            rows: [],
        }));
    });
    
    const [selectedTab, setSelectedTab] = useState(() => {
        if (tabData.length > 0) {
            return { number: 0, label: tabData[0].label };
        }
        return { number: 0, label: "" };
    });

    const handleTabChange = (event, newValue) => {
        setSelectedTab({ number: newValue, label: tabData[newValue].label });
    };

    const style = {
        position: "absolute",
        top: "50%",
        left: "52%",
        transform: "translate(-50%, -50%)",
        width: "80%",
        height: "auto",
        bgcolor: "#fff",
        boxShadow: 4,
        p: 2,
    };

    const onClose = () => {
        closeModal(false);
        // Reset states when closing
        setApiResponse(null);
        setLoading(false);
    };

    const changelogget = (request) => {
        setLoading(true); // Set loading to true only when API call starts
        const url = `/${destination_MaterialMgmt}/${END_POINTS?.CHG_DISPLAY_REQUESTOR.FETCH_CHANGELOG_DATA_FOR_MATERIAL}`;
        var changeLogPayload = {
            material:request?.materialNumber || "FERT2151",
            childRequestId: request?.childRequestId || "CMTE25080804514955002"
        };
        
        return new Promise((resolve, reject) => {
            const hSuccess = (data) => {
                if (data?.statusCode === API_CODE.STATUS_200 && data?.body?.length > 0) {
                    const result = mergeArrays(data?.body);
                    setLoading(false);
                    resolve(result);
                } else {
                    setLoading(false);
                    resolve([]);
                }
            };

            const hError = (error) => {
                setLoading(false);
                customError(error);
                reject(error);
            };

            doAjax(url, "post", hSuccess, hError, changeLogPayload);
        });
    };

    // Fixed useEffect - only fetch when modal is opened and we don't have data yet
    useEffect(() => {
        const fetchChangeLogData = async () => {
            // Only fetch if modal is open, we have a request, and we don't already have data
            if (open && request?.requestId && !apiResponse) {
                try {
                    const result = await changelogget(request);
                    setApiResponse(result);
                } catch (error) {
                    customError("Error fetching changelog data:", error);
                }
            }
        };
        
        fetchChangeLogData();
    }, [open, request?.requestId]); // Dependencies remain the same

    // Reset data when modal closes
    useEffect(() => {
        if (!open) {
            setApiResponse(null);
            setLoading(false);
        }
    }, [open]);

    useEffect(() => {
        if (apiResponse && selectedTab) {
            try {
                setTabData((prevTabData) =>
                    prevTabData?.map((tab) => {
                        const templateData = getObjectValue(TEMPLATE_NAME_MANIPULATION, templateKey);
                        const tabelName = typeof templateData === 'object' 
                            ? templateData[tab?.label]
                            : templateData;
                        const extraField = getObjectValue(CHANGE_TEMPLATES_FIELD_IDENTIFICATION, tabelName);
                        const newRows = extractDataBaedOnTemplateName(apiResponse[tabelName], tabelName);
                        return {
                            ...tab,
                            rows: newRows?.map((row) => ({
                                id: uuidv4(),
                                ...row,
                                Material: getSegregatedPart(row?.ObjectNo, 1),
                                SAPValue: formatDateValue(row?.SAPValue,appSettings), 
                                PreviousValue: formatDateValue(row?.PreviousValue,appSettings),
                                CurrentValue: formatDateValue(row?.CurrentValue,appSettings),
                                ChangedOn: formatDateValue(row?.ChangedOn,appSettings),
                                ...(extraField?.length > 0 && {
                                    [extraField[0]]: getSegregatedPart(row?.ObjectNo, 2),
                                }),
                                ...(extraField?.length > 1 && {
                                    [extraField[1]]: getSegregatedPart(row?.ObjectNo, 3),
                                }),
                            })),
                        };
                    })
                );
            } catch (error) {
                customError(ERROR_MESSAGES.CHANGE_LOG_MESSAGE, error);
            }
        }
    }, [apiResponse]);

    const presentDate = new Date();

    const functions_ExportAsExcel = {
        convertJsonToExcel: () => {
            const sheetsData = tabData.map((tab) => {
                const columns = tab.columns.fieldName.map((field, idx) => ({
                    header: tab.columns.headerName[idx],
                    key: field
                }));
                return {
                    sheetName: tab.label,
                    fileName: `Changelog Data-${moment(presentDate).format("DD-MMM-YYYY")}`,
                    columns: columns,
                    rows: tab.rows
                };
            });
            saveExcelMultiSheets(sheetsData);
        },
    };

    return (
        <>
            {/* Only show loader when modal is open and loading is true */}
            {open && loading && (
                <ReusableBackDrop 
                    blurLoading={loading} 
                    loaderMessage={LOADING_MESSAGE.CHANGELOG_LOADING}
                />
            )}
            <Modal
                open={open}
                onClose={onClose}
                aria-labelledby="modal-modal-title"
                aria-describedby="modal-modal-description"
            >
                <Box sx={style}>
                    <Stack>
                        <Grid
                            item
                            md={12}
                            sx={{ display: "flex", alignItems: "center", justifyContent: "space-between" }}
                        >
                            <Box sx={{ display: "flex", alignItems: "center", gap: "8px" }}>
                                <ChangeCircleOutlinedIcon
                                    sx={{
                                        color: "black",
                                        fontSize: "20px",
                                        "&:hover": {
                                            transform: "rotate(360deg)",
                                            transition: "0.9s",
                                        },
                                        textAlign: "center",
                                        marginTop: "4px",
                                    }}
                                />
                                <Typography
                                    id="modal-modal-title"
                                    variant="subtitle1"
                                    fontSize={"16px"}
                                    fontWeight={"bold"}
                                    sx={{ color: "black" }}
                                >
                                    Change Log
                                </Typography>
                            </Box>

                            <Box sx={{ display: "flex", alignItems: "center", gap: "8px" }}>
                                <Tooltip title="Export Table">
                                    <IconButton
                                        sx={iconButton_SpacingSmall}
                                        onClick={functions_ExportAsExcel.convertJsonToExcel}
                                    >
                                        <ReusableIcon iconName={"IosShare"} />
                                    </IconButton>
                                </Tooltip>

                                <IconButton sx={{ padding: "0 0 0 5px" }} onClick={onClose}>
                                    <CloseIcon />
                                </IconButton>
                            </Box>
                        </Grid>
                    </Stack>

                    <Tabs
                        value={selectedTab?.number}
                        onChange={handleTabChange}
                        variant="scrollable"
                        scrollButtons="auto"
                        aria-label="modal tabs"
                    >
                        {tabData?.map((tab, index) => (
                            <Tab key={index} label={tab.label} />
                        ))}
                    </Tabs>

                    <div
                        className="tab-content"
                        style={{ position: "relative", height: "100%", marginTop: 16 }}
                    >
                        {tabData?.map((tab, index) => {
                            return (
                                selectedTab?.number === index && (
                                    <Typography
                                        key={index}
                                        id={`modal-tab-content-${index}`}
                                        sx={{ mt: 1 }}
                                    >
                                        <Grid item sx={{ position: "relative" }}>
                                            <Stack>
                                                <ReusableDataTable
                                                    rows={tab?.rows}
                                                    columns={tab?.columns?.fieldName?.map((col, index) => ({
                                                        field: col,
                                                        headerName: tab?.columns?.headerName[index],
                                                        flex: 1,
                                                        minWidth: 100,
                                                    }))}
                                                    getRowIdValue={"id"}
                                                    pageSize={tab?.columns?.fieldName?.length}
                                                    autoHeight
                                                    scrollbarSize={10}
                                                    sx={{
                                                        "& .MuiDataGrid-row:hover": {
                                                            backgroundColor: "#EAE9FF40",
                                                        },
                                                        backgroundColor: "#fff",
                                                    }}
                                                />
                                            </Stack>
                                        </Grid>
                                    </Typography>
                                )
                            )}
                        )}
                    </div>
                </Box>
            </Modal>
        </>
    );
};

export default ChangeLogRequestHistory;
