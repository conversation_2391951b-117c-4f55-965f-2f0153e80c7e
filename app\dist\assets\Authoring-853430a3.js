import{c9 as I,ca as A,cb as B,g as j,r as h,j as e,bI as m,bK as L,B as y,n as C,a as O,C as U,cq as D,N as P,c,O as W,bC as q,d as R,bl as $,ag as H,bm as G,bn as V,bo as M,bp as o,bq as z,aZ as K}from"./index-f7d9b065.js";import{i as Z}from"./index-c3f8f9be.js";import"./react-beautiful-dnd.esm-6b676f13.js";import"./redux-dc18bb29.js";import"./index-6a70352f.js";import"./index-0aa14859.js";import"./Check-87f6ada7.js";import"./FileUploadOutlined-4a68a28a.js";import"./DeleteOutline-584dc929.js";import"./Delete-5278579a.js";import"./asyncToGenerator-88583e02.js";import"./FileDownloadOutlined-59854a55.js";import"./AddOutlined-9a9caebd.js";import"./DeleteOutlineOutlined-8fd07dc7.js";import"./EditOutlined-a6f382b7.js";import"./Edit-51c94b76.js";import"./index-67deb11b.js";import"./index-ae6cbb07.js";import"./DataObject-52409c14.js";import"./lz-string-0665f106.js";import"./VisibilityOutlined-b6cd6d28.js";import"./Remove-82c67208.js";import"./ChevronRight-a85c6b03.js";import"./index-7ffbe79f.js";import"./DeleteOutlined-e668453f.js";import"./index-6362276a.js";import"./History-13dab512.js";var x={},w=A;Object.defineProperty(x,"__esModule",{value:!0});var F=x.default=void 0,J=w(I()),Q=B;F=x.default=(0,J.default)((0,Q.jsx)("path",{d:"M3 17.25V21h3.75L17.81 9.94l-3.75-3.75zM20.71 5.63l-2.34-2.34a.9959.9959 0 0 0-1.41 0l-1.83 1.83 3.75 3.75 1.83-1.83c.39-.39.39-1.02 0-1.41"}),"ModeEditOutline");const X=t=>{var u;const l=j(),g=[{Description:"",Name:"WorkRulesServices",URL:m},{Description:"",Name:"CW_Worktext",URL:m},{Description:"",Name:"WorkRuleEngineServices",URL:m},{Description:"",Name:"WorkUtilsServices",URL:m}],p=()=>{var d,r;({...t.DTdetails},t.setShowDT(!1)),l((r=(d=L)==null?void 0:d.BUSINESS_RULES)==null?void 0:r.AUTHORING)},b={headerColors_C:"#DFEAFB",headerColors_A:"#FFF7E2",headerColors_P:"#EEEEEE",headerColors_S:"#EEEEEE",textColor:"rgb(66, 66, 66)",hoveredTextColor:"rgb(25, 118, 210)",fontFamily:"inherit"},f=h.useMemo(()=>e(Z.Authoring,{colors:b,translationDataObjects:[],Dtdetails:t==null?void 0:t.DTdetails,destinations:g,NavBackHandler:p,dateFormat:"dd-MMM-yyyy",disableExistingRows:!1}),[(u=t.DTdetails)==null?void 0:u.DTid]);return e(y,{children:f})},Y=X;function ye(){const[t,l]=h.useState({}),[g,p]=h.useState(!1),[b,f]=h.useState([]),u="",d="baaf87db-3f78-44c8-8c78-32bec835f6ff",r=C(i=>i==null?void 0:i.userManagement),n=r==null?void 0:r.userData,s=C(i=>i.appSettings),{t:a}=O(),E=s==null?void 0:s.dateFormat,T=s==null?void 0:s.timeFormat;h.useEffect(()=>{U(m+`/v1/application/hierarchy?app=${d}&isAuthoring=true&mode=DT`,"get",N,_)},[]);const N=i=>{f(i.data[0].childs[0].childs);let S=i.data[0],k=S.childs[0];l({...t,RMSid:S.id,RSid:k.id})};let _=i=>{console.log("Error Fetching Filter Data in this API",i)};const v=i=>{l({userDetails:{displayName:n==null?void 0:n.displayName,emailId:n==null?void 0:n.emailId},RMSid:t==null?void 0:t.RMSid,RSid:t==null?void 0:t.RSid,DTid:i.id,applicationId:d,ruleName:i.name,version:i.version,token:u.replace("Bearer ","")}),p(!0)};return console.log(D(1702891324188).format(`${E},${T}`)),e("div",{style:{...P,backgroundColor:"#FAFCFF"},children:c(K,{spacing:1,children:[c(W,{item:!0,md:5,sx:q,children:[e(R,{variant:"h3",children:e("strong",{children:a("Authoring")})}),e(R,{variant:"body2",color:"#777",children:a("This view displays the list of rules set for the application")})]}),e(y,{className:"content",sx:{margin:"25px"},children:g?t&&e(Y,{DTdetails:t,setDTdetails:l,setShowDT:p}):e($,{component:H,sx:{boxShadow:"none",border:"1px solid #e0e0e0"},children:c(G,{sx:{minWidth:650},"aria-label":"simple table",children:[e(V,{sx:{background:"#f5f5f5"},children:c(M,{sx:{"& .MuiTableCell-root":{height:"52px",padding:"0px 16px",fontWeight:600}},children:[e(o,{children:a("Decision Table Name")}),e(o,{align:"right",children:a("Version")}),e(o,{align:"right",children:a("Modified By")}),e(o,{align:"right",children:a("Modified On")}),e(o,{align:"right",children:a("Status")}),e(o,{align:"right",children:a("Action")})]})}),e(z,{children:b.map(i=>c(M,{sx:{"&:last-child td, &:last-child th":{border:0},cursor:"pointer","& .MuiTableCell-root":{borderBottom:"1px solid #e0e0e0 !important",height:"52px",padding:"0px 16px"}},onClick:()=>v(i),children:[e(o,{component:"th",scope:"row",children:i.name}),e(o,{align:"right",children:i.version}),e(o,{align:"right",children:i.updatedBy}),e(o,{align:"right",children:D(i.updatedOn).format(`${E},${T}`)}),e(o,{align:"right",children:i.status}),e(o,{align:"right",children:e(F,{onClick:()=>{v(i)}})})]},i.id))})]})})})]})})}export{ye as default};
