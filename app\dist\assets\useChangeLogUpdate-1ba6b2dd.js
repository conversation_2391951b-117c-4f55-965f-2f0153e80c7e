import{s as M,n as r,d$ as b,e0 as v,e1 as D,e2 as I,e3 as d,e4 as N,e5 as S,e6 as U,e7 as m,e8 as $,e9 as j,ea as q}from"./index-f7d9b065.js";const B=()=>{const l=M(),s=r(e=>e.userManagement.userData),t=r(e=>e.changeLog.createPayloadCopyForChangeLog||[]),C=r(e=>e.changeLog.createTemplateArray);return{updateChangeLog:({materialID:e,viewName:o,plantData:u,fieldName:n,jsonName:g,currentValue:h,requestId:i,childRequestId:p,isDescriptionData:A=!1,isUnitOfMeasure:L=!1,isAdditionalEAN:y=!1,uomId:P=null,eanId:V=null,language:E})=>{let a;A?a=b(e,g,t,E):L?a=v(e,P,n,t):y?a=D(e,V,n,t):a=I(e,o,u,g,t);const F=d(N,o),T=S(u,F),f=d(U,o),c={ObjectNo:`${e}${T}`,ChangedBy:s==null?void 0:s.emailId,ChangedOn:m,FieldName:n,PreviousValue:a,CurrentValue:h,SAPValue:a,tableName:f};l($(c));const O=[...C,c],R=j(O);let _={RequestId:i,changeLogId:null,ChildRequestId:p,...R};l(q(_))}}};export{B as u};
