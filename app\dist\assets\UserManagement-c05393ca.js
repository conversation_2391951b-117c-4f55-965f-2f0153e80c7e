import{a as T,c as o,j as r,i5 as C,gd as B,d as n,B as i,ch as j,Z as e,dy as S,bE as R,T as E,i6 as W,a6 as A,i7 as K,aZ as U,bS as H,hZ as $,aW as V,i8 as Y,ae as N,r as f,aM as M,O as J,F as Z,aY as q,a_ as k,R as X,gD as Q,aH as ee,c2 as re,an as ae}from"./index-f7d9b065.js";const te=({users:s,selectedUser:p,onUserSelect:x,loading:d})=>{const{t:a}=T(),t=l=>{const c=l.value||[];return c.length===0?"No data":o(i,{sx:{display:"flex",gap:.5,alignItems:"center"},children:[r(R,{label:c[0],size:"small",sx:{bgcolor:e.primary.light,color:e.primary.dark}}),c.length>1&&r(E,{title:c.slice(1).join(", "),arrow:!0,placement:"top",children:r(R,{label:`+${c.length-1}`,size:"small",sx:{bgcolor:e.secondary.light,color:e.secondary.dark}})})]})},m=[{field:"displayName",headerName:a("User Name"),flex:1,sortable:!0,renderCell:l=>o(i,{sx:{display:"flex",alignItems:"center",gap:1},children:[r(B,{sx:{bgcolor:C(l.value||l.row.email),width:32,height:32},children:(l.value||l.row.email).charAt(0).toUpperCase()}),r(n,{variant:"body1",children:l.value||"-"})]})},{field:"email",headerName:a("Email"),flex:1.5,sortable:!0},{field:"userRoles",headerName:a("Roles"),flex:1,sortable:!1,renderCell:t},{field:"userGroups",headerName:a("User Groups"),flex:1,sortable:!1,renderCell:t}];return o(S,{sx:{width:"100%",height:"100%",display:"flex",flexDirection:"column",overflow:"hidden"},children:[r(i,{sx:{display:"flex",justifyContent:"space-between",alignItems:"center",px:2.5,py:1.5,borderBottom:"1px solid #ddd"},children:o(n,{variant:"subtitle1",children:[a("Users")," (",s.length,")"]})}),r(i,{sx:{flex:1,overflow:"auto"},children:r(j,{rows:s,columns:m,getRowId:l=>l.email,autoHeight:!0,pagination:!0,disableSelectionOnClick:!0,loading:d,onRowClick:l=>x(l.row),sx:{"& .MuiDataGrid-columnHeaders":{backgroundColor:e.primary.light,fontWeight:"bold"},"& .MuiDataGrid-row:hover":{backgroundColor:e.primary.veryLight},"& .MuiDataGrid-row.Mui-selected":{backgroundColor:e.primary.veryLight}}})})]})},ie=({user:s,rolesData:p,onClose:x})=>{if(!s)return null;const{t:d}=T(),a={card:{width:"100%",height:"100%",display:"flex",flexDirection:"column",overflow:"hidden",boxShadow:e.shadow.light,border:`1px solid ${e.border.light}`,borderRadius:1.5,bgcolor:e.background.card},header:{py:1.5,px:2.5,borderBottom:`1px solid ${e.border.light}`,display:"flex",alignItems:"center",flexShrink:0,bgcolor:e.primary.white},backButton:{mr:1.5,color:e.primary.grey,"&:hover":{bgcolor:e.hover.light}},headerTitle:{fontWeight:600,color:e.text.primary},profileSection:{px:3,pt:3,pb:2.5,backgroundImage:`linear-gradient(to right, ${e.primary.light}, ${e.primary.veryLight})`,borderBottom:`1px solid ${e.border.light}`,flexShrink:0},userInfo:{display:"flex",alignItems:"center",gap:2.5},badgeIndicator:{width:12,height:12,borderRadius:"50%",bgcolor:e.success.vibrant,border:`2px solid ${e.primary.white}`},avatar:t=>({width:64,height:64,bgcolor:t,boxShadow:e.shadow.light,color:e.primary.white,fontWeight:500,border:`2px solid ${e.primary.white}`,fontSize:"1.5rem"}),userName:{fontWeight:600,mb:.75,lineHeight:1.2,color:e.text.primary},userEmail:{color:e.text.secondary,fontSize:"0.875rem",display:"flex",alignItems:"center",gap:.75,flexWrap:"wrap"},roleChip:t=>({ml:.5,height:20,borderRadius:"4px",bgcolor:`${t}15`,color:t,border:`1px solid ${t}30`,"& .MuiChip-label":{px:1,py:0}}),statsContainer:{mt:2.5,display:"flex",gap:4,justifyContent:"flex-start",pl:1},statBox:{textAlign:"center"},statValue:{fontWeight:600,fontSize:"1.125rem",color:e.primary.main},statLabel:{color:e.text.secondary,fontSize:"0.75rem"},tabsSection:{borderBottom:`1px solid ${e.border.light}`,flexShrink:0,bgcolor:e.background.panel},activeTab:{py:1.25,px:2,borderBottom:`2px solid ${e.primary.main}`,color:e.primary.main},tabText:{fontWeight:600},contentSection:{px:3,py:2.5,overflow:"auto",flexGrow:1,bgcolor:e.background.default},sectionTitle:{mb:1.5,fontWeight:600,color:e.text.secondary,fontSize:"0.75rem",textTransform:"uppercase",letterSpacing:"0.05em"},roleChips:{direction:"row",spacing:.75,flexWrap:"wrap",useFlexGap:!0},roleChipItem:t=>({m:.25,borderRadius:"4px",height:24,fontSize:"0.75rem",bgcolor:`${t}10`,color:t,border:`1px solid ${t}30`,transition:"all 0.2s ease","&:hover":{bgcolor:`${t}15`,boxShadow:e.shadow.light},"& .MuiChip-label":{px:1.5}}),noRoles:{p:2,textAlign:"center",bgcolor:e.neutral[100],borderRadius:1,width:"100%",border:`1px dashed ${e.border.light}`},noRolesText:{color:e.text.disabled},moduleCard:t=>({mb:2,borderRadius:1,border:`1px solid ${e.border.light}`,overflow:"hidden",transition:"all 0.2s ease-in-out","&:hover":{borderColor:t,boxShadow:e.shadow.medium}}),moduleHeader:t=>({px:2,py:1.25,bgcolor:`${t}08`,borderBottom:`1px solid ${t}20`,display:"flex",alignItems:"center",justifyContent:"space-between"}),moduleName:t=>({color:t,fontWeight:600,fontSize:"0.875rem"}),countChip:t=>({height:20,fontSize:"0.7rem",borderRadius:"4px",bgcolor:`${t}15`,color:t,border:`1px solid ${t}30`,"& .MuiChip-label":{px:1,py:0}}),featureChips:{p:1.75},featureChip:t=>({m:.25,height:24,borderRadius:"4px",fontSize:"0.75rem",bgcolor:e.neutral[100],color:e.text.primary,border:`1px solid ${e.border.light}`,transition:"all 0.2s ease","&:hover":{bgcolor:`${t}10`,borderColor:`${t}30`,color:t,boxShadow:e.shadow.light},"& .MuiChip-label":{px:1.5}}),loadingContainer:{py:4,textAlign:"center"},loadingProgress:{width:"60%",mx:"auto",mb:2,borderRadius:1,"& .MuiLinearProgress-bar":{bgcolor:e.primary.main}},loadingText:{color:e.text.secondary},noPermissions:{p:4,textAlign:"center",bgcolor:e.neutral[100],borderRadius:1,border:`1px dashed ${e.border.light}`},infoIconContainer:{width:48,height:48,borderRadius:"50%",bgcolor:e.neutral[200],display:"flex",alignItems:"center",justifyContent:"center",margin:"0 auto",mb:2},infoIcon:{color:e.text.disabled},noPermissionsText:{color:e.text.secondary,fontWeight:500},divider:{my:3,bgcolor:e.border.light}};return o(S,{sx:a.card,children:[o(i,{sx:a.header,children:[r(A,{size:"small",onClick:x,sx:a.backButton,children:r(W,{fontSize:"small"})}),r(n,{variant:"subtitle1",sx:a.headerTitle,children:d("User Details")})]}),o(i,{sx:a.profileSection,children:[o(i,{sx:a.userInfo,children:[r(K,{overlap:"circular",anchorOrigin:{vertical:"bottom",horizontal:"right"},badgeContent:r(i,{sx:a.badgeIndicator}),children:r(B,{sx:a.avatar(C(s.displayName||s.email)),children:(s.displayName||s.email).charAt(0).toUpperCase()})}),o(i,{children:[r(n,{variant:"h6",sx:a.userName,children:s.displayName||"N/A"}),o(n,{sx:a.userEmail,children:[s.email,s.userRoles.length>0&&r(R,{label:s.userRoles[0],size:"small",sx:a.roleChip(C(s.userRoles[0]))})]})]})]}),o(i,{sx:a.statsContainer,children:[o(i,{sx:a.statBox,children:[r(n,{variant:"h6",sx:a.statValue,children:s.userRoles.length}),r(n,{variant:"caption",sx:a.statLabel,children:d("Roles")})]}),o(i,{sx:a.statBox,children:[r(n,{variant:"h6",sx:a.statValue,children:s.userGroups.length}),r(n,{variant:"caption",sx:a.statLabel,children:d("Groups")})]}),o(i,{sx:a.statBox,children:[r(n,{variant:"h6",sx:a.statValue,children:p?Object.keys(p.entitiesAndActivities[0]||{}).length:"-"}),r(n,{variant:"caption",sx:a.statLabel,children:d("Modules")})]})]})]}),r(i,{sx:a.tabsSection,children:r(i,{sx:{display:"flex",px:1.5},children:r(i,{sx:a.activeTab,children:r(n,{variant:"body2",sx:a.tabText,children:d("Permissions")})})})}),o(i,{sx:a.contentSection,children:[o(i,{sx:{mb:3},children:[r(n,{variant:"subtitle2",sx:a.sectionTitle,children:d("Assigned Roles")}),r(U,{direction:"row",spacing:.75,flexWrap:"wrap",useFlexGap:!0,children:s.userRoles.length?s.userRoles.map(t=>{const m=C(t);return r(R,{label:t,sx:a.roleChipItem(m)},t)}):r(i,{sx:a.noRoles,children:r(n,{variant:"body2",sx:a.noRolesText,children:d("No roles assigned")})})})]}),r(H,{sx:a.divider}),r(i,{sx:{mb:2},children:r(n,{variant:"subtitle2",sx:a.sectionTitle,children:d("Modules & Features")})}),p?p.entitiesAndActivities.length===0?o(i,{sx:a.noPermissions,children:[r(i,{sx:a.infoIconContainer,children:r(Y,{sx:a.infoIcon})}),r(n,{variant:"body2",sx:a.noPermissionsText,children:d(N.ERROR_PERM)})]}):r(i,{children:p.entitiesAndActivities.map((t,m)=>Object.keys(t).map(l=>{const c=C(l);return o(S,{variant:"outlined",sx:a.moduleCard(c),children:[o(i,{sx:a.moduleHeader(c),children:[r(n,{sx:a.moduleName(c),children:l}),r(R,{label:`${t[l].length} features`,size:"small",sx:a.countChip(c)})]}),r(i,{sx:a.featureChips,children:r(U,{direction:"row",spacing:.5,flexWrap:"wrap",useFlexGap:!0,children:t[l].map((G,y)=>r(R,{label:G,size:"small",sx:a.featureChip(c)},y))})})]},`${l}-${m}`)}))}):o(i,{sx:a.loadingContainer,children:[r(i,{sx:a.loadingProgress,children:r($,{color:"inherit"})}),r(n,{variant:"body2",sx:a.loadingText,children:d(V.LOADING_PERM)})]})]})]})},I=(s,p=[e.primary.main,e.primary.dark,e.info.dark,e.secondary.dark,e.blue.main,e.icon.oranges])=>{const x=s.split("").reduce((d,a)=>a.charCodeAt(0)+d,0);return p[x%p.length]},se=()=>{const[s,p]=f.useState([]),[x,d]=f.useState(null),[a,t]=f.useState(null),[m,l]=f.useState(!0),{t:c}=T();f.useEffect(()=>{y()},[]);let G={"<EMAIL>":{userRoles:["CA-MDG-GNRL-ACCESS"],userGroups:["General Access Group"],userName:"tripathya",displayName:"Abhishek Tripathy",userEmail:"<EMAIL>"},"<EMAIL>":{userRoles:["CA-MDG-SUPPLY-EUR","CA-MDG-MRKTNG-SALES-EUR","CA-MDG-MD-TEAM-US","CA-MDG-FINANCE-US","CA-MDG-MRKTNG-FERT-EUR","CA-MDG-MRKTNG-BOM"],userGroups:["Master Data Team US Group","Finance US Group","Supply EUR Group","Marketing BOM Group","Marketing Sales EUR Group","Marketing FERT EUR Group"],userName:"test",displayName:" test",userEmail:"<EMAIL>"},"<EMAIL>":{userRoles:["CA-MDG-SUPPLY-EUR","CA-MDG-MRKTNG-SALES-EUR","CA-MDG-IMPORT-US","CA-MDG-MD-TEAM-US","CA-MDG-MD-TEAM-EUR","CA-MDG-FINANCE-US","CA-MDG-MRKTNG-US","CA-MDG-MRKTNG-FERT-EUR","CA-MDG-GNRL-ACCESS","CA-MDG-FINANCE-EUR","CA-MDG-MRKTNG-BOM"],userGroups:["Import US Group","Master Data Team US Group","Finance US Group","Marketing US Group","General Access Group","Supply EUR Group","Finance EUR Group","Marketing BOM Group","Marketing Sales EUR Group","Marketing FERT EUR Group","Master Data Team EUR Group"],userName:"kumarburraa",displayName:"Anil KumarBurra",userEmail:"<EMAIL>"},"<EMAIL>":{userRoles:["CA-MDG-SUPPLY-EUR","CA-MDG-MRKTNG-SALES-EUR","CA-MDG-IMPORT-US","CA-MDG-MD-TEAM-US","CA-MDG-MD-TEAM-EUR","CA-MDG-FINANCE-US","CA-MDG-MRKTNG-US","CA-MDG-MRKTNG-FERT-EUR","CA-MDG-GNRL-ACCESS","CA-MDG-FINANCE-EUR","CA-MDG-MRKTNG-BOM"],userGroups:["Import US Group","Master Data Team US Group","Finance US Group","Marketing US Group","General Access Group","Supply EUR Group","Finance EUR Group","Marketing BOM Group","Marketing Sales EUR Group","Marketing FERT EUR Group","Master Data Team EUR Group"],userName:"manasS",displayName:"Manas Sahoo",userEmail:"<EMAIL>"},"<EMAIL>":{userRoles:["CA-MDG-MRKTNG-SALES-EUR","CA-MDG-MRKTNG-FERT-EUR"],userGroups:["Marketing Sales EUR Group","Marketing FERT EUR Group"],userName:"prajapatis",displayName:"Sunil Prajapati",userEmail:"<EMAIL>"}};const y=async()=>{p(Object.keys(G).map(h=>({email:h,...G[h]}))),M(q.USER_FETCHED,"success"),l(!1)},L={statusCode:200,message:"fetched successfully",body:{id:0,roles:["CA-MDG-GNRL-ACCESS"],entitiesAndActivities:[{"Request Bench":["Request Bench"],Material:["Change with Upload","Extend with Upload","Extend","Change"],"Master Data":["Material"],Workspace:["Completed Tasks","My Tasks"]}],applications:[1]},count:null},F=h=>{d(L.body)},w={convertJsonToExcel:(h,b)=>{var D;if(!h||h.length===0){M((D=N)==null?void 0:D.ERROR_EXPORT,"warning");return}let v=[];b.forEach(u=>{u.headerName.toLowerCase()!=="action"&&!u.hide&&v.push({header:u.headerName,key:u.field})});const _=h.map(u=>({displayName:u.displayName||"-",email:u.email,userRoles:u.userRoles.length>0?u.userRoles.join(", "):"",userGroups:u.userGroups.length>0?u.userGroups.join(", "):""}));re({fileName:"User Management Data",columns:v,rows:_})},button:(h,b)=>r(ae,{sx:{textTransform:"capitalize",position:"absolute",right:0,top:0},onClick:()=>w.convertJsonToExcel(h,b),children:"Download"})},P=()=>{var b;if(s.length===0){M((b=N)==null?void 0:b.ERROR_EXPORT,"warning");return}const h=[{field:"displayName",headerName:"User Name"},{field:"email",headerName:"Email"},{field:"userRoles",headerName:"Roles"},{field:"userGroups",headerName:"User Groups"}];w.convertJsonToExcel(s,h)},O=h=>{t(h),F()},z=()=>{t(null),d(null)},g={container:{height:"100vh",display:"flex",flexDirection:"column",bgcolor:e.primary.veryLight,overflow:"hidden"},header:{py:1.5,px:3,bgcolor:e.primary.white,borderBottom:`1px solid ${e.basic.grey}`,display:"flex",justifyContent:"space-between",alignItems:"center",flexShrink:0,zIndex:10},headerTitle:{fontWeight:600,fontSize:"1.125rem",color:e.primary.grey},progressBar:{height:2,flexShrink:0,"& .MuiLinearProgress-bar":{bgcolor:e.primary.main}},contentContainer:{display:"flex",flex:1,p:2.5,gap:2.5,overflow:"hidden",height:"100%"},listContainer:{transition:"width 0.3s ease-in-out",height:"100%"},detailsContainer:{width:"42%",height:"100%",transition:"all 0.3s ease"}};return o(Z,{children:[o(i,{sx:g.container,children:[o(J,{sx:g.header,children:[r(n,{variant:"h6",sx:g.headerTitle,children:c("User Management")}),o(U,{direction:"row",spacing:1,children:[r(E,{title:"Export Table",children:r(A,{sx:k,onClick:P,children:r(X,{iconName:"IosShare"})})}),r(E,{title:"Reload",children:r(A,{sx:k,children:r(Q,{sx:{"&:hover":{transform:"rotate(360deg)",transition:"0.9s"}},onClick:y})})})]})]}),m&&r($,{sx:g.progressBar}),o(i,{sx:g.contentContainer,children:[r(i,{sx:{...g.listContainer,width:a?"58%":"100%"},children:r(te,{users:s,selectedUser:a,onUserSelect:O,loading:m,colors:e,getColorFromString:I})}),a&&r(i,{sx:g.detailsContainer,children:r(ie,{user:a,rolesData:x,onClose:z,colors:e,getColorFromString:I})})]})]}),r(ee,{})]})};export{se as default};
