import{r as Pe,fn as Ie,m as Ue}from"./index-f7d9b065.js";var we={exports:{}},A={};/**
 * @license React
 * react-dom-test-utils.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var I=Pe,ie=Ie;function Ee(e){var t=e,r=e;if(e.alternate)for(;t.return;)t=t.return;else{e=t;do t=e,t.flags&4098&&(r=t.return),e=t.return;while(e)}return t.tag===3?r:null}function pe(e){if(Ee(e)!==e)throw Error("Unable to find node on an unmounted component.")}function je(e){var t=e.alternate;if(!t){if(t=Ee(e),t===null)throw Error("Unable to find node on an unmounted component.");return t!==e?null:e}for(var r=e,i=t;;){var d=r.return;if(d===null)break;var m=d.alternate;if(m===null){if(i=d.return,i!==null){r=i;continue}break}if(d.child===m.child){for(m=d.child;m;){if(m===r)return pe(d),e;if(m===i)return pe(d),t;m=m.sibling}throw Error("Unable to find node on an unmounted component.")}if(r.return!==i.return)r=d,i=m;else{for(var h=!1,p=d.child;p;){if(p===r){h=!0,r=d,i=m;break}if(p===i){h=!0,i=d,r=m;break}p=p.sibling}if(!h){for(p=m.child;p;){if(p===r){h=!0,r=m,i=d;break}if(p===i){h=!0,i=m,r=d;break}p=p.sibling}if(!h)throw Error("Child was not found in either parent set. This indicates a bug in React related to the return pointer. Please file an issue.")}}if(r.alternate!==i)throw Error("Return fibers should always be each others' alternates. This error is likely caused by a bug in React. Please file an issue.")}if(r.tag!==3)throw Error("Unable to find node on an unmounted component.");return r.stateNode.current===r?e:t}var x=Object.assign;function J(e){var t=e.keyCode;return"charCode"in e?(e=e.charCode,e===0&&t===13&&(e=13)):e=t,e===10&&(e=13),32<=e||e===13?e:0}function z(){return!0}function he(){return!1}function M(e){function t(r,i,d,m,h){this._reactName=r,this._targetInst=d,this.type=i,this.nativeEvent=m,this.target=h,this.currentTarget=null;for(var p in e)e.hasOwnProperty(p)&&(r=e[p],this[p]=r?r(m):m[p]);return this.isDefaultPrevented=(m.defaultPrevented!=null?m.defaultPrevented:m.returnValue===!1)?z:he,this.isPropagationStopped=he,this}return x(t.prototype,{preventDefault:function(){this.defaultPrevented=!0;var r=this.nativeEvent;r&&(r.preventDefault?r.preventDefault():typeof r.returnValue!="unknown"&&(r.returnValue=!1),this.isDefaultPrevented=z)},stopPropagation:function(){var r=this.nativeEvent;r&&(r.stopPropagation?r.stopPropagation():typeof r.cancelBubble!="unknown"&&(r.cancelBubble=!0),this.isPropagationStopped=z)},persist:function(){},isPersistent:z}),t}var U={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},Ke=M(U),X=x({},U,{view:0,detail:0});M(X);var ee,te,K,Z=x({},X,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:ae,button:0,buttons:0,relatedTarget:function(e){return e.relatedTarget===void 0?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return"movementX"in e?e.movementX:(e!==K&&(K&&e.type==="mousemove"?(ee=e.screenX-K.screenX,te=e.screenY-K.screenY):te=ee=0,K=e),ee)},movementY:function(e){return"movementY"in e?e.movementY:te}});M(Z);var We=x({},Z,{dataTransfer:0});M(We);var Be=x({},X,{relatedTarget:0});M(Be);var Xe=x({},U,{animationName:0,elapsedTime:0,pseudoElement:0});M(Xe);var Ye=x({},U,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}});M(Ye);var ze=x({},U,{data:0});M(ze);var He={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},qe={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},Ve={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function Ze(e){var t=this.nativeEvent;return t.getModifierState?t.getModifierState(e):(e=Ve[e])?!!t[e]:!1}function ae(){return Ze}var Ge=x({},X,{key:function(e){if(e.key){var t=He[e.key]||e.key;if(t!=="Unidentified")return t}return e.type==="keypress"?(e=J(e),e===13?"Enter":String.fromCharCode(e)):e.type==="keydown"||e.type==="keyup"?qe[e.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:ae,charCode:function(e){return e.type==="keypress"?J(e):0},keyCode:function(e){return e.type==="keydown"||e.type==="keyup"?e.keyCode:0},which:function(e){return e.type==="keypress"?J(e):e.type==="keydown"||e.type==="keyup"?e.keyCode:0}});M(Ge);var Fe=x({},Z,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0});M(Fe);var $e=x({},X,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:ae});M($e);var Qe=x({},U,{propertyName:0,elapsedTime:0,pseudoElement:0});M(Qe);var Je=x({},Z,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0});M(Je);function et(e,t,r,i,d,m,h,p,u){var o=Array.prototype.slice.call(arguments,3);try{t.apply(r,o)}catch(v){this.onError(v)}}var W=!1,q=null,V=!1,re=null,tt={onError:function(e){W=!0,q=e}};function rt(e,t,r,i,d,m,h,p,u){W=!1,q=null,et.apply(tt,arguments)}function nt(e,t,r,i,d,m,h,p,u){if(rt.apply(this,arguments),W){if(W){var o=q;W=!1,q=null}else throw Error("clearCaughtError was called but no error was captured. This error is likely caused by a bug in React. Please file an issue.");V||(V=!0,re=o)}}var oe=Array.isArray,Y=ie.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.Events,it=Y[0],at=Y[1],ot=Y[2],ut=Y[3],lt=Y[4],st=I.unstable_act;function ft(){}function ct(e,t){if(!e)return[];if(e=je(e),!e)return[];for(var r=e,i=[];;){if(r.tag===5||r.tag===6||r.tag===1||r.tag===0){var d=r.stateNode;t(d)&&i.push(d)}if(r.child)r.child.return=r,r=r.child;else{if(r===e)return i;for(;!r.sibling;){if(!r.return||r.return===e)return i;r=r.return}r.sibling.return=r.return,r=r.sibling}}}function P(e,t){if(e&&!e._reactInternals){var r=String(e);throw e=oe(e)?"an array":e&&e.nodeType===1&&e.tagName?"a DOM node":r==="[object Object]"?"object with keys {"+Object.keys(e).join(", ")+"}":r,Error(t+"(...): the first argument must be a React class instance. Instead received: "+(e+"."))}}function G(e){return!(!e||e.nodeType!==1||!e.tagName)}function ue(e){return G(e)?!1:e!=null&&typeof e.render=="function"&&typeof e.setState=="function"}function Se(e,t){return ue(e)?e._reactInternals.type===t:!1}function F(e,t){return P(e,"findAllInRenderedTree"),e?ct(e._reactInternals,t):[]}function Ae(e,t){return P(e,"scryRenderedDOMComponentsWithClass"),F(e,function(r){if(G(r)){var i=r.className;typeof i!="string"&&(i=r.getAttribute("class")||"");var d=i.split(/\s+/);if(!oe(t)){if(t===void 0)throw Error("TestUtils.scryRenderedDOMComponentsWithClass expects a className as a second argument.");t=t.split(/\s+/)}return t.every(function(m){return d.indexOf(m)!==-1})}return!1})}function Te(e,t){return P(e,"scryRenderedDOMComponentsWithTag"),F(e,function(r){return G(r)&&r.tagName.toUpperCase()===t.toUpperCase()})}function ke(e,t){return P(e,"scryRenderedComponentsWithType"),F(e,function(r){return Se(r,t)})}function me(e,t,r){var i=e.type||"unknown-event";e.currentTarget=at(r),nt(i,t,void 0,e),e.currentTarget=null}function Ce(e,t,r){for(var i=[];e;){i.push(e);do e=e.return;while(e&&e.tag!==5);e=e||null}for(e=i.length;0<e--;)t(i[e],"captured",r);for(e=0;e<i.length;e++)t(i[e],"bubbled",r)}function xe(e,t){var r=e.stateNode;if(!r)return null;var i=ot(r);if(!i)return null;r=i[t];e:switch(t){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(i=!i.disabled)||(e=e.type,i=!(e==="button"||e==="input"||e==="select"||e==="textarea")),e=!i;break e;default:e=!1}if(e)return null;if(r&&typeof r!="function")throw Error("Expected `"+t+"` listener to be a function, instead got a value of `"+typeof r+"` type.");return r}function dt(e,t,r){e&&r&&r._reactName&&(t=xe(e,r._reactName))&&(r._dispatchListeners==null&&(r._dispatchListeners=[]),r._dispatchInstances==null&&(r._dispatchInstances=[]),r._dispatchListeners.push(t),r._dispatchInstances.push(e))}function pt(e,t,r){var i=r._reactName;t==="captured"&&(i+="Capture"),(t=xe(e,i))&&(r._dispatchListeners==null&&(r._dispatchListeners=[]),r._dispatchInstances==null&&(r._dispatchInstances=[]),r._dispatchListeners.push(t),r._dispatchInstances.push(e))}var _e={},ht=new Set(["mouseEnter","mouseLeave","pointerEnter","pointerLeave"]);function mt(e){return function(t,r){if(I.isValidElement(t))throw Error("TestUtils.Simulate expected a DOM node as the first argument but received a React element. Pass the DOM node you wish to simulate the event on instead. Note that TestUtils.Simulate will not work if you are using shallow rendering.");if(ue(t))throw Error("TestUtils.Simulate expected a DOM node as the first argument but received a component instance. Pass the DOM node you wish to simulate the event on instead.");var i="on"+e[0].toUpperCase()+e.slice(1),d=new ft;d.target=t,d.type=e.toLowerCase();var m=it(t),h=new Ke(i,d.type,m,d,t);h.persist(),x(h,r),ht.has(e)?h&&h._reactName&&dt(h._targetInst,null,h):h&&h._reactName&&Ce(h._targetInst,pt,h),ie.unstable_batchedUpdates(function(){if(ut(t),h){var p=h._dispatchListeners,u=h._dispatchInstances;if(oe(p))for(var o=0;o<p.length&&!h.isPropagationStopped();o++)me(h,p[o],u[o]);else p&&me(h,p,u);h._dispatchListeners=null,h._dispatchInstances=null,h.isPersistent()||h.constructor.release(h)}if(V)throw p=re,V=!1,re=null,p}),lt()}}"blur cancel click close contextMenu copy cut auxClick doubleClick dragEnd dragStart drop focus input invalid keyDown keyPress keyUp mouseDown mouseUp paste pause play pointerCancel pointerDown pointerUp rateChange reset resize seeked submit touchCancel touchEnd touchStart volumeChange drag dragEnter dragExit dragLeave dragOver mouseMove mouseOut mouseOver pointerMove pointerOut pointerOver scroll toggle touchMove wheel abort animationEnd animationIteration animationStart canPlay canPlayThrough durationChange emptied encrypted ended error gotPointerCapture load loadedData loadedMetadata loadStart lostPointerCapture playing progress seeking stalled suspend timeUpdate transitionEnd waiting mouseEnter mouseLeave pointerEnter pointerLeave change select beforeInput compositionEnd compositionStart compositionUpdate".split(" ").forEach(function(e){_e[e]=mt(e)});A.Simulate=_e;A.act=st;A.findAllInRenderedTree=F;A.findRenderedComponentWithType=function(e,t){if(P(e,"findRenderedComponentWithType"),e=ke(e,t),e.length!==1)throw Error("Did not find exactly one match (found: "+e.length+") for componentType:"+t);return e[0]};A.findRenderedDOMComponentWithClass=function(e,t){if(P(e,"findRenderedDOMComponentWithClass"),e=Ae(e,t),e.length!==1)throw Error("Did not find exactly one match (found: "+e.length+") for class:"+t);return e[0]};A.findRenderedDOMComponentWithTag=function(e,t){if(P(e,"findRenderedDOMComponentWithTag"),e=Te(e,t),e.length!==1)throw Error("Did not find exactly one match (found: "+e.length+") for tag:"+t);return e[0]};A.isCompositeComponent=ue;A.isCompositeComponentWithType=Se;A.isDOMComponent=G;A.isDOMComponentElement=function(e){return!!(e&&I.isValidElement(e)&&e.tagName)};A.isElement=function(e){return I.isValidElement(e)};A.isElementOfType=function(e,t){return I.isValidElement(e)&&e.type===t};A.mockComponent=function(e,t){return t=t||e.mockTagName||"div",e.prototype.render.mockImplementation(function(){return I.createElement(t,null,this.props.children)}),this};A.nativeTouchData=function(e,t){return{touches:[{pageX:e,pageY:t}]}};A.renderIntoDocument=function(e){var t=document.createElement("div");return ie.render(e,t)};A.scryRenderedComponentsWithType=ke;A.scryRenderedDOMComponentsWithClass=Ae;A.scryRenderedDOMComponentsWithTag=Te;A.traverseTwoPhase=Ce;we.exports=A;var ar=we.exports,or=({onlyFirst:e=!1}={})=>{const t=["[\\u001B\\u009B][[\\]()#;?]*(?:(?:(?:(?:;[-a-zA-Z\\d\\/#&.:=?%@~_]+)*|[a-zA-Z\\d]+(?:;[-a-zA-Z\\d\\/#&.:=?%@~_]*)*)?\\u0007)","(?:(?:\\d{1,4}(?:;\\d{0,4})*)?[\\dA-PR-TZcf-ntqry=><~]))"].join("|");return new RegExp(t,e?void 0:"g")},vt=Object.prototype.toString;function ve(e){return typeof e=="function"||vt.call(e)==="[object Function]"}function bt(e){var t=Number(e);return isNaN(t)?0:t===0||!isFinite(t)?t:(t>0?1:-1)*Math.floor(Math.abs(t))}var gt=Math.pow(2,53)-1;function yt(e){var t=bt(e);return Math.min(Math.max(t,0),gt)}function D(e,t){var r=Array,i=Object(e);if(e==null)throw new TypeError("Array.from requires an array-like object - not null or undefined");if(typeof t<"u"&&!ve(t))throw new TypeError("Array.from: when provided, the second argument must be a function");for(var d=yt(i.length),m=ve(r)?Object(new r(d)):new Array(d),h=0,p;h<d;)p=i[h],t?m[h]=t(p,h):m[h]=p,h+=1;return m.length=d,m}function B(e){"@babel/helpers - typeof";return B=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},B(e)}function wt(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function be(e,t){for(var r=0;r<t.length;r++){var i=t[r];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,Me(i.key),i)}}function Et(e,t,r){return t&&be(e.prototype,t),r&&be(e,r),Object.defineProperty(e,"prototype",{writable:!1}),e}function St(e,t,r){return t=Me(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function Me(e){var t=At(e,"string");return B(t)==="symbol"?t:String(t)}function At(e,t){if(B(e)!=="object"||e===null)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var i=r.call(e,t||"default");if(B(i)!=="object")return i;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}var Tt=function(){function e(){var t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:[];wt(this,e),St(this,"items",void 0),this.items=t}return Et(e,[{key:"add",value:function(r){return this.has(r)===!1&&this.items.push(r),this}},{key:"clear",value:function(){this.items=[]}},{key:"delete",value:function(r){var i=this.items.length;return this.items=this.items.filter(function(d){return d!==r}),i!==this.items.length}},{key:"forEach",value:function(r){var i=this;this.items.forEach(function(d){r(d,d,i)})}},{key:"has",value:function(r){return this.items.indexOf(r)!==-1}},{key:"size",get:function(){return this.items.length}}]),e}();const kt=typeof Set>"u"?Set:Tt;function k(e){var t;return(t=e.localName)!==null&&t!==void 0?t:e.tagName.toLowerCase()}var Ct={article:"article",aside:"complementary",button:"button",datalist:"listbox",dd:"definition",details:"group",dialog:"dialog",dt:"term",fieldset:"group",figure:"figure",form:"form",footer:"contentinfo",h1:"heading",h2:"heading",h3:"heading",h4:"heading",h5:"heading",h6:"heading",header:"banner",hr:"separator",html:"document",legend:"legend",li:"listitem",math:"math",main:"main",menu:"list",nav:"navigation",ol:"list",optgroup:"group",option:"option",output:"status",progress:"progressbar",section:"region",summary:"button",table:"table",tbody:"rowgroup",textarea:"textbox",tfoot:"rowgroup",td:"cell",th:"columnheader",thead:"rowgroup",tr:"row",ul:"list"},xt={caption:new Set(["aria-label","aria-labelledby"]),code:new Set(["aria-label","aria-labelledby"]),deletion:new Set(["aria-label","aria-labelledby"]),emphasis:new Set(["aria-label","aria-labelledby"]),generic:new Set(["aria-label","aria-labelledby","aria-roledescription"]),insertion:new Set(["aria-label","aria-labelledby"]),paragraph:new Set(["aria-label","aria-labelledby"]),presentation:new Set(["aria-label","aria-labelledby"]),strong:new Set(["aria-label","aria-labelledby"]),subscript:new Set(["aria-label","aria-labelledby"]),superscript:new Set(["aria-label","aria-labelledby"])};function _t(e,t){return["aria-atomic","aria-busy","aria-controls","aria-current","aria-describedby","aria-details","aria-dropeffect","aria-flowto","aria-grabbed","aria-hidden","aria-keyshortcuts","aria-label","aria-labelledby","aria-live","aria-owns","aria-relevant","aria-roledescription"].some(function(r){var i;return e.hasAttribute(r)&&!((i=xt[t])!==null&&i!==void 0&&i.has(r))})}function De(e,t){return _t(e,t)}function Mt(e){var t=Lt(e);if(t===null||t==="presentation"){var r=Dt(e);if(t!=="presentation"||De(e,r||""))return r}return t}function Dt(e){var t=Ct[k(e)];if(t!==void 0)return t;switch(k(e)){case"a":case"area":case"link":if(e.hasAttribute("href"))return"link";break;case"img":return e.getAttribute("alt")===""&&!De(e,"img")?"presentation":"img";case"input":{var r=e,i=r.type;switch(i){case"button":case"image":case"reset":case"submit":return"button";case"checkbox":case"radio":return i;case"range":return"slider";case"email":case"tel":case"text":case"url":return e.hasAttribute("list")?"combobox":"textbox";case"search":return e.hasAttribute("list")?"combobox":"searchbox";case"number":return"spinbutton";default:return null}}case"select":return e.hasAttribute("multiple")||e.size>1?"listbox":"combobox"}return null}function Lt(e){var t=e.getAttribute("role");if(t!==null){var r=t.trim().split(" ")[0];if(r.length>0)return r}return null}function S(e){return e!==null&&e.nodeType===e.ELEMENT_NODE}function Le(e){return S(e)&&k(e)==="caption"}function H(e){return S(e)&&k(e)==="input"}function Rt(e){return S(e)&&k(e)==="optgroup"}function Ot(e){return S(e)&&k(e)==="select"}function Nt(e){return S(e)&&k(e)==="table"}function Pt(e){return S(e)&&k(e)==="textarea"}function It(e){var t=e.ownerDocument===null?e:e.ownerDocument,r=t.defaultView;if(r===null)throw new TypeError("no window available");return r}function Ut(e){return S(e)&&k(e)==="fieldset"}function jt(e){return S(e)&&k(e)==="legend"}function Kt(e){return S(e)&&k(e)==="slot"}function Wt(e){return S(e)&&e.ownerSVGElement!==void 0}function Bt(e){return S(e)&&k(e)==="svg"}function Xt(e){return Wt(e)&&k(e)==="title"}function ne(e,t){if(S(e)&&e.hasAttribute(t)){var r=e.getAttribute(t).split(" "),i=e.getRootNode?e.getRootNode():e.ownerDocument;return r.map(function(d){return i.getElementById(d)}).filter(function(d){return d!==null})}return[]}function R(e,t){return S(e)?t.indexOf(Mt(e))!==-1:!1}function Yt(e){return e.trim().replace(/\s\s+/g," ")}function zt(e,t){if(!S(e))return!1;if(e.hasAttribute("hidden")||e.getAttribute("aria-hidden")==="true")return!0;var r=t(e);return r.getPropertyValue("display")==="none"||r.getPropertyValue("visibility")==="hidden"}function Ht(e){return R(e,["button","combobox","listbox","textbox"])||Re(e,"range")}function Re(e,t){if(!S(e))return!1;switch(t){case"range":return R(e,["meter","progressbar","scrollbar","slider","spinbutton"]);default:throw new TypeError("No knowledge about abstract role '".concat(t,"'. This is likely a bug :("))}}function ge(e,t){var r=D(e.querySelectorAll(t));return ne(e,"aria-owns").forEach(function(i){r.push.apply(r,D(i.querySelectorAll(t)))}),r}function qt(e){return Ot(e)?e.selectedOptions||ge(e,"[selected]"):ge(e,'[aria-selected="true"]')}function Vt(e){return R(e,["none","presentation"])}function Zt(e){return Le(e)}function Gt(e){return R(e,["button","cell","checkbox","columnheader","gridcell","heading","label","legend","link","menuitem","menuitemcheckbox","menuitemradio","option","radio","row","rowheader","switch","tab","tooltip","treeitem"])}function Ft(e){return!1}function $t(e){return H(e)||Pt(e)?e.value:e.textContent||""}function ye(e){var t=e.getPropertyValue("content");return/^["'].*["']$/.test(t)?t.slice(1,-1):""}function Oe(e){var t=k(e);return t==="button"||t==="input"&&e.getAttribute("type")!=="hidden"||t==="meter"||t==="output"||t==="progress"||t==="select"||t==="textarea"}function Ne(e){if(Oe(e))return e;var t=null;return e.childNodes.forEach(function(r){if(t===null&&S(r)){var i=Ne(r);i!==null&&(t=i)}}),t}function Qt(e){if(e.control!==void 0)return e.control;var t=e.getAttribute("for");return t!==null?e.ownerDocument.getElementById(t):Ne(e)}function Jt(e){var t=e.labels;if(t===null)return t;if(t!==void 0)return D(t);if(!Oe(e))return null;var r=e.ownerDocument;return D(r.querySelectorAll("label")).filter(function(i){return Qt(i)===e})}function er(e){var t=e.assignedNodes();return t.length===0?D(e.childNodes):t}function tr(e){var t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},r=new kt,i=It(e),d=t.compute,m=d===void 0?"name":d,h=t.computedStyleSupportsPseudoElements,p=h===void 0?t.getComputedStyle!==void 0:h,u=t.getComputedStyle,o=u===void 0?i.getComputedStyle.bind(i):u,v=t.hidden,f=v===void 0?!1:v;function c(n,g){var b="";if(S(n)&&p){var y=o(n,"::before"),w=ye(y);b="".concat(w," ").concat(b)}var a=Kt(n)?er(n):D(n.childNodes).concat(ne(n,"aria-owns"));if(a.forEach(function(L){var N=T(L,{isEmbeddedInLabel:g.isEmbeddedInLabel,isReferenced:!1,recursion:!0}),O=S(L)?o(L).getPropertyValue("display"):"inline",j=O!=="inline"?" ":"";b+="".concat(j).concat(N).concat(j)}),S(n)&&p){var s=o(n,"::after"),l=ye(s);b="".concat(b," ").concat(l)}return b.trim()}function E(n,g){var b=n.getAttributeNode(g);return b!==null&&!r.has(b)&&b.value.trim()!==""?(r.add(b),b.value):null}function _(n){return S(n)?E(n,"title"):null}function C(n){if(!S(n))return null;if(Ut(n)){r.add(n);for(var g=D(n.childNodes),b=0;b<g.length;b+=1){var y=g[b];if(jt(y))return T(y,{isEmbeddedInLabel:!1,isReferenced:!1,recursion:!1})}}else if(Nt(n)){r.add(n);for(var w=D(n.childNodes),a=0;a<w.length;a+=1){var s=w[a];if(Le(s))return T(s,{isEmbeddedInLabel:!1,isReferenced:!1,recursion:!1})}}else if(Bt(n)){r.add(n);for(var l=D(n.childNodes),L=0;L<l.length;L+=1){var N=l[L];if(Xt(N))return N.textContent}return null}else if(k(n)==="img"||k(n)==="area"){var O=E(n,"alt");if(O!==null)return O}else if(Rt(n)){var j=E(n,"label");if(j!==null)return j}if(H(n)&&(n.type==="button"||n.type==="submit"||n.type==="reset")){var se=E(n,"value");if(se!==null)return se;if(n.type==="submit")return"Submit";if(n.type==="reset")return"Reset"}var $=Jt(n);if($!==null&&$.length!==0)return r.add(n),D($).map(function(Q){return T(Q,{isEmbeddedInLabel:!0,isReferenced:!1,recursion:!0})}).filter(function(Q){return Q.length>0}).join(" ");if(H(n)&&n.type==="image"){var fe=E(n,"alt");if(fe!==null)return fe;var ce=E(n,"title");return ce!==null?ce:"Submit Query"}if(R(n,["button"])){var de=c(n,{isEmbeddedInLabel:!1,isReferenced:!1});if(de!=="")return de}return null}function T(n,g){if(r.has(n))return"";if(!f&&zt(n,o)&&!g.isReferenced)return r.add(n),"";var b=S(n)?n.getAttributeNode("aria-labelledby"):null,y=b!==null&&!r.has(b)?ne(n,"aria-labelledby"):[];if(m==="name"&&!g.isReferenced&&y.length>0)return r.add(b),y.map(function(O){return T(O,{isEmbeddedInLabel:g.isEmbeddedInLabel,isReferenced:!0,recursion:!1})}).join(" ");var w=g.recursion&&Ht(n)&&m==="name";if(!w){var a=(S(n)&&n.getAttribute("aria-label")||"").trim();if(a!==""&&m==="name")return r.add(n),a;if(!Vt(n)){var s=C(n);if(s!==null)return r.add(n),s}}if(R(n,["menu"]))return r.add(n),"";if(w||g.isEmbeddedInLabel||g.isReferenced){if(R(n,["combobox","listbox"])){r.add(n);var l=qt(n);return l.length===0?H(n)?n.value:"":D(l).map(function(O){return T(O,{isEmbeddedInLabel:g.isEmbeddedInLabel,isReferenced:!1,recursion:!0})}).join(" ")}if(Re(n,"range"))return r.add(n),n.hasAttribute("aria-valuetext")?n.getAttribute("aria-valuetext"):n.hasAttribute("aria-valuenow")?n.getAttribute("aria-valuenow"):n.getAttribute("value")||"";if(R(n,["textbox"]))return r.add(n),$t(n)}if(Gt(n)||S(n)&&g.isReferenced||Zt(n)||Ft()){var L=c(n,{isEmbeddedInLabel:g.isEmbeddedInLabel,isReferenced:!1});if(L!=="")return r.add(n),L}if(n.nodeType===n.TEXT_NODE)return r.add(n),n.textContent||"";if(g.recursion)return r.add(n),c(n,{isEmbeddedInLabel:g.isEmbeddedInLabel,isReferenced:!1});var N=_(n);return N!==null?(r.add(n),N):(r.add(n),"")}return Yt(T(e,{isEmbeddedInLabel:!1,isReferenced:m==="description",recursion:!1}))}function rr(e){return R(e,["caption","code","deletion","emphasis","generic","insertion","paragraph","presentation","strong","subscript","superscript"])}function ur(e){var t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};return rr(e)?"":tr(e,t)}var le={exports:{}};le.exports;(function(e){var t=function(){var r=String.fromCharCode,i="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=",d="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+-$",m={};function h(u,o){if(!m[u]){m[u]={};for(var v=0;v<u.length;v++)m[u][u.charAt(v)]=v}return m[u][o]}var p={compressToBase64:function(u){if(u==null)return"";var o=p._compress(u,6,function(v){return i.charAt(v)});switch(o.length%4){default:case 0:return o;case 1:return o+"===";case 2:return o+"==";case 3:return o+"="}},decompressFromBase64:function(u){return u==null?"":u==""?null:p._decompress(u.length,32,function(o){return h(i,u.charAt(o))})},compressToUTF16:function(u){return u==null?"":p._compress(u,15,function(o){return r(o+32)})+" "},decompressFromUTF16:function(u){return u==null?"":u==""?null:p._decompress(u.length,16384,function(o){return u.charCodeAt(o)-32})},compressToUint8Array:function(u){for(var o=p.compress(u),v=new Uint8Array(o.length*2),f=0,c=o.length;f<c;f++){var E=o.charCodeAt(f);v[f*2]=E>>>8,v[f*2+1]=E%256}return v},decompressFromUint8Array:function(u){if(u==null)return p.decompress(u);for(var o=new Array(u.length/2),v=0,f=o.length;v<f;v++)o[v]=u[v*2]*256+u[v*2+1];var c=[];return o.forEach(function(E){c.push(r(E))}),p.decompress(c.join(""))},compressToEncodedURIComponent:function(u){return u==null?"":p._compress(u,6,function(o){return d.charAt(o)})},decompressFromEncodedURIComponent:function(u){return u==null?"":u==""?null:(u=u.replace(/ /g,"+"),p._decompress(u.length,32,function(o){return h(d,u.charAt(o))}))},compress:function(u){return p._compress(u,16,function(o){return r(o)})},_compress:function(u,o,v){if(u==null)return"";var f,c,E={},_={},C="",T="",n="",g=2,b=3,y=2,w=[],a=0,s=0,l;for(l=0;l<u.length;l+=1)if(C=u.charAt(l),Object.prototype.hasOwnProperty.call(E,C)||(E[C]=b++,_[C]=!0),T=n+C,Object.prototype.hasOwnProperty.call(E,T))n=T;else{if(Object.prototype.hasOwnProperty.call(_,n)){if(n.charCodeAt(0)<256){for(f=0;f<y;f++)a=a<<1,s==o-1?(s=0,w.push(v(a)),a=0):s++;for(c=n.charCodeAt(0),f=0;f<8;f++)a=a<<1|c&1,s==o-1?(s=0,w.push(v(a)),a=0):s++,c=c>>1}else{for(c=1,f=0;f<y;f++)a=a<<1|c,s==o-1?(s=0,w.push(v(a)),a=0):s++,c=0;for(c=n.charCodeAt(0),f=0;f<16;f++)a=a<<1|c&1,s==o-1?(s=0,w.push(v(a)),a=0):s++,c=c>>1}g--,g==0&&(g=Math.pow(2,y),y++),delete _[n]}else for(c=E[n],f=0;f<y;f++)a=a<<1|c&1,s==o-1?(s=0,w.push(v(a)),a=0):s++,c=c>>1;g--,g==0&&(g=Math.pow(2,y),y++),E[T]=b++,n=String(C)}if(n!==""){if(Object.prototype.hasOwnProperty.call(_,n)){if(n.charCodeAt(0)<256){for(f=0;f<y;f++)a=a<<1,s==o-1?(s=0,w.push(v(a)),a=0):s++;for(c=n.charCodeAt(0),f=0;f<8;f++)a=a<<1|c&1,s==o-1?(s=0,w.push(v(a)),a=0):s++,c=c>>1}else{for(c=1,f=0;f<y;f++)a=a<<1|c,s==o-1?(s=0,w.push(v(a)),a=0):s++,c=0;for(c=n.charCodeAt(0),f=0;f<16;f++)a=a<<1|c&1,s==o-1?(s=0,w.push(v(a)),a=0):s++,c=c>>1}g--,g==0&&(g=Math.pow(2,y),y++),delete _[n]}else for(c=E[n],f=0;f<y;f++)a=a<<1|c&1,s==o-1?(s=0,w.push(v(a)),a=0):s++,c=c>>1;g--,g==0&&(g=Math.pow(2,y),y++)}for(c=2,f=0;f<y;f++)a=a<<1|c&1,s==o-1?(s=0,w.push(v(a)),a=0):s++,c=c>>1;for(;;)if(a=a<<1,s==o-1){w.push(v(a));break}else s++;return w.join("")},decompress:function(u){return u==null?"":u==""?null:p._decompress(u.length,32768,function(o){return u.charCodeAt(o)})},_decompress:function(u,o,v){var f=[],c=4,E=4,_=3,C="",T=[],n,g,b,y,w,a,s,l={val:v(0),position:o,index:1};for(n=0;n<3;n+=1)f[n]=n;for(b=0,w=Math.pow(2,2),a=1;a!=w;)y=l.val&l.position,l.position>>=1,l.position==0&&(l.position=o,l.val=v(l.index++)),b|=(y>0?1:0)*a,a<<=1;switch(b){case 0:for(b=0,w=Math.pow(2,8),a=1;a!=w;)y=l.val&l.position,l.position>>=1,l.position==0&&(l.position=o,l.val=v(l.index++)),b|=(y>0?1:0)*a,a<<=1;s=r(b);break;case 1:for(b=0,w=Math.pow(2,16),a=1;a!=w;)y=l.val&l.position,l.position>>=1,l.position==0&&(l.position=o,l.val=v(l.index++)),b|=(y>0?1:0)*a,a<<=1;s=r(b);break;case 2:return""}for(f[3]=s,g=s,T.push(s);;){if(l.index>u)return"";for(b=0,w=Math.pow(2,_),a=1;a!=w;)y=l.val&l.position,l.position>>=1,l.position==0&&(l.position=o,l.val=v(l.index++)),b|=(y>0?1:0)*a,a<<=1;switch(s=b){case 0:for(b=0,w=Math.pow(2,8),a=1;a!=w;)y=l.val&l.position,l.position>>=1,l.position==0&&(l.position=o,l.val=v(l.index++)),b|=(y>0?1:0)*a,a<<=1;f[E++]=r(b),s=E-1,c--;break;case 1:for(b=0,w=Math.pow(2,16),a=1;a!=w;)y=l.val&l.position,l.position>>=1,l.position==0&&(l.position=o,l.val=v(l.index++)),b|=(y>0?1:0)*a,a<<=1;f[E++]=r(b),s=E-1,c--;break;case 2:return T.join("")}if(c==0&&(c=Math.pow(2,_),_++),f[s])C=f[s];else if(s===E)C=g+g.charAt(0);else return null;T.push(C),f[E++]=g+C.charAt(0),c--,g=C,c==0&&(c=Math.pow(2,_),_++)}}};return p}();e!=null?e.exports=t:typeof angular<"u"&&angular!=null&&angular.module("LZString",[]).factory("LZString",function(){return t})})(le);var nr=le.exports;const lr=Ue(nr);export{or as a,ur as b,tr as c,nr as d,lr as l,ne as q,ar as t};
