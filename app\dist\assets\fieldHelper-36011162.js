import{aD as A}from"./index-f7d9b065.js";const T=(D=[],c=[])=>{const o=c.map(a=>a.trim()),_=new Set,l=new Set,d=new Set,M={};return D.forEach(a=>{var E;const r=(E=a==null?void 0:a.MDG_MAT_FIELD_NAME)==null?void 0:E.trim(),n=a==null?void 0:a.MDG_MAT_FIELD_VISIBILITY,s=a==null?void 0:a.MDG_MAT_JSON_FIELD_NAME;!r||!s||(M[s==null?void 0:s.toLowerCase()]={maxLength:a==null?void 0:a.MDG_MAT_MAX_LENGTH,fieldType:a==null?void 0:a.MDG_MAT_FIELD_TYPE,sequence:a==null?void 0:a.MDG_MAT_FIELD_SEQUENCE,visibility:n,originalFieldName:r},n===A.HEADER&&r!=="Long Description"&&(l.add(r),d.add(r)),n===A.MANDATORY&&o.includes(r)&&(_.add(r),d.add(r)),o.includes(r)&&d.add(r))}),{allFields:Array.from(d),mandatoryFields:Array.from(_),headerFields:Array.from(l),fieldMetaMap:M}};export{T as g};
