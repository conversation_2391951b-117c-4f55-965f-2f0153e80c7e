import{j as e,c as d,d as c,ag as r,B1 as o}from"./index-f7d9b065.js";const _=2,N=0,s=18,h="32da70b13",p="2025-09-03T06:39:24.734Z",V="cw-mdg-ui-merge-material",l="Shruti Mo<PERSON>patra",R={buildMajor:_,buildMinor:N,buildRevision:s,commitHash:h,buildDate:p,branchName:V,developerName:l},{VITE_ENV:w}={VITE_ENV:"dev",VITE_CLIENT_ID:"************-45noron6i0dj5j5l5ljin32fa635ts1m.apps.googleusercontent.com",VITE_DESTINATION_ADMIN:"cw-mdg-admin-dest",VITE_DESTINATION_SERVICE_REQUEST:"cw-scp-serv-req-oauth2-dev",VITE_DESTINATION_PO:"cw-scp-purch-order-oauth2-dev",VITE_DESTINATION_INVOICE:"cw-scp-invoice-oauth2-dev",VITE_DESTINATION_DOCUMENT_MANAGEMENT:"cw-mdg-documentmanagement-dest",VITE_DESTINATION_RETURNS:"cw-scp-returns-oauth2-dev",VITE_DESTINATION_MANAGE_ACCOUNT:"cw-scp-manage-acct-oauth2-dev",VITE_DESTINATION_NOTIFICATION:"cw-scp-notification-oauth2-dev",VITE_DESTINATION_BOM:"cw-mdg-billofmaterial-dest",VITE_DESTINATION_PR:"cw-scp-pr-oauth2-dev",VITE_DESTINATION_IWA:"cw-mdg-iwm-dev",VITE_DESTINATION_IWA_NPI:"cw-mdg-iwa-oauth2-dest",VITE_DESTINATION_SERVICE_ENTRY_SHEET:"cw-scp-ses-oauth2-dev",VITE_DESTINATION_PLANNING_MANAGEMENT:"cw-scp-pfm-oauth2-dev",VITE_DESTINATION_MATERIAL_MGMT:"cw-mdg-materialmanagement-dest",VITE_DESTINATION_ARTICLE_MGMT:"cw-mdg-articlemanagement-dest",VITE_DESTINATION_AI:"cw-mdg-artificialintelligence-dest",VITE_DESTINATION_WEBSOCKET:"cw-mdg-notification-dest",VITE_DESTINATION_COST_CENTER:"cw-mdg-costcenter-dest",VITE_DESTINATION_PROFIT_CENTER:"cw-mdg-profitcenter-dest",VITE_DESTINATION_BANK_KEY:"cw-mdg-bankkey-dest",VITE_DESTINATION_GENERAL_LEDGER:"cw-mdg-generalledger-dest",VITE_DESTINATION_DASHBOARD:"cw-mdg-dashboard-dest",VITE_DESTINATION_SLA_MGMT:"cw-mdg-slamanagement-dest",VITE_DESTINATION_IDM:"cw-caf-idm-services",VITE_DESTINATION_ITM_JAVA_SERVICES:"ITMJavaServices",VITE_DESTINATION_IWA_NEW:"IWAApi",VITE_CW_MDG_COSTCENTER_MASS_DEST:"cw-mdg-costcenter-dest",VITE_CW_MDG_GENERALLEDGER_MASS_DEST:"cw-mdg-generalledger-dest",VITE_CW_MDG_PROFITCENTER_MASS_DEST:"cw-mdg-profitcenter-dest",VITE_DESTINATION_INTERNAL_ORDER:"cw-mdg-internalorder-dest",VITE_URL_ITM_JAVA_SERVICES:"https://cw-mdg-iwm-dev.cfapps.eu10-004.hana.ondemand.com",VITE_BASE_URL_ITM_JAVA_SERVICES:"https://cherryworkproducts-itm-java-dev.cfapps.eu10-004.hana.ondemand.com",VITE_BASE_URL_MESSAGING_SERVICES:"https://messaging.cherryworkproducts.com",VITE_BASE_URL_CRUD_SERVICES:"https://crudservicesdev.cherryworkproducts.com",VITE_BASE_URL_IWASCP_SERVICES:"https://cw-scp-authentication.cfapps.eu10-004.hana.ondemand.com",VITE_URL_MATERIAL_MGMT:"https://incture-cherrywork-dev-cw-mdg-dev-cw-mdg-materialmanagement.cfapps.eu10-004.hana.ondemand.com",VITE_URL_ARTICLE_MGMT:"https://incture-cherrywork-dev-cw-mdg-dev-cw-mdg-articlemanagement.cfapps.eu10-004.hana.ondemand.com",VITE_URL_AI:"https://incture-cherrywork-dev-cw-mdg-dev-cw-mdg-artificialintelligence.cfapps.eu10-004.hana.ondemand.com",VITE_URL_DOCUMENT_MANAGEMENT:"https://incture-cherrywork-dev-cw-mdg-dev-cw-mdg-documentmanagement.cfapps.eu10-004.hana.ondemand.com",VITE_URL_WEBSOCKET:"https://incture-cherrywork-dev-cw-mdg-dev-cw-mdg-notification.cfapps.eu10-004.hana.ondemand.com",VITE_URL_COST_CENTER:"https://incture-cherrywork-dev-cw-mdg-dev-cw-mdg-costcenter.cfapps.eu10-004.hana.ondemand.com",VITE_URL_PROFIT_CENTER:"https://incture-cherrywork-dev-cw-mdg-dev-cw-mdg-profitcenter.cfapps.eu10-004.hana.ondemand.com",VITE_URL_BANK_KEY:"https://incture-cherrywork-dev-cw-mdg-dev-cw-mdg-bankkey.cfapps.eu10-004.hana.ondemand.com",VITE_URL_GENERAL_LEDGER:"https://incture-cherrywork-dev-cw-mdg-dev-cw-mdg-generalledger.cfapps.eu10-004.hana.ondemand.com",VITE_URL_ADMIN:"https://incture-cherrywork-dev-cw-mdg-dev-cw-mdg-admin.cfapps.eu10-004.hana.ondemand.com",VITE_URL_IWA_NPI:"https://cw-mdg-authentication-dev.cfapps.eu10-004.hana.ondemand.com",VITE_URL_DASHBOARD:"https://incture-cherrywork-dev-cw-mdg-dev-cw-mdg-dashboard.cfapps.eu10-004.hana.ondemand.com",VITE_URL_SLA_MGMT:"https://cw-mdg-slamanagement-dev.cfapps.eu10-004.hana.ondemand.com",VITE_URL_IDM:"https://cw-caf-idm-services.cfapps.eu10-004.hana.ondemand.com",VITE_URL_AUTH_TOKEN:"https://cw-mdg-materialmanagement-dev.cfapps.eu10-004.hana.ondemand.com/authenticate/token",VITE_URL_AUTH_TOKEN_CAF:"https://incture-cherrywork-dev-cw-mdg-dev-cw-mdg-materialmanagement.cfapps.eu10-004.hana.ondemand.com/authenticate/tokenCaf",VITE_URL_PROFIT_CENTER_MASS_CAF:"https://incture-cherrywork-dev-cw-mdg-dev-cw-mdg-profitcenter.cfapps.eu10-004.hana.ondemand.com",VITE_URL_COST_CENTER_MASS_CAF:"https://incture-cherrywork-dev-cw-mdg-dev-cw-mdg-costcenter.cfapps.eu10-004.hana.ondemand.com",VITE_URL_GENERAL_LEDGER_MASS_CAF:"https://incture-cherrywork-dev-cw-mdg-dev-cw-mdg-generalledger.cfapps.eu10-004.hana.ondemand.com",VITE_URL_INTERNAL_ORDER:"https://incture-cherrywork-dev-cw-mdg-dev-cw-mdg-internalorder.cfapps.eu10-004.hana.ondemand.com",VITE_URL_BOM:"https://incture-cherrywork-dev-cw-mdg-dev-cw-mdg-billofmaterial.cfapps.eu10-004.hana.ondemand.com",VITE_URL_IWA_NEW:"https://incture-cherrywork-dev-cw-caf-dev-cw-caf-iwa-services.cfapps.eu10-004.hana.ondemand.com",VITE_APP_TOKEN:"********************************************************************************************************************************************************************************************************************************************************************************.***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.JrvD-cBxLEbrxDkFZeGTVNu84j_qVu6DnlzP2E6Ch-WMSQJfdx4qBzVks4xZR2yBILpenhMUsU_Q8KjyNW8awvvf-pFTtINl2QuHmrTyTAHq0pZBFqHZJ2ACLqnDYbL9ozPmiu4799ANcLfkozycal8IX9pKLhdM0FS5cNK_kLFEtBlPERkd3Q_tZfPtdumL2L8Muf9sUggdl5yJBBBS4ZwFkS08exgq_RBdlA9LML2ug-tR9rW_pHdUtk4M1K1kCtGt2zlIxXQjO-hEf_VxIH3jbf9tzUCULn2B4VIS12vYFpN_dRrNEiYljj8uvHDvHaNoa3w4puKU_NkTQuBJPw",BASE_URL:"/",MODE:"production",DEV:!1,PROD:!0,SSR:!1},S=()=>{const{buildMajor:a,buildMinor:t,buildRevision:i,commitHash:I,buildDate:n,branchName:T,developerName:m}=R,E=`${a}.${t}.${i} `;return e(o,{maxWidth:"sm",style:{padding:"20px"},children:d(r,{elevation:3,style:{padding:"20px"},children:[e(c,{variant:"h5",gutterBottom:!0,children:"Version Information"}),d(c,{variant:"body1",children:[e("strong",{children:"Version:"})," ",E,w]}),d(c,{variant:"body1",children:[e("strong",{children:"Branch:"})," ",T]}),d(c,{variant:"body1",children:[e("strong",{children:"Commit Hash:"})," ",I]}),d(c,{variant:"body1",children:[e("strong",{children:"Name:"})," ",m]}),d(c,{variant:"body1",children:[e("strong",{children:"Build Date/Time:"})," ",new Date(n).toLocaleString()]})]})})};export{S as default};
