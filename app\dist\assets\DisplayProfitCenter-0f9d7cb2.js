import{b9 as le,n as W,g as de,u as pe,s as ge,aP as me,a as ue,r as a,bc as he,C as S,c as u,j as r,Q as fe,O as d,a6 as ye,a_ as xe,a$ as be,d as N,Z as f,aZ as I,B as k,b5 as Se,b6 as Te,ag as Pe,aO as F,ae as Ce,bf as Ae,bg as Re,bU as T,aG as Ee,aK as we,c3 as Oe,aT as w,aJ as U,c4 as C}from"./index-f7d9b065.js";import{d as Ne,a as ve,b as _e}from"./Category-81eb2509.js";import{d as $e}from"./Description-ab582559.js";import{u as De}from"./UseProfitCenterFieldConfig-0c5d1e35.js";import{G as Ie}from"./GenericTabsGlobal-613ace00.js";import"./FilterFieldGlobal-f8e8f75f.js";import"./AdapterDayjs-2a9281df.js";import"./advancedFormat-4f6292d4.js";import"./customParseFormat-1bc1aa07.js";import"./isBetween-c1c6beb9.js";import"./DatePicker-a8e9bd4a.js";import"./useMobilePicker-9b56b5b6.js";import"./CSSTransition-cd337b47.js";const O=({label:v,value:p,icon:_})=>r(d,{item:!0,xs:6,children:u(I,{flexDirection:"row",alignItems:"center",spacing:1,children:[_&&r(k,{children:_}),r(N,{variant:"body2",color:f.secondary.grey,children:v}),u(N,{variant:"body2",fontWeight:"bold",children:[": ",p||""]})]})}),Qe=()=>{var B;const v=le(()=>({customTabs:{"& .MuiTabs-scroller":{overflowX:"auto !important",overflowY:"hidden !important"}}})),p=W(i=>(i.profitCenter.profitCenterTabs||[]).filter(c=>c.tab!=="Initial Screen"&&c.tab!=="History")),{loading:_,error:ke,fetchProfitCenterFieldConfig:V}=De(),H=de();v();const z=pe(),y=ge(),t=z.state,{customError:L}=me(),{t:x}=ue(),[P,K]=a.useState([]),[A,Y]=a.useState([]),[q,$]=a.useState(!1),[Z,Le]=a.useState(""),[R,j]=a.useState(0),[J,Ge]=a.useState(null),[Q,X]=a.useState("");a.useRef(!1);const G=a.useRef(!1),E=W(i=>{var e;return(e=i.profitCenterDropdownData)==null?void 0:e.dropDown});a.useEffect(()=>{p!=null&&p.length||V()},[]),a.useEffect(()=>{he(Ae.MODULE,F.PC),ce()},[]);const ee=(i,e)=>{const c=s=>{((s==null?void 0:s.statusCode)===U.STATUS_200||(s==null?void 0:s.statusCode)===U.STATUS_201)&&y(C({keyName:"CompanyCode",data:s.body}))},o=s=>{L(s)};S(`/${T}/data/getCompCodeBasedOnControllingArea?controllingArea=${i}&rolePrefix=ETP`,"get",c,o)},oe=(i,e)=>{const c=s=>{y(C({keyName:"PrctrHierGrp",data:(s==null?void 0:s.body)||[],keyName2:e}))},o=s=>{console.log(s)};S(`/${T}${w.DROPDOWN_APIS.PROFITCENTER_GROUP}?controllingArea=${i}`,"get",c,o)},re=i=>{const e=o=>{y(C({keyName:"Country",data:o.body,keyName2:i}))},c=o=>{console.log(o)};S(`/${T}${w.DROPDOWN_APIS.COUNTRY}`,"get",e,c)},se=(i,e)=>{const c=s=>{y(C({keyName:"Region",data:s==null?void 0:s.body,keyName2:e}))},o=s=>{console.log(s)};S(`/${T}${w.DROPDOWN_APIS.REGION}?country=${i}`,"get",c,o)},ie=i=>{const e=o=>{y(C({keyName:"Segment",data:o==null?void 0:o.body,keyName2:i}))},c=o=>{L(o)};S(`/${T}${w.DROPDOWN_APIS.SEGMENT}`,"get",e,c)},ne=(i,e)=>{j(e)},te=(i,e)=>{const c={"Basic Data":e==null?void 0:e.basicDataTabDto,Indicators:e==null?void 0:e.indicatorsTabDto,"Comp Codes":e==null?void 0:e.compCodesTabDto,Address:e==null?void 0:e.addressTabDto,Communication:e==null?void 0:e.communicationTabDto,History:e==null?void 0:e.historyTabDto};return i.map(o=>{const s=o.tab,l=c[s];if(!l)return o;const n={};for(const h in o.data)n[h]=o.data[h].map(b=>{const D=b.jsonName;let g=l==null?void 0:l[D];return b.dataType==="Date"&&typeof g=="string"&&g.includes("/Date")&&(g=Re(g)),typeof g=="boolean"&&(g=g?"TRUE":"FALSE"),{...b,value:g??b.value}});return{...o,data:n}})};a.useEffect(()=>{const i=(p==null?void 0:p.length)>0,e=A&&Object.keys(A).length>0;if(i&&e&&!G.current){G.current=!0;const c=te(p,A);K(c)}},[p,A]);const ce=()=>{$(!0);const i={coAreaPCs:[{controllingArea:t==null?void 0:t.controllingArea,profitCenter:t==null?void 0:t.profitCenter}]},e=async o=>{var M;const s=((M=o==null?void 0:o.body)==null?void 0:M[0])||{};Y(s);const l=we(),n=o==null?void 0:o.body.reduce((ae,m)=>({...ae,...m.compCodesTabDto,...m.addressTabDto,...m.basicDataTabDto,...m.communicationTabDto,...m.indicatorsTabDto,...m.historyTabDto,controllingArea:m.controllingArea,costCenter:m.costCenter,fromValid:m.fromValid,toValid:m.toValid}),{});X(l);const h=[];n!=null&&n.controllingArea&&h.push(ee(n.controllingArea),oe(n.controllingArea,l)),n!=null&&n.Country&&h.push(re(l)),n!=null&&n.Country&&h.push(se(n==null?void 0:n.Country,l)),n!=null&&n.Segment&&h.push(ie(l));const b=await Promise.all(h),D=Object.assign({},...b),g={[l]:{...n,...D}};y(Oe({requestHeaderData:{},rowsHeaderData:{},rowsBodyData:g})),setTimeout(()=>{$(!1)},1e3)},c=o=>{console.error("Error fetching profit center data",o),$(!1)};S(`/${T}/data/getProfitCentersData`,"post",e,c,i)};return u("div",{style:{backgroundColor:"#FAFCFF"},children:[r(d,{container:!0,sx:fe,children:r(d,{item:!0,md:12,sx:{padding:"16px",display:"flex"},children:u(d,{md:9,sx:{display:"flex"},children:[r(ye,{color:"primary",sx:xe,onClick:()=>H(-1),children:r(be,{sx:{fontSize:"25px",color:"#000000"}})}),u(d,{item:!0,md:12,children:[r(N,{variant:"h3",children:r("strong",{children:x("Display Profit Center")})}),r(N,{variant:"body2",color:"#777",children:x("This view displays the details of the Profit Centers")})]})]})})}),u(d,{container:!0,display:"flex",flexDirection:"row",flexWrap:"nowrap",sx:{justifyContent:"space-between",alignItems:"center",paddingLeft:"29px",backgroundColor:f.basic.lighterGrey,borderRadius:"10px",boxShadow:"0 2px 8px rgba(0,0,0,0.06)"},children:[u(I,{width:"48%",spacing:1,sx:{padding:"10px 15px",borderRight:"1px solid #eaedf0"},children:[r(d,{item:!0,children:r(O,{label:x("Controlling Area"),value:(t==null?void 0:t.controllingArea)||"",labelWidth:"35%",icon:r(Ne,{sx:{color:f.blue.indigo,fontSize:"20px",marginTop:"10px"}})})}),r(d,{item:!0,children:r(O,{label:x("ProfitCenter Number"),value:(t==null?void 0:t.profitCenter)||"",labelWidth:"35%",icon:r(ve,{sx:{color:f.blue.indigo,fontSize:"20px",marginTop:"10px"}})})})]}),u(I,{width:"48%",spacing:1,marginRight:"-10%",sx:{padding:"10px 15px"},children:[r(d,{item:!0,children:r(O,{label:x("Short Description"),value:(t==null?void 0:t.ProfitCenterName)||"",labelWidth:"35%",icon:r(_e,{sx:{color:f.blue.indigo,fontSize:"20px",marginTop:"10px"}})})}),r(d,{item:!0,children:r(O,{label:x("Long Description"),value:(t==null?void 0:t.description)||"",labelWidth:"35%",icon:r($e,{sx:{color:f.blue.indigo,fontSize:"20px",marginTop:"10px"}})})})]})]}),r(d,{children:P.length>0?u(k,{sx:{mt:3},children:[r(Se,{value:R,onChange:ne,indicatorColor:"primary",textColor:"primary",variant:"scrollable",scrollButtons:"auto",sx:{borderBottom:1,borderColor:"divider",mb:2},children:P.map((i,e)=>r(Te,{label:i.tab},e))}),r(Pe,{elevation:2,sx:{p:3,borderRadius:8},children:P[R]&&(E==null?void 0:E.PrctrHierGrp)&&r(Ie,{disabled:!1,basicDataTabDetails:P[R].data,dropDownData:E,activeViewTab:P[R].tab,uniqueId:Q,selectedRow:J||{},module:(B=F)==null?void 0:B.PC})})]}):r(k,{sx:{marginTop:"30px",border:`1px solid ${f.secondary.grey}`,padding:"16px",background:`${f.primary.white}`,textAlign:"center"},children:r("span",{children:Ce.NO_DATA_AVAILABLE})})}),r(Ee,{blurLoading:q,loaderMessage:Z})]})};export{Qe as default};
