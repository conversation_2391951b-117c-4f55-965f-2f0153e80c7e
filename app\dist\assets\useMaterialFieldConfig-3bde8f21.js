import{s as $,aP as H,n as b,r as m,cZ as x,bf as J,bI as L,aT as p,C as F,aJ as V,dB as k,dC as Z,dD as X,dE as z,dF as ee,dG as se,dH as w,dI as _e,dJ as ae,dK as te,dL as q,dc as Q,dd as re,aD as oe}from"./index-f7d9b065.js";const Me=o=>{let S={},_=o==null?void 0:o.sort((l,n)=>l.MDG_MAT_VIEW_SEQUENCE-n.MDG_MAT_VIEW_SEQUENCE);const R=Q(_,"MDG_MAT_VIEW_NAME");let E=[];Object.entries(R).forEach(([l,n])=>{let i=Q(n,"MDG_MAT_CARD_NAME"),e=[];Object.entries(i).forEach(([A,D])=>{D.sort((s,y)=>s.MDG_MAT_SEQUENCE_NO-y.MDG_MAT_SEQUENCE_NO);let T=D.map(s=>({fieldName:s.MDG_MAT_UI_FIELD_NAME,sequenceNo:s.MDG_MAT_SEQUENCE_NO,fieldType:s.MDG_MAT_FIELD_TYPE,maxLength:s.MDG_MAT_MAX_LENGTH,dataType:s.MDG_MAT_DATA_TYPE,viewName:s.MDG_MAT_VIEW_NAME,cardName:s.MDG_MAT_CARD_NAME,cardSeq:s.MDG_MAT_CARD_SEQUENCE,value:s.MDG_MAT_DEFAULT_VALUE,visibility:s.MDG_MAT_VISIBILITY,jsonName:s.MDG_MAT_JSON_FIELD_NAME,fieldPriority:s.MDG_MAT_MATERIAL_FLD_PRT,fieldTooltip:s.MDG_MAT_TOOLTIP_MESSAGE}));e.push({cardName:A,cardSeq:D[0].MDG_MAT_CARD_SEQUENCE,cardDetails:T})}),e.sort((A,D)=>A.cardSeq-D.cardSeq),E.push({viewName:l,cards:e})});let U=re(E),f={};return U.forEach(l=>{let n={};l.cards.forEach(i=>{n[i.cardName]=i.cardDetails,l.viewName!=="Request Header"&&i.cardDetails.forEach(e=>{e.visibility===oe.MANDATORY&&(S[e.viewName]||(S[e.viewName]=[]),S[e.viewName].push({jsonName:e==null?void 0:e.jsonName,fieldName:e==null?void 0:e.fieldName}))})}),f[l.viewName]=n}),{transformedData:f,mandatoryFields:S}},De=()=>{const o=$(),{customError:S}=H(),_=b(a=>{var M;return(M=a.payload)==null?void 0:M.payloadData}),R=b(a=>a.applicationConfig),{taskData:E}=b(a=>a.userManagement),[U,f]=m.useState(!0),[l,n]=m.useState(!1),[i,e]=m.useState(null),A=window.location.href.includes("DisplayMaterialSAPView"),D=x(J.CURRENT_TASK);let T=null;T=typeof D=="string"?JSON.parse(D):D;let s=T==null?void 0:T.ATTRIBUTE_5;const y=async(a,M)=>{if(_!=null&&_.RequestType||A){const u={decisionTableId:null,decisionTableName:"MDG_MAT_MATERIAL_FIELD_CONFIG",version:"v5",rulePolicy:null,validityDate:null,conditions:[{"MDG_CONDITIONS.MDG_MAT_SCENARIO":(_==null?void 0:_.RequestType)||"Display","MDG_CONDITIONS.MDG_MAT_MATERIAL_TYPE":A?a.split("-")[0]:a||"VERP","MDG_CONDITIONS.MDG_MAT_REGION":(_==null?void 0:_.Region)||"US","MDG_CONDITIONS.MDG_MAT_GROUP_ROLE":A?"Z_MAT_REQ_DISPLAY":E.ATTRIBUTE_5?E.ATTRIBUTE_5:s||"Z_MAT_REQ_INITIATE","MDG_CONDITIONS.MDG_MAT_TASK_NAME":A?"NA":E!=null&&E.taskDesc?E==null?void 0:E.taskDesc:(T==null?void 0:T.taskDesc)||"NA"}],systemFilters:null,systemOrders:null,filterString:null},I=t=>{var d,C,G,P,v;if(t.statusCode===V.STATUS_200){if(Array.isArray((d=t==null?void 0:t.data)==null?void 0:d.result)&&((C=t==null?void 0:t.data)!=null&&C.result.every(O=>Object.keys(O).length!==0))){let O=(P=(G=t==null?void 0:t.data)==null?void 0:G.result[0])==null?void 0:P.MDG_MAT_MATERIAL_FIELD_CONFIG;const{transformedData:N,mandatoryFields:j}=Me(O),B=Object.keys(N).map(r=>(r==="Basic Data"?o(k(N["Basic Data"])):r==="Sales"?o(Z(N.Sales)):r==="Purchasing"?o(X(N.Purchasing)):r==="MRP"?o(z(N.MRP)):r==="Accounting"&&o(ee(N.Accounting)),r!=="Request Header"&&o(se({tab:r,data:N[r]})),{[r]:N[r]}));o(w({[(_==null?void 0:_.Region)||M||"US"]:{[a]:{allfields:_e(B),mandatoryFields:j}}}));let h=[...new Set((v=O==null?void 0:O.sort((r,K)=>r.MDG_MAT_VIEW_SEQUENCE-K.MDG_MAT_VIEW_SEQUENCE))==null?void 0:v.map(r=>r.MDG_MAT_VIEW_NAME))];h=[...h.filter(r=>!ae.includes(r))],o(te({matType:a,views:h}))}else o(w({[(_==null?void 0:_.Region)||M||"US"]:{[a]:{}}}));n(!1)}},c=t=>{S(t),e(t),n(!1)},g=R.environment==="localhost"?`/${L}${p.INVOKE_RULES.LOCAL}`:`/${L}${p.INVOKE_RULES.PROD}`;F(g,"post",I,c,u)}},W=(a,M,u)=>{n(!0);try{y(a,M,u)}catch(I){e(I),n(!1)}},Y=a=>{let M={decisionTableId:null,decisionTableName:"MDG_MAT_REG_SORG_PLNT_CNTRY_STORE_WH_MAPPING",version:"v1",rulePolicy:null,validityDate:null,conditions:[{"MDG_CONDITIONS.MDG_MAT_REGION":"US"}],systemFilters:null,systemOrders:null,filterString:null};const u=c=>{var g,t;if(c.statusCode===V.STATUS_200){let d=(t=(g=c==null?void 0:c.data)==null?void 0:g.result[0])==null?void 0:t.MDG_MAT_REG_SORG_PLNT_CNTRY_STORE_WH_MAPPING;const C=Array.from(new Map(d.map(G=>[G.MDG_MAT_SALES_ORG,{code:G.MDG_MAT_SALES_ORG,desc:G.MDG_MAT_SALES_ORG_DESC}])).values());o(q({keyName:"uniqueSalesOrgList",data:C})),o(q({keyName:"salesOrgData",data:d}))}},I=c=>{S(c)};R.environment==="localhost"?F(`/${L}${p.INVOKE_RULES.LOCAL}`,"post",u,I,M):F(`/${L}${p.INVOKE_RULES.PROD}`,"post",u,I,M)};return{loading:U,error:i,fieldConfigLoading:l,fetchMaterialFieldConfig:W,fetchOrgData:a=>{f(!0);try{Y(a)}catch(M){e(M),f(!1)}}}};export{De as u};
