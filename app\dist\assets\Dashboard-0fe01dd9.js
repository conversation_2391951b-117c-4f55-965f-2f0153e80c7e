import{cs as Ve,k as l2,A as o2,b as c2,n as te,a as xe,r as u,x as pe,s as Qe,j as e,O as _,c as D,V as d2,W as u2,d as O,i as p2,Y as Ee,qN as ze,AO as ye,$ as Se,a1 as h2,a2 as C2,a3 as m2,F as Ae,an as Me,br as g2,w as f2,ej as L2,bs as D2,aZ as ge,a6 as Ie,gD as A2,AP as T2,dy as b2,ai as S2,aj as I2,Z as he,a4 as Le,AQ as G2,a8 as Re,AR as v2,ft as N2,al as x2,AS as se,AT as q2,bD as Te,B as j,c9 as B2,ca as w2,cb as O2,C as oe,AU as ie,cq as He,AV as Ue,aG as k2,aW as H2,ae as re,b5 as _2,b6 as We,AW as R2,hG as U2,g as V2,aT as be,ws as Z2,du as F2,bM as j2,bN as Je,dv as Ge,f as ve,dw as Ne,t as qe,bS as Be,L as we,pG as Pe,am as $2,aP as z2}from"./index-f7d9b065.js";import{F as W2}from"./FilterField-ed1f5dc1.js";import{B as Ze,c as Y2}from"./DashboardSetting-9e43cf21.js";import{L as Q2}from"./LargeDropdown-b2630df6.js";import{d as Oe}from"./DragIndicator-eda0f892.js";import"./useChangeLogUpdate-1ba6b2dd.js";import"./AdapterDayjs-2a9281df.js";import"./advancedFormat-4f6292d4.js";import"./customParseFormat-1bc1aa07.js";import"./isBetween-c1c6beb9.js";import"./AutoCompleteType-13f5746b.js";import"./useMobilePicker-9b56b5b6.js";import"./CSSTransition-cd337b47.js";import"./TableContainer-1e78f44b.js";import"./CircularProgress-8950bd0a.js";const X2=Ve(l2)(({theme:s})=>({marginTop:"0px !important",border:`1px solid ${s.palette.primary.main}`,borderRadius:"8px",boxShadow:"0 2px 4px rgba(0,0,0,0.05)","&:not(:last-child)":{borderBottom:0},"&:before":{display:"none"}})),J2=Ve(o2)(({theme:s})=>({minHeight:"2rem !important",margin:"0px !important",backgroundColor:s.palette.primary.light,borderRadius:"8px 8px 0 0",transition:"all 0.2s ease-in-out","&:hover":{backgroundColor:s.palette.primary.light}})),et=({handleSearch:s,company:p,supplier:I,clearFunction:x})=>{var Ce;const m=c2(),h=te(f=>f.commonFilter.RequestBench),g=te(f=>f.commonFilter.Dashboard),{t:E}=xe(),[A,y]=u.useState({}),[K,G]=u.useState({}),[H,F]=u.useState([]),[b,k]=u.useState([]),[q,L]=u.useState([]),[B,J]=u.useState([]),[Z,R]=u.useState([]),[$,V]=u.useState([...g.dashboardDate]);u.useEffect(()=>{var f=Z.map(le=>le).join("$^$");let T={...h,selectedRegion:f};N(pe({module:"Dashboard",filterData:T}))},[Z]),u.useEffect(()=>{let f="";f={...g,dashBoardModuleName:B},N(pe({module:"Dashboard",filterData:f}))},[B]),u.useEffect(()=>{let f=[];q==null||q.map(T=>{f.push(T==null?void 0:T.code)}),N(pe({module:"Dashboard",filterData:{...g,selectedusersId:f}}))},[q]),u.useEffect(()=>{let f=[];b==null||b.map(T=>{f.push(T==null?void 0:T.lable)}),N(pe({module:"Dashboard",filterData:{...g,selectedRequestStatus:f}}))},[b]),u.useEffect(()=>{let f=[];H==null||H.map(T=>{f.push(T==null?void 0:T.lable)}),N(pe({module:"Dashboard",filterData:{...g,selectedRequestType:f}}))},[H]);const v=new Date,U=new Date;U.setDate(U.getDate()-7);const N=Qe(),P=te(f=>f.appSettings),ee=f=>{N(pe({module:"Dashboard",filterData:{...g,dashboardDate:f}}))};u.useEffect(()=>{if(g!=null&&g.dashboardDate){const f=new Date(g==null?void 0:g.dashboardDate[0]),T=new Date(g==null?void 0:g.dashboardDate[1]);V([f,T])}},[g==null?void 0:g.dashboardDate]);const W=[{type:"singleSelect",filterName:"companyCode",filterData:A,filterTitle:"Company Code"},{type:"singleSelect",filterName:"vendorNo",filterData:K,filterTitle:"Business Partner"},{type:"dateRange",filterName:"dashboardDate",filterTitle:"Date Range"}],Y=["US","EUR"],Q=()=>{Z.length===(Y==null?void 0:Y.length)?R([]):R(Y)};return e(Ae,{children:e(_,{item:!0,md:12,children:D(X2,{defaultExpanded:!1,children:[D(J2,{expandIcon:e(d2,{sx:{fontSize:"1.25rem",color:m.palette.primary.dark}}),"aria-controls":"panel1a-content",id:"panel1a-header",className:"filterDashBoard",children:[e(u2,{sx:{fontSize:"1.25rem",marginRight:1,color:m.palette.primary.dark}}),e(O,{sx:{fontSize:"0.875rem",fontWeight:600,color:m.palette.primary.dark},children:E("Filter Dashboard")})]}),D(p2,{children:[D(_,{container:!0,rowSpacing:1,spacing:2,children:[D(_,{item:!0,md:2,children:[e(O,{sx:Ee,children:E("Module")}),e(ze,{options:[...ye.filter(f=>f!=="Select All").sort((f,T)=>f.localeCompare(T))],value:((Ce=g==null?void 0:g.dashBoardModuleName)==null?void 0:Ce.length)>0&&(g==null?void 0:g.dashBoardModuleName)||(B==null?void 0:B.length)>0&&B||[ye[0]],onChange:f=>{var T;f.length>0&&((T=f[f.length-1])==null?void 0:T.label)==="Select All"?handleSelectAllModule():J(f)},placeholder:"Select Module Name"})]}),D(_,{item:!0,md:2,children:[e(O,{sx:Ee,children:E("Region")}),e(ze,{options:[...Y.filter(f=>f!=="Select All").sort((f,T)=>f.localeCompare(T))],value:Z,onChange:f=>{var T;f.length>0&&((T=f[f.length-1])==null?void 0:T.label)==="Select All"?Q():R(f)},placeholder:"Select Region"})]}),D(_,{item:!0,md:2,children:[e(O,{sx:Ee,children:E("Date Range")}),e(Se,{fullWidth:!0,sx:{padding:0,height:"37px"},children:e(h2,{dateAdapter:C2,children:e(m2,{handleDate:ee,cleanDate:!1,date:$})})})]}),W==null?void 0:W.map(f=>f!=null&&f.hideFilter?e(Ae,{}):e(_,{item:!0,md:3,children:e(W2,{type:f.type,filterName:f.filterName,filterData:f.filterData,moduleName:"Dashboard",onChangeFilter:f.onChangeFilter,filterTitle:f.filterTitle})},f.filterTitle))]}),e(_,{container:!0,style:{display:"flex",justifyContent:"flex-end"},children:e(_,{item:!0,style:{display:"flex",justifyContent:"space-around"},children:e(Me,{variant:"outlined",sx:{...g2},onClick:()=>{V([U,v]),k([]),F([]),R([]),L([]),J([]),x(),N(f2({module:"Dashboard",days:P.range})),N(pe({module:"Dashboard",filterData:{...g,selectedRequestType:[],selectedRequestStatus:[],selectedusersId:[],selectedRegion:"",dashboardDate:[U,v]}}))},children:E("Clear")})})})]})]})})})},tt=()=>e(_,{md:12,sx:{paddingTop:"1%"},children:e(et,{})}),nt="data:image/svg+xml,%3csvg%20width='350'%20height='230'%20viewBox='0%200%20350%20230'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='M137.416%2059.0001L198.175%2059.1176L223.314%2084.3395L223.181%20143.452C220.557%20142.408%20217.695%20141.835%20214.699%20141.835C202.011%20141.835%20191.725%20152.121%20191.725%20164.809C191.725%20177.379%20201.821%20187.592%20214.346%20187.78L137.158%20187.632C136.084%20187.631%20135.021%20187.419%20134.029%20187.006C133.038%20186.594%20132.137%20185.99%20131.379%20185.229C130.621%20184.468%20130.021%20183.565%20129.613%20182.572C129.205%20181.579%20128.996%20180.514%20129%20179.441L129.219%2067.144C129.221%2066.0707%20129.436%2065.0085%20129.849%2064.018C130.263%2063.0276%20130.867%2062.1285%20131.629%2061.372C132.39%2060.6156%20133.293%2060.0167%20134.286%2059.6097C135.279%2059.2027%20136.343%2058.9955%20137.416%2059.0001ZM214.965%20187.781C215.045%20187.781%20215.125%20187.78%20215.205%20187.777C215.125%20187.779%20215.045%20187.78%20214.965%20187.781Z'%20fill='url(%23paint0_linear_12419_6513)'/%3e%3cpath%20d='M198.175%2059.1176L198.53%2058.7646L198.383%2058.618L198.176%2058.6176L198.175%2059.1176ZM137.416%2059.0001L137.414%2059.5001L137.415%2059.5001L137.416%2059.0001ZM223.314%2084.3395L223.814%2084.3406L223.815%2084.1334L223.669%2083.9865L223.314%2084.3395ZM223.181%20143.452L222.996%20143.916L223.679%20144.188L223.681%20143.453L223.181%20143.452ZM214.346%20187.78L214.345%20188.28L214.354%20187.28L214.346%20187.78ZM137.158%20187.632L137.159%20187.132H137.159L137.158%20187.632ZM131.379%20185.229L131.734%20184.876H131.734L131.379%20185.229ZM129.613%20182.572L129.15%20182.762L129.613%20182.572ZM129%20179.441L129.5%20179.442L129.5%20179.442L129%20179.441ZM129.219%2067.144L128.719%2067.1428L128.719%2067.143L129.219%2067.144ZM129.849%2064.018L129.388%2063.8254H129.388L129.849%2064.018ZM131.629%2061.372L131.981%2061.7267H131.981L131.629%2061.372ZM134.286%2059.6097L134.476%2060.0724L134.286%2059.6097ZM214.965%20187.781L214.959%20187.281L214.965%20188.281L214.965%20187.781ZM215.205%20187.777L215.22%20188.277L215.194%20187.277L215.205%20187.777ZM198.176%2058.6176L137.417%2058.5001L137.415%2059.5001L198.174%2059.6176L198.176%2058.6176ZM223.669%2083.9865L198.53%2058.7646L197.821%2059.4705L222.96%2084.6925L223.669%2083.9865ZM223.681%20143.453L223.814%2084.3406L222.814%2084.3384L222.681%20143.451L223.681%20143.453ZM214.699%20142.335C217.63%20142.335%20220.43%20142.896%20222.996%20143.916L223.366%20142.987C220.683%20141.921%20217.759%20141.335%20214.699%20141.335V142.335ZM192.225%20164.809C192.225%20152.397%20202.287%20142.335%20214.699%20142.335V141.335C201.734%20141.335%20191.225%20151.845%20191.225%20164.809H192.225ZM214.354%20187.28C202.101%20187.096%20192.225%20177.106%20192.225%20164.809H191.225C191.225%20177.653%20201.54%20188.088%20214.339%20188.28L214.354%20187.28ZM137.157%20188.132L214.345%20188.28L214.347%20187.28L137.159%20187.132L137.157%20188.132ZM133.838%20187.468C134.89%20187.906%20136.018%20188.131%20137.158%20188.132L137.159%20187.132C136.151%20187.131%20135.152%20186.932%20134.221%20186.545L133.838%20187.468ZM131.025%20185.582C131.829%20186.39%20132.785%20187.031%20133.838%20187.468L134.221%20186.545C133.29%20186.158%20132.445%20185.591%20131.734%20184.876L131.025%20185.582ZM129.15%20182.762C129.584%20183.816%20130.221%20184.775%20131.025%20185.582L131.734%20184.876C131.022%20184.162%20130.459%20183.314%20130.075%20182.382L129.15%20182.762ZM128.5%20179.439C128.496%20180.579%20128.717%20181.708%20129.15%20182.762L130.075%20182.382C129.692%20181.449%20129.497%20180.45%20129.5%20179.442L128.5%20179.439ZM128.719%2067.143L128.5%20179.44L129.5%20179.442L129.719%2067.145L128.719%2067.143ZM129.388%2063.8254C128.949%2064.8765%20128.722%2066.0038%20128.719%2067.1428L129.719%2067.1451C129.721%2066.1376%20129.922%2065.1404%20130.31%2064.2107L129.388%2063.8254ZM131.276%2061.0173C130.468%2061.8201%20129.826%2062.7743%20129.388%2063.8254L130.31%2064.2107C130.699%2063.2809%20131.266%2062.4368%20131.981%2061.7267L131.276%2061.0173ZM134.097%2059.1471C133.043%2059.579%20132.084%2060.2146%20131.276%2061.0173L131.981%2061.7267C132.696%2061.0166%20133.544%2060.4544%20134.476%2060.0724L134.097%2059.1471ZM137.418%2058.5001C136.279%2058.4953%20135.151%2058.7151%20134.097%2059.1471L134.476%2060.0724C135.408%2059.6903%20136.407%2059.4958%20137.414%2059.5001L137.418%2058.5001ZM214.965%20188.281C215.05%20188.281%20215.135%20188.28%20215.22%20188.277L215.189%20187.278C215.114%20187.28%20215.039%20187.281%20214.964%20187.281L214.965%20188.281ZM215.194%20187.277C215.116%20187.279%20215.037%20187.28%20214.959%20187.281L214.97%20188.281C215.052%20188.28%20215.134%20188.279%20215.216%20188.277L215.194%20187.277Z'%20fill='white'/%3e%3cpath%20d='M63.3267%2078.8821L61.5771%2074.0909L62.5083%2075.1425V73.5061L64.4931%2076.5479L62.7435%2065.6732L65.0762%2071.6339L65.3066%2066.3759L65.6594%2068.1302L66.125%2062.1646L66.9385%2071.285L68.1048%2068.1302L67.8745%2072.2187L71.0207%2062.8673L69.5064%2074.5577L71.7215%2072.9214L70.4376%2075.0246L72.0695%2073.855L70.2121%2079.2359L63.4443%2079.3538'%20fill='url(%23paint1_linear_12419_6513)'/%3e%3cpath%20d='M70.8099%2090.6904H63.5177L61.5525%2078.027H72.7701L70.8099%2090.6904Z'%20fill='%23F0EDF7'/%3e%3cpath%20d='M90.7654%2090.6069H53.9712V188.759H90.7654V90.6069Z'%20fill='white'%20stroke='%23EBEBF2'%20stroke-linecap='round'%20stroke-linejoin='round'/%3e%3cpath%20d='M88.2612%2093.8255H56.1079V117.467H88.2612V93.8255Z'%20stroke='%23EBEBF2'%20stroke-linecap='round'%20stroke-linejoin='round'/%3e%3cpath%20d='M78.6118%20103.501H65.7524V107.801H78.6118V103.501Z'%20fill='%23ADA9BF'%20stroke='%23EBEBF2'%20stroke-miterlimit='10'/%3e%3cpath%20d='M88.2612%20121.413H56.1079V145.054H88.2612V121.413Z'%20stroke='%23EBEBF2'%20stroke-linecap='round'%20stroke-linejoin='round'/%3e%3cpath%20d='M78.6118%20131.084H65.7524V135.383H78.6118V131.084Z'%20fill='%23ADA9BF'%20stroke='%23EBEBF2'%20stroke-miterlimit='10'/%3e%3cpath%20d='M88.2612%20148.99H56.1079V172.631H88.2612V148.99Z'%20stroke='%23EBEBF2'%20stroke-linecap='round'%20stroke-linejoin='round'/%3e%3cpath%20d='M78.6118%20158.661H65.7524V162.961H78.6118V158.661Z'%20fill='%23ADA9BF'%20stroke='%23EBEBF2'%20stroke-miterlimit='10'/%3e%3cpath%20d='M54.7358%20141.025C54.2163%20140.946%2053.6871%20140.926%2053.1578%20140.97C53.0941%20140.97%2053.099%20141.079%2053.1578%20141.084C53.6772%20141.084%2054.2016%20141.059%2054.7211%20141.084C54.7407%20141.084%2054.7505%20141.059%2054.7456%20141.044C54.7456%20141.039%2054.7407%20141.029%2054.7358%20141.025Z'%20fill='white'/%3e%3cpath%20d='M49.046%20151.319C48.208%20151.334%2047.3847%20151.501%2046.6055%20151.806V151.845C47.4288%20151.649%2048.2276%20151.57%2049.0411%20151.432C49.0705%20151.418%2049.0852%20151.383%2049.0754%20151.349C49.0705%20151.334%2049.0607%20151.324%2049.046%20151.319Z'%20fill='white'/%3e%3cpath%20d='M47.3406%20147.762C46.9926%20147.762%2046.6447%20147.791%2046.3065%20147.855C46.2918%20147.86%2046.282%20147.88%2046.2869%20147.899C46.2869%20147.909%2046.2967%20147.914%2046.3065%20147.919C46.6545%20147.939%2046.9975%20147.919%2047.3406%20147.875C47.37%20147.875%2047.3945%20147.85%2047.3945%20147.821C47.3945%20147.791%2047.37%20147.767%2047.3406%20147.767V147.762Z'%20fill='white'/%3e%3cpath%20d='M272.355%20163.806C272.497%20160.115%20272.448%20156.415%20272.203%20152.73C271.659%20145.118%20270.502%20137.56%20268.738%20130.135C267.773%20125.958%20266.582%20121.845%20265.342%20117.742C265.303%20117.614%20265.107%20117.649%20265.141%20117.781C268.978%20132.258%20272.124%20147.192%20271.904%20162.238C271.845%20166.44%20271.355%20170.582%20270.958%20174.769C270.958%20174.882%20271.13%20174.926%20271.154%20174.813C271.943%20171.221%20272.203%20167.467%20272.35%20163.811L272.355%20163.806Z'%20fill='%23EBEBEB'/%3e%3cpath%20d='M265.141%20117.762C265.141%20117.762%20259.334%20132.543%20260.632%20136.002C261.931%20139.462%20264.612%20140.204%20264.612%20140.204C264.612%20140.204%20261.059%20145.29%20263.543%20150.061C266.028%20154.833%20271.541%20154.607%20271.924%20160.017C271.914%20160.042%20272.218%20137.428%20265.141%20117.762Z'%20fill='url(%23paint2_linear_12419_6513)'/%3e%3cpath%20d='M271.281%20150.705C271.291%20150.681%20271.291%20150.651%20271.281%20150.627C270.522%20140.533%20268.63%20130.563%20265.651%20120.892C265.651%20120.867%20265.592%20120.872%20265.602%20120.892C266.753%20125.221%20267.768%20129.565%20268.65%20133.934C267.871%20133.192%20266.915%20132.661%20265.876%20132.381C265.876%20132.381%20265.847%20132.405%20265.876%20132.41C266.969%20132.813%20267.949%20133.467%20268.743%20134.322C268.91%20135.182%20269.076%20136.037%20269.233%20136.897C267.934%20135.442%20266.136%20134.72%20264.332%20134.032C264.322%20134.037%20264.318%20134.052%20264.322%20134.061C264.322%20134.066%20264.327%20134.071%20264.332%20134.071C266.209%20134.833%20268.076%20135.821%20269.351%20137.442C269.792%20139.894%20270.179%20142.356%20270.517%20144.823C269.581%20143.84%20268.375%20143.147%20267.052%20142.833C267.008%20142.833%20266.988%20142.882%20267.028%20142.892C268.385%20143.295%20269.596%20144.081%20270.517%20145.162H270.541C270.654%20146.027%20270.767%20146.897%20270.87%20147.771C270.144%20147.157%20269.326%20146.661%20268.444%20146.307C268.419%20146.307%20268.405%20146.332%20268.444%20146.342C269.346%20146.735%20270.169%20147.28%20270.88%20147.958H270.909C271.002%20148.789%20271.1%20149.614%20271.183%20150.445C269.875%20148.857%20268.179%20147.644%20266.258%20146.916C266.244%20146.916%20266.229%20146.931%20266.229%20146.946C266.229%20146.961%20266.244%20146.975%20266.258%20146.975C268.155%20147.811%20269.821%20149.098%20271.11%20150.72C271.139%20150.754%20271.188%20150.769%20271.232%20150.759C271.36%20152.032%20271.473%20153.305%20271.566%20154.577C271.566%20154.597%20271.595%20154.597%20271.595%20154.577C271.497%20153.295%20271.394%20152.002%20271.281%20150.695V150.705Z'%20fill='white'/%3e%3cpath%20d='M267.317%20146.577C266.508%20146.111%20265.621%20145.806%20264.695%20145.678C264.666%20145.678%20264.661%20145.722%20264.695%20145.727C265.597%20145.909%20266.469%20146.214%20267.288%20146.636C267.322%20146.656%20267.351%20146.592%20267.317%20146.577Z'%20fill='white'/%3e%3cpath%20d='M287.62%20136.263C285.165%20137.963%20282.911%20139.939%20280.897%20142.155C278.824%20144.499%20277.221%20147.221%20276.187%20150.174C273.982%20156.332%20272.796%20162.808%20272.673%20169.349C272.6%20172.971%20272.948%20176.587%20273.707%20180.13C273.722%20180.184%20273.776%20180.219%20273.83%20180.209C273.879%20180.194%20273.913%20180.145%20273.908%20180.096C272.963%20173.845%20272.997%20167.486%20274.016%20161.246C275.016%20155.133%20276.579%20148.479%20280.456%20143.511C282.568%20140.803%20285.2%20138.676%20287.802%20136.474C287.939%20136.351%20287.763%20136.165%20287.62%20136.263Z'%20fill='%23EBEBEB'/%3e%3cpath%20d='M273.526%20164.941C274.923%20162.676%20276.565%20160.568%20278.427%20158.661C281.333%20155.747%20283.778%20153.678%20284.161%20151.752C284.543%20149.826%20281.25%20148.405%20281.25%20148.405C281.25%20148.405%20285.268%20149.133%20286.371%20147.437C287.473%20145.747%20288.003%20135.943%20288.003%20135.943C288.003%20135.943%20280.691%20141.182%20277.28%20148.523C273.869%20155.865%20273.531%20164.941%20273.531%20164.941H273.526Z'%20fill='url(%23paint3_linear_12419_6513)'/%3e%3cpath%20d='M285.469%20138.459C282.391%20141.486%20279.294%20144.72%20277.471%20148.705C276.849%20150.081%20276.31%20151.486%20275.854%20152.926C275.761%20153.216%20275.668%20153.506%20275.579%20153.796C275.574%20153.811%20275.574%20153.826%20275.579%20153.84C274.717%20156.818%20274.026%20159.84%20273.501%20162.897C273.501%20162.916%20273.526%20162.921%20273.531%20162.897C273.849%20161.369%20274.192%20159.84%20274.585%20158.287C274.599%20158.287%20274.614%20158.287%20274.629%20158.287C275.236%20158.027%20275.878%20157.865%20276.535%20157.801C276.579%20157.801%20276.574%20157.722%20276.535%20157.722C275.888%20157.752%20275.246%20157.87%20274.629%20158.071C274.981%20156.686%20275.369%20155.3%20275.8%20153.934C277.221%20153.59%20278.677%20153.398%20280.137%20153.364M280.137%20153.339C278.706%20153.27%20277.27%20153.393%20275.868%20153.703C275.947%20153.472%20276.025%20153.236%20276.099%20153.005C277.05%20152.779%20278.015%20152.612%20278.985%20152.514V152.484C278.035%20152.504%20277.089%20152.617%20276.158%20152.828C276.54%20151.649%20276.986%20150.484%20277.461%20149.354C277.858%20148.43%20278.319%20147.536%20278.843%20146.681C279.613%20146.489%20280.397%20146.346%20281.186%20146.263V146.224C280.451%20146.17%20279.706%20146.243%20278.995%20146.44C279.377%20145.816%20279.784%20145.206%20280.22%20144.612C281.862%20144.248%20283.523%20143.909%20285.204%20144.229C285.219%20144.224%20285.224%20144.209%20285.219%20144.194C285.219%20144.189%20285.209%20144.184%20285.204%20144.179C283.607%20143.757%20282.024%20143.998%20280.436%20144.312C280.607%20144.081%20280.789%20143.855%20280.965%20143.629C281.95%20143.324%20282.979%20143.157%20284.013%20143.143M284.013%20143.108C283.053%20143.02%20282.087%20143.108%20281.156%20143.364C282.514%20141.673%20284.008%20140.086%20285.493%20138.518C285.596%20138.43%20285.542%20138.391%20285.469%20138.459L284.013%20143.108Z'%20fill='white'/%3e%3cpath%20d='M252.086%20146.661C254.467%20147.472%20256.746%20148.548%20258.883%20149.875C261.103%20151.29%20263.024%20153.118%20264.548%20155.265C267.753%20159.737%20270.203%20164.7%20271.806%20169.968C272.703%20172.882%20273.252%20175.894%20273.453%20178.936C273.443%20178.985%20273.394%20179.015%20273.35%20179.005C273.315%20179%20273.291%20178.971%20273.281%20178.936C272.61%20173.658%20271.115%20168.518%20268.856%20163.708C266.636%20158.99%20263.837%20153.973%20259.554%20150.848C257.221%20149.147%20254.605%20148.032%20251.988%20146.857C251.85%20146.813%20251.943%20146.617%20252.081%20146.661H252.086Z'%20fill='%23EBEBEB'/%3e%3cpath%20d='M270.1%20166.602C268.444%20165.093%20266.631%20163.772%20264.69%20162.656C261.666%20160.971%20259.211%20159.865%20258.456%20158.396C257.707%20156.926%20260.039%20155.015%20260.039%20155.015C260.039%20155.015%20256.952%20156.543%20255.673%20155.418C254.394%20154.292%20251.703%20146.499%20251.703%20146.499C251.703%20146.499%20258.829%20149.039%20263.274%20154.189C267.719%20159.344%20270.096%20166.602%20270.096%20166.602H270.1Z'%20fill='url(%23paint4_linear_12419_6513)'/%3e%3cpath%20d='M269.728%20164.99C266.165%20158.283%20261.407%20152.214%20254.899%20148.229C256.599%20149.324%20258.197%20150.577%20259.667%20151.973C258.339%20151.899%20257.021%20152.179%20255.835%20152.784C255.825%20152.789%20255.82%20152.803%20255.825%20152.813C255.825%20152.818%20255.83%20152.823%20255.835%20152.823C257.094%20152.346%20258.432%20152.12%20259.775%20152.155C259.79%20152.155%20259.804%20152.155%20259.819%20152.155C260.049%20152.371%20260.275%20152.597%20260.505%20152.823C259.814%20152.853%20259.128%20152.946%20258.457%20153.093V153.123C259.167%20152.985%20259.892%20152.926%20260.618%20152.941C261.809%20154.15%20262.936%20155.423%20263.98%20156.759C263.499%20156.69%20263.014%20156.725%20262.549%20156.862C262.534%20156.862%20262.524%20156.872%20262.524%20156.887C262.524%20156.902%20262.534%20156.912%20262.549%20156.912C263.053%20156.853%20263.563%20156.848%20264.073%20156.892C264.269%20157.143%20264.455%20157.388%20264.656%20157.654C263.2%20157.56%20261.74%20157.703%20260.334%20158.071C260.309%20158.071%20260.334%20158.111%20260.334%20158.106C261.804%20157.796%20263.303%20157.683%20264.803%20157.762C264.984%20158.002%20265.156%20158.248%20265.337%20158.494C264.47%20158.474%20263.602%20158.572%20262.764%20158.794C262.75%20158.799%20262.745%20158.813%20262.75%20158.828C262.75%20158.833%20262.759%20158.838%20262.764%20158.843C263.641%20158.656%20264.538%20158.582%20265.43%20158.636C266.19%20159.693%20266.93%20160.779%20267.636%20161.87C267.253%20161.801%20266.861%20161.826%20266.494%20161.939M266.494%20161.988C266.9%20161.958%20267.312%20161.978%20267.719%20162.047C268.356%20163.034%20268.964%20164.032%20269.557%20165.044C269.65%20165.143%20269.787%20165.079%20269.738%20164.99L266.499%20161.983L266.494%20161.988Z'%20fill='white'/%3e%3cpath%20d='M264.185%20159.428C263.484%20159.378%20262.779%20159.467%20262.112%20159.683C262.112%20159.683%20262.112%20159.717%20262.112%20159.713C262.793%20159.595%20263.485%20159.526%20264.175%20159.496C264.225%20159.496%20264.234%20159.428%20264.185%20159.423V159.428Z'%20fill='white'/%3e%3cpath%20d='M279.294%20188.784H263.21L265.504%20171.934L266.087%20167.614H276.413L277.001%20171.934L279.294%20188.784Z'%20fill='%23F0EDF7'/%3e%3cpath%20d='M277.001%20171.934H265.504L266.087%20167.614H276.413L277.001%20171.934Z'%20fill='%23D9D3EA'/%3e%3cpath%20d='M277.893%20166.12H264.612V169.781H277.893V166.12Z'%20fill='%23F0EDF7'/%3e%3cpath%20d='M19%20188.509L56.25%20188.391L93.5%20188.346L168%20188.263L242.5%20188.346L279.75%20188.391L317%20188.509L279.75%20188.631L242.5%20188.671L168%20188.754L93.5%20188.671L56.25%20188.627L19%20188.509Z'%20fill='%23D9D9F4'/%3e%3cg%20opacity='0.93'%3e%3cpath%20d='M97.0649%2041C89.2974%2041%2083%2047.3145%2083%2055.1032C83%2062.8919%2089.2974%2069.2064%2097.0649%2069.2064C104.832%2069.2064%20111.13%2062.8919%20111.13%2055.1032C111.13%2047.3145%20104.832%2041%2097.0649%2041ZM97.0649%2066.4349C90.8215%2066.4349%2085.764%2061.3587%2085.7689%2055.0983C85.7689%2048.8378%2090.8313%2043.7666%2097.0747%2043.7715C103.313%2043.7715%20108.371%2048.8477%20108.371%2055.1032C108.381%2061.3587%20103.333%2066.4398%2097.0943%2066.4496C97.0845%2066.4496%2097.0747%2066.4496%2097.0649%2066.4496V66.4349Z'%20fill='%23F0EDF7'/%3e%3cpath%20d='M98.0694%2055.1179C98.0842%2055.6732%2097.6431%2056.1351%2097.0893%2056.1499C96.5355%2056.1646%2096.0749%2055.7224%2096.0602%2055.1671C96.0455%2054.6118%2096.4865%2054.1499%2097.0403%2054.1351C97.0501%2054.1351%2097.055%2054.1351%2097.0648%2054.1351C97.6088%2054.1351%2098.0547%2054.5725%2098.0694%2055.1179Z'%20fill='%23F0EDF7'/%3e%3cpath%20d='M93.6982%2060.2334L97.065%2055.1179L102.755%2059.8403'%20stroke='%23F0EDF7'%20stroke-miterlimit='10'/%3e%3c/g%3e%3cpath%20d='M128.944%2062.5124L120.215%2059.6301'%20stroke='black'/%3e%3cpath%20d='M132.995%2059.1223L134.128%2050'%20stroke='black'/%3e%3cpath%20d='M130.394%2059.4708L123.799%2053.068'%20stroke='black'/%3e%3cpath%20d='M151.766%20124.273C155.074%20124.075%20157.582%20121.01%20157.367%20117.426C157.152%20113.843%20154.297%20111.098%20150.989%20111.296C147.681%20111.494%20145.174%20114.56%20145.389%20118.143C145.603%20121.727%20148.459%20124.471%20151.766%20124.273Z'%20fill='%23263238'/%3e%3cpath%20d='M200.679%20121.335C203.986%20121.137%20206.494%20118.072%20206.279%20114.488C206.065%20110.905%20203.209%20108.161%20199.902%20108.359C196.594%20108.557%20194.086%20111.622%20194.301%20115.206C194.515%20118.789%20197.371%20121.533%20200.679%20121.335Z'%20fill='%23263238'/%3e%3cpath%20d='M185.42%20123.26L185.029%20122.64C184.995%20122.584%20181.333%20116.941%20174.674%20117.34C168.615%20117.703%20167.43%20123.292%20167.404%20123.531L167.264%20124.24L165.774%20123.991L165.909%20123.277C165.905%20123.209%20167.307%20116.33%20174.587%20115.894C182.16%20115.441%20186.164%20121.624%20186.331%20121.89L186.723%20122.505L185.42%20123.26Z'%20fill='%23263238'/%3e%3cpath%20opacity='0.22'%20d='M197.565%2060L197.533%2077.0053C197.531%2078.0775%20197.739%2079.1396%20198.147%2080.1312C198.555%2081.1227%20199.154%2082.0242%20199.911%2082.7841C200.667%2083.544%20201.566%2084.1475%20202.555%2084.56C203.545%2084.9726%20204.606%2085.1861%20205.678%2085.1885L222.704%2085.222L197.565%2060Z'%20fill='%23407BFF'/%3e%3cpath%20d='M232%20182L226.978%20176.978'%20stroke='%234B5768'%20stroke-width='4'%20stroke-linecap='round'%20stroke-linejoin='round'/%3e%3cpath%20d='M214.065%20179.13C222.386%20179.13%20229.13%20172.386%20229.13%20164.065C229.13%20155.745%20222.386%20149%20214.065%20149C205.745%20149%20199%20155.745%20199%20164.065C199%20172.386%20205.745%20179.13%20214.065%20179.13Z'%20stroke='%234B5768'%20stroke-width='4'%20stroke-linecap='round'%20stroke-linejoin='round'/%3e%3cdefs%3e%3clinearGradient%20id='paint0_linear_12419_6513'%20x1='180.324'%20y1='57.9809'%20x2='171.927'%20y2='189.862'%20gradientUnits='userSpaceOnUse'%3e%3cstop%20stop-color='%23CACAFC'/%3e%3cstop%20offset='1'%20stop-color='%237D7DD6'/%3e%3c/linearGradient%3e%3clinearGradient%20id='paint1_linear_12419_6513'%20x1='61.5771'%20y1='70.7592'%20x2='72.0695'%20y2='70.7592'%20gradientUnits='userSpaceOnUse'%3e%3cstop%20stop-color='%23FFEFE9'/%3e%3cstop%20offset='0.34'%20stop-color='%23E1E1D0'/%3e%3cstop%20offset='0.76'%20stop-color='%23C3D3B8'/%3e%3cstop%20offset='0.99'%20stop-color='%23B8CEAF'/%3e%3c/linearGradient%3e%3clinearGradient%20id='paint2_linear_12419_6513'%20x1='260.446'%20y1='138.892'%20x2='271.924'%20y2='138.892'%20gradientUnits='userSpaceOnUse'%3e%3cstop%20stop-color='%23FFEFE9'/%3e%3cstop%20offset='0.34'%20stop-color='%23E1E1D0'/%3e%3cstop%20offset='0.76'%20stop-color='%23C3D3B8'/%3e%3cstop%20offset='0.99'%20stop-color='%23B8CEAF'/%3e%3c/linearGradient%3e%3clinearGradient%20id='paint3_linear_12419_6513'%20x1='273.526'%20y1='150.44'%20x2='287.998'%20y2='150.44'%20gradientUnits='userSpaceOnUse'%3e%3cstop%20stop-color='%23FFEFE9'/%3e%3cstop%20offset='0.34'%20stop-color='%23E1E1D0'/%3e%3cstop%20offset='0.76'%20stop-color='%23C3D3B8'/%3e%3cstop%20offset='0.99'%20stop-color='%23B8CEAF'/%3e%3c/linearGradient%3e%3clinearGradient%20id='paint4_linear_12419_6513'%20x1='251.708'%20y1='156.548'%20x2='270.1'%20y2='156.548'%20gradientUnits='userSpaceOnUse'%3e%3cstop%20stop-color='%23FFEFE9'/%3e%3cstop%20offset='0.34'%20stop-color='%23E1E1D0'/%3e%3cstop%20offset='0.76'%20stop-color='%23C3D3B8'/%3e%3cstop%20offset='0.99'%20stop-color='%23B8CEAF'/%3e%3c/linearGradient%3e%3c/defs%3e%3c/svg%3e",Fe=s=>he.colorPalleteDashboard[s]||he.colorPalleteDashboard.default,at=s=>s===se.PIE||s===se.DONUT,e2=s=>q2.includes(s),je=(s,p)=>{var I,x;if(!s)return s;if(s.data&&Array.isArray(s.data)){const m=((x=(I=s.data[0])==null?void 0:I.data)==null?void 0:x.map(g=>g.x))||[],h=s.data.map(g=>({name:g.name,data:g.data.map(E=>E.y)}));return{...s,categories:m,series:h,isStacked:e2(p),graphDetails:{...s.graphDetails,chartType:p}}}return{...s,isStacked:e2(p),graphDetails:{...s.graphDetails,chartType:p}}},rt=({title:s,data:p=[],handleOpenDialog:I=()=>{},isLoading:x=!1,onRefresh:m=()=>{}})=>{var J,Z;const h=c2();L2(h.breakpoints.up("md"));const[g,E]=u.useState(!1),[A,y]=u.useState(null),{t:K}=xe(),G=(J=p==null?void 0:p.graphDetails)==null?void 0:J.chartType;u.useMemo(()=>{g&&!A&&y(G)},[g,G,A]);const H=R=>!!R&&Object.keys(R).length>0,F=()=>{E(!0),y(G)},b=()=>{E(!1),y(null)},k=R=>{y(R.target.value)},q=u.useMemo(()=>{var $;if(!A||!p)return p;const R=je(p,A);return($=R==null?void 0:R.graphDetails)!=null&&$.graphName&&(R.graphDetails.graphName=K(R.graphDetails.graphName)),R},[p,A,K]),L=()=>{var V,v,U,N;if(x){const P=je(p,G);(V=P==null?void 0:P.graphDetails)!=null&&V.graphName&&(P.graphDetails.graphName=K(P.graphDetails.graphName));const ee=Fe((v=P==null?void 0:P.graphDetails)==null?void 0:v.colorPallete);return e(Ze,{values:P,isTable:!0,showDownload:!0,showGraphName:!0,graphColor:ee,isLoading:x})}if(!H(p))return B();const R=je(p,G);(U=R==null?void 0:R.graphDetails)!=null&&U.graphName&&(R.graphDetails.graphName=K(R.graphDetails.graphName));const $=Fe((N=R==null?void 0:R.graphDetails)==null?void 0:N.colorPallete);return e(Ze,{values:R,isTable:!0,showDownload:!0,showGraphName:!0,graphColor:$,isLoading:x})},B=()=>D("div",{style:{textAlign:"center"},children:[e("img",{alt:K("No Data Found"),style:{height:"250px"},src:nt}),e(O,{variant:"h6",style:{marginTop:"10px"},children:K("No Data Found")})]});return D(Ae,{children:[e(b2,{sx:{borderRadius:"10px",boxShadow:1,height:"auto",display:"flex",flexDirection:"column"},children:D(D2,{children:[D(ge,{justifyContent:"flex-end",alignItems:"center",direction:"row",spacing:1,children:[e(Ie,{size:"small",onClick:m,title:"Refresh",disabled:x,sx:{backgroundColor:"rgba(0, 0, 0, 0.05)","&:hover":{backgroundColor:"rgba(0, 0, 0, 0.1)"}},children:e(A2,{fontSize:"small"})}),e(Ie,{size:"small",onClick:F,title:"Maximize",sx:{backgroundColor:"rgba(0, 0, 0, 0.05)","&:hover":{backgroundColor:"rgba(0, 0, 0, 0.1)"}},children:e(T2,{fontSize:"small"})})]}),L()]})}),D(S2,{open:g,onClose:b,fullWidth:!0,maxWidth:"lg",PaperProps:{sx:{height:{xs:"100vh",sm:"100vh",md:"80vh",lg:"80vh",xl:"80vh"},maxHeight:{xs:"100vh",sm:"100vh",md:"80vh",lg:"80vh",xl:"80vh"},borderRadius:"10px",margin:{xs:"0",md:"24px"}}},children:[D(I2,{sx:{display:"flex",justifyContent:"space-between",alignItems:"center",padding:"16px 24px",borderBottom:`1px solid ${he.dialog.borderBottom}`},children:[D(ge,{direction:"row",spacing:2,alignItems:"center",children:[e(O,{variant:"h6",children:(Z=p==null?void 0:p.graphDetails)!=null&&Z.graphName?K(p.graphDetails.graphName):K("Chart View")}),H(p)&&e(Se,{size:"small",sx:{minWidth:120},children:e(Le,{value:A||"",onChange:k,displayEmpty:!0,variant:"outlined",sx:{height:"32px"},children:at(G)?G2.map(R=>e(Re,{value:R.value,children:R.label},R.value)):v2.map(R=>e(Re,{value:R.value,children:R.label},R.value))})})]}),e(Ie,{onClick:b,size:"small",children:e(N2,{})})]}),e(x2,{sx:{display:"flex",flexDirection:"column",padding:"24px",height:"calc(100% - 64px)",overflow:"hidden"},children:e("div",{style:{flex:1,display:"flex",flexDirection:"column",height:"100%",overflow:"hidden"},children:H(p)?(()=>{var $;const R=Fe(($=q==null?void 0:q.graphDetails)==null?void 0:$.colorPallete);return e(Ze,{values:q,isTable:!0,showDownload:!0,showGraphName:!0,height:"100%",graphColor:R})})():B()})})]})]})},P2=({cards:s=[],loading:p,graphLoadingStates:I={},onRefreshGraph:x=()=>{},isTabbed:m=!1,userPreferences:h=[]})=>{if(p)return e(j,{sx:{display:"flex",justifyContent:"center",alignItems:"center",height:"100%"},children:e(Te,{})});if(!s.length)return e(j,{sx:{display:"flex",justifyContent:"center",alignItems:"center",height:"100%"},children:e(O,{variant:"h6",color:"text.secondary",children:"No charts available. Please configure your dashboard."})});const g=[...s].sort((E,A)=>{var y,K;if(m){const G=E.kpiId||E.id||`KPI_${E.id}`,H=A.kpiId||A.id||`KPI_${A.id}`,F=h.find(L=>L.KpiId===G||L.KpiId===E.kpiId||L.KpiId===E.id||L.KpiId===`KPI_${E.id}`),b=h.find(L=>L.KpiId===H||L.KpiId===A.kpiId||L.KpiId===A.id||L.KpiId===`KPI_${A.id}`),k=(F==null?void 0:F.SecKpiSequence)??999,q=(b==null?void 0:b.SecKpiSequence)??999;return k-q}else{const G=E.GraphSequence||((y=E.graphDetails)==null?void 0:y.KpiSequence)||E.Sequence||0,H=A.GraphSequence||((K=A.graphDetails)==null?void 0:K.KpiSequence)||A.Sequence||0;return G-H}});return e(_,{container:!0,spacing:2,children:g.map((E,A)=>{var G;const y=E.kpiId||E.id,K=I[y]||!1;return e(_,{item:!0,xs:12,md:6,lg:4,children:e(rt,{title:(G=E.graphDetails)==null?void 0:G.graphName,data:E,isLoading:K,onRefresh:()=>x(y)})},E.id)})})};var Xe={},st=w2;Object.defineProperty(Xe,"__esModule",{value:!0});var E2=Xe.default=void 0,it=st(B2()),lt=O2;E2=Xe.default=(0,it.default)((0,lt.jsx)("path",{d:"m20.41 8.41-4.83-4.83c-.37-.37-.88-.58-1.41-.58H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V9.83c0-.53-.21-1.04-.59-1.42M7 7h7v2H7zm10 10H7v-2h10zm0-4H7v-2h10z"}),"TextSnippet");const ot={display:"flex",justifyContent:"space-between",alignItems:"center",borderBottom:"1px solid #eee",padding:"15px 0",marginBottom:"10px",backgroundColor:"#f9f9f9",borderRadius:"8px",paddingLeft:"15px",paddingRight:"15px",boxShadow:"0 2px 4px rgba(0,0,0,0.1)",transition:"transform 0.2s, box-shadow 0.2s","&:hover":{transform:"translateY(-2px)",boxShadow:"0 4px 8px rgba(0,0,0,0.2)"}},ct={display:"flex",justifyContent:"center",alignItems:"center",gap:1,fontWeight:"500",color:"#555"};he.primary.white,he.tab.background;const dt={marginLeft:"10px",transition:"all 0.3s ease-in-out","&:hover":{backgroundColor:"#1976d2",color:"#fff",transform:"scale(1.1)"}},t2=["#4dc9f6","#f67019","#8549ba","#f53050","#537bc4","#acc236","#166a8f","#00a950","#58595b","#ff6384","#36a2eb","#ffce56","#cc65fe","#ff9f40","#8c564b","#e377c2","#7f7f7f","#bcbd22","#17becf"],ut=Ve(l2)(({theme:s})=>({marginTop:"0px !important",border:`1px solid ${he.primary.border}`,borderRadius:"8px",boxShadow:"0 2px 4px rgba(0,0,0,0.05)","&:not(:last-child)":{borderBottom:0},"&:before":{display:"none"}})),pt=Ve(o2)(({theme:s})=>({minHeight:"2rem !important",margin:"0px !important",backgroundColor:s.palette.primary.light,borderRadius:"8px 8px 0 0",transition:"all 0.2s ease-in-out","&:hover":{backgroundColor:s.palette.primary.light}})),y2=({reportConfig:s=[],kpiReportPrefs:p=[],loading:I,isTabbed:x=!1,userPreferences:m=[]})=>{var De;const h=te(S=>S.commonFilter.Reports),g=te(S=>S.commonFilter.RequestBench),E=te(S=>S.AllDropDown.dropDown),[A,y]=u.useState([]),[K,G]=u.useState([]),[H,F]=u.useState([...h.reportDate]),[b,k]=u.useState([]),[q,L]=u.useState([]),[B,J]=u.useState([]),[Z,R]=u.useState(!1),[$,V]=u.useState(""),v=Qe(),U=te(S=>S.userManagement.userData),N=(U==null?void 0:U.user_id)||"",{t:P}=xe(),ee=te(S=>S.commonFilter.Dashboard);u.useEffect(()=>{if(h!=null&&h.reportDate){const S=new Date(h==null?void 0:h.reportDate[0]),c=new Date(h==null?void 0:h.reportDate[1]);F([S,c])}},[h==null?void 0:h.reportDate]),u.useEffect(()=>{T(),f()},[]);const W=["US","EUR"],[Y,Q]=u.useState([]),Ce=()=>{Q(Y.length===W.length?[]:W)},f=()=>{oe(`/${ie}/GraphConfig/getReqStatuses`,"get",S=>J((S==null?void 0:S.body)||[]),()=>{})},T=()=>{oe(`/${ie}/GraphConfig/getReqTypes`,"get",S=>L((S==null?void 0:S.body)||[]),()=>{})},le=(S,c,n)=>{V(H2.REPORT_LOADING),R(!0),oe(`/${ie}/excel${S}`,"postandgetblob",t=>{const a=URL.createObjectURL(t),i=document.createElement("a");i.href=a,i.setAttribute("download",c),document.body.appendChild(i),i.click(),document.body.removeChild(i),URL.revokeObjectURL(a),R(!1),V("")},()=>{R(!1),V("")},n)},me=S=>{v(pe({module:"Reports",filterData:{...h,reportDate:S}}))},fe=()=>{Q([]),k([]),y([]),G([]);const S=new Date,c=new Date;c.setDate(c.getDate()-3650),F([c,S]),v(f2({module:"Reports"}))};return D(Ae,{children:[e(_,{item:!0,md:12,children:D(ut,{defaultExpanded:!1,sx:{marginTop:"5px !important",marginLeft:"25px !important",marginRight:"20px !important"},children:[D(pt,{expandIcon:e(d2,{sx:{fontSize:"1.25rem",color:he.primary.main}}),"aria-controls":"panel1a-content",id:"panel1a-header",children:[e(u2,{sx:{fontSize:"1.25rem",marginRight:1,color:he.primary.main}}),e(O,{sx:{fontSize:"0.875rem",fontWeight:600,color:he.primary.dark},children:P("Filter Reports")})]}),D(p2,{children:[D(_,{container:!0,rowSpacing:1,spacing:2,children:[D(_,{item:!0,md:2,children:[e(O,{sx:Ee,children:P("Date Range")}),e(Se,{fullWidth:!0,sx:{padding:0,height:"37px"},children:e(h2,{dateAdapter:C2,children:e(m2,{handleDate:me,cleanDate:!1,date:H})})})]}),D(_,{item:!0,md:2,children:[e(O,{sx:Ee,children:P("Region")}),e(ze,{options:[...(De=W==null?void 0:W.filter(S=>S!=="Select All"))==null?void 0:De.sort((S,c)=>typeof S=="string"&&typeof c=="string"?S.localeCompare(c):0)],value:Y,onChange:S=>{var c;S.length>0&&((c=S[S.length-1])==null?void 0:c.label)==="Select All"?Ce():Q(S)},placeholder:"Select Region"})]}),D(_,{item:!0,md:2,children:[e(O,{sx:Ee,children:P("Division")}),e(Q2,{matGroup:(E==null?void 0:E.Division)??[],selectedMaterialGroup:A,setSelectedMaterialGroup:S=>{if(!S||S.length===0){y([]);return}y(S)},placeholder:"Select Division"})]})]}),e(_,{container:!0,style:{display:"flex",justifyContent:"flex-end"},children:e(_,{item:!0,style:{display:"flex",justifyContent:"space-around"},children:e(Me,{variant:"outlined",sx:{...g2},onClick:fe,children:P("Clear")})})})]})]})}),e(_,{item:!0,md:12,children:e(ge,{justifyContent:"space-between",direction:"row",children:e(_,{container:!0,spacing:2,children:e(_,{item:!0,md:12,children:e(b2,{sx:{borderRadius:"10px",boxShadow:4,height:{xs:"calc(70vh - 100px)",md:"calc(75vh - 100px)",lg:"calc(80vh - 130px)"},marginLeft:"25px",marginTop:"20px",marginRight:"20px"},children:D(D2,{children:[e(O,{variant:"h6",sx:{fontWeight:"bold",marginBottom:"20px"},children:P("Application Report List")}),D(ge,{spacing:2,sx:{height:{xs:"calc(70vh - 160px)",md:"calc(75vh - 160px)",lg:"calc(80vh - 190px)"},overflowY:"auto",overflowX:"hidden","&::-webkit-scrollbar":{width:"4px"},"&::-webkit-scrollbar-thumb":{backgroundColor:"rgba(0,0,0,0.2)",borderRadius:"4px"}},children:[I&&e(j,{sx:{display:"flex",justifyContent:"center",alignItems:"center",height:"100%"},children:e(Te,{})}),e(_,{container:!0,spacing:1,children:s.filter(S=>{const c=p.find(n=>n.KpiId===S.MDG_KPI_ID);return(c==null?void 0:c.KpiVisibility)===!0&&(c==null?void 0:c.IsActive)===!0}).sort((S,c)=>{if(x){const n=m.find(l=>l.KpiId===S.MDG_KPI_ID),t=m.find(l=>l.KpiId===c.MDG_KPI_ID),a=(n==null?void 0:n.SecKpiSequence)??999,i=(t==null?void 0:t.SecKpiSequence)??999;return a-i}else{const n=m.find(l=>l.KpiId===S.MDG_KPI_ID),t=m.find(l=>l.KpiId===c.MDG_KPI_ID),a=(n==null?void 0:n.KpiSequence)??S.MDG_KPI_GRAPH_SEQUENCE??0,i=(t==null?void 0:t.KpiSequence)??c.MDG_KPI_GRAPH_SEQUENCE??0;return a-i}}).map((S,c)=>{var t,a,i,l,o;const n={FromDate:He(((t=h==null?void 0:h.reportDate)==null?void 0:t[0])??((a=g==null?void 0:g.createdOn)==null?void 0:a[0])).format("YYYY-MM-DD"),ToDate:He(((i=h==null?void 0:h.reportDate)==null?void 0:i[1])??((l=g==null?void 0:g.createdOn)==null?void 0:l[1])).format("YYYY-MM-DD"),Requestor:"",KpiId:S.MDG_KPI_ID,Module:Ue(ee==null?void 0:ee.dashBoardModuleName)||ye[0],UserId:N,Priority:"",Region:Y.join(","),ReqType:b.join(","),ReqStatus:K.join(","),GraphType:S.MDG_KPI_GRAPH_TYPE||"",KpiName:S.MDG_KPI_NAME||"",ColPallet:S.MDG_KPI_COLOR_PALLET||"",GraphColumn:((o=S.MDG_KPI_GRAPH_COLUMN)==null?void 0:o.toLowerCase())||"",GraphSequence:S.MDG_KPI_GRAPH_SEQUENCE||""};return e(_,{item:!0,xs:6,sx:{paddingRight:"8px"},children:D(j,{sx:{...ot,width:"100%",boxSizing:"border-box"},children:[D(O,{variant:"body1",sx:{...ct,whiteSpace:"nowrap",overflow:"hidden",textOverflow:"ellipsis"},children:[e(E2,{sx:{color:t2[c%t2.length],marginRight:"4px"}}),P(S.MDG_KPI_NAME)]}),e(Me,{variant:"outlined",sx:dt,onClick:()=>le(S.MDG_KPI_ENDPOINT,`${S.MDG_KPI_NAME}.xlsx`,n),children:P("Download")})]})},S.MDG_KPI_ID)})})]})]})})})})})}),e(k2,{blurLoading:Z,loaderMessage:$})]})},ht=({children:s,value:p,index:I,...x})=>e("div",{role:"tabpanel",hidden:p!==I,id:`dashboard-tabpanel-${I}`,"aria-labelledby":`dashboard-tab-${I}`,...x,children:p===I&&e(j,{sx:{p:3},children:s})}),Ct=({sectionedCards:s={},sectionedReports:p={},loading:I=!1,reportsLoading:x=!1,showReports:m=!1,kpiReportPrefs:h=[],graphLoadingStates:g={},onRefreshGraph:E=()=>{},decisionTableConfig:A=[],userPreferences:y=[]})=>{const{t:K}=xe(),[G,H]=u.useState(0),b=(()=>Object.keys(m?p:s).sort((J,Z)=>{const R=y.filter(U=>{const N=A.find(ee=>ee.MDG_KPI_ID===U.KpiId);return((N==null?void 0:N.MDG_KPI_SECTION_NAME)||"General")===J}),$=y.filter(U=>{const N=A.find(ee=>ee.MDG_KPI_ID===U.KpiId);return((N==null?void 0:N.MDG_KPI_SECTION_NAME)||"General")===Z}),V=R.length>0?Math.min(...R.map(U=>U.SectionSequence||0)):0,v=$.length>0?Math.min(...$.map(U=>U.SectionSequence||0)):0;return V-v}))();if(b.length===0)return I?e(j,{sx:{display:"flex",justifyContent:"center",alignItems:"center",height:"100%"},children:e(Te,{})}):e(j,{sx:{p:2},children:e(O,{variant:"h6",color:"textSecondary",children:K(re.NO_DATA_AVAILABLE)})});const k=(L,B)=>{H(B)},q=L=>({id:`dashboard-tab-${L}`,"aria-controls":`dashboard-tabpanel-${L}`});return I?e(j,{sx:{display:"flex",justifyContent:"center",alignItems:"center",height:"100%"},children:e(Te,{})}):D(j,{sx:{width:"100%"},children:[e(j,{sx:{borderBottom:1,borderColor:"divider"},children:e(_2,{value:G,onChange:k,"aria-label":"dashboard sections",variant:"scrollable",scrollButtons:"auto",children:b.map((L,B)=>e(We,{label:K(L),...q(B)},L))})}),b.map((L,B)=>e(ht,{value:G,index:B,children:m?e(y2,{reportConfig:p[L]||[],kpiReportPrefs:h,loading:x,isTabbed:!0,userPreferences:y},`reports-${L}`):e(P2,{cards:s[L]||[],loading:!1,graphLoadingStates:g,onRefreshGraph:E,isTabbed:!0,userPreferences:y},`charts-${L}`)},L))]})},ae={Open:"Open",PendingConfirmation:"PendingConfirmation",Confirmed:"Confirmed",Delivered:"Delivered",Blocked:"Blocked",ProductionInProgress:"ProductionInProgress",ProductionCompleted:"ProductionCompleted",PendingASN:"PendingASN",OverdueShipment:"OverdueShipment",PendingReturns:"PendingReturns",OnTimeDelivery:"OnTimeDelivery",OrderFillRate:"OrderFillRate",ReadyToINV:"ReadyToINV",ReadyToPostINV:"ReadyToPostINV",PostedINV:"PostedINV",PaidINV:"PaidINV",UnpaidINV:"UnpaidINV",RejectedINV:"RejectedINV",MaterialQuantity:"MaterialQuantity",MaterialValue:"MaterialValue",POStatus:"POStatus",INVStatus:"INVStatus",PaymentStatus:"PaymentStatus",ProductionStatus:"ProductionStatus",SRStatus:"SRStatus",SRPriority:"SRPriority",DeliveryDelay:"DeliveryDelay",PlanningTask:"PlanningTask",PendingAck:"PendingAck",PendingConsumption:"PendingConsumption",PendingPlanning:"PendingPlanning",SubmiitedAck:"SubmiitedAck",ConfirmationSubmitted:"ConfirmationSubmitted",SubmittedASN:"SubmittedASN",SubmittedConsumption:"SubmittedConsumption",ConsumptionSummary:"ConsumptionSummary",ConsumptionByPO:"ConsumptionByPO",ConsumptionByASN:"ConsumptionByASN",ConsumptionByMaterial:"ConsumptionByMaterial",MaterialGroup:"MaterialGroup",PlanningTable:"PlanningTable",Change:"Change",Create:"Create",Extend:"Extend",MassExtend:"MassExtend",MassChange:"MassChange",MassCreate:"MassCreate",pieStatus:"pieStatus",ExtendTable:"ExtendTable",ExtendTableHeader:"ExtendTableHeader",OnboardBar:"OnboardBar",FormToSupplier:"FormToSupplier",FinanceReview:"FinanceReview",ProcurementLeadReview:"ProcurementLeadReview",BuyerReview:"BuyerReview",ComplianceReview:"ComplianceReview",Completed:"Completed",dashboardDate:"dashboardDate",CycleTime:"CycleTime",BottleNeck:"BottleNeck",BottleNeckTable:"BottleNeckTable",BottleNeckTable2:"BottleNeckTable2",BottleNeckGraph:"BottleNeckGraph",ReviewPending:"ReviewPending",Approved:"Approved",CorrectionPending:"CorrectionPending",ApprovalPending:"ApprovalPending",PlanningTaskContent:"PlanningTaskContent",PlanningTaskLength:"PlanningTaskLength",PlanningTaskHeader:"PlanningTaskHeader",requestItemLength:"requestItemLength",requestTypeGraph:"requestTypeGraph",BasedOnGroupGraph:"BasedOnGroupGraph",topFiveSlaBreached:"topFiveSlaBreached",slaRequestType:"slaRequestType",slaRequestTypeContent:"slaRequestTypeContent",selectedRequestTypeSLATable:"selectedRequestTypeSLATable",selectedRequestTypeRole:"selectedRequestTypeRole"},_e=(s,p)=>{var I=p===void 0?.5:1-p;return R2(s).alpha(I).rgbString()},n2={BUPA:{product:!0,TabPanel:[{uid:0,name:"Status by Scenario",icon:"HowToReg",required:!0},{uid:1,name:"Bottleneck",icon:"RunningWithErrors",required:!0}],Tiles:[,{uid:0,name:"Create",count:ae.Create,required:!0,width:2,type:"RBEC",status:"Create",color:_e("#4dc9f6",.7),borderColor:"#4dc9f6"},{uid:0,name:"Change",count:ae.Change,required:!0,width:2,type:"RBEC",status:"Change",color:_e("#f6d55c",.7),borderColor:"#f6d55c"},{uid:0,name:"Extend",count:ae.Extend,required:!0,width:2,type:"RBEC",status:"Extend",color:_e("#537bc4",.7),borderColor:"#537bc4"},{uid:0,name:"Create With Upload",count:ae.MassCreate,required:!0,width:2,type:"RBEC",status:"Mass Create",color:_e("#00a950",.7),borderColor:"#00a950"},{uid:0,name:"Change With Upload",count:ae.MassChange,required:!0,width:2,type:"RBEC",status:"Mass Change",color:_e("#8549ba",.7),borderColor:"#8549ba"},{uid:0,name:"Extend With Upload",count:ae.MassExtend,required:!0,width:2,type:"RBEC",status:"Mass Extend",color:_e("#ff6384",.7),borderColor:"#ff6384"}],Graphs:[{uid:1,id:1,name:"Time Log Based on Roles",count:ae.OnboardBar,required:!0,stackedBar:!0,isStacked:!0,xaxis:"status",yaxis:"statusCount",yaxisHeader:"Requests",type:"po",width:12},{uid:0,id:1,name:"Status ",count:ae.pieStatus,required:!0,xaxis:"status",yaxis:"statusCount",type:"RBEC",isPie:!0,width:6},{uid:0,id:0,name:"Extend Table",count:ae.ExtendTable,required:!0,width:6,isTable:!0},{uid:2,id:1010,name:"Extend Table",count:ae.BottleNeckTable,required:!0,width:6,isTable2:!0,isTable:!1},{uid:2,id:1011,name:"Extend Table",count:ae.BottleNeckTable2,required:!0,width:6,isTable3:!0,isTable2:!1,isTable:!1},{uid:2,id:1091,name:"",count:ae.BottleNeckGraph,required:!0,width:12,isTable3:!1,isTable2:!1,isTable:!1,isgroup:!0}]}},mt=()=>{var F;const s=(((F=U2)==null?void 0:F.system)==="CHWSCP",n2.BUPA),p=s.Tiles.reduce((b,k)=>(b[k.count]=!0,b),{}),{t:I}=xe(),x=s.Tiles.reduce((b,k)=>(b[k.count]=0,b),{}),m=te(b=>b==null?void 0:b.commonFilter.Dashboard),h=te(b=>b.commonFilter.RequestBench);u.useState(p),u.useState(x);const[g,E]=u.useState([]),A=Qe(),y=V2(),K=(b,k)=>{var q=k===void 0?.5:1-k;return R2(b).alpha(q).rgbString()},G=b=>{let k={...h,requestType:b};A(pe({module:"RequestBench",filterData:k})),y("/requestBench")};u.useEffect(()=>{let b=new FormData;b.append("fromDate",He(m!=null&&m.dashboardDate[0]?m==null?void 0:m.dashboardDate[0]:h==null?void 0:h.createdOn[0]).format("YYYY-MM-DD")+" 00:00:00"),b.append("toDate",He(m!=null&&m.dashboardDate[1]?m==null?void 0:m.dashboardDate[1]:h==null?void 0:h.createdOn[1]).format("YYYY-MM-DD")+" 23:59:59"),b.append("module",Ue(m==null?void 0:m.dashBoardModuleName)||ye[0]),b.append("userId","");const k=L=>{E(L.body)},q=()=>{};oe(`/${ie}${be.DASHBOARD_APIS.KPI_CARDS}`,"postformdata",k,q,b)},[m]);const H={Create:K("#4dc9f6",.7),Change:K("#f6d55c",.7),Extend:K("#537bc4",.7),"Create With Upload":K("#00a950",.7),"Change With Upload":K("#8549ba",.7),"Extend With Upload":K("#ff6384",.7)};return e(_,{container:!0,spacing:2,sx:{mt:2},className:"kpiCard",children:g==null?void 0:g.map(b=>e(Ae,{children:e(_,{item:!0,xs:12,sm:6,md:4,lg:2,children:e(_,{onClick:()=>G(b.status),children:e(Z2,{events:{allow:!0,type:"option"},value:b==null?void 0:b.statusCount,graphName:I(b==null?void 0:b.status),KPIColor:H[b.status]})})})}))})},M2=s=>s?s.split(",").map(p=>({value:p.trim(),label:p.trim()})):[],gt=s=>({Bar:se.BAR,"Stacked Bar":se.STACKED_BAR,Column:se.COLUMN,"Stacked Column":se.STACK_COLUMN,Line:se.LINE,"Stacked Line":se.STACKED_LINE,Area:se.AREA,"Stacked Area":se.STACKED_AREA,Pie:se.PIE,Donut:se.DONUT})[s]||s.toUpperCase().replace(/\s+/g,"_"),a2=(s,p)=>{const I=p==null?void 0:p.find(x=>x.MDG_KPI_ID===s);return I!=null&&I.MDG_KPI_GRAPH_OPTIONS?M2(I.MDG_KPI_GRAPH_OPTIONS).map(x=>({value:gt(x.value),label:x.label})):[]},r2=(s,p)=>{const I=p==null?void 0:p.find(x=>x.MDG_KPI_ID===s);return I!=null&&I.MDG_KPI_PALLET_OPTIONS?M2(I.MDG_KPI_PALLET_OPTIONS):[]};function s2(s){const{children:p,value:I,index:x,...m}=s;return e("div",{role:"tabpanel",hidden:I!==x,id:`dashboard-tabpanel-${x}`,"aria-labelledby":`dashboard-tab-${x}`,...m,children:I===x&&e(j,{sx:{p:1},children:p})})}const ft=({open:s,onClose:p,onSave:I,decisionTableConfig:x,userPreferences:m,reportConfig:h,onRefreshSpecificGraphs:g})=>{const[E,A]=u.useState(0),[y,K]=u.useState([]),[G,H]=u.useState([]),[F,b]=u.useState(!0),[k,q]=u.useState(!1),[L,B]=u.useState({}),[J,Z]=u.useState({}),[R,$]=u.useState({}),[V,v]=u.useState({}),[U,N]=u.useState("section"),{t:P}=xe(),ee=te(c=>c.userManagement.userData),W=(ee==null?void 0:ee.user_id)||"",Y=(c,n)=>{A(n)},Q=c=>{if(!c.destination)return;const{source:n,destination:t,type:a}=c;a==="KPI_METRICS"?Ce(n,t):a==="KPI_REPORTS"&&f(n,t)},Ce=(c,n)=>{if(U==="section"){const t=c.droppableId,a=n.droppableId;if(t===a){const l=y.filter(r=>(r.section||"General")===t).sort((r,M)=>(r.secKpiSequence||0)-(M.secKpiSequence||0)),[o]=l.splice(c.index,1);l.splice(n.index,0,o);const d=y.map(r=>{if((r.section||"General")===t){const M=l.findIndex(w=>w.id===r.id);return{...r,secKpiSequence:M+1}}return r});K(d);const C={};l.forEach(r=>{C[r.id]=!0}),B(r=>({...r,...C}))}}else{const t=[...y].sort((o,d)=>(o.sequence||0)-(d.sequence||0)),[a]=t.splice(c.index,1);t.splice(n.index,0,a);const i=t.map((o,d)=>({...o,sequence:d+1}));K(i);const l={};i.forEach(o=>{l[o.id]=!0}),B(o=>({...o,...l}))}},f=(c,n)=>{if(U==="section"){const t=c.droppableId,a=n.droppableId;if(t===a){const l=G.filter(r=>(r.section||"General")===t).sort((r,M)=>(r.secKpiSequence||0)-(M.secKpiSequence||0)),[o]=l.splice(c.index,1);l.splice(n.index,0,o);const d=G.map(r=>{if((r.section||"General")===t){const M=l.findIndex(w=>w.id===r.id);return{...r,secKpiSequence:M+1}}return r});H(d);const C={};l.forEach(r=>{C[r.id]=!0}),Z(r=>({...r,...C}))}}else{const t=[...G].sort((o,d)=>(o.sequence||0)-(d.sequence||0)),[a]=t.splice(c.index,1);t.splice(n.index,0,a);const i=t.map((o,d)=>({...o,sequence:d+1}));H(i);const l={};i.forEach(o=>{l[o.id]=!0}),Z(o=>({...o,...l}))}},T=u.useCallback(()=>{if((x==null?void 0:x.length)>0){const c=x.map(t=>{var i;const a=m==null?void 0:m.find(l=>l.KpiId===t.MDG_KPI_ID);return{id:t.MDG_KPI_ID,prefId:(a==null?void 0:a.Id)||null,name:t.MDG_KPI_NAME,enabled:String(t.MDG_KPI_VISIBILITY).toLowerCase()==="true",userEnabled:a?a.KpiVisibility===!0&&a.IsActive===!0:!1,sequence:(a==null?void 0:a.KpiSequence)||t.MDG_KPI_GRAPH_SEQUENCE||0,sectionSequence:(a==null?void 0:a.SectionSequence)||0,secKpiSequence:(a==null?void 0:a.SecKpiSequence)||0,chartType:(a==null?void 0:a.KpiChartType)||t.MDG_KPI_GRAPH_TYPE,column:(i=t.MDG_KPI_GRAPH_COLUMN)==null?void 0:i.toLowerCase(),colorPallet:(a==null?void 0:a.KpiColPallet)||t.MDG_KPI_COLOR_PALLET,section:t.MDG_KPI_SECTION_NAME||"General"}}),n=h.map(t=>{const a=m==null?void 0:m.find(i=>i.KpiId===t.MDG_KPI_ID);return{id:t.MDG_KPI_ID,prefId:(a==null?void 0:a.Id)||null,name:t.MDG_KPI_NAME,enabled:["true","enabled"].includes(String(t.MDG_KPI_VISIBILITY).toLowerCase()),userEnabled:a?a.KpiVisibility===!0&&a.IsActive===!0:!0,sequence:(a==null?void 0:a.KpiSequence)||t.MDG_KPI_GRAPH_SEQUENCE||0,sectionSequence:(a==null?void 0:a.SectionSequence)||0,secKpiSequence:(a==null?void 0:a.SecKpiSequence)||t.MDG_KPI_GRAPH_SEQUENCE||0,section:t.MDG_KPI_SECTION_NAME||"General"}});K(c),H(n),B({}),Z({}),$({}),v({}),b(!1)}},[x,m,h]);u.useEffect(()=>{s?(b(!0),T()):A(0)},[s,T]);const le=c=>{K(n=>n.map(t=>t.id===c?{...t,userEnabled:!t.userEnabled}:t)),$(n=>({...n,[c]:!0})),B(n=>({...n,[c]:!0}))},me=c=>{H(n=>n.map(t=>t.id===c?{...t,userEnabled:!t.userEnabled}:t)),v(n=>({...n,[c]:!0})),Z(n=>({...n,[c]:!0}))},fe=(c,n)=>{K(t=>t.map(a=>a.id===c?{...a,chartType:n}:a)),B(t=>({...t,[c]:!0}))},De=(c,n)=>{K(t=>t.map(a=>a.id===c?{...a,colorPallet:n}:a)),B(t=>({...t,[c]:!0}))},S=async()=>{q(!0);try{const c=Object.keys(L),n=Object.keys(J);if(c.length===0&&n.length===0){p();return}const t=y.filter(r=>c.includes(r.id)).map(r=>({Id:r.prefId,UserId:W,KpiId:r.id,KpiType:"KPI Metrics",KpiChartType:r.chartType,KpiChartName:r.name,KpiColPallet:r.colorPallet,KpiSequence:Number(r.sequence),SectionSequence:Number(r.sectionSequence),SecKpiSequence:Number(r.secKpiSequence),KpiColumn:r.column,KpiVisibility:r.userEnabled,IsActive:r.userEnabled})),a=G.filter(r=>n.includes(r.id)).map(r=>({Id:r.prefId,UserId:W,KpiId:r.id,KpiType:"KPI Reports",KpiChartType:"REPORT",KpiChartName:r.name,KpiColPallet:"",KpiSequence:Number(r.sequence),SectionSequence:Number(r.sectionSequence),SecKpiSequence:Number(r.secKpiSequence),KpiColumn:"",KpiVisibility:r.userEnabled,IsActive:r.userEnabled})),i=[...t,...a];await new Promise((r,M)=>{oe(`/${ie}${be.DASHBOARD_APIS.SAVE_USER_CONFIG}`,"post",r,M,i)});const l=Object.keys(R),o=Object.keys(V),d=c.filter(r=>!l.includes(r)),C=l.length>0||o.length>0;!C&&g&&d.length>0&&g(d),I?I(c,n,C):p()}catch{p()}finally{q(!1)}};return D(S2,{open:s,onClose:p,fullWidth:!0,maxWidth:"md",children:[e(I2,{children:P("Manage Dashboard")}),e(x2,{dividers:!0,children:F?e(j,{sx:{display:"flex",justifyContent:"center",p:3},children:e(Te,{})}):D(F2,{onDragEnd:Q,children:[D(j,{sx:{mb:2,display:"flex",justifyContent:"space-between",alignItems:"center"},children:[e(O,{variant:"h6",children:P("Graph Sequencing")}),D(j2,{value:U,exclusive:!0,onChange:(c,n)=>{n!==null&&N(n)},"aria-label":"sequencing mode",size:"small",children:[e(Je,{value:"section","aria-label":"section-based",children:P("Section-Based")}),e(Je,{value:"overall","aria-label":"overall",children:P("Overall Sequence")})]})]}),e(j,{sx:{borderBottom:1,borderColor:"divider",mb:2},children:D(_2,{value:E,onChange:Y,"aria-label":"dashboard management tabs",children:[e(We,{label:P("KPI Metrics"),id:"dashboard-tab-0","aria-controls":"dashboard-tabpanel-0"}),e(We,{label:P("KPI Reports"),id:"dashboard-tab-1","aria-controls":"dashboard-tabpanel-1"})]})}),e(s2,{value:E,index:0,children:U==="section"?(()=>{const c=y.reduce((t,a)=>{const i=a.section||"General";return t[i]||(t[i]=[]),t[i].push(a),t},{});return Object.keys(c).sort((t,a)=>{var o,d;const i=((o=y.find(C=>C.section===t))==null?void 0:o.sectionSequence)||0,l=((d=y.find(C=>C.section===a))==null?void 0:d.sectionSequence)||0;return i-l}).map(t=>D(j,{sx:{mb:3},children:[D(O,{variant:"h6",sx:{mb:2,color:"primary.main",fontWeight:"bold"},children:[P(t)," ",P("Section")]}),e(Ge,{droppableId:t,type:"KPI_METRICS",children:a=>D(ve,{...a.droppableProps,ref:a.innerRef,children:[c[t].sort((i,l)=>(i.secKpiSequence||0)-(l.secKpiSequence||0)).map((i,l)=>e(Ne,{draggableId:i.id,index:l,children:(o,d)=>D(qe.Fragment,{children:[l>0&&e(Be,{}),e(we,{ref:o.innerRef,...o.draggableProps,sx:{backgroundColor:d.isDragging?"rgba(0, 0, 0, 0.1)":l%2===0?"transparent":"rgba(0, 0, 0, 0.02)",borderRadius:1,mb:1,border:"1px solid",borderColor:"divider"},children:D(_,{container:!0,spacing:2,alignItems:"center",children:[e(_,{item:!0,xs:1,sx:{textAlign:"center"},children:e(O,{variant:"h6",color:"text.secondary",sx:{fontWeight:"bold"},children:l+1})}),e(_,{item:!0,xs:1,children:e(Ie,{size:"small",sx:{cursor:"grab"},...o.dragHandleProps,children:e(Oe,{})})}),e(_,{item:!0,xs:3,children:e(O,{variant:"body1",sx:{fontWeight:i.enabled?"normal":"light",color:i.enabled?"text.primary":"text.disabled",p:1},children:P(i.name)})}),e(_,{item:!0,xs:2,children:e(Se,{fullWidth:!0,size:"small",children:e(Le,{value:i.chartType,onChange:C=>fe(i.id,C.target.value),displayEmpty:!0,sx:{"& .MuiSelect-select":{fontWeight:i.enabled?"normal":"light",color:i.enabled?"text.primary":"text.disabled"}},children:a2(i.id,x).map(C=>e(Re,{value:C.value,children:C.label},C.value))})})}),D(_,{item:!0,xs:2,sx:{textAlign:"center"},children:[e(O,{variant:"body2",color:"text.secondary",sx:{mb:.5},children:"Enabled"}),e(Pe,{checked:i.userEnabled,onChange:()=>le(i.id),disabled:!i.enabled,size:"small"})]}),e(_,{item:!0,xs:3,children:e(Se,{fullWidth:!0,size:"small",children:e(Le,{value:i.colorPallet||"default",onChange:C=>De(i.id,C.target.value),renderValue:C=>C||"Color Palette",displayEmpty:!0,children:r2(i.id,x).map(C=>e(Re,{value:C.value,children:C.label},C.value))})})})]})})]})},i.id)),a.placeholder]})})]},t))})():e(Ge,{droppableId:"overall-metrics",type:"KPI_METRICS",children:c=>D(ve,{...c.droppableProps,ref:c.innerRef,children:[y.sort((n,t)=>(n.sequence||0)-(t.sequence||0)).map((n,t)=>e(Ne,{draggableId:n.id,index:t,children:(a,i)=>D(qe.Fragment,{children:[t>0&&e(Be,{}),e(we,{ref:a.innerRef,...a.draggableProps,sx:{backgroundColor:i.isDragging?"rgba(0, 0, 0, 0.1)":t%2===0?"transparent":"rgba(0, 0, 0, 0.02)",borderRadius:1,mb:1,border:"1px solid",borderColor:"divider"},children:D(_,{container:!0,spacing:2,alignItems:"center",children:[e(_,{item:!0,xs:1,sx:{textAlign:"center"},children:e(O,{variant:"h6",color:"text.secondary",sx:{fontWeight:"bold"},children:t+1})}),e(_,{item:!0,xs:1,children:e(Ie,{size:"small",sx:{cursor:"grab"},...a.dragHandleProps,children:e(Oe,{})})}),e(_,{item:!0,xs:3,children:e(O,{variant:"body1",sx:{fontWeight:n.enabled?"normal":"light",color:n.enabled?"text.primary":"text.disabled",p:1},children:P(n.name)})}),e(_,{item:!0,xs:2,children:e(Se,{fullWidth:!0,size:"small",children:e(Le,{value:n.chartType,onChange:l=>fe(n.id,l.target.value),displayEmpty:!0,sx:{"& .MuiSelect-select":{fontWeight:n.enabled?"normal":"light",color:n.enabled?"text.primary":"text.disabled"}},children:a2(n.id,x).map(l=>e(Re,{value:l.value,children:l.label},l.value))})})}),D(_,{item:!0,xs:2,sx:{textAlign:"center"},children:[e(O,{variant:"body2",color:"text.secondary",sx:{mb:.5},children:"Enabled"}),e(Pe,{checked:n.userEnabled,onChange:()=>le(n.id),disabled:!n.enabled,size:"small"})]}),e(_,{item:!0,xs:3,children:e(Se,{fullWidth:!0,size:"small",children:e(Le,{value:n.colorPallet||"default",onChange:l=>De(n.id,l.target.value),renderValue:l=>l||"Color Palette",displayEmpty:!0,children:r2(n.id,x).map(l=>e(Re,{value:l.value,children:l.label},l.value))})})})]})})]})},n.id)),c.placeholder]})})}),e(s2,{value:E,index:1,children:U==="section"?(()=>{const c=G.reduce((t,a)=>{const i=a.section||"General";return t[i]||(t[i]=[]),t[i].push(a),t},{});return Object.keys(c).sort((t,a)=>{var o,d;const i=((o=G.find(C=>C.section===t))==null?void 0:o.sectionSequence)||0,l=((d=G.find(C=>C.section===a))==null?void 0:d.sectionSequence)||0;return i-l}).map(t=>D(j,{sx:{mb:3},children:[D(O,{variant:"h6",sx:{mb:2,color:"primary.main",fontWeight:"bold"},children:[P(t)," ",P("Reports")]}),e(Ge,{droppableId:t,type:"KPI_REPORTS",children:a=>D(ve,{...a.droppableProps,ref:a.innerRef,children:[c[t].sort((i,l)=>(i.secKpiSequence||0)-(l.secKpiSequence||0)).map((i,l)=>e(Ne,{draggableId:i.id,index:l,children:(o,d)=>D(qe.Fragment,{children:[l>0&&e(Be,{}),e(we,{ref:o.innerRef,...o.draggableProps,sx:{backgroundColor:d.isDragging?"rgba(0, 0, 0, 0.1)":l%2===0?"transparent":"rgba(0, 0, 0, 0.02)",borderRadius:1,mb:1,border:"1px solid",borderColor:"divider"},children:D(_,{container:!0,spacing:2,alignItems:"center",children:[e(_,{item:!0,xs:1,sx:{textAlign:"center"},children:e(O,{variant:"h6",color:"text.secondary",sx:{fontWeight:"bold"},children:l+1})}),e(_,{item:!0,xs:1,children:e(Ie,{size:"small",sx:{cursor:"grab"},...o.dragHandleProps,children:e(Oe,{})})}),e(_,{item:!0,xs:8,children:e(O,{variant:"body1",sx:{fontWeight:i.enabled?"normal":"light",color:i.enabled?"text.primary":"text.disabled",p:1},children:P(i.name)})}),D(_,{item:!0,xs:2,sx:{textAlign:"center"},children:[e(O,{variant:"body2",color:"text.secondary",sx:{mb:.5},children:"Enabled"}),e(Pe,{checked:i.userEnabled,onChange:()=>me(i.id),disabled:!i.enabled,size:"small"})]})]})})]})},i.id)),a.placeholder]})})]},t))})():e(Ge,{droppableId:"overall-reports",type:"KPI_REPORTS",children:c=>D(ve,{...c.droppableProps,ref:c.innerRef,children:[G.sort((n,t)=>(n.sequence||0)-(t.sequence||0)).map((n,t)=>e(Ne,{draggableId:n.id,index:t,children:(a,i)=>D(qe.Fragment,{children:[t>0&&e(Be,{}),e(we,{ref:a.innerRef,...a.draggableProps,sx:{backgroundColor:i.isDragging?"rgba(0, 0, 0, 0.1)":t%2===0?"transparent":"rgba(0, 0, 0, 0.02)",borderRadius:1,mb:1,border:"1px solid",borderColor:"divider"},children:D(_,{container:!0,spacing:2,alignItems:"center",children:[e(_,{item:!0,xs:1,sx:{textAlign:"center"},children:e(O,{variant:"h6",color:"text.secondary",sx:{fontWeight:"bold"},children:t+1})}),e(_,{item:!0,xs:1,children:e(Ie,{size:"small",sx:{cursor:"grab"},...a.dragHandleProps,children:e(Oe,{})})}),e(_,{item:!0,xs:8,children:e(O,{variant:"body1",sx:{fontWeight:n.enabled?"normal":"light",color:n.enabled?"text.primary":"text.disabled",p:1},children:P(n.name)})}),D(_,{item:!0,xs:2,sx:{textAlign:"center"},children:[e(O,{variant:"body2",color:"text.secondary",sx:{mb:.5},children:"Enabled"}),e(Pe,{checked:n.userEnabled,onChange:()=>me(n.id),disabled:!n.enabled,size:"small"})]})]})})]})},n.id)),c.placeholder]})})})]})}),D($2,{children:[e(Me,{onClick:p,disabled:k,children:P("Cancel")}),e(Me,{onClick:S,variant:"contained",color:"primary",disabled:k||Object.keys(L).length===0&&Object.keys(J).length===0,children:P(k?"Saving...":"Save Changes")})]})]})},Ye=s=>["First","Second","Third"][s%3],ke=(s,p="metrics")=>{const I={};return s.forEach(x=>{const m=x.MDG_KPI_SECTION_NAME||"General";I[m]||(I[m]=[]),I[m].push(x)}),I},$e=(s,p)=>{const I={};return s.forEach(x=>{const m=p.find(g=>g.MDG_KPI_ID===x.id||g.MDG_KPI_ID===`KPI_${x.id}`),h=(m==null?void 0:m.MDG_KPI_SECTION_NAME)||"General";I[h]||(I[h]=[]),I[h].push(x)}),I},i2=(s,p,I,x=null,m=0)=>{var H,F,b,k,q;if(!s||typeof s!="object"||Array.isArray(s))return null;const h=(H=s==null?void 0:s.graphDetails)==null?void 0:H.chartType,g=(b=(F=h==null?void 0:h.toString())==null?void 0:F.trim())==null?void 0:b.toUpperCase(),E=((k=s==null?void 0:s.graphDetails)==null?void 0:k.graphName)||"Untitled Chart",A=((q=s==null?void 0:s.graphDetails)==null?void 0:q.color)||"Pallet 1",y=(s==null?void 0:s.Sequence)||m||0,K=(s==null?void 0:s.id)||p,G=(s==null?void 0:s.column)||Ye(K);if(!g||!E)return null;if(["PIE","DONUT"].includes(g)){const L=s==null?void 0:s.label,B=s==null?void 0:s.series;return!Array.isArray(L)||!Array.isArray(B)?null:{id:K,column:G,kpiId:x,GraphSequence:y,graphDetails:{chartType:g,graphName:E,colorPallete:A,KpiSequence:y},label:L,series:B}}return Array.isArray(s==null?void 0:s.data)?{id:K,column:G,kpiId:x,GraphSequence:y,graphDetails:{chartType:g,graphName:E,colorPallete:A,KpiSequence:y},data:s.data}:null},Dt=()=>{const{customError:s}=z2(),[p,I]=u.useState([]),[x,m]=u.useState(!0),[h,g]=u.useState([]),[E,A]=u.useState([]),[y,K]=u.useState([]),[G,H]=u.useState({}),[F,b]=u.useState([]),[k,q]=u.useState({}),[L,B]=u.useState({}),[J,Z]=u.useState({}),[R,$]=u.useState(!1),V=te(n=>n.commonFilter.Dashboard),v=te(n=>{var t;return((t=n.commonFilter)==null?void 0:t.Dashboard)||{}});te(n=>n.applicationConfig);const U=te(n=>n.userManagement.userData),N=(U==null?void 0:U.user_id)||"";let P=te(n=>n.userManagement.roles);const ee=P.join(", "),W=()=>P&&Array.isArray(P)&&P.length>0&&P.some(n=>n&&n.trim()!==""),Y=u.useRef(!1),Q=u.useRef(!1),Ce=u.useCallback(async()=>{if(!Q.current&&W()){Q.current=!0,m(!0),Y.current=!1;try{const n=await f("KPI Metrics"),t=await f("KPI Reports"),a=await T(n,"KPI Metrics"),i=await T(t,"KPI Reports"),l=await me(n,a),o=ke(n),d=ke(t),C=$e(l,n);b(i),I(l),q(C),A(t),B(d),K([...a,...i]),Y.current=!0}catch(n){s(re.DASHBOARD_REFRESH_FAILED,n)}finally{Q.current=!1,m(!1)}}},[P]),f=async(n="KPI Metrics")=>{if(!W())return[];const t={dtName:"MDG_MAT_DYNAMIC_DASHBOARD_DT",version:"v6",region:"US",role:`${ee}`,kpiType:n};let a="";n==="KPI Metrics"?a=`/${ie}${be.DASHBOARD_APIS.FETCH_DECISION_TABLE_METRICS}`:a=`/${ie}${be.DASHBOARD_APIS.FETCH_DECISION_TABLE_REPORTS}`;try{const i=await new Promise((l,o)=>{oe(a,"post",d=>{const C=(d==null?void 0:d.body)||[];l(C)},o,t)});return n==="KPI Metrics"?g(i):A(i),i}catch(i){return s(n==="KPI Metrics"?re.DECISION_TABLE_FETCH_ERROR:re.REPORT_CONFIG_FETCH_ERROR,i),[]}},T=u.useCallback(async(n,t)=>{try{let a=await new Promise(i=>{oe(`/${ie}${be.DASHBOARD_APIS.FETCH_USER_CONFIG}?userId=${N}&kpiType=${encodeURIComponent(t)}`,"get",l=>i((l==null?void 0:l.body)||[]),()=>i([]))});if(!a.length&&n.length){const i=n.map(l=>{var o;return{Id:null,UserId:N,KpiId:l.MDG_KPI_ID,KpiChartType:t==="KPI Metrics"?l.MDG_KPI_GRAPH_TYPE:"REPORT",KpiChartName:l.MDG_KPI_NAME,KpiColPallet:t==="KPI Metrics"?l.MDG_KPI_COLOR_PALLET:"",KpiSequence:Number(l.MDG_KPI_GRAPH_SEQUENCE),SectionSequence:0,SecKpiSequence:Number(l.MDG_KPI_GRAPH_SEQUENCE),KpiColumn:(o=l.MDG_KPI_GRAPH_COLUMN)==null?void 0:o.toLowerCase(),KpiVisibility:!0,IsActive:!0,KpiType:t}});await new Promise((l,o)=>{oe(`/${ie}${be.DASHBOARD_APIS.SAVE_USER_CONFIG}`,"post",l,o,i)}),a=await new Promise(l=>{oe(`/${ie}${be.DASHBOARD_APIS.FETCH_USER_CONFIG}?userId=${N}&kpiType=${encodeURIComponent(t)}`,"get",o=>l((o==null?void 0:o.body)||[]),()=>l(i))})}return a}catch(a){return s(re.USER_PREFERENCES_FETCH_ERROR,a),[]}},[N,s]),le=u.useCallback(async(n,t,a)=>{var i,l,o;try{const d=t.find(z=>z.MDG_KPI_ID===n);if(!d||!d.MDG_KPI_ENDPOINT)return null;const C=a.find(z=>z.KpiId===n),r=d.MDG_KPI_ENDPOINT.replace(/^\//,""),M=parseInt(n.split("_")[1])-1,w={FromDate:"2024-01-01",ToDate:"2025-12-31",Requestor:"",KpiId:n,Module:Ue(V==null?void 0:V.dashBoardModuleName)||ye[0],UserId:N,Priority:"",Region:(v==null?void 0:v.selectedRegion)||"",ReqType:((i=v==null?void 0:v.selectedRequestType)==null?void 0:i.join(","))||"",ReqStatus:((l=v==null?void 0:v.selectedRequestStatus)==null?void 0:l.join(","))||"",GraphType:(C==null?void 0:C.KpiChartType)||(d==null?void 0:d.MDG_KPI_GRAPH_TYPE)||"",KpiName:(C==null?void 0:C.KpiChartName)||(d==null?void 0:d.MDG_KPI_NAME)||"",ColPallet:(C==null?void 0:C.KpiColPallet)||(d==null?void 0:d.MDG_KPI_COLOR_PALLET)||"",GraphColumn:(C==null?void 0:C.KpiColumn)||((o=d==null?void 0:d.MDG_KPI_GRAPH_COLUMN)==null?void 0:o.toLowerCase())||Ye(M),GraphSequence:(C==null?void 0:C.KpiSequence)||Number(d==null?void 0:d.MDG_KPI_GRAPH_SEQUENCE)||M+1};return new Promise(z=>{oe(`/${ie}/counts/${r}`,"post",X=>z(i2(X.body,M+1,M,n,w.GraphSequence)),()=>z(null),w)})}catch(d){return s(re.GRAPH_DATA_FETCH_ERROR,d),null}},[V,N,v,s]),me=async(n,t)=>{if(!n.length)return[];try{const a={};n.forEach(r=>{r.MDG_KPI_ID&&r.MDG_KPI_ENDPOINT&&(a[r.MDG_KPI_ID]=r.MDG_KPI_ENDPOINT.replace(/^\//,""))});const i=n.filter(r=>["TRUE","ENABLED"].includes((r.MDG_KPI_VISIBILITY||"").toString().toUpperCase())).map(r=>r.MDG_KPI_ID),l=t.filter(r=>r.KpiVisibility===!0&&r.IsActive===!0).map(r=>r.KpiId),o=l.length>0?i.filter(r=>l.includes(r)):i,d={},C=await Promise.all(o.map(r=>{var ue,ce,Ke;const M=t.find(de=>de.KpiId===r),w=n.find(de=>de.MDG_KPI_ID===r),z=a[r];if(!z)return Promise.resolve(null);const X=parseInt(r.split("_")[1])-1,ne={FromDate:"2024-01-01",ToDate:"2025-12-31",Requestor:"",KpiId:r,Module:Ue(V==null?void 0:V.dashBoardModuleName)||ye[0],UserId:N,Priority:"",Region:(v==null?void 0:v.selectedRegion)||"",ReqType:((ue=v==null?void 0:v.selectedRequestType)==null?void 0:ue.join(","))||"",ReqStatus:((ce=v==null?void 0:v.selectedRequestStatus)==null?void 0:ce.join(","))||"",GraphType:(M==null?void 0:M.KpiChartType)||(w==null?void 0:w.MDG_KPI_GRAPH_TYPE)||"",KpiName:(M==null?void 0:M.KpiChartName)||(w==null?void 0:w.MDG_KPI_NAME)||"",ColPallet:(M==null?void 0:M.KpiColPallet)||(w==null?void 0:w.MDG_KPI_COLOR_PALLET)||"",GraphColumn:(M==null?void 0:M.KpiColumn)||((Ke=w==null?void 0:w.MDG_KPI_GRAPH_COLUMN)==null?void 0:Ke.toLowerCase())||Ye(X),GraphSequence:(M==null?void 0:M.KpiSequence)||Number(w==null?void 0:w.MDG_KPI_GRAPH_SEQUENCE)||X+1};return d[r]=ne,new Promise(de=>{oe(`/${ie}/counts/${z}`,"post",K2=>de(i2(K2.body,X+1,X,r,ne.GraphSequence)),()=>de(null),ne)})}));return H(d),C.filter(Boolean)}catch(a){return s(re.GRAPH_DATA_FETCH_ERROR,a),[]}},fe=async()=>{if(!(Q.current||Y.current)&&W()){Q.current=!0,m(!0);try{const n=await f("KPI Metrics"),t=await f("KPI Reports"),a=await T(n,"KPI Metrics"),i=await T(t,"KPI Reports"),l=await me(n,a),o=ke(t),d=$e(l,n);I(l),q(d),b(i),A(t),B(o),K([...a,...i]),Y.current=!0}catch(n){s(re.DASHBOARD_INIT_FAILED,n)}finally{Q.current=!1,m(!1)}}};u.useEffect(()=>{(N||N!=="")&&fe()},[N]),u.useEffect(()=>{W()&&N&&!Y.current&&!Q.current&&fe()},[P,N]),u.useEffect(()=>{(async()=>{if(!(!Y.current||Q.current)){Q.current=!0,m(!0);try{const t=await me(h,y),a=$e(t,h);I(t),q(a)}catch(t){s(re.FILTER_CHANGE_UPDATE_FAILED,t)}finally{Q.current=!1,m(!1)}}})()},[v]);const De=u.useCallback(async n=>{if(!h.length||!y.length)return;const t=y.find(o=>o.KpiId===n),a=h.find(o=>o.MDG_KPI_ID===n),i=a&&["TRUE","ENABLED"].includes((a.MDG_KPI_VISIBILITY||"").toString().toUpperCase()),l=t&&t.KpiVisibility===!0&&t.IsActive===!0;if(!i||!l){I(o=>o.filter(d=>d.kpiId!==n)),q(o=>{const d={...o};return Object.keys(d).forEach(C=>{d[C]=d[C].filter(r=>r.kpiId!==n)}),d});return}Z(o=>({...o,[n]:!0}));try{const o=await le(n,h,y);o&&(I(d=>{const C=d.findIndex(r=>r.kpiId===n);if(C>=0){const r=[...d];return r[C]=o,r}else return[...d,o]}),q(d=>{const C=h.find(z=>z.MDG_KPI_ID===n),r=(C==null?void 0:C.MDG_KPI_SECTION_NAME)||"General",M={...d};M[r]||(M[r]=[]);const w=M[r].findIndex(z=>z.kpiId===n);return w>=0?M[r][w]=o:M[r].push(o),M}))}catch(o){s(re.GRAPH_DATA_FETCH_ERROR,o)}finally{Z(o=>({...o,[n]:!1}))}},[h,y,le,s]),S=u.useCallback(async n=>{if(!(!h.length||!n.length))try{const t=await T(h,"KPI Metrics");K(r=>{const M=r.filter(w=>w.KpiType==="KPI Reports");return[...t,...M]});const a=h.filter(r=>["TRUE","ENABLED"].includes((r.MDG_KPI_VISIBILITY||"").toString().toUpperCase())).map(r=>r.MDG_KPI_ID),i=t.filter(r=>r.KpiVisibility===!0&&r.IsActive===!0).map(r=>r.KpiId),l=i.length>0?a.filter(r=>i.includes(r)):a,o=n.filter(r=>l.includes(r)),d=n.filter(r=>!l.includes(r)),C={};if(o.forEach(r=>{C[r]=!0}),Z(r=>({...r,...C})),d.length>0&&(I(r=>r.filter(M=>!d.includes(M.kpiId))),q(r=>{const M={...r};return Object.keys(M).forEach(w=>{M[w]=M[w].filter(z=>!d.includes(z.kpiId))}),M})),o.length>0){const r=o.map(z=>le(z,h,t)),w=(await Promise.all(r)).filter(Boolean);w.length>0&&(I(z=>{let X=[...z];return w.forEach(ne=>{const ue=X.findIndex(ce=>ce.kpiId===ne.kpiId);ue>=0?X[ue]=ne:X.push(ne)}),X}),q(z=>{const X={...z};return w.forEach(ne=>{const ue=h.find(de=>de.MDG_KPI_ID===ne.kpiId),ce=(ue==null?void 0:ue.MDG_KPI_SECTION_NAME)||"General";X[ce]||(X[ce]=[]);const Ke=X[ce].findIndex(de=>de.kpiId===ne.kpiId);Ke>=0?X[ce][Ke]=ne:X[ce].push(ne)}),X}))}}catch(t){s(re.GRAPH_DATA_FETCH_ERROR,t)}finally{const t={};n.forEach(a=>{t[a]=!1}),Z(a=>({...a,...t}))}},[h,le,T,s]),c=u.useCallback(async()=>{if(W()){$(!0);try{const n=await f("KPI Reports"),t=await T(n,"KPI Reports"),a=ke(n);b(t),A(n),B(a),K(i=>[...i.filter(o=>o.KpiType==="KPI Metrics"),...t])}catch(n){s(re.DASHBOARD_INIT_FAILED,n)}finally{$(!1)}}},[W,f,T,s]);return{cards:p,reportConfig:E,loading:x,decisionTableConfig:h,userPreferences:y,kpiPayloadMap:G,kpiReportPrefs:F,refreshDashboard:Ce,sectionedCards:k,sectionedReports:L,graphLoadingStates:J,refreshSingleGraph:De,refreshMultipleGraphs:S,refreshReports:c,reportsLoading:R}},vt=()=>{const[s,p]=u.useState(!1),[I,x]=u.useState(!1),[m,h]=u.useState(!0),{cards:g,loading:E,decisionTableConfig:A,userPreferences:y,reportConfig:K,kpiReportPrefs:G,refreshDashboard:H,sectionedCards:F,sectionedReports:b,graphLoadingStates:k,refreshSingleGraph:q,refreshMultipleGraphs:L,refreshReports:B,reportsLoading:J}=Dt(),{t:Z}=xe(),R=u.useCallback((V=[],v=[],U=!1)=>{U?H():v.length>0&&V.length===0&&B(),x(!1)},[H,B]),$=u.useCallback(V=>{L(V)},[L]);return D(j,{sx:{height:"100vh",overflow:"hidden",display:"flex",flexDirection:"column"},children:[D(j,{sx:{position:"sticky",top:0,bgcolor:"background.default",p:2},children:[D(ge,{direction:"row",justifyContent:"space-between",alignItems:"center",children:[D(j,{children:[e(O,{variant:"h3",children:e("strong",{children:Z("Dashboard")})}),e(O,{variant:"body2",color:"text.secondary",children:Z("This view displays various metrics related to Master Data")})]}),D(ge,{direction:"column",spacing:1,children:[D(ge,{direction:"row",alignItems:"center",spacing:1,children:[e(Me,{variant:"outlined",startIcon:e(Y2,{}),color:"primary",onClick:()=>x(!0),size:"small",sx:{mr:"20px !important"},className:"manageDashBoard",children:Z("Manage Dashboard")}),D(j,{sx:{display:"flex",alignItems:"center"},className:"parentChildSwitchDB",children:[e(O,{variant:"body2",children:Z("KPI Metrics")}),e(Pe,{checked:s,onChange:()=>p(V=>!V),color:"primary"}),e(O,{variant:"body2",children:Z("KPI Reports")})]})]}),e(ge,{direction:"row",alignItems:"center",spacing:1,justifyContent:"flex-end",children:D(j,{sx:{display:"flex",alignItems:"center"},className:"tabbed",children:[e(O,{variant:"body2",children:Z("Tabbed View")}),e(Pe,{checked:m,onChange:()=>h(V=>!V),color:"primary"})]})})]})]}),!s&&D(j,{mt:2,children:[e(mt,{}),e(tt,{})]})]}),e(j,{sx:{flex:1,overflowY:"auto",p:2},children:m?e(Ct,{sectionedCards:F,sectionedReports:b,loading:E,reportsLoading:J,showReports:s,kpiReportPrefs:G,decisionTableConfig:A,userPreferences:y,graphLoadingStates:k,onRefreshGraph:q}):s?e(y2,{reportConfig:K,kpiReportPrefs:G,loading:J,isTabbed:!1,userPreferences:y}):e(P2,{cards:g,loading:E,graphLoadingStates:k,onRefreshGraph:q})}),e(ft,{open:I,onClose:()=>x(!1),onSave:R,onRefreshSpecificGraphs:$,decisionTableConfig:A,userPreferences:y,reportConfig:K})]})};export{vt as default};
