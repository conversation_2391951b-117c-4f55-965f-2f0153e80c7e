import{c as l,aj as Q,j as e,gq as R,F as B,d as k,f as Z,L as X,gr as ee,h as ae,al as te,an as H,am as se,ai as oe,aP as ne,u as le,s as re,r as D,n as F,aO as _,fM as ie,aW as ce,aG as de,B as T,aZ as ge,O as he,gs as ue,T as fe,a6 as $,a_ as me,R as pe,ak as be,b5 as xe,b6 as Ce,af as De,ds as Te,cq as Ee,gt as Se,gu as Le,gv as ye,C as Ie,aT as q,aJ as we}from"./index-f7d9b065.js";import{M as Ae}from"./createChangeLogTemplate-fc8912a0.js";const Oe=({open:E,onClose:b,missingFields:S=[],customMessage:L=null,title:i=null,buttonText:y="Close",t:h=c=>c})=>{const c=S.length>0&&!L,x=i||h(c?"Missing Mandatory Fields":"Validation Error");return l(oe,{open:E,onClose:b,"aria-labelledby":"flexible-validation-dialog-title",maxWidth:"sm",fullWidth:!0,children:[l(Q,{id:"flexible-validation-dialog-title",sx:{backgroundColor:"#fff3e0",color:"#e65100",display:"flex",alignItems:"center",gap:1,fontWeight:"bold"},children:[e(R,{fontSize:"medium"}),x]}),e(te,{sx:{pt:2},children:c?l(B,{children:[e(k,{variant:"body1",gutterBottom:!0,children:h("Please complete the following mandatory fields:")}),e(Z,{dense:!0,children:S.map((N,p)=>l(X,{disablePadding:!0,children:[e(ee,{sx:{minWidth:30},children:e(R,{fontSize:"small",color:"warning"})}),e(ae,{primary:N})]},p))})]}):e(k,{variant:"body1",gutterBottom:!0,children:L})}),e(se,{sx:{pr:3,pb:2},children:e(H,{onClick:b,variant:"contained",color:"warning",sx:{textTransform:"none",fontWeight:500},children:h(y)})})]})},Ge=({open:E,closeModal:b,requestId:S,requestType:L,module:i})=>{const{customError:y}=ne(),h=le(),c=re(),[x,N]=D.useState(!1);F(t=>{var s,o,r;switch(i){case((s=_)==null?void 0:s.BK):return(r=(o=t==null?void 0:t.bankKey)==null?void 0:o.payload)==null?void 0:r.requestHeaderData;default:return null}});const p=F(t=>{var s,o;switch(i){case((s=_)==null?void 0:s.BK):return(o=t==null?void 0:t.bankKey)==null?void 0:o.changeLogData;default:return null}}),[I,j]=D.useState(0),[n,U]=D.useState([]),w=new URLSearchParams(h.search).get("RequestId"),{destination:O}=ie(i),W=(t,s)=>{j(s)},z={position:"absolute",top:"50%",left:"52%",transform:"translate(-50%, -50%)",width:"80%",height:"auto",bgcolor:"#fff",boxShadow:4,p:2,borderRadius:"20px"},G=()=>{b(!1)},M=new Date;M.setDate(M.getDate()-15);const v={convertJsonToExcel:()=>{const t=new Date,s=n.map(o=>({sheetName:o.label,fileName:`Changelog Data-${Ee(t).format("DD-MMM-YYYY")}`,columns:o.columns.map(r=>({header:r.headerName,key:r.field})),rows:o.rows}));Se(s)},button:()=>e(H,{sx:{textTransform:"capitalize",position:"absolute",right:0,top:0},onClick:()=>v.convertJsonToExcel(),children:"Download"})},V=t=>{const o=(d=>{var g,C;switch(d){case _.IO:return`/${O}/${(g=q)==null?void 0:g.CHG_DISPLAY_REQUESTOR.FETCH_CHANGELOG_DATA_IO}`;default:return`/${O}/${(C=q)==null?void 0:C.CHG_DISPLAY_REQUESTOR.FETCH_CHANGELOG_DATA}`}})(i);let r={ChildRequestId:t};return new Promise((d,g)=>{Ie(o,"post",a=>{(a==null?void 0:a.statusCode)===we.STATUS_200?d((a==null?void 0:a.body)||[]):d([])},a=>{y(a),g(a)},r)})};return D.useEffect(()=>{const t=async()=>{var s;if(w)try{const d=(await V(w)).flatMap(a=>{const u=a==null?void 0:a.ChangeLogRows;return Array.isArray(u)?u:[]}).map((a,u)=>({...a,id:`${a.ViewName}-${a.FieldName}-${a.ChangedOn}-${u}`}));c(Le(d));const g=((s=Ae)==null?void 0:s[i])||{},P=Object.keys(g).map(a=>{const{fieldName:u,headerName:Y}=g[a],J=d.filter(f=>(f==null?void 0:f.ViewName)===a);return{label:a,columns:u.map((f,K)=>({field:f,headerName:Y[K],flex:1,align:"center",headerAlign:"center",renderCell:f.toLowerCase().includes("date")?A=>ye(A.value):A=>{const m=A.value;return typeof m=="object"&&(m!=null&&m.desc)?m.desc:m}})),rows:J}});U(P)}catch(o){console.error("Change log fetch failed:",o),showSnackbar("Failed to load change log data","error")}};p!=null&&p.length||t()},[w,c]),l(B,{children:[x&&e(de,{blurLoading:x,loaderMessage:ce.CHANGELOG_LOADING}),e(Te,{open:E,onClose:G,"aria-labelledby":"modal-modal-title","aria-describedby":"modal-modal-description",children:l(T,{sx:z,children:[e(ge,{children:l(he,{item:!0,md:12,sx:{display:"flex",alignItems:"center",justifyContent:"space-between"},children:[l(T,{sx:{display:"flex",alignItems:"center",gap:"8px"},children:[e(ue,{sx:{color:"black",fontSize:"20px","&:hover":{transform:"rotate(360deg)",transition:"0.9s"},textAlign:"center",marginTop:"4px"}}),e(k,{id:"modal-modal-title",variant:"subtitle1",fontSize:"16px",fontWeight:"bold",sx:{color:"black"},children:"Change Log"})]}),l(T,{sx:{display:"flex",alignItems:"center",gap:"8px"},children:[e(fe,{title:"Export Table",children:e($,{sx:me,onClick:v.convertJsonToExcel,children:e(pe,{iconName:"IosShare"})})}),e($,{sx:{padding:"0 0 0 5px"},onClick:G,children:e(be,{})})]})]})}),e(xe,{value:I,onChange:W,sx:{mt:2},children:n==null?void 0:n.map((t,s)=>e(Ce,{label:t.label},s))}),e(T,{sx:{mt:2},children:(n==null?void 0:n.length)>0&&e(De,{rows:n[I].rows,columns:n[I].columns,getRowIdValue:"id",autoHeight:!0,scrollbarSize:10,sx:{"& .MuiDataGrid-row:hover":{backgroundColor:"#EAE9FF40"},backgroundColor:"#fff"}})})]})})]})};export{Ge as C,Oe as F};
