import{r as m,c as i,j as e,fU as me,an as J,ai as xe,aj as be,d as u,ak as ye,a6 as G,al as Se,B,aZ as w,hZ as Ie,bD as se,F as ge,aM as oe,ae as ne,aK as Ce,n as ue,a as Ee,cq as De,qF as I,Z as g,bE as we,pG as ve,yI as Fe,yJ as _e,bG as Te,O,Q as Le,af as Ae,gf as Oe,gg as fe,gh as ke,C as Y,yK as Z,aT as q,aJ as ie,yL as Ue,yM as Re,aF as Pe}from"./index-f7d9b065.js";import{a as Me,d as Ne}from"./AttachFile-8d552da8.js";import{d as Be}from"./CloudUpload-0ba6431e.js";import{d as ze}from"./Delete-5278579a.js";import{i as $e}from"./utilityImages-067c3dc2.js";import{M as je,b as Ge}from"./UtilDoc-6f590135.js";import"./FileDownloadOutlined-59854a55.js";import"./VisibilityOutlined-b6cd6d28.js";import"./DeleteOutlined-e668453f.js";const He=({onFilesAdded:E=()=>{},maxFiles:z=5,maxFileSize:v=500,acceptedTypes:M=".jpeg, .jpg, .xls, .xlsx, .docx, .pdf",currentFileCount:k=0,loading:$=!1})=>{const[y,C]=m.useState(!1),[d,x]=m.useState([]),[U,F]=m.useState(!1),[R,_]=m.useState([]),[a,H]=m.useState(!1),[P,T]=m.useState(0),[D,W]=m.useState(!1),Q=()=>{F(!0),x([]),_([]),T(0)},N=()=>{a||(F(!1),x([]),_([]),T(0))},X=t=>{t.preventDefault(),C(!0)},b=()=>{C(!1)},V=t=>{var c;t.preventDefault(),C(!1);const l=Array.from(t.dataTransfer.files);if(d.length+l.length>5){oe((c=ne)==null?void 0:c.DOC_UPLOAD_FILES_LIMIT,"error");return}j(l)},j=t=>{let l=[];t.forEach(c=>{c.id=Ce(),l.push(c)}),x(c=>[...c,...l])},ee=()=>{a||document.getElementById("fileButton").click()},re=t=>{a||x(l=>l.filter(c=>c.id!==t))},te=async()=>{if(d.length!==0){H(!0),T(0);try{const t=setInterval(()=>{T(c=>c>=90?(clearInterval(t),90):c+10)},200),l=await E(d);clearInterval(t),T(100),setTimeout(()=>{N()},500)}catch{_(["Upload failed. Please try again."]),T(0)}finally{H(!1)}}};let ae=()=>{var l;let t=0;d.forEach(c=>{t+=c.length}),t>5e9?(oe((l=ne)==null?void 0:l.DOC_UPLOAD_SIZE_LIMIT,"error"),W(!0)):W(!1)};m.useEffect(()=>{ae()},[d]);const K=t=>{if(t===0)return"0 Bytes";const l=1024,c=["Bytes","KB","MB","GB"],L=Math.floor(Math.log(t)/Math.log(l));return parseFloat((t/Math.pow(l,L)).toFixed(2))+" "+c[L]};return i(ge,{children:[e(J,{className:"uploadDoc",variant:"contained",onClick:Q,startIcon:e(me,{}),disabled:$,sx:{color:"#fff",textTransform:"capitalize",borderRadius:"5px",padding:"10px 20px","&:disabled":{backgroundColor:"#ccc"},boxShadow:"0px 4px 10px rgba(0, 0, 0, 0.2)"},children:"Upload Files"}),i(xe,{fullWidth:!0,maxWidth:"sm",open:U,onClose:N,disableEscapeKeyDown:a,sx:{"& .MuiDialog-paper":{borderRadius:"12px",padding:"1rem"}},children:[i(be,{sx:{padding:"1rem 1.5rem"},children:[e(u,{variant:"h6",sx:{fontWeight:500},children:"Upload Files"}),e(G,{"aria-label":"close",onClick:N,disabled:a,sx:{position:"absolute",right:12,top:10,color:a?"#ccc":"#666"},children:e(ye,{})})]}),i(Se,{sx:{padding:"1.5rem"},children:[a&&i(B,{sx:{mb:2},children:[i(w,{direction:"row",justifyContent:"space-between",alignItems:"center",sx:{mb:1},children:[e(u,{variant:"body2",children:"Uploading files..."}),i(u,{variant:"body2",children:[P,"%"]})]}),e(Ie,{variant:"determinate",value:P,sx:{height:8,borderRadius:4,backgroundColor:"#e0e0e0","& .MuiLinearProgress-bar":{borderRadius:4}}})]}),R.length>0&&e(B,{sx:{mb:2},children:R.map((t,l)=>i(u,{variant:"body2",color:"error",sx:{mb:.5},children:["• ",t]},l))}),i(B,{sx:{width:"100%",border:`2px dashed ${y?"#3b30c8":"#d0d5dd"}`,borderRadius:"8px",padding:"2rem",backgroundColor:y?"#f8f9ff":"#fafbff",transition:"all 0.3s ease",cursor:a?"not-allowed":"pointer",minHeight:"200px",display:"flex",alignItems:"center",justifyContent:"center",opacity:a?.6:1},onDragOver:a?void 0:X,onDragLeave:a?void 0:b,onDrop:a?void 0:V,onClick:a?void 0:ee,children:[i(w,{alignItems:"center",spacing:1,children:[a?e(se,{sx:{color:"#3b30c8"}}):e(Be,{sx:{fontSize:48,color:"#3b30c8"}}),e(u,{variant:"body1",sx:{color:a?"#999":"#344054"},children:a?"Uploading...":"Drag and drop files here"}),!a&&i(ge,{children:[e(u,{variant:"body2",color:"primary",sx:{cursor:"pointer",textDecoration:"underline","&:hover":{color:"#3b30c8"}},children:"or click to browse"}),i(u,{variant:"caption",color:"textSecondary",children:["Max ",z," files, ",v,"MB each"]})]})]}),e("input",{id:"fileButton",multiple:!0,accept:M,type:"file",onChange:t=>{var c;const l=Array.from(t.target.files);if(d.length+l.length>5){oe((c=ne)==null?void 0:c.DOC_UPLOAD_FILES_LIMIT,"error");return}j(l),t.target.value=""},style:{display:"none"},disabled:a})]}),d.length>0&&i(B,{sx:{maxHeight:"200px",overflowY:"auto",marginTop:"1.5rem",padding:"1rem",backgroundColor:"#fff",borderRadius:"8px",border:"1px solid #eee"},children:[i(u,{variant:"subtitle2",sx:{mb:1,fontWeight:500},children:["Selected Files (",d.length,")"]}),e(w,{spacing:1,children:d==null?void 0:d.map(t=>{var l,c,L;return(c=(l=t==null?void 0:t.name)==null?void 0:l.split(".").pop())==null||c.toLowerCase(),i(B,{sx:{display:"flex",alignItems:"center",padding:"0.75rem",backgroundColor:"#f8f9fa",borderRadius:"6px",border:"1px solid #e9ecef"},children:[e("img",{style:{width:"24px",height:"24px",marginRight:"0.75rem"},src:$e[(L=t.name)==null?void 0:L.split(".")[1]]}),e(u,{variant:"body1",sx:{flexGrow:1},children:t.name}),e(B,{sx:{flex:1,minWidth:0},children:e(u,{variant:"caption",color:"textSecondary",children:K(t.size)})}),e(G,{size:"small",onClick:()=>re(t.id),disabled:a,sx:{color:a?"#ccc":"#666","&:hover":{color:a?"#ccc":"#d32f2f",backgroundColor:a?"transparent":"rgba(211, 47, 47, 0.04)"}},children:e(ze,{fontSize:"small"})})]},t.id)})})]}),i(w,{direction:"row",spacing:2,justifyContent:"flex-end",sx:{mt:3},children:[e(J,{variant:"outlined",onClick:N,disabled:a,sx:{color:a?"#ccc":"#666",borderColor:a?"#ccc":"#ddd"},children:"Cancel"}),e(J,{variant:"contained",onClick:te,disabled:d.length===0||a,startIcon:a?e(se,{size:16,color:"inherit"}):void 0,sx:{backgroundColor:"#1976d2","&:hover":{backgroundColor:"#1565c0"},"&:disabled":{backgroundColor:"#ccc"}},children:a?`Uploading... ${P}%`:`Upload ${d.length} file${d.length!==1?"s":""}`})]})]})]})]})},We=E=>{var M,k,$,y,C,d,x,U,F,R,_,a;const z=((k=(M=E==null?void 0:E.split("."))==null?void 0:M.pop())==null?void 0:k.toLowerCase())||"",v={fontSize:"small",sx:{mr:1}};switch(z){case"xlsx":case"xls":case"csv":return e(Me,{sx:{color:(y=($=g)==null?void 0:$.secondary)==null?void 0:y.dark}});case"pdf":return e(ke,{...v,sx:{color:(d=(C=g)==null?void 0:C.error)==null?void 0:d.dark}});case"doc":case"docx":return e(fe,{...v,sx:{color:(U=(x=g)==null?void 0:x.primary)==null?void 0:U.lightPlus}});case"ppt":case"pptx":return e(fe,{...v,sx:{color:(R=(F=g)==null?void 0:F.secondary)==null?void 0:R.amber}});default:return e(Oe,{...v,sx:{color:(a=(_=g)==null?void 0:_.secondary)==null?void 0:a.grey}})}},tr=({onFilesChange:E=()=>{},maxFiles:z=5,maxFileSize:v=500,acceptedTypes:M=".jpeg, .jpg, .xls, .xlsx, .docx, .pdf",disabled:k=!1,title:$=""})=>{var K,t,l,c,L,le,ce,de,he,pe;const[y,C]=m.useState([]),[d,x]=m.useState(!1),[U,F]=m.useState(!1),[R,_]=m.useState(""),[a,H]=m.useState("success");m.useState({});const P=ue(r=>r.userManagement.userData),T=ue(r=>r.appSettings),{t:D}=Ee(),W=async()=>new Promise((r,o)=>{var h,f;const s=p=>{r(p)},n=p=>{r(p)};Y(`/${Z}${(f=(h=q)==null?void 0:h.DOCUMENT_CONFIGURATION_APIS)==null?void 0:f.GET_FILES_LIST_API}?adminEmail=${P==null?void 0:P.emailId}`,"get",s,n)}),Q=async r=>new Promise((o,s)=>{var p,S;const n=new FormData;r.forEach((A,Ke)=>{n.append("file",A)});const h=A=>{o(A)},f=A=>{o(data)};Y(`/${Z}${(S=(p=q)==null?void 0:p.DOCUMENT_CONFIGURATION_APIS)==null?void 0:S.UPLOAD_FILES_API}`,"postformdata",h,f,n)}),N=async r=>new Promise((o,s)=>{var f,p;const n=S=>{o(S)},h=S=>{o(S)};Y(`/${Z}${(p=(f=q)==null?void 0:f.DOCUMENT_CONFIGURATION_APIS)==null?void 0:p.DELETE_FILE_API}?documentId=${r}`,"get",n,h)}),X=async(r,o)=>new Promise((s,n)=>{var p,S;const h=A=>{s(A)},f=A=>{s(A)};Y(`/${Z}${(S=(p=q)==null?void 0:p.DOCUMENT_CONFIGURATION_APIS)==null?void 0:S.UPDATE_VISIBILITY_API}/${r}?visibility=${o}`,"post",h,f)}),b=(r,o="success")=>{_(r),H(o),F(!0)},V=()=>{F(!1)};m.useEffect(()=>{j()},[]);const j=async()=>{var r;x(!0);try{const o=await W();if(((r=o==null?void 0:o.responseMessage)==null?void 0:r.status)==="Success"){const s=o.documentDetailDtoList.map(n=>({id:n.documentId,docType:n.fileType,docName:n.fileName,uploadedOn:De(n.docCreationDate).format(T.dateFormat),uploadedBy:n.createdBy,attachmentType:n.attachmentType,documentViewUrl:n.documentViewUrl,visibility:n.visibility||!1}));C(s),E(s)}else b(I.FILES.FETCH_ERROR,"error")}catch{b(I.FILES.FETCH_ERROR,"error")}finally{x(!1)}},ee=async r=>{var o;x(!0);try{const s=await Q(r);(s==null?void 0:s.statusCode)===((o=ie)==null?void 0:o.STATUS_202)?b(I.FILES.UPLOAD_SUCCESS(r.length),"success"):b(I.FILES.UPLOAD_FAILED,"error")}catch{b(I.FILES.UPLOAD_ERROR,"error")}finally{x(!1)}},re=async r=>{var o;x(!0);try{if((await N(r)).statusCode===((o=ie)==null?void 0:o.STATUS_200)){const n=y.filter(h=>h.id!==r);C(n),E(n),b(I.FILES.DELETE_SUCCESS,"success")}else b(I.FILES.DELETE_FAILED,"error")}catch{b(I.FILES.DELETE_ERROR,"error")}finally{x(!1)}},te=async(r,o)=>{var n;const s=!o;try{const h=await X(r,s);if((h==null?void 0:h.statusCode)===((n=ie)==null?void 0:n.STATUS_200)){const f=y.map(p=>p.id===r?{...p,visibility:s}:p);C(f),E(f),b(I.FILES.VISIBILITY_SUCCESS(s),"success")}else b(I.FILES.VISIBILITY_FAILED,"error")}catch{b(I.FILES.VISIBILITY_ERROR,"error")}},ae=[{field:"id",headerName:"Document ID",flex:1.2,hideable:!1,hidden:!0},{field:"attachmentType",headerName:D("Attachment Type"),flex:1.5,renderCell:r=>{var o;return e(we,{label:r.value,size:"small",sx:{backgroundColor:(o=g)==null?void 0:o.reportTile.lightBlue,color:g.primary.lightPlus,fontWeight:"medium"}})}},{field:"docName",headerName:D("Document Name"),flex:2,renderCell:r=>i(w,{direction:"row",spacing:1,alignItems:"center",children:[We(r.value),e(u,{variant:"body2",children:r.value})]})},{field:"uploadedOn",headerName:D("Uploaded On"),flex:1,align:"center",headerAlign:"center"},{field:"visibility",headerName:"Visibility",sortable:!1,filterable:!1,align:"center",headerAlign:"center",flex:1,renderCell:r=>{var o,s,n,h;return e(Te,{control:e(ve,{checked:r.value,onChange:()=>te(r.row.id,r.value),disabled:k||d,size:"small",color:"primary"}),label:i(w,{direction:"row",alignItems:"center",spacing:.5,children:[r.value?e(Fe,{fontSize:"small",sx:{color:(s=(o=g)==null?void 0:o.secondary)==null?void 0:s.green}}):e(_e,{fontSize:"small",sx:{color:(h=(n=g)==null?void 0:n.error)==null?void 0:h.dark}}),e(u,{variant:"caption",children:r.value?"Visible":"Hidden"})]}),sx:{margin:0}})}},{field:"action",headerName:D("Action"),sortable:!1,filterable:!1,align:"center",headerAlign:"center",flex:1,renderCell:r=>{var o,s,n,h,f,p,S;return i(w,{direction:"row",spacing:0,children:[e(G,{size:"small",sx:{color:g.icon.matView,"&:hover":{backgroundColor:"rgba(2, 136, 209, 0.1)"}},children:e(je,{index:r.row.id,name:((o=r==null?void 0:r.row)==null?void 0:o.docName)||((s=r==null?void 0:r.row)==null?void 0:s.fileName),documentViewUrl:r.row.documentViewUrl})}),e(G,{size:"small",sx:{color:(n=g)==null?void 0:n.icon.matDownload,"&:hover":{backgroundColor:"rgba(46, 125, 50, 0.1)"}},children:e(Ge,{index:r.row.id,name:((h=r==null?void 0:r.row)==null?void 0:h.docName)||((f=r==null?void 0:r.row)==null?void 0:f.fileName)})}),e(G,{size:"small",onClick:()=>re(r.row.id),disabled:k||d,sx:{color:(S=(p=g)==null?void 0:p.error)==null?void 0:S.dark,"&:hover":{backgroundColor:"rgba(211, 47, 47, 0.1)"}},children:e(Ue,{fontSize:"small"})})]})}}];return i("div",{children:[i(O,{container:!0,spacing:2,sx:{padding:"35px",pt:"40px"},children:[i(O,{container:!0,sx:Le,children:[i(O,{item:!0,md:5,xs:12,children:[e(u,{variant:"h3",children:e("strong",{children:D("Documents Configuration")})}),e(u,{variant:"body2",color:g.secondary.grey,children:D("This view displays the list of Documents uploaded for AI")})]}),e(O,{item:!0,md:7,xs:12,sx:{display:"flex"},children:e(O,{container:!0,direction:"row",justifyContent:"flex-end",alignItems:"center",spacing:0,mt:0})})]}),i(O,{item:!0,md:12,sx:{backgroundColor:(t=(K=g)==null?void 0:K.primary)==null?void 0:t.white,maxHeight:"max-content",height:"max-content",borderRadius:"8px",border:`1px solid ${(c=(l=g)==null?void 0:l.primary)==null?void 0:c.white}`,boxShadow:"0px 2px 14px 0px rgba(48, 38, 185, 0.10)",paddingRight:"16px",mt:"20px"},children:[i(O,{container:!0,sx:{display:"flex",justifyContent:"space-between",flexDirection:"row",alignItems:"center"},children:[e(J,{className:"refreshButtonCD",variant:"outlined",startIcon:e(Re,{}),sx:{textTransform:"capitalize",borderRadius:"5px",marginLeft:"15px"},onClick:j,children:"Refresh"}),!k&&e(He,{onFilesAdded:ee,maxFiles:z,maxFileSize:v,acceptedTypes:M,currentFileCount:y.length,loading:d})]}),d&&y.length===0?i(w,{alignItems:"center",spacing:2,sx:{py:4},children:[e(se,{}),e(u,{variant:"body2",color:(le=(L=g)==null?void 0:L.secondary)==null?void 0:le.grey,children:"Loading files..."})]}):y.length>0?e(O,{sx:{padding:"15px"},children:e(Ae,{width:"100%",rows:y,columns:ae,hideFooter:!1,getRowIdValue:"id",autoHeight:!0,disableSelectionOnClick:!0,stopPropagation_Column:"action",title:D("Documents Uploaded")})}):i(w,{alignItems:"center",spacing:2,sx:{py:"25vh"},children:[e(Ne,{sx:{fontSize:40,color:(de=(ce=g)==null?void 0:ce.primary)==null?void 0:de.whiteSmoke,transform:"rotate(90deg)"}}),e(u,{variant:"body2",color:(pe=(he=g)==null?void 0:he.secondary)==null?void 0:pe.grey,children:D("No Files Added")})]})]})]}),U&&e(Pe,{openSnackBar:U,alertMsg:R,alertType:a,handleSnackBarClose:V})]})};export{tr as default};
