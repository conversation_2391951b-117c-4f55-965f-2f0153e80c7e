import{s as h,aP as v,n as f,r as O,aX as F,da as U,bI as m,aT as p,C as R,aJ as b,f2 as q,f3 as Q,dI as j,f4 as w,dc as y,dd as P,aD as V}from"./index-f7d9b065.js";const Y=r=>{let o={},D=r==null?void 0:r.sort((a,t)=>a.MDG_CC_SEQUENCE_NO-t.MDG_CC_SEQUENCE_NO);const i=y(D,"MDG_CC_VIEW_NAME");let l=[];Object.entries(i).forEach(([a,t])=>{let n=y(t,"MDG_CC_CARD_NAME"),s=[];Object.entries(n).forEach(([C,_])=>{_.sort((e,N)=>e.MDG_CC_SEQUENCE_NO-N.MDG_CC_SEQUENCE_NO);let u=_.map(e=>({fieldName:e.MDG_CC_UI_FIELD_NAME,sequenceNo:e.MDG_CC_SEQUENCE_NO,fieldType:e.MDG_CC_FIELD_TYPE,maxLength:e.MDG_CC_MAX_LENGTH,dataType:e.MDG_CC_DATA_TYPE,viewName:e.MDG_CC_VIEW_NAME,cardName:e.MDG_CC_CARD_NAME,cardSeq:e.MDG_CC_CARD_SEQUENCE,viewSeq:e.MDG_CC_VIEW_SEQUENCE,value:e.MDG_CC_DEFAULT_VALUE,visibility:e.MDG_CC_VISIBILITY,jsonName:e.MDG_CC_JSON_FIELD_NAME}));s.push({cardName:C,cardSeq:_[0].MDG_CC_CARD_SEQUENCE,cardDetails:u})}),s.sort((C,_)=>C.cardSeq-_.cardSeq),l.push({viewName:a,viewSeq:t[0].MDG_CC_VIEW_SEQUENCE,cards:s})}),l.sort((a,t)=>a.viewSeq-t.viewSeq);let E=P(l),c={};return E.forEach(a=>{let t={};a.cards.forEach(n=>{t[n.cardName]=n.cardDetails,a.viewName!=="Request Header"&&n.cardDetails.forEach(s=>{s.visibility===V.MANDATORY&&(o[s.viewName]||(o[s.viewName]=[]),o[s.viewName].push({jsonName:s==null?void 0:s.jsonName,fieldName:s==null?void 0:s.fieldName}))})}),c[a.viewName]=t}),{transformedData:c,mandatoryFields:o}},x=()=>{const r=h(),{customError:o}=v(),D=f(s=>{var C;return(C=s.payload)==null?void 0:C.payloadData}),i=f(s=>s.applicationConfig);f(s=>s.userManagement.userData);const[l,E]=O.useState(!1),[c,a]=O.useState(null),t=async()=>{E(!0);const s={decisionTableId:null,decisionTableName:"MDG_CC_FIELD_CONFIG",version:"v3",rulePolicy:null,validityDate:null,conditions:[{"MDG_CONDITIONS.MDG_CC_SCENARIO":(D==null?void 0:D.RequestType)||F.CREATE,"MDG_CONDITIONS.MDG_CC_ROLE":U.REQ_INITIATE_FIN}],systemFilters:null,systemOrders:null,filterString:null},C=e=>{var N,M,S,T;if(e.statusCode===b.STATUS_200){if(Array.isArray((N=e==null?void 0:e.data)==null?void 0:N.result)&&((M=e==null?void 0:e.data)!=null&&M.result.every(I=>Object.keys(I).length!==0))){let I=(T=(S=e==null?void 0:e.data)==null?void 0:S.result[0])==null?void 0:T.MDG_CC_FIELD_DETAILS_ACTION_TYPE;const{transformedData:A,mandatoryFields:g}=Y(I);let d=Object.keys(A);const L=d.map(G=>({tab:G,data:A[G]}));r(q(L)),r(Q({CostCenter:{allfields:j(d),mandatoryFields:g}}))}else r(w({CostCenter:{}}));E(!1)}},_=e=>{o(e),a(e),E(!1)},u=i.environment==="localhost"?`/${m}${p.INVOKE_RULES.LOCAL}`:`/${m}${p.INVOKE_RULES.PROD}`;R(u,"post",C,_,s)};return{loading:l,error:c,fetchCostCenterFieldConfig:()=>{try{t()}catch(s){a(s),E(!1)}}}};export{x as u};
