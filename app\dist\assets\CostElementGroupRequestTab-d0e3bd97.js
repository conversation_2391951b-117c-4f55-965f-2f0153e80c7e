import{s as Je,n as I,u as Ye,g as Ot,r as d,cw as ps,eW as at,aX as b,Ae as os,j as e,c as Q,O as qe,b1 as Es,d as Xe,B as X,an as Ge,aG as ct,c5 as dt,y6 as Xs,aZ as Js,aD as Zs,C as we,bK as xe,aO as be,aY as Vs,z9 as er,ap as fs,cz as tr,ae as _e,cd as nt,aP as bt,f7 as Tt,e$ as St,aT as We,AM as as,aJ as At,fP as Rt,fQ as rt,Af as sr,Ag as rr,Ah as or,Ai as ar,Aj as nr,Ak as ir,Al as lr,Am as it,An as lt,Ao as cr,aa as dr,T as ke,bD as ur,cH as ut,cq as Lt,Ap as hs,a6 as Fe,d5 as Ns,d3 as Ds,d4 as ms,af as pr,fb as gs,aF as Cs,aH as Er,Z as Qe,Aa as fr,a as hr,Aq as Nr,f9 as Dr,xY as mr,xZ as gr,g4 as ns,wZ as Cr,w_ as Tr,y0 as is,y1 as Sr,wY as Rr,aA as Ar,aB as Lr,Ar as Or,As as br,bd as ls,er as _r,bJ as Pr,cA as yr,y3 as Ir,bc as xr,F as cs,cI as Me,AN as Gr,d6 as wr,d7 as qr,am as kr,br as Fr,ad as Hr,g3 as ze,bf as ot,bI as ds,d8 as Br,a$ as Mr,aE as $r,d9 as vr,fY as Ur,al as Wr,cZ as Yr,gA as jr,At as zr}from"./index-f7d9b065.js";import{D as Kr,S as Qr,A as Xr,P as Jr,B as Zr,g as Vr}from"./PreviewPage-634057fa.js";import{u as eo,R as to,a as so,b as ro,C as oo,d as ao}from"./useDynamicWorkflowDTHierarchy-548ceabc.js";import{d as no}from"./PermIdentityOutlined-0746a749.js";import{d as io}from"./TrackChangesTwoTone-7a2ab513.js";import{d as lo}from"./FileUploadOutlined-4a68a28a.js";import{E as co}from"./ExcelOperationsCard-49e9ffd2.js";import{F as uo}from"./FilterFieldGlobal-f8e8f75f.js";import{u as po}from"./useDownloadExcel-cd596a31.js";import{R as Eo}from"./ReusableHIerarchyTree-7527be6c.js";import{u as Ts,E as fo}from"./ErrorReportDialog-cb66d1ed.js";import{d as $e}from"./DeleteOutline-584dc929.js";import"./AttachFile-8d552da8.js";import"./UtilDoc-6f590135.js";import"./FileDownloadOutlined-59854a55.js";import"./VisibilityOutlined-b6cd6d28.js";import"./DeleteOutlined-e668453f.js";import"./CloudUpload-0ba6431e.js";import"./utilityImages-067c3dc2.js";import"./Delete-5278579a.js";import"./ReusablePromptBox-e1871d49.js";import"./featureConfig-652a9f8d.js";import"./Description-ab582559.js";import"./DataObject-52409c14.js";import"./Download-52c4427b.js";import"./useFinanceCostingRows-ffbb569f.js";import"./CheckCircleOutline-e186af3e.js";import"./FeedOutlined-41109ec9.js";import"./useChangeMaterialRowsRequestor-fc0d44be.js";import"./FilterChangeDropdown-22e24089.js";import"./CloudDownload-9a7605e9.js";import"./AttachmentUploadDialog-43cc9099.js";import"./AdapterDayjs-2a9281df.js";import"./advancedFormat-4f6292d4.js";import"./customParseFormat-1bc1aa07.js";import"./isBetween-c1c6beb9.js";import"./DatePicker-a8e9bd4a.js";import"./useMobilePicker-9b56b5b6.js";import"./CSSTransition-cd337b47.js";import"./context-4d14404f.js";import"./EyeOutlined-0bb7ab85.js";import"./EditOutlined-9d614b39.js";import"./asyncToGenerator-88583e02.js";import"./ArrowLeftOutlined-a31a51d5.js";import"./index-190fd75d.js";import"./lz-string-0665f106.js";import"./ErrorHistory-ef441d1f.js";const ho=({downloadClicked:u,setDownloadClicked:n,setIsSecondTabEnabled:D,setIsAttachmentTabEnabled:O})=>{var s,E,T,A,P;const c=Je(),t=I(a=>a.hierarchyData.requestHeaderData),m=I(a=>a.request.requestHeader),o=I(a=>a.userManagement.userData),K=I(a=>a.tabsData.requestHeaderData),M=I(a=>a.AllDropDown.dropDown.FieldName||[]),j=I(a=>a.AllDropDown.dropDown),N=Ye(),G=new URLSearchParams(N.search),F=G.get("RequestType"),J=G.get("RequestId"),ue=G.get("RequestId"),pe=`/Date(${Date.now()})/`,Ee=Ot(),[Pe,De]=d.useState(!1),[H,ie]=d.useState("systemGenerated"),[Z,$]=d.useState(!1),[le,V]=d.useState(""),[q,re]=d.useState(""),[_,fe]=d.useState(!1),{getRequestHeaderTemplatePCG:k}=eo(),{handleDownload:ae,handleEmailDownload:de}=po((s=dt)==null?void 0:s.CEG),{showSnackbar:Re}=ps(),oe=N.state,z=[{code:"Create",tooltip:"Create New Cost Element Group Directly in Application"},{code:"Change",tooltip:"Modify Existing Cost Element Group Directly in Application"},{code:"Create with Upload",tooltip:"Create New Cost Element Group with Excel Upload"},{code:"Change with Upload",tooltip:"Modify Existing Cost Element Group with Excel Upload"}],ge=[{code:"High",desc:""},{code:"Medium",desc:""},{code:"Low",desc:""}];c(at({keyName:"RequestStatus",data:"DRAFT"})),c(at({keyName:"ReqCreatedBy",data:o==null?void 0:o.user_id})),d.useEffect(()=>{var a;if(u){if((t==null?void 0:t.RequestType)===b.CREATE_WITH_UPLOAD){fe(!0);return}if((t==null?void 0:t.RequestType)===((a=b)==null?void 0:a.CHANGE_WITH_UPLOAD)){$(!0);return}}},[u]);const r=()=>{var y,l;let a=!0;return t&&((y=K[Object.keys(K)])!=null&&y.length)?(l=K[Object.keys(K)[0]])==null||l.forEach(se=>{var B;!t[se.jsonName]&&se.visibility===((B=Zs)==null?void 0:B.MANDATORY)&&(a=!1)}):a=!1,a};d.useEffect(()=>{k()},[t==null?void 0:t.RequestType]),d.useEffect(()=>{var a,y;J&&(F===((a=b)==null?void 0:a.CREATE)||F===((y=b)==null?void 0:y.CHANGE))&&oe&&(!oe.parentNode||oe.parentNode.length===0)&&$(!0)},[J,F,oe]);const p=()=>{const a=new Date(t==null?void 0:t.ReqCreatedOn).getTime();M.map(W=>W.code||"").join(", "),$(!1);const y={RequestId:"",ReqCreatedBy:(o==null?void 0:o.user_id)||"",ReqCreatedOn:a?`/Date(${a})/`:pe,ReqUpdatedOn:a?`/Date(${a})/`:pe,RequestType:(t==null?void 0:t.RequestType)||"",RequestPrefix:"",RequestPriority:(t==null?void 0:t.RequestPriority)||"",RequestDesc:(t==null?void 0:t.RequestDesc)||"",RequestStatus:"DRAFT",FirstProd:"",LaunchDate:"",LeadingCat:"",Division:"",TemplateName:"",FieldName:"",Region:(t==null?void 0:t.Region)||"",FilterDetails:"",IsBifurcated:!0,IsHierarchyGroup:!0},l=W=>{var ce,ye,Ae;if(Re(`${(ce=Vs)==null?void 0:ce.SUCCESS_REQUEST_HEADER}${(ye=W==null?void 0:W.body)==null?void 0:ye.requestId}`,"success"),De(!1),O(!0),c(er(W==null?void 0:W.body)),c(fs(W.body)),c(at({keyName:tr.REQUEST_ID,data:(Ae=W==null?void 0:W.body)==null?void 0:Ae.requestId})),(t==null?void 0:t.RequestType)===b.CREATE||(t==null?void 0:t.RequestType)===b.CHANGE){$(!0);return}if((t==null?void 0:t.RequestType)===b.CREATE_WITH_UPLOAD||(t==null?void 0:t.RequestType)===b.CHANGE_WITH_UPLOAD){fe(!0);return}},se=W=>{var ce;Re((ce=_e)==null?void 0:ce.ERROR_REQUEST_HEADER,"error")};we(`/${nt}/massAction/createRequestHeader`,"post",l,se,y)},i=()=>{var a;n(!1),fe(!1),ie("systemGenerated"),ue||Ee((a=xe)==null?void 0:a.REQUEST_BENCH)},g=a=>{var y;ie((y=a==null?void 0:a.target)==null?void 0:y.value)},f=()=>{var a,y;H==="systemGenerated"&&(ae(V,re,t,(a=be)==null?void 0:a.CEG),i()),H==="mailGenerated"&&(de(V,re,t,(y=be)==null?void 0:y.CEG),i())};let R={[(E=b)==null?void 0:E.CREATE]:"CREATE_CEG",[(T=b)==null?void 0:T.CHANGE]:"CHANGE_CEG"};return console.log("check ten",os[R==null?void 0:R[t==null?void 0:t.RequestType]]),e("div",{children:Q(Js,{spacing:2,children:[Object.entries(K).map(([a,y])=>Q(qe,{item:!0,md:12,sx:{backgroundColor:"white",maxHeight:"max-content",height:"max-content",borderRadius:"8px",border:"1px solid #E0E0E0",mt:.25,boxShadow:"0px 2px 14px 0px rgba(48, 38, 185, 0.10)",...Es},children:[e(Xe,{sx:{fontSize:"12px",fontWeight:"700",paddingBottom:"10px"},children:a}),e(X,{children:e(qe,{container:!0,spacing:1,children:y.filter(l=>l.visibility!=="Hidden").sort((l,se)=>l.sequenceNo-se.sequenceNo).map(l=>e(uo,{isHeader:!0,field:l,dropDownData:{RequestType:z,RequestPriority:ge},disabled:ue||!!(m!=null&&m.requestId),requestHeader:!0,module:"CEG"},l.id))})}),!ue&&!(m!=null&&m.requestId)&&e(X,{sx:{display:"flex",justifyContent:"flex-end",marginTop:"20px"},children:e(Ge,{variant:"contained",color:"primary",disabled:!r(),onClick:p,children:"Save Request Header"})})]},a)),e(ct,{blurLoading:q,loaderMessage:le}),Z&&e(to,{open:Z,onClose:()=>{$(!1)},parameters:os[R==null?void 0:R[t==null?void 0:t.RequestType]],setShowTable:!1,allDropDownData:j,setIsSecondTabEnabled:D,module:(A=dt)==null?void 0:A.CEG,mandatoryFields:(P=Xs)==null?void 0:P[R==null?void 0:R[t==null?void 0:t.RequestType]]}),e(Kr,{onDownloadTypeChange:f,open:_,downloadType:H,handleDownloadTypeChange:g,onClose:i})]})})},_t=({setSuccessDialogOpen:u,setDialogData:n,selectedLevel:D}={})=>{var R;const O=Je();Ot();const c=Ye(),t=new URLSearchParams(c.search),m=t.get("RequestType"),o=t.get("RequestId"),[K,M]=d.useState(!1),[j,N]=d.useState(!1),[G,F]=d.useState(!1),[J,ue]=d.useState(""),[pe,Ee]=d.useState("success"),[Pe,De]=d.useState([]),H=I(s=>s.userManagement.taskData),ie=I(s=>s.payload.filteredButtons),Z=I(s=>s.hierarchyData.requestHeaderData),$=I(s=>s.hierarchyData.treeData),le=I(s=>s.payload.requestorPayload),V=I(s=>s.payload.dataLoading),q=I(s=>s.payload.dynamicKeyValues),re=I(s=>s.request.requestHeader),_=I(s=>s.hierarchyData),fe=I(s=>s.hierarchyData.TreeChanges),k=I(s=>s.hierarchyData.DisplayRecords),{getDynamicWorkflowDT:ae}=so(),{getButtonsDisplayGlobal:de,showWfLevels:Re}=Ts(),{preparePayload:oe}=ro(),{customError:z}=bt();d.useEffect(()=>{(H!=null&&H.ATTRIBUTE_1||m)&&de("Hierarchy Node (General Ledger)","MDG_DYN_BTN_DT","v3")},[H]),d.useEffect(()=>{var E;const s=async()=>{var T,A;try{let P={"MDG_CONDITIONS.MDG_CEG_REQUEST_TYPE":m||"","MDG_CONDITIONS.MDG_HIERARCHY_REGION":((T=_==null?void 0:_.GeneralInformation)==null?void 0:T["Hierarchy Region"])||""};const a=await ae(H==null?void 0:H.ATTRIBUTE_3,"v4","MDG_DYNAMIC_WF_CEG_DT",(A=be)==null?void 0:A.PCG,P);De(a)}catch{}};m&&((E=_==null?void 0:_.GeneralInformation)!=null&&E["Hierarchy Region"])&&(H!=null&&H.ATTRIBUTE_3)&&s()},[m,(R=_==null?void 0:_.GeneralInformation)==null?void 0:R["Hierarchy Region"],H==null?void 0:H.ATTRIBUTE_3]),d.useEffect(()=>{($==null?void 0:$.length)!==0&&M(!0)},[$]);const ge=async s=>{var E,T,A,P;try{const a={chartOfAccount:(_==null?void 0:_.ChartOfAccount)??"",nodes:[s==null?void 0:s.toUpperCase()]},l=await(await Tt(`/${St}${(T=(E=We)==null?void 0:E.MASS_ACTION)==null?void 0:T.OLD_DESCRIPTION_FOR_NODE}`,"post",a)).json();return((P=(A=l==null?void 0:l.body)==null?void 0:A[0])==null?void 0:P.Description)||""}catch{return z(_e.ERROR_FETCHING_DATA),""}},r=async s=>{var E,T,A,P;try{const a={chartOfAccount:(_==null?void 0:_.ChartOfAccount)??"",nodes:[s==null?void 0:s.toUpperCase()]},l=await(await Tt(`/${St}${(T=(E=We)==null?void 0:E.MASS_ACTION)==null?void 0:T.OLD_PARENTNODE_FOR_NODE}`,"post",a)).json();return((P=(A=l==null?void 0:l.body)==null?void 0:A[0])==null?void 0:P.ParentNode)||""}catch{return z(_e.ERROR_FETCHING_DATA),""}},p=async s=>{var E,T,A,P;try{const a={chartOfAccount:(_==null?void 0:_.ChartOfAccount)??"",hierarchy:(_==null?void 0:_.ParentNode)??"",pcList:[s==null?void 0:s.toUpperCase()]},l=await(await Tt(`/${St}${(T=(E=We)==null?void 0:E.MASS_ACTION)==null?void 0:T.PARENTNODE_FOR_OBJECT}`,"post",a)).json();return((P=(A=l==null?void 0:l.body)==null?void 0:A[0])==null?void 0:P.Node)||""}catch{return z(_e.ERROR_FETCHING_DATA),""}},i=()=>{const s=[],E=[],T=[],A=[],P=[],a=[],y=[],l=[],se=[],B=[],W=(k==null?void 0:k["NEW NODES"])||[],ce=(k==null?void 0:k["MOVE NODE"])||[],ye=(k==null?void 0:k["GENERAL LEDGERS"])||[],Ae=(k==null?void 0:k["MOVE GENERAL LEDGER"])||[],S=(k==null?void 0:k.DESCRIPTIONS)||[],ve=(k==null?void 0:k["DELETE NODE"])||[];W.forEach(h=>{h["Parent Node"]&&h["New Node"]&&(s.push(`${h["Parent Node"]}$$${h["New Node"]}`),l.push(`${h["New Node"]}`))}),ce.forEach(h=>{h["New Parent Node"]&&h["Selected Node"]&&s.push(`${h["New Parent Node"]}$$${h["Selected Node"]}`)}),ce.forEach(h=>{h["Selected Node"]&&h["Old Parent Node"]&&E.push(`${h["Selected Node"]}$$${h["Old Parent Node"]}`)}),ye.forEach(h=>{h.Node&&h["General Ledger"]&&(T.push(`${h.Node}$$${h["General Ledger"]}`),B.push(`${h["General Ledger"]}`))}),Ae.forEach(h=>{h["New Parent Node"]&&h["Selected General Ledger"]&&T.push(`${h["New Parent Node"]}$$${h["Selected General Ledger"]}`)}),Ae.forEach(h=>{h["Old Parent Node"]&&h["Selected General Ledger"]&&A.push(`${h["Old Parent Node"]}$$${h["Selected General Ledger"]}`)}),W.forEach(h=>{h["New Node"]&&h.Description&&(P.push(`${h["New Node"]}$~$${h.Description}`),se.push(`${h.Description}`))}),S.forEach(h=>{h["Parent Node"]&&h["New Description"]&&(a.push(`${h["Parent Node"]}$~$${h["New Description"]}`),se.push(`${h["New Description"]}`))}),ve.forEach(h=>{h["Parent Node"]&&h["Deleted Node"]&&y.push(`${h["Parent Node"]}$$${h["Deleted Node"]}`)});const Ie={NodeList:s,ReplaceNodesList:E,TagList:T,ReplaceTagList:A,DescList:P,EditDescList:a,DeleteNodeList:y,nodesListForDBDuplicateCheck:l,descListForDBDuplicateCheck:se,tagListForDBDuplicateCheck:B,success:!0};return O(sr(s)),O(rr(E)),O(or(T)),O(ar(A)),O(nr(P)),O(ir(a)),O(lr(y)),O(it(l)),O(lt(se)),O(cr(B)),Ie};return{showTree:K,buttonsLoading:j,openButtonSnackBar:G,alertButtonMsg:J,alertButtonType:pe,filteredButtons:ie,requestorPayload:le,loadForFetching:V,initialNodeData:$,showWfLevels:Re,wfLevels:Pe,fetchOldDescriptionForNode:ge,fetchOldParentForNode:r,fetchOldParentForObject:p,handleButtonClick:async(s,E,T)=>{var A,P,a,y,l,se,B;try{let W=(l=(y=(a=(A=We)==null?void 0:A.MASTER_BUTTON_APIS)==null?void 0:a[(P=dt)==null?void 0:P.CEG])==null?void 0:y[Z==null?void 0:Z.RequestType])==null?void 0:l[s],ce={};if(m!=((se=b)==null?void 0:se.CHANGE_WITH_UPLOAD)){const S=oe(fe);if(N(!0),!(S!=null&&S.success))return;ce=as({..._,NodesList:S.NodeList,ReplaceNodesList:S.ReplaceNodesList,TagList:S.TagList,ReplaceTagList:S.ReplaceTagList,DescList:S.DescList,EditDescList:S.EditDescList,DeleteNodeList:S.DeleteNodeList,nodesListForDBDuplicateCheck:S.nodesListForDBDuplicateCheck,descListForDBDuplicateCheck:S.descListForDBDuplicateCheck,tagListForDBDuplicateCheck:S.tagListForDBDuplicateCheck},re,o,H,q,le,s,E,T,D)}if(m===((B=b)==null?void 0:B.CHANGE_WITH_UPLOAD)){const S=i();N(!0),S!=null&&S.success,ce=as({..._,NodesList:S.NodeList,ReplaceNodesList:S.ReplaceNodesList,TagList:S.TagList,ReplaceTagList:S.ReplaceTagList,DescList:S.DescList,EditDescList:S.EditDescList,DeleteNodeList:S.DeleteNodeList,nodesListForDBDuplicateCheck:S.nodesListForDBDuplicateCheck,descListForDBDuplicateCheck:S.descListForDBDuplicateCheck,tagListForDBDuplicateCheck:S.tagListForDBDuplicateCheck},re,o,H,q,le,E,T,D)}const ye=S=>{var ve,Ie,h,Ce,Ze,pt,he,Le;S.statusCode>=((ve=At)==null?void 0:ve.STATUS_200)&&S.statusCode<((Ie=At)==null?void 0:Ie.STATUS_300)?(N(!1),n({title:Rt.TITLE,message:S.message,subText:Rt.SUBTEXT,buttonText:Rt.BUTTONTEXT,redirectTo:(Le=(he=(pt=(Ze=(h=We)==null?void 0:h.MASTER_BUTTON_APIS)==null?void 0:Ze[(Ce=dt)==null?void 0:Ce.PCG])==null?void 0:pt[Z==null?void 0:Z.RequestType])==null?void 0:he[s])==null?void 0:Le.NAVIGATE_TO}),u(!0)):(N(!1),n({title:rt.TITLE,message:S.message,subText:rt.SUBTEXT,buttonText:rt.BUTTONTEXT,redirectTo:rt.REDIRECT}),u(!0))},Ae=S=>{z(_e.ERROR_FETCHING_DATA),N(!1),Ee("error"),ue((S==null?void 0:S.error)||"An error occurred"),F(!0)};we(W==null?void 0:W.URL,"POST",ye,Ae,ce)}catch{z(_e.ERROR_FETCHING_DATA),N(!1)}},handleSnackBarButtonClose:()=>{F(!1)}}},te={ADD_NODE:"ADD_NODE",EDIT_DESCRIPTION:"EDIT_DESCRIPTION",ADD_GENERAL_LEDGER:"ADD_GENERAL_LEDGER",MOVE_NODE:"MOVE_NODE",MOVE_GENERAL_LEDGER:"MOVE_GENERAL_LEDGER",REMOVE_GENERAL_LEDGER:"REMOVE_GENERAL_LEDGER",DELETE_NODE:"DELETE_NODE",CHANGE_PERSON_RESPONSIBLE:"CHANGE_PERSON_RESPONSIBLE",DELETE_ROW:"DELETE_ROW",FIELD_UPDATE:"FIELD_UPDATE"},No=()=>Date.now()+Math.random().toString(36).substr(2,9),Do=(u,n,D={})=>{switch(u){case te.ADD_NODE:return`New node "${n["New Node"]}" added under parent "${n["Parent Node"]}"`;case te.EDIT_DESCRIPTION:return`Description changed for node "${n["Parent Node"]}" from "${D["Old Description"]||"N/A"}" to "${n["New Description"]}"`;case te.ADD_GENERAL_LEDGER:return`General Ledger "${n["General Ledger"]}" added to node "${n.Node}"`;case te.MOVE_NODE:return`Node "${n["Selected Node"]}" moved from "${n["Old Parent Node"]}" to "${n["New Parent Node"]}"`;case te.MOVE_GENERAL_LEDGER:return`General Ledger "${n["Selected General Ledger"]}" moved from "${n["Old Parent Node"]}" to "${n["New Parent Node"]}"`;case te.REMOVE_GENERAL_LEDGER:return`General Ledger "${n["Selected General Ledger"]}" removed from node "${n["Parent Node"]}"`;case te.DELETE_NODE:return`Node "${n["Deleted Node"]}" deleted from parent "${n["Parent Node"]}"`;case te.CHANGE_PERSON_RESPONSIBLE:return`Person responsible changed for node "${n["Parent Node"]}" from "${D["Old Person Responsible"]||"N/A"}" to "${n["New Person Responsible"]}"`;case te.DELETE_ROW:return`Row deleted: ${n.description}`;case te.FIELD_UPDATE:return`Field updated: ${n.description}`;default:return`Unknown change type: ${u}`}},mo=(u,n)=>{const D=[],O=["Id","Updated By","Updated On"];return Object.keys(n).forEach(c=>{if(!O.includes(c)){const t=u[c]||"",m=n[c]||"";t!==m&&D.push({field:c,oldValue:t,newValue:m})}}),D},go=(u,n,D,O,c)=>{const t=n?`"${n}"`:"empty",m=D?`"${D}"`:"empty";return`${u} changed from ${t} to ${m} in row ${c} (${O})`},Co=(u,n,D,O)=>{const c=[];switch(O){case 0:u==="New Node"?(n&&n.trim()!==""&&c.push({type:"node",value:n,action:"remove"}),D&&D.trim()!==""&&c.push({type:"node",value:D,action:"add"})):u==="Description"&&(n&&n.trim()!==""&&c.push({type:"desc",value:n,action:"remove"}),D&&D.trim()!==""&&c.push({type:"desc",value:D,action:"add"}));break;case 1:u==="New Description"&&(n&&n.trim()!==""&&c.push({type:"desc",value:n,action:"remove"}),D&&D.trim()!==""&&c.push({type:"desc",value:D,action:"add"}));break}return c},To=(u,n,D,O,c,t=null)=>{const m=O.findIndex(M=>M.key===D);if(m===-1)return;const o=mo(u,n);let K=[];o.forEach(({field:M,oldValue:j,newValue:N})=>{if(N.trim()!==""){const G=go(M,j,N,D,n.Id);let F="FIELD_UPDATE";switch(m){case 0:M==="New Node"&&N&&(F=te.ADD_NODE);break;case 1:M==="New Description"&&N&&(F=te.EDIT_DESCRIPTION);break;case 2:M==="General Ledger"&&N&&(F=te.ADD_GENERAL_LEDGER);break;case 3:M==="Selected Node"&&N&&(F=te.MOVE_NODE);break;case 4:M==="Selected General Ledger"&&N&&(F=te.MOVE_GENERAL_LEDGER);break;case 5:M==="Selected General Ledger"&&N&&(F=te.REMOVE_GENERAL_LEDGER);break;case 6:M==="Deleted Node"&&N&&(F=te.DELETE_NODE);break;case 7:M==="New Person Responsible"&&N&&(F=te.CHANGE_PERSON_RESPONSIBLE);break}const J=Co(M,j,N,m);K=[...K,...J],c(F,G,{rowId:n.Id,fieldName:M,oldValue:j,newValue:N,oldData:{...u},newData:{...n},tableKey:D})}}),K.length>0&&t&&(t.batchUpdateDuplicateLists?t.batchUpdateDuplicateLists(K):K.forEach(M=>{const{type:j,value:N,action:G}=M;j==="node"&&t.updateNodesListForDuplicateCheck?t.updateNodesListForDuplicateCheck(N,G):j==="desc"&&t.updateDescListForDuplicateCheck&&t.updateDescListForDuplicateCheck(N,G)}))},us=(u,n,D)=>{if(!u||!D)return{isValid:!0,message:""};if(D(u,n)){const c=n==="node"?"Node":"Description";return{isValid:!1,message:`${c} "${u}" already exists. Please choose a different ${c.toLowerCase()}.`}}return{isValid:!0,message:""}},So=[{label:"New Nodes",value:"1",key:"NEW NODES"},{label:"Description Change",value:"2",key:"DESCRIPTIONS"},{label:"Add General Ledgers",value:"3",key:"GENERAL LEDGERS"},{label:"Move Node",value:"4",key:"MOVE NODE"},{label:"Move General Ledgers",value:"5",key:"MOVE GENERAL LEDGER"},{label:"Remove General Ledgers",value:"6",key:"REMOVE GENERAL LEDGER"},{label:"Delete Node",value:"7",key:"DELETE NODE"}],Ne=({value:u,placeholder:n,maxLength:D=10,disabled:O=!1,rowId:c,fieldName:t,tableKey:m,fetchOldData:o=!1,fetchFunction:K=null,oldDataField:M=null,onNodeValueChange:j=null,addToChangeLog:N,updateDuplicateCheckLists:G=null,isDuplicateCheckField:F=!1,duplicateCheckType:J=null})=>{const ue=Je(),pe=I(A=>A.hierarchyData.DisplayRecords),Ee=Ye(),De=new URLSearchParams(Ee.search).get("reqBench"),H=I(A=>A.userManagement.userData),[ie,Z]=d.useState(u||""),[$,le]=d.useState((u==null?void 0:u.length)||0),[V,q]=d.useState(!1),[re,_]=d.useState({}),[fe,k]=d.useState(""),[ae,de]=d.useState(!1),Re=I(A=>{var P;return(P=A.hierarchyData.requestHeaderData)==null?void 0:P.RequestStatus}),oe=De&&!ut.includes(Re),{customError:z}=bt();d.useEffect(()=>{V||(Z(u||""),le((u==null?void 0:u.length)||0),k(""),de(!1))},[u,V]);const ge=()=>F&&J&&(G==null?void 0:G.checkForDuplicates),r=()=>J,p=A=>{const P=pe[m]||[],a=P.find(l=>l.Id===c),y=P.map(l=>l.Id===c?{...l,...A,"Updated By":H==null?void 0:H.emailId,"Updated On":Lt().utc().format("YYYY-MM-DD HH:mm:ss.SSS")}:l);if(ue(hs({...pe,[m]:y})),N&&a){const l=y.find(se=>se.Id===c);l&&To(a,l,m,So,N,G)}},i=A=>{const P=A.target.value.trimStart();if(Z(P),le(P.length),ge()&&(G!=null&&G.checkForDuplicates)){const a=r();if(a&&P.trim()!==""){const y=us(P.trim(),a,G.checkForDuplicates);y.isValid?(k(""),de(!1)):(k(y.message),de(!0))}else k(""),de(!1)}j&&j(P)},g=async()=>{q(!1);const A=ie.trim(),P=ie;if(ge()&&(G!=null&&G.checkForDuplicates)&&A!==""){const a=r();if(a){const y=us(A,a,G.checkForDuplicates);y.isValid||(k(y.message),de(!0))}}if(p({[t]:A.toUpperCase()}),o&&A&&K&&M){_(a=>({...a,[c]:!0}));try{const a=await K(A);a!=null&&p({[M]:a,[t]:P.toUpperCase()})}catch{z(_e.ERROR_FETCHING_DATA)}finally{_(a=>({...a,[c]:!1}))}}},f=()=>{q(!0),k(""),de(!1)},R=()=>ae?"#ff9800":V&&$>=D?"red":"",s=()=>fe&&ae?fe:V?$===D?"Max Length Reached":`${$}/${D}`:"",E=()=>fe&&ae?"#ff9800":V&&$===D?"red":"blue",T=e(dr,{variant:"outlined",size:"small",fullWidth:!0,disabled:oe||O||re[c],value:ie,placeholder:n,error:ae,inputProps:{style:{textTransform:"uppercase"},maxLength:D},onChange:i,onFocus:f,onBlur:g,helperText:s(),FormHelperTextProps:{sx:{color:E(),position:"absolute",bottom:"-20px",fontSize:"0.75rem"}},sx:{"& .MuiOutlinedInput-root":{"&.Mui-focused fieldset":{borderColor:R()},"& fieldset":{borderColor:R()},"&.Mui-error fieldset":{borderColor:"#ff9800"}}},onKeyDown:A=>A.stopPropagation()});return Q(X,{sx:{position:"relative"},children:[ae?e(ke,{title:fe,arrow:!0,placement:"top",open:ae,children:T}):T,re[c]&&e(ur,{size:24,sx:{position:"absolute",top:"50%",right:8,marginTop:"-12px"}}),ae&&e(X,{sx:{position:"absolute",top:-2,right:-2,width:8,height:8,borderRadius:"50%",backgroundColor:"#ff9800",border:"2px solid white",zIndex:1}})]})},Ro=(u,n,D,O,c,t,m)=>[{field:"Id",headerName:"ID",hide:!0},{field:"Parent Node",headerName:"Parent Node",flex:1,renderCell:o=>e(Ne,{value:o.row["Parent Node"],placeholder:"Enter Parent Node",maxLength:10,rowId:o.row.Id,fieldName:"Parent Node",tableKey:"NEW NODES",addToChangeLog:c,updateDuplicateCheckLists:t,isDuplicateCheckField:!1})},{field:"New Node",headerName:"New Node",flex:1,renderCell:o=>e(Ne,{value:o.row["New Node"],placeholder:"Enter New Node",maxLength:10,rowId:o.row.Id,fieldName:"New Node",tableKey:"NEW NODES",addToChangeLog:c,updateDuplicateCheckLists:t,isDuplicateCheckField:!0,duplicateCheckType:"node"})},{field:"Description",headerName:"Description",flex:1,renderCell:o=>e(Ne,{value:o.row.Description,placeholder:"Enter Description",maxLength:40,rowId:o.row.Id,fieldName:"Description",tableKey:"NEW NODES",addToChangeLog:c,updateDuplicateCheckLists:t,isDuplicateCheckField:!0,duplicateCheckType:"desc"})},{field:"Action",headerName:"Action",renderCell:o=>e(ke,{title:"Delete Row",children:e(Fe,{disabled:m,sx:{color:m?"grey.400":"red"},onClick:()=>u(o.row.Id,0),children:e($e,{})})})}],Ao=(u,n,D,O,c,t,m)=>[{field:"Id",headerName:"ID",type:"text",hide:!0},{field:"Parent Node",headerName:"Parent Node",type:"text",flex:1,renderCell:o=>e(Ne,{value:o.row["Parent Node"],placeholder:"Enter Parent Node",maxLength:10,rowId:o.row.Id,fieldName:"Parent Node",tableKey:"DESCRIPTIONS",fetchOldData:!0,fetchFunction:n,oldDataField:"Old Description",addToChangeLog:c,updateDuplicateCheckLists:t,isDuplicateCheckField:!1})},{field:"Old Description",headerName:"Old Description",flex:1,editable:!1,renderCell:o=>e("div",{style:{padding:"8px",fontSize:"14px",color:"#666"},children:o.row["Old Description"]||""})},{field:"New Description",headerName:"New Description",flex:1,renderCell:o=>e(Ne,{value:o.row["New Description"],placeholder:"Enter New Description",maxLength:40,rowId:o.row.Id,fieldName:"New Description",tableKey:"DESCRIPTIONS",addToChangeLog:c,updateDuplicateCheckLists:t,isDuplicateCheckField:!0,duplicateCheckType:"desc"})},{field:"Action",headerName:"Action",hide:!1,renderCell:o=>e(ke,{title:"Delete Row",children:e(Fe,{disabled:m,sx:{color:m?"grey.400":"red"},onClick:()=>u(o.row.Id,1),children:e($e,{})})})}],Lo=(u,n,D,O,c,t,m)=>[{field:"Id",headerName:"ID",type:"text",hide:!0},{field:"Node",headerName:"Node",type:"text",flex:1,renderCell:o=>e(Ne,{value:o.row.Node,placeholder:"Enter Node",maxLength:10,rowId:o.row.Id,fieldName:"Node",tableKey:"GENERAL LEDGERS",addToChangeLog:c,updateDuplicateCheckLists:t,isDuplicateCheckField:!1})},{field:"General Ledger",headerName:"General Ledger",flex:1,renderCell:o=>e(Ne,{value:o.row["General Ledger"],placeholder:"Enter General Ledger",maxLength:10,rowId:o.row.Id,fieldName:"General Ledger",tableKey:"GENERAL LEDGERS",addToChangeLog:c,updateDuplicateCheckLists:t,isDuplicateCheckField:!1})},{field:"Action",headerName:"Action",hide:!1,renderCell:o=>e(ke,{title:"Delete Row",children:e(Fe,{disabled:m,sx:{color:m?"grey.400":"red"},onClick:()=>u(o.row.Id,2),children:e($e,{})})})}],Oo=(u,n,D,O,c,t,m)=>[{field:"Id",headerName:"ID",hide:!0},{field:"Old Parent Node",headerName:"Old Parent Node",flex:1,editable:!1,renderCell:o=>e("div",{style:{padding:"8px",fontSize:"14px",color:"#666"},children:o.row["Old Parent Node"]||""})},{field:"New Parent Node",headerName:"New Parent Node",flex:1,renderCell:o=>e(Ne,{value:o.row["New Parent Node"],placeholder:"Enter New Parent Node",maxLength:10,rowId:o.row.Id,fieldName:"New Parent Node",tableKey:"MOVE NODE",addToChangeLog:c,updateDuplicateCheckLists:t,isDuplicateCheckField:!1})},{field:"Selected Node",headerName:"Selected Node",flex:1,renderCell:o=>e(Ne,{value:o.row["Selected Node"],placeholder:"Enter Selected Node",maxLength:10,rowId:o.row.Id,fieldName:"Selected Node",tableKey:"MOVE NODE",fetchOldData:!0,fetchFunction:D,oldDataField:"Old Parent Node",addToChangeLog:c,updateDuplicateCheckLists:t,isDuplicateCheckField:!1})},{field:"Action",headerName:"Action",renderCell:o=>e(ke,{title:"Delete Row",children:e(Fe,{disabled:m,sx:{color:m?"grey.400":"red"},onClick:()=>u(o.row.Id,3),children:e($e,{})})})}],bo=(u,n,D,O,c,t,m)=>[{field:"Id",headerName:"ID",hide:!0},{field:"Old Parent Node",headerName:"Old Parent Node",flex:1,editable:!1,renderCell:o=>e("div",{style:{padding:"8px",fontSize:"14px",color:"#666"},children:o.row["Old Parent Node"]||""})},{field:"New Parent Node",headerName:"New Parent Node",flex:1,renderCell:o=>e(Ne,{value:o.row["New Parent Node"],placeholder:"Enter New Parent Node",maxLength:10,rowId:o.row.Id,fieldName:"New Parent Node",tableKey:"MOVE GENERAL LEDGER",addToChangeLog:c,updateDuplicateCheckLists:t,isDuplicateCheckField:!1})},{field:"Selected General Ledger",headerName:"Selected General Ledger",flex:1,renderCell:o=>e(Ne,{value:o.row["Selected General Ledger"],placeholder:"Enter General Ledger",maxLength:10,rowId:o.row.Id,fieldName:"Selected General Ledger",tableKey:"MOVE GENERAL LEDGER",fetchOldData:!0,fetchFunction:O,oldDataField:"Old Parent Node",addToChangeLog:c,updateDuplicateCheckLists:t,isDuplicateCheckField:!1})},{field:"Action",headerName:"Action",renderCell:o=>e(ke,{title:"Delete Row",children:e(Fe,{disabled:m,sx:{color:m?"grey.400":"red"},onClick:()=>u(o.row.Id,4),children:e($e,{})})})}],_o=(u,n,D,O,c,t,m)=>[{field:"Id",headerName:"ID",hide:!0},{field:"Parent Node",headerName:"Parent Node",flex:1,renderCell:o=>e("div",{style:{padding:"8px",fontSize:"14px",color:"#666"},children:o.row["Parent Node"]||""})},{field:"Selected General Ledger",headerName:"Selected General Ledger",flex:1,renderCell:o=>e(Ne,{value:o.row["Selected General Ledger"],placeholder:"Enter General Ledger",maxLength:10,rowId:o.row.Id,fieldName:"Selected General Ledger",tableKey:"REMOVE GENERAL LEDGER",fetchOldData:!0,fetchFunction:O,oldDataField:"Parent Node",addToChangeLog:c,updateDuplicateCheckLists:t,isDuplicateCheckField:!1})},{field:"Action",headerName:"Action",renderCell:o=>e(ke,{title:"Delete Row",children:e(Fe,{disabled:m,sx:{color:m?"grey.400":"red"},onClick:()=>u(o.row.Id,5),children:e($e,{})})})}],Po=(u,n,D,O,c,t,m)=>[{field:"Id",headerName:"ID",hide:!0},{field:"Parent Node",headerName:"Parent Node",flex:1,renderCell:o=>e("div",{style:{padding:"8px",fontSize:"14px",color:"#666"},children:o.row["Parent Node"]||""})},{field:"Deleted Node",headerName:"Deleted Node",flex:1,renderCell:o=>e(Ne,{value:o.row["Deleted Node"],placeholder:"Enter Node to Delete",maxLength:10,rowId:o.row.Id,fieldName:"Deleted Node",tableKey:"DELETE NODE",addToChangeLog:c,updateDuplicateCheckLists:t,isDuplicateCheckField:!1})},{field:"Action",headerName:"Action",renderCell:o=>e(ke,{title:"Delete Row",children:e(Fe,{disabled:m,sx:{color:m?"grey.400":"red"},onClick:()=>u(o.row.Id,6),children:e($e,{})})})}],Ke=[{label:"New Nodes",value:"1",key:"NEW NODES"},{label:"Description Change",value:"2",key:"DESCRIPTIONS"},{label:"Add Profit Centers",value:"3",key:"PROFIT CENTERS"},{label:"Move Node",value:"4",key:"MOVE NODE"},{label:"Move Profit Center",value:"5",key:"MOVE PROFIT CENTER"},{label:"Remove Profit Center",value:"6",key:"REMOVE PROFIT CENTER"},{label:"Delete Node",value:"7",key:"DELETE NODE"}],yo=()=>{const u=Je(),[n,D]=d.useState(0),[O,c]=d.useState(0),[t,m]=d.useState(100),{showSnackbar:o}=ps(),K=Ye(),j=new URLSearchParams(K.search).get("reqBench"),N=I(r=>r.hierarchyData.DisplayRecords);I(r=>r.hierarchyData.changeLog||[]);const G=I(r=>r.hierarchyData.nodesListForDBDuplicateCheck||[]),F=I(r=>r.hierarchyData.descListForDBDuplicateCheck||[]),J=I(r=>r.userManagement.userData),{fetchOldDescriptionForNode:ue,fetchOldParentForNode:pe,fetchOldParentForObject:Ee}=_t(),Pe=I(r=>{var p;return(p=r.hierarchyData.requestHeaderData)==null?void 0:p.RequestStatus}),De=j&&!ut.includes(Pe);d.useEffect(()=>{H()},[]);const H=()=>{const r=new Set,p=new Set;((N==null?void 0:N["NEW NODES"])||[]).forEach(s=>{var E,T;(E=s["New Node"])!=null&&E.trim()&&r.add(s["New Node"].trim().toUpperCase()),(T=s.Description)!=null&&T.trim()&&p.add(s.Description.trim().toUpperCase())}),((N==null?void 0:N.DESCRIPTIONS)||[]).forEach(s=>{var E;(E=s["New Description"])!=null&&E.trim()&&p.add(s["New Description"].trim().toUpperCase())});const f=Array.from(r),R=Array.from(p);(f.length>0||G.length===0)&&u(it(f)),(R.length>0||F.length===0)&&u(lt(R))},ie=(r,p="add")=>{var f,R;if(!(r!=null&&r.trim()))return;const i=r.trim().toUpperCase();let g=[...G];if(p==="add"&&!g.some(s=>s.toUpperCase()===i))g.push(i);else if(p==="remove")g=g.filter(s=>s.toUpperCase()!==i);else if(p==="replace"){const s=(f=r.old)==null?void 0:f.trim().toUpperCase(),E=(R=r.new)==null?void 0:R.trim().toUpperCase();s&&E&&s!==E&&(g=g.filter(T=>T.toUpperCase()!==s),g.some(T=>T.toUpperCase()===E)||g.push(E))}u(it(g))},Z=(r,p="add")=>{var f,R;if(!(r!=null&&r.trim()))return;const i=r.trim().toUpperCase();let g=[...F];if(p==="add"&&!g.some(s=>s.toUpperCase()===i))g.push(i);else if(p==="remove")g=g.filter(s=>s.toUpperCase()!==i);else if(p==="replace"){const s=(f=r.old)==null?void 0:f.trim().toUpperCase(),E=(R=r.new)==null?void 0:R.trim().toUpperCase();s&&E&&s!==E&&(g=g.filter(T=>T.toUpperCase()!==s),g.some(T=>T.toUpperCase()===E)||g.push(E))}u(lt(g))},$=r=>{let p=[...G],i=[...F];r.forEach(g=>{const{type:f,value:R,action:s}=g;if(f==="node"&&(R!=null&&R.trim())){const E=R.trim().toUpperCase();s==="add"&&!p.some(T=>T.toUpperCase()===E)?p.push(E):s==="remove"&&(p=p.filter(T=>T.toUpperCase()!==E))}else if(f==="desc"&&(R!=null&&R.trim())){const E=R.trim().toUpperCase();s==="add"&&!i.some(T=>T.toUpperCase()===E)?i.push(E):s==="remove"&&(i=i.filter(T=>T.toUpperCase()!==E))}}),u(it(p)),u(lt(i))},le=(r,p)=>{if(!(r!=null&&r.trim()))return!1;const i=r.trim().toUpperCase();return p==="node"?G.some(g=>g.toUpperCase()===i):p==="desc"?F.some(g=>g.toUpperCase()===i):!1},V=(r,p,i={})=>{const g={id:No(),type:r,description:p,updatedBy:(J==null?void 0:J.emailId)||"<EMAIL>",updatedOn:Lt().utc().format("YYYY-MM-DDTHH:mm:ss[Z]"),...i};u(gs(g))},q=(r,p,i=null,g=null,f=null)=>{if(u(hs({...N,[r]:p})),i&&g){const R=Do(i,g,f);V(i,R,{tableName:r,rowData:{...g},oldRowData:f?{...f}:null})}},re=r=>String((r.length>0?Math.max(...r.map(p=>parseInt(p.Id))):0)+1),_=(r,p)=>c(p),fe=r=>{m(r.target.value),c(0)},k=()=>{const r=Ke[n],p=(N==null?void 0:N[r.key])||[];let i={Id:re(p),"Updated By":J==null?void 0:J.emailId,"Updated On":Lt().utc().format("YYYY-MM-DD HH:mm:ss.SSS")};switch(n){case 0:i={...i,"Parent Node":"","New Node":"",Description:"","Person Responsible":""};break;case 1:i={...i,"Parent Node":"","Old Description":"","New Description":""};break;case 2:i={...i,Node:"","Profit Center":""};break;case 3:i={...i,"Old Parent Node":"","New Parent Node":"","Selected Node":""};break;case 4:i={...i,"Old Parent Node":"","New Parent Node":"","Selected Profit Center":""};break;case 5:i={...i,"Parent Node":"","Selected Profit Center":""};break;case 6:i={...i,"Parent Node":"","Deleted Node":""};break;case 7:i={...i,"Parent Node":"","Old Person Responsible":"","New Person Responsible":""};break}const g=[...p,i];q(r.key,g),V("ROW_CREATED",`New ${r.label.toLowerCase()} row created`,{stepLabel:r.label,rowId:i.Id})},ae=(r,p)=>{const i=Ke[p],g=(N==null?void 0:N[i.key])||[],f=g.find(s=>s.Id===r),R=g.filter(s=>s.Id!==r);if(q(i.key,R),f){const s=[];switch(p){case 0:f["New Node"]&&s.push({type:"node",value:f["New Node"],action:"remove"}),f.Description&&s.push({type:"desc",value:f.Description,action:"remove"});break;case 1:f["New Description"]&&s.push({type:"desc",value:f["New Description"],action:"remove"});break}s.length>0&&$(s);let E=`Row deleted from ${i.label}`;switch(p){case 0:f["New Node"]&&f["Parent Node"]&&(E=`Deleted new node "${f["New Node"]}" under "${f["Parent Node"]}"`);break;case 1:f["Parent Node"]&&(E=`Cancelled description change for node "${f["Parent Node"]}"`);break;case 2:f["Profit Center"]&&f.Node&&(E=`Cancelled adding profit center "${f["Profit Center"]}" to node "${f.Node}"`);break;case 3:f["Selected Node"]&&(E=`Cancelled move operation for node "${f["Selected Node"]}"`);break;case 4:f["Selected Profit Center"]&&(E=`Cancelled move operation for profit center "${f["Selected Profit Center"]}"`);break;case 5:f["Selected Profit Center"]&&f["Parent Node"]&&(E=`Cancelled removal of profit center "${f["Selected Profit Center"]}" from "${f["Parent Node"]}"`);break;case 6:f["Deleted Node"]&&f["Parent Node"]&&(E=`Cancelled deletion of node "${f["Deleted Node"]}" from "${f["Parent Node"]}"`);break;case 7:f["Parent Node"]&&(E=`Cancelled person responsible change for node "${f["Parent Node"]}"`);break}V(te.DELETE_ROW,E,{deletedRow:{...f},stepLabel:i.label})}},de=(r,p)=>console.log("Selected rows:",r,"for step:",p),oe=(()=>{const r=Ke[n],p=(N==null?void 0:N[r.key])||[];let i=[];const g=[ae,ue,pe,Ee,V,{updateNodesListForDuplicateCheck:ie,updateDescListForDuplicateCheck:Z,checkForDuplicates:le,batchUpdateDuplicateLists:$},De];switch(n){case 0:i=Ro(...g);break;case 1:i=Ao(...g);break;case 2:i=Lo(...g);break;case 3:i=Oo(...g);break;case 4:i=bo(...g);break;case 5:i=_o(...g);break;case 6:i=Po(...g);break;default:i=[]}return{rows:p,columns:i,title:r.label,key:r.key}})(),z=r=>{const p=Ke[r],i=(N==null?void 0:N[p.key])||[];if(i.length===0)return{isValid:!0,message:""};const f={0:["Parent Node","New Node","Description"],1:["Parent Node","New Description"],2:["Node","General Ledger"],3:["New Parent Node","Selected Node"],4:["New Parent Node","Selected General Ledger"],5:["Selected General Ledger"],6:["Deleted Node"],7:["Parent Node","New Person Responsible"]}[r]||[];for(const R of i)for(const s of f)if(!R[s]||R[s].toString().trim()==="")return{isValid:!1,message:`Please fill all required fields in ${p.label} table. Missing: ${s}`};return{isValid:!0,message:""}},ge=r=>{const p=z(n);if(!p.isValid){o(`${p.message}`,"error");return}D(r)};return Q(X,{sx:{display:"flex",flexDirection:"column",height:"100%"},children:[e(X,{sx:{width:"100%",scrollbarWidth:"none","&::-webkit-scrollbar":{display:"none"}},children:e(Ns,{nonLinear:!0,activeStep:n,sx:{mb:2},alternativeLabel:!0,children:Ke.map((r,p)=>e(Ds,{children:e(ms,{onClick:()=>ge(p),children:r.label})},r.value))})}),e(X,{sx:{flexGrow:1,overflowY:"auto",scrollbarWidth:"none","&::-webkit-scrollbar":{display:"none"}},children:e(qe,{container:!0,children:e(qe,{item:!0,md:12,sx:{backgroundColor:"white",maxHeight:"max-content",height:"max-content",borderRadius:"8px",border:"1px solid #E0E0E0",mt:.25,boxShadow:"0px 2px 14px 0px rgba(48, 38, 185, 0.10)",...Es},children:Q(X,{sx:{width:"100%",height:"auto",display:"flex",flexDirection:"column",justifyContent:"space-between"},children:[e(X,{sx:{display:"flex",justifyContent:"space-between",alignItems:"center",mt:1,mb:1},children:e(X,{children:e(Ge,{variant:"contained",disabled:De,onClick:k,sx:{ml:1},children:"Add Row"})})}),oe.columns.length>0&&e(pr,{width:"100%",rowHeight:70,rows:oe.rows,columns:oe.columns,title:`This table shows the ${oe.title}`,rowCount:oe.rows.length,page:O,pageSize:t,onPageChange:_,onPageSizeChange:fe,getRowIdValue:"Id",hideFooter:!0,checkboxSelection:!1,callback_onRowDoubleClick:r=>console.log("params",r.row),onRowsSelectionHandler:r=>de(r,n),stopPropagation_Column:"action",status_onRowDoubleClick:!0,showCustomNavigation:!0})]})})})})]})},Io=u=>{var Z,$,le,V;const n=I(q=>q.userManagement.taskData),D=Ye(),O=new URLSearchParams(D.search),c=O.get("RequestType"),t=O.get("reqBench"),m=I(q=>{var re;return(re=q.hierarchyData.requestHeaderData)==null?void 0:re.RequestStatus}),o=t&&!ut.includes(m)||c===((Z=b)==null?void 0:Z.CHANGE_WITH_UPLOAD),{getButtonsDisplayGlobal:K}=Ts();d.useEffect(()=>{(n!=null&&n.ATTRIBUTE_1||c)&&K("Hierarchy Node (General Ledger)","MDG_DYN_BTN_DT","v3")},[n]);const{showTree:M,blurLoading:j,openSnackBar:N,alertMsg:G,alertType:F,filteredButtons:J,requestorPayload:ue,loadForFetching:pe,initialNodeData:Ee,handleButtonClick:Pe,handleSnackBarClose:De}=_t(),H=q=>Array.isArray(q)&&q.length===1&&typeof q[0]=="object"&&q[0].code&&q[0].desc?e(Xe,{component:"span",variant:"body1",children:q[0].code}):typeof q=="string"?q:JSON.stringify(q),ie=({requestorPayload:q,renderValue:re})=>!q||!Object.keys(q).length?null:e(X,{sx:{backgroundColor:Qe.primary.whiteSmoke,px:2,py:1,borderBottom:`1px solid ${Qe.primary.whiteSmoke}`,borderRadius:"5px",mb:2},children:e(X,{sx:{display:"flex",flexWrap:"wrap",gap:3},children:Object.keys(q).map(_=>Q(X,{sx:{display:"flex",alignItems:"center",gap:1},children:[Q(Xe,{variant:"body1",sx:{fontWeight:700,color:Qe.primary.grey},children:[_," :"]}),e(X,{sx:{display:"flex",alignItems:"center"},children:re(q[_])})]},_))})});return Q("div",{children:[Q(qe,{container:!0,sx:{height:"80vh",overflow:"hidden"},children:[Q(qe,{item:!0,md:c===(($=b)==null?void 0:$.CHANGE_WITH_UPLOAD)?6:12,sx:{overflowY:"auto",height:"100%",p:2,pr:1,borderRight:"1px solid #e0e0e0",scrollbarWidth:"none","&::-webkit-scrollbar":{display:"none"}},children:[e(ie,{requestorPayload:ue,renderValue:H}),M&&e(Eo,{initialRawTreeData:Ee,editmode:!o,object:"CEG",moduleObject:(le=be)==null?void 0:le.GL})]}),c===((V=b)==null?void 0:V.CHANGE_WITH_UPLOAD)&&e(qe,{item:!0,md:6,sx:{overflowY:"auto",height:"100%",p:2,pl:1,scrollbarWidth:"none","&::-webkit-scrollbar":{display:"none"}},children:e(yo,{})})]}),e(ct,{blurLoading:pe||j}),e(Cs,{openSnackBar:N,alertMsg:G,handleSnackBarClose:De,alertType:F}),e(Er,{})]})},ba=()=>{var xt,Gt,wt,qt,kt,Ft,Ht,Bt,Mt,$t,vt,Ut,Wt,Yt;const{toPDF:u,targetRef:n}=fr({filename:"my-component.pdf"}),{t:D}=hr(),{customError:O}=bt(),[c,t]=d.useState(!1),[m,o]=d.useState(!1),[K,M]=d.useState([]),[j,N]=d.useState(!1),[G,F]=d.useState(!1),[J,ue]=d.useState(""),[pe,Ee]=d.useState(!1),[Pe,De]=d.useState([]),[H,ie]=d.useState(!1),[Z,$]=d.useState(!1),[le,V]=d.useState(""),[q,re]=d.useState(),[_,fe]=d.useState(""),[k,ae]=d.useState(!1),[de,Re]=d.useState(!1),[oe,z]=d.useState(!1),[ge,r]=d.useState(""),[p,i]=d.useState(!1),[g,f]=d.useState("success"),[R,s]=d.useState(!1),[E,T]=d.useState(!1),[A,P]=d.useState(""),[a,y]=d.useState({title:"",message:"",subText:"",buttonText:"",redirectTo:""}),l=Je(),se=I(L=>L.applicationConfig),B=I(L=>L.payload.payloadData),[W,ce]=d.useState(!1),[ye,Ae]=d.useState([]),S=I(L=>{var x;return(x=L.request.requestHeader)==null?void 0:x.requestId}),ve=I(L=>L.request.requestHeader.requestType),Ie=I(L=>{var x;return(x=L.userManagement)==null?void 0:x.taskData}),h=I(L=>L.hierarchyData),Ce=Ot(),[Ze,pt]=d.useState(!0),he=I(L=>L.request.tabValue),Le=I(L=>L.request.requestHeader),{buttonsLoading:Ss,filteredButtons:Pt,handleButtonClick:He,showWfLevels:Rs,wfLevels:As}=_t({setSuccessDialogOpen:Re,setDialogData:y,selectedLevel:A}),Ls=["Request Header","Hierarchy Tree","Attachments & Remarks","Preview"],[xo,yt]=d.useState([!1]),Os=L=>{l(ze(L))},Et=Ye(),C=Et.state,Oe=new URLSearchParams(Et.search.split("?")[1]).get("RequestId"),ft=new URLSearchParams(Et.search),Te=ft.get("RequestId"),Se=ft.get("RequestType"),Ue=ft.get("reqBench"),bs={requestId:Te||"",isChild:!!((xt=h==null?void 0:h.requestHeaderData)!=null&&xt.childRequestId)},_s=()=>{ie(!0)},ht=()=>{ie(!1)},Ps=()=>{$(!0)},ys=L=>{$(L)},Is=()=>{_==="success"?Ce("/requestBench"):ht()},xs=()=>{o(!0)},Gs=L=>{let x="";Se===b.CREATE_WITH_UPLOAD?x="getAllHierarchyNodeFromExcel":Se===b.CHANGE_WITH_UPLOAD&&(x="getAllHierarchyNodeFromExcelForChange"),r("Initiating Excel Upload"),z(!0);const Y=new FormData;[...L].forEach(ee=>Y.append("files",ee)),Y.append("requestId",Oe||"");const U=ee=>{var v,ne;ee.statusCode===200?(Ee(!1),z(!1),r(""),Ce((v=xe)==null?void 0:v.REQUEST_BENCH)):(Ee(!1),z(!1),r(""),Ce((ne=xe)==null?void 0:ne.REQUEST_BENCH))},me=ee=>{var v;z(!1),r(""),Ce((v=xe)==null?void 0:v.REQUEST_BENCH)};we(`/${nt}/massAction/${x}`,"postformdata",U,me,Y)},ws=async(L=null)=>new Promise((x,Y)=>{z(!0);const U=L||Te,me=Yr(ot.CURRENT_TASK,!0,{}),ee=Se||(Ie==null?void 0:Ie.ATTRIBUTE_2)||(me==null?void 0:me.ATTRIBUTE_2),v=(C==null?void 0:C.childRequestIds)!=="Not Available";let ne=Ue?{parentId:v?"":C==null?void 0:C.requestId,massChangeId:v&&(ee===b.CHANGE||ee===b.CHANGE_WITH_UPLOAD)?U:"",massCreationId:v&&(ee===b.CREATE||ee===b.CREATE_WITH_UPLOAD)?U:""}:{parentId:"",massChangeId:ee===b.CHANGE||ee===b.CHANGE_WITH_UPLOAD?U:"",massCreationId:ee===b.CREATE||ee===b.CREATE_WITH_UPLOAD?U:""};const je=async Be=>{var Nt,et,tt,jt,zt,Kt,Qt,Xt,Jt,Zt,Vt,es,ts,ss;try{z(!1);const w=(Be==null?void 0:Be.body)||{},{ChartOfAccount:Dt,ControllingArea:mt,ParentNode:gt,ParentDesc:rs,RequestType:st,HierarchyTree:$s,ChangeLogId:vs,ErrorLogId:Us,GeneralInformation:Ws,Torequestheaderdata:Ys,...js}=w,zs={ReqCreatedBy:(Nt=w==null?void 0:w.Torequestheaderdata)==null?void 0:Nt.ReqCreatedBy,RequestStatus:(et=w==null?void 0:w.Torequestheaderdata)==null?void 0:et.RequestStatus,Region:(tt=w==null?void 0:w.Torequestheaderdata)==null?void 0:tt.Region,ReqCreatedOn:new Date().toISOString(),ReqUpdatedOn:new Date().toISOString(),RequestType:(jt=w==null?void 0:w.Torequestheaderdata)==null?void 0:jt.RequestType,RequestDesc:(zt=w==null?void 0:w.Torequestheaderdata)==null?void 0:zt.RequestDesc,RequestPriority:(Kt=w==null?void 0:w.Torequestheaderdata)==null?void 0:Kt.RequestPriority,LeadingCat:(Qt=w==null?void 0:w.Torequestheaderdata)==null?void 0:Qt.LeadingCat,RequestId:(Xt=w==null?void 0:w.Torequestheaderdata)==null?void 0:Xt.RequestId,TemplateName:(Jt=w==null?void 0:w.Torequestheaderdata)==null?void 0:Jt.TemplateName};let Ct={};(st===((Zt=b)==null?void 0:Zt.CREATE)||st===((Vt=b)==null?void 0:Vt.CREATE_WITH_UPLOAD))&&(Ct={"Chart Of Accounts":Dt,"Controlling Area":mt,"Cost Element Group":gt,"Cost Element Group Description":rs}),(st===((es=b)==null?void 0:es.CHANGE)||st===((ts=b)==null?void 0:ts.CHANGE_WITH_UPLOAD))&&(Ct={"Chart Of Accounts":Dt,"Controlling Area":mt,"Cost Element Group":gt}),l(is(Ct)),l(ns(zs));const Ks={ChartOfAccount:Dt,ControllingArea:mt,ParentNode:gt,ParentDesc:rs,ChangeLogId:vs,ErrorLogId:Us,GeneralInformation:Ws,treeData:[$s],requestHeaderData:{...Ys,childRequestId:(ss=w==null?void 0:w.ToChildHeaderdata)==null?void 0:ss.RequestId},...js};l(zr({data:Ks}));const Qs=await qs(Te);l(gs(Qs||[])),x()}catch(w){z(!1),O(_e.ERROR_GET_DISPLAY_DATA),Y(w)}},Ve=Be=>{z(!1),O(_e.ERROR_FETCHING_DATA),Y(Be)};we(`/${nt}/data/displayHierarchyTreeNodeStructureFromDb`,"post",je,Ve,ne)});d.useEffect(()=>{var Y;let L=(Y=Vr)==null?void 0:Y[he];const x=Nr(Pt,L);Ae(x)},[he,Pt]),d.useEffect(()=>((async()=>{var x,Y,U;Te?(await ws(Te),(Se===b.CHANGE_WITH_UPLOAD&&!((x=C==null?void 0:C.parentNode)!=null&&x.length)||Se===b.CREATE_WITH_UPLOAD&&!((Y=C==null?void 0:C.parentNode)!=null&&Y.length))&&((C==null?void 0:C.reqStatus)===Me.DRAFT||(C==null?void 0:C.reqStatus)===Me.UPLOAD_FAILED)?(l(ze(0)),N(!1),F(!1)):(Se===b.CREATE||Se===b.CHANGE)&&!((U=C==null?void 0:C.parentNode)!=null&&U.length)&&Ue?(l(ze(0)),N(!1),F(!0)):(l(ze(1)),N(!0),F(!0)),T(!0)):l(ze(0))})(),()=>{l(Dr([])),l(at({})),l(mr()),l(gr()),l(ns({data:{}})),l(Cr([])),l(Tr([])),l(is({})),l(Sr()),l(Rr([])),l(Ar([])),l(Lr({})),l(Or()),l(br()),ls(ot.CURRENT_TASK),ls(ot.ROLE)}),[Oe,l]);const qs=L=>{const x=`/${nt}/node/getTreeModificationHistory?requestId=${L}`;return new Promise((Y,U)=>{we(x,"get",v=>{var ne;(v==null?void 0:v.statusCode)===At.STATUS_200?Y(((ne=v==null?void 0:v.body)==null?void 0:ne.Records)||[]):Y([])},v=>{O(v),U(v)})})};d.useEffect(()=>(ks(),l(_r([])),l(Pr({keyName:"Region",data:yr})),()=>{l(fs({}))}),[]),d.useEffect(()=>{j&&yt([!0])},[j]),d.useEffect(()=>{ue(Ir("CEG")),xr(ot.MODULE,be.CEG)},[]);const ks=()=>{let L={decisionTableId:null,decisionTableName:"MDG_ATTACHMENTS_LIST_DT",version:"v1",rulePolicy:null,validityDate:null,conditions:[{"MDG_CONDITIONS.MDG_ATTACHMENTS_OBJECT_TYPE":"Material","MDG_CONDITIONS.MDG_ATTACHMENTS_SCENARIO":"Create","MDG_CONDITIONS.MDG_ATTACHMENTS_REG_COMP_CODE":1}],systemFilters:null,systemOrders:null,filterString:null};t(!0);const x=U=>{var me,ee,v,ne;if(t(!1),U.statusCode===200){let je=(ee=(me=U==null?void 0:U.data)==null?void 0:me.result[0])==null?void 0:ee.MDG_ATTACHMENTS_ACTION_TYPE,Ve=[];je==null||je.map((Nt,et)=>{var tt={id:et};Ve.push(tt)}),M(Ve);const Be=((ne=(v=U==null?void 0:U.data)==null?void 0:v.result[0])==null?void 0:ne.MDG_ATTACHMENTS_ACTION_TYPE)||[];De(Be)}},Y=U=>{console.log(U)};se.environment==="localhost"?we(`/${ds}/rest/v1/invoke-rules`,"post",x,Y,L):we(`/${ds}/v1/invoke-rules`,"post",x,Y,L)},Fs=()=>{var U,me;const L=(me=(U=We)==null?void 0:U.EXCEL)==null?void 0:me.EXPORT_HIERARCHY_EXCEL;r("Please wait 1-2 minutes while real-time data are exported into the template. Thank you for your patience."),z(!0);const x=ee=>{const v=URL.createObjectURL(ee),ne=document.createElement("a");ne.href=v,ne.setAttribute("download",`${Te}_Data Export.xlsx`),document.body.appendChild(ne),ne.click(),document.body.removeChild(ne),URL.revokeObjectURL(v),z(!1),r(""),i(!0),re(`${Te}_Data Export.xlsx has been exported successfully.`),f("success"),Hs()},Y=()=>{};we(`/${jr}${L}?reqId=${Te}&attachmentType=Export_Excel`,"getblobfile",x,Y)},Hs=()=>{s(!0)},Bs=()=>{s(!1)},Ms=()=>{var L,x,Y;Oe&&!Ue?Ce((L=xe)==null?void 0:L.MY_TASK):Ue?Ce((x=xe)==null?void 0:x.REQUEST_BENCH):!Oe&&!Ue&&Ce((Y=xe)==null?void 0:Y.MASTER_DATA_CEG)},It=()=>{ae(!1)};return Q(cs,{children:[Ze&&e(ct,{blurLoading:Ss||oe,loaderMessage:ge}),Q(X,{sx:{padding:2},children:[Q(qe,{sx:{display:"flex",justifyContent:"space-between",alignItems:"center"},children:[S||Oe?Q(Xe,{variant:"h6",sx:{mb:1,textAlign:"left",display:"flex",alignItems:"center",gap:1},children:[e(no,{sx:{fontSize:"1.5rem"}}),D("Request Header ID"),":"," ",e("span",{children:S?(Le==null?void 0:Le.requestPrefix)+""+(Le==null?void 0:Le.requestId):Oe})]}):e("div",{style:{flex:1}}),he===1&&Q(X,{sx:{display:"flex",justifyContent:"flex-end",gap:"1rem"},children:[e(Ge,{variant:"outlined",size:"small",title:"Download Error Report",disabled:!Te,onClick:()=>ce(!0),color:"primary",children:e(Br,{sx:{padding:"2px"}})}),e(Ge,{variant:"outlined",disabled:!1,size:"small",onClick:Ps,title:"Change Log",children:e(io,{sx:{padding:"2px"}})}),(Se===((Gt=b)==null?void 0:Gt.CREATE_WITH_UPLOAD)||Se===((wt=b)==null?void 0:wt.CHANGE_WITH_UPLOAD))&&e(Ge,{variant:"outlined",disabled:!Te,size:"small",onClick:Fs,title:"Export Excel",children:e(lo,{sx:{padding:"2px"}})})]}),Z&&e(oo,{open:!0,closeModal:ys,requestId:S||Oe.slice(4),requestType:B==null?void 0:B.RequestType}),he===3&&e(X,{sx:{display:"flex",justifyContent:"flex-end"},children:e(Ge,{variant:"outlined",color:"primary",startIcon:e(ao,{}),onClick:u,children:D("Export Preview")})})]}),e(Fe,{onClick:()=>{var L,x;if(Ue&&!((L=ut)!=null&&L.includes(B==null?void 0:B.RequestStatus))){Ce((x=xe)==null?void 0:x.REQUEST_BENCH);return}ae(!0)},color:"primary","aria-label":"upload picture",component:"label",sx:{left:"-10px"},title:"Back",children:e(Mr,{sx:{fontSize:"25px",color:"#000000"}})}),e(Ns,{nonLinear:!0,activeStep:he,sx:{display:"flex",alignItems:"center",justifyContent:"center",margin:"25px 14%",marginTop:"-35px"},children:Ls.map((L,x)=>e(Ds,{children:e(ms,{color:"error",disabled:x===1&&!j||x===2&&!G||x===3&&!j&&!G,onClick:()=>Os(x),sx:{fontSize:"50px",fontWeight:"bold"},children:e("span",{style:{fontSize:"15px",fontWeight:"bold"},children:L})})},L))}),e($r,{dialogState:H,openReusableDialog:_s,closeReusableDialog:ht,dialogTitle:le,dialogMessage:q,handleDialogConfirm:ht,dialogOkText:"OK",handleOk:Is,dialogSeverity:_}),e(fo,{dialogState:W,closeReusableDialog:()=>ce(!1),module:(qt=be)==null?void 0:qt.CEG,isHierarchyCheck:!0}),e(Qr,{open:de,onClose:()=>Re(!1),title:a.title,message:a.message,subText:a.subText,buttonText:a.buttonText,redirectTo:a.redirectTo}),e(ct,{blurLoading:oe,loaderMessage:ge}),he===0&&Q(cs,{children:[e(ho,{setIsSecondTabEnabled:N,setIsAttachmentTabEnabled:F,requestStatus:C!=null&&C.reqStatus?C==null?void 0:C.reqStatus:Me.ENABLE_FOR_FIRST_TIME,downloadClicked:m,setDownloadClicked:o}),(Se===b.CHANGE_WITH_UPLOAD||Se===b.CREATE_WITH_UPLOAD)&&((C==null?void 0:C.reqStatus)==Me.DRAFT&&!((kt=C==null?void 0:C.material)!=null&&kt.length)||(C==null?void 0:C.reqStatus)==Me.UPLOAD_FAILED)&&e(co,{handleDownload:xs,setEnableDocumentUpload:Ee,enableDocumentUpload:pe,handleUploadMaterial:Gs}),((B==null?void 0:B.RequestType)===((Ft=b)==null?void 0:Ft.CHANGE)||(B==null?void 0:B.RequestType)===((Ht=b)==null?void 0:Ht.CHANGE_WITH_UPLOAD))&&!Te&&(B==null?void 0:B.DirectAllowed)!=="X"&&(B==null?void 0:B.DirectAllowed)!==void 0&&Q(Xe,{sx:{fontSize:"13px",fontWeight:"500",color:(Mt=(Bt=Qe)==null?void 0:Bt.error)==null?void 0:Mt.dark,marginTop:"1rem",marginLeft:"0.5rem"},children:[Q(X,{component:"span",sx:{fontWeight:"bold"},children:[D("Note"),":"]})," ","You are not authorized to Tcode"," ",Q(X,{component:"span",sx:{fontWeight:"bold"},children:[" ","MM02."]})]})]}),he===1&&e(Io,{setIsAttachmentTabEnabled:!0,setCompleted:yt,downloadClicked:m,setDownloadClicked:o}),he===2&&e(Xr,{requestStatus:C!=null&&C.reqStatus?C==null?void 0:C.reqStatus:Me.ENABLE_FOR_FIRST_TIME,attachmentsData:Pe,requestIdHeader:S?Gr(ve,S):Oe,pcNumber:J,module:($t=be)==null?void 0:$t.CEG,artifactName:wr.CEG}),he===3&&e(X,{ref:n,sx:{width:"100%",overflow:"auto"},children:e(Jr,{requestStatus:C!=null&&C.reqStatus?C==null?void 0:C.reqStatus:Me.ENABLE_FOR_FIRST_TIME,module:(vt=be)==null?void 0:vt.CEG,payloadData:h,payloadForPreviewDownloadExcel:bs})}),he!=0&&e(Zr,{handleSaveAsDraft:He,handleSubmitForReview:He,handleSubmitForApprove:He,handleSendBack:He,handleCorrection:He,handleRejectAndCancel:He,handleValidateAndSyndicate:He,filteredButtons:ye,moduleName:(Ut=be)==null?void 0:Ut.CEG,isHierarchy:!0,showWfLevels:Rs,selectedLevel:A,workFlowLevels:As,setSelectedLevel:P})]}),e(Cs,{openSnackBar:R,alertMsg:q,alertType:g,handleSnackBarClose:Bs}),k&&Q(qr,{isOpen:k,titleIcon:e(vr,{size:"small",sx:{color:(Yt=(Wt=Qe)==null?void 0:Wt.secondary)==null?void 0:Yt.amber,fontSize:"20px"}}),Title:"Warning",handleClose:It,children:[e(Wr,{sx:{mt:2},children:Ur.LEAVE_PAGE_MESSAGE}),Q(kr,{children:[e(Ge,{variant:"outlined",size:"small",sx:{...Fr},onClick:It,children:D("No")}),e(Ge,{variant:"contained",size:"small",sx:{...Hr},onClick:Ms,children:D("Yes")})]})]})]})};export{ba as default};
