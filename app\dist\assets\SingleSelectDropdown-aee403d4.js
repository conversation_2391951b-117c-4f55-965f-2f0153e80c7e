import{t as f,j as s,d as C,c as S,aa as y,bD as E,F as I,Z as i,b3 as T,dO as k,dQ as w}from"./index-f7d9b065.js";const O=8,b=f.createContext({}),P=f.forwardRef((d,t)=>{const a=f.useContext(b);return s("div",{ref:t,...d,...a})});function M(d){const{children:t,...a}=d,l=[];t.forEach(c=>{l.push(c)});const m=l.length,n=w.HEIGHT,o=()=>m>8?8*n:l.length*n;return s("div",{ref:d.ref,children:s(b.Provider,{value:a,children:s(k,{itemData:l,height:o()+2*O,width:"100%",outerElementType:P,innerElementType:"ul",itemSize:n,overscanCount:5,itemCount:m,children:({data:c,index:h,style:u})=>{const x=c[h],r={...u,top:u.top+O};return f.cloneElement(x,{style:r})}})})})}const A=({options:d=[],value:t=null,onChange:a,placeholder:l="SELECT OPTION",disabled:m=!1,minWidth:n,isFieldError:o,handleInputChange:c,isLoading:h=!1,isOptionDisabled:u=()=>!1})=>{const x=f.useMemo(()=>t?typeof t=="string"||typeof t=="number"?d.find(r=>r.code===t.toString())||null:t:null,[t,d]);return s(T,{fullWidth:!0,size:"small",options:d,value:x,onChange:(r,e)=>a(e),getOptionDisabled:u||(()=>!1),getOptionLabel:r=>r!=null&&r.desc?`${r.code} - ${r.desc}`:(r==null?void 0:r.code)||"",isOptionEqualToValue:(r,e)=>r.code===e.code,ListboxComponent:M,disableCloseOnSelect:!1,renderOption:(r,e)=>s(C,{...r,component:"li",style:{fontSize:12,padding:"8px 16px",width:"100%",cursor:"pointer",display:"flex",alignItems:"start"},children:S("span",{style:{whiteSpace:"nowrap",overflow:"hidden",textOverflow:"ellipsis"},title:`${e==null?void 0:e.code}${e!=null&&e.desc?` - ${e==null?void 0:e.desc}`:""}`,children:[s("strong",{children:e==null?void 0:e.code}),e!=null&&e.desc?` - ${e==null?void 0:e.desc}`:""]})}),renderInput:r=>{var e,g;return s(y,{...r,placeholder:l==null?void 0:l.toUpperCase(),title:t!=null&&t.code?t==null?void 0:t.code:t,fullWidth:!0,onChange:c||void 0,InputProps:{...r.InputProps,endAdornment:S(I,{children:[h?s(E,{size:20,sx:{mr:1}}):null,r.InputProps.endAdornment]}),sx:{"& .MuiOutlinedInput-notchedOutline":{borderColor:o&&((g=(e=i)==null?void 0:e.error)==null?void 0:g.dark)}}},sx:{minWidth:n}})},disabled:m,sx:{minWidth:n,"& .MuiAutocomplete-popper":{width:`${n}px !important`},"& .MuiAutocomplete-option":{fontSize:"12px",color:i.black.dark,"&:hover":{backgroundColor:i.primary.whiteSmoke}},"& .MuiAutocomplete-listbox":{padding:"0px !important"},"& .MuiInputBase-root.Mui-disabled":{"& > input":{WebkitTextFillColor:i.black.dark,color:i.black.dark}}}})};export{A as S};
