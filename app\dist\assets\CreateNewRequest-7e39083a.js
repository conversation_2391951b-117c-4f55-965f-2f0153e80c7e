import{n as oe,s as bn,u as sl,aX as $,C as he,bI as rn,aT as gt,dG as Zi,xi as Qi,r as i,g as <PERSON>,a as Ll,o as Zo,aP as vl,M as ws,cz as Wo,ep as qo,xj as er,au as Ce,fw as Bs,f1 as qn,j as n,c as N,O as xt,b1 as tr,d as Gt,B as Ve,an as je,bD as sr,aH as co,ai as kn,aj as Wn,al as Js,b3 as Ol,aa as on,am as zs,aG as Hn,aF as yl,aZ as bl,aD as Qo,bK as ln,xk as nr,xl as lr,xm as or,g3 as Un,aY as ir,ae as Tl,aJ as Qe,ap as ei,xn as Fn,aC as rr,wV as cr,H as ti,wW as dr,wX as ar,dT as Ho,J as si,xo as ur,be as m,xp as fr,xq as gr,xr as Sl,Z as ke,xs as ao,d7 as uo,xt as hr,b9 as ni,b as Er,cZ as li,bf as yn,g5 as oi,cw as ii,xu as ri,xv as ci,d0 as di,ge as Vs,bJ as Sr,xw as Tr,f_ as io,ek as Al,xx as ai,xy as ui,xz as fi,xA as Ds,xB as Ar,em as Fo,bi as Bo,eb as jt,xC as Gn,xD as gi,xE as js,er as bt,cH as $n,a9 as Nl,fy as wo,xF as ml,T as gs,F as Is,xG as hi,a6 as Ft,xH as Ei,ch as _l,b5 as Si,b6 as Rl,aO as Cn,aE as fo,xI as Ti,bE as Ai,ak as pi,bl as Ci,ag as Oi,bm as bi,bn as Ni,bo as Il,bp as qe,bq as mi,xJ as Ln,$ as pr,bP as Cr,bQ as Vo,bG as jo,fY as _i,br as Ri,qT as Po,ad as Ii,fR as Mi,xK as Ml,aK as no,xL as pl,xM as Cl,xN as Or,qF as xi,aU as ts,xO as Di,xP as br,xQ as Li,xR as vi,xS as us,xT as Jo,dJ as go,bh as Ps,cI as Ls,eq as zo,xU as Nr,xV as mr,xW as _r,ao as Rr,ba as Ir,xX as lo,xY as Mr,xZ as xr,I as Dr,x_ as Lr,x$ as vr,g4 as yr,wZ as Gr,w_ as Ur,y0 as $r,y1 as kr,wY as Wr,aA as qr,aB as Hr,bd as oo,K as Fr,aW as Br,bb as wr,bc as Vr,cA as jr,y2 as Pr,y3 as Jr,d8 as zr,a$ as Xr,d6 as Kr,d3 as Yr,d4 as Zr,d5 as Qr,d9 as ec}from"./index-f7d9b065.js";import{F as tc}from"./FilterField-ed1f5dc1.js";import{u as sc}from"./useProfitcenterRequestHeaderConfig-c3b21e37.js";import{u as nc}from"./useChangeMaterialRows-cefb1340.js";import{R as yi,u as Gi,a as Ui,b as $i,c as ki,d as ro,S as lc,G as Wi,o as vn,e as qi,E as oc,f as Xo,C as ic,g as rc,h as cc}from"./RequestDetailsForFC-231ddbc2.js";import{D as dc,u as ac,a as Hi,b as On,c as uc,d as fc,e as gc,A as hc,P as Ec}from"./PreviewPage-634057fa.js";import{u as ho}from"./useMaterialFieldConfig-3bde8f21.js";import{d as el}from"./DeleteOutlineOutlined-8fd07dc7.js";import{d as tl}from"./Description-ab582559.js";import{d as Fi}from"./TaskAlt-0afc1812.js";import Bi from"./AdditionalData-148835c9.js";import{S as ft}from"./SingleSelectDropdown-aee403d4.js";import{d as xl,a as Dl}from"./CloseFullscreen-2870eb3e.js";import{u as wi}from"./useDynamicWorkflowDT-955b7628.js";import{u as Vi}from"./useCustomDtCall-0fd16760.js";import{d as Sc}from"./LibraryAdd-286802b4.js";import{d as Tc}from"./PermIdentityOutlined-0746a749.js";import{d as Ac}from"./FeedOutlined-41109ec9.js";import{d as Ko}from"./TrackChangesTwoTone-7a2ab513.js";import{d as pc}from"./FileUploadOutlined-4a68a28a.js";import{E as Cc}from"./ExcelOperationsCard-49e9ffd2.js";import{u as Oc}from"./useDisplayDataDto-d5eb4f04.js";import"./useChangeLogUpdate-1ba6b2dd.js";import"./AdapterDayjs-2a9281df.js";import"./advancedFormat-4f6292d4.js";import"./customParseFormat-1bc1aa07.js";import"./isBetween-c1c6beb9.js";import"./AutoCompleteType-13f5746b.js";import"./useMobilePicker-9b56b5b6.js";import"./CSSTransition-cd337b47.js";import"./useChangeMaterialRowsRequestor-fc0d44be.js";import"./FilterChangeDropdown-22e24089.js";import"./DatePicker-a8e9bd4a.js";import"./GenericViewGeneral-e6209433.js";import"./Edit-51c94b76.js";import"./createChangeLogTemplate-fc8912a0.js";import"./useFinanceCostingRows-ffbb569f.js";import"./AttachFile-8d552da8.js";import"./UtilDoc-6f590135.js";import"./FileDownloadOutlined-59854a55.js";import"./VisibilityOutlined-b6cd6d28.js";import"./DeleteOutlined-e668453f.js";import"./CloudUpload-0ba6431e.js";import"./utilityImages-067c3dc2.js";import"./Delete-5278579a.js";import"./ReusablePromptBox-e1871d49.js";import"./featureConfig-652a9f8d.js";import"./DataObject-52409c14.js";import"./Download-52c4427b.js";import"./CheckCircleOutline-e186af3e.js";import"./DeleteOutline-584dc929.js";import"./CloudDownload-9a7605e9.js";import"./AttachmentUploadDialog-43cc9099.js";const ji=()=>{const U=oe(J=>J.payload.payloadData),dt=oe(J=>J.applicationConfig),re=oe(J=>{var He;return(He=J.userManagement)==null?void 0:He.taskData}),_=bn(),lt=sl(),tt=new URLSearchParams(lt.search).get("RequestType");return{getRequestHeaderTemplate:()=>{var w,Q,_e;let J={decisionTableId:null,decisionTableName:"MDG_MAT_REQUEST_HEADER_CONFIG",version:"v2",rulePolicy:null,validityDate:null,conditions:[{"MDG_CONDITIONS.MDG_MAT_SCENARIO":(re==null?void 0:re.ATTRIBUTE_2)===((w=$)==null?void 0:w.FINANCE_COSTING)?(Q=$)==null?void 0:Q.FINANCE_COSTING:tt||(U==null?void 0:U.RequestType)||((_e=$)==null?void 0:_e.CREATE),"MDG_CONDITIONS.MDG_MAT_REGION":(U==null?void 0:U.Region)||"US","MDG_CONDITIONS.MDG_MODULE":"Material"}],systemFilters:null,systemOrders:null,filterString:null};const He=z=>{var C,Ze;if(z.statusCode===200){const Ae={"Header Data":((Ze=(C=z==null?void 0:z.data)==null?void 0:C.result[0])==null?void 0:Ze.MDG_MAT_REQUEST_HEADER_CONFIG).sort((ae,Ot)=>ae.MDG_MAT_SEQUENCE_NO-Ot.MDG_MAT_SEQUENCE_NO).map(ae=>({fieldName:ae.MDG_MAT_UI_FIELD_NAME,sequenceNo:ae.MDG_MAT_SEQUENCE_NO,fieldType:ae.MDG_MAT_FIELD_TYPE,maxLength:ae.MDG_MAT_MAX_LENGTH,value:ae.MDG_MAT_DEFAULT_VALUE,visibility:ae.MDG_MAT_VISIBILITY,jsonName:ae.MDG_MAT_JSON_FIELD_NAME}))};_(Zi({tab:"Request Header",data:Ae})),_(Qi(Ae))}},h=z=>{console.log(z)};dt.environment==="localhost"?he(`/${rn}${gt.INVOKE_RULES.LOCAL}`,"post",He,h,J):he(`/${rn}${gt.INVOKE_RULES.PROD}`,"post",He,h,J)}}},bc=({setIsSecondTabEnabled:U,setIsAttachmentTabEnabled:dt,requestStatus:re,downloadClicked:_,setDownloadClicked:lt})=>{var Le,kt,$s,rs,Wt,le,Je,ve;const[et,tt]=i.useState({}),[F,J]=i.useState(!1),[He,h]=i.useState(!1),[w,Q]=i.useState("success"),[_e,z]=i.useState(!1),[C,Ze]=i.useState([]),[I,be]=i.useState(),[Ae,ae]=i.useState({}),[Ot,Ie]=i.useState(!1),[Fe,ot]=i.useState("systemGenerated"),[at,Ne]=i.useState(""),[st,de]=i.useState(""),[Ee,X]=i.useState([]),[B,ne]=i.useState(!1),j=bn(),ht=Yo(),r=oe(R=>R.payload.payloadData),De=oe(R=>R.tabsData.requestHeaderData),Oe=oe(R=>R.tabsData.changeFieldsDT);let zt=oe(R=>R.userManagement.roles);const V=oe(R=>R.payload.payloadData),Y=oe(R=>R.userManagement.userData),ee=oe(R=>{var H,ce;return(ce=(H=R.userManagement)==null?void 0:H.entitiesAndActivities)==null?void 0:ce.Material}),f=oe(R=>R.request.requestHeader),it=oe(R=>R.request.salesOrgDTData),Dt=sl(),v=new URLSearchParams(Dt.search),Xt=v.get("reqBench"),ss=v.get("RequestId"),{t:Lt}=Ll(),{getRequestHeaderTemplate:rt}=ji(),{getChangeTemplate:Bn}=nc();sc();const{fetchOrgData:vs}=ho(),{getDtCall:ns}=Zo(),{customError:ct}=vl(),mn=[{code:"Create",desc:"Create New Material in Application"},{code:"Change",desc:"Modify Existing Material in Application"},{code:"Extend",desc:"Extend Existing Material in Application"},{code:"Create with Upload",desc:"Create New Material with Excel Upload"},{code:"Change with Upload",desc:"Modify Existing Material with Excel Upload"},{code:"Extend with Upload",desc:"Extend Existing Material with Excel Upload"}].filter(R=>ee==null?void 0:ee.includes(R.code)),ys=[{code:"Oncology",desc:""},{code:"Anesthesia/Pain Management",desc:""},{code:"Cardiovascular",desc:""}],_n=[{code:(Le=Bs)==null?void 0:Le.LOGISTIC,desc:""},{code:(kt=Bs)==null?void 0:kt.MRP,desc:""},{code:($s=Bs)==null?void 0:$s.WARE_VIEW_2,desc:""},{code:(rs=Bs)==null?void 0:rs.ITEM_CAT,desc:""},{code:(Wt=Bs)==null?void 0:Wt.SET_DNU,desc:""},{code:(le=Bs)==null?void 0:le.UPD_DESC,desc:""},{code:(Je=Bs)==null?void 0:Je.CHG_STAT,desc:""}],Kt=[{code:"High",desc:""},{code:"Medium",desc:""},{code:"Low",desc:""}];j(ws({keyName:(ve=Wo)==null?void 0:ve.REQUEST_TYPE,data:mn})),j(ws({keyName:"LeadingCat",data:ys})),j(ws({keyName:"RequestPriority",data:Kt})),j(ws({keyName:"TemplateName",data:_n})),!ss&&!Xt&&(j(qo({keyName:"ReqCreatedBy",data:Y==null?void 0:Y.user_id})),j(qo({keyName:"RequestStatus",data:"DRAFT"})));const cn="Basic Data",[Yt,q]=i.useState([cn]),[hs,Ue]=i.useState(""),[Es,we]=i.useState(""),[ye,Ut]=i.useState(!0),[Ss,Ts]=i.useState(!1);i.useEffect(()=>{j(er(Yt))},[j,Yt]);const Zt=()=>{var H,ce;let R=!0;return V&&((H=De[Object.keys(De)])!=null&&H.length)?(ce=De[Object.keys(De)[0]])==null||ce.forEach(x=>{var Ge;!V[x.jsonName]&&x.visibility===((Ge=Qo)==null?void 0:Ge.MANDATORY)&&(R=!1)}):R=!1,R};i.useEffect(()=>{V!=null&&V.MatlType&&Qt(V),Zt()},[V]);const Qt=R=>{var x;const H=Ge=>{Ue(Ge.body[0].MaintStatus.split("")),we(Ge.body[0].MaterialType)},ce=Ge=>{console.log(Ge)};he(`/${Ce}/data/getViewForMaterialType?materialType=${(x=R==null?void 0:R.MatlType)==null?void 0:x.code}`,"get",H,ce)},Me=()=>{z(!0)},ls=()=>{z(!1)},Xs=()=>{var R;lt(!1),Ie(!1),ot("systemGenerated"),ss||ht((R=ln)==null?void 0:R.REQUEST_BENCH)},Tt=R=>{var H;ot((H=R==null?void 0:R.target)==null?void 0:H.value)},Et=()=>{Fe==="systemGenerated"&&(Ks(),Xs()),Fe==="mailGenerated"&&(As(),Xs())},Ks=()=>{Ne("Please wait 1-2 minutes while real-time data are downloaded into the template. Thank you for your patience."),de(!0);let R={region:r==null?void 0:r.Region,scenario:r==null?void 0:r.RequestType,matlType:"ALL",dtName:"MDG_MAT_MASS_CREATE_EXTEND_FIELD_CONFIG",version:"v1",rolePrefix:"",requestId:f!=null&&f.requestId?f==null?void 0:f.requestId:r!=null&&r.RequestId?r==null?void 0:r.RequestId:""};const H=Ge=>{if((Ge==null?void 0:Ge.size)==0){de(!1),Ne(""),h(!0),be("No data found for the selected criteria."),Q("danger"),Me();return}const ue=URL.createObjectURL(Ge),We=document.createElement("a");We.href=ue,We.setAttribute("download",`${(r==null?void 0:r.RequestType)===$.EXTEND_WITH_UPLOAD?"Mass_Extend.xlsx":"Mass_Create.xlsx"}`),document.body.appendChild(We),We.click(),document.body.removeChild(We),URL.revokeObjectURL(ue),de(!1),Ne(""),h(!0),be(`${r!=null&&r.TemplateName?`${r.TemplateName}_Mass Change`:(r==null?void 0:r.RequestType)===$.EXTEND_WITH_UPLOAD?"Mass_Extend":"Mass_Create"}.xlsx has been downloaded successfully.`),Q("success"),Me(),setTimeout(()=>{ht("/requestBench")},2600)},ce=()=>{de(!1)},x=`/${Ce}${(r==null?void 0:r.RequestType)===$.EXTEND_WITH_UPLOAD?gt.EXCEL.DOWNLOAD_EXCEL_FOR_EXTEND:gt.EXCEL.DOWNLOAD_EXCEL}`;he(x,"postandgetblob",H,ce,R)},As=()=>{de(!0);let R={region:r==null?void 0:r.Region,scenario:r==null?void 0:r.RequestType,matlType:"ALL",dtName:"MDG_MAT_MASS_CREATE_EXTEND_FIELD_CONFIG",version:"v1",rolePrefix:"",requestId:f!=null&&f.requestId?f==null?void 0:f.requestId:r!=null&&r.RequestId?r==null?void 0:r.RequestId:""};const H=()=>{var Ge;de(!1),Ne(""),h(!0),be((Ge=ir)==null?void 0:Ge.DOWNLOAD_MAIL_INITIATED),Q("success"),Me(),setTimeout(()=>{var ue;ht((ue=ln)==null?void 0:ue.REQUEST_BENCH)},2600)},ce=()=>{var Ge;de(!1),h(!0),be((Ge=Tl)==null?void 0:Ge.ERR_DOWNLOADING_EXCEL),Q("danger"),Me(),setTimeout(()=>{var ue;ht((ue=ln)==null?void 0:ue.REQUEST_BENCH)},2600)},x=`/${Ce}${(r==null?void 0:r.RequestType)===$.EXTEND_WITH_UPLOAD?gt.EXCEL.DOWNLOAD_EXCEL_FOR_EXTEND_MAIL:gt.EXCEL.DOWNLOAD_EXCEL_MAIL}`;he(x,"post",H,ce,R)},vt=()=>J(!1),Ys=R=>{if(C.includes("Distribution Channel")){const H=x=>X(x==null?void 0:x.body),ce=x=>console.error(x);he(`/${Ce}/data/getDistrChan?salesOrg=${R.code}`,"get",H,ce)}},$t={orgData:["Plant","Sales Organization","Distribution Channel"].map(R=>({info:Ae[R]||{code:"",desc:""},desc:R})),selectedViews:{selectedSections:Yt}},Gs=(R,H)=>{ae(ce=>({...ce,[R]:H})),R==="Sales Organization"&&Ys(H)},Us=`/Date(${Date.now()})/`,Zs=()=>{var ue;Ts(!0);let R=nr(V==null?void 0:V.Region,zt);j(lr({...Y,role:R})),ne(!1);const H=new Date(V==null?void 0:V.ReqCreatedOn).getTime(),ce={RequestId:f!=null&&f.requestId?f==null?void 0:f.requestId:"",Region:(V==null?void 0:V.Region)||"",MatlType:(V==null?void 0:V.MatlType)||"",ReqCreatedBy:(Y==null?void 0:Y.user_id)||"",ReqCreatedOn:H?`/Date(${H})/`:Us,ReqUpdatedOn:H?`/Date(${H})/`:Us,RequestType:(V==null?void 0:V.RequestType)||"",RequestDesc:(V==null?void 0:V.RequestDesc)||"",Division:(V==null?void 0:V.Division)||"",RequestStatus:"DRAFT",RequestPriority:(V==null?void 0:V.RequestPriority)||"",LeadingCat:(V==null?void 0:V.LeadingCat)||"",FieldName:((ue=V==null?void 0:V.FieldName)==null?void 0:ue.join("$^$"))||"",TemplateName:(V==null?void 0:V.TemplateName)||""},x=We=>{var K,ze,ut;if((We==null?void 0:We.statusCode)===Qe.STATUS_200){if(Ts(!1),h(!0),be(We==null?void 0:We.message),Q("success"),Me(),j(ei(We.body)),j(qn({keyName:Wo.REQUEST_ID,data:(K=We==null?void 0:We.body)==null?void 0:K.requestId})),dt(!0),Ut(!1),j(Fn({})),j(rr({})),(r==null?void 0:r.RequestType)===$.CREATE_WITH_UPLOAD||(r==null?void 0:r.RequestType)===$.EXTEND_WITH_UPLOAD){Ie(!0);return}if((r==null?void 0:r.RequestType)===((ze=$)==null?void 0:ze.CHANGE_WITH_UPLOAD)){ne(!0);return}if((r==null?void 0:r.RequestType)===((ut=$)==null?void 0:ut.CHANGE)){const ps=cr(Oe==null?void 0:Oe["Config Data"],r==null?void 0:r.FieldName,["Material","Plant","Sales Org","Distribution Channel","Warehouse","MRP Controller"]);j(ti({...Oe,"Config Data":ps}));const dn=dr(Oe==null?void 0:Oe[r==null?void 0:r.TemplateName],r==null?void 0:r.FieldName);j(ar([...dn]))}setTimeout(()=>{j(Un(1)),U(!0)},2500)}else h(!0),be(We==null?void 0:We.message),Q("error"),Me(),Ts(!1)},Ge=()=>{Ts(!1),h(!0),Q("error"),be("Error occured while saving Request Header"),Me()};he(`/${Ce}/alter/createRequestHeader`,"post",x,Ge,ce)};i.useEffect(()=>{var R;if(_){if((r==null?void 0:r.RequestType)===$.CREATE_WITH_UPLOAD||(r==null?void 0:r.RequestType)===$.EXTEND_WITH_UPLOAD){Ie(!0);return}if((r==null?void 0:r.RequestType)===((R=$)==null?void 0:R.CHANGE_WITH_UPLOAD)){ne(!0);return}}},[_]);function Pt(R){return R.every(H=>H.info.code&&H.info.desc)}const os=()=>{if(!Pt($t.orgData))h(!0),Q("error"),be("Please choose all mandatory fields"),Me();else{const H={label:"Attachments & Comments",value:"attachments&comments"},x=[{label:"General Information",value:"generalInformation"},...Yt,H];$t.selectedViews=x,j(or($t)),j(Un(1)),U(!0)}};i.useEffect(()=>{rt()},[r==null?void 0:r.RequestType]);const Ms=(R="")=>{var Ge,ue,We,K;const H={materialNo:R??"",top:500,skip:0,salesOrg:((ue=(Ge=it==null?void 0:it.uniqueSalesOrgList)==null?void 0:Ge.map(ze=>ze.code))==null?void 0:ue.join("$^$"))||""},ce=ze=>{(ze==null?void 0:ze.statusCode)===Qe.STATUS_200&&(j(ws({keyName:Ho.RETURN_MAT_NUMBER,data:ze==null?void 0:ze.body})),j(ws({keyName:Ho.PARENT_MAT_NUMBER,data:ze==null?void 0:ze.body})))},x=ze=>{ct(ze)};he(`/${Ce}${(K=(We=gt)==null?void 0:We.DATA)==null?void 0:K.GET_SEARCH_PARAMS_MATERIAL_NO}`,"post",ce,x,H)};i.useEffect(()=>{it!=null&&it.uniqueSalesOrgList&&Ms()},[]);const is=R=>{let H={decisionTableId:null,decisionTableName:si.MDG_MAT_REGION_DIVISION_MAPPING,version:"v1",conditions:[{"MDG_CONDITIONS.MDG_MAT_REGION":R||""}]};ns(H)};return i.useEffect(()=>{r!=null&&r.Region&&(vs(),is(r==null?void 0:r.Region))},[r==null?void 0:r.Region]),i.useEffect(()=>{r!=null&&r.TemplateName&&(((r==null?void 0:r.TemplateName)===Bs.MRP||(r==null?void 0:r.TemplateName)===Bs.WARE_VIEW_2)&&j(qn({keyName:"FieldName",data:void 0})),Bn())},[r==null?void 0:r.TemplateName]),n("div",{children:N(bl,{spacing:2,children:[Object.entries(De).map(([R,H])=>N(xt,{item:!0,md:12,sx:{backgroundColor:"white",maxHeight:"max-content",height:"max-content",borderRadius:"8px",border:"1px solid #E0E0E0",mt:.25,boxShadow:"0px 2px 14px 0px rgba(48, 38, 185, 0.10)",...tr},children:[n(Gt,{sx:{fontSize:"12px",fontWeight:"700",paddingBottom:"10px"},children:Lt(R)}),n(Ve,{children:n(xt,{container:!0,sx:{gap:"1rem 1.5rem"},children:H.filter(ce=>ce.visibility!=="Hidden").sort((ce,x)=>ce.sequenceNo-x.sequenceNo).map(ce=>n(tc,{isHeader:!0,field:ce,dropDownData:et,disabled:ss||(f==null?void 0:f.requestId),requestHeader:!0},ce.id))})}),!ss&&!(f!=null&&f.requestId)&&n(Ve,{sx:{display:"flex",justifyContent:"flex-end",marginTop:"20px"},children:n(je,{variant:"contained",color:"primary",disabled:Ss||!Zt(),onClick:Zs,startIcon:Ss?n(sr,{size:20,color:"inherit"}):null,children:Lt("Save Request Header")})}),n(co,{})]},R)),N(kn,{open:F,onClose:vt,children:[n(Wn,{sx:{backgroundColor:"#EAE9FF"},children:"Select Org Data"}),n(Js,{children:n(xt,{container:!0,columnSpacing:1,children:C.map((R,H)=>N(i.Fragment,{children:[n(xt,{item:!0,md:4,children:N(Gt,{children:[R,n("span",{style:{color:"red"},children:"*"})]})}),n(xt,{item:!0,md:8,children:n(Ol,{options:R==="Distribution Channel"?Ee:et[R]||[],size:"small",getOptionLabel:ce=>`${ce.code} - ${ce.desc}`,renderOption:(ce,x)=>n("li",{...ce,children:n(Gt,{children:`${x.code} - ${x.desc}`})}),onChange:(ce,x)=>Gs(R,x),renderInput:ce=>n(on,{...ce,placeholder:`Select ${R}`})})})]},H))})}),N(zs,{children:[n(je,{onClick:vt,variant:"outlined",children:Lt("Cancel")}),n(je,{variant:"contained",onClick:()=>{os()},children:Lt("Proceed")})]})]}),B&&n(yi,{downloadClicked:_,setDownloadClicked:lt}),n(dc,{onDownloadTypeChange:Et,open:Ot,downloadType:Fe,handleDownloadTypeChange:Tt,onClose:Xs}),n(Hn,{blurLoading:st,loaderMessage:at}),He&&n(yl,{openSnackBar:_e,alertMsg:I,alertType:w,handleSnackBarClose:ls})]})})},Pi=(U,dt,re,_,lt,et="")=>({checkValidation:(F,J,He,h,w)=>{var ae,Ot,Ie,Fe,ot,at,Ne,st,de,Ee,X,B,ne,j,ht;const Q=(ae=U==null?void 0:U[F])==null?void 0:ae.payloadData,_e=(Ot=U==null?void 0:U[F])==null?void 0:Ot.headerData;(Ie=U==null?void 0:U[F])==null||Ie.ManufacturerID;const z=(Fe=U==null?void 0:U.payloadData)==null?void 0:Fe.Region;if(!(_e!=null&&_e.materialNumber))return{missingFields:["Material number"],isValid:!1};if(!(_e!=null&&_e.globalMaterialDescription)||!Q)return{missingFields:["Material Description"],isValid:!1};const C=ur(Q[m.BASIC_DATA]);C.Material=_e==null?void 0:_e.materialNumber,C.MatlDesc=_e==null?void 0:_e.globalMaterialDescription;const Ze=((ot=_e==null?void 0:_e.materialType)==null?void 0:ot.code)??(_e==null?void 0:_e.materialType),I=dt==null?void 0:dt.find(r=>(r==null?void 0:r[z])&&(r==null?void 0:r[z][Ze])),be=I&&I[z]&&((at=I[z][Ze])==null?void 0:at.mandatoryFields),Ae=be==null?void 0:be[m.BASIC_DATA];if((Ae==null?void 0:Ae.length)>0){for(const r of Ae)if(!C[r==null?void 0:r.jsonName])return{missingFields:fr(Ae,C),viewType:m.BASIC_DATA,isValid:!1,plant:[m.BASIC_DATA]}}for(const r of re){const De=gr(J),{displayCombinations:Oe}=(De==null?void 0:De[r])||{};if(Oe&&Oe[0]&&(Oe==null?void 0:Oe.length)>0){const zt=be==null?void 0:be[r];if(zt){let V={};for(const Y of Oe){const ee=(Ne=Q[r])==null?void 0:Ne[Y];if(ee){const f=Sl(zt,ee);((st=Object.keys(f))==null?void 0:st.length)>0&&(V[Y]=Object.keys(f))}else V[Y]=zt.map(f=>f.fieldName);if(((de=Object.keys(V))==null?void 0:de.length)>0)return{missingFields:V,viewType:r,isValid:!1,plant:Object.keys(V)}}}}}if(re.includes(m.SALES)){const r=be==null?void 0:be[m.SALES_GENERAL];let De={};if(r&&Q[m.SALES_GENERAL]){const Oe=Sl(r,(Ee=Q[m.SALES_GENERAL])==null?void 0:Ee[m.SALES_GENERAL]);Object.keys(Oe).length>0&&(De[m.SALES_GENERAL]=Object.keys(Oe))}else r&&(De[m.SALES_GENERAL]=r.map(Oe=>Oe.fieldName));if(((X=Object.keys(De))==null?void 0:X.length)>0)return{missingFields:De,viewType:m.SALES,isValid:!1,plant:[m.SALES_GENERAL]}}if(re.includes(m.PURCHASING)){const r=be==null?void 0:be[m.PURCHASING_GENERAL];let De={};if(r&&Q[m.PURCHASING_GENERAL]){const Oe=Sl(r,(B=Q[m.PURCHASING_GENERAL])==null?void 0:B[m.PURCHASING_GENERAL]);Object.keys(Oe).length>0&&(De[m.PURCHASING_GENERAL]=Object.keys(Oe))}else r&&(De[m.PURCHASING_GENERAL]=r.map(Oe=>Oe.fieldName));if(((ne=Object.keys(De))==null?void 0:ne.length)>0)return{missingFields:De,viewType:m.PURCHASING,isValid:!1,plant:[m.PURCHASING_GENERAL]}}if(re.includes(m.STORAGE)){const r=be==null?void 0:be[m.STORAGE_GENERAL];let De={};if(r&&Q[m.STORAGE_GENERAL]){const Oe=Sl(r,(j=Q[m.STORAGE_GENERAL])==null?void 0:j[m.STORAGE_GENERAL]);Object.keys(Oe).length>0&&(De[m.STORAGE_GENERAL]=Object.keys(Oe))}else r&&(De[m.STORAGE_GENERAL]=r.map(Oe=>Oe.fieldName));if(((ht=Object.keys(De))==null?void 0:ht.length)>0)return{missingFields:De,viewType:m.STORAGE,isValid:!1,plant:[m.STORAGE_GENERAL]}}return{missingFields:null,isValid:!0}}}),Ji=({open:U,onClose:dt,title:re,lengthOfOrgRow:_,selectedMaterialPayload:lt,materialID:et,orgRows:tt})=>{var Q,_e;const[F,J]=i.useState({}),He=bn(),h=()=>{const z=[];return tt&&tt.length>0&&(tt==null||tt.forEach((C,Ze)=>{var I,be,Ae,ae,Ot,Ie,Fe,ot,at,Ne,st,de,Ee,X;if(Ze!==(_==null?void 0:_.copyFor)){const B=(be=(I=C.plant)==null?void 0:I.value)==null?void 0:be.code,ne=((ae=(Ae=C.plant)==null?void 0:Ae.value)==null?void 0:ae.desc)||B,j=(Ot=C.salesOrg)==null?void 0:Ot.code,ht=((Ie=C.salesOrg)==null?void 0:Ie.desc)||j,r=(ot=(Fe=C.dc)==null?void 0:Fe.value)==null?void 0:ot.code,De=((Ne=(at=C.dc)==null?void 0:at.value)==null?void 0:Ne.desc)||r,Oe=(de=(st=C.warehouse)==null?void 0:st.value)==null?void 0:de.code,zt=((X=(Ee=C.warehouse)==null?void 0:Ee.value)==null?void 0:X.desc)||Oe;if(B){let V=`Plant: ${ne||"N/A"}`;j&&(V+=` | SalesOrg: ${ht||"N/A"}`),r&&(V+=` | DC: ${De||"N/A"}`),Oe&&(V+=` | Warehouse: ${zt||"N/A"}`);let Y=B;j&&(Y+=`-${j}`),r&&(Y+=`-${r}`),Oe&&(Y+=`-${Oe}`),z==null||z.push({code:Y,desc:V,index:Ze,plant:B,salesOrg:j,dc:r,warehouse:Oe})}}})),z},w=()=>{var ae,Ot,Ie,Fe,ot,at,Ne,st;if(!F.code)return;const z=tt[_.copyFor],C=(Ot=(ae=z==null?void 0:z.plant)==null?void 0:ae.value)==null?void 0:Ot.code,Ze=(Ie=z==null?void 0:z.salesOrg)==null?void 0:Ie.code,I=(ot=(Fe=z==null?void 0:z.dc)==null?void 0:Fe.value)==null?void 0:ot.code,be=(Ne=(at=z==null?void 0:z.warehouse)==null?void 0:at.value)==null?void 0:Ne.code;if(!C)return;const Ae=JSON.parse(JSON.stringify(lt));(st=Object.keys(Ae))==null||st.forEach(de=>{const Ee=Ae[de];if(!(de===m.BASIC_DATA||de===m.SALES_GENERAL||de===m.PURCHASING_GENERAL||de===m.TAX_DATA)&&typeof Ee=="object"){const X=Object.keys(Ee);if(de===m.WAREHOUSE){const B=X==null?void 0:X.find(j=>j.includes(F.warehouse)),ne=X==null?void 0:X.find(j=>j.includes(be));if(B&&ne&&ne!==B){const j=JSON.parse(JSON.stringify(Ee[B]));delete j.WarehouseId,Ae[de][ne]={...JSON.parse(JSON.stringify(Ae[de][ne]||{})),...j}}}else if(de===m.SALES){const B=`${F.salesOrg}-${F.dc}`,ne=`${Ze}-${I}`,j=X==null?void 0:X.find(r=>r===B),ht=X==null?void 0:X.find(r=>r===ne);if(j&&ht&&ht!==j){const r=JSON.parse(JSON.stringify(Ee[j]));delete r.SalesId,Ae[de][ht]={...JSON.parse(JSON.stringify(Ae[de][ht]||{})),...r}}}else{const B=X==null?void 0:X.find(j=>j.includes(F.plant)),ne=X==null?void 0:X.find(j=>j.includes(C));if(B&&ne&&ne!==B){const j=JSON.parse(JSON.stringify(Ee[B]));j&&(delete j.SalesId,delete j.PlantId,delete j.StorageLocationId,delete j.AccountingId,ne&&(Ae[de][ne]={...JSON.parse(JSON.stringify(Ae[de][ne]||{})),...j}))}}}}),He(hr({materialID:et,data:Ae})),dt()};return N(uo,{isOpen:U,titleIcon:n(tl,{size:"small",sx:{color:(_e=(Q=ke)==null?void 0:Q.primary)==null?void 0:_e.dark,fontSize:"20px"}}),Title:re,handleClose:()=>dt(),children:[N(Js,{sx:{mt:2},children:[n(Gt,{sx:{mb:2},children:ao.COPY_ORG_DATA_VALUES}),n(ft,{options:h(),placeholder:"SELECT SOURCE ORGANIZATION",onChange:z=>J(z),value:F})]}),n(zs,{children:n(je,{variant:"contained",size:"small",onClick:()=>w(),children:"Ok"})})]})},Nc=ni(()=>({customTabs:{"& .MuiTabs-scroller":{overflowX:"auto !important",overflowY:"hidden !important"}}})),mc=U=>{var po,Co,Oo,bo,No,mo,_o,Ro,Io,Mo,xo,Do,Lo,vo,yo;const dt=Nc(),{customError:re}=vl(),_=bn(),lt=Er(),{getDynamicWorkflowDT:et}=wi(),{fetchMaterialFieldConfig:tt,fieldConfigLoading:F}=ho(),{getNextDisplayDataForCreate:J}=Gi(),{fetchValuationClassData:He}=Ui(),h=oe(e=>e.payload.payloadData),w=h==null?void 0:h.RequestType,Q=oe(e=>e.request.salesOrgDTData),_e=oe(e=>e.applicationConfig),z=oe(e=>e.paginationData),C=oe(e=>e.payload),Ze=oe(e=>e.request.requestHeader),I=oe(e=>e.request.materialRows),be=oe(e=>e.payload.payloadData),Ae=oe(e=>{var t;return((t=e.materialDropDownData)==null?void 0:t.dropDown)||{}}),ae=oe(e=>e.tabsData.allTabsData);oe(e=>e.userManagement.userData);let Ot=oe(e=>e.userManagement.roles),Ie=oe(e=>e.userManagement.taskData);const Fe=li(yn.CURRENT_TASK),ot=typeof Fe=="string"?JSON.parse(Fe):Fe,at=oe(e=>e.tabsData.allMaterialFieldConfigDT),Ne=sl(),st=new URLSearchParams(Ne.search),de=st.get("reqBench"),Ee=st.get("RequestId"),[X,B]=i.useState(!1),[ne,j]=i.useState(!1),[ht,r]=i.useState(0),[De,Oe]=i.useState(null),[zt,V]=i.useState(null),Y="Basic Data",[ee,f]=i.useState([Y]),[it,Dt]=i.useState({data:{},isVisible:!1}),[v,Xt]=i.useState(I||[]),ss=oe(e=>e.selectedSections.selectedSections),[Lt,rt]=i.useState(!!(v!=null&&v.length)),[Bn,vs]=i.useState(!1),[ns,ct]=i.useState(!1),[Nn,mn]=i.useState(""),{fetchTabSpecificData:ys}=$i(),[_n,Kt]=i.useState([]),[cn,Yt]=i.useState(0),[q,hs]=i.useState(null),[Ue,Es]=i.useState(!1),[we,ye]=i.useState(!0),[Ut,Ss]=i.useState(v.length+1),[Ts,Zt]=i.useState(0),[Qt,Me]=i.useState(I.length>0),[ls,Xs]=i.useState({}),[Tt,Et]=i.useState({}),[Ks,As]=i.useState(0),[vt,Ys]=i.useState([]),[$t,Gs]=i.useState({}),[Us,Zs]=i.useState([]),[Pt,os]=i.useState(!1),[Ms,is]=i.useState(""),[Le,kt]=i.useState("Basic Data"),[$s,rs]=i.useState(!1);let Wt={id:0,salesOrg:null,dc:{value:null,options:[]},plant:{value:null,options:[]},sloc:{value:null,options:[]},warehouse:{value:null,options:[]},mrpProfile:null};const[le,Je]=i.useState([Wt]),[ve,R]=i.useState(!1),[H,ce]=i.useState(null),[x,Ge]=i.useState("yes"),[ue,We]=i.useState([]),[K,ze]=i.useState(null),ut=(po=C==null?void 0:C[K])==null?void 0:po.headerData,[ps,dn]=i.useState("success"),[wn,an]=i.useState(!1),[Vn,Jt]=i.useState([]),[un,jn]=i.useState(""),[Cs,Rn]=i.useState(""),[ks,fn]=i.useState(""),Os=oe(e=>e.tabsData.matViews),xs=oe(e=>{var t;return(t=e.tabsData)==null?void 0:t.matOdataViews}),{checkValidation:gn}=Pi(C,at,ee),{t:D}=Ll(),{getDtCall:hn,dtData:pe}=Vi(),[En,Qs]=i.useState(null),fe=i.useRef(null),ie=[{region:"US",temp:"MIDDLE EAST HUB"},{region:"US",temp:"SOUTHERN HUB"},{region:"EUR",temp:"NORTH HUB"},{region:"EUR",temp:"CENTRAL HUB"},{region:"EUR",temp:"WEST HUB"}],[Xe,Rt]=i.useState(null),[cs,me]=i.useState(""),[Ke,bs]=i.useState(""),At=i.useRef(le),[Ws,en]=i.useState(!1),qs=(Co=C==null?void 0:C[K])==null?void 0:Co.payloadData,{fetchDataAndDispatch:Sn}=ki(),It=["Sales Org","Plant","Distribution Channel","Storage Location","Warehouse"],[te,yt]=i.useState({}),[nl,ll]=i.useState(!1),[Gl,ol]=i.useState(0),[Ns,Be]=i.useState({"Material No":!1}),{getContryBasedOnPlant:Ul}=qi({doAjax:he,customError:re,fetchDataAndDispatch:Sn,destination_MaterialMgmt:Ce}),[Pn,Nt]=i.useState([]),{filteredButtons:il,showWfLevels:rl}=ac(Ie,_e,rn,Mi),cl=oi(il,[On.HANDLE_SUBMIT_FOR_APPROVAL,On.HANDLE_SAP_SYNDICATION,On.HANDLE_SUBMIT_FOR_REVIEW]),{showSnackbar:mt}=ii(),Bt=40,Tn=18,[$l,In]=i.useState([]);i.useEffect(()=>{var e,t,o,c,d,u,g,T,W,p,L,A,y,k,se,xe,$e;if(Xt(I),Me((I==null?void 0:I.length)>0),(I==null?void 0:I.length)>0&&Ee&&(!K||ks)){ze((e=I==null?void 0:I[0])==null?void 0:e.id),fn((t=I==null?void 0:I[0])==null?void 0:t.materialNumber),sn((c=(o=I==null?void 0:I[0])==null?void 0:o.materialType)==null?void 0:c.code),Zt(0),kt(((g=(u=(d=I==null?void 0:I[0])==null?void 0:d.views)==null?void 0:u.filter(_t=>pn(_t)))==null?void 0:g[0])||m.BASIC_DATA),f((W=(T=I==null?void 0:I[0])==null?void 0:T.views)!=null&&W.length?(p=I==null?void 0:I[0])==null?void 0:p.views:[Y]);const Te=ri(C),nt=ci(Te);let Mt=JSON.parse(JSON.stringify(nt));_(di(Mt)),_(qn({keyName:"selectedMaterialID",data:(L=I==null?void 0:I[0])==null?void 0:L.id})),(k=(y=C==null?void 0:C[(A=I==null?void 0:I[0])==null?void 0:A.id])==null?void 0:y.Tochildrequestheaderdata)!=null&&k.ChildRequestId&&_(qn({keyName:"childRequestId",data:($e=(xe=C==null?void 0:C[(se=I==null?void 0:I[0])==null?void 0:se.id])==null?void 0:xe.Tochildrequestheaderdata)==null?void 0:$e.ChildRequestId}))}},[I]),i.useEffect(()=>{var e,t;(e=I==null?void 0:I[0])!=null&&e.materialType&&(zn({row:I[0]}),Vs(I)&&(ye(!1),rt(!1))),I!=null&&I.length&&As((t=I==null?void 0:I.at(-1))==null?void 0:t.lineNumber),_(Sr({keyName:"VarOrdUn",data:Tr})),!(I!=null&&I.length)&&!Ee&&Jn(),Mn()},[]),i.useEffect(()=>{F?(me(!0),bs(io.LOADING)):(me(!1),bs(""))},[F]),i.useEffect(()=>{var e,t,o,c;if(pe&&((e=pe==null?void 0:pe.customParam)==null?void 0:e.dt)===Al.MDG_ORG_ELEMENT_DEFAULT_VALUE){const d=ai((c=(o=(t=pe==null?void 0:pe.data)==null?void 0:t.result)==null?void 0:o[0])==null?void 0:c.MDG_ORG_ELEMENT_DEFAULT_VALUE_ACTION_TYPE);_(ui({data:d}))}},[pe]);const Jn=()=>{let e={decisionTableId:null,decisionTableName:Al.MDG_ORG_ELEMENT_DEFAULT_VALUE,version:"v1",conditions:[{"MDG_CONDITIONS.MDG_REGION":h==null?void 0:h.Region,"MDG_CONDITIONS.MDG_SCENARIO":h==null?void 0:h.RequestType}]};hn(e,{dt:Al.MDG_ORG_ELEMENT_DEFAULT_VALUE})};i.useEffect(()=>{const e=async()=>{var t,o;try{const c=await et(w,h==null?void 0:h.Region,"",(o=(t=C[K])==null?void 0:t.Tochildrequestheaderdata)==null?void 0:o.MaterialGroupType,Ie==null?void 0:Ie.ATTRIBUTE_3,"v4","MDG_MAT_DYNAMIC_WF_DT",Cn.MAT);Nt(c)}catch(c){re(c)}};w&&(h!=null&&h.Region)&&K&&(Ie!=null&&Ie.ATTRIBUTE_3||ot!=null&&ot.ATTRIBUTE_3)&&e()},[w,h==null?void 0:h.Region,K,Ie==null?void 0:Ie.ATTRIBUTE_3]),i.useEffect(()=>{x==="no"&&(yt({}),ce(null),hs(null))},[x]),i.useEffect(()=>{var e,t,o,c,d,u,g,T,W,p,L,A,y,k,se,xe,$e,Te,nt,Mt;K&&(ae!=null&&ae[m.BASIC_DATA])&&(((e=C[K])!=null&&e.headerData.refMaterialData||C!=null&&C.OrgElementDefaultValues)&&!((c=(o=(t=C[K])==null?void 0:t.payloadData)==null?void 0:o["Basic Data"])!=null&&c.basic)&&!Ee&&dl((u=(d=C[K])==null?void 0:d.headerData)==null?void 0:u.refMaterialData),(p=(W=(T=(g=C[K])==null?void 0:g.payloadData)==null?void 0:T[m.CLASSIFICATION])==null?void 0:W.basic)!=null&&p.Classtype&&fi((k=(y=(A=(L=C[K])==null?void 0:L.payloadData)==null?void 0:A[m.CLASSIFICATION])==null?void 0:y.basic)==null?void 0:k.Classtype,_)),(!Le||(($e=(xe=(se=C[K])==null?void 0:se.headerData.views)==null?void 0:xe.filter(_t=>pn(_t)))==null?void 0:$e[0])!==m.BASIC_DATA)&&kt(((Mt=(nt=(Te=C[K])==null?void 0:Te.headerData.views)==null?void 0:nt.filter(_t=>pn(_t)))==null?void 0:Mt[0])||m.BASIC_DATA)},[K,ae]),i.useEffect(()=>{(v==null?void 0:v.length)===0&&rt(!1)},[v,ve]),i.useEffect(()=>{H!=null&&H.code?kl(H==null?void 0:H.code,"extended"):Et(e=>({...e,"Sales Org":[]})),yt(e=>({...e,[Ds.SALES_ORG]:null}))},[H]),i.useEffect(()=>{var e;(e=te==null?void 0:te["Material Type"])!=null&&e.code&&(Mn(),ce(null),hs(null))},[(Oo=te==null?void 0:te["Material Type"])==null?void 0:Oo.code]),i.useEffect(()=>{["Distribution Channel","Plant"].forEach(t=>{yt(o=>({...o,[t]:""})),Tt[t]&&Et(o=>({...o,[t]:[]}))})},[(bo=te==null?void 0:te["Sales Org"])==null?void 0:bo.code]),i.useEffect(()=>{["Storage Location","Warehouse"].forEach(t=>{yt(o=>({...o,[t]:""})),Tt[t]&&Et(o=>({...o,[t]:[]}))})},[(No=te==null?void 0:te.Plant)==null?void 0:No.code]),i.useEffect(()=>{var e,t,o,c,d,u,g,T,W;if(K&&((t=(e=C[K])==null?void 0:e.headerData)!=null&&t.materialType)){let p=(o=C[K])==null?void 0:o.headerData;if(Os&&Os[(c=p==null?void 0:p.materialType)==null?void 0:c.code]&&((d=p==null?void 0:p.views)==null?void 0:d.length)<2&&xs&&xs[(u=p==null?void 0:p.materialType)==null?void 0:u.code]){let L=(h==null?void 0:h.Region)==="EUR"?((T=Os[(g=p==null?void 0:p.materialType)==null?void 0:g.code])==null?void 0:T.filter(A=>A!==m.WAREHOUSE))||[]:Os[(W=p==null?void 0:p.materialType)==null?void 0:W.code]||[];L=L.filter(A=>{var y,k;return(k=xs[(y=p==null?void 0:p.materialType)==null?void 0:y.code])==null?void 0:k.includes(A)}),Zs(L),f(L),qt({id:K,field:"views",value:L})}}},[Os,xs,K,(_o=(mo=C[K])==null?void 0:mo.headerData)==null?void 0:_o.materialType]),i.useEffect(()=>{te[Ds.SALES_ORG]&&(ul(),yt(e=>({...e,[Ds.DIST_CHNL]:null,[Ds.PLANT]:null})))},[te[Ds.SALES_ORG]]);const dl=e=>{var u,g,T,W,p,L;const t=((T=(g=(u=e==null?void 0:e.copyPayload)==null?void 0:u.payloadData)==null?void 0:g["Basic Data"])==null?void 0:T.basic)||{},o=((p=(W=C==null?void 0:C.OrgElementDefaultValues)==null?void 0:W[m.BASIC_DATA])==null?void 0:p[m.BASIC_DATA])||{};new Set([...Object.keys(o),...Object.keys(t)]).forEach(A=>{const y=(t==null?void 0:t[A])||"",k=(o==null?void 0:o[A])||"",se=A==="Division"?h==null?void 0:h.Division:Ar(A,y,ae["Basic Data"],k);_(Fo({materialID:K,viewID:"Basic Data",itemID:"basic",keyName:A,data:se}))});let d=(L=e==null?void 0:e.copyPayload)==null?void 0:L.unitsOfMeasureData;if(d!=null&&d.length){let A=[];d==null||d.forEach(y=>{A.push({...y,id:(y==null?void 0:y.id)||A.length+1})}),_(Bo({materialID:K,data:A}))}},kl=(e,t)=>{const o=d=>{Be(u=>({...u,"Sales Org":!1})),(d==null?void 0:d.statusCode)===Qe.STATUS_200&&Et(t==="notExtended"?u=>({...u,"Sales Org":d.body}):u=>({...u,"Sales Org":(d==null?void 0:d.body.length)>0?d.body:[]}))},c=()=>{Be(d=>({...d,"Sales Org":!1}))};Be(d=>({...d,"Sales Org":!0})),he(`/${Ce}/data/${t==="notExtended"?"getSalesOrgNotExtended":"getSalesOrgExtended"}?materialNo=${e}&region=${h==null?void 0:h.Region}`,"get",o,c)},Wl=(e,t,o)=>{Be(g=>({...g,Plant:!0}));const c=g=>{Be(T=>({...T,Plant:!1})),(g==null?void 0:g.statusCode)===Qe.STATUS_200&&Et(t==="notExtended"?T=>({...T,Plant:g.body}):T=>({...T,Plant:(g==null?void 0:g.body.length)>0?g.body:[]}))},d=()=>{Be(g=>({...g,Plant:!1}))},u=o?`&salesOrg=${o.code}`:"";he(`/${Ce}/data/${t==="notExtended"?"getPlantNotExtended":"getPlantExtended"}?materialNo=${e}&region=${h==null?void 0:h.Region}${u}`,"get",c,d)},al=(e,t,o)=>{Be(g=>({...g,Warehouse:!0}));const c=g=>{Be(T=>({...T,Warehouse:!1})),(g==null?void 0:g.statusCode)===Qe.STATUS_200&&Et(t==="notExtended"?T=>({...T,Warehouse:g.body}):T=>({...T,Warehouse:(g==null?void 0:g.body.length)>0?g.body:[]}))},d=()=>{Be(g=>({...g,Warehouse:!1}))},u=o?`&plant=${o.code}`:"";he(`/${Ce}/data/${t==="notExtended"?"getWarehouseNotExtended":"getWarehouseExtended"}?materialNo=${e}&region=${h==null?void 0:h.Region}${u}`,"get",c,d)},ql=(e,t,o)=>{Be(u=>({...u,"Storage Location":!0}));const c=u=>{Be(g=>({...g,"Storage Location":!1})),(u==null?void 0:u.statusCode)===Qe.STATUS_200&&Et(g=>{var T;return{...g,[(T=Ds)==null?void 0:T.STORAGE_LOC]:u.body||[]}})},d=u=>{re(u),Be(g=>({...g,"Storage Location":!1}))};he(`/${Ce}/data/getStorageLocationExtended?plant=${t==null?void 0:t.code}&materialNo=${e}&region=${h==null?void 0:h.Region}&salesOrg=${o==null?void 0:o.code}`,"get",c,d)},ul=()=>{var o;Be(c=>({...c,"Distribution Channel":!0}));const e=c=>{Be(d=>({...d,"Distribution Channel":!1})),(c==null?void 0:c.statusCode)===Qe.STATUS_200&&Et(d=>{var u;return{...d,[(u=Ds)==null?void 0:u.DIST_CHNL]:c.body&&(c==null?void 0:c.body)}})},t=c=>{re(c),Be(d=>({...d,"Distribution Channel":!1}))};he(`/${Ce}/data/getDistributionChannelExtended?materialNo=${H==null?void 0:H.code}&salesOrg=${(o=te[Ds.SALES_ORG])==null?void 0:o.code}`,"get",e,t)};i.useEffect(()=>{["Mrp Profile"].forEach(O),(I==null?void 0:I.length)===0&&(w===$.CREATE||w===$.CREATE_WITH_UPLOAD)&&R(!0),Hl(),Fl()},[]),i.useEffect(()=>{var t,o,c,d,u,g,T,W,p,L,A,y,k,se,xe,$e,Te,nt,Mt,_t,ms,wt,_s,Pe,Ht,Rs;At.current=le,le.some(Hs=>{var Kn,Yn,Zn;return((Kn=Hs==null?void 0:Hs.salesOrg)==null?void 0:Kn.code)&&!((Zn=(Yn=Hs==null?void 0:Hs.dc)==null?void 0:Yn.value)!=null&&Zn.code)})?en(!1):((o=(t=le[0])==null?void 0:t.salesOrg)!=null&&o.code&&((u=(d=(c=le[0])==null?void 0:c.dc)==null?void 0:d.value)!=null&&u.code)||!((p=(T=(g=C[K])==null?void 0:g.headerData)==null?void 0:T.views)!=null&&p.includes((W=m)==null?void 0:W.SALES)))&&((y=(A=(L=le[0])==null?void 0:L.plant)==null?void 0:A.value)!=null&&y.code)&&((xe=(se=(k=le[0])==null?void 0:k.sloc)==null?void 0:se.value)!=null&&xe.code||!((Mt=(Te=($e=C[K])==null?void 0:$e.headerData)==null?void 0:Te.views)!=null&&Mt.includes((nt=m)==null?void 0:nt.STORAGE)))&&((wt=(ms=(_t=le[0])==null?void 0:_t.warehouse)==null?void 0:ms.value)!=null&&wt.code||(h==null?void 0:h.Region)==="EUR"||!((Rs=(Pe=(_s=C[K])==null?void 0:_s.headerData)==null?void 0:Pe.views)!=null&&Rs.includes((Ht=m)==null?void 0:Ht.WAREHOUSE)))?en(!0):en(!1)},[le]),i.useEffect(()=>{ye(!0)},[(Ro=C[K])==null?void 0:Ro.headerData,(Io=C[K])==null?void 0:Io.payloadData]);const Hl=()=>{if(h!=null&&h.Region){const e=o=>{(o==null?void 0:o.statusCode)===Qe.STATUS_200&&Et(c=>({...c,"Sales Organization":o.body?o==null?void 0:o.body:[]}))},t=o=>{re(o)};he(`/${Ce}${gt.DATA.GET_SALES_ORG}?region=${h==null?void 0:h.Region}`,"get",e,t)}},Fl=()=>{if(h!=null&&h.Region){const e=o=>{if((o==null?void 0:o.statusCode)===Qe.STATUS_200){let c=At.current?JSON.parse(JSON.stringify(At.current)):JSON.parse(JSON.stringify(le));Et(d=>({...d,PlantNotExtended:o.body?o==null?void 0:o.body:[]})),Je(c),At.current=c}},t=o=>{re(o)};he(`/${Ce}${gt.DATA.GET_PLANT}?region=${h==null?void 0:h.Region}`,"get",e,t)}},Bl=(e,t)=>{if(e){Be(d=>({...d,[us.STORAGE_LOCATION]:{...d[us.STORAGE_LOCATION],[t]:!0}}));const o=d=>{if(Be(u=>({...u,[us.STORAGE_LOCATION]:{...u[us.STORAGE_LOCATION],[t]:!1}})),(d==null?void 0:d.statusCode)===Qe.STATUS_200){let u=At.current?JSON.parse(JSON.stringify(At.current)):JSON.parse(JSON.stringify(le));t!==-1&&(u[t].sloc.options=ts(d.body)),Je(u),At.current=u}},c=d=>{re(d),Be(u=>({...u,[us.STORAGE_LOCATION]:{...u[us.STORAGE_LOCATION],[t]:!1}}))};he(`/${Ce}${gt.DATA.GET_STORAGE_LOCATION}?region=${h==null?void 0:h.Region}&plant=${e==null?void 0:e.code}`,"get",o,c)}},wl=(e,t,o)=>{if(e){Be(u=>({...u,[us.WAREHOUSE]:{...u[us.WAREHOUSE],[o]:!0}}));const c=u=>{if(Be(g=>({...g,[us.WAREHOUSE]:{...g[us.WAREHOUSE],[o]:!1}})),(u==null?void 0:u.statusCode)===Qe.STATUS_200){let g=At.current?JSON.parse(JSON.stringify(At.current)):JSON.parse(JSON.stringify(le));o!==-1&&(g[o].warehouse.options=ts(u.body)),Je(g),At.current=g}},d=u=>{re(u),Be(g=>({...g,[us.WAREHOUSE]:{...g[us.WAREHOUSE],[o]:!1}}))};he(`/${Ce}${gt.DATA.GET_WAREHOUSE_NO}?region=${h==null?void 0:h.Region}&plant=${e==null?void 0:e.code}&storageLocation=${t==null?void 0:t.code}`,"get",c,d)}},fl=(e,t="",o)=>new Promise((c,d)=>{var L;const u=[{materialNo:e,requestNo:t||(Ze==null?void 0:Ze.requestId),materialDesc:o||""}],g=A=>{var y;((y=A==null?void 0:A.body)==null?void 0:y.totalDuplicatesFound)>0?(mt(A==null?void 0:A.message,"error"),c(!0)):c(!1)},T=A=>{re(A),c(!1)};let W=0;Object.keys(C).forEach((A,y)=>{var k,se;(A.includes("-")||/\d/.test(A))&&((se=(k=C[A])==null?void 0:k.headerData)==null?void 0:se.materialNumber)===e&&W++});let p=0;Object.keys(C).forEach(A=>{var y,k;(A.includes("-")||/\d/.test(A))&&((k=(y=C[A])==null?void 0:y.headerData)==null?void 0:k.globalMaterialDescription)===o&&p++}),W>1?(mt(`${Tl.DUPLICATE_MATERIAL}${e}`,"error"),c(!0)):p>1?(mt(`${Tl.DUPLICATE_MATERIAL_DESCRIPTION}${o}`,"error"),c(!0)):he(`/${Ce}${(L=gt.MASS_ACTION)==null?void 0:L.MAT_NO_DUPLICATE_CHECK}`,"post",g,T,u)}),Vl=async()=>{let e=[...v],t=!0;return me(!0),bs(io.VALIDATING_MATS),new Promise(async(o,c)=>{for(let u=0;u<(v==null?void 0:v.length);u++){const g=v[u],{missingFields:T,viewType:W,isValid:p,plant:L=[]}=gn(g.id,(g==null?void 0:g.orgData)||[],!1,!1,!1);if(Jt(L),Qs(W),Zt(W?ee==null?void 0:ee.indexOf(W):0),kt(W||m.BASIC_DATA),In(T),p){let A=!1;p&&(!Ee||g!=null&&g.isMatNoChanged||g!=null&&g.isMatDescChanged)&&(A=await fl(g.materialNumber,Ee,g==null?void 0:g.globalMaterialDescription)),A&&(t=!1),e=e==null?void 0:e.map(y=>y.id===g.id?{...y,validated:!A}:y),_(bt(e))}else{if(t=!1,e=e.map(A=>A.id===g.id?{...A,validated:!1}:A),_(bt(e)),T)if(ze(g.id),fn(g.materialNumber),typeof T=="object"&&!Array.isArray(T)){const A=Object.entries(T).map(([y,k])=>`Combination ${y}: ${k.join(", ")}`);mt(`Line No ${g.lineNumber} : Please fill all the Mandatory fields in ${W||""}: ${A.join(" | ")}`,"error",1e4)}else mt(`Line No ${g.lineNumber} : Please fill all the Mandatory fields in ${W||""}: ${T.join(", ")}`,"error",1e4);break}}t?o(!0):c(),me(!1);const d=Vs(e);rt(!d),ye(!d),t&&mt("Validation successful for all materials.","success")})},jl=e=>{var t,o;if(e){let c=JSON.parse(JSON.stringify(((o=(t=C==null?void 0:C[K])==null?void 0:t.headerData)==null?void 0:o.calledMrpCodes)||[]))||[];e.forEach((u,g)=>{var T,W,p,L,A,y,k,se,xe;(T=u==null?void 0:u.mrpProfile)!=null&&T.code&&!((A=(p=(W=C==null?void 0:C[K])==null?void 0:W.headerData)==null?void 0:p.calledMrpCodes)!=null&&A.includes((L=u==null?void 0:u.mrpProfile)==null?void 0:L.code))&&(Pl((k=(y=u==null?void 0:u.plant)==null?void 0:y.value)==null?void 0:k.code,(se=u==null?void 0:u.mrpProfile)==null?void 0:se.code),c.push((xe=u==null?void 0:u.mrpProfile)==null?void 0:xe.code))}),_(js({materialID:K,keyName:"calledMrpCodes",data:c}));const d=v==null?void 0:v.map(u=>u.id===K?{...u,calledMrpCodes:c}:u);_(bt(d))}},Pl=(e,t,o)=>{var g;const c={mrpProfile:t},d=T=>{T.body[0]&&Object.keys(T==null?void 0:T.body[0]).filter(p=>T==null?void 0:T.body[0][p]).forEach(p=>{Jl(e,p,T==null?void 0:T.body[0][p],m.MRP)})},u=T=>{re(T)};he(`/${Ce}${(g=gt.MASS_ACTION)==null?void 0:g.MRP_DEFAULT_VALUES}`,"post",d,u,c)},Jl=(e,t,o,c)=>{_(Fo({materialID:K||"",keyName:t||"",data:o??null,viewID:c,itemID:e}))};i.useEffect(()=>{Vs(I)&&(I!=null&&I.length)||de||Ee?(U.setCompleted([!0,!0]),U==null||U.setIsAttachmentTabEnabled(!0)):(U.setCompleted([!0,!1]),U==null||U.setIsAttachmentTabEnabled(!1))},[I]);const tn=Ks+10,zl=()=>{var o,c;const e=no(),t={id:e,included:!0,lineNumber:tn,industrySector:(o=wo)==null?void 0:o.DEFAULT_IND_SECTOR,materialType:((c=be==null?void 0:be.MatlType)==null?void 0:c.code)??"",materialNumber:(H==null?void 0:H.code)||"",globalMaterialDescription:"",views:[],orgData:[],validated:pl.default,withReference:x};_(Cl({materialID:e,data:t})),_(bt([...v,t])),Ss(Ut+1),As(tn),Me(!0),rt(!0),ye(!0),f([Y]),ze(e),sn("")},Xl=()=>{R(!1),(h==null?void 0:h.RequestType)==="Create"?zl():(h==null?void 0:h.RequestType)==="Change"&&vs(!0)},Kl=()=>{An()},Mn=(e="",t=!0)=>{var u,g;const o={materialNo:e??"",salesOrg:((u=Q==null?void 0:Q.uniqueSalesOrgList)==null?void 0:u.map(T=>T.code).join("$^$"))||"",top:500,skip:t?0:cn,matlType:((g=te==null?void 0:te["Material Type"])==null?void 0:g.code)??""};Be(T=>({...T,"Material No":!0}));const c=T=>{(T==null?void 0:T.statusCode)===Qe.STATUS_200&&(T!=null&&T.body)&&Kt(t?T==null?void 0:T.body:W=>[...W,...T==null?void 0:T.body]),Es(!1),Be(W=>({...W,"Material No":!1}))},d=()=>{Es(!1),Be(T=>({...T,"Material No":!1}))};Es(!0),he(`/${Ce}/data/getSearchParamsMaterialNo`,"post",c,d,o)},Yl=((Mo=vt==null?void 0:vt[0])==null?void 0:Mo.External)==="X",Zl=((xo=vt==null?void 0:vt[1])==null?void 0:xo.External)==="X",Ql=vt==null?void 0:vt.some(e=>e.ExtNAwock==="X");(e=>{const t=new Set;let o=null;e==null||e.forEach(d=>{d.External==="X"&&d.ExtNAwock==="X"?(t.add(`External Number Range: Allowed (${d.FromNumber}-${d.ToNumber})`),t.add("Ext W/O Check: Allowed")):d.External!=="X"&&d.ExtNAwock==="X"?(t.add("Internal Number Range: Allowed"),t.add("Ext W/O Check: Allowed")):d.External==="X"&&d.ExtNAwock!=="X"?(t.add(`External Number Range: Allowed (${d.FromNumber}-${d.ToNumber})`),o="Ext W/O Check: Not Allowed"):d.External!=="X"&&d.ExtNAwock!=="X"&&(t.add("Internal Number Range: Allowed"),o="Ext W/O Check: Not Allowed")});const c=Array.from(t);return o&&c.push(o),c.map((d,u)=>n("div",{children:n(Gt,{children:d})},u))})(vt);function sn(e){var d;const t=(h==null?void 0:h.Region)||jt.US,o=at.some(u=>u[t]&&u[t][e]),c=e!==fe.current;if(!o&&e)tt(e,t),(Ee&&de||!Ee&&!de)&&eo(e);else if(!e)_(Fn({}));else{const u=at==null?void 0:at.find(g=>(g==null?void 0:g[t])&&(g==null?void 0:g[t][e]));u&&_(Fn((d=u[t][e])==null?void 0:d.allfields))}e&&c&&He(e),fe.current=e}const eo=e=>{const t=c=>{var d;(c==null?void 0:c.statusCode)===((d=Qe)==null?void 0:d.STATUS_200)?_(Ml({matType:e,views:c==null?void 0:c.body})):_(Ml({matType:e,views:[]}))},o=c=>{re(c)};he(`/${Ce}${gt.DATA.GET_VIEWS_FOR_MAT}=${e}`,"get",t,o)},qt=e=>{const{id:t,field:o,value:c}=e;let d=v.map(u=>u.id===t?{...u,[o]:c}:u);Gs({...$t,[o]:c}),o===Gn.MATERIALTYPE&&(f([Y]),gi([Wt]),_(js({materialID:t,keyName:"views",data:[Y]})),_(js({materialID:t,keyName:"orgData",data:[]})),d=d.map(u=>u.id===t?{...u,orgData:[]}:u),sn(c==null?void 0:c.code)),o===Gn.INCLUDED&&(Vs(d)?(rt(!1),ye(!1)):(rt(!0),ye(!0))),o===Gn.VIEWS&&(rt(!0),ye(!0)),Xt(d),_(js({materialID:t,keyName:o,data:c})),_(bt(d))},zn=e=>{var t,o,c,d,u,g,T,W,p,L,A,y,k,se;ze(e.row.id),fn(e.row.materialNumber),sn((o=(t=e==null?void 0:e.row)==null?void 0:t.materialType)==null?void 0:o.code),Zs((u=Os[(d=(c=e==null?void 0:e.row)==null?void 0:c.materialType)==null?void 0:d.code])==null?void 0:u.filter(xe=>{var $e,Te,nt;return(nt=xs[(Te=($e=e==null?void 0:e.row)==null?void 0:$e.materialType)==null?void 0:Te.code])==null?void 0:nt.includes(xe)})),f((T=(g=e==null?void 0:e.row)==null?void 0:g.views)!=null&&T.length?(W=e.row)==null?void 0:W.views:[Y]),Je((L=(p=e==null?void 0:e.row)==null?void 0:p.orgData)!=null&&L.length?(A=e.row)==null?void 0:A.orgData:[Wt]),Zt(0),kt(((se=(k=(y=e.row)==null?void 0:y.views)==null?void 0:k.filter(xe=>pn(xe)))==null?void 0:se[0])||"Basic Data")},to=()=>{ct(!0)},An=()=>{ct(!1)},xn=(e,t)=>{t==="backdropClick"||t==="escapeKeyDown"||rs(!1)},gl=()=>f(Us||[Y]),hl=()=>{if(R(!1),x==="yes")if(ue!=null&&ue.length){let e=[...v];ue==null||ue.forEach(t=>{var g,T;const o=no();let c=JSON.parse(JSON.stringify(t));c!=null&&c.refMaterialData&&delete c.refMaterialData;let d=JSON.parse(JSON.stringify((g=C==null?void 0:C[t.id])==null?void 0:g.payloadData));c.id=o,c.lineNumber=tn,c.globalMaterialDescription="",c.materialNumber="",c.validated=pl.default,_(Cl({materialID:o,data:c,payloadData:d})),e.push(c),Xt(e),_(bt(e)),Ss(Ut+1),As(tn),Me(!0),rt(!0),ye(!0);let u=(T=C==null?void 0:C[t.id])==null?void 0:T.unitsOfMeasureData;if(u!=null&&u.length){let W=[];u==null||u.forEach(p=>{var L,A,y;W.push({...p,eanUpc:"",eanCategory:"",length:"",width:"",height:"",volume:"",grossWeight:"",netWeight:"",eanCategory:(h==null?void 0:h.Region)===((L=jt)==null?void 0:L.US)?p==null?void 0:p.EanCat:"",eanUpc:(p==null?void 0:p.EanCat)==="MB"&&(h==null?void 0:h.Region)===((A=jt)==null?void 0:A.US)||(h==null?void 0:h.Region)===((y=jt)==null?void 0:y.EUR)?"":p==null?void 0:p.EanUpc,id:(p==null?void 0:p.id)||W.length+1})}),_(Bo({materialID:o,data:W}))}}),We([])}else H&&El();else Xl()},El=()=>{var c,d,u,g,T,W,p;me(!0);let e={material:H==null?void 0:H.code,wareHouseNumber:(c=te==null?void 0:te.Warehouse)==null?void 0:c.code,storageLocation:(d=te==null?void 0:te["Storage Location"])==null?void 0:d.code,salesOrg:(u=te==null?void 0:te["Sales Org"])==null?void 0:u.code,distributionChannel:(g=te==null?void 0:te["Distribution Channel"])==null?void 0:g.code,valArea:(T=te==null?void 0:te.Plant)==null?void 0:T.code,plant:(W=te==null?void 0:te.Plant)==null?void 0:W.code};const t=L=>{var A,y,k,se,xe,$e,Te,nt,Mt,_t,ms;if(me(!1),yt({}),L!=null&&L.body[0]){Jo(L==null?void 0:L.body,h);let wt=[...v];const _s=no();let Pe={};Pe.id=_s,Pe.included=!0,Pe.lineNumber=tn,Pe.globalMaterialDescription="",Pe.materialType={code:((A=L.body[0])==null?void 0:A.MatlType)||"",desc:((k=(y=Ae==null?void 0:Ae.MatlType)==null?void 0:y.find(Ht=>{var Rs;return Ht.code===((Rs=L.body[0])==null?void 0:Rs.MatlType)}))==null?void 0:k.desc)||""},Pe.industrySector={code:((se=L.body[0])==null?void 0:se.IndSector)||"",desc:(($e=(xe=Ae==null?void 0:Ae.IndSector)==null?void 0:xe.find(Ht=>{var Rs;return Ht.code===((Rs=L.body[0])==null?void 0:Rs.IndSector)}))==null?void 0:$e.desc)||""},Pe.materialNumber="",Pe.views=(nt=(((Te=L.body[0])==null?void 0:Te.Views)||"").split(",").map(Ht=>Ht.trim()==="Storage"?m.STORAGE:Ht.trim()))==null?void 0:nt.filter(Ht=>!go.includes(Ht)),(h==null?void 0:h.Region)===((Mt=jt)==null?void 0:Mt.EUR)&&(Pe.views=((_t=Pe==null?void 0:Pe.views)==null?void 0:_t.filter(Ht=>Ht!==m.WAREHOUSE))||[]),Pe.validated=pl.default,Pe.withReference=x,Pe.refMaterialData=Jo(L.body,h),_(Cl({materialID:_s,data:Pe,payloadData:{}})),wt.push(Pe),Xt(wt),_(bt(wt)),f(Pe==null?void 0:Pe.views),Ss(Ut+1),As(tn),Me(!0),rt(!0),ye(!0),sn((ms=L.body[0])==null?void 0:ms.MatlType),ze(_s)}else me(!1),mt(Tl.NO_MATERIAL_FOUND,"warning"),R(!0)},o=L=>{re(L),me(!1),R(!0)};yt({}),ce(null),hs(null),he(`/${Ce}${(p=gt.DATA)==null?void 0:p.GET_COPY_MATERIAL}`,"post",t,o,e)},pt=!$n.includes(U==null?void 0:U.requestStatus)||Ee&&!de,s=e=>({hasFertRole:e.includes("CA-MDG-MRKTNG-FERT-EUR"),hasSalesRole:e.includes("CA-MDG-MRKTNG-SALES-EUR")}),l=(e,t,o)=>{var u;const{hasFertRole:c,hasSalesRole:d}=s(Ot);if(c&&!d&&(h==null?void 0:h.Region)===jt.EUR)return(e==null?void 0:e.code)!=="FERT";if(!c&&d&&(h==null?void 0:h.Region)===jt.EUR)return(e==null?void 0:e.code)==="FERT";if(c&&d&&(h==null?void 0:h.Region)===jt.EUR){const g=t[0];if(o===(g==null?void 0:g.id))return!1;const T=(u=g==null?void 0:g.materialType)==null?void 0:u.code;if(T==="FERT")return(e==null?void 0:e.code)!=="FERT";if(T)return(e==null?void 0:e.code)==="FERT"}return!1},a=(e,t)=>{var o;lc.fire({title:D("Are you sure?"),text:D("Changing the material type will reset all the field values entered!"),icon:"warning",showCancelButton:!0,confirmButtonColor:(o=ke.primary)==null?void 0:o.main,cancelButtonColor:ke.error.red,confirmButtonText:D("Yes, do it!"),cancelButtonText:D("Cancel"),reverseButtons:!0}).then(c=>{c.isConfirmed&&(f([]),qt({id:Ms,field:Gn.MATERIALTYPE,value:e}),_(Or({materialId:t})),qt({id:t,field:"materialType",value:e}))})},E=[{field:"included",headerName:D("Included"),flex:.5,align:"center",headerAlign:"center",renderCell:e=>n(Nl,{checked:e.row.included,disabled:pt,onChange:t=>qt({id:e.row.id,field:"included",value:t.target.checked})})},{field:"lineNumber",headerName:D("Line Number"),flex:.5,editable:!0,align:"center",headerAlign:"center"},{field:"industrySector",headerName:D("Industry Sector"),flex:.7,align:"center",headerAlign:"center",...w===$.CREATE||w===$.CREATE_WITH_UPLOAD?{renderCell:e=>{var t;return n(ft,{options:(Ae==null?void 0:Ae.IndSector)||[],value:e.row.industrySector||((t=wo)==null?void 0:t.DEFAULT_IND_SECTOR),onChange:o=>qt({id:e.row.id,field:"industrySector",value:o}),placeholder:D("Select Industry Sector"),disabled:pt,minWidth:"90%",listWidth:235})}}:{editable:!1,renderCell:e=>{var t,o;return((o=(t=C==null?void 0:C[e.row.id])==null?void 0:t.headerData)==null?void 0:o.industrySector)||""}}},{field:"materialType",headerName:D("Material Type"),flex:.7,align:"center",headerAlign:"center",renderHeader:()=>N("span",{children:[D("Material Type"),n("span",{style:{color:"red"},children:"*"})]}),...w===$.CREATE||w===$.CREATE_WITH_UPLOAD?{renderCell:e=>n(ft,{options:ml||[],value:e.row.materialType,onChange:t=>{e.row.materialType?a(t,e.row.id):qt({id:e.row.id,field:"materialType",value:t})},placeholder:D("Select Material Type"),disabled:pt,minWidth:"90%",listWidth:235,isOptionDisabled:t=>l(t,v,e.row.id)})}:{editable:!1,renderCell:e=>{var t,o;return((o=(t=C==null?void 0:C[e.row.id])==null?void 0:t.headerData)==null?void 0:o.materialType)||""}}},{field:"materialNumber",headerName:D("Material Number"),flex:.7,editable:!1,align:"center",headerAlign:"center",renderHeader:()=>N("span",{children:[D("Material Number"),n("span",{style:{color:"red"},children:"*"})]}),renderCell:e=>{var T,W;const[t,o]=i.useState({[(T=e==null?void 0:e.row)==null?void 0:T.id]:e.row.materialNumber}),c=e.row.id,d=i.useRef(null),u=p=>{const L=p.target.value.toUpperCase();(L==null?void 0:L.length)>=Tn&&(d.current||(d.current=setTimeout(()=>{mt(`Material Number cannot exceed ${Tn} characters`,"error"),d.current=null},1e3)));const A=L.replace(/[^A-Z0-9-]/g,"").slice(0,Tn);o(k=>({...k,[c]:A})),qt({id:e.row.id,field:"materialNumber",value:A});const y=v.map(k=>k.id===e.row.id?{...k,isMatNoChanged:!0,materialNumber:A}:k);_(bt(y))},g=(((W=t[c])==null?void 0:W.length)||0)===Tn;return n(gs,{title:t[c]||"",arrow:!0,children:(h==null?void 0:h.RequestType)===$.CREATE||(h==null?void 0:h.RequestType)===$.CREATE_WITH_UPLOAD?n(on,{fullWidth:!0,placeholder:D("ENTER MATERIAL NUMBER"),variant:"outlined",size:"small",name:"material number",value:t[c]||"",onChange:p=>{u(p)},error:g,sx:{"& .MuiInputBase-root.Mui-disabled":{"& > input":{WebkitTextFillColor:ke.black.dark,color:ke.black.dark}}},disabled:pt}):e.row.materialNumber})}},{field:"globalMaterialDescription",flex:.7,headerName:D("Material Description"),renderHeader:()=>N("span",{children:[D("Material Description"),n("span",{style:{color:"red"},children:"*"})]}),renderCell:e=>{var T,W;const[t,o]=i.useState({[(T=e==null?void 0:e.row)==null?void 0:T.id]:e.row.globalMaterialDescription}),c=e.row.id,d=i.useRef(null),u=p=>{const L=p.target.value.toUpperCase();(L==null?void 0:L.length)>Bt&&(d.current||(d.current=setTimeout(()=>{mt(`Material Description cannot exceed ${Bt} characters`,"error"),d.current=null},1e3)));const A=L.replace(/[^A-Z0-9\s-]/g,"").slice(0,Bt);o(k=>({...k,[c]:A})),qt({id:e.row.id,field:"globalMaterialDescription",value:A});const y=v.map(k=>k.id===e.row.id?{...k,isMatDescChanged:!0,globalMaterialDescription:A}:k);_(bt(y))},g=(((W=t[c])==null?void 0:W.length)||0)===Bt;return n(Ve,{sx:{display:"flex",alignItems:"center",width:"100%"},children:n(gs,{title:t[c]||"",arrow:!0,placement:"top",children:n(on,{fullWidth:!0,variant:"outlined",size:"small",placeholder:D("ENTER MATERIAL DESCRIPTION"),value:t[c]||"",onChange:u,onKeyDown:p=>{p.key===" "&&p.stopPropagation()},error:g,sx:{flexGrow:1,"& .MuiOutlinedInput-root":{"& fieldset":{borderColor:g?ke.error.dark:void 0},"&:hover fieldset":{borderColor:g?ke.error.dark:void 0},"&.Mui-focused fieldset":{borderColor:g?ke.error.dark:void 0}},"& .MuiInputBase-root.Mui-disabled":{"& > input":{WebkitTextFillColor:ke.black.dark,color:ke.black.dark}}},disabled:pt})})})},align:"center",headerAlign:"center",editable:!1},{...w===$.CREATE||w===$.CREATE_WITH_UPLOAD?{field:"views",headerName:"",flex:.8,align:"center",headerAlign:"center",renderCell:e=>{var t,o,c;return N(Is,{children:[n(je,{variant:"contained",size:"small",disabled:!((t=e==null?void 0:e.row)!=null&&t.materialType),onClick:()=>{var d,u,g;os(!0),is(e.row.id),f((u=(d=e==null?void 0:e.row)==null?void 0:d.views)!=null&&u.length?(g=e.row)==null?void 0:g.views:[Y])},children:D("Views")}),n(je,{variant:"contained",disabled:!(((c=(o=e==null?void 0:e.row)==null?void 0:o.views)==null?void 0:c.length)>1),size:"small",sx:{marginLeft:"4px"},onClick:()=>{var d,u,g;rs(!0),is(e.row.id),Je((u=(d=e==null?void 0:e.row)==null?void 0:d.orgData)!=null&&u.length?(g=e.row)==null?void 0:g.orgData:[Wt])},children:D("ORG Data")})]})}}:{}},{field:"action",headerName:D("Action"),flex:.5,align:"center",headerAlign:"center",renderCell:e=>{let t=hi(e==null?void 0:e.row);const o=async c=>{var A,y,k,se,xe,$e,Te,nt,Mt,_t,ms;if(c.stopPropagation(),((y=(A=e==null?void 0:e.row)==null?void 0:A.views)==null?void 0:y.length)>1&&(!((k=e==null?void 0:e.row)!=null&&k.orgData)||((xe=(se=e==null?void 0:e.row)==null?void 0:se.orgData)==null?void 0:xe.length)===0)){mt(D((Te=($e=xi)==null?void 0:$e.FILES)==null?void 0:Te.MISSING_ORG_DATA),"error",1e4);return}const{missingFields:d,viewType:u,isValid:g,plant:T=[]}=gn(e.row.id,((nt=e==null?void 0:e.row)==null?void 0:nt.orgData)||[],Yl,Zl,Ql);if(Jt(T),Qs(u),In(d),Zt(u?ee==null?void 0:ee.indexOf(u):0),kt(u||m.BASIC_DATA),d)if(typeof d=="object"&&!Array.isArray(d)){const wt=Object.entries(d).map(([_s,Pe])=>`Combination ${_s}: ${Pe.join(", ")}`);mt(`${D("Line No")} ${e.row.lineNumber} : ${D("Please fill all the Mandatory fields in")} ${u||""}: ${wt.join(" | ")}`,"error",1e4)}else mt(`${D("Line No")} ${e.row.lineNumber} : ${D("Please fill all the Mandatory fields in")} ${u||""}: ${d.join(", ")}`,"error",1e4);let W=!1;g&&(!Ee||(Mt=e.row)!=null&&Mt.isMatNoChanged||(_t=e.row)!=null&&_t.isMatDescChanged)&&(W=await fl(e.row.materialNumber,Ee,(ms=e==null?void 0:e.row)==null?void 0:ms.globalMaterialDescription)),t=g&&!W?"success":"error",t==="success"&&mt("Validation successful","success");const p=v.map(wt=>wt.id===e.row.id?{...wt,validated:g&&!W}:wt);_(bt(p));const L=Vs(p);rt(!L),ye(!L)};return N(bl,{direction:"row",alignItems:"center",sx:{marginLeft:"0.5rem",marginRight:"0.5rem",cursor:pt&&de?"not-allowed":"pointer"},spacing:.5,children:[n(gs,{title:t==="success"?"Validated Successfully":D(t==="error"?"Validation Failed":"Click to Validate"),children:n(Ft,{onClick:o,color:t==="success"?"success":t==="error"?"error":"default",disabled:pt&&de,children:t==="error"?n(Ei,{}):n(Fi,{})})}),!pt&&n(gs,{title:D("Delete Row"),children:n(Ft,{onClick:()=>{Dt({...it,data:e,isVisible:!0})},color:"error",children:n(el,{})})})]})}}],S=(e,t)=>{var o;Zt(ee.filter(c=>pn(c)).indexOf(e.target.innerText)!==-1?ee.filter(c=>pn(c)).indexOf(e.target.innerText):t),kt(((o=e==null?void 0:e.target)==null?void 0:o.id)==="AdditionalKey"?"Additional Data":e.target.innerText)},O=e=>{const t={"Sales Org":"/getSalesOrg","Mrp Profile":"/getMRPProfile"},o=d=>{const u=ts(d.body);Et(g=>({...g,[e]:u}))},c=d=>console.error(d);he(`/${Ce}/data${t[e]}`,"get",o,c)},M=e=>{Di(e,ee,qs,K,le,_,Ps)},G=e=>{br(e,ee,qs,K,le,_,Ps,m)},b=(e,t,o)=>(c,d)=>{var A,y,k;let u={},g="",T="";o==="Purchasing"||o==="Costing"?(u={materialNo:t==null?void 0:t.Material,plant:t==null?void 0:t.Plant},T=t==null?void 0:t.Plant,g=`/${Ce}/data/displayLimitedPlantData`):o==="Accounting"?(u={materialNo:t==null?void 0:t.Material,valArea:t==null?void 0:t.ValArea},T=t==null?void 0:t.ValArea,g=`/${Ce}/data/displayLimitedAccountingData`):o==="Sales"&&(u={materialNo:t==null?void 0:t.Material,salesOrg:t==null?void 0:t.SalesOrg,distChnl:t==null?void 0:t.DistrChan},T=`${t==null?void 0:t.SalesOrg}-${t==null?void 0:t.DistrChan}`,g=`/${Ce}/data/displayLimitedSalesData`);const W=se=>{var xe,$e,Te;o==="Purchasing"||o==="Costing"?_(Ps({materialID:K,viewID:o,itemID:t==null?void 0:t.Plant,data:(xe=se==null?void 0:se.body)==null?void 0:xe.SpecificPlantDataViewDto[0]})):o==="Accounting"?_(Ps({materialID:K,viewID:o,itemID:t==null?void 0:t.ValArea,data:($e=se==null?void 0:se.body)==null?void 0:$e.SpecificAccountingDataViewDto[0]})):o==="Sales"&&_(Ps({materialID:K,viewID:o,itemID:`${t==null?void 0:t.SalesOrg}-${t==null?void 0:t.DistrChan}`,data:(Te=se==null?void 0:se.body)==null?void 0:Te.SpecificSalesDataViewDto[0]}))},p=()=>{};!((k=(y=(A=C==null?void 0:C[K])==null?void 0:A.payloadData)==null?void 0:y[o])!=null&&k[T])&&he(g,"post",W,p,u),V(d?e:null)},Z=()=>ae&&Le&&(ae[Le]||Le==="Additional Data")?Le==="Additional Data"?[n(Bi,{disableCheck:de&&!$n.includes(U==null?void 0:U.requestStatus),materialID:K,selectedMaterialNumber:ks})]:[n(Wi,{disabled:de&&!$n.includes(U==null?void 0:U.requestStatus),selectedMaterialNumber:ks,materialID:K,basicData:ls,setBasicData:Xs,dropDownData:Tt,basicDataTabDetails:ae[Le],allTabsData:ae,activeViewTab:Le,selectedViews:ee,handleAccordionClick:b,missingValidationPlant:Vn,isDisplay:Ee||de,mandatoryFailedView:En,moduleName:"Material",missingFields:$l})]:n(Is,{}),ge=e=>{var c,d;const t=((d=(c=e==null?void 0:e.target)==null?void 0:c.value)==null?void 0:d.toUpperCase())||"";hs(null),Yt(0),De&&clearTimeout(De);const o=setTimeout(()=>{Mn(t,!0)},500);Oe(o)},P=(e,t)=>{const o=H==null?void 0:H.code,c=x==="yes"?"extended":"notExtended";yt(d=>({...d,[e]:t})),e==="Sales Org"&&t?Wl(o,c,t):e==="Plant"&&t&&(al(o,c,t),ql(o,t,te["Sales Org"]))},Se=(e,t,o)=>{e==="Sales Organization"&&(t?(Je(c=>c.map((d,u)=>u===o?{...d,salesOrg:t}:d)),Ye(t,o).then(c=>{})):Je(c=>c.map((d,u)=>u===o?{...d,salesOrg:null}:d)))},Ye=(e,t,o="",c="")=>new Promise((d,u)=>{Be(p=>({...p,"Distribution Channel":{...p["Distribution Channel"],[t]:!0}}));let g={salesOrg:e==null?void 0:e.code};const T=p=>{Be(A=>({...A,"Distribution Channel":{...A["Distribution Channel"],[t]:!1}}));let L=JSON.parse(JSON.stringify(o||At.current));if(L[t].dc.options=ts(p.body),Je(L),At.current=L,c){_(js({materialID:c==null?void 0:c.id,keyName:"orgData",data:L}));let A=(v==null?void 0:v.length)||[JSON.parse(JSON.stringify(c))],y=A.findIndex(k=>k.id===(c==null?void 0:c.id));A[y].orgData=L,_(bt(A)),d({org:L,material:A[y]})}else d(""),Be(A=>({...A,"Distribution Channel":{...A["Distribution Channel"],[t]:!1}}))},W=p=>{console.error(p),Be(L=>({...L,"Distribution Channel":{...L["Distribution Channel"],[t]:!1}}))};he(`/${Ce}/data/getDistrChan`,"post",T,W,g)}),Re=(e,t)=>{let o=JSON.parse(JSON.stringify(le));o[t].dc.value=e,Je(o)},Ct=e=>{let t=JSON.parse(JSON.stringify(le));t.splice(e,1),Je(t)},St=(e,t)=>{let o=JSON.parse(JSON.stringify(le));o[t].plant.value=e,o[t].sloc.value={},o[t].sloc.options=[],o[t].warehouse.value={},o[t].warehouse.options=[],Je(o),Bl(e,t),At.current=o},es=(e,t)=>{var c,d;let o=JSON.parse(JSON.stringify(le));o[t].sloc.value=e,o[t].warehouse.value={},o[t].warehouse.options=[],Je(o),At.current=o,wl((d=(c=o[t])==null?void 0:c.plant)==null?void 0:d.value,e,t)},ds=(e,t)=>{let o=JSON.parse(JSON.stringify(le));o[t].mrpProfile=e,Je(o)},as=(e,t)=>{let o=JSON.parse(JSON.stringify(le));o[t].warehouse.value=e,Je(o)},Xn=()=>{let e=JSON.parse(JSON.stringify(le));e.push(Wt),Je(e)},so=e=>{if(!(e!=null&&e.temp)||(e==null?void 0:e.temp)===(Xe==null?void 0:Xe.temp))return;me(!0);let t={decisionTableId:null,decisionTableName:"MDG_MAT_ORGDATA_TEMPLATE_CONFIG",version:"v2",rulePolicy:null,validityDate:null,conditions:[{"MDG_CONDITIONS.MDG_MAT_REGION":(h==null?void 0:h.Region)||jt.US,"MDG_CONDITIONS.MDG_MAT_TEMPLATE":e.temp||""}],systemFilters:null,systemOrders:null,filterString:null};const o=d=>{var u,g;if(d.statusCode===Qe.STATUS_200){me(!1);let T=(g=(u=d==null?void 0:d.data)==null?void 0:u.result[0])==null?void 0:g.MDG_MAT_ORGDATA_TEMPLATE_CONFIG,W=[];T==null||T.forEach((p,L)=>{var $e;let A=JSON.parse(JSON.stringify(Wt));A.salesOrg={},A.salesOrg.code=p.MDG_MAT_SALES_ORG,A.salesOrg.desc=p.MDG_MAT_SALES_ORG_DESC,A.plant.value={},A.plant.value.code=p.MDG_MAT_PLANT,A.plant.value.desc=p.MDG_MAT_PLANT_DESC;let y=($e=Cs==null?void 0:Cs.filter(Te=>Te.MDG_MAT_SALES_ORG===p.MDG_MAT_SALES_ORG))==null?void 0:$e.map(Te=>({code:Te.MDG_MAT_PLANT,desc:Te.MDG_MAT_PLANT_DESC}));y=y==null?void 0:y.filter((Te,nt,Mt)=>nt===Mt.findIndex(_t=>_t.code===Te.code)),A.plant.options=y==null?void 0:y.sort((Te,nt)=>Te.code-nt.code);let k=Cs==null?void 0:Cs.filter(Te=>Te.MDG_MAT_SALES_ORG===p.MDG_MAT_SALES_ORG&&Te.MDG_MAT_PLANT===p.MDG_MAT_PLANT),se=k==null?void 0:k.map(Te=>({code:Te.MDG_MAT_STORAGE_LOCATION,desc:Te.MDG_MAT_STORE_LOC_DESC})),xe=k==null?void 0:k.map(Te=>Te.MDG_MAT_WAREHOUSE?{code:Te.MDG_MAT_WAREHOUSE,desc:Te.MDG_MAT_WAREHOUSE_DESC}:null).filter(Boolean);p.MDG_MAT_STORAGE_LOCATION&&(A.sloc.value={},A.sloc.value.code=p.MDG_MAT_STORAGE_LOCATION,A.sloc.value.desc=p.MDG_MAT_STORE_LOC_DESC),A.sloc.options=se,p.MDG_MAT_WAREHOUSE&&(A.warehouse.value={},A.warehouse.value.code=p.MDG_MAT_WAREHOUSE||"",A.warehouse.value.desc=p.MDG_MAT_WAREHOUSE_DESC||""),A.warehouse.options=xe,W.push(A)}),At.current=W,Je(W),So(W,0)}else re("Something went wrong"),me(!1),mt("No Org data found","error")},c=d=>{re("Something went wrong"),me(!1),mt("No Org data found","error")};_e.environment==="localhost"?he(`/${rn}${gt.INVOKE_RULES.LOCAL}`,"post",o,c,t):he(`/${rn}${gt.INVOKE_RULES.PROD}`,"post",o,c,t)},So=async(e,t)=>{t<(e==null?void 0:e.length)&&(await Ye(e[t].salesOrg,t),t++,So(e,t))},zi=()=>{const e=it==null?void 0:it.data;Xt(v==null?void 0:v.filter(t=>{var o;return t.id!==((o=e==null?void 0:e.row)==null?void 0:o.id)})),_(Li(e==null?void 0:e.row.id)),sn(""),_(bt(v==null?void 0:v.filter(t=>{var o;return t.id!==((o=e==null?void 0:e.row)==null?void 0:o.id)}))),v!=null&&v.length?v.filter(t=>{var o,c;return((o=t.params)==null?void 0:o.id)!==((c=e==null?void 0:e.row)==null?void 0:c.id)}).every(t=>t.validated)&&rt(!1):rt(!1),Dt({...it,isVisible:!1})};i.useEffect(()=>{var u,g,T,W,p,L,A;const e=ee==null?void 0:ee.includes((u=m)==null?void 0:u.SALES),t=ee==null?void 0:ee.includes((g=m)==null?void 0:g.SALES_PLANT),o=ee==null?void 0:ee.includes((T=m)==null?void 0:T.STORAGE),c=ee==null?void 0:ee.includes((W=m)==null?void 0:W.STORAGE_PLANT),d=(A=(L=(p=C[K])==null?void 0:p.headerData)==null?void 0:L.orgData)==null?void 0:A.some(y=>{var k,se;return(se=(k=y==null?void 0:y.plant)==null?void 0:k.value)==null?void 0:se.code});e&&!t&&d&&f(y=>{var xe,$e;const k=[...y],se=k.indexOf((xe=m)==null?void 0:xe.SALES);return k.splice(se+1,0,($e=m)==null?void 0:$e.SALES_PLANT),k}),o&&!c&&f(y=>{var xe,$e;const k=[...y],se=k.indexOf((xe=m)==null?void 0:xe.STORAGE);return k.splice(se+1,0,($e=m)==null?void 0:$e.STORAGE_PLANT),k})},[ee,(Lo=(Do=C[K])==null?void 0:Do.headerData)==null?void 0:Lo.orgData]);const To=e=>{!e||!Array.isArray(e)||e.forEach(t=>{var o,c,d,u,g,T,W,p,L,A,y,k,se,xe,$e,Te;if((c=(o=t.plant)==null?void 0:o.value)!=null&&c.code){if(ys((u=(d=t.plant)==null?void 0:d.value)==null?void 0:u.code,m.PLANT),(g=t.salesOrg)!=null&&g.code||(W=(T=t.dc)==null?void 0:T.value)!=null&&W.code){const nt=`${((p=t.salesOrg)==null?void 0:p.code)||""}-${((A=(L=t.dc)==null?void 0:L.value)==null?void 0:A.code)||""}`;ys(nt,m.SALES)}(k=(y=t.warehouse)==null?void 0:y.value)!=null&&k.code&&ys((xe=(se=t.warehouse)==null?void 0:se.value)==null?void 0:xe.code,m.WAREHOUSE),Ul((Te=($e=t.plant)==null?void 0:$e.value)==null?void 0:Te.code)}})};i.useEffect(()=>{if(Ee){const e=ut==null?void 0:ut.orgData;(e==null?void 0:e.length)>0&&e.some(t=>{var o,c,d,u,g;return((c=(o=t.plant)==null?void 0:o.value)==null?void 0:c.code)&&(((d=t.salesOrg)==null?void 0:d.code)||((g=(u=t.dc)==null?void 0:u.value)==null?void 0:g.code))})&&To(e)}},[ut==null?void 0:ut.orgData]);const Ao=e=>{_(vi(e)),r(e)};i.useEffect(()=>{var e,t;(z==null?void 0:z.page)!==0&&(w===((e=$)==null?void 0:e.CREATE_WITH_UPLOAD)||w===((t=$)==null?void 0:t.CREATE))&&J(),r((z==null?void 0:z.page)||0)},[z==null?void 0:z.page]);const Xi=()=>{B(!X),ne&&j(!1)},Ki=()=>{j(!ne),X&&B(!1)},pn=e=>{if(!ae||!e)return!1;const t=Object.keys(ae).find(d=>d.toLowerCase()===e.toLowerCase());return t?!Object.values(ae[t]).flat().every(d=>d.visibility==="Hidden"):!1};return N("div",{children:[n("div",{style:{padding:"0",width:"100%",margin:"0",marginTop:"20px"},children:N(Ve,{sx:{position:X?"fixed":"relative",top:X?0:"auto",left:X?0:"auto",right:X?0:"auto",bottom:X?0:"auto",width:X?"100vw":"100%",height:X?"100vh":"auto",zIndex:X?1004:void 0,backgroundColor:X?"white":"transparent",padding:X?"20px":"0",display:"flex",flexDirection:"column",boxShadow:X?"0px 0px 15px rgba(0, 0, 0, 0.2)":"none",transition:"all 0.3s ease",borderRadius:"8px",border:"1px solid #e0e0e0"},children:[N(Ve,{sx:{display:"flex",justifyContent:"space-between",alignItems:"center",padding:"8px 16px",backgroundColor:"#f5f5f5",borderRadius:"8px 8px 0 0"},children:[n(Gt,{variant:"h6",children:D("Material Data")}),N(Ve,{sx:{display:"flex",alignItems:"center",gap:1},children:[N(je,{variant:"contained",color:"primary",size:"small",onClick:()=>{w===$.CREATE&&(R(!0),We([]),ce(null),yt({}),Mn("",!0))},disabled:Lt||pt,children:["+ ",D("Add")]}),n(gs,{title:D(X?"Exit Zoom":"Zoom In"),sx:{zIndex:"1009"},children:n(Ft,{onClick:Xi,color:"primary",sx:{backgroundColor:"rgba(0, 0, 0, 0.05)","&:hover":{backgroundColor:"rgba(0, 0, 0, 0.1)"}},children:X?n(xl,{}):n(Dl,{})})})]})]}),Ee&&v&&(v==null?void 0:v.length)>0?n("div",{style:{width:"100%",height:"100%",overflowX:"auto"},children:n("div",{style:{height:"100%"},children:n(_l,{rows:v,columns:E,pageSize:50,autoHeight:!1,page:ht,rowCount:(z==null?void 0:z.totalElements)||0,rowsPerPageOptions:[50],onRowClick:zn,onCellEditCommit:qt,onPageChange:e=>Ao(e),pagination:!0,disableSelectionOnClick:!0,getRowClassName:e=>e.id===K?"selected-row":"",style:{border:"1px solid #ccc",borderRadius:"8px",width:"100%",height:X?"calc(100vh - 150px)":`${Math.min(v.length*50+130,300)}px`,overflow:"auto"},sx:{"& .selected-row":{backgroundColor:"rgb(234 233 255)"}}})})}):n(Is,{children:n("div",{style:{width:"100%",height:"100%",overflowX:"auto"},children:n("div",{style:{height:"100%"},children:n(_l,{autoHeight:!1,rows:v,columns:E,pageSize:50,page:ht,rowsPerPageOptions:[50],onRowClick:zn,onCellEditCommit:qt,onPageChange:e=>Ao(e),disableSelectionOnClick:!0,getRowClassName:e=>e.id===K?"selected-row":"",style:{border:"1px solid #ccc",borderRadius:"8px",width:"100%",height:X?"calc(100vh - 150px)":`${Math.min(v.length*50+130,300)}px`,overflow:"auto"},sx:{"& .selected-row":{backgroundColor:"rgb(234 233 255)"}}})})})})]})}),w===$.CREATE||w===$.CREATE_WITH_UPLOAD||Ie!=null&&Ie.ATTRIBUTE_1?K&&Qt&&(v==null?void 0:v.length)>0&&(ss==null?void 0:ss.length)>0&&ae&&((vo=Object.getOwnPropertyNames(ae))==null?void 0:vo.length)>0&&N(Ve,{sx:{position:ne?"fixed":"relative",top:ne?0:"auto",left:ne?0:"auto",right:ne?0:"auto",bottom:ne?0:"auto",width:ne?"100vw":"100%",height:ne?"100vh":"auto",zIndex:ne?1004:void 0,backgroundColor:ne?"white":"transparent",padding:ne?"20px":"0",marginTop:"20px",display:"flex",flexDirection:"column",boxShadow:ne?"0px 0px 15px rgba(0, 0, 0, 0.2)":"none",transition:"all 0.3s ease",borderRadius:"8px",border:"1px solid #e0e0e0"},children:[N(Ve,{sx:{display:"flex",justifyContent:"space-between",alignItems:"center",padding:"8px 16px",borderRadius:"8px 8px 0 0"},children:[n(Gt,{variant:"h6",children:D("View Details")}),n(gs,{title:D(ne?"Exit Zoom":"Zoom In"),sx:{zIndex:"1009"},children:n(Ft,{onClick:Ki,color:"primary",sx:{backgroundColor:"rgba(0, 0, 0, 0.05)","&:hover":{backgroundColor:"rgba(0, 0, 0, 0.1)"}},children:ne?n(xl,{}):n(Dl,{})})})]}),N(Ve,{sx:{flexGrow:1,display:"flex",flexDirection:"column"},children:[N(Si,{value:Ts,onChange:S,className:dt.customTabs,"aria-label":"material tabs",sx:{top:0,position:"sticky",zIndex:998,backgroundColor:ke.background.container,borderBottom:`1px solid ${ke.border.light}`,"& .MuiTab-root":{minHeight:"48px",textTransform:"none",fontSize:"14px",fontWeight:600,color:ke.black.graphite,"&.Mui-selected":{color:ke.primary.main,fontWeight:700},"&:hover":{color:ke.primary.main,opacity:.8}},"& .MuiTabs-indicator":{backgroundColor:ke.primary.main,height:"3px"}},children:[ee&&le.length>0&&(ee==null?void 0:ee.length)>0?ee==null?void 0:ee.map((e,t)=>pn(e)&&n(Rl,{label:D(e)},t)):n(Is,{}),n(Rl,{label:D("Additional Data"),id:"AdditionalKey"},"Additional data")]}),n(Ve,{sx:{padding:2,marginTop:2,flexGrow:1,overflow:"auto",height:ne?"calc(100vh - 180px)":"auto"},children:(v==null?void 0:v.length)>0&&Z()}),(!pt||Ee&&!de||de&&$n.includes(U==null?void 0:U.requestStatus))&&n(Ve,{sx:{borderTop:"1px solid #e0e0e0",padding:"16px"},children:n(Hi,{activeTab:Ts,submitForApprovalDisabled:!Vs(I),filteredButtons:cl,validateMaterials:Vl,workFlowLevels:Pn,showWfLevels:rl,childRequestHeaderData:(yo=C==null?void 0:C[K])==null?void 0:yo.Tochildrequestheaderdata,module:Cn.MAT})})]})]}):n(Is,{}),n(fo,{dialogState:ns,openReusableDialog:to,closeReusableDialog:An,dialogTitle:"Warning",dialogMessage:Nn,showCancelButton:!1,handleOk:Kl,handleDialogConfirm:An,dialogOkText:"OK",dialogSeverity:"danger"}),Pt&&n(kn,{fullWidth:!0,maxWidth:!1,open:!0,onClose:xn,sx:{display:"flex",justifyContent:"center"},disableEscapeKeyDown:!0,children:N(Ve,{sx:{width:"600px !important"},children:[N(Wn,{sx:{backgroundColor:lt.palette.primary.light,marginBottom:".5rem"},children:[n(tl,{style:{height:"20px",width:"20px",marginBottom:"-5px"}}),n("span",{children:D("Select Views")})]}),n(Js,{sx:{paddingBottom:".5rem"},children:N(Ve,{display:"flex",alignItems:"center",sx:{flex:1,padding:"22px 0px",gap:"5px"},children:[n(Ol,{size:"small",multiple:!0,fullWidth:!0,options:Us||[],disabled:pt,disableCloseOnSelect:!0,value:ee==null?void 0:ee.filter(e=>!Ti.includes(e)),onChange:(e,t)=>{Ie!=null&&Ie.requestId||(f([Y,...t.filter(o=>o!==Y)]),qt({id:Ms,field:Gn.VIEWS,value:t}))},getOptionDisabled:e=>e===Y,renderOption:(e,t,{selected:o})=>N("li",{...e,children:[n(Nl,{checked:o,sx:{marginRight:1}}),t]}),renderTags:(e,t)=>e.map((o,c)=>{const{key:d,...u}=t({index:c});return n(Ai,{label:o,...u,disabled:o===Y||pt},d)}),renderInput:e=>n(on,{...e,label:D("Select Views")})}),n(je,{variant:"contained",size:"small",onClick:()=>gl(),disabled:pt,children:D("Select all")})]})}),n(zs,{children:n(je,{onClick:()=>{os(!1),qt({id:Ms,field:"views",value:ee})},variant:"contained",children:D("Ok")})})]})}),$s&&N(kn,{fullWidth:!0,maxWidth:"xl",open:!0,onClose:xn,disableEscapeKeyDown:!0,sx:{"& .MuiDialog-paper":{padding:2,borderRadius:2}},children:[N(Wn,{sx:{display:"flex",alignItems:"center",gap:1,backgroundColor:lt.palette.primary.light},children:[n(tl,{fontSize:"small"}),n("span",{children:D("Select Org Data")}),n(Ve,{sx:{position:"absolute",right:"7%",width:"15%"},children:n(Ol,{options:ie.filter(e=>e.region===(h==null?void 0:h.Region)),value:Xe,size:"small",disabled:pt,isOptionEqualToValue:(e,t)=>e.region===t.region,onChange:(e,t)=>{Rt(t),so(t)},getOptionLabel:e=>e==null?void 0:e.temp,renderInput:e=>n(on,{...e,label:D("Select Template"),sx:{minWidth:165}}),sx:{"& .MuiAutocomplete-popper":{minWidth:250}}})}),n(Ft,{onClick:xn,sx:{position:"absolute",right:15},children:n(pi,{})})]}),n(Js,{sx:{padding:0},children:n(Ci,{component:Oi,children:N(bi,{children:[n(Ni,{children:N(Il,{children:[n(qe,{align:"center",children:D("S NO.")}),N(qe,{align:"center",children:[D("Sales Org"),n("span",{style:{color:ke.error.deepRed,marginLeft:2},children:"*"})]}),N(qe,{align:"center",children:[D("Distribution Channel"),n("span",{style:{color:ke.error.deepRed,marginLeft:2},children:"*"})]}),N(qe,{align:"center",children:[D("Plant"),n("span",{style:{color:ke.error.deepRed,marginLeft:2},children:"*"})]}),N(qe,{align:"center",children:[D("Storage Location"),n("span",{style:{color:ke.error.deepRed,marginLeft:2},children:"*"})]}),(h==null?void 0:h.Region)!=="EUR"&&N(qe,{align:"center",children:[D("Warehouse"),n("span",{style:{color:ke.error.deepRed,marginLeft:2},children:"*"})]}),n(qe,{align:"center",children:D("MRP Profile")}),le.length>1&&n(qe,{align:"center",children:D("Action")})]})}),n(mi,{children:le.map((e,t)=>{var o,c,d,u,g,T,W,p,L,A,y,k,se,xe,$e,Te,nt,Mt,_t,ms,wt,_s,Pe,Ht,Rs,Hs,Kn,Yn,Zn,Go;return N(Il,{sx:{padding:"12px",opacity:pt?.5:1,pointerEvents:pt?"none":"auto"},children:[n(qe,{children:n(Gt,{variant:"body2",children:t+1})}),n(qe,{children:n(ft,{options:Tt["Sales Organization"],value:e.salesOrg,onChange:Vt=>Se("Sales Organization",Vt,t),placeholder:D("Select Sales Org"),minWidth:165,listWidth:215,title:((o=e==null?void 0:e.salesOrg)==null?void 0:o.code)+` - ${(c=e==null?void 0:e.salesOrg)==null?void 0:c.desc}`,disabled:!Ln(vn.salesOrg,ee)})}),n(qe,{children:n(ft,{options:((d=e.dc)==null?void 0:d.options)||[],isLoading:((u=Ns["Distribution Channel"])==null?void 0:u[t])||!1,value:(g=e.dc)==null?void 0:g.value,onChange:Vt=>Re(Vt,t),placeholder:D("Select DC"),disabled:!Ln(vn.distributionChannel,ee),minWidth:165,listWidth:215,title:((W=(T=e==null?void 0:e.dc)==null?void 0:T.value)==null?void 0:W.code)+` - ${(L=(p=e==null?void 0:e.dc)==null?void 0:p.value)==null?void 0:L.desc}`})}),n(qe,{children:n(ft,{options:Tt.PlantNotExtended||[],value:(A=e.plant)==null?void 0:A.value,onChange:Vt=>St(Vt,t),placeholder:D("Select Plant"),disabled:!Ln(vn.plant,ee),minWidth:165,listWidth:215,title:((k=(y=e==null?void 0:e.plant)==null?void 0:y.value)==null?void 0:k.code)+` - ${(xe=(se=e==null?void 0:e.plant)==null?void 0:se.value)==null?void 0:xe.desc}`})}),n(qe,{children:n(ft,{options:($e=e==null?void 0:e.sloc)==null?void 0:$e.options,value:(Te=e==null?void 0:e.sloc)==null?void 0:Te.value,isLoading:((nt=Ns["Storage Location"])==null?void 0:nt[t])||!1,onChange:Vt=>es(Vt,t),placeholder:D("Select Sloc"),disabled:!Ln(vn.storage,ee),minWidth:165,listWidth:215,title:((_t=(Mt=e==null?void 0:e.sloc)==null?void 0:Mt.value)==null?void 0:_t.code)+` - ${(wt=(ms=e==null?void 0:e.sloc)==null?void 0:ms.value)==null?void 0:wt.desc}`})}),(h==null?void 0:h.Region)!=="EUR"&&n(qe,{children:n(ft,{options:((_s=e==null?void 0:e.warehouse)==null?void 0:_s.options)||[],value:(Pe=e==null?void 0:e.warehouse)==null?void 0:Pe.value,isLoading:((Ht=Ns.Warehouse)==null?void 0:Ht[t])||!1,onChange:Vt=>as(Vt,t),disabled:!Ln(vn.warehouse,ee),placeholder:D("Select Warehouse"),minWidth:165,listWidth:215,title:((Hs=(Rs=e==null?void 0:e.warehouse)==null?void 0:Rs.value)==null?void 0:Hs.code)+` - ${(Yn=(Kn=e==null?void 0:e.warehouse)==null?void 0:Kn.value)==null?void 0:Yn.desc}`})}),n(qe,{children:n(ft,{options:Tt["Mrp Profile"]||[],value:e.mrpProfile,onChange:Vt=>ds(Vt,t),placeholder:D("Select MRP Profile"),disabled:!Ln(vn.mrpProfile,ee),isOptionDisabled:Vt=>{var nn,Uo;if(t===0)return!1;const Dn=(Uo=(nn=le[t].plant)==null?void 0:nn.value)==null?void 0:Uo.code;if(!Dn)return!1;const Fs=le.slice(0,t).find(Yi=>{var $o,ko;return((ko=($o=Yi.plant)==null?void 0:$o.value)==null?void 0:ko.code)===Dn});return Fs&&Fs.mrpProfile?Vt.code!==Fs.mrpProfile.code:!1},minWidth:165,listWidth:215,title:((Zn=e==null?void 0:e.mrpProfile)==null?void 0:Zn.code)+` - ${(Go=e==null?void 0:e.mrpProfile)==null?void 0:Go.desc}`})}),le.length>1&&N(qe,{align:"right",children:[n(Ft,{size:"small",color:"primary",onClick:()=>{ll(!0),ol({orgRowLength:le.length,copyFor:t});const Vt=le.filter(Dn=>{var Fs,nn;return(nn=(Fs=Dn.plant)==null?void 0:Fs.value)==null?void 0:nn.code}).map(Dn=>{var Fs,nn;return(nn=(Fs=Dn.plant)==null?void 0:Fs.value)==null?void 0:nn.code});x==="yes"&&G(Vt)},style:{display:t===0?"none":"inline-flex"},children:n(ro,{})}),n(Ft,{size:"small",color:"error",onClick:()=>Ct(t),children:n(el,{})})]})]},t)})})]})})}),N(zs,{sx:{justifyContent:"flex-end",gap:.5},children:[N(je,{onClick:Xn,variant:"contained",disabled:!Ws||pt,children:["+ ",D("Add")]}),n(gs,{title:Ws?"":D("Please fill all the fields of first row at least"),arrow:!0,children:n("span",{children:n(je,{onClick:()=>{var e,t;if(rs(!1),(t=(e=le[0].plant)==null?void 0:e.value)!=null&&t.code){To(le),qt({id:Ms,field:"orgData",value:le}),jl(le);const o=v==null?void 0:v.map(c=>c.id===K?{...c,orgData:le}:c);if(_(bt(o)),x==="no"){const c=le.filter(d=>{var u,g;return(g=(u=d.plant)==null?void 0:u.value)==null?void 0:g.code}).map(d=>{var u,g;return(g=(u=d.plant)==null?void 0:u.value)==null?void 0:g.code});c.length>0&&M(c)}Rt(null)}},variant:"contained",disabled:!Ws||pt,tooltip:Ws?"":D("Please fill all the fields of first row at least"),children:D("Apply")})})})]})]}),ve&&N(kn,{fullWidth:!0,open:!0,maxWidth:"lg",sx:{"&::webkit-scrollbar":{width:"1px"}},children:[n(Wn,{sx:{display:"flex",alignItems:"center",justifyContent:"space-between",padding:"0.75rem 1rem",background:e=>e.palette.primary.light,borderBottom:"1px solid #d6d6f0"},children:N(Ve,{sx:{display:"flex",alignItems:"center"},children:[n(Sc,{sx:{mr:1,color:lt.palette.primary.dark}}),n(Gt,{variant:"h6",sx:{fontWeight:600,color:lt.palette.primary.dark},children:D("Add New Material")})]})}),N(Js,{sx:{padding:".5rem 1rem",alignItems:"center",justifyContent:"center",margin:"0px 25px"},children:[N(pr,{component:"fieldset",sx:{paddingBottom:"2%"},children:[n(Gt,{sx:{padding:"15px 0px",fontWeight:"600",fontSize:"15px",color:lt.palette.primary.dark},children:D("How would you like to proceed?")}),N(Cr,{row:!0,"aria-label":"profit-center-number",name:"profit-center-number",value:x,onChange:e=>Ge(e.target.value),children:[n(jo,{value:"yes",control:n(Vo,{}),label:D("With Reference")}),n(jo,{value:"no",control:n(Vo,{}),label:D("Without Reference")})]})]}),N(xt,{container:!0,spacing:2,children:[n(xt,{item:!0,xs:12,children:N(xt,{container:!0,spacing:2,children:[n(xt,{item:!0,xs:3,children:n(ft,{options:ml||[],value:te[Ds.MATERIAL_TYPE]||"",onChange:e=>{yt(t=>({...t,[Ds.MATERIAL_TYPE]:e}))},placeholder:D("Select Material Type"),minWidth:180,listWidth:266,disabled:(ue==null?void 0:ue.length)||x==="no",getOptionLabel:e=>e!=null&&e.desc?`${e.code} - ${e.desc}`:(e==null?void 0:e.code)||"",renderOption:(e,t)=>N("li",{...e,children:[n("strong",{children:t==null?void 0:t.code}),t!=null&&t.desc?` - ${t==null?void 0:t.desc}`:""]})})}),n(xt,{item:!0,xs:3,children:n(ft,{options:_n,value:q||H,onChange:e=>{ce(e),hs(e),e||ge(e)},minWidth:180,listWidth:266,placeholder:D("Select Material"),disabled:(ue==null?void 0:ue.length)||x==="no",getOptionLabel:e=>e!=null&&e.desc?`${e.code} - ${e.desc}`:(e==null?void 0:e.code)||"",renderOption:(e,t)=>N("li",{...e,children:[n("strong",{children:t==null?void 0:t.code}),t!=null&&t.desc?` - ${t==null?void 0:t.desc}`:""]}),handleInputChange:ge,sx:{minWidth:270,"& .MuiAutocomplete-popper":{minWidth:306}},isLoading:Ns["Material No"]})}),It==null?void 0:It.slice(0,2).map(e=>n(xt,{item:!0,xs:3,children:n(ft,{options:(Tt==null?void 0:Tt[e])||[],value:te[e]||"",onChange:t=>{P(e,t)},placeholder:D(`Select ${e}`),minWidth:180,listWidth:306,sx:{minWidth:270,"& .MuiAutocomplete-popper":{minWidth:306}},disabled:(ue==null?void 0:ue.length)||x==="no",isLoading:Ns[e]})},e))]})}),n(xt,{item:!0,xs:12,children:N(xt,{container:!0,spacing:2,alignItems:"center",children:[n(xt,{item:!0,xs:3,children:n(ft,{options:(Tt==null?void 0:Tt[It[2]])||[],value:te[It[2]]||"",onChange:e=>{yt(t=>({...t,[It[2]]:e}))},placeholder:D(`Select ${It[2]}`),minWidth:180,listWidth:306,sx:{minWidth:270,"& .MuiAutocomplete-popper":{minWidth:306}},disabled:(ue==null?void 0:ue.length)||x==="no",isLoading:Ns["Distribution Channel"]===!0})}),It==null?void 0:It.slice(3).map(e=>n(xt,{item:!0,xs:3,children:n(ft,{options:(Tt==null?void 0:Tt[e])||[],value:te[e]||"",onChange:t=>{yt(o=>({...o,[e]:t}))},placeholder:D(`Select ${e}`),minWidth:180,listWidth:306,sx:{minWidth:270,"& .MuiAutocomplete-popper":{minWidth:306}},disabled:(ue==null?void 0:ue.length)||x==="no",isLoading:Ns[e]===!0})},e)),(I==null?void 0:I.length)>0&&N(Is,{children:[n(xt,{item:!0,xs:1,sx:{textAlign:"center"},children:n(Gt,{variant:"body1",sx:{fontWeight:"bold",color:"gray"},children:"OR"})}),n(xt,{item:!0,xs:3,children:n(ft,{options:I.map(e=>({...e,code:e.lineNumber,desc:""})),value:ue[0],onChange:e=>{We(e?[e]:[]),yt({}),ce(null),hs(null)},minWidth:180,listWidth:266,placeholder:D("Select Material Line Number"),disabled:(H==null?void 0:H.code)||x==="no",getOptionLabel:e=>e!=null&&e.desc?`${e.code} - ${e.desc}`:(e==null?void 0:e.code)||"",renderOption:(e,t)=>N("li",{...e,children:[n("strong",{children:t==null?void 0:t.code}),t!=null&&t.desc?` - ${t==null?void 0:t.desc}`:""]}),sx:{minWidth:270,"& .MuiAutocomplete-popper":{minWidth:306}}})})]})]})})]})]}),N(zs,{sx:{display:"flex",justifyContent:"end"},children:[n(je,{sx:{width:"max-content",textTransform:"capitalize"},onClick:()=>{R(!1),yt({})},variant:"outlined",children:D("Cancel")}),n(je,{type:"save",disabled:!(ue!=null&&ue.length||H!=null&&H.code)&&x==="yes",onClick:hl,variant:"contained",children:D("Proceed")})]})]}),(it==null?void 0:it.isVisible)&&N(uo,{isOpen:it==null?void 0:it.isVisible,titleIcon:n(el,{size:"small",color:"error",sx:{fontSize:"20px"}}),Title:D("Delete Row")+"!",handleClose:()=>Dt({...it,isVisible:!1}),children:[n(Js,{sx:{mt:2},children:D(_i.DELETE_MESSAGE)}),N(zs,{children:[n(je,{variant:"outlined",size:"small",sx:{...Ri},onClick:()=>Dt({...it,isVisible:!1}),children:D(Po.CANCEL)}),n(je,{variant:"contained",size:"small",sx:{...Ii},onClick:zi,children:D(Po.DELETE)})]})]}),nl&&n(Ji,{open:nl,onClose:()=>ll(!1),title:ao.COPY_ORG_DATA_VALES_HEADING,selectedMaterialPayload:qs,lengthOfOrgRow:Gl,materialID:K,orgRows:le}),un&&n(yl,{openSnackBar:wn,alertMsg:un,alertType:ps,handleSnackBarClose:()=>an(!1)}),n(Hn,{blurLoading:cs,loaderMessage:Ke}),n(co,{})]})},_c=({openSearchMat:U,setOpenSearchMat:dt,AddCopiedMaterial:re})=>{const[_,lt]=i.useState(!1),et=oe(J=>J.AllDropDown.dropDown),tt={Extend:[{key:"Material Type",options:ml},{key:"Material Number",options:[]},{key:"Plant",options:[]},{key:"Sales Org",options:[]},{key:"Distribution Channel",options:[]},{key:"Storage Location",options:[]},{key:"Division",options:[]}]},F=(J,He="0",h)=>{var z,C,Ze,I,be,Ae,ae,Ot,Ie,Fe,ot,at;const w={materialNo:((C=(z=J==null?void 0:J["Material Number"])==null?void 0:z.map(Ne=>Ne.code))==null?void 0:C.join(","))??"",division:((I=(Ze=J==null?void 0:J.Division)==null?void 0:Ze.map(Ne=>Ne.code))==null?void 0:I.join(","))??"",plant:((Ae=(be=J==null?void 0:J.Plant)==null?void 0:be.map(Ne=>Ne.code))==null?void 0:Ae.join(","))??"",salesOrg:((Ot=(ae=J==null?void 0:J["Sales Org"])==null?void 0:ae.map(Ne=>Ne.code))==null?void 0:Ot.join(","))??"",distrChan:((Fe=(Ie=J==null?void 0:J["Distribution Channel"])==null?void 0:Ie.map(Ne=>Ne.code))==null?void 0:Fe.join(","))??"",storageLocation:((at=(ot=J==null?void 0:J["Storage Location"])==null?void 0:ot.map(Ne=>Ne.code))==null?void 0:at.join(","))??"",top:200,skip:He},Q=Ne=>{var st;if((Ne==null?void 0:Ne.statusCode)===Qe.STATUS_200){const de=(st=Ne==null?void 0:Ne.body)==null?void 0:st.map(Ee=>{if(Ee.Views){const X=Ee.Views.split(",").map(B=>B.trim()).filter(B=>!go.includes(B)).join(",");return{...Ee,Views:X}}return Ee});re(de||[]),h==null||h(de||[]),lt(!1)}},_e=()=>{lt(!1),h==null||h([])};lt(!0),he(`/${Ce}${gt.DATA.GET_EXTEND_SEARCH_SET}`,"post",Q,_e,w)};return N(Is,{children:[n(oc,{open:U,onClose:()=>dt(!1),parameters:tt.Extend,onSearch:(J,He,h)=>F(J,He,h),templateName:"Extend",name:"Extend",allDropDownData:et,buttonName:"Search"}),n(Hn,{blurLoading:_})]})};function Rc(){const U=bn(),{customError:dt}=vl(),re=oe(et=>et.tabsData.allTabsData),_=i.useCallback(et=>{const tt=J=>{var He;(J==null?void 0:J.statusCode)===((He=Qe)==null?void 0:He.STATUS_200)?U(Ml({matType:et,views:(J==null?void 0:J.body)||[]})):U(Ml({matType:et,views:[]}))},F=J=>{dt(J)};he(`/${Ce}${gt.DATA.GET_VIEWS_FOR_MAT}=${et}`,"get",tt,F)},[U]),lt=i.useCallback(et=>{if(!re||!et)return!1;const tt=Object.keys(re).find(He=>He.toLowerCase()===et.toLowerCase());return tt?!Object.values(re[tt]).flat().every(He=>He.visibility===Qo.HIDDEN):!1},[re]);return{getViews:_,checkForHiddenView:lt}}function Ic(U){if(!U)return!1;const dt=["salesOrg","dc","plant","sloc","warehouse"];for(const re of dt){if(!U[re])return!1;if(re==="salesOrg"){if(!U[re].code)return!1}else if(!U[re].value||!U[re].value.code)return!1}return!0}const Qn=()=>n("span",{style:{color:ke.error.deepRed,marginLeft:2},children:"*"}),Mc=ni(()=>({customTabs:{"& .MuiTabs-scroller":{overflowX:"auto !important",overflowY:"hidden !important"}}})),fs={NOT_EXTENDED:"notExtended",EXTENDED:"Extended"},xc=U=>{var xn,gl,hl,El,pt;const dt=Mc(),{customError:re}=vl(),_=bn(),{fetchMaterialFieldConfig:lt}=ho(),{getNextDisplayDataForCreate:et}=Gi(),{fetchValuationClassData:tt}=Ui(),F=oe(s=>s.payload.payloadData),J=F==null?void 0:F.RequestType,He=oe(s=>s.applicationConfig),h=oe(s=>s.paginationData),w=oe(s=>s.payload),Q=oe(s=>s.request.materialRows),_e=oe(s=>{var l;return((l=s.materialDropDownData)==null?void 0:l.dropDown)||{}}),z=oe(s=>s.tabsData.allTabsData);let C=oe(s=>s.userManagement.taskData);const Ze=oe(s=>s.tabsData.allMaterialFieldConfigDT),{checkForHiddenView:I}=Rc(),be=sl(),Ae=new URLSearchParams(be.search),ae=Ae.get("RequestId"),Ot=Ae.get("RequestType"),Ie=Ae.get("reqBench"),{showSnackbar:Fe}=ii(),[ot,at]=i.useState(0),[Ne,st]=i.useState(null),[de,Ee]=i.useState(null),X="Basic Data",[B,ne]=i.useState([X]),[j,ht]=i.useState([]),[r,De]=i.useState(Q||[]),Oe=oe(s=>s.selectedSections.selectedSections),[zt,V]=i.useState(!1),[Y,ee]=i.useState(!1),[f,it]=i.useState(""),[Dt,v]=i.useState([]),[Xt,ss]=i.useState(0),[Lt,rt]=i.useState({code:"",desc:""}),[Bn,vs]=i.useState(!1),[ns,ct]=i.useState(""),[Nn,mn]=i.useState(""),{fetchDataAndDispatch:ys}=ki(),[_n,Kt]=i.useState(!0),[cn,Yt]=i.useState(r.length+1),[q,hs]=i.useState(!1),[Ue,Es]=i.useState(!1),[we,ye]=i.useState(0),[Ut,Ss]=i.useState(Q.length>0),[Ts,Zt]=i.useState({}),[Qt,Me]=i.useState({}),[ls,Xs]=i.useState([]),[Tt,Et]=i.useState({}),[Ks,As]=i.useState([]),[vt,Ys]=i.useState(!1),[$t,Gs]=i.useState(""),[Us,Zs]=i.useState(0),[Pt,os]=i.useState(m.BASIC_DATA),[Ms,is]=i.useState(!1),[Le,kt]=i.useState(null),[$s,rs]=i.useState([]),Wt=oe(s=>s.request.salesOrgDTData),le=(xn=w==null?void 0:w[Le])==null?void 0:xn.headerData,Je=(F==null?void 0:F.Region)===jt.EUR?{id:0,salesOrg:null,dc:{value:null,options:[]},plant:{value:null,options:[]},sloc:{value:null,options:[]},mrpProfile:null}:{id:0,salesOrg:null,dc:{value:null,options:[]},plant:{value:null,options:[]},sloc:{value:null,options:[]},warehouse:{value:null,options:[]},mrpProfile:null},[ve,R]=i.useState([Je]),[H,ce]=i.useState([]),[x,Ge]=i.useState({id:1,plant:{value:null,options:[]},salesOrg:null,dc:{value:null,options:[]},sloc:{value:null,options:[]},mrpProfile:null,warehouse:{value:null,options:[]}}),[ue,We]=i.useState(null),[K,ze]=i.useState(!1),[ut,ps]=i.useState({}),[dn,wn]=i.useState("success"),an=(gl=w==null?void 0:w[Le])==null?void 0:gl.payloadData,{getDtCall:Vn,dtData:Jt}=Vi(),[un,jn]=i.useState(!1),[Cs,Rn]=i.useState([]),[ks,fn]=i.useState(""),[Os,xs]=i.useState([]),{getDynamicWorkflowDT:gn}=wi(),[D,hn]=i.useState(!1),[pe,En]=i.useState(!1),[Qs,fe]=i.useState(!1),[ie,Xe]=i.useState(""),[Rt,cs]=i.useState(!1),[me,Ke]=i.useState({"Sales Organization":!1,"Distribution Channel":{},Plant:{},"Storage Location":{},warehouse:{},"Mrp Profile":!1}),[bs,At]=i.useState(!1),[Ws,en]=i.useState(0),{fetchTabSpecificData:qs}=$i(),Sn=i.useRef(null),{getContryBasedOnPlant:It}=qi({doAjax:he,customError:re,fetchDataAndDispatch:ys,destination_MaterialMgmt:Ce}),{t:te}=Ll(),{checkValidation:yt}=Pi(w,Ze,B),{extendFilteredButtons:nl,showWfLevels:ll}=uc(C,He,rn,Mi),Gl=oi(nl,[On.HANDLE_SUBMIT_FOR_APPROVAL,On.HANDLE_SAP_SYNDICATION,On.HANDLE_SUBMIT_FOR_REVIEW,On.HANDLE_SUBMIT]),ol=s=>{!s||!Array.isArray(s)||s.forEach(l=>{var a,E,S,O,M,G,b,Z,ge,P,Se,Ye,Re,Ct,St,es;if((E=(a=l.plant)==null?void 0:a.value)!=null&&E.code){if(qs((O=(S=l.plant)==null?void 0:S.value)==null?void 0:O.code,m.PLANT),(M=l.salesOrg)!=null&&M.code||(b=(G=l.dc)==null?void 0:G.value)!=null&&b.code){const ds=`${((Z=l.salesOrg)==null?void 0:Z.code)||""}-${((P=(ge=l.dc)==null?void 0:ge.value)==null?void 0:P.code)||""}`;qs(ds,m.SALES)}(Ye=(Se=l.warehouse)==null?void 0:Se.value)!=null&&Ye.code&&qs((Ct=(Re=l.warehouse)==null?void 0:Re.value)==null?void 0:Ct.code,m.WAREHOUSE),It((es=(St=l.plant)==null?void 0:St.value)==null?void 0:es.code)}})};i.useEffect(()=>{const l=(ve==null?void 0:ve.map(a=>Ic(a))).every(a=>a===!0);cs(l)},[ve]);const Ns=s=>{if(!s||!Array.isArray(s))return[];let l=(F==null?void 0:F.Region)===jt.EUR?s==null?void 0:s.filter(a=>a!==m.WAREHOUSE&&a!==m.WORKSCHEDULING&&a!==m.WORK_SCHEDULING):[...s];return l.sort((a,E)=>a===m.BASIC_DATA?-1:E===m.BASIC_DATA?1:0),l};i.useEffect(()=>{var s,l,a,E;if(Jt&&((s=Jt==null?void 0:Jt.customParam)==null?void 0:s.dt)===Al.MDG_ORG_ELEMENT_DEFAULT_VALUE){const S=ai((E=(a=(l=Jt==null?void 0:Jt.data)==null?void 0:l.result)==null?void 0:a[0])==null?void 0:E.MDG_ORG_ELEMENT_DEFAULT_VALUE_ACTION_TYPE);_(ui({data:S}))}},[Jt]);const Be=async()=>{var s,l;try{const a=await gn(J,F==null?void 0:F.Region,"",(l=(s=w[Le])==null?void 0:s.Tochildrequestheaderdata)==null?void 0:l.MaterialGroupType,C==null?void 0:C.ATTRIBUTE_3,"v4","MDG_MAT_DYNAMIC_WF_DT",Cn.MAT);xs(a)}catch(a){re(a)}};i.useEffect(()=>{J&&(F!=null&&F.Region)&&Le&&(C!=null&&C.ATTRIBUTE_3)&&Be()},[J,F==null?void 0:F.Region,Le,C==null?void 0:C.ATTRIBUTE_3]),i.useEffect(()=>{var s,l,a,E,S,O,M,G;(E=(a=(l=(s=w[Le])==null?void 0:s.payloadData)==null?void 0:l[m.CLASSIFICATION])==null?void 0:a.basic)!=null&&E.Classtype&&fi((G=(M=(O=(S=w[Le])==null?void 0:S.payloadData)==null?void 0:O[m.CLASSIFICATION])==null?void 0:M.basic)==null?void 0:G.Classtype,_)},[Le]),i.useEffect(()=>{var s,l,a,E,S,O,M,G;if(De(Q),Ss((Q==null?void 0:Q.length)>0),(Q==null?void 0:Q.length)>0){let b=null;Le&&(b=Q.find(Se=>Se.id===Le.toString()||Se.id===Le)),!b&&ie&&(b=Q.find(Se=>Se.materialNumber===ie)),b||(b=Q[0]),kt(b==null?void 0:b.id),mt(((s=b==null?void 0:b.materialType)==null?void 0:s.code)||(b==null?void 0:b.materialType)),ye(0),Xe(b==null?void 0:b.materialNumber),os(((a=(l=b==null?void 0:b.views)==null?void 0:l.filter(Se=>I(Se)))==null?void 0:a[0])||m.BASIC_DATA),ne((E=b==null?void 0:b.views)!=null&&E.length?Ns(b==null?void 0:b.views):Ns([X]));const Z=ri(w),ge=ci(Z);let P=JSON.parse(JSON.stringify(ge));_(di(P)),_(qn({keyName:"selectedMaterialID",data:b==null?void 0:b.id})),(O=(S=w==null?void 0:w[b==null?void 0:b.id])==null?void 0:S.Tochildrequestheaderdata)!=null&&O.ChildRequestId&&_(qn({keyName:"childRequestId",data:(G=(M=w==null?void 0:w[b==null?void 0:b.id])==null?void 0:M.Tochildrequestheaderdata)==null?void 0:G.ChildRequestId}))}},[Q,z]),i.useEffect(()=>{(r==null?void 0:r.length)===0&&V(!1)},[r]),i.useEffect(()=>{Vs(Q)&&(Q!=null&&Q.length)||Ie||ae?(U.setCompleted([!0,!0]),U==null||U.setIsAttachmentTabEnabled(!0)):(U.setCompleted([!0,!1]),U==null||U.setIsAttachmentTabEnabled(!1))},[Q]),i.useEffect(()=>{w!=null&&w.isAnyMandatoryFieldEmpty?(U==null||U.setIsAttachmentTabEnabled(!1),V(!0)):V(!1)},[w==null?void 0:w.isAnyMandatoryFieldEmpty]),i.useEffect(()=>{["Sales Organization","Mrp Profile"].forEach(wl)},[]),i.useEffect(()=>{if(ae){const s=le==null?void 0:le.orgData;(s==null?void 0:s.length)>0&&s.some(l=>{var a,E,S,O,M;return((E=(a=l.plant)==null?void 0:a.value)==null?void 0:E.code)&&(((S=l.salesOrg)==null?void 0:S.code)||((M=(O=l.dc)==null?void 0:O.value)==null?void 0:M.code))})&&ol(s)}},[le==null?void 0:le.orgData]),i.useEffect(()=>{var S,O,M,G;const s=B==null?void 0:B.includes((S=m)==null?void 0:S.SALES),l=B==null?void 0:B.includes((O=m)==null?void 0:O.SALES_PLANT),a=B==null?void 0:B.includes((M=m)==null?void 0:M.STORAGE),E=B==null?void 0:B.includes((G=m)==null?void 0:G.STORAGE_PLANT);s&&!l&&ne(b=>{var P,Se;const Z=[...b],ge=Z.indexOf((P=m)==null?void 0:P.SALES);return Z.splice(ge+1,0,(Se=m)==null?void 0:Se.SALES_PLANT),Z}),a&&!E&&ne(b=>{var P,Se;const Z=[...b],ge=Z.indexOf((P=m)==null?void 0:P.STORAGE);return Z.splice(ge+1,0,(Se=m)==null?void 0:Se.STORAGE_PLANT),Z})},[B]);const Ul=()=>{In()},Pn=(s="",l=!1)=>{var O,M,G,b;const a={materialNo:s??"",top:500,skip:l?0:Xt,salesOrg:((M=(O=Wt==null?void 0:Wt.uniqueSalesOrgList)==null?void 0:O.map(Z=>Z.code))==null?void 0:M.join("$^$"))||""},E=Z=>{(Z==null?void 0:Z.statusCode)===Qe.STATUS_200&&(v(l?Z==null?void 0:Z.body:ge=>[...ge,...Z==null?void 0:Z.body]),vs(!1))},S=()=>{vs(!1)};vs(!0),he(`/${Ce}${(b=(G=gt)==null?void 0:G.DATA)==null?void 0:b.GET_SEARCH_PARAMS_MATERIAL_NO}`,"post",E,S,a)},Nt=!$n.includes(U==null?void 0:U.requestStatus);function il(s){const l=E=>{var S;if((E==null?void 0:E.statusCode)===Qe.STATUS_200){let O=(S=E==null?void 0:E.body)==null?void 0:S.filter(M=>!go.includes(M));O=O==null?void 0:O.map(M=>M==="Storage"?m.STORAGE:M),(F==null?void 0:F.Region)===jt.EUR&&(O=O==null?void 0:O.filter(M=>M!==m.WAREHOUSE&&M!==m.WORK_SCHEDULING&&M!==m.WORKSCHEDULING)),As(O)}},a=E=>{re(E)};he(`/${Ce}/data/getViewForMaterialType?materialType=${s}`,"get",l,a)}i.useEffect(()=>{Pn()},[]);const rl=((hl=ls==null?void 0:ls[1])==null?void 0:hl.External)==="X",cl=ls==null?void 0:ls.some(s=>s.ExtNAwock==="X");function mt(s){var S;const l=(F==null?void 0:F.Region)||jt.US,a=Ze.some(O=>O[l]&&O[l][s]),E=s!==Sn.current;if(!a&&s)lt(s,l);else if(!s)_(Fn({}));else{const O=Ze==null?void 0:Ze.find(M=>(M==null?void 0:M[l])&&(M==null?void 0:M[l][s]));O&&_(Fn((S=O[l][s])==null?void 0:S.allfields))}s&&E&&tt(s),Sn.current=s}const Bt=s=>{const{id:l,field:a,value:E}=s,S=r.map(O=>O.id===l?{...O,[a]:E}:O);Et({...Tt,[a]:E}),a===Gn.MATERIALTYPE&&(il(E),ne([X]),gi([Je]),_(js({materialID:l,keyName:"views",data:[X]})),_(js({materialID:l,keyName:"orgData",data:""})),mt(E==null?void 0:E.code)),De(S),_(js({materialID:l,keyName:a,data:E}))},Tn=s=>{var l,a,E,S,O,M,G,b,Z,ge,P,Se;kt(s.row.id),ps(s.row),Xe(s.row.materialNumber),As((l=s==null?void 0:s.row)==null?void 0:l.views),mt(((E=(a=s==null?void 0:s.row)==null?void 0:a.materialType)==null?void 0:E.code)||((S=s.row)==null?void 0:S.materialType)),ne((O=s==null?void 0:s.row)!=null&&O.views?(M=s.row)==null?void 0:M.views:[X]),R((b=(G=s==null?void 0:s.row)==null?void 0:G.orgData)!=null&&b.length?(Z=s.row)==null?void 0:Z.orgData:[Je]),ye(0),os(((Se=(P=(ge=s.row)==null?void 0:ge.views)==null?void 0:P.filter(Ye=>I(Ye)))==null?void 0:Se[0])||m.BASIC_DATA)},$l=()=>{ee(!0)},In=()=>{ee(!1)},Jn=(s,l)=>{l==="backdropClick"||l==="escapeKeyDown"||is(!1)},dl=Us+10,kl=()=>ne(Ns(Ks)),Wl=s=>{if(ze(!1),s!=null&&s.length){let l=[...r];s==null||s.forEach(a=>{var M,G,b;const E=a==null?void 0:a.Material;let S={...a},O=(M=w==null?void 0:w[a.id])!=null&&M.payloadData?JSON.parse(JSON.stringify((G=w==null?void 0:w[a.id])==null?void 0:G.payloadData)):"";S.id=E,S.globalMaterialDescription="",S.materialNumber="",S.included=!0,S.lineNumber=dl,S.industrySector=a==null?void 0:a.IndSector,S.materialType=a==null?void 0:a.MatlType,S.materialNumber=a==null?void 0:a.Material,S.globalMaterialDescription=a==null?void 0:a.MaterialDescrption,S.views=a!=null&&a.Views?(b=a==null?void 0:a.Views.split(","))==null?void 0:b.map(Z=>Z.trim()==="Storage"?m.STORAGE:Z.trim()):[X],S.validated=pl.default,l.push(S),_(Cl({materialID:E,data:S,payloadData:O}))}),ht(a=>[...a,...l.map(E=>({material:E==null?void 0:E.Material,views:E==null?void 0:E.views}))]),De(l),_(bt(l)),Yt(cn+1),Zs(dl),Ss(!0),Kt(!0)}},al=[{field:"included",headerName:"Included",flex:.5,align:"center",headerAlign:"center",renderCell:s=>{var l;return s!=null&&s.row?n(Nl,{checked:(l=s==null?void 0:s.row)==null?void 0:l.included,disabled:Nt,onChange:a=>{var E;(E=s==null?void 0:s.row)!=null&&E.id&&Bt({id:s.row.id,field:"included",value:a.target.checked})}}):null}},{field:"lineNumber",headerName:"Line Number",flex:.6,editable:J==="Create",align:"center",headerAlign:"center",renderCell:s=>{const a=((Q==null?void 0:Q.findIndex(E=>{var S;return(E==null?void 0:E.id)===((S=s==null?void 0:s.row)==null?void 0:S.id)}))+1)*10;return n("div",{children:a})}},{field:"industrySector",headerName:"Industry Sector",flex:1,align:"center",headerAlign:"center",renderCell:s=>{var l,a,E,S,O;return n(ft,{options:(_e==null?void 0:_e.IndSector)||[],value:(l=s==null?void 0:s.row)==null?void 0:l.industrySector,onChange:M=>Bt({id:s.row.id,field:"industrySector",value:M}),placeholder:"Select Industry Sector",minWidth:"90%",disabled:!0,listWidth:232,title:`${((E=(a=s.row)==null?void 0:a.industrySector)==null?void 0:E.code)||""} - ${((O=(S=s.row)==null?void 0:S.industrySector)==null?void 0:O.desc)||""}`})}},{field:"materialType",headerName:"Material Type",flex:1,align:"center",headerAlign:"center",renderCell:s=>{var l,a,E,S,O;return n(ft,{options:ml||[],value:(l=s==null?void 0:s.row)==null?void 0:l.materialType,onChange:M=>Bt({id:s.row.id,field:"materialType",value:M}),placeholder:"Select Material Type",disabled:!0,minWidth:"90%",listWidth:232,title:`${((E=(a=s.row)==null?void 0:a.materialType)==null?void 0:E.code)||""} - ${((O=(S=s.row)==null?void 0:S.materialType)==null?void 0:O.desc)||""}`})}},{field:"materialNumber",headerName:"Material Number",flex:1,editable:!(!rl&&!cl),align:"center",headerAlign:"center",renderHeader:()=>N("span",{children:["Material Number",n("span",{style:{color:"red"},children:"*"})]}),renderCell:s=>{var l;return n(on,{fullWidth:!0,placeholder:"Enter Material Number",variant:"outlined",size:"small",name:"material number",value:(l=s==null?void 0:s.row)==null?void 0:l.materialNumber,sx:{"& .MuiInputBase-root.Mui-disabled":{"& > input":{WebkitTextFillColor:ke.black.dark,color:ke.black.dark}}},onChange:(a,E)=>Bt({id:s.row.id,field:"materialNumber",value:E}),disabled:!rl&&!cl})}},{field:"globalMaterialDescription",flex:1,headerName:"Material Description",renderHeader:()=>N("span",{children:["Material Description",n("span",{style:{color:"red"},children:"*"})]}),renderCell:s=>{var l;return n(on,{fullWidth:!0,placeholder:"Enter Material Description",variant:"outlined",disabled:!0,size:"small",name:"material description",sx:{"& .MuiInputBase-root.Mui-disabled":{"& > input":{WebkitTextFillColor:ke.black.dark,color:ke.black.dark}}},onChange:(a,E)=>Bt({id:s.row.id,field:"globalMaterialDescription",value:E}),value:(l=s==null?void 0:s.row)==null?void 0:l.globalMaterialDescription})},align:"center",headerAlign:"center",editable:!0},{field:"views",headerName:"",flex:1.8,align:"center",headerAlign:"center",renderCell:s=>N(bl,{direction:"row",spacing:0,alignItems:"center",children:[n(je,{variant:"contained",size:"small",sx:{minWidth:80},onClick:()=>{var l,a;il(s.row.materialType),Ys(!0),Gs(s.row.id),ps(s.row),ne((l=s==null?void 0:s.row)!=null&&l.Views?(a=s==null?void 0:s.row)==null?void 0:a.Views:[X])},children:"Views"}),n(Xo,{color:"disabled",fontSize:"small",sx:{mx:.5}}),n(je,{variant:"contained",size:"small",sx:{minWidth:100},onClick:()=>{var l,a,E,S;is(!0),Gs(s.row.id),R((a=(l=s==null?void 0:s.row)==null?void 0:l.orgData)!=null&&a.length?(E=s.row)==null?void 0:E.orgData:[Je]),ps(s.row),ul((S=s==null?void 0:s.row)==null?void 0:S.materialNumber,fs.NOT_EXTENDED),En(!1)},children:"ORG Data"}),n(Xo,{color:"disabled",fontSize:"small",sx:{mx:.5}}),n(gs,{title:"Click after changing Views or ORG Data",children:n(Ft,{onClick:()=>{var l,a;is(!0),Gs(s.row.id),En(!0),ps(s.row),ul((l=s==null?void 0:s.row)==null?void 0:l.materialNumber,fs.EXTENDED),Ge(H.find(E=>{var S;return E.id===((S=s.row)==null?void 0:S.id)})||{id:(a=s.row)==null?void 0:a.id,plant:null,salesOrg:null,dc:{value:null,options:[]},sloc:{value:null,options:[]},mrpProfile:null,warehouse:null})},disabled:Nt,color:"primary",size:"small",children:n(ro,{})})})]})},{field:"action",headerName:"Action",flex:.9,align:"center",headerAlign:"center",renderCell:s=>{let l=hi(s==null?void 0:s.row);return N(bl,{direction:"row",alignItems:"center",sx:{marginLeft:"0.5rem",magrinRight:"0.5rem"},spacing:.5,children:[n(gs,{title:l==="success"?"Validated Successfully":l==="error"?"Validation Failed":"Click to Validate",children:n(Ft,{onClick:async E=>{var P,Se,Ye,Re,Ct,St,es,ds;if(E.stopPropagation(),((Se=(P=s==null?void 0:s.row)==null?void 0:P.views)==null?void 0:Se.length)>1&&(!((Ye=s==null?void 0:s.row)!=null&&Ye.orgData)||((Ct=(Re=s==null?void 0:s.row)==null?void 0:Re.orgData)==null?void 0:Ct.length)===0)){Fe(te((es=(St=xi)==null?void 0:St.FILES)==null?void 0:es.MISSING_ORG_DATA),"error",1e4);return}const{missingFields:S,viewType:O,isValid:M,plant:G=[]}=yt(s.row.id,((ds=s==null?void 0:s.row)==null?void 0:ds.orgData)||[]);if(Rn(G),We(O),rs(S),ye(O?B==null?void 0:B.indexOf(O):0),os(O||m.BASIC_DATA),S)if(typeof S=="object"&&!Array.isArray(S)){const as=Object.entries(S).map(([Xn,so])=>`Combination ${Xn}: ${so.join(", ")}`);Fe(`${te("Line No")} ${s.row.lineNumber} : ${te("Please fill all the Mandatory fields in")} ${O||""}: ${as.join(" | ")}`,"error",1e4)}else Fe(`${te("Line No")} ${s.row.lineNumber} : ${te("Please fill all the Mandatory fields in")} ${O||""}: ${S.join(", ")}`,"error",1e4);else l==="success"&&Fe("Validation successful","success");const b=r.map(as=>as.id===s.row.id?{...as,validated:M}:as);b.every(as=>as.validated===!0)&&_(zo(!1)),_(bt(b));const ge=Vs(b);V(!ge)},color:l==="success"?"success":l==="error"?"error":"default",disabled:Nt&&Ie,children:l==="error"?n(Ei,{}):n(Fi,{})})}),!ae&&n(gs,{title:"Delete Row",children:n(Ft,{onClick:()=>{De(r.filter(E=>E.id!==s.row.id)),_(Li(s.row.id)),_(bt(r.filter(E=>E.id!==s.row.id))),r!=null&&r.length||V(!1)},color:"error",children:n(el,{})})})]})}}],ql=async()=>{let s=[...r],l=!0;return ct(!0),mn(io.VALIDATING_MATS),new Promise(async(a,E)=>{for(let O=0;O<(r==null?void 0:r.length);O++){const M=r[O],{missingFields:G,viewType:b,isValid:Z,plant:ge=[]}=yt(M.id,(M==null?void 0:M.orgData)||[],!1,!1,!1);if(Rn(ge),We(b),ye(b?B==null?void 0:B.indexOf(b):0),os(b||m.BASIC_DATA),rs(G),!Z){if(l=!1,s=s.map(P=>P.id===M.id?{...P,validated:!1}:P),_(bt(s)),G)if(kt(M.id),Xe(M.materialNumber),typeof G=="object"&&!Array.isArray(G)){const P=Object.entries(G).map(([Se,Ye])=>`Combination ${Se}: ${Ye.join(", ")}`);Fe(`Line No ${M.lineNumber} : Please fill all the Mandatory fields in ${b||""}: ${P.join(" | ")}`,"error",1e4)}else Fe(`Line No ${M.lineNumber} : Please fill all the Mandatory fields in ${b||""}: ${G.join(", ")}`,"error",1e4);break}}l?a(!0):E(),ct(!1);const S=Vs(s);V(!S),Kt(!S),l&&(Fe("Validation successful for all materials.","success"),_(zo(!1)))})},ul=(s,l)=>{Ke(S=>({...S,"Sales Organization":!0}));const a=S=>{if((S==null?void 0:S.statusCode)===Qe.STATUS_200){let O;l===fs.NOT_EXTENDED?O=ts(S.body):O=S.body.length>0?ts(S.body):[],Me(M=>({...M,"Sales Organization":O}))}Ke(O=>({...O,"Sales Organization":!1}))},E=()=>{Ke(S=>({...S,"Sales Organization":!1}))};he(`/${Ce}/data/${l===fs.NOT_EXTENDED?"getSalesOrgNotExtended":"getSalesOrgExtended"}?materialNo=${s}&region=${F==null?void 0:F.Region}`,"get",a,E)},Hl=(s,l,a,E)=>{Ke(G=>({...G,Plant:{...G.Plant,[E]:!0}}));const S=G=>{if((G==null?void 0:G.statusCode)===Qe.STATUS_200){let b;l===fs.NOT_EXTENDED?b=ts(G.body):b=G.body.length>0?ts(G.body||[]):[],Me(Z=>({...Z,Plant:b}))}Ke(b=>({...b,Plant:{...b.Plant,[E]:!1}}))},O=()=>{Ke(G=>({...G,Plant:{...G.Plant,[E]:!1}}))},M=a?`&salesOrg=${a.code}`:"";he(`/${Ce}/data/${l===fs.NOT_EXTENDED?"getPlantNotExtended":"getPlantExtended"}?materialNo=${s}&region=${F==null?void 0:F.Region}${M}`,"get",S,O)},Fl=(s,l,a,E)=>{Ke(G=>({...G,warehouse:{...G.warehouse,[E]:!0}}));const S=G=>{if((G==null?void 0:G.statusCode)===Qe.STATUS_200){let b;l===fs.NOT_EXTENDED?b=ts(G.body):b=G.body.length>0?ts(G.body||[]):[],Me(Z=>({...Z,warehouse:b}))}Ke(b=>({...b,warehouse:{...b.warehouse,[E]:!1}}))},O=()=>{Ke(G=>({...G,warehouse:{...G.warehouse,[E]:!1}}))},M=a?`&plant=${a.code}`:"";he(`/${Ce}/data/${l===fs.NOT_EXTENDED?"getWarehouseNotExtended":"getWarehouseExtended"}?materialNo=${s}&region=${F==null?void 0:F.Region}${M}`,"get",S,O)},Bl=(s,l)=>{var a;ye(B.filter(E=>I(E)).indexOf(s.target.innerText)!==-1?B.filter(E=>I(E)).indexOf(s.target.innerText):l),os(((a=s==null?void 0:s.target)==null?void 0:a.id)==="AdditionalKey"?"Additional Data":s.target.innerText)},wl=s=>{Ke(S=>({...S,[s]:!0}));const l={"Sales Organization":"/getSalesOrg","Mrp Profile":"/getMRPProfile"},a=S=>{if((S==null?void 0:S.statusCode)===Qe.STATUS_200){const O=ts(S.body);Me(M=>({...M,[s]:O}))}Ke(O=>({...O,[s]:!1}))},E=S=>{console.error(S),Ke(O=>({...O,[s]:!1}))};he(`/${Ce}/data${l[s]}`,"get",a,E)},fl=s=>{Di(s,B,an,Le,ve,_,Ps)},Vl=(s,l,a)=>(E,S)=>{var P,Se,Ye;let O={},M="",G="";a==="Purchasing"||a==="Costing"?(O={materialNo:l==null?void 0:l.Material,plant:l==null?void 0:l.Plant},G=l==null?void 0:l.Plant,M=`/${Ce}/data/displayLimitedPlantData`):a==="Accounting"?(O={materialNo:l==null?void 0:l.Material,valArea:l==null?void 0:l.ValArea},G=l==null?void 0:l.ValArea,M=`/${Ce}/data/displayLimitedAccountingData`):a==="Sales"&&(O={materialNo:l==null?void 0:l.Material,salesOrg:l==null?void 0:l.SalesOrg,distChnl:l==null?void 0:l.DistrChan},G=`${l==null?void 0:l.SalesOrg}-${l==null?void 0:l.DistrChan}`,M=`/${Ce}/data/displayLimitedSalesData`);const b=Re=>{var Ct,St,es;(Re==null?void 0:Re.statusCode)===Qe.STATUS_200&&(a==="Purchasing"||a==="Costing"?_(Ps({materialID:Le,viewID:a,itemID:l==null?void 0:l.Plant,data:(Ct=Re==null?void 0:Re.body)==null?void 0:Ct.SpecificPlantDataViewDto[0]})):a==="Accounting"?_(Ps({materialID:Le,viewID:a,itemID:l==null?void 0:l.ValArea,data:(St=Re==null?void 0:Re.body)==null?void 0:St.SpecificAccountingDataViewDto[0]})):a==="Sales"&&_(Ps({materialID:Le,viewID:a,itemID:`${l==null?void 0:l.SalesOrg}-${l==null?void 0:l.DistrChan}`,data:(es=Re==null?void 0:Re.body)==null?void 0:es.SpecificSalesDataViewDto[0]})))},Z=()=>{};!((Ye=(Se=(P=w==null?void 0:w[Le])==null?void 0:P.payloadData)==null?void 0:Se[a])!=null&&Ye[G])&&he(M,"post",b,Z,O),Ee(S?s:null)},jl=()=>z&&Pt&&(z[Pt]||Pt==="Additional Data")?Pt==="Additional Data"?[n(Bi,{disabled:Nt,materialID:Le,selectedMaterialNumber:ie})]:[n(Wi,{disabled:Nt,materialID:Le,basicData:Ts,setBasicData:Zt,dropDownData:Qt,allTabsData:z,basicDataTabDetails:z[Pt],activeViewTab:Pt,selectedViews:B,handleAccordionClick:Vl,missingValidationPlant:Cs,selectedMaterialNumber:ie,callGetCountryBasedonSalesOrg:Qs,mandatoryFailedView:ue,missingFields:$s})]:n(Is,{}),Pl=s=>{const l=s.target.value;rt({code:l,desc:""}),ss(0),Ne&&clearTimeout(Ne);const a=setTimeout(()=>{Pn(l,!0)},500);st(a)};i.useEffect(()=>{Xt>0&&Pn(Lt==null?void 0:Lt.code)},[Xt]);const Jl=(s,l,a)=>{var E;if(s==="Sales Organization"){tn(l,a);const S=(E=r==null?void 0:r.find(O=>O.id===$t))==null?void 0:E.materialNumber;Hl(S,pe?fs.EXTENDED:fs.NOT_EXTENDED,l,a)}},tn=(s,l,a="",E="")=>(Ke(S=>({...S,"Distribution Channel":{...S["Distribution Channel"],[l]:!0}})),new Promise((S,O)=>{var Z;const M=ge=>{if(ge.statusCode===Qe.STATUS_200){const P=ts(ge.body);let Se=JSON.parse(JSON.stringify(a||ve));pe?Ge(Ye=>({...Ye,salesOrg:s,dc:{value:null,options:(P==null?void 0:P.length)>0?P:[]}})):(Se[l].salesOrg=s,Se[l].dc.options=P,R(Se))}Ke(P=>({...P,"Distribution Channel":{...P["Distribution Channel"],[l]:!1}})),S(ge)},G=ge=>{Ke(P=>({...P,"Distribution Channel":{...P["Distribution Channel"],[l]:!1}})),O(ge)};let b=(Z=r==null?void 0:r.find(ge=>ge.id===$t))==null?void 0:Z.materialNumber;b&&he(`/${Ce}/data/${pe?"getDistributionChannelExtended":"getDistributionChannelNotExtended"}?materialNo=${b}&salesOrg=${s==null?void 0:s.code}`,"get",M,G)})),zl=(s,l)=>{var E;Xl(s,l);const a=(E=r==null?void 0:r.find(S=>S.id===$t))==null?void 0:E.materialNumber;Fl(a,pe?fs.EXTENDED:fs.NOT_EXTENDED,s,l)},Xl=(s,l,a="",E)=>{var Z;Ke(ge=>({...ge,"Storage Location":{...ge["Storage Location"],[l]:!0}}));const S=ge=>{if(Ke(P=>({...P,"Storage Location":{...P["Storage Location"],[l]:!1}})),ge.statusCode===Qe.STATUS_200){const P=ts(ge.body);let Se=JSON.parse(JSON.stringify(a||ve));pe?Ge(Ye=>({...Ye,plant:{value:s,options:[]},sloc:{value:null,options:(P==null?void 0:P.length)>0?P:[]}})):(Se[l].plant.value=s,Se[l].sloc.options=P,R(Se))}if(E){_(js({materialID:E==null?void 0:E.id,keyName:"orgData",data:rowOption}));let P=(r==null?void 0:r.length)||[JSON.parse(JSON.stringify(E))],Se=P.findIndex(Ye=>Ye.id===(E==null?void 0:E.id));P[Se].orgData=rowOption,_(bt(P))}},O=ge=>{console.error(ge),Ke(P=>({...P,"Storage Location":{...P["Storage Location"],[l]:!1}}))};let M=(Z=r.find(ge=>ge.id===$t))==null?void 0:Z.materialNumber;const G=ve[l],b=G!=null&&G.salesOrg?`&salesOrg=${G.salesOrg.code}`:"";M&&he(`/${Ce}/data/${pe?"getStorageLocationExtended":"getStorageLocationNotExtended"}?materialNo=${M}&region=${F==null?void 0:F.Region}&plant=${s==null?void 0:s.code}${b}`,"get",S,O)},Kl=(s,l)=>{let a=JSON.parse(JSON.stringify(ve));a[l].dc.value=s,R(a)},Mn=s=>{let l=JSON.parse(JSON.stringify(ve));l.splice(s,1),R(l)},Yl=(s,l)=>{let a=JSON.parse(JSON.stringify(ve));a[l].sloc.value=s,R(a)},Zl=(s,l)=>{let a=JSON.parse(JSON.stringify(ve));a[l].warehouse.value=s,R(a)},Ql=(s,l)=>{let a=JSON.parse(JSON.stringify(ve));a[l].mrpProfile=s,R(a)},Eo=()=>{let s=JSON.parse(JSON.stringify(ve));s.push({id:1,plant:{value:null,options:[]},salesOrg:null,dc:{value:null,options:[]},sloc:{value:null,options:[]},warehouse:{value:null,options:[]},mrpProfile:null}),R(s)},sn=(s,l,a,E)=>{var G,b,Z,ge,P,Se,Ye,Re,Ct,St,es;const S={material:ut==null?void 0:ut.materialNumber,wareHouseNumber:((b=(G=s==null?void 0:s.warehouse)==null?void 0:G.value)==null?void 0:b.code)??"",plant:((ge=(Z=s==null?void 0:s.plant)==null?void 0:Z.value)==null?void 0:ge.code)??"",salesOrg:((P=s==null?void 0:s.salesOrg)==null?void 0:P.code)??"",storageLocation:((Ye=(Se=s==null?void 0:s.sloc)==null?void 0:Se.value)==null?void 0:Ye.code)??"",distributionChannel:((Ct=(Re=s==null?void 0:s.dc)==null?void 0:Re.value)==null?void 0:Ct.code)??"",valArea:((es=(St=s==null?void 0:s.plant)==null?void 0:St.value)==null?void 0:es.code)??""},O=ds=>{const as=Nr(ds==null?void 0:ds.body,l,a,E,ut),Xn=J===$.EXTEND_WITH_UPLOAD||Ot===$.EXTEND_WITH_UPLOAD?mr(w,as):_r(w,as);_(Rr({data:Xn})),fe(!Qs)},M=ds=>{re(ds)};he(`/${Ce}${gt.DATA.COPY_FROM_MATERIAL_ORG_ELMS_ETEXTEND}`,"post",O,M,S)},eo=[{id:1,plant:{value:null,options:[]},salesOrg:null,dc:{value:null,options:[]},sloc:{value:null,options:[]},warehouse:{value:null,options:[]},mrpProfile:null}],qt=s=>{_(vi(s)),at(s)};i.useEffect(()=>{var s,l;(h==null?void 0:h.page)!==0&&(J===((s=$)==null?void 0:s.EXTEND_WITH_UPLOAD)||J===((l=$)==null?void 0:l.EXTEND))&&et(),at((h==null?void 0:h.page)||0)},[h==null?void 0:h.page]);const zn=()=>{hs(!q),Ue&&Es(!1)},to=()=>{Es(!Ue),q&&hs(!1)},An=pe?eo:ve;return N("div",{children:[n("div",{style:{padding:"0",width:"100%",margin:"0",marginTop:"20px"},children:N(Ve,{sx:{position:q?"fixed":"relative",top:q?0:"auto",left:q?0:"auto",right:q?0:"auto",bottom:q?0:"auto",width:q?"100vw":"100%",height:q?"100vh":"auto",zIndex:q?1004:void 0,backgroundColor:q?"white":"transparent",padding:q?"20px":"0",display:"flex",flexDirection:"column",boxShadow:q?"0px 0px 15px rgba(0, 0, 0, 0.2)":"none",transition:"all 0.3s ease",borderRadius:"8px",border:"1px solid #e0e0e0"},children:[N(Ve,{sx:{display:"flex",justifyContent:"space-between",alignItems:"center",padding:"8px 16px",backgroundColor:"#f5f5f5",borderRadius:"8px 8px 0 0"},children:[n(Gt,{variant:"h6",children:"Details"}),N(Ve,{sx:{display:"flex",alignItems:"center",gap:1},children:[n(je,{variant:"contained",color:"primary",onClick:()=>{hn(!0)},disabled:zt||Nt||ae&&(U==null?void 0:U.requestStatus)!==Ls.DRAFT,children:"+ Add"}),n(gs,{title:q?"Exit Zoom":"Zoom In",sx:{zIndex:"1009"},children:n(Ft,{onClick:zn,color:"primary",sx:{backgroundColor:"rgba(0, 0, 0, 0.05)","&:hover":{backgroundColor:"rgba(0, 0, 0, 0.1)"}},children:q?n(xl,{}):n(Dl,{})})})]})]}),r&&(r==null?void 0:r.length)>0?n("div",{style:{width:"100%",height:"100%",overflowX:"auto"},children:n("div",{style:{height:"100%"},children:n(_l,{rows:r,columns:al,pageSize:50,page:ot,rowsPerPageOptions:[50],rowCount:(h==null?void 0:h.totalElements)||0,onRowClick:Tn,onCellEditCommit:Bt,onPageChange:s=>qt(s),disableSelectionOnClick:!0,getRowClassName:s=>s.id===Le?"selected-row":"",style:{border:"1px solid #ccc",borderRadius:"8px",width:"100%",height:q?"calc(100vh - 150px)":`${Math.min(r.length*50+130,300)}px`,overflow:"auto"},sx:{"& .selected-row":{backgroundColor:"rgb(234 233 255)"}}})})}):n("div",{style:{width:"100%",height:"100%",overflowX:"auto"},children:n("div",{style:{height:"100%"},children:n(_l,{rows:r,columns:al,pageSize:5,rowsPerPageOptions:[5],page:ot,onRowClick:Tn,onCellEditCommit:Bt,onPageChange:s=>qt(s),disableSelectionOnClick:!0,getRowClassName:s=>s.id===Le?"selected-row":"",style:{border:"1px solid #ccc",borderRadius:"8px",width:"100%",height:q?"calc(100vh - 150px)":`${Math.min(r.length*50+130,300)}px`,overflow:"auto"},sx:{"& .selected-row":{backgroundColor:"rgb(234 233 255)"}}})})})]})}),Le&&Ut&&(r==null?void 0:r.length)>0&&Oe.length>0&&z&&((El=Object.getOwnPropertyNames(z))==null?void 0:El.length)>0&&N(Ve,{sx:{position:Ue?"fixed":"relative",top:Ue?0:"auto",left:Ue?0:"auto",right:Ue?0:"auto",bottom:Ue?0:"auto",width:Ue?"100vw":"100%",height:Ue?"100vh":"auto",zIndex:Ue?1004:void 0,backgroundColor:Ue?"white":"transparent",padding:Ue?"20px":"0",marginTop:"20px",display:"flex",flexDirection:"column",boxShadow:Ue?"0px 0px 15px rgba(0, 0, 0, 0.2)":"none",transition:"all 0.3s ease",borderRadius:"8px",border:"1px solid #e0e0e0"},children:[N(Ve,{sx:{display:"flex",justifyContent:"space-between",alignItems:"center",padding:"8px 16px",borderRadius:"8px 8px 0 0"},children:[n(Gt,{variant:"h6",children:"View Details"}),n(gs,{title:Ue?"Exit Zoom":"Zoom In",sx:{zIndex:"1009"},children:n(Ft,{onClick:to,color:"primary",sx:{backgroundColor:"rgba(0, 0, 0, 0.05)","&:hover":{backgroundColor:"rgba(0, 0, 0, 0.1)"}},children:Ue?n(xl,{}):n(Dl,{})})})]}),N(Ve,{sx:{flexGrow:1,display:"flex",flexDirection:"column"},children:[N(Si,{sx:{top:0,position:"sticky",zIndex:998,backgroundColor:ke.background.container,borderBottom:`1px solid ${ke.border.light}`,"& .MuiTab-root":{minHeight:"48px",textTransform:"none",fontSize:"14px",fontWeight:600,color:ke.black.graphite,"&.Mui-selected":{color:ke.primary.main,fontWeight:700},"&:hover":{color:ke.primary.main,opacity:.8}},"& .MuiTabs-indicator":{backgroundColor:ke.primary.main,height:"3px"}},value:we,onChange:Bl,className:dt.customTabs,"aria-label":"material tabs",children:[B&&ve.length>0&&(B==null?void 0:B.length)>0?B==null?void 0:B.map((s,l)=>I(s)&&n(Rl,{label:s},l)):n(Is,{}),n(Rl,{label:"Additional Data",id:"AdditionalKey"},"Additional data")]}),(r==null?void 0:r.length)>0&&n(Ve,{sx:{padding:2,marginTop:2},children:jl()}),n(Hi,{activeTab:we,submitForApprovalDisabled:_n,filteredButtons:Gl,validateMaterials:ql,workFlowLevels:Os,showWfLevels:ll,childRequestHeaderData:(pt=w==null?void 0:w[Le])==null?void 0:pt.Tochildrequestheaderdata})]})]}),n("div",{}),n(fo,{dialogState:Y,openReusableDialog:$l,closeReusableDialog:In,dialogTitle:"Warning",dialogMessage:f,showCancelButton:!1,handleOk:Ul,handleDialogConfirm:In,dialogOkText:"OK",dialogSeverity:"danger"}),vt&&n(kn,{fullWidth:!0,maxWidth:!1,open:!0,onClose:Jn,sx:{display:"flex",justifyContent:"center"},disableEscapeKeyDown:!0,children:N(Ve,{sx:{width:"600px !important"},children:[N(Wn,{sx:{backgroundColor:"#EAE9FF",marginBottom:".5rem"},children:[n(tl,{style:{height:"20px",width:"20px",marginBottom:"-5px"}}),n("span",{children:"Select Views"})]}),n(Js,{sx:{paddingBottom:".5rem"},children:N(Ve,{display:"flex",alignItems:"center",sx:{flex:1,padding:"22px 0px",gap:"5px"},children:[n(Ol,{size:"small",multiple:!0,fullWidth:!0,options:Ks,disabled:Nt,disableCloseOnSelect:!0,value:B==null?void 0:B.filter(s=>!Ti.includes(s)),onChange:(s,l)=>{ne([X,...l.filter(a=>a!==X)]),Bt({id:$t,field:"views",value:l})},getOptionDisabled:s=>s===X,renderOption:(s,l,{selected:a})=>{var O;const E=j.find(M=>(M==null?void 0:M.material)===(ut==null?void 0:ut.materialNumber)),S=((O=E==null?void 0:E.views)==null?void 0:O.includes(l))||!1;return N("li",{...s,children:[n(Nl,{checked:a||l=="Basic Data",sx:{marginRight:1}}),l," ",S?"(extended)":""]})},renderTags:(s,l)=>s.map((a,E)=>{var b;const{key:S,...O}=l({index:E}),M=j.find(Z=>(Z==null?void 0:Z.material)===(ut==null?void 0:ut.materialNumber)),G=((b=M==null?void 0:M.views)==null?void 0:b.includes(a))||!1;return n(Ai,{label:`${a} ${G?"(extended)":""}`,...O,disabled:a===X},S)}),renderInput:s=>n(on,{...s,label:"Select Views"})}),n(je,{variant:"contained",disabled:Nt,size:"small",onClick:()=>kl(),children:"Select all"})]})}),N(zs,{children:[n(je,{onClick:()=>{Ys(!1)},color:"error",variant:"outlined",sx:{height:36,minWidth:"3.5rem",textTransform:"none",borderColor:"#cc3300",fontWeight:500},children:"Cancel"}),n(je,{onClick:()=>{Ys(!1),Bt({id:$t,field:"views",value:B})},variant:"contained",children:"OK"})]})]})}),Ms&&N(kn,{fullWidth:!0,maxWidth:"xl",open:!0,onClose:Jn,disableEscapeKeyDown:!0,sx:{"& .MuiDialog-paper":{padding:2,borderRadius:2}},children:[N(Wn,{sx:{display:"flex",alignItems:"center",gap:1,backgroundColor:"#EAE9FF"},children:[n(tl,{fontSize:"small"}),pe?n("span",{children:"Select org data for copy"}):n("span",{children:"Select org data to be extended"}),n(Ft,{onClick:Jn,sx:{position:"absolute",right:15},children:n(pi,{})})]}),n(Js,{sx:{padding:0},children:n(Ci,{component:Oi,children:N(bi,{children:[n(Ni,{children:N(Il,{children:[!pe&&n(qe,{align:"center",children:te("S NO.")}),N(qe,{align:"center",children:[te("Sales Org"),!pe&&n(Qn,{})]}),N(qe,{align:"center",children:[te("Distribution Channel"),!pe&&n(Qn,{})]}),N(qe,{align:"center",children:[te("Plant"),!pe&&n(Qn,{})]}),N(qe,{align:"center",children:[te("Storage Location"),!pe&&n(Qn,{})]}),(F==null?void 0:F.Region)!==jt.EUR&&N(qe,{align:"center",children:[te("Warehouse"),!pe&&n(Qn,{})]}),n(qe,{align:"center",children:te("MRP Profile")}),(ve==null?void 0:ve.length)>1&&!pe&&n(qe,{align:"center",children:"Action"})]})}),n(mi,{children:An==null?void 0:An.map((s,l)=>{var a,E,S,O,M,G,b,Z,ge,P,Se,Ye;return N(Il,{sx:{padding:"12px"},children:[!pe&&n(qe,{children:n(Gt,{variant:"body2",children:l+1})}),n(qe,{children:n(ft,{options:Qt["Sales Organization"],value:pe?x==null?void 0:x.salesOrg:s==null?void 0:s.salesOrg,onChange:Re=>Jl("Sales Organization",Re,l),placeholder:"Select Sales Org",disabled:Nt,isFieldError:!1,minWidth:165,isLoading:me["Sales Organization"]})}),n(qe,{children:n(ft,{options:pe?(E=x==null?void 0:x.dc)==null?void 0:E.options:(a=s.dc)==null?void 0:a.options,value:pe?(O=x==null?void 0:x.dc)==null?void 0:O.value:(S=s.dc)==null?void 0:S.value,onChange:Re=>pe?Ge(Ct=>{var St;return{...Ct,dc:{value:Re,options:(St=x==null?void 0:x.dc)==null?void 0:St.options}}}):Kl(Re,l),placeholder:"Select DC",disabled:Nt,isFieldError:!1,minWidth:165,isLoading:me["Distribution Channel"][l]})}),n(qe,{children:n(ft,{options:Qt.Plant||[],value:pe?(G=x==null?void 0:x.plant)==null?void 0:G.value:(M=s.plant)==null?void 0:M.value,onChange:Re=>zl(Re,l),placeholder:"Select Plant",disabled:Nt,isFieldError:!1,minWidth:165,isLoading:me.Plant[l]})}),n(qe,{children:n(ft,{options:pe?(Z=x==null?void 0:x.sloc)==null?void 0:Z.options:(b=s==null?void 0:s.sloc)==null?void 0:b.options,value:pe?(P=x==null?void 0:x.sloc)==null?void 0:P.value:(ge=s==null?void 0:s.sloc)==null?void 0:ge.value,onChange:Re=>pe?Ge(Ct=>{var St;return{...Ct,sloc:{value:Re,options:(St=x==null?void 0:x.sloc)==null?void 0:St.options}}}):Yl(Re,l),placeholder:"Select Sloc",disabled:Nt,isFieldError:!1,minWidth:165,isLoading:me["Storage Location"][l]})}),(F==null?void 0:F.Region)!==jt.EUR&&n(qe,{children:n(ft,{options:Qt.warehouse||[],value:pe?(Ye=x==null?void 0:x.warehouse)==null?void 0:Ye.value:(Se=s==null?void 0:s.warehouse)==null?void 0:Se.value,onChange:Re=>pe?Ge(Ct=>{var St;return{...Ct,warehouse:{value:Re,options:(St=x==null?void 0:x.warehouse)==null?void 0:St.options}}}):Zl(Re,l),placeholder:"Select Warehouse",disabled:Nt,isFieldError:!1,minWidth:165,isLoading:me.warehouse[l]})}),n(qe,{children:n(ft,{options:Qt["Mrp Profile"]||[],value:pe?x==null?void 0:x.mrpProfile:s.mrpProfile,onChange:Re=>pe?Ge(Ct=>({...Ct,mrpProfile:Re})):Ql(Re,l),placeholder:"Select MRP Profile",disabled:Nt,isFieldError:!1,minWidth:165,isLoading:me["Mrp Profile"]})}),ve.length>1&&N(qe,{align:"right",children:[n(Ft,{size:"small",color:"primary",disabled:Nt,onClick:()=>{At(!0),en({orgRowLength:ve.length,copyFor:l})},style:{display:l===0?"none":"inline-flex"},children:n(ro,{})}),n(Ft,{style:{display:l===0?"none":"inline-flex"},size:"small",color:"error",onClick:()=>Mn(l),children:n(el,{})})]})]},l)})})]})})}),N(zs,{sx:{justifyContent:"flex-end",gap:.5},children:[!pe&&n(je,{onClick:Eo,disabled:Nt||!Rt,variant:"contained",children:"+ Add"}),n(je,{onClick:()=>{if(is(!1),ve[0].plant&&(Bt({id:$t,field:"orgData",value:ve}),!pe)){ol(ve);const l=r==null?void 0:r.map(a=>(a==null?void 0:a.id)===$t?{...a,orgData:ve}:a);_(bt(l))}const s=ve.filter(l=>{var a,E;return(E=(a=l.plant)==null?void 0:a.value)==null?void 0:E.code}).map(l=>{var a,E;return(E=(a=l.plant)==null?void 0:a.value)==null?void 0:E.code});s.length>0&&fl(s),pe&&(ce(l=>{const a=l.findIndex(E=>E.id===x.id);return a!==-1?l.map((E,S)=>S===a?{...E,...x}:E):[...l,x]}),sn(x,ve,F,B))},variant:"contained",disabled:Nt||!Rt,children:"Apply"})]})]}),bs&&n(Ji,{open:bs,onClose:()=>At(!1),title:ao.COPY_ORG_DATA_VALES_HEADING,selectedMaterialPayload:an,lengthOfOrgRow:Ws,materialID:Le,orgRows:ve}),ks&&n(yl,{openSnackBar:un,alertMsg:ks,alertType:dn,handleSnackBarClose:()=>jn(!1)}),n(_c,{openSearchMat:D,materialOptions:Dt,handleMatInputChange:Pl,inputState:Lt,setOpenSearchMat:hn,dropDownData:Qt,AddCopiedMaterial:Wl}),n(Hn,{blurLoading:ns,loaderMessage:Nn}),n(co,{})]})},xd=()=>{var Le,kt,$s,rs,Wt,le,Je,ve,R,H,ce,x,Ge,ue,We,K,ze,ut,ps,dn,wn,an,Vn,Jt,un,jn,Cs,Rn,ks,fn,Os,xs,gn,D,hn,pe,En,Qs;const[U,dt]=i.useState(!1),[re,_]=i.useState([]),[lt,et]=i.useState(!1),[tt,F]=i.useState(!1),[J,He]=i.useState(!1),[h,w]=i.useState(""),[Q,_e]=i.useState(!1),[z,C]=i.useState([]),[Ze,I]=i.useState(!1),[be,Ae]=i.useState(!1),[ae,Ot]=i.useState(""),[Ie,Fe]=i.useState(),[ot,at]=i.useState(""),[Ne,st]=i.useState(!1),[de,Ee]=i.useState(""),[X,B]=i.useState("success"),[ne,j]=i.useState(!1),[ht,r]=i.useState(!1),[De,Oe]=i.useState(!1),[zt,V]=i.useState(!1),Y=bn(),ee=oe(fe=>fe.applicationConfig),f=oe(fe=>{var ie;return((ie=fe.payload.payloadData)==null?void 0:ie.data)||fe.payload.payloadData}),it=oe(fe=>fe.payload),Dt=oe(fe=>{var ie;return(ie=fe.request.requestHeader)==null?void 0:ie.requestId}),v=oe(fe=>{var ie;return(ie=fe.userManagement)==null?void 0:ie.taskData}),Xt=oe(fe=>{var ie;return(ie=fe.materialDropDownData)==null?void 0:ie.isOdataApiCalled}),{getDtCall:ss,dtData:Lt}=Zo(),rt=Yo(),[Bn,vs]=i.useState(!0),ns=oe(fe=>fe.request.tabValue),{t:ct}=Ll(),{fetchAllDropdownMasterData:Nn}=Ir(),{getRequestHeaderTemplate:mn}=ji(),ys=[ct("Request Header"),ct("Material List"),ct("Attachments & Remarks"),ct("Preview")],[_n,Kt]=i.useState([!1]),cn=fe=>{Y(Un(fe))},Yt=sl(),q=Yt.state,Ue=new URLSearchParams(Yt.search.split("?")[1]).get("RequestId"),Es=new URLSearchParams(Yt.search),we=Es.get("RequestId"),ye=Es.get("RequestType"),Ut=Es.get("reqBench"),Ss=!(v!=null&&v.taskId)&&!Ut,{createPayloadFromReduxState:Ts}=fc({initialReqScreen:Ss,isReqBench:Ut}),{changePayloadForTemplate:Zt}=gc(f==null?void 0:f.TemplateName),Qt=((Le=Yt.state)==null?void 0:Le.isChildRequest)??(we&&!Ut)??!1,Me=(f==null?void 0:f.RequestType)===((kt=$)==null?void 0:kt.CHANGE)||(f==null?void 0:f.RequestType)===(($s=$)==null?void 0:$s.CHANGE_WITH_UPLOAD)?Zt(!!we):Ts(it),ls={materialDetails:Me,dtName:lo((Wt=(rs=Me==null?void 0:Me[0])==null?void 0:rs.Torequestheaderdata)==null?void 0:Wt.RequestType).dtName,version:lo((Je=(le=Me==null?void 0:Me[0])==null?void 0:le.Torequestheaderdata)==null?void 0:Je.RequestType).version,requestId:((R=(ve=Me==null?void 0:Me[0])==null?void 0:ve.Torequestheaderdata)==null?void 0:R.RequestId)||"",scenario:(x=lo((ce=(H=Me==null?void 0:Me[0])==null?void 0:H.Torequestheaderdata)==null?void 0:ce.RequestType))==null?void 0:x.scenario,templateName:(f==null?void 0:f.RequestType)===((Ge=$)==null?void 0:Ge.CHANGE)||(f==null?void 0:f.RequestType)===((ue=$)==null?void 0:ue.CHANGE_WITH_UPLOAD)?(K=(We=Me==null?void 0:Me[0])==null?void 0:We.Torequestheaderdata)==null?void 0:K.TemplateName:"",matlType:"ALL",region:((ut=(ze=Me==null?void 0:Me[0])==null?void 0:ze.Torequestheaderdata)==null?void 0:ut.Region)||""},{getDisplayData:Xs}=Oc(),Tt=()=>{I(!0)},Et=()=>{I(!1)},Ks=()=>{Ae(!0)},As=fe=>{Ae(fe)},vt=()=>{ot==="success"?rt("/requestBench"):Et()},Ys=()=>{et(!0)},$t=fe=>{let ie="";ye===$.CREATE_WITH_UPLOAD?ie="getAllMaterialsFromExcel":ye===$.EXTEND_WITH_UPLOAD?ie="getAllMaterialsFromExcelForMassExtend":ie="getAllMaterialsFromExcelForMassChange",Ee("Initiating Excel Upload"),st(!0);const Xe=new FormData;[...fe].forEach(me=>Xe.append("files",me)),Xe.append("dtName",ye===$.CREATE_WITH_UPLOAD||ye===$.EXTEND_WITH_UPLOAD?"MDG_MAT_MASS_CREATE_EXTEND_FIELD_CONFIG":"MDG_MAT_CHANGE_TEMPLATE"),Xe.append("version",ye===$.CREATE_WITH_UPLOAD||ye===$.EXTEND_WITH_UPLOAD?"v1":"v5"),Xe.append("requestId",Ue||""),Xe.append("region",f!=null&&f.Region?f==null?void 0:f.Region:"US"),Xe.append("matlType","ALL");const Rt=me=>{var Ke;(me==null?void 0:me.statusCode)===Qe.STATUS_200?(_e(!1),st(!1),Ee(""),rt((Ke=ln)==null?void 0:Ke.REQUEST_BENCH)):(_e(!1),st(!1),Fe(me==null?void 0:me.message),Ee(""),B("error"),Pt())},cs=me=>{st(!1),Fe(me==null?void 0:me.message),Ee(""),B("error"),Pt()};he(`/${Ce}/massAction/${ie}`,"postformdata",Rt,cs,Xe)};i.useEffect(()=>((async()=>{if(we){const ie=li(yn.CURRENT_TASK,!0,{}),Xe=ye||(v==null?void 0:v.ATTRIBUTE_2)||(ie==null?void 0:ie.ATTRIBUTE_2);await Xs(we,Xe,Ut,v,q,"Material"),(ye===$.CHANGE_WITH_UPLOAD||ye===$.CREATE_WITH_UPLOAD||ye===$.EXTEND_WITH_UPLOAD)&&(q==null?void 0:q.objectNumbers)==="Not Available"&&((q==null?void 0:q.reqStatus)===Ls.DRAFT||(q==null?void 0:q.reqStatus)===Ls.UPLOAD_FAILED)?(Y(Un(0)),F(!1),He(!1)):(Y(Un(1)),F(!0),He(!0)),r(!0)}else Y(Un(0))})(),()=>{Y(ti([])),Y(Mr()),Y(xr()),Y(Dr()),Y(Lr()),Y(vr()),Y(Fn({})),Y(yr({data:{}})),Y(Gr([])),Y(Ur([])),Y($r({})),Y(kr()),Y(Wr([])),Y(qr([])),Y(Hr({})),oo(yn.CURRENT_TASK),oo(yn.ROLE)}),[Ue,Y]);function Gs(fe){let ie={decisionTableId:null,decisionTableName:si.MDG_MAT_REGION_DIVISION_MAPPING,version:"v1",conditions:[{"MDG_CONDITIONS.MDG_MAT_REGION":fe}]};ss(ie)}i.useEffect(()=>{f!=null&&f.Region&&Gs(f==null?void 0:f.Region)},[f==null?void 0:f.Region]),i.useEffect(()=>{var fe,ie;if(Lt){const Rt=[...Fr((ie=(fe=Lt==null?void 0:Lt.result)==null?void 0:fe[0])==null?void 0:ie.MDG_MAT_REGION_DIVISION_MAPPING)].sort((cs,me)=>cs.code.localeCompare(me.code));Y(ws({keyName:"Division",data:Rt})),vs(!1),Ee(Br.DT_LOADING)}},[Lt]),i.useEffect(()=>(Xt||(Nn(),Y(wr(!0))),Vr(yn.MODULE,Cn.MAT),mn(),Us(),Y(bt([])),Y(ws({keyName:"Region",data:jr})),Y(ws({keyName:"DiversionControlFlag",data:Pr})),w(Jr("MAT")),()=>{Y(ei({})),oo(yn.MODULE)}),[]),i.useEffect(()=>{tt&&Kt([!0])},[tt]);const Us=()=>{let fe={decisionTableId:null,decisionTableName:"MDG_ATTACHMENTS_LIST_DT",version:"v1",rulePolicy:null,validityDate:null,conditions:[{"MDG_CONDITIONS.MDG_ATTACHMENTS_OBJECT_TYPE":"Material","MDG_CONDITIONS.MDG_ATTACHMENTS_SCENARIO":"Create","MDG_CONDITIONS.MDG_ATTACHMENTS_REG_COMP_CODE":1}],systemFilters:null,systemOrders:null,filterString:null};dt(!0);const ie=Rt=>{var cs,me;if(dt(!1),Rt.statusCode===200){const bs=((me=(cs=Rt==null?void 0:Rt.data)==null?void 0:cs.result[0])==null?void 0:me.MDG_ATTACHMENTS_ACTION_TYPE)||[];C(bs)}},Xe=Rt=>{console.log(Rt)};ee.environment==="localhost"?he(`/${rn}/rest/v1/invoke-rules`,"post",ie,Xe,fe):he(`/${rn}/v1/invoke-rules`,"post",ie,Xe,fe)},Zs=()=>{var me,Ke,bs,At,Ws,en;const fe=we!=null&&we.includes("FCA")?gt.EXCEL.DOWNLOAD_EXCEL_FINANCE:gt.EXCEL.DOWNLOAD_EXCEL_MAT;Ee("Please wait 1-2 minutes while real-time data are exported into the template. Thank you for your patience."),st(!0);let ie={massSchedulingId:f==null?void 0:f.RequestId},Xe={dtName:(f==null?void 0:f.RequestType)===((me=$)==null?void 0:me.CHANGE)||(f==null?void 0:f.RequestType)===((Ke=$)==null?void 0:Ke.CHANGE_WITH_UPLOAD)?"MDG_MAT_CHANGE_TEMPLATE":"MDG_MAT_MASS_CREATE_EXTEND_FIELD_CONFIG",version:(f==null?void 0:f.RequestType)===((bs=$)==null?void 0:bs.CHANGE)||(f==null?void 0:f.RequestType)===((At=$)==null?void 0:At.CHANGE_WITH_UPLOAD)?"v4":"v1",requestId:(f==null?void 0:f.RequestId)||Dt||"",scenario:(f==null?void 0:f.RequestType)===((Ws=$)==null?void 0:Ws.CHANGE)||(f==null?void 0:f.RequestType)===((en=$)==null?void 0:en.CHANGE_WITH_UPLOAD)?"Change with Upload":"Create with Upload",templateName:(f==null?void 0:f.TemplateName)||"",region:(f==null?void 0:f.Region)||"",isChildRequest:Qt,matlType:"ALL"};const Rt=qs=>{const Sn=URL.createObjectURL(qs),It=document.createElement("a");It.href=Sn,It.setAttribute("download",`${f!=null&&f.TemplateName?f==null?void 0:f.TemplateName:we!=null&&we.includes("FCA")?$.FINANCE_COSTING:"Mass_Create"}_Data Export.xlsx`),document.body.appendChild(It),It.click(),document.body.removeChild(It),URL.revokeObjectURL(Sn),st(!1),Ee(""),Fe(`${f!=null&&f.TemplateName?f==null?void 0:f.TemplateName:we!=null&&we.includes("FCA")?$.FINANCE_COSTING:"Mass_Create"}_Data Export.xlsx has been exported successfully.`),B("success"),Pt()},cs=()=>{};he(`/${Ce}${fe}`,"postandgetblob",Rt,cs,we!=null&&we.includes("FCA")?ie:Xe)},Pt=()=>{j(!0)},os=()=>{j(!1)},Ms=()=>{var fe,ie,Xe;Ue&&!Ut?rt((fe=ln)==null?void 0:fe.MY_TASK):Ut?rt((ie=ln)==null?void 0:ie.REQUEST_BENCH):!Ue&&!Ut&&rt((Xe=ln)==null?void 0:Xe.MASTER_DATA)},is=()=>{V(!1)};return N(Is,{children:[Bn&&n(Hn,{blurLoading:Ne,loaderMessage:de}),N(Ve,{sx:{padding:2},children:[N(xt,{sx:{display:"flex",justifyContent:"space-between",alignItems:"center"},children:[Dt||Ue?N(Gt,{variant:"h6",sx:{mb:1,textAlign:"left",display:"flex",alignItems:"center",gap:1},children:[n(Tc,{sx:{fontSize:"1.5rem"}}),ct("Request Header ID"),": ",n("span",{children:Dt||Ue})]}):n("div",{style:{flex:1}}),ns===1&&N(Ve,{sx:{display:"flex",justifyContent:"flex-end",gap:"1rem"},children:[n(je,{variant:"outlined",size:"small",title:ct("Error History"),disabled:!we,onClick:()=>{rt(`/requestBench/errorHistory?RequestId=${we||""}`,{state:{display:!0,childRequest:Qt,module:Cn.MAT}})},color:"primary",children:n(zr,{sx:{padding:"2px"}})}),(f==null?void 0:f.RequestType)===$.CREATE||(f==null?void 0:f.RequestType)===$.EXTEND||(f==null?void 0:f.RequestType)===$.EXTEND_WITH_UPLOAD||(f==null?void 0:f.RequestType)===$.CREATE_WITH_UPLOAD||Ue!=null&&Ue.includes("FCA")?n(je,{variant:"outlined",disabled:!we,size:"small",onClick:()=>Oe(!0),title:Ue!=null&&Ue.includes("FCA")?ct("Finance Costing Change Log"):ct("Create Change Log"),children:n(Ko,{sx:{padding:"2px"}})}):n(je,{variant:"outlined",disabled:!we,size:"small",onClick:Ks,title:ct("Change Log"),children:n(Ko,{sx:{padding:"2px"}})}),n(je,{variant:"outlined",disabled:!we,size:"small",onClick:Zs,title:ct("Export Excel"),children:n(pc,{sx:{padding:"2px"}})})]}),be&&n(ic,{open:!0,closeModal:As,requestId:Dt||Ue,requestType:f==null?void 0:f.RequestType}),De&&n(rc,{open:!0,closeModal:()=>Oe(!1),requestId:Dt||Ue,requestType:f==null?void 0:f.RequestType})]}),(f==null?void 0:f.TemplateName)&&N(Gt,{variant:"h6",sx:{mb:1,textAlign:"left",display:"flex",alignItems:"center",gap:1},children:[n(Ac,{sx:{fontSize:"1.5rem"}}),ct("Template Name"),": ",n("span",{children:f==null?void 0:f.TemplateName})]}),n(Ft,{onClick:()=>{var fe,ie;if(Ut&&!((fe=$n)!=null&&fe.includes(f==null?void 0:f.RequestStatus))){rt((ie=ln)==null?void 0:ie.REQUEST_BENCH);return}V(!0)},color:"primary","aria-label":"upload picture",component:"label",sx:{left:"-10px"},title:ct("Back"),children:n(Xr,{sx:{fontSize:"25px",color:"#000000"}})}),n(Qr,{nonLinear:!0,activeStep:ns,sx:{display:"flex",alignItems:"center",justifyContent:"center",margin:"25px 14%",marginTop:"-35px"},children:ys.map((fe,ie)=>n(Yr,{children:n(Zr,{color:"error",disabled:ie===1&&!tt||ie===2&&!J||ie===3&&!J,onClick:()=>cn(ie),sx:{fontSize:"50px",fontWeight:"bold"},children:n("span",{style:{fontSize:"15px",fontWeight:"bold"},children:fe})})},fe))}),n(fo,{dialogState:Ze,openReusableDialog:Tt,closeReusableDialog:Et,dialogTitle:ae,dialogMessage:Ie,handleDialogConfirm:Et,dialogOkText:"OK",handleOk:vt,dialogSeverity:ot}),n(Hn,{blurLoading:Ne,loaderMessage:de}),ns===0&&N(Is,{children:[n(bc,{setIsSecondTabEnabled:F,setIsAttachmentTabEnabled:He,requestStatus:q!=null&&q.reqStatus?q==null?void 0:q.reqStatus:Ls.ENABLE_FOR_FIRST_TIME,downloadClicked:lt,setDownloadClicked:et}),(ye===$.CHANGE_WITH_UPLOAD||ye===$.CREATE_WITH_UPLOAD||ye===$.EXTEND_WITH_UPLOAD)&&((q==null?void 0:q.reqStatus)==Ls.DRAFT&&(q==null?void 0:q.objectNumbers)==="Not Available"||(q==null?void 0:q.reqStatus)==Ls.UPLOAD_FAILED)&&n(Cc,{handleDownload:Ys,setEnableDocumentUpload:_e,enableDocumentUpload:Q,handleUploadMaterial:$t}),((f==null?void 0:f.RequestType)===((ps=$)==null?void 0:ps.CHANGE)||(f==null?void 0:f.RequestType)===((dn=$)==null?void 0:dn.CHANGE_WITH_UPLOAD))&&!we&&(f==null?void 0:f.DirectAllowed)!=="X"&&(f==null?void 0:f.DirectAllowed)!==void 0&&N(Gt,{sx:{fontSize:"13px",fontWeight:"500",color:(an=(wn=ke)==null?void 0:wn.error)==null?void 0:an.dark,marginTop:"1rem",marginLeft:"0.5rem"},children:[n(Ve,{component:"span",sx:{fontWeight:"bold"},children:"Note:"})," ","You are not authorized to Tcode"," ",N(Ve,{component:"span",sx:{fontWeight:"bold"},children:[" ","MM02."]})]})]}),ns===1&&((f==null?void 0:f.RequestType)===((Vn=$)==null?void 0:Vn.CREATE)||(v==null?void 0:v.ATTRIBUTE_2)===((Jt=$)==null?void 0:Jt.CREATE)||ye===((un=$)==null?void 0:un.CREATE)||ye===((jn=$)==null?void 0:jn.CREATE_WITH_UPLOAD)?n(mc,{requestStatus:q!=null&&q.reqStatus?q==null?void 0:q.reqStatus:Ls.ENABLE_FOR_FIRST_TIME,mandFields:re,addHardCodeData:ht,setIsAttachmentTabEnabled:He,setCompleted:Kt}):(f==null?void 0:f.RequestType)===((Cs=$)==null?void 0:Cs.EXTEND)||(v==null?void 0:v.ATTRIBUTE_2)===((Rn=$)==null?void 0:Rn.EXTEND)||(v==null?void 0:v.ATTRIBUTE_2)===((ks=$)==null?void 0:ks.EXTEND_WITH_UPLOAD)||ye===((fn=$)==null?void 0:fn.EXTEND)||ye===((Os=$)==null?void 0:Os.EXTEND_WITH_UPLOAD)?n(xc,{requestStatus:q!=null&&q.reqStatus?q==null?void 0:q.reqStatus:Ls.ENABLE_FOR_FIRST_TIME,mandFields:re,addHardCodeData:ht,setIsAttachmentTabEnabled:He,setCompleted:Kt}):(f==null?void 0:f.RequestType)===((xs=$)==null?void 0:xs.FINANCE_COSTING)||(v==null?void 0:v.ATTRIBUTE_2)===((gn=$)==null?void 0:gn.FINANCE_COSTING)||ye===((D=$)==null?void 0:D.FINANCE_COSTING)?n(cc,{setCompleted:Kt}):n(yi,{setIsAttachmentTabEnabled:!0,setCompleted:Kt,downloadClicked:lt,setDownloadClicked:et})),ns===2&&n(hc,{requestStatus:q!=null&&q.reqStatus?q==null?void 0:q.reqStatus:Ls.ENABLE_FOR_FIRST_TIME,attachmentsData:z,requestIdHeader:Dt||Ue,pcNumber:h,module:(hn=Cn)==null?void 0:hn.MAT,artifactName:Kr.MATERIALMASTER}),ns===3&&n(Ve,{sx:{width:"100%",overflow:"auto"},children:n(Ec,{requestStatus:q!=null&&q.reqStatus?q==null?void 0:q.reqStatus:Ls.ENABLE_FOR_FIRST_TIME,module:(pe=Cn)==null?void 0:pe.MAT,payloadData:it,payloadForDownloadExcel:ls})})]}),n(yl,{openSnackBar:ne,alertMsg:Ie,alertType:X,handleSnackBarClose:os}),zt&&N(uo,{isOpen:zt,titleIcon:n(ec,{size:"small",sx:{color:(Qs=(En=ke)==null?void 0:En.secondary)==null?void 0:Qs.amber,fontSize:"20px"}}),Title:ct("Warning"),handleClose:is,children:[n(Js,{sx:{mt:2},children:ct(_i.LEAVE_PAGE_MESSAGE)}),N(zs,{children:[n(je,{variant:"outlined",size:"small",sx:{...Ri},onClick:is,children:ct("No")}),n(je,{variant:"contained",size:"small",sx:{...Ii},onClick:Ms,children:ct("Yes")})]})]})]})};export{xd as default};
