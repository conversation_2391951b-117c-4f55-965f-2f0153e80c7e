import{t as A,j as s,c as f,a9 as q,bE as P,dP as w,Z as T,B as J,F as $,aa as K,bD as Q,b3 as U,dO as N}from"./index-f7d9b065.js";const M=8,O=A.createContext({}),V=A.forwardRef((l,g)=>{const n=A.useContext(O);return s("div",{ref:g,...l,...n})});function m(l){const{children:g,...n}=l,c=[];g.forEach(x=>{c.push(x)});const h=c.length,i=48,S=()=>h>8?8*i:c.length*i;return s("div",{ref:l.ref,children:s(O.Provider,{value:n,children:s(N,{itemData:c,height:S()+2*M,width:"100%",outerElementType:V,innerElementType:"ul",itemSize:i,overscanCount:5,itemCount:h,children:({data:x,index:y,style:b})=>{const E=x[y],v={...b,top:b.top+M};return A.cloneElement(E,{style:v})}})})})}const p=({param:l,mandatory:g=!1,dropDownData:n,allDropDownData:c,selectedValues:h,inputState:i,handleSelectAll:S,handleSelectionChange:x,handleMatInputChange:y,handleScroll:b,dropdownRef:E,errors:v,formatOptionLabel:k,handlePopoverOpen:F,handlePopoverClose:L,handleMouseEnterPopover:R,handleMouseLeavePopover:j,isPopoverVisible:B,popoverId:H,popoverAnchorEl:_,popoverRef:W,popoverContent:G,isMaterialNum:o=!1,isLoading:X=!1,isSelectAll:Y=!1,singleSelect:t=!1})=>{const z=()=>{const r=o?(n==null?void 0:n[l==null?void 0:l.key])||[]:(n==null?void 0:n[l==null?void 0:l.key])||(c==null?void 0:c[l==null?void 0:l.key])||[];return Y&&r.length>0&&!t?["Select All",...r]:r},Z=()=>{if(!t)return h[l.key]||[];const r=h[l.key];return Array.isArray(r)&&r.length>0?r[0]:null};return s(U,{multiple:!t,disableListWrap:!0,ListboxComponent:m,options:z(),getOptionLabel:r=>typeof r=="string"?r:r==="Select All"?"Select All":k(r),value:t?Z():h[l.key]||[],inputValue:o&&!t?i==null?void 0:i.code:void 0,onChange:(r,e)=>{!t&&e.includes("Select All")?S(l.key,z().filter(u=>u!=="Select All")):t?x(l.key,e?[e]:[]):x(l.key,e)},disableCloseOnSelect:!t,renderOption:(r,e,{selected:u})=>{var d,C;return f("li",{...r,style:{display:"flex",alignItems:"center",width:"100%",cursor:"pointer"},children:[!t&&s(q,{checked:e==="Select All"?((d=h[l.key])==null?void 0:d.length)===z().length-1:(C=h[l.key])==null?void 0:C.some(I=>(I==null?void 0:I.code)===(e==null?void 0:e.code)),sx:{marginRight:1}}),typeof e=="string"||e==="Select All"?s("span",{style:{whiteSpace:"nowrap",overflow:"hidden",textOverflow:"ellipsis"},title:e,children:e}):f("span",{style:{whiteSpace:"nowrap",overflow:"hidden",textOverflow:"ellipsis"},title:`${e==null?void 0:e.code}${e!=null&&e.desc?` - ${e==null?void 0:e.desc}`:""}`,children:[s("strong",{children:e==null?void 0:e.code}),e!=null&&e.desc?` - ${e==null?void 0:e.desc}`:""]})]})},renderTags:(r,e)=>{if(t)return null;const u=r.map(d=>typeof d=="string"?d:k(d)).join("<br />");return r.length>1?f($,{children:[s(P,{sx:{height:25,fontSize:"0.85rem",".MuiChip-label":{padding:"0 6px"}},label:`${k(r[0])}`,...e({index:0})}),s(P,{sx:{height:25,fontSize:"0.85rem",".MuiChip-label":{padding:"0 6px"}},label:`+${r.length-1}`,onMouseEnter:d=>F(d,u),onMouseLeave:L}),s(w,{id:H,open:B,anchorEl:_,onClose:L,anchorOrigin:{vertical:"bottom",horizontal:"center"},transformOrigin:{vertical:"top",horizontal:"center"},onMouseEnter:R,onMouseLeave:j,ref:W,sx:{"& .MuiPopover-paper":{backgroundColor:T.primary.whiteSmoke,boxShadow:"0px 4px 8px rgba(0, 0, 0, 0.1)",borderRadius:"8px",padding:"10px",fontSize:"0.875rem",color:T.blue.main,border:"1px solid #ddd"}},children:s(J,{sx:{maxHeight:"270px",overflowY:"auto",padding:"5px"},dangerouslySetInnerHTML:{__html:G}})})]}):r.map((d,C)=>s(P,{sx:{height:25,fontSize:"0.85rem",".MuiChip-label":{padding:"0 6px"}},label:`${k(d)}`,...e({index:C})}))},renderInput:r=>{var e,u;return s(K,{...r,label:g?f($,{children:[f("strong",{children:["Select ",l.key]})," ",s("span",{style:{color:(u=(e=T)==null?void 0:e.error)==null?void 0:u.dark},children:"*"})]}):`Select ${l!=null&&l.label?l==null?void 0:l.label:l==null?void 0:l.key}`,variant:"outlined",error:!!v[l.key],helperText:v[l.key],onChange:y||void 0,ListboxProps:{onScroll:o?b:void 0,ref:o?E:void 0},InputProps:{...r.InputProps,endAdornment:f($,{children:[X?s(Q,{size:20,sx:{mr:1}}):null,r.InputProps.endAdornment]})},sx:{"& .MuiOutlinedInput-root":{borderRadius:"8px",height:50,boxShadow:"0 2px 4px rgba(0, 0, 0, 0.1)"},"& .MuiInputLabel-root":{fontWeight:500}}})}},l.key)};export{p as F};
