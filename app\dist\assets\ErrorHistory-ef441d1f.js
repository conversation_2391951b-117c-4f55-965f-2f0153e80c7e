import{r as s,s as xe,u as ye,aP as Ee,a as He,c,j as e,ag as Ce,bm as $e,bn as ze,bo as z,bp as S,bq as qe,d as y,bE as Q,gB as Se,T as q,ae as Fe,an as Ue,bl as We,gC as Te,B as L,fM as Re,C as we,aT as je,aL as ve,c2 as Ne,aJ as Ge,g as Je,n as Ke,N as Ye,Z as d,aZ as k,O as I,Q as Ve,bC as Qe,a6 as Y,d2 as Ze,gD as Xe,gE as er,gF as rr,dx as or,bM as tr,bN as he,F as ge,b5 as sr,b6 as nr,af as ar,dy as me,cI as pe,bs as be,gG as lr,aO as V,gH as ir}from"./index-f7d9b065.js";import{d as cr}from"./Description-ab582559.js";import{d as ur}from"./CheckCircleOutline-e186af3e.js";const fe=b=>(b.includes("Duplicate"),"error"),dr=s.forwardRef(({module:b,errorType:v},A)=>{const n=xe(),[T,m]=s.useState(0),[l,O]=s.useState(5),[f,N]=s.useState([]),[t,x]=s.useState([]),[h,E]=s.useState(!1),F=ye(),_=new URLSearchParams(F.search.split("?")[1]).get("RequestId"),[U,M]=s.useState(""),{customError:D}=Ee(),{t:B}=He(),W=(i,C)=>m(C),H=i=>{O(parseInt(i.target.value,10)),m(0)},{destination:j}=Re(b),u=t==null?void 0:t.slice(T*l,T*l+l);s.useEffect(()=>{x(f)},[f]);const G=()=>{E(!0);const i=o=>{(o==null?void 0:o.statusCode)===Ge.STATUS_200?(N(o==null?void 0:o.body),x(o==null?void 0:o.body),onHandled()):M(o==null?void 0:o.message),E(!1)},C=o=>{D(o),M(o==null?void 0:o.message),E(!1)};we(`/${j}/${je.ERROR_HISTORY.EXCEL_ERROR_HISTORY}?requestId=${_}`,"get",i,C)},J=()=>{x(f),m(0),n(ve({module:"ErrorHistory"}))},X=[{label:"Sheet Name",key:"sheetName"},{label:"Error Type",key:"errorType"},{label:"Row Number",key:"rowNumber"},{label:"Column Number",key:"columnNumber"},{label:"Error Details",key:"errorDetails"}],ee={convertJsonToExcel:()=>{let i=[];[{field:"requestId",headerName:"Request ID"},...X.filter(({label:o,key:P})=>o.trim()!=="-"&&P.trim()!=="-").map(({label:o,key:P})=>({field:P,headerName:o}))].forEach(o=>{o.headerName.toLowerCase()!=="action"&&!o.hide&&i.push({header:o.headerName,key:o.field})}),Ne({fileName:`${b} Excel Error Log`,columns:i,rows:t.length>0?t:f})}};return s.useImperativeHandle(A,()=>({triggerExcelErrorAction:G,triggerExcelExportAction:ee.convertJsonToExcel})),c(L,{sx:{mt:2,display:"flex",flexDirection:"column",height:"100%"},children:[e(We,{component:Ce,elevation:3,sx:{borderRadius:2,maxHeight:"56vh",overflow:"auto"},children:c($e,{stickyHeader:!0,children:[e(ze,{children:e(z,{children:["Sheet Name","Error Type","Row","Column","Error Details"].map(i=>e(S,{sx:{minWidth:"150px",fontWeight:"bold",backgroundColor:"#f5f5f5"},children:i},i))})}),e(qe,{children:h?e(z,{children:e(S,{colSpan:5,align:"center",children:e(y,{children:B("Loading...")})})}):(u==null?void 0:u.length)>0?u==null?void 0:u.map((i,C)=>c(z,{hover:!0,sx:{"&:hover":{backgroundColor:"#f9f9f9"}},children:[e(S,{children:i.sheetName}),e(S,{children:e(Q,{icon:fe(i.errorType)==="error"?e(Se,{fontSize:"small",sx:{color:"#dee3e2 !important"}}):e(ur,{fontSize:"small",sx:{color:"#dee3e2 !important"}}),label:i.errorType,color:fe(i.errorType),size:"small",sx:{fontSize:"0.65rem",fontWeight:500,height:27,borderRadius:"99px",color:"#dee3e2 !important",p:.3}})}),e(S,{children:i.rowNumber}),e(S,{children:i.columnNumber}),e(S,{children:e(q,{title:i.errorDetails,arrow:!0,children:e(y,{variant:"body2",sx:{flex:1,overflow:"hidden",textOverflow:"ellipsis",display:"-webkit-box",WebkitLineClamp:2,WebkitBoxOrient:"vertical",maxWidth:"300px"},children:i.errorDetails})})})]},i.id||C)):e(z,{children:c(S,{colSpan:5,align:"center",children:[e(y,{variant:"body1",sx:{py:3},children:t.length===0&&f.length>0?B("No results found for your search"):U||Fe.NO_ERROR_FOUND}),t.length===0&&f.length>0&&e(Ue,{variant:"text",onClick:J,sx:{mt:1},children:B("Clear search to show all records")})]})})})]})}),e(Te,{rowsPerPageOptions:[5,10,25,50],count:(t==null?void 0:t.length)??0,rowsPerPage:l,page:T,onPageChange:W,onRowsPerPageChange:H,sx:{mt:1},showFirstButton:!0,showLastButton:!0})]})}),hr=(b,v)=>{const A=m=>{if(!m||m.length===0)return{columns:[],rows:[]};const l=m[0],O=Object.keys(l).map(t=>({field:t,headerName:t,width:200,flex:1,renderCell:x=>{const h=x.value;return h==="Validated Successfully"?e(Q,{label:h,color:"success",size:"small"}):h&&h.toString().includes("Error")?e(Q,{label:h,color:"error",size:"small"}):e(y,{variant:"body2",children:h})}})),f=Math.max(...Object.values(l).map(t=>Array.isArray(t)?t.length:0)),N=[];for(let t=0;t<f;t++){const x={id:t};Object.keys(l).forEach(h=>{const E=l[h];Array.isArray(E)?x[h]=E[t]||"":x[h]=t===0?E:""}),N.push(x)}return{columns:O,rows:N}},n=s.useMemo(()=>{const m=(b==null?void 0:b.body)||{};return Object.keys(m).filter(l=>Array.isArray(m[l])&&m[l].length>0)},[b]),T=s.useMemo(()=>{if(n.length===0||v>=n.length)return{columns:[],rows:[]};const m=n[v],l=b.body[m];return A(l)},[v,n,b]);return console.log("check tabs",n,b),{tabs:n,currentTabData:T}},fr=({isHierarchyCheck:b=!1,module:v})=>{var le,ie,ce,ue,de;const{customError:A}=Ee(),[n,T]=s.useState("sap"),m=xe(),l=ye(),f=new URLSearchParams(l.search.split("?")[1]).get("RequestId"),N=Je(),[t,x]=s.useState([]),[h,E]=s.useState([]),[F,Z]=s.useState([]),[_,U]=s.useState(0),[M,D]=s.useState(!1),B=((le=l.state)==null?void 0:le.display)??("childRequest"in(l.state??{})?!0:("isChildRequest"in(l.state??{}),!1));s.useState([]),s.useState(!1);const W=Ke(r=>r==null?void 0:r.userManagement.taskData),H=((ie=l.state)==null?void 0:ie.isHierarchyCheck)??b,j=((ce=l.state)==null?void 0:ce.childRequest)===!0||((ue=l.state)==null?void 0:ue.isChildRequest)===!0||Object.keys(W).length!==0?"TRUE":"FALSE",u=((de=l.state)==null?void 0:de.module)||v,{destination:G,errorHistoryUrl:J,downloadErrorHistoryParentUrl:X,downloadErrorHistoryChildUrl:ee}=Re(u),{tabs:i,currentTabData:C}=hr(F,_),o=()=>{const r={isChild:j,requestId:f,isHierarchyGroup:u===V.PCG||u===V.CCG||u===V.CEG?"TRUE":"FALSE"},g=a=>{var R;H?Z(a):(x(a==null?void 0:a.body),E(a==null?void 0:a.body),Ae((a==null?void 0:a.count)??((R=a==null?void 0:a.body)==null?void 0:R.length))),D(!1),m(ve({module:"ErrorHistory"}))},p=a=>{A(a),D(!1)};D(!0),we(`/${G}/${J}`,"post",g,p,r)};s.useState({}),s.useEffect(()=>{o()},[u]);const P=ir[u]||[],Pe=r=>{if(r){const g=r.toString().toLowerCase(),p=t.filter(a=>{var R;return(R=a==null?void 0:a.materialNo)==null?void 0:R.toString().toLowerCase().includes(g)});E(p)}else o()},ke=r=>{De(parseInt(r.target.value,10)),oe(0)},Ie=(r,g)=>{oe(g)},[Le,Ae]=s.useState(0),[re,oe]=s.useState(0),[K,De]=s.useState(10),te=re*K,Be=te+K,se=h==null?void 0:h.slice(te,Be),ne={"Profit Center":"profitCenter","Cost Center":"costCenter","General Ledger":"glAccount","Bank Key":"bankKey",Material:"materialNo","Internal Order":"InternalOrderErrorId"}[u],ae={convertJsonToExcel:()=>{let r=[];[{field:ne,headerName:`${u} Number`},...P.filter(({label:p,key:a})=>p.trim()!=="-"&&a.trim()!=="-").map(({label:p,key:a})=>({field:a,headerName:p}))].forEach(p=>{p.headerName.toLowerCase()!=="action"&&!p.hide&&r.push({header:p.headerName,key:p.field,width:p.width})}),Ne({fileName:`${u} Error Logsheet`,columns:r,rows:t})},button:()=>e(Button,{sx:{textTransform:"capitalize",position:"absolute",right:0,top:0},onClick:()=>ae.convertJsonToExcel(),children:"Download"})},$=s.useRef(),Oe=(r,g)=>{g!==null&&T(g)};s.useEffect(()=>{n==="excel"&&$.current.triggerExcelErrorAction()},[n]);const _e=()=>{var r;n==="sap"?o():n==="excel"&&((r=$.current)==null||r.triggerExcelErrorAction())},Me=()=>{var r;n==="sap"?ae.convertJsonToExcel():n==="excel"&&((r=$.current)==null||r.triggerExcelExportAction())};return e("div",{id:"container_outermost",children:e("div",{className:"purchaseOrder",style:{...Ye,backgroundColor:`${d.primary.veryLight}`},children:e(k,{spacing:1,children:c(ge,{children:[c(I,{container:!0,sx:Ve,alignItems:"center",children:[e(I,{item:!0,md:6,sx:{display:"flex",alignItems:"center",...Qe},children:c(k,{direction:"row",spacing:2,alignItems:"center",children:[B&&e(Y,{onClick:()=>{N(-1)},color:"primary","aria-label":"upload picture",component:"label",children:e(Ze,{sx:{fontSize:"25px",color:"#000000"}})}),c(k,{children:[e(y,{variant:"h3",children:c("strong",{children:["Error History - ",f]})}),e(y,{variant:"body2",color:"#777",children:"This view displays the error history of a Request"})]})]})}),e(I,{item:!0,md:6,sx:{display:"flex",justifyContent:"flex-end",alignItems:"center"},children:c(I,{container:!0,direction:"row",justifyContent:"flex-end",alignItems:"center",spacing:0,marginBottom:"10px",children:[e(q,{slotProps:{tooltip:{sx:{fontSize:"0.9em"}}},title:"Reload",placement:"bottom",arrow:!0,children:e(Y,{onClick:_e,children:e(Xe,{})})}),e(q,{slotProps:{tooltip:{sx:{fontSize:"0.9em"}}},title:"Export Table",placement:"bottom",arrow:!0,children:e(Y,{onClick:Me,children:e(er,{})})}),e(q,{slotProps:{tooltip:{sx:{fontSize:"0.9em"}}},title:"Search",children:e(rr,{title:"Type Material Number to know its status",handleSearchAction:r=>Pe(r),keyName:"errorHistorySearch",message:"Search",module:"ErrorHistory",clearSearchBar:()=>o()})})]})})]}),c(k,{sx:{padding:"16px",pb:"0 !important",width:"100%",maxWidth:"100%",...or},children:[c(tr,{value:n,exclusive:!0,onChange:Oe,sx:{width:"40%","& .MuiToggleButton-root":{borderRadius:"0 !important"},"& .MuiToggleButton-root:first-of-type":{borderTopLeftRadius:"8px !important",borderBottomLeftRadius:"8px !important"},"& .MuiToggleButton-root:last-of-type":{borderTopRightRadius:"8px !important",borderBottomRightRadius:"8px !important"}},children:[c(he,{value:"sap",sx:{flex:1,p:1,color:n==="sap"?`${d.reportTile.blue}`:`${d.primary.grey}`,backgroundColor:n==="sap"?`${d.reportTile.lightBlue}`:"transparent","&:hover":{backgroundColor:`${d.reportTile.lightBlue}`}},children:[e(Se,{sx:{fontSize:18,mr:1}}),"SAP/DB Error Log"]}),c(he,{value:"excel",sx:{flex:1,p:1,color:n==="excel"?`${d.reportTile.blue}`:`${d.primary.grey}`,backgroundColor:n==="excel"?`${d.reportTile.lightBlue}`:"transparent","&:hover":{backgroundColor:`${d.reportTile.lightBlue}`}},children:[e(cr,{sx:{fontSize:18,mr:1}}),"Excel Upload Error"]})]}),H&&n==="sap"?c(ge,{children:[e(L,{sx:{borderBottom:1,borderColor:"divider",mb:3},children:e(sr,{value:_,onChange:(r,g)=>U(g),variant:"scrollable",scrollButtons:"auto",sx:{"& .MuiTab-root":{textTransform:"none",fontWeight:"medium"}},children:i.map((r,g)=>e(nr,{label:r,id:`tab-${g}`,"aria-controls":`tabpanel-${g}`},r))})}),e(ar,{rows:C.rows,columns:C.columns,getRowIdValue:"id",autoHeight:!0,scrollbarSize:10,sx:{"& .MuiDataGrid-row:hover":{backgroundColor:"#EAE9FF40"},backgroundColor:"#fff"}})]}):n==="sap"&&(t==null?void 0:t.length)>0?c(L,{id:"container_outermost",sx:{backgroundColor:d.basic.white,borderRadius:2,boxShadow:"0 2px 4px rgba(0,0,0,0.1)",mt:2,px:2,py:1},children:[se.length>0?e(k,{spacing:2,sx:{maxHeight:"calc(100vh - 289px)",overflowY:"auto"},children:se.map((r,g)=>c(Ce,{elevation:2,sx:{p:3,borderRadius:2,backgroundColor:d.basic.white,position:"relative",border:"1px solid #e0e0e0"},children:[e(L,{sx:{display:"flex",justifyContent:"space-between",alignItems:"flex-start",mb:2},children:c(y,{variant:"subtitle1",sx:{fontWeight:700,color:d.blue.main},children:[u," No. ",r==null?void 0:r[ne]]})}),e(I,{container:!0,spacing:2,children:P.map(({label:p,key:a},R)=>{const w=r[a];return e(I,{item:!0,xs:4,children:e(me,{variant:"outlined",sx:{height:"100%",border:`3px solid ${w&&w!==pe.VALIDATED_SUCCESS&&w!==pe.SYNDICATED_IN_SAP?d.border.error:"#e0e0e0"}`},children:c(be,{children:[e(y,{sx:{fontWeight:600,fontSize:"0.875rem",color:d.blue.indigo},children:p}),e(y,{sx:{fontWeight:700,fontSize:"0.875rem",color:d.black.main,whiteSpace:"pre-line"},children:w!=null&&w!==""?w:"-"})]})})},R)})})]},g))}):e(L,{sx:{textAlign:"center",py:4},children:e(y,{sx:{fontSize:"0.875rem",fontWeight:500,color:"#777"},children:"No data found"})}),e(L,{sx:{mt:2,borderTop:"1px solid #e0e0e0",backgroundColor:d.basic.white,p:"8px 16px"},children:e(Te,{component:"div",count:Le,page:re,onPageChange:Ie,rowsPerPage:K,onRowsPerPageChange:ke})})]}):n!=="excel"&&e(me,{sx:{width:"100%",mt:2,border:"1px solid #e0e0e0",borderRadius:2,boxShadow:"0 2px 4px rgba(0,0,0,0.1)"},children:e(be,{sx:{p:3},children:e(k,{spacing:2,alignItems:"center",children:e(y,{variant:"h5",sx:{color:d.black.light,fontWeight:500},children:"No Data Found for this Request ID"})})})}),n==="excel"&&e(dr,{module:u,ref:$,errorType:n})]}),M&&e(lr,{})]})})})})};export{fr as default};
