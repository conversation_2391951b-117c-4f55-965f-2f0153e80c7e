import{b9 as Ro,n as ho,a as Oo,g as $o,u as Mo,s as Go,aP as Uo,r as _,C as Q,be as i,c as A,j as n,Q as Wo,O as C,a6 as _o,a_ as vo,a$ as wo,d as w,Z as O,aZ as lo,B as Z,b5 as Vo,b6 as mo,F as D,k as Ho,A as ko,V as Bo,i as Fo,ae as jo,aQ as v,aG as zo,bg as c,bh as R,bi as qo,b2 as Ko,aT as oo}from"./index-f7d9b065.js";import{d as Qo,a as Zo,b as Xo}from"./Category-81eb2509.js";import{d as Yo}from"./Description-ab582559.js";import{G as bo}from"./GenericTabsForChange-c448c967.js";import{u as Jo}from"./useArticleFieldConfig-f9acb4d0.js";import{G as go}from"./GenericViewGeneral-e6209433.js";import{A as ao}from"./AdditionalData-c6566943.js";import{T as Do}from"./TaxDataSAP-07618524.js";import"./FilterField-ed1f5dc1.js";import"./useChangeLogUpdate-1ba6b2dd.js";import"./AdapterDayjs-2a9281df.js";import"./advancedFormat-4f6292d4.js";import"./customParseFormat-1bc1aa07.js";import"./isBetween-c1c6beb9.js";import"./AutoCompleteType-13f5746b.js";import"./useMobilePicker-9b56b5b6.js";import"./CSSTransition-cd337b47.js";import"./DeleteOutline-584dc929.js";import"./useCustomDtCall-0fd16760.js";import"./SingleSelectDropdown-aee403d4.js";const X=({label:Y,value:J,labelWidth:M="25%",centerWidth:S="5%",icon:y})=>A(lo,{flexDirection:"row",alignItems:"center",children:[y&&n("div",{style:{marginRight:"10px"},children:y}),n(w,{variant:"body2",color:O.secondary.grey,style:{width:M},children:Y}),n(w,{variant:"body2",fontWeight:"bold",sx:{width:S,textAlign:"center"},children:":"}),n(w,{variant:"body2",fontWeight:"bold",justifyContent:"flex-start",children:J||""})]}),yl=()=>{var eo,no,ro;const{fetchMaterialFieldConfig:Y}=Jo(),J=Ro(()=>({customTabs:{"& .MuiTabs-scroller":{overflowX:"auto !important",overflowY:"hidden !important"}}})),M=ho(e=>e.payload),S=ho(e=>e.tabsData.allTabsData),{t:y}=Oo(),So=$o();J();const xo=Mo(),f=Go(),r=xo.state,{customError:To}=Uo(),F=[],[h,uo]=_.useState([]),[Ao,fo]=_.useState({}),[to,so]=_.useState(!1),[yo,Co]=_.useState(""),[b,No]=_.useState(0),[Io,a]=_.useState(null);_.useEffect(()=>{Eo(),Y(r==null?void 0:r.materialType.split(" - ")[0])},[]);const Eo=()=>{so(!0);const e={materialNo:r==null?void 0:r.Number},o={"Basic Data":"Toclientdata",Sales:"Tosalesdata",Purchasing:"Toplantdata",Costing:"Toplantdata",Accounting:"Toaccountingdata",MRP:"Toplantdata",Warehouse:"Towarehousedata","Sales-Plant":"Tosalesdata","Work Scheduling":"Toplantdata"};function d(s){if(!Array.isArray(s))return{UniqueTaxDataSet:[]};const x=new Map;return s.forEach(g=>{const G=g==null?void 0:g.Depcountry;for(let N=1;N<=9;N++){const U=g[`TaxType${N}`],T=g[`Taxclass${N}`];if(U&&T!==void 0){const I=`${G}-${U}`,E=T==="1"?"Taxable":"Exempt";x.has(I)||x.set(I,{Country:G,TaxType:U,TaxClasses:[],SelectedTaxClass:{TaxClass:T,TaxClassDesc:E}});const p=x.get(I);p.TaxClasses.some(P=>P.TaxClass===T)||p.TaxClasses.push({TaxClass:T,TaxClassDesc:E}),p.SelectedTaxClass={TaxClass:T,TaxClassDesc:E}}}}),{UniqueTaxDataSet:Array.from(x.values())}}const io=s=>{var x,g,G,N,U,T,I,E,p,$,P,W,V,m;if(s!=null&&s.body){const L=((x=s.body[0].ViewNames)==null?void 0:x.split(",").map(l=>l.trim()))||[],H=d((g=s==null?void 0:s.body[0])==null?void 0:g.Tocontroldata),k=[],K={};L.forEach(l=>{const t=o[l]??"";t!==""&&s.body[0][t]&&(k.push(l),K[l]=s.body[0][t])}),L.includes("Sales")&&(k.push("Sales-Plant"),K["Sales-Plant"]=s.body[0].Toplantdata),k.push("Additional Data"),uo(k),fo(K);const u=(G=s==null?void 0:s.body[0])==null?void 0:G.Toclientdata;if(u){if(u.Pvalidfrom){const l=c(u.Pvalidfrom);u.Pvalidfrom=l?l.toISOString().split("T")[0]:""}if(u.Svalidfrom){const l=c(u.Svalidfrom);u.Svalidfrom=l?l.toISOString().split("T")[0]:""}}f(R({materialID:(N=s==null?void 0:s.body[0])==null?void 0:N.Material,viewID:"Basic Data",itemID:"basic",data:u})),f(R({materialID:(U=s==null?void 0:s.body[0])==null?void 0:U.Material,viewID:i.PURCHASING_GENERAL,itemID:i.PURCHASING_GENERAL,data:(T=s==null?void 0:s.body[0])==null?void 0:T.Toclientdata})),f(R({materialID:(I=s==null?void 0:s.body[0])==null?void 0:I.Material,viewID:i.SALES_GENERAL,itemID:i.SALES_GENERAL,data:(E=s==null?void 0:s.body[0])==null?void 0:E.Toclientdata})),f(qo({materialID:(p=s==null?void 0:s.body[0])==null?void 0:p.Material,data:(P=($=s==null?void 0:s.body[0])==null?void 0:$.Touomdata)==null?void 0:P.map((l,t)=>{var B,po;return{...l,id:`${l.Material}-${t}`,uomId:`${l.Material}-${t}`,xValue:(l==null?void 0:l.Denominatr)||"",aUnit:(l==null?void 0:l.AltUnit)||"",measureUnitText:"",yValue:(l==null?void 0:l.Numerator)||"",eanUpc:(l==null?void 0:l.EanUpc)||"",eanCategory:(l==null?void 0:l.EanCat)||"",length:l==null?void 0:l.Length,width:l==null?void 0:l.Width,height:l==null?void 0:l.Height,unitsOfDimension:(l==null?void 0:l.UnitDim)||"",volume:(l==null?void 0:l.Volume)||"",volumeUnit:(l==null?void 0:l.Volumeunit)||"",grossWeight:(l==null?void 0:l.GrossWt)||"",netWeight:t===0&&(((po=(B=s==null?void 0:s.body[0])==null?void 0:B.Toclientdata)==null?void 0:po.NetWeight)||(l==null?void 0:l.NetWeight))||"",weightUnit:(l==null?void 0:l.UnitOfWt)||""}})})),f(Ko({materialID:(W=s==null?void 0:s.body[0])==null?void 0:W.Material,data:(V=s==null?void 0:s.body[0])==null?void 0:V.Tomaterialdescription.map((l,t)=>({id:t+1,language:l==null?void 0:l.Langu,materialDescription:l==null?void 0:l.MatlDesc}))})),f(R({materialID:(m=s==null?void 0:s.body[0])==null?void 0:m.Material,viewID:"TaxData",itemID:"TaxData",data:H||{TaxData:{UniqueTaxDataSet:[]}}})),so(!1),Co("")}},q=()=>{};Q(`/${v}/data/displayLimitedMaterialData`,"post",io,q,e)},Po=(e,o)=>{No(o),a(null)},Lo=(e,o,d)=>(io,q)=>{var T,I,E;if(d===i.COSTING){let p={materialNo:o==null?void 0:o.Material,plant:o==null?void 0:o.Plant};o==null||o.Plant;let $={materialNo:o==null?void 0:o.Material,valArea:o==null?void 0:o.Plant},P=`/${v}/${oo.ACCORDION_API.PLANT}`,W=`/${v}/${oo.ACCORDION_API.ACCOUNTING}`;Q(P,"post",L=>{const H=L==null?void 0:L.body[0].Toplantdata[0];Q(W,"post",u=>{var B;const l=(B=u==null?void 0:u.body[0])==null?void 0:B.Toaccountingdata[0],t={...H,...l};f(R({materialID:o==null?void 0:o.Material,viewID:d,itemID:o==null?void 0:o.Plant,data:t}))},()=>{},$)},()=>{},p),a(q?e:null);return}let s={},x="",g="";d===i.PURCHASING||d===i.MRP||d===i.WORKSCHEDULING||d===i.SALES_PLANT?(s={materialNo:o==null?void 0:o.Material,plant:o==null?void 0:o.Plant},g=o==null?void 0:o.Plant,x=`/${v}/data/displayLimitedPlantData`):d===i.WAREHOUSE?(s={materialNo:o==null?void 0:o.Material,whNumber:o==null?void 0:o.WhseNo},g=o==null?void 0:o.WhseNo,x=`/${v}/${oo.ACCORDION_API.WAREHOUSE}`):d===i.ACCOUNTING?(s={materialNo:o==null?void 0:o.Material,valArea:o==null?void 0:o.ValArea},g=o==null?void 0:o.ValArea,x=`/${v}/data/displayLimitedAccountingData`):d===i.SALES&&(s={materialNo:o==null?void 0:o.Material,salesOrg:o==null?void 0:o.SalesOrg,distChnl:o==null?void 0:o.DistrChan},g=`${o==null?void 0:o.SalesOrg}-${o==null?void 0:o.DistrChan}`,x=`/${v}/data/displayLimitedSalesData`);const G=p=>{var $,P,W,V,m,L;if(d===i.PURCHASING||d===i.MRP||d===i.WORKSCHEDULING||d===i.SALES_PLANT)f(R({materialID:o==null?void 0:o.Material,viewID:d,itemID:o==null?void 0:o.Plant,data:{...p==null?void 0:p.body[0].Toplantdata[0],ProdProf:(($=p==null?void 0:p.body[0].Toplantdata[0])==null?void 0:$.Prodprof)||""}}));else if(d===i.ACCOUNTING)f(R({materialID:o==null?void 0:o.Material,viewID:d,itemID:o==null?void 0:o.ValArea,data:(P=p==null?void 0:p.body[0])==null?void 0:P.Toaccountingdata[0]}));else if(d===i.WAREHOUSE)f(R({materialID:o==null?void 0:o.Material,viewID:d,itemID:o==null?void 0:o.WhseNo,data:(W=p==null?void 0:p.body[0])==null?void 0:W.Towarehousedata[0]}));else if(d===i.SALES){if((m=(V=p==null?void 0:p.body[0])==null?void 0:V.Tosalesdata[0])!=null&&m.ValidFrom){const H=c(p.body[0].Tosalesdata[0].ValidFrom);p.body[0].Tosalesdata[0].ValidFrom=H?H.toISOString().split("T")[0]:""}f(R({materialID:o==null?void 0:o.Material,viewID:d,itemID:`${o==null?void 0:o.SalesOrg}-${o==null?void 0:o.DistrChan}`,data:(L=p==null?void 0:p.body[0])==null?void 0:L.Tosalesdata[0]}))}},N=p=>{To(p)};!((E=(I=(T=M==null?void 0:M[o==null?void 0:o.Material])==null?void 0:T.payloadData)==null?void 0:I[d])!=null&&E[g])&&Q(x,"post",G,N,s),a(q?e:null)},j=S!=null&&S.hasOwnProperty(i.SALES_GENERAL)?Object.entries(S[i.SALES_GENERAL]):[],z=S!=null&&S.hasOwnProperty(i.PURCHASING_GENERAL)?Object.entries(S[i.PURCHASING_GENERAL]):[];return A("div",{style:{backgroundColor:"#FAFCFF"},children:[n(C,{container:!0,sx:Wo,children:n(C,{item:!0,md:12,sx:{padding:"16px",display:"flex"},children:A(C,{md:9,sx:{display:"flex"},children:[n(_o,{color:"primary",sx:vo,onClick:()=>So(-1),children:n(wo,{sx:{fontSize:"25px",color:"#000000"}})}),A(C,{item:!0,md:12,children:[n(w,{variant:"h3",children:n("strong",{children:y("Display Article")})}),n(w,{variant:"body2",color:"#777",children:y("This view displays the details of the articles")})]})]})})}),A(C,{container:!0,display:"flex",flexDirection:"row",flexWrap:"nowrap",sx:{justifyContent:"space-between",alignItems:"center",paddingLeft:"29px",backgroundColor:O.basic.lighterGrey,borderRadius:"10px",boxShadow:"0 2px 8px rgba(0,0,0,0.06)"},children:[A(lo,{width:"48%",spacing:1,sx:{padding:"10px 15px",borderRight:"1px solid #eaedf0"},children:[n(C,{item:!0,children:n(X,{label:y("Article"),value:(r==null?void 0:r.Number)||"",labelWidth:"35%",icon:n(Qo,{sx:{color:O.blue.indigo,fontSize:"20px"}})})}),n(C,{item:!0,children:n(X,{label:y("Industry Sector"),value:(r==null?void 0:r.indSector)||"",labelWidth:"35%",icon:n(Zo,{sx:{color:O.blue.indigo,fontSize:"20px"}})})})]}),A(lo,{width:"48%",spacing:1,marginRight:"-10%",sx:{padding:"10px 15px"},children:[n(C,{item:!0,children:n(X,{label:y("Material Type"),value:(r==null?void 0:r.materialType)||"",labelWidth:"35%",icon:n(Xo,{sx:{color:O.blue.indigo,fontSize:"20px"}})})}),n(C,{item:!0,children:n(X,{label:y("Material Description"),value:(r==null?void 0:r.materialDesc)||"",labelWidth:"35%",icon:n(Yo,{sx:{color:O.blue.indigo,fontSize:"20px"}})})})]})]}),n(C,{children:S&&h.length>0?n(Z,{sx:{marginTop:"30px",border:"1px solid #e0e0e0",padding:"16px",background:O.primary.white},children:A(Z,{sx:{marginTop:"-10px",marginLeft:"5px"},children:[n(Vo,{value:b,onChange:Po,"aria-label":"material tabs",sx:{top:100,zIndex:1e3,background:"#fafcff",marginLeft:"20px",marginBottom:"-20px"},children:h.map((e,o)=>n(mo,{label:y(e)},o))}),n(Z,{sx:{padding:2,marginTop:2},children:h[b]==="Basic Data"&&M?n(bo,{disabled:!0,materialID:r==null?void 0:r.Number,dropDownData:F,basicDataTabDetails:S["Basic Data"],activeViewTab:"Basic Data",plantData:"basic"}):h[b]==="Additional Data"?n(ao,{disableCheck:!0,materialID:r==null?void 0:r.Number}):A(D,{children:[h[b]===i.SALES&&A(D,{children:[n(Do,{materialID:r==null?void 0:r.Number}),(j==null?void 0:j.length)>0&&n(go,{materialID:r==null?void 0:r.Number,GeneralFields:j,disabled:!0,dropDownData:F,viewName:(eo=i)==null?void 0:eo.SALES_GENERAL})]}),h[b]===i.PURCHASING&&A(D,{children:[" ",(z==null?void 0:z.length)>0&&n(go,{materialID:r==null?void 0:r.Number,GeneralFields:z,disabled:!0,dropDownData:F,viewName:(no=i)==null?void 0:no.PURCHASING_GENERAL})]}),(ro=Ao[h[b]])==null?void 0:ro.map((e,o)=>A(Ho,{sx:{marginBottom:"20px",boxShadow:3},expanded:Io===o,onChange:Lo(o,e,h[b]),children:[n(ko,{expandIcon:n(Bo,{}),sx:{backgroundColor:"#f5f5f5",borderRadius:"10px",padding:"8px 16px","&:hover":{backgroundColor:"#e0e0e0"}},children:n(w,{variant:"h6",sx:{fontWeight:"bold"},children:h[b]===i.PURCHASING||h[b]===i.COSTING||h[b]===i.MRP||h[b]===i.WORKSCHEDULING||h[b]===i.SALES_PLANT?`Plant - ${e==null?void 0:e.Plant}`:h[b]===i.SALES?`Sales Org - ${e==null?void 0:e.SalesOrg} ,  Distribution Channel - ${e==null?void 0:e.DistrChan}`:h[b]===i.ACCOUNTING?`Plant - ${e==null?void 0:e.ValArea}`:h[b]===i.WAREHOUSE?`Warehouse - ${e==null?void 0:e.WhseNo}`:`${e==null?void 0:e.Material}`})}),M&&n(Fo,{sx:{padding:"16px"},children:n(w,{sx:{fontSize:"0.875rem",color:"#555"},children:n(bo,{disabled:!0,materialID:r==null?void 0:r.Number,dropDownData:F,basicDataTabDetails:S[h[b]],activeViewTab:h[b],plantData:h[b]==="Sales"?`${e==null?void 0:e.SalesOrg}-${e==null?void 0:e.DistrChan}`:h[b]==="Purchasing"?`${e==null?void 0:e.Plant}`:h[b]==="Accounting"?`${e==null?void 0:e.ValArea}`:h[b]==="Warehouse"?`${e==null?void 0:e.WhseNo}`:`${e==null?void 0:e.Plant}`})})})]},o))]})})]})}):n(Z,{sx:{marginTop:"30px",border:`1px solid ${O.secondary.grey}`,padding:"16px",background:`${O.primary.white}`,textAlign:"center"},children:n("span",{children:jo.NO_DATA_AVAILABLE})})}),n(zo,{blurLoading:to,loaderMessage:yo})]})};export{yl as default};
