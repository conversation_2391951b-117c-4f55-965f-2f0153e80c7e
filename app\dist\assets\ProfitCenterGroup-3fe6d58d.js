import{cs as M,A as Me,k as Te,O as l,an as Q,d as D,r as s,a as Se,b as Ge,t as U,o as xe,n as T,s as ye,g as Ne,E as be,cc as Ie,ct as Pe,bA as Re,cu as Oe,c as i,j as t,N as ve,aZ as Le,Q as ke,U as Fe,V as He,W as Ue,i as Be,X as B,Y as w,Z as $,$ as W,a7 as we,ab as z,ac as $e,ad as We,F as ze,b1 as je,ag as j,ah as Ye,bT as qe,C as _,bU as S,x as Y,bI as q,w as Je,aE as Ke,aG as Ve,bJ as G,J as Qe}from"./index-f7d9b065.js";import{S as J}from"./SingleSelectDropdown-aee403d4.js";import{R as Ze}from"./ReusableHIerarchyTree-7527be6c.js";import{R as Xe}from"./ReusablePresetFilter-36c3fb2e.js";import"./context-4d14404f.js";import"./EyeOutlined-0bb7ab85.js";import"./EditOutlined-9d614b39.js";import"./asyncToGenerator-88583e02.js";import"./ArrowLeftOutlined-a31a51d5.js";import"./index-190fd75d.js";const et=M(Me)(({theme:h})=>({minHeight:"2rem !important",margin:"0px !important",backgroundColor:h.palette.primary.light,borderRadius:"8px 8px 0 0",transition:"all 0.2s ease-in-out","&:hover":{backgroundColor:`${h.palette.primary.light}20`}})),tt=M(Te)(({theme:h})=>({marginTop:"0px !important",border:`1px solid ${h.palette.primary.main}`,borderRadius:"8px",boxShadow:"0 2px 4px rgba(0,0,0,0.05)","&:not(:last-child)":{borderBottom:0},"&:before":{display:"none"}})),ot=M(l)({display:"flex",justifyContent:"flex-end",paddingRight:"0.75rem",paddingBottom:"0.75rem",paddingTop:"0rem",gap:"0.5rem"}),K=M(Q)({borderRadius:"4px",padding:"4px 12px",textTransform:"none",fontSize:"0.875rem"}),V=M(D)(({theme:h})=>({fontSize:"0.75rem",color:h.palette.primary.dark,marginBottom:"0.25rem",fontWeight:500})),Gt=()=>{var F,H;let h=s.useRef(null);const{t:c}=Se(),x=Ge(),[Z,rt]=s.useState(!1),[X,at]=s.useState(""),[nt,ee]=s.useState(!1),[st,I]=s.useState(!1),[lt,te]=s.useState({code:"ETCA",desc:"ET FAMILY CO AREA"}),[it,oe]=s.useState(null),[ct,re]=s.useState(null),ae=U.useRef(null),[ne,P]=s.useState(!1),[se,dt]=s.useState(""),[le,ut]=s.useState(),[ie,pt]=s.useState(""),[y,ce]=s.useState([]),{getDtCall:de,dtData:E}=xe(),[gt,ue]=s.useState(),[m,R]=s.useState([{id:"1",title:"",child:[],tags:[],description:""}]);let N=T(e=>{var o;return(o=e.userManagement.entitiesAndActivities)==null?void 0:o.Material});const[ft,pe]=s.useState(m.length===0||!((F=m[0])!=null&&F.title)),ge=T(e=>e.applicationConfig),O=()=>{var r,u,C,p;pe(!1),I(!0);var e={node:((r=n==null?void 0:n.profitCenterGroup)==null?void 0:r.code)===""?"":(u=n==null?void 0:n.profitCenterGroup)==null?void 0:u.code,controllingArea:((C=n==null?void 0:n.controllingArea)==null?void 0:C.code)===""?"":(p=n==null?void 0:n.controllingArea)==null?void 0:p.code,classValue:"0106",id:"",screenName:"Display"};const o=f=>{console.log(f.body,"data");let g=[];f.statusCode===200&&(g.push(f.body.HierarchyTree),R(g)),R(g),I(!1)},a=f=>{console.log(f)};_(`/${S}/data/displayHierarchyTreeNodeStructure`,"post",o,a,e)},d=ye(),fe=Ne(),A=T(e=>{var o;return(o=e==null?void 0:e.AllDropDown)==null?void 0:o.dropDown}),n=T(e=>e.commonFilter.HierarchyNodeProfitCenter);console.log("pcSearchForm",n);const he=e=>{var o=e;let a={...n,controllingArea:o};d(Y({module:"HierarchyNodeProfitCenter",filterData:a})),v(e.code)},Ce=e=>{console.log("value",e);var o=e;console.log("tempProfitCenterGroup",o);let a={...n,profitCenterGroup:o};d(Y({module:"HierarchyNodeProfitCenter",filterData:a}))},me=e=>{console.log("first",e);const o=r=>{d(G({keyName:"COAREA",data:r.body}))},a=r=>{console.log(r)};_(`/${S}/data/getControllingArea`,"get",o,a)},Ae=()=>{let e={decisionTableId:null,decisionTableName:"MDG_CUSTOM_DROPDOWN_LIST",version:"v1",rulePolicy:null,validityDate:null,conditions:[{"MDG_CONDITIONS.MDG_MODULE":"PCG","MDG_CONDITIONS.MDG_FIELD_NAME":"Controlling Area"}],systemFilters:null,systemOrders:null,filterString:null};const o=r=>{var u,C;if(r.statusCode===200){const p=((C=(u=r==null?void 0:r.data)==null?void 0:u.result[0])==null?void 0:C.MDG_CUSTOM_LOOKUP_ACTION_TYPE)||[];console.log("questionData",p);let f=[];p==null||p.map(g=>{let b={};b.code=g==null?void 0:g.MDG_LOOKUP_CODE,b.desc=g==null?void 0:g.MDG_LOOKUP_DESC,f.push(b)}),console.log(f,"lookupDataArr"),d(G({keyName:"NewControllingArea",data:f}))}},a=r=>{console.log(r)};ge.environment==="localhost"?_(`/${q}/rest/v1/invoke-rules`,"post",o,a,e):_(`/${q}/v1/invoke-rules`,"post",o,a,e)},_e=e=>{console.log("first",e);const o=r=>{d(G({keyName:"PRCTRGroup",data:r.body}))},a=r=>{console.log(r)};_(`/${S}/node/getZeroLevelNodes?controllingArea=${e}`,"get",o,a)},v=e=>{console.log("first",e);const o=r=>{d(G({keyName:"PRCTRGroupFilter",data:r.body}))},a=r=>{console.log(r)};_(`/${S}/data/getProfitCtrGroup?controllingArea=${e}`,"get",o,a)},L=()=>{d(Je({module:"HierarchyNodeProfitCenter"}))};s.useEffect(()=>{me(),_e("ETCA"),v("ETCA"),Ae(),d(be()),d(Ie()),d(Pe()),d(Re()),d(Oe({module:"HierarchyNodeProfitCenter",groupName:"profitCenterGroup"}))},[]);const Ee=()=>{P(!0)},k=()=>{P(!1)};s.useEffect(()=>{te({code:"ETCA",desc:"ET FAMILY CO AREA"}),oe(null),re(null)},[]);const De=()=>{let e={decisionTableId:null,decisionTableName:Qe.MDG_MAT_SEARCHSCREEN_PARAMETER,version:"v2",conditions:[{"MDG_CONDITIONS.MDG_MAT_REGION":"US","MDG_CONDITIONS.MDG_MODULE":"Profit Center Group","MDG_CONDITIONS.MDG_MAT_SEARCHTYPE":"Master Data","MDG_CONDITIONS.MDG_MAT_VIEW_TYPE":"NA"}]};de(e)};return s.useEffect(()=>{var e,o;if(E){const a=(o=(e=E==null?void 0:E.result)==null?void 0:e[0])==null?void 0:o.MDG_MAT_SEARCHSCREEN_PARAMETER_ACTION_TYPE,r=a==null?void 0:a.filter(u=>u.MDG_MAT_FILTER_TYPE==="Additional").map(u=>({title:c(u.MDG_MAT_UI_FIELD_NAME)}));ce(a),ue(r)}},[E]),s.useEffect(()=>{De()},[]),i("div",{ref:h,children:[t(Ke,{dialogState:ne,openReusableDialog:Ee,closeReusableDialog:k,dialogTitle:se,dialogMessage:le,handleDialogConfirm:k,dialogOkText:"OK",dialogSeverity:ie}),t(Ve,{blurLoading:Z,loaderMessage:X}),t("div",{style:{...ve,backgroundColor:"#FAFCFF"},children:i(Le,{spacing:1,children:[t(l,{container:!0,mt:0,sx:ke,children:i(l,{item:!0,md:5,children:[t(D,{variant:"h3",children:t("strong",{children:c("Profit Center Group")})}),t(D,{variant:"body2",color:"#777",children:c("This view displays the selected Profit Center Hierarchy")})]})}),t(l,{container:!0,sx:Fe,children:t(l,{item:!0,md:12,children:i(tt,{defaultExpanded:!0,className:"filterPCG",children:[i(et,{expandIcon:t(He,{sx:{fontSize:"1.25rem",color:x.palette.primary.dark}}),"aria-controls":"panel1a-content",id:"panel1a-header",children:[t(Ue,{sx:{fontSize:"1.25rem",marginRight:1,color:x.palette.primary.dark}}),t(D,{sx:{fontSize:"0.875rem",fontWeight:600,color:x.palette.primary.dark},children:c("Filter Profit Center Group")})]}),i(Be,{sx:{padding:"1rem 1rem 0.5rem"},children:[t(l,{container:!0,rowSpacing:1,spacing:2,justifyContent:"space-between",alignItems:"center",children:t(l,{container:!0,spacing:1,sx:{padding:"0rem 1rem 0.5rem"},children:y==null?void 0:y.filter(e=>e.MDG_MAT_VISIBILITY!=="Hidden").sort((e,o)=>e.MDG_MAT_SEQUENCE_NO-o.MDG_MAT_SEQUENCE_NO).map((e,o)=>{var a,r,u,C;return i(U.Fragment,{children:[(e==null?void 0:e.MDG_MAT_JSON_FIELD_NAME)===B.CONTROLINGAREA&&i(l,{item:!0,md:2,children:[i(V,{sx:w,children:[c(e==null?void 0:e.MDG_MAT_UI_FIELD_NAME)," ",t("span",{style:{color:(r=(a=$)==null?void 0:a.error)==null?void 0:r.dark},children:"*"})]}),t(W,{size:"small",fullWidth:!0,children:t(J,{options:(A==null?void 0:A.COAREA)??[],value:n==null?void 0:n.controllingArea,onChange:p=>{he(p)},placeholder:c("SELECT CONTROLLING AREA"),disabled:!1,minWidth:"90%",listWidth:210})})]}),(e==null?void 0:e.MDG_MAT_JSON_FIELD_NAME)===B.CCGROUP&&i(l,{item:!0,md:2,children:[i(V,{sx:w,children:[c(e==null?void 0:e.MDG_MAT_UI_FIELD_NAME)," ",t("span",{style:{color:(C=(u=$)==null?void 0:u.error)==null?void 0:C.dark},children:"*"})]}),t(W,{size:"small",fullWidth:!0,children:t(J,{options:(A==null?void 0:A.PRCTRGroupFilter)??[],value:n==null?void 0:n.profitCenterGroup,onChange:p=>{Ce(p)},placeholder:c("SELECT PROFIT CENTER GROUP"),disabled:!1,minWidth:"90%",listWidth:210})})]})]},o)})})}),i(ot,{children:[t(K,{variant:"outlined",size:"small",startIcon:t(we,{sx:{fontSize:"1rem"}}),onClick:()=>{L()},children:c("Clear")}),t(l,{sx:{...z},children:t(Xe,{moduleName:"MaterialMaster",handleSearch:O,onPresetActiveChange:e=>ee(e),onClearPreset:L})}),t(K,{variant:"contained",size:"small",startIcon:t($e,{sx:{fontSize:"1rem"}}),sx:{...We,...z},onClick:()=>{O()},children:c("Search")})]})]})]})})}),t(ze,{children:m.length>0&&((H=m[0])==null?void 0:H.label)&&t(l,{container:!0,children:t(l,{item:!0,md:12,sx:{backgroundColor:"white",maxHeight:"max-content",height:"max-content",borderRadius:"8px",border:"1px solid #E0E0E0",mt:.25,boxShadow:"0px 2px 14px 0px rgba(48, 38, 185, 0.10)",...je},children:t(l,{container:!0,display:"flex",flexDirection:"row",flexWrap:"nowrap",children:i(l,{item:!0,md:12,children:[m[0]?t(l,{item:!0,sx:{display:"flex",justifyContent:"space-between"}}):"",t(l,{children:t(D,{variant:"body1",fontWeight:"bold",justifyContent:"flex-start",marginBottom:2,children:c("Existing Structure in SAP")})}),t(Ze,{initialRawTreeData:m,editmode:!1,object:"Profit Center"})]})})})})}),t(j,{sx:{position:"fixed",bottom:0,left:0,right:0},elevation:2,children:(N==null?void 0:N.length)>0&&t(j,{sx:{position:"fixed",bottom:0,left:0,right:0},elevation:2,children:i(Ye,{className:"container_BottomNav",showLabels:!0,sx:{display:"flex",justifyContent:"flex-end",gap:1},children:[t(Q,{size:"small",variant:"contained",onClick:()=>{fe("/requestBench/ProfitCenterGroupRequestTab")},className:"createRequestButtonPCG",children:c("Create Request")}),t(qe,{variant:"contained",ref:ae,"aria-label":"split button"})]})})})]})})]})};export{Gt as default};
