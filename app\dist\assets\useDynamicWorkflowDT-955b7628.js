import{aO as A,n as w,C as L,bI as G,aT as P,aJ as U}from"./index-f7d9b065.js";var m,S;const Y={[(m=A)==null?void 0:m.BK]:"MDG_BNKY_DYNAMIC_WORKFLOW_DT_ACTION_TYPE",[(S=A)==null?void 0:S.BOM]:"MDG_MAT_DYNAMIC_WF_DT",[A.MAT]:"MDG_MAT_DYNAMIC_WF_DT",[A.ART]:"MDG_MAT_DYNAMIC_WF_DT"},K=()=>{const h=w(i=>i.applicationConfig);return{getDynamicWorkflowDT:(i,R,l="NA",W="GROUP-2",c=1,u,d,T)=>new Promise((E,I)=>{let O={decisionTableId:null,decisionTableName:d,version:u,conditions:[{"MDG_CONDITIONS.MDG_MAT_REQUEST_TYPE":i||"","MDG_CONDITIONS.MDG_MAT_REGION":R||"","MDG_CONDITIONS.MDG_MAT_TEMPLATE":l||"NA","MDG_CONDITIONS.MDG_MAT_BIFURCATION_GROUP":W||""}]};const N=e=>{var p,C;if(e.statusCode===U.STATUS_200){let s;if(T){const r=Y[T]||T;s=((C=(p=e==null?void 0:e.data)==null?void 0:p.result[0])==null?void 0:C[r])||[];const D=new Map;s.forEach(M=>{if(M.MDG_DYNAMIC_WF_APPROVAL_LEVEL===parseInt(c)){const o=M.MDG_MAT_SENDBACK_ALLOWED;typeof o=="string"&&o.trim().length>0&&o.split(",").map(a=>{const n=a.trim().match(/^(-?\d+)-(.*)$/);return n?{key:parseInt(n[1],10),Name:n[2].trim()}:null}).filter(Boolean).forEach(({key:a,Name:n})=>{D.has(a)||D.set(a,n)})}});const _=Array.from(D,([M,o])=>({key:M,Name:o}));E(_);return}let t=[];s==null||s.forEach(r=>{r.MDG_DYNAMIC_WF_APPROVAL_LEVEL===parseInt(c)&&r.MDG_MAT_SENDBACK_ALLOWED.split(",").map(_=>parseInt(_)).forEach(_=>t.push(_))}),t=[...new Set(t)],E(t)}else I(new Error("Failed to fetch workflow levels"))},f=e=>{I(e)};h.environment==="localhost"?L(`/${G}${P.INVOKE_RULES.LOCAL}`,"post",N,f,O):L(`/${G}${P.INVOKE_RULES.PROD}`,"post",N,f,O)})}};export{K as u};
