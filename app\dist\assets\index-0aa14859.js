import{pG as t,pH as a,pI as s,b6 as o,pJ as l,pK as r,hD as e}from"./index-f7d9b065.js";const i=Object.freeze(Object.defineProperty({__proto__:null,default:t,getSwitchUtilityClass:a,switchClasses:s},Symbol.toStringTag,{value:"Module"})),c=Object.freeze(Object.defineProperty({__proto__:null,default:o,getTabUtilityClass:l,tabClasses:r},Symbol.toStringTag,{value:"Module"})),b=e(c),n=e(i);export{b as a,n as r};
