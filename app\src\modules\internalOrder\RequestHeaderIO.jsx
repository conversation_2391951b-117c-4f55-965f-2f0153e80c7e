import React, { useEffect, useState } from "react";
import { Box, Button, Grid, Stack, Typography } from "@mui/material";
import { useDispatch, useSelector } from "react-redux";
import { useLocation, useNavigate } from "react-router-dom";
import { container_Padding } from "@components/Common/commonStyles";
import useLang from "@hooks/useLang";
import { ToastContainer } from "react-toastify";
import { REQUEST_HEADER_FILED_NAMES, REQUEST_PRIORITY, VISIBILITY_TYPE, MODULE, SUCCESS_MESSAGES, REQUEST_TYPE_OPTIONS_IO, REGION, ERROR_MESSAGES, API_CODE, REQUEST_TYPE, MODULE_MAP } from "@constant/enum";
import { setDropDownDataIO, updateModuleFieldDataIO, setRequestHeaderDTIO, setSavedReqData, setTabValue, setIOPayloadFields, setIOpayloadData } from "./slice/InternalOrderSlice";
import { doAjax } from "@components/Common/fetchService";
import { END_POINTS } from "@constant/apiEndPoints";
import FilterFieldGlobal from "@components/MasterDataCockpit/FilterFieldGlobal";
import { destination_InternalOrder } from "../../destinationVariables";
import { useSnackbar } from "@hooks/useSnackbar";
import useDownloadExcel from "@hooks/useDownloadExcel";
import useRequestHeaderFieldsIO from "./hooks/useRequestHeaderFieldsIO";
import DownloadDialog from "@components/Common/Downloaddialog";
import { APP_END_POINTS } from "@constant/appEndPoints";
import useInternalOrderChangeFieldconfig from "./hooks/useInternalOrderChangeFieldconfig";

const RequestHeaderIO = ({ setIsSecondTabEnabled, setIsAttachmentTabEnabled, setDownloadClicked, downloadClicked }) => {
  const { t } = useLang();
  const { handleDownload, handleEmailDownload } = useDownloadExcel(MODULE?.IO);
  const [downloadType, setDownloadType] = useState("systemGenerated");
  const [openDownloadDialog, setOpenDownloadDialog] = useState(false);
  const [loaderMessage, setLoaderMessage] = useState("");
  const [blurLoading, setBlurLoading] = useState(false);
  const dispatch = useDispatch();
  const location = useLocation();
  const queryParams = new URLSearchParams(location.search);
  const isWorkspace = queryParams.get("RequestId");
  const isreqBench = queryParams.get("reqBench");
  const navigate = useNavigate();

  const requestHeaderFields = useSelector((state) => state.internalOrder.requestHeaderDTIO);
  const payloadFields = useSelector((state) => state.internalOrder.IOpayloadData);
  const requestHeaderData = useSelector((state) => state.request.requestHeader);
  const savedReqData = useSelector((state) => state.internalOrder.savedReqData);
  const userData = useSelector((state) => state.userManagement.userData);
  const { showSnackbar } = useSnackbar();
  const { getChangeTemplate } =useInternalOrderChangeFieldconfig();

   const templateNames = [
    { code: "Description Change", desc: "" },
    { code: "General Data", desc: "" },
    { code: "Assignments", desc: "" },
  ];

  // Use hook to fetch header fields whenever request type changes
  // Hook gets request type directly from Redux state
  useRequestHeaderFieldsIO({
    requestId: isWorkspace,
    reqBench: isreqBench,
  });

  dispatch(setDropDownDataIO({ keyName: "RequestPriority", data: REQUEST_PRIORITY }));
  dispatch(setDropDownDataIO({ keyName: REQUEST_HEADER_FILED_NAMES?.REGION, data: REGION }));
  dispatch(setDropDownDataIO({ keyName: "TemplateName", data: templateNames }));
  dispatch(
    setDropDownDataIO({
      keyName: REQUEST_HEADER_FILED_NAMES?.REQUEST_TYPE,
      data: REQUEST_TYPE_OPTIONS_IO,
    })
  );
  useEffect(() => {

    if (!isWorkspace && !isreqBench && userData?.user_id && requestHeaderFields?.['Header Data']?.length>0) {
      const currentCreatedBy = payloadFields?.requestHeaderData?.ReqCreatedBy;

      if (!currentCreatedBy) {
        dispatch(updateModuleFieldDataIO({ keyName: "ReqCreatedBy", data: userData?.user_id }));
      }

      dispatch(updateModuleFieldDataIO({ keyName: "RequestStatus", data: "DRAFT" }));
      dispatch(setIOPayloadFields({ keyName: "RequestStatus", data: "DRAFT" }));
    }
  }, [isWorkspace, isreqBench, userData?.user_id,requestHeaderFields]);

  useEffect(() => {
    if (payloadFields?.requestHeaderData?.RequestType === "Change" || payloadFields?.requestHeaderData?.RequestType === "Change with Upload") {
      getChangeTemplate(payloadFields?.requestHeaderData?.TemplateName);
    }
 
  }, [payloadFields?.requestHeaderData?.RequestType]);

useEffect(() => {
  if (payloadFields?.requestHeaderData?.TemplateName) {
    getChangeTemplateFields(payloadFields?.requestHeaderData?.TemplateName);
  }
}, [payloadFields?.requestHeaderData?.TemplateName]);
const templateFullData = useSelector((state) => state?.app?.changeFieldSelectionData || []);
const getChangeTemplateFields = (selectedTemplateName) => {
  const filteredAndSortedFields = templateFullData
    .filter((item) => item?.MDG_CHANGE_TEMPLATE_NAME === selectedTemplateName && item?.MDG_MAT_CHANGE_TYPE === "Item" && item?.MDG_MAT_FIELD_VISIBILITY !== "Hidden" && item?.MDG_MAT_FIELD_VISIBILITY !== "Display")
    .sort((a, b) => {
      const seqA = Number(a?.MDG_MAT_FIELD_SEQUENCE) || 0;
      const seqB = Number(b?.MDG_MAT_FIELD_SEQUENCE) || 0;
      return seqA - seqB;
    });

  const uniqueFieldNames = [...new Set(filteredAndSortedFields.map((item) => item?.MDG_MAT_FIELD_NAME).filter(Boolean))].map((field) => ({ code: field }));

  dispatch(
    setDropDownDataIO({
      keyName: "FieldName",
      data: uniqueFieldNames || [],
    })
  );
};

  useEffect(() => {
    if (downloadClicked) {
      if (payloadFields?.requestHeaderData?.RequestType === REQUEST_TYPE.CREATE_WITH_UPLOAD) {
        setOpenDownloadDialog(true);
        return;
      }
      if (payloadFields?.requestHeaderData?.RequestType === REQUEST_TYPE?.CHANGE_WITH_UPLOAD) {
        return;
      }
    }
  }, [downloadClicked]);
  const checkAllFieldsFilled = () => {
    const fields = requestHeaderFields[Object.keys(requestHeaderFields)[0]];
    if (!fields?.length) return false;
    return fields.every((field) => field.visibility !== VISIBILITY_TYPE?.MANDATORY || Boolean(payloadFields?.requestHeaderData?.[field.jsonName]));
  };
  const onDownloadTypeChange = () => {
    if (downloadType === "systemGenerated") {
      handleDownload(setLoaderMessage, setBlurLoading, payloadFields, MODULE_MAP?.IO);
      handleDownloadDialogClose();
    }
    if (downloadType === "mailGenerated") {
      handleEmailDownload(setLoaderMessage, setBlurLoading, payloadFields, MODULE_MAP?.IO);
      handleDownloadDialogClose();
    }
  };
  const handleDownloadTypeChange = (event) => {
    setDownloadType(event?.target?.value);
  };

  const handleDownloadDialogClose = () => {
    setDownloadClicked(false);
    setOpenDownloadDialog(false);
    setDownloadType("systemGenerated");
    if (!isWorkspace) {
      navigate(APP_END_POINTS?.REQUEST_BENCH);
    }
  };

  const handleButtonClick = async () => {
    const headerData = payloadFields?.requestHeaderData || {};
    const now = new Date().toISOString();

    const payload = {
      RequestId: "",
      ReqCreatedBy: userData?.user_id || "",
      ReqCreatedOn: headerData?.ReqCreatedOn ? new Date(headerData.ReqCreatedOn).toISOString() : now,
      ReqUpdatedOn: now,
      RequestType: headerData?.RequestType || "",
      RequestPriority: headerData?.RequestPriority || "Medium",
      RequestDesc: headerData?.RequestDesc || "",
      RequestStatus: headerData?.RequestStatus || "DRAFT",
      TemplateName: "",
      FieldName: "",
      Region: headerData?.Region || "US",
      IsBifurcated: headerData?.IsBifurcated ?? false,
    };

    const hSuccess = (data) => {
      if (data?.status === true || data?.statusCode === API_CODE.STATUS_200 || data?.status === "success") {
        showSnackbar(`${t(SUCCESS_MESSAGES.REQUEST_HEADER_CREATED)} ${data?.body?.RequestId || data?.RequestId || ""}`, "success");
        dispatch(setSavedReqData(data.body || data));
        dispatch(setIOpayloadData({ keyName: "requestHeaderData", data: { ...(data.body || data), RequestId: data?.body?.RequestId || data?.RequestId } }));
        if (setIsAttachmentTabEnabled) setIsAttachmentTabEnabled(true);
        if (setIsSecondTabEnabled) setIsSecondTabEnabled(true);
        if (payloadFields?.requestHeaderData?.RequestType === REQUEST_TYPE.CREATE_WITH_UPLOAD) {
          setOpenDownloadDialog(true);
          return;
        }
        if (payloadFields?.RequestType === REQUEST_TYPE?.CHANGE_WITH_UPLOAD) {
          return;
        }
        dispatch(setTabValue(1));
      } else {
        showSnackbar(ERROR_MESSAGES.ERROR_REQUEST_HEADER, "error");
      }
    };

    const hError = () => {
      showSnackbar(ERROR_MESSAGES.ERROR_REQUEST_HEADER, "error");
    };

    try {
      doAjax(`/${destination_InternalOrder}${END_POINTS.MASS_ACTION.CREATE_IO_REQUEST}`, "post", hSuccess, hError, payload);
    } catch {
      showSnackbar(ERROR_MESSAGES.ERROR_REQUEST_HEADER, "error");
    }
  };

  return (
    <div>
      <Stack spacing={2}>
        {Object.entries(requestHeaderFields).map(([key, fields]) => (
          <Grid
            item
            md={12}
            key={key}
            sx={{
              backgroundColor: "white",
              borderRadius: "8px",
              border: "1px solid #E0E0E0",
              mt: 0.25,
              boxShadow: "0px 2px 14px 0px rgba(48, 38, 185, 0.10)",
              ...container_Padding,
            }}
          >
            <Typography sx={{ fontSize: "12px", fontWeight: "700", paddingBottom: "10px" }}>{t(key)}</Typography>
            <Box>
              <Grid container spacing={1}>
                {Array.isArray(fields) &&
                  fields
                    .filter((field) => field.visibility !== VISIBILITY_TYPE.HIDDEN)
                    .sort((a, b) => a.sequenceNo - b.sequenceNo)
                    .map((innerItem) => <FilterFieldGlobal isHeader={true} key={innerItem.jsonName} field={innerItem} dropDownData={{}} module={MODULE.IO} disabled={isWorkspace || requestHeaderData?.requestId || savedReqData?.RequestId} requestHeader={true} />)}
              </Grid>
            </Box>
            {!isWorkspace && !requestHeaderData?.requestId && !savedReqData?.RequestId &&  (
              <Box sx={{ display: "flex", justifyContent: "flex-end", marginTop: "20px" }}>
                <Button variant="contained" color="primary" onClick={handleButtonClick} disabled={!checkAllFieldsFilled()}>
                  {t("Save Request Header")}
                </Button>
              </Box>
            )}
            <ToastContainer />
            <DownloadDialog onDownloadTypeChange={onDownloadTypeChange} open={openDownloadDialog} downloadType={downloadType} handleDownloadTypeChange={handleDownloadTypeChange} onClose={handleDownloadDialogClose} />
          </Grid>
        ))}
      </Stack>
    </div>
  );
};

export default RequestHeaderIO;
