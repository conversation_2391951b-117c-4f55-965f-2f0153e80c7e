import{r as e,cb as i}from"./index-f7d9b065.js";import{i as s}from"./index-c3f8f9be.js";import"./react-beautiful-dnd.esm-6b676f13.js";import"./redux-dc18bb29.js";import"./index-6a70352f.js";import"./index-0aa14859.js";import"./Check-87f6ada7.js";import"./FileUploadOutlined-4a68a28a.js";import"./DeleteOutline-584dc929.js";import"./Delete-5278579a.js";import"./asyncToGenerator-88583e02.js";import"./FileDownloadOutlined-59854a55.js";import"./AddOutlined-9a9caebd.js";import"./DeleteOutlineOutlined-8fd07dc7.js";import"./EditOutlined-a6f382b7.js";import"./Edit-51c94b76.js";import"./index-67deb11b.js";import"./index-ae6cbb07.js";import"./DataObject-52409c14.js";import"./lz-string-0665f106.js";import"./VisibilityOutlined-b6cd6d28.js";import"./Remove-82c67208.js";import"./ChevronRight-a85c6b03.js";import"./index-7ffbe79f.js";import"./DeleteOutlined-e668453f.js";import"./index-6362276a.js";import"./History-13dab512.js";let p=class extends e.Component{constructor(r){super(r),this.state={hasError:!1}}static getDerivedStateFromError(){return{hasError:!0}}componentDidCatch(r,o){console.error("Error caught in ErrorBoundary:",r,o)}render(){return this.state.hasError?i.jsx("div",{children:"Error fetching data"}):this.props.children}};const k=({roleDetails:t,destinations:r})=>i.jsx(p,{children:t&&i.jsx(s.TextRules,{orchestration:!1,ruleDetails:t,destinations:r,saveHandler:o=>console.log(o),translationDataObjects:[]})});export{k as default};
