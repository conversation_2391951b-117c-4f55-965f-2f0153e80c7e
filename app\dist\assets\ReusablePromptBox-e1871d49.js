import{j as e,n as v,c as a,O as s,aj as B,R as y,a6 as N,al as z,S as F,d as O,$ as U,aa as w,am as G,an as u,ai as H,F as $,dW as q}from"./index-f7d9b065.js";import{f as D}from"./featureConfig-652a9f8d.js";function J({promptState:n,setPromptState:o,handlePromptClose:i,onCloseAction:l,promptMessage:c,showInputText:d,inputText:R,setInputText:W,dialogInputPlaceholder:j,DialogMessageContent:h,dialogSeverity:f,dialogTitleText:g,handleCancelButtonAction:x,cancelButtonText:I,showCancelButton:m,handleOkButtonAction:C,okButtonText:S,showOkButton:b,handleExtraButtonAction:k,extraButtonText:T,showExtraButton:p}){v(t=>t.userManagement.userData);const r=()=>{o(!1)};return e($,{children:a(H,{hideBackdrop:!1,elevation:2,PaperProps:{sx:{boxShadow:"none",minWidth:450}},open:n,onClose:()=>{l?l():i?i():r()},children:[a(s,{container:!0,sx:{display:"flex",justifyContent:"space-between"},children:[g&&e(s,{item:!0,children:a(B,{id:"alert-dialog-title",sx:{fontWeight:600,display:"flex",alignItems:"center",justifyContent:"center",textAlign:"center",fontSize:"16px"},children:[f&&a("span",{style:{display:"flex",alignItems:"center"},children:[e(y,{iconName:D.severityIcons[f.toUpperCase()].iconName,iconColor:D.severityIcons[f.toUpperCase()].iconColor}),"  "]}),g]})}),e(s,{item:!0,sx:{padding:"12px"},children:e(N,{onClick:t=>{t.stopPropagation(),i?i():r()},children:e(y,{iconName:"Close"})})})]}),e(z,{children:a(F,{children:[e(s,{container:!0,children:a(s,{item:!0,md:12,sx:{padding:"0px 20px 20px 0px",textAlign:"left"},children:[h&&e(h,{}),c&&e(O,{children:c})]})}),d&&e(U,{sx:{height:"auto"},fullWidth:!0,children:e(w,{sx:{backgroundColor:"#F5F5F5"},value:R,onChange:t=>W(t.target.value),multiline:!0,placeholder:j})})]})}),(m||b||p)&&a(G,{sx:{paddingRight:"1.5rem"},children:[m&&e(u,{variant:"text",sx:{height:40,minWidth:"4rem",textTransform:"none",borderColor:"#3B30C8",color:"#3B30C8"},onClick:()=>{x?x():i?i():r()},children:I??"Cancel"}),b&&e(u,{variant:"contained",style:{height:40,minWidth:"4rem",backgroundColor:"#3B30C8",textTransform:"none"},onClick:()=>{C?C():i?i():r()},children:S??"Ok"}),p&&e(u,{variant:"contained",style:{height:40,minWidth:"4rem",backgroundColor:"#3B30C8",textTransform:"none"},onClick:()=>{k&&k(),i?i():r()},children:T??"Ok"})]})]})})}function K({promptState:n,setPromptState:o,handleSnackBarPromptClose:i,promptMessage:l}){v(d=>d.userManagement.userData);const c=()=>{o(!1)};return e(F,{spacing:2,sx:{width:"100%"},children:e(q,{autoHideDuration:5e3,anchorOrigin:{vertical:"top",horizontal:"center"},open:n,onClose:()=>{i?i():c()},message:l,sx:{height:"150px"}})})}function Q(n){switch(n==null?void 0:n.type){case"dialog":return e(J,{...n});case"snackbar":return e(K,{...n})}}export{J as R,Q as a};
