import{r as G,c as s,ai as T,aj as D,B as w,j as t,gs as P,d as o,a6 as S,af as V,al as v,ag as j,an as z,am as H,F,bE as I,T as d,qY as a}from"./index-f7d9b065.js";import{C as N,E as R}from"./EyeOutlined-0bb7ab85.js";const K=({open:k,onClose:b,data:h})=>{const[x,g]=G.useState({show:!1,content:"",title:""}),O=e=>{if(!e||!e.includes("Task Details -"))return null;const l=e.replace("Task Details - ",""),r={};let i=0;for(;i<l.length;){let f=i,C=l.indexOf("=",f);if(C===-1)break;let p=l.substring(f,C).trim();p.startsWith(",")&&(p=p.substring(1).trim());let m=C+1,u;if(l[m]==="["){let c=0,n=m;for(;n<l.length;){if(l[n]==="["&&c++,l[n]==="]"&&c--,c===0){u=n+1;break}n++}}else{let c=0,n=m;for(;n<l.length;){if(l[n]==="["&&c++,l[n]==="]"&&c--,l[n]===","&&c===0){u=n;break}n++}u===void 0&&(u=l.length)}let W=l.substring(m,u).trim();r[p]=W,i=u+1,i<l.length&&l[i]===","&&i++}return r},A=e=>{if(!e)return"";const l=r=>{if(r&&r.startsWith("[")&&r.endsWith("]"))try{return JSON.parse(r).map(f=>`${a.LEVEL} - ${f}`).join(", ")}catch{return r}return r&&r.includes(",")&&!r.startsWith("[")?r.split(",").map(i=>`${a.LEVEL} - ${i.trim()}`).join(", "):r};return Object.entries(e).map(([r,i])=>{const f=r==="WORKFLOW_DATA_CONSTANTS.SENDBACKALLOWED"?l(i):i;return`${r}: ${f}`}).join(`
`)},L=(e,l)=>{const r=O(e);if(r){const i=A(r);g({show:!0,content:i,title:l})}},y=e=>new Date(e).toLocaleString(),_=e=>{switch(e){case a.CHANGE_LOGS_TYPES.ADDED:return{color:"success",bgcolor:"#f6ffed",textColor:"#52c41a"};case a.CHANGE_LOGS_TYPES.REMOVED:return{color:"error",bgcolor:"#fff2f0",textColor:"#ff4d4f"};case a.CHANGE_LOGS_TYPES.CHANGED:return{color:"primary",bgcolor:"#f0f5ff",textColor:"#1890ff"};default:return{color:"default",bgcolor:"#fafafa",textColor:"#666666"}}},E=(e,l=!0)=>{const r=l?e.row.oldValue:e.row.newValue,i=e.row.operationType;return(i===a.CHANGE_LOGS_TYPES.ADDED||i===a.CHANGE_LOGS_TYPES.REMOVED)&&r&&r.includes("Task Details -")?s(w,{display:"flex",alignItems:"center",gap:1,children:[t(S,{size:"small",onClick:()=>L(r,`${i} - Task Details`),sx:{p:.5},children:t(R,{style:{color:"#1890ff",fontSize:"16px"}})}),t(o,{variant:"caption",color:"textSecondary",children:"View Details"})]}):i===a.CHANGE_LOGS_TYPES.ADDED&&l?t(o,{variant:"body2",color:"textSecondary",fontStyle:"italic",children:"-"}):i===a.CHANGE_LOGS_TYPES.REMOVED&&!l?t(o,{variant:"body2",color:"textSecondary",fontStyle:"italic",children:"-"}):t(o,{variant:"body2",children:r||t("span",{style:{color:"#999",fontStyle:"italic"},children:"-"})})};return s(F,{children:[s(T,{open:k,onClose:b,maxWidth:"lg",fullWidth:!0,PaperProps:{sx:{height:"90vh",maxHeight:"90vh"}},children:[s(D,{sx:{display:"flex",justifyContent:"space-between",alignItems:"center"},children:[s(w,{sx:{display:"flex",alignItems:"center",gap:"8px"},children:[t(P,{sx:{color:"black",fontSize:"20px","&:hover":{transform:"rotate(360deg)",transition:"0.9s"},textAlign:"center",marginTop:"4px"}}),t(o,{id:"modal-modal-title",variant:"subtitle1",fontSize:"16px",fontWeight:"bold",sx:{color:"black",paddingTop:"3px"},children:"Change Log"})]}),t(S,{onClick:b,size:"small",children:t(N,{})})]}),t(v,{sx:{p:0,height:"calc(100% - 64px)"},children:t(w,{sx:{height:"100%",width:"100%",padding:"10px"},children:t(V,{title:"Change Log History",rows:h||[],columns:[{field:"operationType",headerName:"Operation",width:120,renderCell:e=>{const l=_(e.value);return t(d,{title:e.value,arrow:!0,children:t(I,{label:e.value,size:"small",sx:{backgroundColor:l.bgcolor,color:l.textColor,fontWeight:500,fontSize:"0.75rem"}})})}},{field:"taskName",headerName:"Task Name",width:150,renderCell:e=>t(d,{title:e.value||"No task name",arrow:!0,children:t(o,{variant:"body2",children:e.value||"-"})})},{field:"fieldName",headerName:"Field Name",width:200,renderCell:e=>t(d,{title:e.value,arrow:!0,children:t(o,{variant:"body2",children:e.value})})},{field:"oldValue",headerName:"Old Value",width:150,renderCell:e=>t(d,{title:e.value||"No previous value",arrow:!0,children:E(e,!0)})},{field:"newValue",headerName:"New Value",width:150,renderCell:e=>t(d,{title:e.value||"No new value",arrow:!0,children:E(e,!1)})},{field:"changedBy",headerName:"Changed By",width:250,renderCell:e=>t(d,{title:e.value,arrow:!0,children:t(o,{variant:"body2",children:e.value})})},{field:"changedOn",headerName:"Changed On",width:180,renderCell:e=>t(d,{title:y(e.value),arrow:!0,children:t(o,{variant:"body2",children:y(e.value)})})}],getRowIdValue:"id",pageSize:10,rowsPerPageOptions:[5,10,20],disableSelectionOnClick:!0,hideFooter:!1,tempheight:"calc(100vh - 160px)",width:"100%",module:"changelog",showRefresh:!1,showSearch:!1,showExport:h&&h.length>0,showFilter:!0,showColumns:!0,checkboxSelection:!1,isLoading:!1,rowCount:(h==null?void 0:h.length)||0})})})]}),s(T,{open:x.show,onClose:()=>g({show:!1,content:"",title:""}),maxWidth:"sm",fullWidth:!0,children:[s(D,{sx:{display:"flex",justifyContent:"space-between",alignItems:"center",bgcolor:"#fafafa"},children:[t(o,{variant:"h6",component:"div",children:x.title}),t(S,{onClick:()=>g({show:!1,content:"",title:""}),size:"small",children:t(N,{})})]}),t(v,{children:s(w,{sx:{mt:2},children:[t(o,{variant:"subtitle2",sx:{mb:2,fontWeight:600},children:"Task Details:"}),t(j,{variant:"outlined",sx:{p:2,bgcolor:"#fafafa",fontFamily:"monospace",fontSize:"0.875rem",whiteSpace:"pre-wrap"},children:x.content})]})}),t(H,{children:t(z,{onClick:()=>g({show:!1,content:"",title:""}),variant:"outlined",children:"Close"})})]})]})};export{K as C};
