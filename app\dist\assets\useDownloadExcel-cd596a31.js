import{aO as x,dg as v,aT as f,bj as q,cw as j,g as y,aX as W,C as u,aW as p,dm as d,aY as $,ae as T,bK as b,aJ as G,gm as Q,gn as X}from"./index-f7d9b065.js";var M,g;const I={[(M=x)==null?void 0:M.BK]:{destination:v,dtName:"MDG_BNKY_FIELD_CONFIG",version:"v2",rolePrefix:"Z_FIN_REQ_DOWNLOAD",scenario:"Create with Upload",getPayload:C=>({requestId:(C==null?void 0:C.RequestId)||"",dtName:"MDG_BNKY_FIELD_CONFIG",version:"v2",region:(C==null?void 0:C.Region)||"US",bankCtry:(C==null?void 0:C.BankCtry)||"US",rolePrefix:"Z_FIN_REQ_DOWNLOAD",scenario:"Create with Upload"}),downloadEndpoints:{create:f.EXCEL.DOWNLOAD_EXCEL,change:f.EXCEL.DOWNLOAD_EXCEL_WITH_DATA,createMail:f.EXCEL.DOWNLOAD_EXCEL_MAIL,extendMail:f.EXCEL.DOWNLOAD_EXCEL_FOR_EXTEND_MAIL},uploadEndpoint:"getAllBankKeysFromExcel",method:"postandgetblob",mailMethod:"post"},[(g=x)==null?void 0:g.IO]:{destination:q,dtName:"MDG_INTORD_FIELD_CONFIG",version:"v2",scenario:"Create with Upload",getPayload:C=>{var r;return{dtName:"MDG_INTORD_FIELD_CONFIG",version:"v2",requestId:((r=C==null?void 0:C.requestHeaderData)==null?void 0:r.RequestId)||"",scenario:"Create with Upload",orderType:"ALL",region:(C==null?void 0:C.Region)||"US"}},downloadEndpoints:{create:"/api/v1/excel/downloadExcel",change:"/api/v1/excel/downloadExcel",createMail:f.EXCEL.DOWNLOAD_EXCEL_MAIL,extendMail:f.EXCEL.DOWNLOAD_EXCEL_FOR_EXTEND_MAIL},uploadEndpoint:"/api/v1/massAction/getAllInternalOrdersFromExcel",method:"postandgetblob",mailMethod:"post"},DEFAULT:{getPayload:()=>({}),downloadEndpoints:{create:f.EXCEL.DOWNLOAD_EXCEL_PCG_CREATE_WITH_UPLOAD,change:f.EXCEL.DOWNLOAD_EXCEL_PCG_CHANGE_WITH_UPLOAD,createMail:f.EXCEL.DOWNLOAD_EXCEL_PCG_CREATE_WITH_UPLOAD_MAIL,changeMail:f.EXCEL.DOWNLOAD_EXCEL_PCG_CHANGE_WITH_UPLOAD_MAIL},method:"getblobfile",mailMethod:"get"}},oe=C=>{var H,k;const{showSnackbar:r}=j(),S=y(),P=(k=(H=f)==null?void 0:H.MODULE_DESTINATION_MAP)==null?void 0:k[C];return{handleDownload:(_,c,n,i)=>{var a;_((a=p)==null?void 0:a.REPORT_LOADING),c(!0);const t=I[i]||I.DEFAULT,A=t.destination||P,D=O=>{var m,L;if((O==null?void 0:O.size)==0){c(!1),_(""),r((m=T)==null?void 0:m.DATA_NOT_FOUND_FOR_SEARCH,"error");return}const R=URL.createObjectURL(O),U=document.createElement("a"),N=(n==null?void 0:n.RequestType)===((L=W)==null?void 0:L.CHANGE_WITH_UPLOAD)?`${i}_Mass Change.xlsx`:`${i}_Mass Create.xlsx`;U.href=R,U.setAttribute("download",N),document.body.appendChild(U),U.click(),document.body.removeChild(U),URL.revokeObjectURL(R),c(!1),_(""),r(`${N} has been downloaded successfully.`,"success"),setTimeout(()=>{var w;S(`${(w=b)==null?void 0:w.REQUEST_BENCH}`)},2600)},s=()=>{var O;c(!1),_(""),r((O=T)==null?void 0:O.ERR_DOWNLOADING_EXCEL,"error")},o=(n==null?void 0:n.RequestType)===W.CHANGE_WITH_UPLOAD?t.downloadEndpoints.change:t.downloadEndpoints.create,E=`/${A}${o}`,h=t.getPayload?t.getPayload(n):{},e=t.method||"getblobfile";u(E,e,D,s,h)},handleEmailDownload:(_,c,n,i)=>{c(!0),_("Initiating email download...");const t=I[i]||I.DEFAULT,A=t.destination||P,D=()=>{var O;c(!1),_(""),r((O=$)==null?void 0:O.DOWNLOAD_MAIL_INITIATED,"success"),setTimeout(()=>{var R;S((R=b)==null?void 0:R.REQUEST_BENCH)},2600)},s=()=>{var O;c(!1),_(""),r((O=T)==null?void 0:O.ERR_DOWNLOADING_EXCEL,"error"),setTimeout(()=>{var R;S((R=b)==null?void 0:R.REQUEST_BENCH)},2600)},l=(n==null?void 0:n.RequestType)===W.EXTEND_WITH_UPLOAD,o=(n==null?void 0:n.RequestType)===W.CHANGE_WITH_UPLOAD;let E;l&&t.downloadEndpoints.extendMail?E=t.downloadEndpoints.extendMail:o&&t.downloadEndpoints.changeMail?E=t.downloadEndpoints.changeMail:E=t.downloadEndpoints.createMail;const h=`/${A}${E}`,e=t.getPayload?t.getPayload(n):{},a=t.mailMethod||"get";u(h,a,D,s,e)},handleUploadMaterial:(_,c,n,i,t,A,D,s)=>{var O,R,U;const l=I[t]||I.DEFAULT,o=l.destination||P;if(A!==W.CREATE_WITH_UPLOAD){r("Upload is only supported for Create with Upload request type","error");return}c("Initiating Excel Upload"),n(!0);const E=new FormData;[..._].forEach(N=>E.append("files",N)),E.append("requestId",D||""),E.append("dtName",l.dtName),E.append("version",l.version),t===((O=x)==null?void 0:O.BK)?(E.append("region",(s==null?void 0:s.Region)||"US"),E.append("bankCtry",(s==null?void 0:s.bankCtry)||"US"),E.append("role",l.rolePrefix)):t===((R=x)==null?void 0:R.IO)&&(E.append("region",(s==null?void 0:s.Region)||"US"),E.append("orderType","ALL"),E.append("scenario",l.scenario));const h=N=>{var m,L,w;n(!1),c(""),N.statusCode===200?r(((m=$)==null?void 0:m.EXCEL_UPLOAD_SUCCESS)||"Excel uploaded successfully","success"):r(((L=T)==null?void 0:L.EXCEL_UPLOAD_ERROR)||"Error uploading excel","error"),S((w=b)==null?void 0:w.REQUEST_BENCH)},e=N=>{var m,L;n(!1),c(""),r(((m=T)==null?void 0:m.EXCEL_UPLOAD_ERROR)||"Error uploading excel","error"),S((L=b)==null?void 0:L.REQUEST_BENCH)};let a;t===((U=x)==null?void 0:U.IO)?a=`/${o}${l.uploadEndpoint}`:a=`/${o}/massAction/${l.uploadEndpoint||"getAllFromExcel"}`,u(a,"postformdata",h,e,E)},handleDownloadWF:(_,c,n,i)=>{var s;_((s=p)==null?void 0:s.REPORT_LOADING),c(!0);const t=l=>{var h;if((l==null?void 0:l.size)==0){c(!1),_(""),r((h=T)==null?void 0:h.DATA_NOT_FOUND_FOR_SEARCH,"error");return}const o=URL.createObjectURL(l),E=document.createElement("a");E.href=o,E.setAttribute("download",i==="create"?"Workflow_Create.xlsx":`All_Workflows_Data_${n}.xlsx`),document.body.appendChild(E),E.click(),document.body.removeChild(E),URL.revokeObjectURL(o),c(!1),_(""),r(i==="create"?"Workflow_Create.xlsx has been downloaded successfully.":"All_Workflows_Data.xlsx has been downloaded successfully.","success")},A=()=>{c(!1)},D=i==="create"?`/${d}${f.EXCEL.EXPORT_WF_EXCEL}`:`/${d}${f.EXCEL.DOWNLOAD_ALL_WF_DATA}`;i!=="create"?u(D,"postandgetblob",t,A,{module:n}):u(D,"getblobfile",t,A)},handleUploadWF:(_,c,n,i,t,A,D,s)=>{n(!0);const l=new FormData;[..._].forEach(e=>l.append("files",e)),l.append("module",i),l.append("scenario",s);const o=e=>{var a;e.statusCode===((a=G)==null?void 0:a.STATUS_200)?(n(!1),c(""),r(Array.isArray(e.data)||!(e!=null&&e.data)?e==null?void 0:e.message:e==null?void 0:e.data,"success"),D(Q(X,i)),t(!1),A()):(n(!1),c(""),r(Array.isArray(e.data)||!(e!=null&&e.data)?e==null?void 0:e.message:e==null?void 0:e.data,"error"))},E=e=>{n(!1),c(""),r(e.message,"error")},h=`/${d}${f.EXCEL.UPLOAD_WORKFLOW_EXCEL}`;u(h,"postformdata",o,E,l)},handleMassCancelWF:(_,c,n,i,t,A)=>{var E,h;n(!0),c("Cancelling selected workflows...");const D={workflowIds:_},s=`/${d}${(h=(E=f)==null?void 0:E.WORKFLOW_APIS)==null?void 0:h.MASS_CANCEL_WF}`;u(s,"post",e=>{n(!1),c(""),r(e==null?void 0:e.message,"success"),i([]),A(t)},e=>{n(!1),c(""),r((e==null?void 0:e.message)||"Error cancelling workflows","error")},D)},handleMassDownloadWF:(_,c,n,i,t)=>{var o,E,h;n(!0),c((o=p)==null?void 0:o.REPORT_LOADING);const A={workflowIds:_},D=`/${d}${(h=(E=f)==null?void 0:E.WORKFLOW_APIS)==null?void 0:h.MASS_DOWNLOAD_WF}`;u(D,"postandgetblob",e=>{var R,U,N;if((e==null?void 0:e.size)==0){n(!1),c(""),r((R=T)==null?void 0:R.DATA_NOT_FOUND_FOR_SEARCH,"error");return}const a=URL.createObjectURL(e),O=document.createElement("a");O.href=a,O.setAttribute("download",`Workflows_${(U=X)==null?void 0:U[t]}_${new Date().toISOString().split("T")[0]}.xlsx`),document.body.appendChild(O),O.click(),document.body.removeChild(O),URL.revokeObjectURL(a),n(!1),c(""),r(`Workflows_${(N=X)==null?void 0:N[t]}_${new Date().toISOString().split("T")[0]}.xlsx has been downloaded successfully.`,"success"),i([])},e=>{n(!1),c(""),r((e==null?void 0:e.message)||"Error downloading workflows","error")},A)},handleUploadHolidays:(_,c,n,i,t)=>{n(!0);const A=new FormData;[..._].forEach(o=>A.append("files",o));const D=o=>{var E;o.statusCode===((E=G)==null?void 0:E.STATUS_200)?(n(!1),c(""),r(Array.isArray(o.data)||!(o!=null&&o.data)?o==null?void 0:o.message:o==null?void 0:o.data,"success"),i(!1),t()):(n(!1),c(""),r(Array.isArray(o.data)||!(o!=null&&o.data)?o==null?void 0:o.message:o==null?void 0:o.data,"error"))},s=o=>{n(!1),c(""),r(o.message,"error")},l=`/${d}/api/holidays/getAllHolidaysFromExcel`;u(l,"postformdata",D,s,A)},handleDownloadHolidays:(_,c)=>{var t;_((t=p)==null?void 0:t.REPORT_LOADING),c(!0);const n=A=>{var l;if((A==null?void 0:A.size)==0){c(!1),_(""),r((l=T)==null?void 0:l.DATA_NOT_FOUND_FOR_SEARCH,"error");return}const D=URL.createObjectURL(A),s=document.createElement("a");s.href=D,s.setAttribute("download","All_Holidays.xlsx"),document.body.appendChild(s),s.click(),document.body.removeChild(s),URL.revokeObjectURL(D),c(!1),_(""),r("All_Holidays.xlsx has been downloaded successfully.","success")},i=()=>{c(!1)};u(`/${d}/excel/downloadHolidayExcel`,"getblobfile",n,i)}}};export{oe as u};
