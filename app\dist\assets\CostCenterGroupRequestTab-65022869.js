import{s as Xe,n as L,u as je,g as bt,r as u,cw as ds,eW as rt,aX as I,j as e,c as j,O as Fe,b1 as us,d as Ye,B as X,an as ke,aG as lt,Ae as Xs,c5 as ct,y6 as Vs,aZ as Js,aD as Zs,C as Be,bH as nt,aT as dt,bK as qe,aO as Ae,aY as eo,z9 as to,ap as ps,cz as so,ae as St,f7 as mt,cf as Tt,fJ as os,fP as gt,fQ as st,Af as oo,Ag as ro,Ah as no,Ai as ao,Aj as io,Ak as lo,Al as co,Am as at,An as it,Ao as uo,aa as po,T as ye,bD as fo,cH as ut,cq as Rt,Ap as fs,a6 as xe,d5 as Cs,d3 as hs,d4 as Ns,af as Co,fb as Es,aF as Ds,aH as ho,F as Ot,Z as Qe,Aa as No,a as Eo,aP as Do,o as mo,Aq as To,f9 as go,xY as So,xZ as Ro,g4 as rs,wZ as Oo,w_ as bo,y0 as ns,y1 as Po,wY as Ao,aA as Io,aB as _o,Ar as yo,As as xo,bd as as,er as Lo,bJ as is,cA as wo,y2 as qo,y3 as ko,bc as Bo,cI as ve,d6 as Fo,d7 as Mo,am as Ho,br as $o,ad as vo,g3 as ze,bf as ot,bI as ls,d8 as Go,a$ as Uo,aE as Wo,d9 as Yo,fY as jo,al as zo,cZ as Ko,J as Qo,gA as Xo,At as Vo,aJ as Jo}from"./index-f7d9b065.js";import{D as Zo,A as er,P as tr,S as sr,B as or,g as rr}from"./PreviewPage-634057fa.js";import{u as nr,R as ar,a as ir,b as lr,C as cr,d as dr}from"./useDynamicWorkflowDTHierarchy-548ceabc.js";import{d as ur}from"./PermIdentityOutlined-0746a749.js";import{d as pr}from"./FeedOutlined-41109ec9.js";import{d as fr}from"./TrackChangesTwoTone-7a2ab513.js";import{d as Cr}from"./FileUploadOutlined-4a68a28a.js";import{E as hr}from"./ExcelOperationsCard-49e9ffd2.js";import{F as Nr}from"./FilterFieldGlobal-f8e8f75f.js";import{u as Er}from"./useDownloadExcel-cd596a31.js";import{R as Dr}from"./ReusableHIerarchyTree-7527be6c.js";import{u as ms,E as mr}from"./ErrorReportDialog-cb66d1ed.js";import{d as Me}from"./DeleteOutline-584dc929.js";import"./AttachFile-8d552da8.js";import"./UtilDoc-6f590135.js";import"./FileDownloadOutlined-59854a55.js";import"./VisibilityOutlined-b6cd6d28.js";import"./DeleteOutlined-e668453f.js";import"./CloudUpload-0ba6431e.js";import"./utilityImages-067c3dc2.js";import"./Delete-5278579a.js";import"./ReusablePromptBox-e1871d49.js";import"./featureConfig-652a9f8d.js";import"./Description-ab582559.js";import"./DataObject-52409c14.js";import"./Download-52c4427b.js";import"./useFinanceCostingRows-ffbb569f.js";import"./CheckCircleOutline-e186af3e.js";import"./useChangeMaterialRowsRequestor-fc0d44be.js";import"./FilterChangeDropdown-22e24089.js";import"./CloudDownload-9a7605e9.js";import"./AttachmentUploadDialog-43cc9099.js";import"./AdapterDayjs-2a9281df.js";import"./advancedFormat-4f6292d4.js";import"./customParseFormat-1bc1aa07.js";import"./isBetween-c1c6beb9.js";import"./DatePicker-a8e9bd4a.js";import"./useMobilePicker-9b56b5b6.js";import"./CSSTransition-cd337b47.js";import"./context-4d14404f.js";import"./EyeOutlined-0bb7ab85.js";import"./EditOutlined-9d614b39.js";import"./asyncToGenerator-88583e02.js";import"./ArrowLeftOutlined-a31a51d5.js";import"./index-190fd75d.js";import"./lz-string-0665f106.js";import"./ErrorHistory-ef441d1f.js";const Tr=({downloadClicked:f,setDownloadClicked:a,setIsSecondTabEnabled:E,setIsAttachmentTabEnabled:b})=>{var l,p,d,F,S;const c=Xe(),s=L(o=>o.hierarchyData.requestHeaderData),m=L(o=>o.request.requestHeader),t=L(o=>o.userManagement.userData),z=L(o=>o.tabsData.requestHeaderData);L(o=>o.AllDropDown.dropDown.FieldName||[]);const H=L(o=>o.AllDropDown.dropDown),P=je(),y=new URLSearchParams(P.search),_=y.get("RequestType"),M=y.get("RequestId"),oe=y.get("RequestId"),fe=`/Date(${Date.now()})/`,Ce=bt(),[he,Ie]=u.useState(!1),[ce,de]=u.useState("systemGenerated"),[$,te]=u.useState(!1),[Y,Z]=u.useState(""),[K,B]=u.useState(""),[re,ee]=u.useState(!1),{getRequestHeaderTemplatePCG:w}=nr(),{handleDownload:ie,handleEmailDownload:A}=Er((l=ct)==null?void 0:l.CCG),{showSnackbar:ne}=ds(),se=P.state,Le=[{code:"Create",tooltip:"Create New Cost Center Group Directly in Application"},{code:"Change",tooltip:"Modify Existing Cost Center Group Directly in Application"},{code:"Create with Upload",tooltip:"Create New Cost Center Group with Excel Upload"},{code:"Change with Upload",tooltip:"Modify Existing Cost Center Group with Excel Upload"}],ue=[{code:"All Other Fields",desc:""},{code:"Address Change",desc:""},{code:"Block",desc:""},{code:"Temporary Block/Unblock",desc:""}],Se=[{code:"High",desc:""},{code:"Medium",desc:""},{code:"Low",desc:""}];c(rt({keyName:"RequestStatus",data:"DRAFT"})),c(rt({keyName:"ReqCreatedBy",data:t==null?void 0:t.user_id})),u.useEffect(()=>{var o;if(f){if((s==null?void 0:s.RequestType)===I.CREATE_WITH_UPLOAD){ee(!0);return}if((s==null?void 0:s.RequestType)===((o=I)==null?void 0:o.CHANGE_WITH_UPLOAD)){te(!0);return}}},[f]);const n=()=>{var q,v;let o=!0;return s&&((q=z[Object.keys(z)])!=null&&q.length)?(v=z[Object.keys(z)[0]])==null||v.forEach(V=>{var ae;!s[V.jsonName]&&V.visibility===((ae=Zs)==null?void 0:ae.MANDATORY)&&(o=!1)}):o=!1,o};u.useEffect(()=>{w()},[s==null?void 0:s.RequestType]),u.useEffect(()=>{var o,q;M&&(_===((o=I)==null?void 0:o.CREATE)||_===((q=I)==null?void 0:q.CHANGE))&&se&&(!se.parentNode||se.parentNode.length===0)&&te(!0)},[M,_,se]);const h=()=>{var ae,me;const o=new Date(s==null?void 0:s.ReqCreatedOn).getTime();te(!1);const q={RequestId:"",ReqCreatedBy:(t==null?void 0:t.user_id)||"",ReqCreatedOn:o?`/Date(${o})/`:fe,ReqUpdatedOn:o?`/Date(${o})/`:fe,RequestType:(s==null?void 0:s.RequestType)||"",RequestPrefix:"",RequestPriority:(s==null?void 0:s.RequestPriority)||"",RequestDesc:(s==null?void 0:s.RequestDesc)||"",RequestStatus:"DRAFT",FirstProd:"",LaunchDate:"",LeadingCat:"",Division:"",TemplateName:"",FieldName:"",Region:(s==null?void 0:s.Region)||"",FilterDetails:"",IsBifurcated:!0,IsHierarchyGroup:!0},v=D=>{var Ne,R,_e,Re;if(ne(`${(Ne=eo)==null?void 0:Ne.SUCCESS_REQUEST_HEADER}${(R=D==null?void 0:D.body)==null?void 0:R.requestPrefix}${(_e=D==null?void 0:D.body)==null?void 0:_e.requestId}`,"success"),Ie(!1),b(!0),c(to(D==null?void 0:D.body)),c(ps(D.body)),c(rt({keyName:so.REQUEST_ID,data:(Re=D==null?void 0:D.body)==null?void 0:Re.requestId})),(s==null?void 0:s.RequestType)===I.CREATE||(s==null?void 0:s.RequestType)===I.CHANGE){te(!0);return}if((s==null?void 0:s.RequestType)===I.CREATE_WITH_UPLOAD||(s==null?void 0:s.RequestType)===I.CHANGE_WITH_UPLOAD){ee(!0);return}},V=D=>{var Ne;ne((Ne=St)==null?void 0:Ne.ERROR_REQUEST_HEADER,"error")};Be(`/${nt}${(me=(ae=dt)==null?void 0:ae.MASS_ACTION)==null?void 0:me.CREATE_BOM_REQUEST}`,"post",v,V,q)},i=()=>{var o;a(!1),ee(!1),de("systemGenerated"),oe||Ce((o=qe)==null?void 0:o.REQUEST_BENCH)},T=o=>{var q;de((q=o==null?void 0:o.target)==null?void 0:q.value)},C=()=>{var o,q;ce==="systemGenerated"&&(ie(Z,B,s,(o=Ae)==null?void 0:o.CCG),i()),ce==="mailGenerated"&&(A(Z,B,s,(q=Ae)==null?void 0:q.CCG),i())};let r={[(p=I)==null?void 0:p.CREATE]:"CREATE_CCG",[(d=I)==null?void 0:d.CHANGE]:"CHANGE_CCG"};return e("div",{children:j(Js,{spacing:2,children:[Object.entries(z).map(([o,q])=>j(Fe,{item:!0,md:12,sx:{backgroundColor:"white",maxHeight:"max-content",height:"max-content",borderRadius:"8px",border:"1px solid #E0E0E0",mt:.25,boxShadow:"0px 2px 14px 0px rgba(48, 38, 185, 0.10)",...us},children:[e(Ye,{sx:{fontSize:"12px",fontWeight:"700",paddingBottom:"10px"},children:o}),e(X,{children:e(Fe,{container:!0,spacing:1,children:q.filter(v=>v.visibility!=="Hidden").sort((v,V)=>v.sequenceNo-V.sequenceNo).map(v=>e(Nr,{isHeader:!0,field:v,dropDownData:{RequestType:Le,RequestPriority:Se,TemplateName:ue},disabled:oe||!!(m!=null&&m.requestId),requestHeader:!0,module:"CCG"},v.id))})}),!oe&&!(m!=null&&m.requestId)&&e(X,{sx:{display:"flex",justifyContent:"flex-end",marginTop:"20px"},children:e(ke,{variant:"contained",color:"primary",disabled:!n(),onClick:h,children:"Save Request Header"})})]},o)),e(lt,{blurLoading:K,loaderMessage:Y}),$&&e(ar,{open:$,onClose:()=>{te(!1)},parameters:Xs[r==null?void 0:r[s==null?void 0:s.RequestType]],setShowTable:!1,allDropDownData:H,setIsSecondTabEnabled:E,module:(F=ct)==null?void 0:F.CCG,mandatoryFields:(S=Vs)==null?void 0:S[r==null?void 0:r[s==null?void 0:s.RequestType]]}),e(Zo,{onDownloadTypeChange:C,open:re,downloadType:ce,handleDownloadTypeChange:T,onClose:i})]})})},Pt=({setSuccessDialogOpen:f,setDialogData:a,selectedLevel:E}={})=>{var C;const b=Xe();bt();const c=je(),{getDynamicWorkflowDT:s}=ir(),m=new URLSearchParams(c.search),t=m.get("RequestType"),z=m.get("RequestId"),[H,P]=u.useState(!1),[y,_]=u.useState(!1),[M,oe]=u.useState(!1),[fe,Ce]=u.useState(""),[he,Ie]=u.useState("success"),[ce,de]=u.useState([]),$=L(r=>r.userManagement.taskData),te=L(r=>r.payload.filteredButtons),Y=L(r=>r.hierarchyData.requestHeaderData),Z=L(r=>r.hierarchyData.treeData),K=L(r=>r.payload.requestorPayload),B=L(r=>r.payload.dataLoading),re=L(r=>r.payload.dynamicKeyValues),ee=L(r=>r.request.requestHeader),w=L(r=>r.hierarchyData),ie=L(r=>r.hierarchyData.TreeChanges),A=L(r=>r.hierarchyData.DisplayRecords),{getButtonsDisplayGlobal:ne,showWfLevels:se}=ms(),{preparePayload:Le}=lr();u.useEffect(()=>{($!=null&&$.ATTRIBUTE_1||t)&&ne("Hierarchy Node (Cost Center)","MDG_DYN_BTN_DT","v3")},[$]),u.useEffect(()=>{var l;const r=async()=>{var p,d;try{let F={"MDG_CONDITIONS.MDG_CCG_REQUEST_TYPE":t||"","MDG_CONDITIONS.MDG_HIERARCHY_REGION":((p=w==null?void 0:w.GeneralInformation)==null?void 0:p["Hierarchy Region"])||""};const S=await s($==null?void 0:$.ATTRIBUTE_3,"v4","MDG_DYNAMIC_WF_CCG_DT",(d=Ae)==null?void 0:d.CCG,F);de(S)}catch{}};t&&((l=w==null?void 0:w.GeneralInformation)!=null&&l["Hierarchy Region"])&&($!=null&&$.ATTRIBUTE_3)&&r()},[t,(C=w==null?void 0:w.GeneralInformation)==null?void 0:C["Hierarchy Region"],$==null?void 0:$.ATTRIBUTE_3]),u.useEffect(()=>{(Z==null?void 0:Z.length)!==0&&P(!0)},[Z]);const ue=async r=>{var l,p;try{const d={controllingArea:(w==null?void 0:w.ControllingArea)??"",nodes:[r==null?void 0:r.toUpperCase()]},S=await(await mt(`/${Tt}/node/fetchDescriptionForNode`,"post",d)).json();return((p=(l=S==null?void 0:S.body)==null?void 0:l[0])==null?void 0:p.Description)||""}catch(d){return console.error("Error fetching old description:",d),""}},Se=async r=>{var l,p;try{const d={controllingArea:(w==null?void 0:w.ControllingArea)??"",nodes:[r==null?void 0:r.toUpperCase()]},S=await(await mt(`/${Tt}/node/fetchParentNodeForSubNode`,"post",d)).json();return((p=(l=S==null?void 0:S.body)==null?void 0:l[0])==null?void 0:p.ParentNode)||""}catch(d){return console.error("Error fetching old parent node:",d),""}},n=async r=>{var l,p;try{const d={controllingArea:(w==null?void 0:w.ControllingArea)??"",hierarchy:(w==null?void 0:w.ParentNode)??"",ccList:[r==null?void 0:r.toUpperCase()]},S=await(await mt(`/${Tt}/node/fetchParentNodeForObject`,"post",d)).json();return((p=(l=S==null?void 0:S.body)==null?void 0:l[0])==null?void 0:p.Node)||""}catch(d){return console.error("Error fetching old parent for cost center:",d),""}},h=()=>{const r=[],l=[],p=[],d=[],F=[],S=[],o=[],q=[],v=[],V=[],ae=(A==null?void 0:A["NEW NODES"])||[],me=(A==null?void 0:A["MOVE NODE"])||[],D=(A==null?void 0:A["COST CENTERS"])||[],Ne=(A==null?void 0:A["MOVE COST CENTER"])||[],R=(A==null?void 0:A.DESCRIPTIONS)||[],_e=(A==null?void 0:A["DELETE NODE"])||[];ae.forEach(N=>{N["Parent Node"]&&N["New Node"]&&(r.push(`${N["Parent Node"]}$$${N["New Node"]}`),q.push(`${N["New Node"]}`))}),me.forEach(N=>{N["New Parent Node"]&&N["Selected Node"]&&r.push(`${N["New Parent Node"]}$$${N["Selected Node"]}`)}),me.forEach(N=>{N["Selected Node"]&&N["Old Parent Node"]&&l.push(`${N["Selected Node"]}$$${N["Old Parent Node"]}`)}),D.forEach(N=>{N.Node&&N["Cost Center"]&&(p.push(`${N.Node}$$${N["Cost Center"]}`),V.push(`${N["Cost Center"]}`))}),Ne.forEach(N=>{N["New Parent Node"]&&N["Selected Cost Center"]&&p.push(`${N["New Parent Node"]}$$${N["Selected Cost Center"]}`)}),Ne.forEach(N=>{N["Old Parent Node"]&&N["Selected Cost Center"]&&d.push(`${N["Old Parent Node"]}$$${N["Selected Cost Center"]}`)}),ae.forEach(N=>{N["New Node"]&&N.Description&&(F.push(`${N["New Node"]}$~$${N.Description}`),v.push(`${N.Description}`))}),R.forEach(N=>{N["Parent Node"]&&N["New Description"]&&(S.push(`${N["Parent Node"]}$~$${N["New Description"]}`),v.push(`${N["New Description"]}`))}),_e.forEach(N=>{N["Parent Node"]&&N["Deleted Node"]&&o.push(`${N["Parent Node"]}$$${N["Deleted Node"]}`)});const Re={NodeList:r,ReplaceNodesList:l,TagList:p,ReplaceTagList:d,DescList:F,EditDescList:S,DeleteNodeList:o,nodesListForDBDuplicateCheck:q,descListForDBDuplicateCheck:v,tagListForDBDuplicateCheck:V,success:!0};return b(oo(r)),b(ro(l)),b(no(p)),b(ao(d)),b(io(F)),b(lo(S)),b(co(o)),b(at(q)),b(it(v)),b(uo(V)),console.log("Lists populated:",Re),Re};return{showTree:H,buttonsLoading:y,openButtonSnackBar:M,alertButtonMsg:fe,alertButtonType:he,filteredButtons:te,requestorPayload:K,loadForFetching:B,initialNodeData:Z,showWfLevels:se,wfLevels:ce,fetchOldDescriptionForNode:ue,fetchOldParentForNode:Se,fetchOldParentForObject:n,handleButtonClick:async(r,l,p)=>{var d,F,S,o,q,v,V;try{let ae=(q=(o=(S=(d=dt)==null?void 0:d.MASTER_BUTTON_APIS)==null?void 0:S[(F=ct)==null?void 0:F.CCG])==null?void 0:o[Y==null?void 0:Y.RequestType])==null?void 0:q[r],me={};if(t!=((v=I)==null?void 0:v.CHANGE_WITH_UPLOAD)){const R=Le(ie);if(_(!0),!(R!=null&&R.success))return;me=os({...w,NodesList:R.NodeList,ReplaceNodesList:R.ReplaceNodesList,TagList:R.TagList,ReplaceTagList:R.ReplaceTagList,DescList:R.DescList,EditDescList:R.EditDescList,DeleteNodeList:R.DeleteNodeList,nodesListForDBDuplicateCheck:R.nodesListForDBDuplicateCheck,descListForDBDuplicateCheck:R.descListForDBDuplicateCheck,tagListForDBDuplicateCheck:R.tagListForDBDuplicateCheck},ee,z,$,re,K,r,l,p,E)}if(t===((V=I)==null?void 0:V.CHANGE_WITH_UPLOAD)){const R=h();_(!0),R!=null&&R.success,me=os({...w,NodesList:R.NodeList,ReplaceNodesList:R.ReplaceNodesList,TagList:R.TagList,ReplaceTagList:R.ReplaceTagList,DescList:R.DescList,EditDescList:R.EditDescList,DeleteNodeList:R.DeleteNodeList,nodesListForDBDuplicateCheck:R.nodesListForDBDuplicateCheck,descListForDBDuplicateCheck:R.descListForDBDuplicateCheck,tagListForDBDuplicateCheck:R.tagListForDBDuplicateCheck},ee,z,$,re,K,l,p,E)}const D=R=>{var _e,Re,N,Ge,Oe,Ve;R.statusCode>=200&&R.statusCode<300?(_(!1),a({title:gt.TITLE,message:R.message,subText:gt.SUBTEXT,buttonText:gt.BUTTONTEXT,redirectTo:(Ve=(Oe=(Ge=(N=(_e=dt)==null?void 0:_e.MASTER_BUTTON_APIS)==null?void 0:N[(Re=ct)==null?void 0:Re.PCG])==null?void 0:Ge[Y==null?void 0:Y.RequestType])==null?void 0:Oe[r])==null?void 0:Ve.NAVIGATE_TO}),f(!0)):(_(!1),a({title:st.TITLE,message:R.message,subText:st.SUBTEXT,buttonText:st.BUTTONTEXT,redirectTo:st.REDIRECT}),f(!0))},Ne=R=>{_(!1),Ie("error"),Ce((R==null?void 0:R.error)||"An error occurred"),oe(!0)};Be(ae==null?void 0:ae.URL,"POST",D,Ne,me)}catch(ae){console.error("Error in handleButtonClick:",ae),_(!1)}},handleSnackBarButtonClose:()=>{oe(!1)}}},J={ADD_NODE:"ADD_NODE",EDIT_DESCRIPTION:"EDIT_DESCRIPTION",ADD_COST_CENTER:"ADD_COST_CENTER",MOVE_NODE:"MOVE_NODE",MOVE_COST_CENTER:"MOVE_COST_CENTER",REMOVE_COST_CENTER:"REMOVE_COST_CENTER",DELETE_NODE:"DELETE_NODE",CHANGE_PERSON_RESPONSIBLE:"CHANGE_PERSON_RESPONSIBLE",DELETE_ROW:"DELETE_ROW",FIELD_UPDATE:"FIELD_UPDATE"},gr=()=>Date.now()+Math.random().toString(36).substr(2,9),Sr=(f,a,E={})=>{switch(f){case J.ADD_NODE:return`New node "${a["New Node"]}" added under parent "${a["Parent Node"]}"`;case J.EDIT_DESCRIPTION:return`Description changed for node "${a["Parent Node"]}" from "${E["Old Description"]||"N/A"}" to "${a["New Description"]}"`;case J.ADD_COST_CENTER:return`Cost Center "${a["Cost Center"]}" added to node "${a.Node}"`;case J.MOVE_NODE:return`Node "${a["Selected Node"]}" moved from "${a["Old Parent Node"]}" to "${a["New Parent Node"]}"`;case J.MOVE_COST_CENTER:return`Cost Center "${a["Selected Cost Center"]}" moved from "${a["Old Parent Node"]}" to "${a["New Parent Node"]}"`;case J.REMOVE_COST_CENTER:return`Cost Center "${a["Selected Cost Center"]}" removed from node "${a["Parent Node"]}"`;case J.DELETE_NODE:return`Node "${a["Deleted Node"]}" deleted from parent "${a["Parent Node"]}"`;case J.CHANGE_PERSON_RESPONSIBLE:return`Person responsible changed for node "${a["Parent Node"]}" from "${E["Old Person Responsible"]||"N/A"}" to "${a["New Person Responsible"]}"`;case J.DELETE_ROW:return`Row deleted: ${a.description}`;case J.FIELD_UPDATE:return`Field updated: ${a.description}`;default:return`Unknown change type: ${f}`}},Rr=(f,a)=>{const E=[],b=["Id","Updated By","Updated On"];return Object.keys(a).forEach(c=>{if(!b.includes(c)){const s=f[c]||"",m=a[c]||"";s!==m&&E.push({field:c,oldValue:s,newValue:m})}}),E},Or=(f,a,E,b,c)=>{const s=a?`"${a}"`:"empty",m=E?`"${E}"`:"empty";return`${f} changed from ${s} to ${m} in row ${c} (${b})`},br=(f,a,E,b)=>{const c=[];switch(b){case 0:f==="New Node"?(a&&a.trim()!==""&&c.push({type:"node",value:a,action:"remove"}),E&&E.trim()!==""&&c.push({type:"node",value:E,action:"add"})):f==="Description"&&(a&&a.trim()!==""&&c.push({type:"desc",value:a,action:"remove"}),E&&E.trim()!==""&&c.push({type:"desc",value:E,action:"add"}));break;case 1:f==="New Description"&&(a&&a.trim()!==""&&c.push({type:"desc",value:a,action:"remove"}),E&&E.trim()!==""&&c.push({type:"desc",value:E,action:"add"}));break}return c},Pr=(f,a,E,b,c,s=null)=>{const m=b.findIndex(H=>H.key===E);if(m===-1)return;const t=Rr(f,a);let z=[];t.forEach(({field:H,oldValue:P,newValue:y})=>{if(y.trim()!==""){const _=Or(H,P,y,E,a.Id);let M="FIELD_UPDATE";switch(m){case 0:H==="New Node"&&y&&(M=J.ADD_NODE);break;case 1:H==="New Description"&&y&&(M=J.EDIT_DESCRIPTION);break;case 2:H==="Cost Center"&&y&&(M=J.ADD_COST_CENTER);break;case 3:H==="Selected Node"&&y&&(M=J.MOVE_NODE);break;case 4:H==="Selected Cost Center"&&y&&(M=J.MOVE_COST_CENTER);break;case 5:H==="Selected Cost Center"&&y&&(M=J.REMOVE_COST_CENTER);break;case 6:H==="Deleted Node"&&y&&(M=J.DELETE_NODE);break;case 7:H==="New Person Responsible"&&y&&(M=J.CHANGE_PERSON_RESPONSIBLE);break}const oe=br(H,P,y,m);z=[...z,...oe],c(M,_,{rowId:a.Id,fieldName:H,oldValue:P,newValue:y,oldData:{...f},newData:{...a},tableKey:E})}}),z.length>0&&s&&(s.batchUpdateDuplicateLists?s.batchUpdateDuplicateLists(z):z.forEach(H=>{const{type:P,value:y,action:_}=H;P==="node"&&s.updateNodesListForDuplicateCheck?s.updateNodesListForDuplicateCheck(y,_):P==="desc"&&s.updateDescListForDuplicateCheck&&s.updateDescListForDuplicateCheck(y,_)}))},cs=(f,a,E)=>{if(!f||!E)return{isValid:!0,message:""};if(E(f,a)){const c=a==="node"?"Node":"Description";return{isValid:!1,message:`${c} "${f}" already exists. Please choose a different ${c.toLowerCase()}.`}}return{isValid:!0,message:""}},Ar=[{label:"New Nodes",value:"1",key:"NEW NODES"},{label:"Description Change",value:"2",key:"DESCRIPTIONS"},{label:"Add Cost Centers",value:"3",key:"COST CENTERS"},{label:"Move Node",value:"4",key:"MOVE NODE"},{label:"Move Cost Center",value:"5",key:"MOVE COST CENTER"},{label:"Remove Cost Center",value:"6",key:"REMOVE COST CENTER"},{label:"Delete Node",value:"7",key:"DELETE NODE"},{label:"Change Person Responsible",value:"8",key:"PERSON RESPONSIBLE"}],le=({value:f,placeholder:a,maxLength:E=10,disabled:b=!1,rowId:c,fieldName:s,tableKey:m,fetchOldData:t=!1,fetchFunction:z=null,oldDataField:H=null,onNodeValueChange:P=null,addToChangeLog:y,updateDuplicateCheckLists:_=null,isDuplicateCheckField:M=!1,duplicateCheckType:oe=null})=>{const fe=Xe(),Ce=L(d=>d.hierarchyData.DisplayRecords),he=je(),ce=new URLSearchParams(he.search).get("reqBench"),de=L(d=>d.userManagement.userData),[$,te]=u.useState(f||""),[Y,Z]=u.useState((f==null?void 0:f.length)||0),[K,B]=u.useState(!1),[re,ee]=u.useState({}),[w,ie]=u.useState(""),[A,ne]=u.useState(!1),se=L(d=>{var F;return(F=d.hierarchyData.requestHeaderData)==null?void 0:F.RequestStatus}),Le=ce&&!ut.includes(se);u.useEffect(()=>{K||(te(f||""),Z((f==null?void 0:f.length)||0),ie(""),ne(!1))},[f,K]);const ue=()=>M&&oe&&(_==null?void 0:_.checkForDuplicates),Se=()=>oe,n=d=>{const F=Ce[m]||[],S=F.find(q=>q.Id===c),o=F.map(q=>q.Id===c?{...q,...d,"Updated By":de==null?void 0:de.emailId,"Updated On":Rt().utc().format("YYYY-MM-DD HH:mm:ss.SSS")}:q);if(fe(fs({...Ce,[m]:o})),y&&S){const q=o.find(v=>v.Id===c);q&&Pr(S,q,m,Ar,y,_)}},h=d=>{const F=d.target.value.trimStart();if(te(F),Z(F.length),ue()&&(_!=null&&_.checkForDuplicates)){const S=Se();if(S&&F.trim()!==""){const o=cs(F.trim(),S,_.checkForDuplicates);o.isValid?(ie(""),ne(!1)):(ie(o.message),ne(!0))}else ie(""),ne(!1)}P&&P(F)},i=async()=>{B(!1);const d=$.trim(),F=$;if(ue()&&(_!=null&&_.checkForDuplicates)&&d!==""){const S=Se();if(S){const o=cs(d,S,_.checkForDuplicates);o.isValid||(ie(o.message),ne(!0))}}if(n({[s]:d.toUpperCase()}),t&&d&&z&&H){ee(S=>({...S,[c]:!0}));try{const S=await z(d);S!=null&&n({[H]:S,[s]:F.toUpperCase()})}catch(S){console.error(`Error fetching old ${H}:`,S)}finally{ee(S=>({...S,[c]:!1}))}}},T=()=>{B(!0),ie(""),ne(!1)},C=()=>A?"#ff9800":K&&Y>=E?"red":"",r=()=>w&&A?w:K?Y===E?"Max Length Reached":`${Y}/${E}`:"",l=()=>w&&A?"#ff9800":K&&Y===E?"red":"blue",p=e(po,{variant:"outlined",size:"small",fullWidth:!0,disabled:Le||b||re[c],value:$,placeholder:a,error:A,inputProps:{style:{textTransform:"uppercase"},maxLength:E},onChange:h,onFocus:T,onBlur:i,helperText:r(),FormHelperTextProps:{sx:{color:l(),position:"absolute",bottom:"-20px",fontSize:"0.75rem"}},sx:{"& .MuiOutlinedInput-root":{"&.Mui-focused fieldset":{borderColor:C()},"& fieldset":{borderColor:C()},"&.Mui-error fieldset":{borderColor:"#ff9800"}}},onKeyDown:d=>d.stopPropagation()});return j(X,{sx:{position:"relative"},children:[A?e(ye,{title:w,arrow:!0,placement:"top",open:A,children:p}):p,re[c]&&e(fo,{size:24,sx:{position:"absolute",top:"50%",right:8,marginTop:"-12px"}}),A&&e(X,{sx:{position:"absolute",top:-2,right:-2,width:8,height:8,borderRadius:"50%",backgroundColor:"#ff9800",border:"2px solid white",zIndex:1}})]})},Ir=(f,a,E,b,c,s,m)=>[{field:"Id",headerName:"ID",hide:!0},{field:"Parent Node",headerName:"Parent Node",flex:1,renderCell:t=>e(le,{value:t.row["Parent Node"],placeholder:"Enter Parent Node",maxLength:10,rowId:t.row.Id,fieldName:"Parent Node",tableKey:"NEW NODES",addToChangeLog:c,updateDuplicateCheckLists:s,isDuplicateCheckField:!1})},{field:"New Node",headerName:"New Node",flex:1,renderCell:t=>e(le,{value:t.row["New Node"],placeholder:"Enter New Node",maxLength:10,rowId:t.row.Id,fieldName:"New Node",tableKey:"NEW NODES",addToChangeLog:c,updateDuplicateCheckLists:s,isDuplicateCheckField:!0,duplicateCheckType:"node"})},{field:"Description",headerName:"Description",flex:1,renderCell:t=>e(le,{value:t.row.Description,placeholder:"Enter Description",maxLength:40,rowId:t.row.Id,fieldName:"Description",tableKey:"NEW NODES",addToChangeLog:c,updateDuplicateCheckLists:s,isDuplicateCheckField:!0,duplicateCheckType:"desc"})},{field:"Action",headerName:"Action",renderCell:t=>e(ye,{title:"Delete Row",children:e(xe,{disabled:m,sx:{color:m?"grey.400":"red"},onClick:()=>f(t.row.Id,0),children:e(Me,{})})})}],_r=(f,a,E,b,c,s,m)=>[{field:"Id",headerName:"ID",type:"text",hide:!0},{field:"Parent Node",headerName:"Parent Node",type:"text",flex:1,renderCell:t=>e(le,{value:t.row["Parent Node"],placeholder:"Enter Parent Node",maxLength:10,rowId:t.row.Id,fieldName:"Parent Node",tableKey:"DESCRIPTIONS",fetchOldData:!0,fetchFunction:a,oldDataField:"Old Description",addToChangeLog:c,updateDuplicateCheckLists:s,isDuplicateCheckField:!1})},{field:"Old Description",headerName:"Old Description",flex:1,editable:!1,renderCell:t=>e("div",{style:{padding:"8px",fontSize:"14px",color:"#666"},children:t.row["Old Description"]||""})},{field:"New Description",headerName:"New Description",flex:1,renderCell:t=>e(le,{value:t.row["New Description"],placeholder:"Enter New Description",maxLength:40,rowId:t.row.Id,fieldName:"New Description",tableKey:"DESCRIPTIONS",addToChangeLog:c,updateDuplicateCheckLists:s,isDuplicateCheckField:!0,duplicateCheckType:"desc"})},{field:"Action",headerName:"Action",hide:!1,renderCell:t=>e(ye,{title:"Delete Row",children:e(xe,{disabled:m,sx:{color:m?"grey.400":"red"},onClick:()=>f(t.row.Id,1),children:e(Me,{})})})}],yr=(f,a,E,b,c,s,m)=>[{field:"Id",headerName:"ID",type:"text",hide:!0},{field:"Node",headerName:"Node",type:"text",flex:1,renderCell:t=>e(le,{value:t.row.Node,placeholder:"Enter Node",maxLength:10,rowId:t.row.Id,fieldName:"Node",tableKey:"COST CENTERS",addToChangeLog:c,updateDuplicateCheckLists:s,isDuplicateCheckField:!1})},{field:"Cost Center",headerName:"Cost Center",flex:1,renderCell:t=>e(le,{value:t.row["Cost Center"],placeholder:"Enter Cost Center",maxLength:10,rowId:t.row.Id,fieldName:"Cost Center",tableKey:"COST CENTERS",addToChangeLog:c,updateDuplicateCheckLists:s,isDuplicateCheckField:!1})},{field:"Action",headerName:"Action",hide:!1,renderCell:t=>e(ye,{title:"Delete Row",children:e(xe,{disabled:m,sx:{color:m?"grey.400":"red"},onClick:()=>f(t.row.Id,2),children:e(Me,{})})})}],xr=(f,a,E,b,c,s,m)=>[{field:"Id",headerName:"ID",hide:!0},{field:"Old Parent Node",headerName:"Old Parent Node",flex:1,editable:!1,renderCell:t=>e("div",{style:{padding:"8px",fontSize:"14px",color:"#666"},children:t.row["Old Parent Node"]||""})},{field:"New Parent Node",headerName:"New Parent Node",flex:1,renderCell:t=>e(le,{value:t.row["New Parent Node"],placeholder:"Enter New Parent Node",maxLength:10,rowId:t.row.Id,fieldName:"New Parent Node",tableKey:"MOVE NODE",addToChangeLog:c,updateDuplicateCheckLists:s,isDuplicateCheckField:!1})},{field:"Selected Node",headerName:"Selected Node",flex:1,renderCell:t=>e(le,{value:t.row["Selected Node"],placeholder:"Enter Selected Node",maxLength:10,rowId:t.row.Id,fieldName:"Selected Node",tableKey:"MOVE NODE",fetchOldData:!0,fetchFunction:E,oldDataField:"Old Parent Node",addToChangeLog:c,updateDuplicateCheckLists:s,isDuplicateCheckField:!1})},{field:"Action",headerName:"Action",renderCell:t=>e(ye,{title:"Delete Row",children:e(xe,{disabled:m,sx:{color:m?"grey.400":"red"},onClick:()=>f(t.row.Id,3),children:e(Me,{})})})}],Lr=(f,a,E,b,c,s,m)=>[{field:"Id",headerName:"ID",hide:!0},{field:"Old Parent Node",headerName:"Old Parent Node",flex:1,editable:!1,renderCell:t=>e("div",{style:{padding:"8px",fontSize:"14px",color:"#666"},children:t.row["Old Parent Node"]||""})},{field:"New Parent Node",headerName:"New Parent Node",flex:1,renderCell:t=>e(le,{value:t.row["New Parent Node"],placeholder:"Enter New Parent Node",maxLength:10,rowId:t.row.Id,fieldName:"New Parent Node",tableKey:"MOVE COST CENTER",addToChangeLog:c,updateDuplicateCheckLists:s,isDuplicateCheckField:!1})},{field:"Selected Cost Center",headerName:"Selected Cost Center",flex:1,renderCell:t=>e(le,{value:t.row["Selected Cost Center"],placeholder:"Enter Cost Center",maxLength:10,rowId:t.row.Id,fieldName:"Selected Cost Center",tableKey:"MOVE COST CENTER",fetchOldData:!0,fetchFunction:b,oldDataField:"Old Parent Node",addToChangeLog:c,updateDuplicateCheckLists:s,isDuplicateCheckField:!1})},{field:"Action",headerName:"Action",renderCell:t=>e(ye,{title:"Delete Row",children:e(xe,{disabled:m,sx:{color:m?"grey.400":"red"},onClick:()=>f(t.row.Id,4),children:e(Me,{})})})}],wr=(f,a,E,b,c,s,m)=>[{field:"Id",headerName:"ID",hide:!0},{field:"Parent Node",headerName:"Parent Node",flex:1,renderCell:t=>e("div",{style:{padding:"8px",fontSize:"14px",color:"#666"},children:t.row["Parent Node"]||""})},{field:"Selected Cost Center",headerName:"Selected Cost Center",flex:1,renderCell:t=>e(le,{value:t.row["Selected Cost Center"],placeholder:"Enter Cost Center",maxLength:10,rowId:t.row.Id,fieldName:"Selected Cost Center",tableKey:"REMOVE COST CENTER",fetchOldData:!0,fetchFunction:b,oldDataField:"Parent Node",addToChangeLog:c,updateDuplicateCheckLists:s,isDuplicateCheckField:!1})},{field:"Action",headerName:"Action",renderCell:t=>e(ye,{title:"Delete Row",children:e(xe,{disabled:m,sx:{color:m?"grey.400":"red"},onClick:()=>f(t.row.Id,5),children:e(Me,{})})})}],qr=(f,a,E,b,c,s,m)=>[{field:"Id",headerName:"ID",hide:!0},{field:"Parent Node",headerName:"Parent Node",flex:1,renderCell:t=>e("div",{style:{padding:"8px",fontSize:"14px",color:"#666"},children:t.row["Parent Node"]||""})},{field:"Deleted Node",headerName:"Deleted Node",flex:1,renderCell:t=>e(le,{value:t.row["Deleted Node"],placeholder:"Enter Node to Delete",maxLength:10,rowId:t.row.Id,fieldName:"Deleted Node",tableKey:"DELETE NODE",fetchOldData:!0,fetchFunction:E,oldDataField:"Parent Node",addToChangeLog:c,updateDuplicateCheckLists:s,isDuplicateCheckField:!1})},{field:"Action",headerName:"Action",renderCell:t=>e(ye,{title:"Delete Row",children:e(xe,{disabled:m,sx:{color:m?"grey.400":"red"},onClick:()=>f(t.row.Id,6),children:e(Me,{})})})}],kr=(f,a,E,b,c,s,m)=>[{field:"Id",headerName:"ID",hide:!0},{field:"Parent Node",headerName:"Parent Node",flex:1,renderCell:t=>e(le,{value:t.row["Parent Node"],placeholder:"Enter Parent Node",maxLength:10,rowId:t.row.Id,fieldName:"Parent Node",tableKey:"PERSON RESPONSIBLE",fetchOldData:!0,fetchFunction:a,oldDataField:"Old Person Responsible",addToChangeLog:c,updateDuplicateCheckLists:s,isDuplicateCheckField:!1})},{field:"Old Person Responsible",headerName:"Old Person Responsible",flex:1,editable:!1,renderCell:t=>e("div",{style:{padding:"8px",fontSize:"14px",color:"#666"},children:t.row["Old Person Responsible"]||""})},{field:"New Person Responsible",headerName:"New Person Responsible",flex:1,renderCell:t=>e(le,{value:t.row["New Person Responsible"],placeholder:"Enter New Person Responsible",maxLength:40,rowId:t.row.Id,fieldName:"New Person Responsible",tableKey:"PERSON RESPONSIBLE",addToChangeLog:c,updateDuplicateCheckLists:s,isDuplicateCheckField:!1})},{field:"Action",headerName:"Action",renderCell:t=>e(ye,{title:"Delete Row",children:e(xe,{disabled:m,sx:{color:m?"grey.400":"red"},onClick:()=>f(t.row.Id,7),children:e(Me,{})})})}],Ke=[{label:"New Nodes",value:"1",key:"NEW NODES"},{label:"Description Change",value:"2",key:"DESCRIPTIONS"},{label:"Add Cost Centers",value:"3",key:"COST CENTERS"},{label:"Move Node",value:"4",key:"MOVE NODE"},{label:"Move Cost Center",value:"5",key:"MOVE COST CENTER"},{label:"Remove Cost Center",value:"6",key:"REMOVE COST CENTER"},{label:"Delete Node",value:"7",key:"DELETE NODE"}],Br=()=>{const f=Xe(),[a,E]=u.useState(0),[b,c]=u.useState(0),[s,m]=u.useState(100),t=je(),H=new URLSearchParams(t.search).get("reqBench"),P=L(n=>n.hierarchyData.DisplayRecords),y=L(n=>n.hierarchyData.nodesListForDBDuplicateCheck||[]),_=L(n=>n.hierarchyData.descListForDBDuplicateCheck||[]),M=L(n=>n.userManagement.userData),{fetchOldDescriptionForNode:oe,fetchOldParentForNode:fe,fetchOldParentForObject:Ce}=Pt(),he=L(n=>{var h;return(h=n.hierarchyData.requestHeaderData)==null?void 0:h.RequestStatus}),{showSnackbar:Ie}=ds(),ce=H&&!ut.includes(he);u.useEffect(()=>{de()},[]);const de=()=>{const n=new Set,h=new Set;((P==null?void 0:P["NEW NODES"])||[]).forEach(l=>{var p,d;(p=l["New Node"])!=null&&p.trim()&&n.add(l["New Node"].trim().toUpperCase()),(d=l.Description)!=null&&d.trim()&&h.add(l.Description.trim().toUpperCase())}),((P==null?void 0:P.DESCRIPTIONS)||[]).forEach(l=>{var p;(p=l["New Description"])!=null&&p.trim()&&h.add(l["New Description"].trim().toUpperCase())});const C=Array.from(n),r=Array.from(h);(C.length>0||y.length===0)&&f(at(C)),(r.length>0||_.length===0)&&f(it(r))},$=(n,h="add")=>{var C,r;if(!(n!=null&&n.trim()))return;const i=n.trim().toUpperCase();let T=[...y];if(h==="add"&&!T.some(l=>l.toUpperCase()===i))T.push(i);else if(h==="remove")T=T.filter(l=>l.toUpperCase()!==i);else if(h==="replace"){const l=(C=n.old)==null?void 0:C.trim().toUpperCase(),p=(r=n.new)==null?void 0:r.trim().toUpperCase();l&&p&&l!==p&&(T=T.filter(d=>d.toUpperCase()!==l),T.some(d=>d.toUpperCase()===p)||T.push(p))}f(at(T))},te=(n,h="add")=>{var C,r;if(!(n!=null&&n.trim()))return;const i=n.trim().toUpperCase();let T=[..._];if(h==="add"&&!T.some(l=>l.toUpperCase()===i))T.push(i);else if(h==="remove")T=T.filter(l=>l.toUpperCase()!==i);else if(h==="replace"){const l=(C=n.old)==null?void 0:C.trim().toUpperCase(),p=(r=n.new)==null?void 0:r.trim().toUpperCase();l&&p&&l!==p&&(T=T.filter(d=>d.toUpperCase()!==l),T.some(d=>d.toUpperCase()===p)||T.push(p))}f(it(T))},Y=n=>{let h=[...y],i=[..._];n.forEach(T=>{const{type:C,value:r,action:l}=T;if(C==="node"&&(r!=null&&r.trim())){const p=r.trim().toUpperCase();l==="add"&&!h.some(d=>d.toUpperCase()===p)?h.push(p):l==="remove"&&(h=h.filter(d=>d.toUpperCase()!==p))}else if(C==="desc"&&(r!=null&&r.trim())){const p=r.trim().toUpperCase();l==="add"&&!i.some(d=>d.toUpperCase()===p)?i.push(p):l==="remove"&&(i=i.filter(d=>d.toUpperCase()!==p))}}),f(at(h)),f(it(i))},Z=(n,h)=>{if(!(n!=null&&n.trim()))return!1;const i=n.trim().toUpperCase();return h==="node"?y.some(T=>T.toUpperCase()===i):h==="desc"?_.some(T=>T.toUpperCase()===i):!1},K=(n,h,i={})=>{const T={id:gr(),type:n,description:h,updatedBy:(M==null?void 0:M.emailId)||"<EMAIL>",updatedOn:Rt().utc().format("YYYY-MM-DDTHH:mm:ss[Z]"),...i};f(Es(T))},B=(n,h,i=null,T=null,C=null)=>{if(f(fs({...P,[n]:h})),i&&T){const r=Sr(i,T,C);K(i,r,{tableName:n,rowData:{...T},oldRowData:C?{...C}:null})}},re=n=>String((n.length>0?Math.max(...n.map(h=>parseInt(h.Id))):0)+1),ee=(n,h)=>c(h),w=n=>{m(n.target.value),c(0)},ie=()=>{const n=Ke[a],h=(P==null?void 0:P[n.key])||[];let i={Id:re(h),"Updated By":M==null?void 0:M.emailId,"Updated On":Rt().utc().format("YYYY-MM-DD HH:mm:ss.SSS")};switch(a){case 0:i={...i,"Parent Node":"","New Node":"",Description:"","Person Responsible":""};break;case 1:i={...i,"Parent Node":"","Old Description":"","New Description":""};break;case 2:i={...i,Node:"","Cost Center":""};break;case 3:i={...i,"Old Parent Node":"","New Parent Node":"","Selected Node":""};break;case 4:i={...i,"Old Parent Node":"","New Parent Node":"","Selected Cost Center":""};break;case 5:i={...i,"Parent Node":"","Selected Cost Center":""};break;case 6:i={...i,"Parent Node":"","Deleted Node":""};break;case 7:i={...i,"Parent Node":"","Old Person Responsible":"","New Person Responsible":""};break}const T=[...h,i];B(n.key,T),K("ROW_CREATED",`New ${n.label.toLowerCase()} row created`,{stepLabel:n.label,rowId:i.Id})},A=(n,h)=>{const i=Ke[h],T=(P==null?void 0:P[i.key])||[],C=T.find(l=>l.Id===n),r=T.filter(l=>l.Id!==n);if(B(i.key,r),C){const l=[];switch(h){case 0:C["New Node"]&&l.push({type:"node",value:C["New Node"],action:"remove"}),C.Description&&l.push({type:"desc",value:C.Description,action:"remove"});break;case 1:C["New Description"]&&l.push({type:"desc",value:C["New Description"],action:"remove"});break}l.length>0&&Y(l);let p=`Row deleted from ${i.label}`;switch(h){case 0:C["New Node"]&&C["Parent Node"]&&(p=`Deleted new node "${C["New Node"]}" under "${C["Parent Node"]}"`);break;case 1:C["Parent Node"]&&(p=`Cancelled description change for node "${C["Parent Node"]}"`);break;case 2:C["Cost Center"]&&C.Node&&(p=`Cancelled adding cost center "${C["Cost Center"]}" to node "${C.Node}"`);break;case 3:C["Selected Node"]&&(p=`Cancelled move operation for node "${C["Selected Node"]}"`);break;case 4:C["Selected Cost Center"]&&(p=`Cancelled move operation for cost center "${C["Selected Cost Center"]}"`);break;case 5:C["Selected Cost Center"]&&C["Parent Node"]&&(p=`Cancelled removal of cost center "${C["Selected Cost Center"]}" from "${C["Parent Node"]}"`);break;case 6:C["Deleted Node"]&&C["Parent Node"]&&(p=`Cancelled deletion of node "${C["Deleted Node"]}" from "${C["Parent Node"]}"`);break;case 7:C["Parent Node"]&&(p=`Cancelled person responsible change for node "${C["Parent Node"]}"`);break}K(J.DELETE_ROW,p,{deletedRow:{...C},stepLabel:i.label})}},ne=(n,h)=>console.log("Selected rows:",n,"for step:",h),se=()=>{const n=Ke[a],h=(P==null?void 0:P[n.key])||[];let i=[];const T=[A,oe,fe,Ce,K,{updateNodesListForDuplicateCheck:$,updateDescListForDuplicateCheck:te,checkForDuplicates:Z,batchUpdateDuplicateLists:Y},ce];switch(a){case 0:i=Ir(...T);break;case 1:i=_r(...T);break;case 2:i=yr(...T);break;case 3:i=xr(...T);break;case 4:i=Lr(...T);break;case 5:i=wr(...T);break;case 6:i=qr(...T);break;case 7:i=kr(...T);break;default:i=[]}return{rows:h,columns:i,title:n.label,key:n.key}},Le=n=>{const h=Ke[n],i=(P==null?void 0:P[h.key])||[];if(i.length===0)return{isValid:!0,message:""};const C={0:["Parent Node","New Node","Description"],1:["Parent Node","New Description"],2:["Node","Cost Center"],3:["New Parent Node","Selected Node"],4:["New Parent Node","Selected Cost Center"],5:["Selected Cost Center"],6:["Deleted Node"],7:["Parent Node","New Person Responsible"]}[n]||[];for(const r of i)for(const l of C)if(!r[l]||r[l].toString().trim()==="")return{isValid:!1,message:`Please fill all required fields in ${h.label} table. Missing: ${l}`};return{isValid:!0,message:""}},ue=se(),Se=n=>{const h=Le(a);if(!h.isValid){Ie(`${h.message}`,"error");return}E(n)};return j(X,{sx:{display:"flex",flexDirection:"column",height:"100%"},children:[e(X,{sx:{width:"100%",scrollbarWidth:"none","&::-webkit-scrollbar":{display:"none"}},children:e(Cs,{nonLinear:!0,activeStep:a,sx:{mb:2},alternativeLabel:!0,children:Ke.map((n,h)=>e(hs,{children:e(Ns,{onClick:()=>Se(h),children:n.label})},n.value))})}),e(X,{sx:{flexGrow:1,overflowY:"auto",scrollbarWidth:"none","&::-webkit-scrollbar":{display:"none"}},children:e(Fe,{container:!0,children:e(Fe,{item:!0,md:12,sx:{backgroundColor:"white",maxHeight:"max-content",height:"max-content",borderRadius:"8px",border:"1px solid #E0E0E0",mt:.25,boxShadow:"0px 2px 14px 0px rgba(48, 38, 185, 0.10)",...us},children:j(X,{sx:{width:"100%",height:"auto",display:"flex",flexDirection:"column",justifyContent:"space-between"},children:[e(X,{sx:{display:"flex",justifyContent:"space-between",alignItems:"center",mt:1,mb:1},children:e(X,{children:e(ke,{variant:"contained",disabled:ce,onClick:ie,sx:{ml:1},children:"Add Row"})})}),ue.columns.length>0&&e(Co,{width:"100%",rowHeight:70,rows:ue.rows,columns:ue.columns,title:`This table shows the ${ue.title}`,rowCount:ue.rows.length,page:b,pageSize:s,onPageChange:ee,onPageSizeChange:w,getRowIdValue:"Id",hideFooter:!0,checkboxSelection:!1,callback_onRowDoubleClick:n=>console.log("params",n.row),onRowsSelectionHandler:n=>ne(n,a),stopPropagation_Column:"action",status_onRowDoubleClick:!0,showCustomNavigation:!0})]})})})})]})},Fr=f=>{var te,Y,Z,K;const a=L(B=>B.userManagement.taskData),E=je(),b=new URLSearchParams(E.search),c=b.get("RequestType"),s=b.get("reqBench"),m=L(B=>{var re;return(re=B.hierarchyData.requestHeaderData)==null?void 0:re.RequestStatus}),t=s&&!ut.includes(m)||c===((te=I)==null?void 0:te.CHANGE_WITH_UPLOAD),{getButtonsDisplayGlobal:z}=ms(),{showTree:H,blurLoading:P,openSnackBar:y,alertMsg:_,alertType:M,filteredButtons:oe,requestorPayload:fe,loadForFetching:Ce,initialNodeData:he,handleButtonClick:Ie,handleSnackBarClose:ce}=Pt();u.useEffect(()=>{(a!=null&&a.ATTRIBUTE_1||c)&&z("Hierarchy Node (Cost Center)","MDG_DYN_BTN_DT","v3")},[a]);const de=B=>Array.isArray(B)&&B.length===1&&typeof B[0]=="object"&&B[0].code&&B[0].desc?e(Ot,{children:e(Ye,{component:"span",variant:"body1",children:B[0].code})}):typeof B=="string"?B:JSON.stringify(B),$=({requestorPayload:B,renderValue:re})=>!B||!Object.keys(B).length?null:e(X,{sx:{backgroundColor:Qe.primary.whiteSmoke,px:2,py:1,borderBottom:`1px solid ${Qe.primary.whiteSmoke}`,borderRadius:"5px",mb:2},children:e(X,{sx:{display:"flex",flexWrap:"wrap",gap:3},children:Object.keys(B).map(ee=>j(X,{sx:{display:"flex",alignItems:"center",gap:1},children:[j(Ye,{variant:"body1",sx:{fontWeight:700,color:Qe.primary.grey},children:[ee," :"]}),e(X,{sx:{display:"flex",alignItems:"center"},children:re(B[ee])})]},ee))})});return j("div",{children:[j(Fe,{container:!0,sx:{height:"80vh",overflow:"hidden"},children:[j(Fe,{item:!0,md:c===((Y=I)==null?void 0:Y.CHANGE_WITH_UPLOAD)?6:12,sx:{overflowY:"auto",height:"100%",p:2,pr:1,borderRight:"1px solid #e0e0e0",scrollbarWidth:"none","&::-webkit-scrollbar":{display:"none"}},children:[e($,{requestorPayload:fe,renderValue:de}),H&&e(Dr,{initialRawTreeData:he,editmode:!t,object:"CCG",moduleObject:(Z=Ae)==null?void 0:Z.CC})]}),c===((K=I)==null?void 0:K.CHANGE_WITH_UPLOAD)&&e(Fe,{item:!0,md:6,sx:{overflowY:"auto",height:"100%",p:2,pl:1,scrollbarWidth:"none","&::-webkit-scrollbar":{display:"none"}},children:e(Br,{})})]}),e(lt,{blurLoading:Ce||P}),e(Ds,{openSnackBar:y,alertMsg:_,handleSnackBarClose:ce,alertType:M}),e(ho,{})]})},Bn=()=>{var yt,xt,Lt,wt,qt,kt,Bt,Ft,Mt,Ht,$t,vt,Gt,Ut,Wt;const{toPDF:f,targetRef:a}=No({filename:"my-component.pdf"}),{t:E}=Eo(),{customError:b}=Do(),[c,s]=u.useState(!1),[m,t]=u.useState(!1),[z,H]=u.useState([]),[P,y]=u.useState(!1),[_,M]=u.useState(!1),[oe,fe]=u.useState(""),[Ce,he]=u.useState(!1),[Ie,ce]=u.useState([]),[de,$]=u.useState(!1),[te,Y]=u.useState(!1),[Z,K]=u.useState(""),[B,re]=u.useState(),[ee,w]=u.useState(""),[ie,A]=u.useState(!1),[ne,se]=u.useState(""),[Le,ue]=u.useState(!1),[Se,n]=u.useState("success"),[h,i]=u.useState(!1),[T,C]=u.useState(!1),[r,l]=u.useState(!1),[p,d]=u.useState(!1),[F,S]=u.useState(""),o=Xe(),[q,v]=u.useState(!1),[V,ae]=u.useState({title:"",message:"",subText:"",buttonText:"",redirectTo:""}),me=L(O=>O.applicationConfig),D=L(O=>O.payload.payloadData),[Ne,R]=u.useState(!1),[_e,Re]=u.useState([]),N=L(O=>{var x;return(x=O.request.requestHeader)==null?void 0:x.requestId});L(O=>O.request.requestHeader.requestType);const Ge=L(O=>{var x;return(x=O.userManagement)==null?void 0:x.taskData}),Oe=L(O=>O.hierarchyData),{getDtCall:Ve,dtData:Mr}=mo(),we=bt(),[Ts,Hr]=u.useState(!0),be=L(O=>O.request.tabValue),Ue=L(O=>O.request.requestHeader),gs=["Request Header","Hierarchy Tree","Attachments & Remarks","Preview"],[$r,At]=u.useState([!1]),Ss=O=>{o(ze(O))},pt=je(),g=pt.state,Pe=new URLSearchParams(pt.search.split("?")[1]).get("RequestId"),ft=new URLSearchParams(pt.search),Ee=ft.get("RequestId"),Te=ft.get("RequestType"),We=ft.get("reqBench"),Rs={requestId:Ee||"",isChild:!!((yt=Oe==null?void 0:Oe.requestHeaderData)!=null&&yt.childRequestId)},{buttonsLoading:Os,filteredButtons:It,handleButtonClick:He,showWfLevels:bs,wfLevels:Ps}=Pt({setSuccessDialogOpen:v,setDialogData:ae,selectedLevel:F}),As=()=>{$(!0)},Ct=()=>{$(!1)},Is=()=>{Y(!0)},_s=O=>{Y(O)},ys=()=>{ee==="success"?we("/requestBench"):Ct()},xs=()=>{t(!0)},Ls=O=>{let x="";Te===I.CREATE_WITH_UPLOAD?x="getAllHierarchyNodeFromExcel":Te===I.CHANGE_WITH_UPLOAD&&(x="getAllHierarchyNodeFromExcelForChange"),se("Initiating Excel Upload"),A(!0);const W=new FormData;[...O].forEach(Q=>W.append("files",Q)),W.append("requestId",Pe||"");const G=Q=>{var U,pe;Q.statusCode===200?(he(!1),A(!1),se(""),we((U=qe)==null?void 0:U.REQUEST_BENCH)):(he(!1),A(!1),se(""),we((pe=qe)==null?void 0:pe.REQUEST_BENCH))},De=Q=>{var U;A(!1),se(""),we((U=qe)==null?void 0:U.REQUEST_BENCH)};Be(`/${nt}/massAction/${x}`,"postformdata",G,De,W)},ws=async(O=null)=>new Promise((x,W)=>{A(!0);const G=O||Ee,De=Ko(ot.CURRENT_TASK,!0,{}),Q=Te||(Ge==null?void 0:Ge.ATTRIBUTE_2)||(De==null?void 0:De.ATTRIBUTE_2),U=(g==null?void 0:g.childRequestIds)!=="Not Available";let pe=We?{parentId:U?"":g==null?void 0:g.requestId,massChangeId:U&&(Q===I.CHANGE||Q===I.CHANGE_WITH_UPLOAD)?G:"",massCreationId:U&&(Q===I.CREATE||Q===I.CREATE_WITH_UPLOAD)?G:""}:{parentId:"",massChangeId:Q===I.CHANGE||Q===I.CHANGE_WITH_UPLOAD?G:"",massCreationId:Q===I.CREATE||Q===I.CREATE_WITH_UPLOAD?G:""};const ge=async $e=>{var ht,Ze,et,Yt,jt,zt,Kt,Qt,Xt,Vt,Jt,Zt,es,ts;try{A(!1);const k=($e==null?void 0:$e.body)||{},{ControllingArea:Nt,ParentNode:Et,ParentDesc:ss,RequestType:tt,HierarchyTree:vs,ChangeLogId:Gs,ErrorLogId:Us,GeneralInformation:Ws,Torequestheaderdata:Ys,...js}=k,zs={ReqCreatedBy:(ht=k==null?void 0:k.Torequestheaderdata)==null?void 0:ht.ReqCreatedBy,RequestStatus:(Ze=k==null?void 0:k.Torequestheaderdata)==null?void 0:Ze.RequestStatus,Region:(et=k==null?void 0:k.Torequestheaderdata)==null?void 0:et.Region,ReqCreatedOn:new Date().toISOString(),ReqUpdatedOn:new Date().toISOString(),RequestType:(Yt=k==null?void 0:k.Torequestheaderdata)==null?void 0:Yt.RequestType,RequestDesc:(jt=k==null?void 0:k.Torequestheaderdata)==null?void 0:jt.RequestDesc,RequestPriority:(zt=k==null?void 0:k.Torequestheaderdata)==null?void 0:zt.RequestPriority,LeadingCat:(Kt=k==null?void 0:k.Torequestheaderdata)==null?void 0:Kt.LeadingCat,RequestId:(Qt=k==null?void 0:k.Torequestheaderdata)==null?void 0:Qt.RequestId,TemplateName:(Xt=k==null?void 0:k.Torequestheaderdata)==null?void 0:Xt.TemplateName};let Dt={};(tt===((Vt=I)==null?void 0:Vt.CREATE)||tt===((Jt=I)==null?void 0:Jt.CREATE_WITH_UPLOAD))&&(Dt={"Controlling Area":Nt,"Cost Center Group":Et,"Cost Center Group Description":ss}),(tt===((Zt=I)==null?void 0:Zt.CHANGE)||tt===((es=I)==null?void 0:es.CHANGE_WITH_UPLOAD))&&(Dt={"Controlling Area":Nt,"Cost Center Group":Et}),o(ns(Dt)),o(rs(zs));const Ks={ControllingArea:Nt,ParentNode:Et,ParentDesc:ss,ChangeLogId:Gs,ErrorLogId:Us,GeneralInformation:Ws,treeData:[vs],requestHeaderData:{...Ys,childRequestId:(ts=k==null?void 0:k.ToChildHeaderdata)==null?void 0:ts.RequestId},...js};o(Vo({data:Ks}));const Qs=await qs(Ee);o(Es(Qs||[])),x()}catch(k){A(!1),b(St.ERROR_GET_DISPLAY_DATA),W(k)}},Je=$e=>{A(!1),b(St.ERROR_FETCHING_DATA),W($e)};Be(`/${nt}/data/displayHierarchyTreeNodeStructureFromDb`,"post",ge,Je,pe)});u.useEffect(()=>{var W;let O=(W=rr)==null?void 0:W[be];const x=To(It,O);Re(x)},[be,It]),u.useEffect(()=>((async()=>{var x,W,G;Ee?(await ws(Ee),(Te===I.CHANGE_WITH_UPLOAD&&!((x=g==null?void 0:g.parentNode)!=null&&x.length)||Te===I.CREATE_WITH_UPLOAD&&!((W=g==null?void 0:g.parentNode)!=null&&W.length))&&((g==null?void 0:g.reqStatus)===ve.DRAFT||(g==null?void 0:g.reqStatus)===ve.UPLOAD_FAILED)?(o(ze(0)),y(!1),M(!1)):(Te===I.CREATE||Te===I.CHANGE)&&!((G=g==null?void 0:g.parentNode)!=null&&G.length)&&We?(o(ze(0)),y(!1),M(!0)):(o(ze(1)),y(!0),M(!0)),C(!0)):o(ze(0))})(),()=>{o(go([])),o(rt({})),o(So()),o(Ro()),o(rs({data:{}})),o(Oo([])),o(bo([])),o(ns({})),o(Po()),o(Ao([])),o(Io([])),o(_o({})),o(yo()),o(xo()),as(ot.CURRENT_TASK),as(ot.ROLE)}),[Pe,o]);const qs=O=>{const x=`/${nt}/node/getTreeModificationHistory?requestId=${O}`;return new Promise((W,G)=>{Be(x,"get",U=>{var pe;(U==null?void 0:U.statusCode)===Jo.STATUS_200?W(((pe=U==null?void 0:U.body)==null?void 0:pe.Records)||[]):W([])},U=>{b(U),G(U)})})};function ks(O){let x={decisionTableId:null,decisionTableName:Qo.MDG_MAT_REGION_DIVISION_MAPPING,version:"v1",conditions:[{"MDG_CONDITIONS.MDG_MAT_REGION":O}]};Ve(x)}u.useEffect(()=>{D!=null&&D.Region&&ks(D==null?void 0:D.Region)},[D==null?void 0:D.Region]),u.useEffect(()=>(Bs(),o(Lo([])),o(is({keyName:"Region",data:wo})),o(is({keyName:"DiversionControlFlag",data:qo})),()=>{o(ps({}))}),[]),u.useEffect(()=>{P&&At([!0])},[P]),u.useEffect(()=>{fe(ko("CCG")),Bo(ot.MODULE,Ae.PCG)},[]);const Bs=()=>{let O={decisionTableId:null,decisionTableName:"MDG_ATTACHMENTS_LIST_DT",version:"v1",rulePolicy:null,validityDate:null,conditions:[{"MDG_CONDITIONS.MDG_ATTACHMENTS_OBJECT_TYPE":"Material","MDG_CONDITIONS.MDG_ATTACHMENTS_SCENARIO":"Create","MDG_CONDITIONS.MDG_ATTACHMENTS_REG_COMP_CODE":1}],systemFilters:null,systemOrders:null,filterString:null};s(!0);const x=G=>{var De,Q,U,pe;if(s(!1),G.statusCode===200){let ge=(Q=(De=G==null?void 0:G.data)==null?void 0:De.result[0])==null?void 0:Q.MDG_ATTACHMENTS_ACTION_TYPE,Je=[];ge==null||ge.map((ht,Ze)=>{var et={id:Ze};Je.push(et)}),H(Je);const $e=((pe=(U=G==null?void 0:G.data)==null?void 0:U.result[0])==null?void 0:pe.MDG_ATTACHMENTS_ACTION_TYPE)||[];ce($e)}},W=G=>{console.log(G)};me.environment==="localhost"?Be(`/${ls}/rest/v1/invoke-rules`,"post",x,W,O):Be(`/${ls}/v1/invoke-rules`,"post",x,W,O)},Fs=()=>{var G,De,Q;const O=(De=(G=dt)==null?void 0:G.EXCEL)==null?void 0:De.EXPORT_HIERARCHY_EXCEL;se("Please wait 1-2 minutes while real-time data are exported into the template. Thank you for your patience."),A(!0),(Q=Oe==null?void 0:Oe.requestHeaderData)!=null&&Q.childRequestId;const x=U=>{const pe=URL.createObjectURL(U),ge=document.createElement("a");ge.href=pe,ge.setAttribute("download",`${Ee}_Data Export.xlsx`),document.body.appendChild(ge),ge.click(),document.body.removeChild(ge),URL.revokeObjectURL(pe),A(!1),se(""),ue(!0),re(`${Ee}_Data Export.xlsx has been exported successfully.`),n("success"),Ms()},W=()=>{};Be(`/${Xo}${O}?reqId=${Ee}&attachmentType=Export_Excel`,"getblobfile",x,W)},Ms=()=>{i(!0)},Hs=()=>{i(!1)},$s=()=>{var O,x,W;Pe&&!We?we((O=qe)==null?void 0:O.MY_TASK):We?we((x=qe)==null?void 0:x.REQUEST_BENCH):!Pe&&!We&&we((W=qe)==null?void 0:W.MASTER_DATA_CCG)},_t=()=>{d(!1)};return j(Ot,{children:[Ts&&e(lt,{blurLoading:Os||ie,loaderMessage:ne}),j(X,{sx:{padding:2},children:[j(Fe,{sx:{display:"flex",justifyContent:"space-between",alignItems:"center"},children:[N||Pe?j(Ye,{variant:"h6",sx:{mb:1,textAlign:"left",display:"flex",alignItems:"center",gap:1},children:[e(ur,{sx:{fontSize:"1.5rem"}}),E("Request Header ID"),":"," ",e("span",{children:N?(Ue==null?void 0:Ue.requestPrefix)+""+(Ue==null?void 0:Ue.requestId):Pe})]}):e("div",{style:{flex:1}}),be===1&&j(X,{sx:{display:"flex",justifyContent:"flex-end",gap:"1rem"},children:[e(ke,{variant:"outlined",size:"small",title:"Download Error Report",disabled:!Ee,onClick:()=>R(!0),color:"primary",children:e(Go,{sx:{padding:"2px"}})}),e(ke,{variant:"outlined",disabled:!1,size:"small",onClick:Is,title:"Change Log",children:e(fr,{sx:{padding:"2px"}})}),(Te===((xt=I)==null?void 0:xt.CREATE_WITH_UPLOAD)||Te===((Lt=I)==null?void 0:Lt.CHANGE_WITH_UPLOAD))&&e(ke,{variant:"outlined",disabled:!Ee,size:"small",onClick:Fs,title:"Export Excel",children:e(Cr,{sx:{padding:"2px"}})})]}),te&&e(cr,{open:!0,closeModal:_s,requestId:N||Pe,requestType:D==null?void 0:D.RequestType,module:(wt=Ae)==null?void 0:wt.CCG}),r&&e(CreateChangeLog,{open:!0,closeModal:()=>l(!1),requestId:N||Pe,requestType:D==null?void 0:D.RequestType}),be===3&&e(X,{sx:{display:"flex",justifyContent:"flex-end"},children:e(ke,{variant:"outlined",color:"primary",startIcon:e(dr,{}),onClick:f,children:E("Export Preview")})})]}),(D==null?void 0:D.TemplateName)&&j(Ye,{variant:"h6",sx:{mb:1,textAlign:"left",display:"flex",alignItems:"center",gap:1},children:[e(pr,{sx:{fontSize:"1.5rem"}}),E("Template Name:")," ",e("span",{children:D==null?void 0:D.TemplateName})]}),e(xe,{onClick:()=>{var O,x;if(We&&!((O=ut)!=null&&O.includes(D==null?void 0:D.RequestStatus))){we((x=qe)==null?void 0:x.REQUEST_BENCH);return}d(!0)},color:"primary","aria-label":"upload picture",component:"label",sx:{left:"-10px"},title:"Back",children:e(Uo,{sx:{fontSize:"25px",color:"#000000"}})}),e(Cs,{nonLinear:!0,activeStep:be,sx:{display:"flex",alignItems:"center",justifyContent:"center",margin:"25px 14%",marginTop:"-35px"},children:gs.map((O,x)=>e(hs,{children:e(Ns,{color:"error",disabled:x===1&&!P||x===2&&!_||x===3&&!P&&!_,onClick:()=>Ss(x),sx:{fontSize:"50px",fontWeight:"bold"},children:e("span",{style:{fontSize:"15px",fontWeight:"bold"},children:O})})},O))}),e(Wo,{dialogState:de,openReusableDialog:As,closeReusableDialog:Ct,dialogTitle:Z,dialogMessage:B,handleDialogConfirm:Ct,dialogOkText:"OK",handleOk:ys,dialogSeverity:ee}),e(mr,{dialogState:Ne,closeReusableDialog:()=>R(!1),module:(qt=Ae)==null?void 0:qt.CCG,isHierarchyCheck:!0}),e(lt,{blurLoading:ie,loaderMessage:ne}),be===0&&j(Ot,{children:[e(Tr,{setIsSecondTabEnabled:y,setIsAttachmentTabEnabled:M,requestStatus:g!=null&&g.reqStatus?g==null?void 0:g.reqStatus:ve.ENABLE_FOR_FIRST_TIME,downloadClicked:m,setDownloadClicked:t}),(Te===I.CHANGE_WITH_UPLOAD||Te===I.CREATE_WITH_UPLOAD)&&((g==null?void 0:g.reqStatus)==ve.DRAFT&&!((kt=g==null?void 0:g.material)!=null&&kt.length)||(g==null?void 0:g.reqStatus)==ve.UPLOAD_FAILED)&&e(hr,{handleDownload:xs,setEnableDocumentUpload:he,enableDocumentUpload:Ce,handleUploadMaterial:Ls}),((D==null?void 0:D.RequestType)===((Bt=I)==null?void 0:Bt.CHANGE)||(D==null?void 0:D.RequestType)===((Ft=I)==null?void 0:Ft.CHANGE_WITH_UPLOAD))&&!Ee&&(D==null?void 0:D.DirectAllowed)!=="X"&&(D==null?void 0:D.DirectAllowed)!==void 0&&j(Ye,{sx:{fontSize:"13px",fontWeight:"500",color:(Ht=(Mt=Qe)==null?void 0:Mt.error)==null?void 0:Ht.dark,marginTop:"1rem",marginLeft:"0.5rem"},children:[j(X,{component:"span",sx:{fontWeight:"bold"},children:[E("Note"),":"]})," ","You are not authorized to Tcode"," ",j(X,{component:"span",sx:{fontWeight:"bold"},children:[" ","MM02."]})]})]}),be===1&&e(Fr,{setIsAttachmentTabEnabled:!0,setCompleted:At,downloadClicked:m,setDownloadClicked:t}),be===2&&e(er,{requestStatus:g!=null&&g.reqStatus?g==null?void 0:g.reqStatus:ve.ENABLE_FOR_FIRST_TIME,attachmentsData:Ie,requestIdHeader:N||Pe,pcNumber:oe,module:($t=Ae)==null?void 0:$t.CCG,artifactName:Fo.CCG}),be===3&&e(X,{ref:a,sx:{width:"100%",overflow:"auto"},children:e(tr,{requestStatus:g!=null&&g.reqStatus?g==null?void 0:g.reqStatus:ve.ENABLE_FOR_FIRST_TIME,module:(vt=Ae)==null?void 0:vt.CCG,payloadData:Oe,payloadForPreviewDownloadExcel:Rs})}),be!=0&&e(or,{handleSaveAsDraft:He,handleSubmitForReview:He,handleSubmitForApprove:He,handleSendBack:He,handleCorrection:He,handleRejectAndCancel:He,handleValidateAndSyndicate:He,filteredButtons:_e,moduleName:(Gt=Ae)==null?void 0:Gt.CCG,isHierarchy:!0,showWfLevels:bs,selectedLevel:F,workFlowLevels:Ps,setSelectedLevel:S})]}),e(Ds,{openSnackBar:h,alertMsg:B,alertType:Se,handleSnackBarClose:Hs}),e(sr,{open:q,onClose:()=>v(!1),title:V.title,message:V.message,subText:V.subText,buttonText:V.buttonText,redirectTo:V.redirectTo}),p&&j(Mo,{isOpen:p,titleIcon:e(Yo,{size:"small",sx:{color:(Wt=(Ut=Qe)==null?void 0:Ut.secondary)==null?void 0:Wt.amber,fontSize:"20px"}}),Title:"Warning",handleClose:_t,children:[e(zo,{sx:{mt:2},children:jo.LEAVE_PAGE_MESSAGE}),j(Ho,{children:[e(ke,{variant:"outlined",size:"small",sx:{...$o},onClick:_t,children:E("No")}),e(ke,{variant:"contained",size:"small",sx:{...vo},onClick:$s,children:E("Yes")})]})]})]})};export{Bn as default};
