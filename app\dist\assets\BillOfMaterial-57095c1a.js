import{s as Ge,aP as Le,b as nt,a as ve,r as d,n as Re,aO as q,j as l,U as lt,O as A,c as p,V as st,W as at,d as m,i as it,X as E,Y as de,$ as Ae,a4 as ot,a5 as rt,a6 as ke,a7 as Te,a8 as ct,a9 as dt,ab as Ee,ac as ut,ad as Mt,at as w,k as pt,A as ft,an as Ye,w as be,C as U,cp as se,Z as Dt,x as gt,ae as _t,o as Se,g as ht,P as Ct,c5 as At,N as Tt,Q as Et,S as Oe,af as St,ag as Ot,ah as yt,bK as ye,ao as xt,F as xe,aD as It,cq as b,aT as ue,av as Nt,ay as mt,az as Pt,aA as Gt,aB as Lt,aC as vt,J as Ie,cr as Ne,T as Me,B as V,aN as Rt,aJ as le,aK as pe,aM as me}from"./index-f7d9b065.js";import"./SingleSelectDropdown-aee403d4.js";import{R as kt}from"./ReusablePresetFilter-36c3fb2e.js";import{L as Yt}from"./LargeDropdown-b2630df6.js";import{d as bt}from"./History-13dab512.js";const Ut=w(pt,{target:"eg56yq35"})(({theme:g})=>({marginTop:"0px !important",border:`1px solid ${g.palette.primary.main}`,borderRadius:"8px",boxShadow:"0 2px 4px rgba(0,0,0,0.05)","&:not(:last-child)":{borderBottom:0},"&:before":{display:"none"}}),""),wt=w(ft,{target:"eg56yq34"})(({theme:g})=>({minHeight:"2rem !important",margin:"0px !important",backgroundColor:g.palette.primary.light,borderRadius:"8px 8px 0 0",transition:"all 0.2s ease-in-out","&:hover":{backgroundColor:g.palette.primary.light}}),""),zt=w(A,{target:"eg56yq33"})({name:"seull4",styles:"padding:0.75rem;gap:0.5rem"}),$t=w(A,{target:"eg56yq32"})({name:"1ikq1ll",styles:"display:flex;justify-content:flex-end;padding-right:0.75rem;padding-bottom:0.75rem;padding-top:0rem;gap:0.5rem"}),Pe=w(Ye,{target:"eg56yq31"})({name:"1x9mjbh",styles:"border-radius:4px;padding:4px 12px;text-transform:none;font-size:0.875rem"}),fe=w(m,{target:"eg56yq30"})(({theme:g})=>({fontSize:"0.75rem",color:g.palette.primary.dark,marginBottom:"0.25rem",fontWeight:500}),""),Bt=({searchParameters:g,onSearch:W,onClear:P,filterFieldData:De,setFilterFieldData:G,items:T})=>{var F,Z,X,j,ee,te;const Y=Ge(),{customError:S}=Le(),J=nt(),{t:x}=ve(),[L,_]=d.useState([]),[ge,z]=d.useState([]),[K,Q]=d.useState({}),[ae,_e]=d.useState({});d.useState(""),d.useState([]),d.useState(!1),d.useState(null);const h=Re(s=>{var M;return s.commonFilter[(M=q)==null?void 0:M.BOM]}),$=48,ie=8,B={PaperProps:{style:{maxHeight:$*4.5+ie,width:250}}},O=()=>{var s;Y(be({module:(s=q)==null?void 0:s.BOM})),G(M=>{const f={...M};return Object.keys(f).forEach(y=>{f[y]={code:"",desc:""}}),f}),_([]),P()},oe=(s,M)=>{var y;Q(D=>({...D,[s]:M}));let f={...h,[s]:M==null?void 0:M.map(D=>D==null?void 0:D.code).join("$^$")};Y(gt({module:(y=q)==null?void 0:y.BOM,filterData:f}))},re=(s,M)=>{const f=D=>{z(v=>({...v,[M]:(D==null?void 0:D.body)||[]}))},y=()=>{S(_t.ERROR_FETCHING_DATA)};U(`/${se}/data/${s}`,"get",f,y)},ce=(s,M)=>{var v,H;const f=s==null?void 0:s.MDG_MAT_JSON_FIELD_NAME,y=x(s==null?void 0:s.MDG_MAT_UI_FIELD_NAME),D={matGroup:ge[f],selectedMaterialGroup:K[f]||[],setSelectedMaterialGroup:R=>oe(f,R),placeholder:`SELECT ${y}`,isDropDownLoading:ae[f],minWidth:"90%"};return p(A,{item:!0,md:2,children:[p(fe,{sx:de,children:[x(s==null?void 0:s.MDG_MAT_UI_FIELD_NAME),l("span",{style:{color:(H=(v=Dt)==null?void 0:v.error)==null?void 0:H.dark},children:"*"})]}),l(Ae,{size:"small",fullWidth:!0,children:l(Yt,{...D})})]},M)},he=[{key:(F=E)==null?void 0:F.MATERIALBOM,endpoint:"getMaterialMDS"},{key:(Z=E)==null?void 0:Z.PLANTBOM,endpoint:"getPlantMDS"},{key:(X=E)==null?void 0:X.BOMUSAGE,endpoint:"getBOMUsageMDS"},{key:(j=E)==null?void 0:j.COMPO,endpoint:"getAlternativeBOMMDS"},{key:(ee=E)==null?void 0:ee.DOCUMENT,endpoint:"getBOMDocument"},{key:(te=E)==null?void 0:te.DOCTYPE,endpoint:"getBOMDocumentType"}];return d.useEffect(()=>{he.forEach(({key:s,endpoint:M})=>{re(M,s)})},[]),l(A,{container:!0,sx:lt,children:l(A,{item:!0,md:12,children:p(Ut,{defaultExpanded:!1,children:[p(wt,{expandIcon:l(st,{sx:{fontSize:"1.25rem",color:J.palette.primary.dark}}),"aria-controls":"panel1a-content",id:"panel1a-header",className:"filterBillOfMaterial",children:[l(at,{sx:{fontSize:"1.25rem",marginRight:1,color:J.palette.primary.dark}}),l(m,{sx:{fontSize:"0.875rem",fontWeight:600,color:J.palette.primary.dark},children:x("Filter Bill Of Material")})]}),p(it,{sx:{padding:"1rem 1rem 0.5rem"},children:[p(zt,{container:!0,children:[p(A,{container:!0,rowSpacing:1,spacing:2,alignItems:"center",sx:{padding:"0rem 1rem 0.5rem"},children:[g==null?void 0:g.filter(s=>s.MDG_MAT_VISIBILITY!=="Hidden").sort((s,M)=>s.MDG_MAT_SEQUENCE_NO-M.MDG_MAT_SEQUENCE_NO).map((s,M)=>{const f=s==null?void 0:s.MDG_MAT_JSON_FIELD_NAME;return[E.MATERIALBOM,E.PLANTBOM,E.BOMUSAGE,E.COMPO,E.DOCUMENT,E.DOCTYPE].includes(f)?ce(s,M):null}),p(A,{item:!0,md:2,children:[l(fe,{sx:de,children:x("Add New Filters")}),l(Ae,{sx:{width:"100%"},children:l(ot,{sx:{font_Small:de,fontSize:"12px",width:"100%"},size:"small",multiple:!0,limitTags:2,value:L,onChange:s=>_(s.target.value),renderValue:s=>s.join(", "),MenuProps:B,endAdornment:L.length>0&&l(rt,{position:"end",sx:{marginRight:"10px"},children:l(ke,{size:"small",onClick:()=>_([]),"aria-label":"Clear selections",children:l(Te,{})})}),children:T==null?void 0:T.map(s=>p(ct,{value:s.title,children:[l(dt,{checked:L.indexOf(s.title)>-1}),s.title]},s.title))})})]})]}),l(A,{container:!0,sx:{flexDirection:"row",padding:"0rem 1rem 0.5rem"},gap:1,children:L.map((s,M)=>l(A,{item:!0,md:2,children:l(fe,{sx:{fontSize:"12px"},children:x(s)})},M))})]}),p($t,{children:[l(Pe,{variant:"outlined",size:"small",startIcon:l(Te,{sx:{fontSize:"1rem"}}),onClick:O,children:x("Clear")}),l(A,{sx:{...Ee},children:l(kt,{moduleName:"BillOfMaterial",handleSearch:W})}),l(Pe,{variant:"contained",size:"small",startIcon:l(ut,{sx:{fontSize:"1rem"}}),sx:{...Mt,...Ee},onClick:W,children:x("Search")})]})]})]})})})},Kt=()=>{var Ce;const{customError:g}=Le(),{getDtCall:W,dtData:P}=Se(),{getDtCall:De,dtData:G}=Se(),T=Ge(),Y=ht(),{t:S}=ve(),[J,x]=d.useState(!1),[L,_]=d.useState(!1),[ge,z]=d.useState(!1),[K,Q]=d.useState(!1),[ae,_e]=d.useState(null),[h,$]=d.useState([]),[ie,B]=d.useState([...h]),[O,oe]=d.useState((Ce=Ct)==null?void 0:Ce.TOP_SKIP),[re,ce]=d.useState({}),[he,F]=d.useState([]),[Z,X]=d.useState([]),[j,ee]=d.useState([]),[te,s]=d.useState(!1),[M,f]=d.useState(),[y,D]=d.useState(0),[v,H]=d.useState(0),[R,k]=d.useState(0),e=Re(n=>{var i;return n.commonFilter[(i=q)==null?void 0:i.BOM]});let Ue={};const we=(n,i)=>{let r={};z(!0);const a=t=>{z(!1);const u=t.body;F(c=>({...c,[i]:u}))},o=t=>{z(!1)};i==="Profit Center"?U(n,"post",a,o,r):U(n,"get",a,o)},ze=n=>{var r;let i={decisionTableId:null,decisionTableName:Ie.MDG_MAT_SEARCHSCREEN_COLUMN,version:"v2",conditions:[{"MDG_CONDITIONS.MDG_MAT_REGION":"US","MDG_CONDITIONS.MDG_MODULE":(r=Ne)==null?void 0:r.BOM,"MDG_CONDITIONS.MDG_MAT_SEARCHTYPE":"Master Data","MDG_CONDITIONS.MDG_MAT_VIEW_TYPE":"NA"}]};W(i)},$e=()=>{var i;let n={decisionTableId:null,decisionTableName:Ie.MDG_MAT_SEARCHSCREEN_PARAMETER,version:"v2",conditions:[{"MDG_CONDITIONS.MDG_MAT_REGION":"US","MDG_CONDITIONS.MDG_MODULE":(i=Ne)==null?void 0:i.BOM,"MDG_CONDITIONS.MDG_MAT_SEARCHTYPE":"Master Data","MDG_CONDITIONS.MDG_MAT_VIEW_TYPE":"NA"}]};De(n)},Be=(n,i)=>({field:n,headerName:S(i),editable:!1,flex:1,renderCell:r=>{const a=r.value?r.value.split(",").map(u=>u.trim()):[],o=a.length-1;if(a.length===0)return"-";const t=u=>{const[c,...I]=u.split("-");return p(xe,{children:[l("strong",{children:c}),I.length?` - ${I.join("-")}`:""]})};return p(V,{sx:{display:"flex",alignItems:"center",width:"100%",minWidth:0},children:[l(Me,{title:a[0],placement:"top",arrow:!0,children:l(m,{variant:"body2",sx:{overflow:"hidden",textOverflow:"ellipsis",whiteSpace:"nowrap",flex:1,minWidth:0},children:t(a[0])})}),o>0&&l(V,{sx:{display:"flex",alignItems:"center",ml:1,flexShrink:0},children:l(Me,{arrow:!0,placement:"right",title:p(V,{sx:{p:1,maxHeight:200,overflowY:"auto"},children:[p(m,{variant:"subtitle2",sx:{fontWeight:600,mb:1},children:["Additional ",i,"s (",o,")"]}),a.slice(1).map((u,c)=>l(m,{variant:"body2",sx:{mb:.5},children:t(u)},c))]}),children:p(V,{sx:{display:"flex",alignItems:"center",cursor:"pointer"},children:[l(Rt,{sx:{fontSize:"1rem",color:"primary.main","&:hover":{color:"primary.dark"}}}),p(m,{variant:"caption",sx:{ml:.5,color:"primary.main",fontSize:"11px"},children:["+",o]})]})})})]})}}),He=(n,i)=>({field:n,headerName:S(i),editable:!1,flex:1,renderCell:r=>{var t;const[a,...o]=((t=r.value)==null?void 0:t.split(" - "))||[];return p("span",{style:{flex:1,wordBreak:"break-word",whiteSpace:"normal"},children:[l("strong",{children:a})," ",o.length?`- ${o.join(" - ")}`:""]})}}),Ve=()=>({field:"dataValidation",headerName:S("Audit History"),editable:!1,flex:1,renderCell:n=>l(V,{sx:{display:"flex",justifyContent:"center",alignItems:"center",height:"100%"},children:l(Me,{title:"View Audit Log",placement:"top",children:l(ke,{onClick:r=>{var a,o;r.stopPropagation(),Y((a=ye)==null?void 0:a.AUDIT_LOG,{state:{materialNumber:n.row.Material,module:(o=q)==null?void 0:o.BOM}})},size:"small",sx:{color:"primary.main",marginLeft:"20px","&:hover":{color:"primary.dark",backgroundColor:"rgba(25, 118, 210, 0.04)",transform:"scale(1.05)",marginLeft:"20px"},transition:"all 0.2s ease-in-out"},children:l(bt,{sx:{fontSize:"1.5rem"}})})})})}),qe=n=>{const i=[];let r=(n==null?void 0:n.sort((a,o)=>a.MDG_MAT_SEQUENCE_NO-o.MDG_MAT_SEQUENCE_NO))||[];return r&&(r==null||r.forEach(a=>{if((a==null?void 0:a.MDG_MAT_VISIBILITY)===It.DISPLAY&&a!=null&&a.MDG_MAT_UI_FIELD_NAME){const o=a.MDG_MAT_JSON_FIELD_NAME,t=a.MDG_MAT_UI_FIELD_NAME;o==="DataValidation"?i.push(Ve()):a.MDG_MAT_FIELD_TYPE==="Multiple"?i.push(Be(o,t)):a.MDG_MAT_FIELD_TYPE==="Single"&&i.push(He(o,t))}})),i},We=n=>{if(!n){D(v),k(0),B([...h]);return}const i=h.filter(r=>{var t;let a=!1,o=Object.keys(r);for(let u=0;u<o.length&&(a=r[o[u]]?(r==null?void 0:r[o==null?void 0:o[u]])&&((t=r==null?void 0:r[o==null?void 0:o[u]].toString().toLowerCase())==null?void 0:t.indexOf(n==null?void 0:n.toLowerCase()))!=-1:!1,!a);u++);return a});B([...i]),D(i==null?void 0:i.length)},ne=()=>{var a,o;k(0),_(!0),s(!1);let n={material:(e==null?void 0:e.Material)??"",plant:(e==null?void 0:e.Plant)??"",bomUsage:(e==null?void 0:e.BOMUsage)??"",document:(e==null?void 0:e.Document)??"",documentType:(e==null?void 0:e.DocType)??"",altBOM:(e==null?void 0:e.AlternativeBOM)??"",component:(e==null?void 0:e.Component)??"",fromDate:b(e==null?void 0:e.Date[0]).format("YYYYMMDD")??"",toDate:b(e==null?void 0:e.Date[1]).format("YYYYMMDD")??"",top:O,skip:0};const i=t=>{var N;if((t==null?void 0:t.statusCode)===le.STATUS_200){var u=[];for(let C=0;C<((N=t==null?void 0:t.body)==null?void 0:N.length);C++){var c=t==null?void 0:t.body[C],I={id:pe(),Plant:c.Plant,BOMUsage:c.BOMUsage,AlternativeBOM:c.AltBOM,Material:c.Material,Document:c.Document!==""?c.Document:"-",DocType:c.DocumentType!==""?c.DocumentType:"-",Component:c.Component!==""?c.Component:"-"};u.push(I)}$(u.reverse()),_(!1),D(t.count),H(t.count)}else(t==null?void 0:t.statusCode)===le.STATUS_414&&(me(t==null?void 0:t.message,"error"),_(!1))},r=t=>{g(t),_(!1)};U(`/${se}${(o=(a=ue)==null?void 0:a.MASS_ACTION)==null?void 0:o.BOM_MASTER_DATA}`,"post",i,r,n)},Je=()=>{var a,o;_(!0);let n={material:(e==null?void 0:e.Material)??"",plant:(e==null?void 0:e.Plant)??"",bomUsage:(e==null?void 0:e.BOMUsage)??"",document:(e==null?void 0:e.Document)??"",documentType:(e==null?void 0:e.DocType)??"",altBOM:(e==null?void 0:e.AlternativeBOM)??"",component:(e==null?void 0:e.Component)??"",fromDate:b(e==null?void 0:e.Date[0]).format("YYYYMMDD")??"",toDate:b(e==null?void 0:e.Date[1]).format("YYYYMMDD")??"",top:O,skip:O*R};const i=t=>{var N;var u=[];for(let C=0;C<((N=t==null?void 0:t.body)==null?void 0:N.length);C++){var c=t==null?void 0:t.body[C],I={id:pe(),Plant:c.Plant,BOMUsage:c.BOMUsage,AlternativeBOM:c.AltBOM,Material:c.Material,Document:c.Document!==""?c.Document:"-",DocType:c.DocumentType!==""?c.DocumentType:"-",Component:c.Component!==""?c.Component:"-"};u.push(I)}$(C=>[...C,...u]),_(!1)},r=t=>{g(t),_(!1)};U(`/${se}${(o=(a=ue)==null?void 0:a.MASS_ACTION)==null?void 0:o.BOM_MASTER_DATA}`,"post",i,r,n)},Ke=n=>{n.map(i=>h.find(r=>r.id===i))},Qe=n=>{const i=n.target.value;oe(i),k(0)},Fe=(n,i)=>{k(isNaN(i)?0:i)},Ze=()=>{s(!0),je()},Xe=()=>{s(!0),k(0)},je=()=>{var a,o;k(0),_(!0);let n={material:(e==null?void 0:e.Material)??"",plant:(e==null?void 0:e.Plant)??"",bomUsage:(e==null?void 0:e.BOMUsage)??"",document:(e==null?void 0:e.Document)??"",documentType:(e==null?void 0:e.DocType)??"",altBOM:(e==null?void 0:e.AlternativeBOM)??"",component:(e==null?void 0:e.Component)??"",fromDate:b(e==null?void 0:e.Date[0]).format("YYYYMMDD")??"",toDate:b(e==null?void 0:e.Date[1]).format("YYYYMMDD")??"",top:v,skip:0};const i=t=>{var N;if((t==null?void 0:t.statusCode)===le.STATUS_200){var u=[];for(let C=0;C<((N=t==null?void 0:t.body)==null?void 0:N.length);C++){var c=t==null?void 0:t.body[C],I={id:pe(),Plant:c.Plant,BOMUsage:c.BOMUsage,AlternativeBOM:c.AltBOM,Material:c.Material,Document:c.Document!==""?c.Document:"-",DocType:c.DocumentType!==""?c.DocumentType:"-",Component:c.Component!==""?c.Component:"-"};u.push(I)}$(u.reverse()),_(!1),k(Math.floor((u==null?void 0:u.length)/O)),D(t.count),H(t.count)}else(t==null?void 0:t.statusCode)===le.STATUS_414&&(me(t==null?void 0:t.message,"error"),_(!1))},r=t=>{g(t)};U(`/${se}${(o=(a=ue)==null?void 0:a.MASS_ACTION)==null?void 0:o.BOM_MASTER_DATA}`,"post",i,r,n)},et=()=>{ne()},tt=()=>{T(Nt()),T(mt()),T(Pt()),T(Gt([])),T(Lt({})),T(vt({}))};return d.useEffect(()=>{K&&(ne(),Q(!1))},[K]),d.useEffect(()=>(tt(),we([Ue]),()=>{var n;T(be({module:(n=At)==null?void 0:n.BOM,days:7}))}),[]),d.useEffect(()=>B([...h]),[h]),d.useEffect(()=>{var n,i,r,a;if(P){const o=qe((i=(n=P==null?void 0:P.result)==null?void 0:n[0])==null?void 0:i.MDG_MAT_SEARCHSCREEN_COLUMN_ACTION_TYPE);X(o)}if(G){const o=(a=(r=G==null?void 0:G.result)==null?void 0:r[0])==null?void 0:a.MDG_MAT_SEARCHSCREEN_PARAMETER_ACTION_TYPE,t=o==null?void 0:o.filter(u=>u.MDG_MAT_FILTER_TYPE==="Additional").map(u=>({title:S(u.MDG_MAT_UI_FIELD_NAME)}));ee(o),f(t)}},[P,G]),d.useEffect(()=>{ne()},[O]),d.useEffect(()=>{ze(),$e()},[]),d.useEffect(()=>{te||R!=0&&R*O>=(h==null?void 0:h.length)&&Je()},[R,O]),l(xe,{children:l("div",{style:{...Tt,backgroundColor:"#FAFCFF"},children:p(Oe,{spacing:1,children:[p(A,{container:!0,mt:0,sx:Et,children:[p(A,{item:!0,md:5,children:[l(m,{variant:"h3",children:l("strong",{children:S("Bill Of Material")})}),l(m,{sx:{mb:"0.5rem"},variant:"body2",color:"#777",children:S("This view displays the Bill of Materials")})]}),l(Bt,{searchParameters:j,onSearch:ne,onClear:()=>Q(!0),filterFieldData:re,setFilterFieldData:ce,items:M})]}),l(A,{item:!0,sx:{position:"relative"},children:l(Oe,{children:l(St,{isLoading:L,paginationLoading:L,module:"Bill Of Material",width:"100%",title:S("List of Bill Of Material"),rows:ie??[],columns:Z??[],showSearch:!0,showRefresh:!0,showSelectedCount:!0,showExport:!0,onSearch:n=>We(n),onRefresh:et,pageSize:O,page:R,onPageSizeChange:Qe,rowCount:y??(h==null?void 0:h.length)??0,onPageChange:Fe,getRowIdValue:"id",hideFooter:!0,disableSelectionOnClick:!0,status_onRowSingleClick:!0,tempheight:"calc(100vh - 320px)",onRowsSelectionHandler:Ke,callback_onRowSingleClick:n=>{var r,a;const i=n.row.Number;(a=(r=n==null?void 0:n.row)==null?void 0:r.materialType)==null||a.split(" - ")[0],x(!0),Y(`/masterDataCockpit/articleMaster/DisplayArticleSAPView/${i}`,{state:n.row})},showCustomNavigation:!0,stopPropagation_Column:"action",status_onRowDoubleClick:!0,showFirstPageoptions:!0,showSelectAllOptions:!0,onSelectAllOptions:Ze,onSelectFirstPageOptions:Xe})})}),l(Ot,{sx:{position:"fixed",bottom:0,left:0,right:0},elevation:2,children:l(yt,{className:"container_BottomNav",showLabels:!0,sx:{display:"flex",justifyContent:"flex-end",gap:1},value:ae,onChange:n=>{_e(n)},children:l(Ye,{size:"small",variant:"contained",className:"createRequestButtonBOM",onClick:()=>{var n;Y((n=ye)==null?void 0:n.CREATE_BOM),T(xt({}))},children:S("Create Request")})})})]})})})};export{Kt as default};
