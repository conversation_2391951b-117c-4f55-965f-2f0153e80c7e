import{n as a,r as l,j as e,B as m,bI as o}from"./index-f7d9b065.js";import{i as c}from"./index-c3f8f9be.js";import"./react-beautiful-dnd.esm-6b676f13.js";import"./redux-dc18bb29.js";import"./index-6a70352f.js";import"./index-0aa14859.js";import"./Check-87f6ada7.js";import"./FileUploadOutlined-4a68a28a.js";import"./DeleteOutline-584dc929.js";import"./Delete-5278579a.js";import"./asyncToGenerator-88583e02.js";import"./FileDownloadOutlined-59854a55.js";import"./AddOutlined-9a9caebd.js";import"./DeleteOutlineOutlined-8fd07dc7.js";import"./EditOutlined-a6f382b7.js";import"./Edit-51c94b76.js";import"./index-67deb11b.js";import"./index-ae6cbb07.js";import"./DataObject-52409c14.js";import"./lz-string-0665f106.js";import"./VisibilityOutlined-b6cd6d28.js";import"./Remove-82c67208.js";import"./ChevronRight-a85c6b03.js";import"./index-7ffbe79f.js";import"./DeleteOutlined-e668453f.js";import"./index-6362276a.js";import"./History-13dab512.js";const A=d=>{const p=[{Description:"",Name:"WorkRulesServices",URL:o},{Description:"",Name:"CW_Worktext",URL:o},{Description:"",Name:"WorkRuleEngineServices",URL:o},{Description:"",Name:"WorkUtilsServices",URL:o}],r=a(t=>t==null?void 0:t.userManagement),i=r==null?void 0:r.userData;let s={emailId:i==null?void 0:i.emailId,user_id:i==null?void 0:i.user_id};const n=l.useMemo(()=>e(c.Modelling,{translationDataObjects:[],userDetails:s,destinations:p}),[i]);return e(m,{className:"content",sx:{padding:"8px 16px"},children:e(m,{sx:{height:"100%","& .MuiSnackbar-root":{left:"50% !important"}},children:n})})};export{A as default};
