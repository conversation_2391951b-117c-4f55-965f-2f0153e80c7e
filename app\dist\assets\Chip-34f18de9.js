import{qa as e,cb as r,q6 as a,qH as l}from"./index-f7d9b065.js";const n=o=>r.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"none",...o,children:r.jsx("g",{id:"Icons /General",children:r.jsx("path",{d:"M12.6185 7.50008L7.61849 12.5001M7.61849 7.50008L12.6185 12.5001M18.4518 10.0001C18.4518 14.6025 14.7209 18.3334 10.1185 18.3334C5.51612 18.3334 1.78516 14.6025 1.78516 10.0001C1.78516 5.39771 5.51612 1.66675 10.1185 1.66675C14.7209 1.66675 18.4518 5.39771 18.4518 10.0001Z",stroke:"currentColor",strokeWidth:"1.25",strokeLinecap:"round",strokeLinejoin:"round"})})}),t=e(n),c=a(l)({display:"inline-flex",padding:"0.25rem 0.5rem",flexDirection:"row",justifyContent:"center",alignItems:"center",gap:"0.5rem",flexShrink:0,borderRadius:"1rem",fontFamily:"inherit","&.Mui-disabled":{backgroundColor:"var(--background-disabled)",color:"var(--text-disabled)"},"& .MuiChip-avatar":{margin:"0rem"},"&.MuiChip-clickable":{cursor:"pointer","&:hover":{backgroundColor:"var(--text-disabled)"}},"&.MuiChip-deletable":{"& .MuiChip-deleteIcon":{color:"var(--text-secondary)",margin:"0rem","&:hover":{color:"var(--text-primary)"}}},"&.MuiChip-filled":{color:"var(--text-primary)",border:"1px solid var(--divider-primary)",backgroundColor:"var(--background-read-only)","&.MuiChip-colorPrimary":{color:"var(--primary-dark)",backgroundColor:"var(--primary-light)",borderColor:"var(--primary-light)"},"&.MuiChip-colorSuccess":{color:"var(--success-dark)",backgroundColor:"var(--success-light)",borderColor:"var(--success-light)"},"&.MuiChip-colorError":{color:"var(--error-dark)",backgroundColor:"var(--error-light)",borderColor:"var(--error-light)"},"&.MuiChip-colorInfo":{color:"var(--info-dark)",backgroundColor:"var(--info-light)",borderColor:"var(--info-light)"},"&.MuiChip-colorWarning":{color:"var(--warning-main)",backgroundColor:"var(--warning-light)",borderColor:"var(--warning-light)"}},"& .MuiChip-icon":{color:"inherit",margin:"0rem",marginBottom:"0.125rem"},"& .MuiChip-label":{padding:"0.5rem 0rem",fontSize:"0.875rem",lineHeight:"normal",textOverflow:"ellipsis",fontStyle:"normal"},"&.MuiChip-outlined":{border:"1px solid var(--divider-primary)",color:"var(--text-primary)","&.MuiChip-colorPrimary":{color:"var(--primary-main)",borderColor:"var(--primary-main)"},"&.MuiChip-colorSuccess":{color:"var(--success-main)",borderColor:"var(--success-main)"},"&.MuiChip-colorError":{color:"var(--error-dark)",borderColor:"var(--error-dark)"},"&.MuiChip-colorInfo":{color:"var(--info-main)",borderColor:"var(--info-main)"},"&.MuiChip-colorWarning":{color:"var(--warning-main)",borderColor:"var(--warning-main)"}},"&.MuiChip-sizeMedium":{height:"1.5rem","& .MuiChip-avatar":{height:"1.25rem",width:"1.25rem"},"& .MuiChip-icon":{fontSize:"0.75rem"}},"&.MuiChip-sizeSmall":{height:"1.25rem","& .MuiChip-avatar":{height:"1rem",width:"1rem"},"& .MuiChip-icon":{fontSize:"1rem"}}});function s({deleteIcon:o,...i}){return r.jsx(c,{...i,deleteIcon:o||r.jsx(t,{size:"xxsmall"})})}export{t as i,s as m};
