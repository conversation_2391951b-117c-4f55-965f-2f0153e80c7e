import{aP as va,n as L,cZ as Ja,bf as Xa,s as Qa,u as Za,wU as ea,eb as za,da as ja,aX as Ha,C as k,bI as Ba,aT as v,aJ as p,M as pa,H as $a,wV as at,wW as tt,wX as et,r as ba,fw as R,aA as Wa,aB as Ka,wY as Ya,fS as b,fx as xa,fy as ot,g4 as lt,aK as m,wZ as ka,w_ as st,w$ as dt,bJ as oa,au as la,fY as ct}from"./index-f7d9b065.js";const ht=()=>{const{customError:g}=va(),u=L(N=>N.payload.payloadData),U=L(N=>N.applicationConfig),q=Ja(Xa.CURRENT_TASK);let G=null;G=typeof q=="string"?JSON.parse(q):q;let W=G==null?void 0:G.ATTRIBUTE_5;const O=Qa(),V=Za(),P=new URLSearchParams(V.search),B=L(N=>N.userManagement.taskData),A=P.get("reqBench"),aa=P.get("RequestId");return{getChangeTemplate:(N,S)=>{var Z,z,j;O(ea(!0));let Q={decisionTableId:null,decisionTableName:"MDG_MAT_CHANGE_TEMPLATE",version:"v6",rulePolicy:null,validityDate:null,conditions:[{"MDG_CONDITIONS.MDG_MAT_TEMPLATE":(u==null?void 0:u.TemplateName)||(N==null?void 0:N.TemplateName),"MDG_CONDITIONS.MDG_MAT_REGION":(u==null?void 0:u.Region)||(N==null?void 0:N.Region)||((Z=za)==null?void 0:Z.US),"MDG_CONDITIONS.MDG_MAT_GROUP_ROLE":aa&&!A?(B==null?void 0:B.ATTRIBUTE_5)||W:(z=ja)==null?void 0:z.REQ_INITIATE,"MDG_CONDITIONS.MDG_MAT_SCENARIO":(u==null?void 0:u.RequestType)||(N==null?void 0:N.RequestType)||((j=Ha)==null?void 0:j.CHANGE)}],systemFilters:null,systemOrders:null,filterString:null};const J=F=>{var a,o,c,s,t,e,l,h,d,T,D,C,n,_;if(F.statusCode===((a=p)==null?void 0:a.STATUS_200)){O(ea(!1));let f=(c=(o=F==null?void 0:F.data)==null?void 0:o.result[0])==null?void 0:c.MDG_MAT_CHANGE_TEMPLATE,r=(u==null?void 0:u.TemplateName)||(N==null?void 0:N.TemplateName)||"";const E=(t=(s=f==null?void 0:f.filter(i=>i.MDG_MAT_VIEW_NAME!=="Header"))==null?void 0:s.sort((i,M)=>i.MDG_MAT_FIELD_SEQUENCE-M.MDG_MAT_FIELD_SEQUENCE))==null?void 0:t.map(i=>({fieldName:i.MDG_MAT_UI_FIELD_NAME,viewName:i.MDG_MAT_VIEW_NAME,sequenceNo:i.MDG_MAT_FIELD_SEQUENCE,fieldType:i.MDG_MAT_FIELD_TYPE,maxLength:i.MDG_MAT_MAX_LENGTH,value:i.MDG_MAT_DEFAULT_VALUE,visibility:i.MDG_MAT_VISIBILITY,jsonName:i.MDG_MAT_JSON_FIELD_NAME,templateVisibility:i.MDG_MAT_TEMPLATE_SELECTIVITY}));let K=[];const Y=E==null?void 0:E.reduce((i,M)=>{M.fieldName!=="Material"&&M.fieldName!=="Plant"&&M.fieldName!=="Warehouse"&&M.fieldName!=="Sales Org"&&M.fieldName!=="Distribution Channel"&&M.fieldName!=="MRP Controller"&&K.push({code:M.fieldName,desc:""});const $=M.viewName;return i[$]||(i[$]=[]),i[$].push(M),i},{}),H=(l=(e=Object==null?void 0:Object.keys(Y))==null?void 0:e.sort())==null?void 0:l.reduce((i,M)=>(i[M]=Y[M],i),{}),x=[];E==null||E.forEach(i=>{(i==null?void 0:i.visibility)==="Mandatory"&&x.push({jsonName:i==null?void 0:i.jsonName,fieldName:i==null?void 0:i.fieldName})});const y={[r]:E,"Config Data":H,"Field Selectivity":(h=E[0])==null?void 0:h.templateVisibility,FieldNames:K,"Mandatory Fields":[{jsonName:"Material",fieldName:"Material"},...x]};if(O(pa({keyName:"FieldName",data:y==null?void 0:y.FieldNames})),O($a(y)),((d=Object==null?void 0:Object.keys(S))==null?void 0:d.length)>0){const i=at(y==null?void 0:y["Config Data"],(D=(T=S==null?void 0:S.Torequestheaderdata)==null?void 0:T.FieldName)==null?void 0:D.split("$^$"),["Material","Plant","Sales Org","Distribution Channel","Warehouse","MRP Controller"]);O($a({...y,"Config Data":i}));const M=tt(y==null?void 0:y[r||((C=S==null?void 0:S.Torequestheaderdata)==null?void 0:C.TemplateName)],(_=(n=S==null?void 0:S.Torequestheaderdata)==null?void 0:n.FieldName)==null?void 0:_.split("$^$"));O(et([...M]))}}else O(ea(!1)),g("Failed to fetch data")},X=F=>{g(F)};U.environment==="localhost"?k(`/${Ba}${v.INVOKE_RULES.LOCAL}`,"post",J,X,Q):k(`/${Ba}${v.INVOKE_RULES.PROD}`,"post",J,X,Q)}}},ut=()=>{const g=Qa(),u=L(a=>a.userManagement.taskData),U=L(a=>a.paginationData),q=L(a=>a.payload.changeFieldRows),G=L(a=>a.payload.changeFieldRowsDisplay),W=L(a=>a.payload.dynamicKeyValues),O=L(a=>a.payload.selectedRows),V=L(a=>a.payload.whseList),P=L(a=>a.payload.plantList),B=L(a=>a.payload.matNoList),{customError:A}=va(),aa=a=>{var l,h,d,T,D,C,n,_,f,r,E,K,Y,H,x,y,i,M,$,da,ca,ia,ha,ua,Ta,ga,Na,Ea,Ma,Da,na,Ia,Ca,fa,_a,ya,ma,Sa,La,Aa,ra,Oa,Pa,wa,Ua,Fa,Ga,Va;const o=((l=a[0])==null?void 0:l.TemplateName)===((h=R)==null?void 0:h.LOGISTIC)?sa(a):((d=a[0])==null?void 0:d.TemplateName)===((T=R)==null?void 0:T.ITEM_CAT)?N(a):((D=a[0])==null?void 0:D.TemplateName)===((C=R)==null?void 0:C.MRP)?S(a):((n=a[0])==null?void 0:n.TemplateName)===((_=R)==null?void 0:_.UPD_DESC)?X(a):((f=a[0])==null?void 0:f.TemplateName)===((r=R)==null?void 0:r.WARE_VIEW_2)?Z(a):((E=a[0])==null?void 0:E.TemplateName)===((K=R)==null?void 0:K.CHG_STAT)?Q(a):((Y=a[0])==null?void 0:Y.TemplateName)===((H=R)==null?void 0:H.SET_DNU)?J(a):[];if(Array.isArray(o))g(Wa([...q,...o])),g(Ka({...G,[U==null?void 0:U.page]:o}));else if(typeof o=="object"&&o!==null){const I={...q};(x=Object==null?void 0:Object.keys(o))==null||x.forEach(w=>{I[w]=[...I[w]||[],...o[w]]}),g(Wa(I)),g(Ka({...G,[U==null?void 0:U.page]:o}))}let c;if(Array.isArray(o))c=o.map(I=>I==null?void 0:I.id),g(Ya([...O,...c]));else if(typeof o=="object"&&o!==null){c=Object.keys(o).reduce((w,Ra)=>{var qa;return w[Ra]=((qa=o[Ra])==null?void 0:qa.map(ta=>ta==null?void 0:ta.id))||[],w},{});const I={...O};(y=Object==null?void 0:Object.keys(c))==null||y.forEach(w=>{I[w]=[...I[w]||[],...c[w]]}),g(Ya(I))}g(b({keyName:"requestHeaderData",data:(i=a[0])==null?void 0:i.Torequestheaderdata})),g(b({keyName:"childRequestHeaderData",data:(M=a[0])==null?void 0:M.Tochildrequestheaderdata})),g(b({keyName:"changeLogData",data:($=a[0])==null?void 0:$.changeLogData})),g(b({keyName:"templateName",data:(da=a[0])==null?void 0:da.TemplateName}));const s={};a==null||a.forEach(I=>{s[I==null?void 0:I.Material]=I==null?void 0:I.Tomaterialerrordata}),g(b({keyName:"errorData",data:{...(W==null?void 0:W.errorData)||{},...s}}));const t={};t.IntermediateTaskCount=(ca=a[0])==null?void 0:ca.IntermediateTaskCount,t.TotalIntermediateTasks=(ia=a[0])==null?void 0:ia.TotalIntermediateTasks,t.MassEditId=(ha=a[0])==null?void 0:ha.MassEditId,t.MassChildEditId=(ua=a[0])==null?void 0:ua.MassChildEditId,t.Comments=((Ta=a[0])==null?void 0:Ta.Comments)||"",t.TaskId=u==null?void 0:u.taskId,t.TaskName=u==null?void 0:u.taskDesc,t.CreationTime=u!=null&&u.createdOn?xa(u==null?void 0:u.createdOn):null,t.DueDate=u!=null&&u.criticalDeadline?xa(u==null?void 0:u.criticalDeadline):null,g(b({keyName:"otherPayloadData",data:t}));const e={ReqCreatedBy:(Na=(ga=a[0])==null?void 0:ga.Torequestheaderdata)==null?void 0:Na.ReqCreatedBy,RequestStatus:(Ma=(Ea=a[0])==null?void 0:Ea.Torequestheaderdata)==null?void 0:Ma.RequestStatus,Region:(na=(Da=a[0])==null?void 0:Da.Torequestheaderdata)==null?void 0:na.Region,ReqCreatedOn:new Date().toISOString(),ReqUpdatedOn:new Date().toISOString(),RequestType:(Ca=(Ia=a[0])==null?void 0:Ia.Torequestheaderdata)==null?void 0:Ca.RequestType,RequestDesc:(_a=(fa=a[0])==null?void 0:fa.Torequestheaderdata)==null?void 0:_a.RequestDesc,RequestPriority:(ma=(ya=a[0])==null?void 0:ya.Torequestheaderdata)==null?void 0:ma.RequestPriority,LeadingCat:(La=(Sa=a[0])==null?void 0:Sa.Torequestheaderdata)==null?void 0:La.LeadingCat,RequestId:(ra=(Aa=a[0])==null?void 0:Aa.Torequestheaderdata)==null?void 0:ra.RequestId,TemplateName:(Pa=(Oa=a[0])==null?void 0:Oa.Torequestheaderdata)==null?void 0:Pa.TemplateName,FieldName:(Fa=(Ua=(wa=a[0])==null?void 0:wa.Torequestheaderdata)==null?void 0:Ua.FieldName)==null?void 0:Fa.split("$^$"),Version:((Va=(Ga=a[0])==null?void 0:Ga.Torequestheaderdata)==null?void 0:Va.Version)||ot.DEFAULT_VERSION};g(lt({data:e}))},sa=a=>{const o=[];let c=1;const s=new Set;return a.forEach(t=>{t.Touomdata.forEach(e=>{var h,d;s.add(e.Material);const l={...e,Material:t==null?void 0:t.Material,MaterialId:t==null?void 0:t.MaterialId,Version:t==null?void 0:t.Version,ClientId:(h=t==null?void 0:t.Toclientdata)==null?void 0:h.ClientId,id:m(),slNo:c++,MatlType:(t==null?void 0:t.MatlType)||"",ChangeLogId:((d=t==null?void 0:t.changeLogData)==null?void 0:d.ChangeLogId)??null};o.push(l)})}),g(ka([...B,...s])),o},N=a=>{const o=[];let c=1;return a.forEach(s=>{s.Tosalesdata.forEach(t=>{var l,h;const e={...t,Material:s==null?void 0:s.Material,MaterialId:s==null?void 0:s.MaterialId,Version:s==null?void 0:s.Version,ClientId:(l=s==null?void 0:s.Toclientdata)==null?void 0:l.ClientId,id:m(),slNo:c++,MatlType:(s==null?void 0:s.MatlType)||"",ChangeLogId:((h=s==null?void 0:s.changeLogData)==null?void 0:h.ChangeLogId)??null};o.push(e)})}),o},S=a=>{const o={"Basic Data":[],"Plant Data":[]};let c=1,s=1;const t=new Set;return a.forEach(e=>{var C;const{Toplantdata:l,Toclientdata:h,Material:d,MaterialId:T,MatlType:D}=e;o["Basic Data"].push({...h,id:m(),slNo:c++,type:"Basic Data",Material:d,MaterialId:T,Version:e==null?void 0:e.Version,Function:"UPD",MatlType:D,ChangeLogId:((C=e==null?void 0:e.changeLogData)==null?void 0:C.ChangeLogId)??null}),l==null||l.forEach(n=>{var _;t.add(n==null?void 0:n.Plant),o["Plant Data"].push({...n,id:m(),Material:d,slNo:s++,type:"Plant Data",MaterialId:T,Function:"UPD",ChangeLogId:((_=e==null?void 0:e.changeLogData)==null?void 0:_.ChangeLogId)??null})})}),g(st([...P,...t])),o},Q=a=>{const o={"Basic Data":[],"Plant Data":[],"Sales Data":[]};let c=1,s=1,t=1;return a.forEach(e=>{var n;const{Toplantdata:l,Toclientdata:h,Tosalesdata:d,Material:T,MaterialId:D,MatlType:C}=e;o["Basic Data"].push({...h,id:m(),slNo:c++,type:"Basic Data",Material:T,MaterialId:D,Version:e==null?void 0:e.Version,Function:"UPD",MatlType:C,ChangeLogId:((n=e==null?void 0:e.changeLogData)==null?void 0:n.ChangeLogId)??null}),l==null||l.forEach(_=>{var f;o["Plant Data"].push({..._,id:m(),Material:T,slNo:s++,type:"Plant Data",MaterialId:D,Function:"UPD",ChangeLogId:((f=e==null?void 0:e.changeLogData)==null?void 0:f.ChangeLogId)??null})}),d==null||d.forEach(_=>{var f;o["Sales Data"].push({..._,id:m(),Material:T,slNo:t++,type:"Sales Data",MaterialId:D,Function:"UPD",ChangeLogId:((f=e==null?void 0:e.changeLogData)==null?void 0:f.ChangeLogId)??null})})}),o},J=a=>{const o={"Basic Data":[],"Plant Data":[],"Sales Data":[],Description:[]};let c=1,s=1,t=1,e=1;return a.forEach(l=>{var f;const{Toplantdata:h,Toclientdata:d,Tosalesdata:T,Tomaterialdescription:D,Material:C,MaterialId:n,MatlType:_}=l;o["Basic Data"].push({...d,id:m(),slNo:c++,type:"Basic Data",Material:C,MaterialId:n,Version:l==null?void 0:l.Version,Function:"UPD",MatlType:_,ChangeLogId:((f=l==null?void 0:l.changeLogData)==null?void 0:f.ChangeLogId)??null}),h==null||h.forEach(r=>{var E;o["Plant Data"].push({...r,id:m(),Material:C,slNo:s++,type:"Plant Data",MaterialId:n,Function:"UPD",ChangeLogId:((E=l==null?void 0:l.changeLogData)==null?void 0:E.ChangeLogId)??null})}),T==null||T.forEach(r=>{var E;o["Sales Data"].push({...r,id:m(),Material:C,slNo:t++,type:"Sales Data",MaterialId:n,Function:"UPD",ChangeLogId:((E=l==null?void 0:l.changeLogData)==null?void 0:E.ChangeLogId)??null})}),D==null||D.forEach(r=>{var E;o.Description.push({...r,id:m(),Material:C,slNo:e++,type:"Description",MaterialId:n,Function:"UPD",ChangeLogId:((E=l==null?void 0:l.changeLogData)==null?void 0:E.ChangeLogId)??null})})}),o},X=a=>{const o=[];let c=1;const s=new Set;return a.forEach(t=>{t.Tomaterialdescription.forEach(e=>{var h,d;s.add(e.Material);const l={...e,Material:t==null?void 0:t.Material,MaterialId:t==null?void 0:t.MaterialId,Version:t==null?void 0:t.Version,ClientId:(h=t==null?void 0:t.Toclientdata)==null?void 0:h.ClientId,id:m(),slNo:c++,MatlType:(t==null?void 0:t.MatlType)||"",ChangeLogId:((d=t==null?void 0:t.changeLogData)==null?void 0:d.ChangeLogId)??null};o.push(l)})}),g(ka([...B,...s])),o},Z=a=>{const o=[],c=new Set;let s=1;a.forEach(e=>{e.Towarehousedata.forEach(l=>{var d,T;c.add(l.WhseNo);const h={...l,Material:e==null?void 0:e.Material,MaterialId:e==null?void 0:e.MaterialId,Version:e==null?void 0:e.Version,ClientId:(d=e==null?void 0:e.Toclientdata)==null?void 0:d.ClientId,id:m(),slNo:s++,MatlType:(e==null?void 0:e.MatlType)||"",ChangeLogId:((T=e==null?void 0:e.changeLogData)==null?void 0:T.ChangeLogId)??null};o.push(h)})});const t=[...c];return g(dt(t)),o};ba.useEffect(()=>{(async()=>{if((V==null?void 0:V.length)>0){const o=await z(V);g(oa({keyName:"Unittype1",data:o}))}})()},[V]);const z=async a=>{const o={};for(const c of a){let s={whseNo:c};try{const t=await new Promise(e=>{var l,h;k(`/${la}${(h=(l=v)==null?void 0:l.DEPENDENT_LOOKUPS)==null?void 0:h.UNITTYPE}`,"post",d=>{var T,D;d.statusCode===((T=p)==null?void 0:T.STATUS_200)?e(d==null?void 0:d.body):(A((D=ct)==null?void 0:D.ERROR_MSG),e([]))},d=>{A(d),e([])},s)});o[c]=t}catch(t){A(t),o[c]=[]}}return o};ba.useEffect(()=>{(async()=>{if((P==null?void 0:P.length)>0){const o=await j(P);g(oa({keyName:"Spproctype",data:o}));const c=await F(P);g(oa({keyName:"MrpCtrler",data:c}))}})()},[P]);const j=async a=>{const o={};for(const c of a){let s={plant:c};try{const t=await new Promise(e=>{var l,h;k(`/${la}${(h=(l=v)==null?void 0:l.DATA)==null?void 0:h.GET_SPPROC_TYPE}`,"post",d=>{var T;d.statusCode===((T=p)==null?void 0:T.STATUS_200)?e(d==null?void 0:d.body):(A("Failed to fetch data"),e([]))},d=>{A(d),e([])},s)});o[c]=t}catch(t){A(t),o[c]=[]}}return o},F=async a=>{const o={};for(const c of a){let s={plant:c};try{const t=await new Promise(e=>{var l,h;k(`/${la}${(h=(l=v)==null?void 0:l.DATA)==null?void 0:h.GET_MRP_CONTROLLER_BASED_ON_PLANT}`,"post",d=>{var T;d.statusCode===((T=p)==null?void 0:T.STATUS_200)?e(d==null?void 0:d.body):(A("Failed to fetch data"),e([]))},d=>{A(d),e([])},s)});o[c]=t}catch(t){A(t),o[c]=[]}}return o};return{fetchDisplayDataRows:aa}};export{ut as a,ht as u};
