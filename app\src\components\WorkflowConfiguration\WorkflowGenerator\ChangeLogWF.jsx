import React, { useState, useEffect } from 'react';
import {
  <PERSON><PERSON>,
  DialogTitle,
  DialogContent,
  <PERSON>alogA<PERSON>,
  Button,
  Chip,
  Box,
  Typography,
  Paper,
  IconButton,
  Tooltip
} from '@mui/material';
import { EyeOutlined, CloseOutlined } from '@ant-design/icons';
import ReusableDataTable from "@components/Common/ReusableTable";
import ChangeCircleOutlinedIcon from "@mui/icons-material/ChangeCircleOutlined";
import { WORKFLOW_DATA_CONSTANTS } from '@constant/enum';
import { useSelector } from 'react-redux';
import { formatDateTime } from "@helper/helper.js";

const ChangeLogWF = ({ open, onClose, data }) => {
  const appSettings = useSelector((state) => state.appSettings);
  const [taskDetailsPopup, setTaskDetailsPopup] = useState({
    show: false,
    content: '',
    title: ''
  });

  const parseTaskDetails = (taskDetailsString) => {
    if (!taskDetailsString || !taskDetailsString.includes('Task Details -')) {
      return null;
    }

    const detailsStr = taskDetailsString.replace('Task Details - ', '');
    const details = {};

    // Use regex to properly parse key=value pairs, handling arrays in values
    let currentIndex = 0;

    while (currentIndex < detailsStr.length) {
      // Find the next key by looking for the pattern "key="
      let keyStart = currentIndex;
      let equalIndex = detailsStr.indexOf('=', keyStart);

      if (equalIndex === -1) break;

      let key = detailsStr.substring(keyStart, equalIndex).trim();

      // Remove leading comma if present (except for the first key)
      if (key.startsWith(',')) {
        key = key.substring(1).trim();
      }

      // Find the value start
      let valueStart = equalIndex + 1;
      let valueEnd;

      // Check if the value starts with '[' (array)
      if (detailsStr[valueStart] === '[') {
        // Find the matching closing bracket
        let bracketCount = 0;
        let i = valueStart;
        while (i < detailsStr.length) {
          if (detailsStr[i] === '[') bracketCount++;
          if (detailsStr[i] === ']') bracketCount--;
          if (bracketCount === 0) {
            valueEnd = i + 1;
            break;
          }
          i++;
        }
      } else {
        // Find the next comma that's not inside brackets
        let bracketCount = 0;
        let i = valueStart;
        while (i < detailsStr.length) {
          if (detailsStr[i] === '[') bracketCount++;
          if (detailsStr[i] === ']') bracketCount--;
          if (detailsStr[i] === ',' && bracketCount === 0) {
            valueEnd = i;
            break;
          }
          i++;
        }
        if (valueEnd === undefined) valueEnd = detailsStr.length;
      }

      let value = detailsStr.substring(valueStart, valueEnd).trim();
      details[key] = value;

      currentIndex = valueEnd + 1;

      // Skip the comma if we're not at the end
      if (currentIndex < detailsStr.length && detailsStr[currentIndex] === ',') {
        currentIndex++;
      }
    }

    return details;
  };

  const formatTaskDetails = (details) => {
    if (!details) return '';

    const formatSendbackAllowed = (value) => {
      // Handle array format for SendbackAllowed
      if (value && value.startsWith('[') && value.endsWith(']')) {
        // Parse the array and format it nicely
        try {
          const array = JSON.parse(value);
          return array.map(level => `${WORKFLOW_DATA_CONSTANTS.LEVEL} - ${level}`).join(', ');
        } catch (e) {
          // If parsing fails, return as-is
          return value;
        }
      }
      // Handle comma-separated values (fallback for old format)
      if (value && value.includes(',') && !value.startsWith('[')) {
        return value.split(',').map(level => `${WORKFLOW_DATA_CONSTANTS.LEVEL} - ${level.trim()}`).join(', ');
      }
      return value;
    };

    return Object.entries(details).map(([key, value]) => {
      const formattedValue = key === 'WORKFLOW_DATA_CONSTANTS.SENDBACKALLOWED' ? formatSendbackAllowed(value) : value;
      return `${key}: ${formattedValue}`;
    }).join('\n');
  };

  const showTaskDetails = (taskDetailsString, title) => {
    const details = parseTaskDetails(taskDetailsString);
    if (details) {
      const formattedDetails = formatTaskDetails(details);
      setTaskDetailsPopup({
        show: true,
        content: formattedDetails,
        title: title
      });
    }
  };

  

  const getOperationTypeColor = (operationType) => {
    switch (operationType) {
      case WORKFLOW_DATA_CONSTANTS.CHANGE_LOGS_TYPES.ADDED: return { color: 'success', bgcolor: '#f6ffed', textColor: '#52c41a' };
      case WORKFLOW_DATA_CONSTANTS.CHANGE_LOGS_TYPES.REMOVED: return { color: 'error', bgcolor: '#fff2f0', textColor: '#ff4d4f' };
      case WORKFLOW_DATA_CONSTANTS.CHANGE_LOGS_TYPES.CHANGED: return { color: 'primary', bgcolor: '#f0f5ff', textColor: '#1890ff' };
      default: return { color: 'default', bgcolor: '#fafafa', textColor: '#666666' };
    }
  };

  const renderValue = (params, isOldValue = true) => {
    const value = isOldValue ? params.row.oldValue : params.row.newValue;
    const operationType = params.row.operationType;

    if ((operationType === WORKFLOW_DATA_CONSTANTS.CHANGE_LOGS_TYPES.ADDED || operationType === WORKFLOW_DATA_CONSTANTS.CHANGE_LOGS_TYPES.REMOVED) &&
      value && value.includes('Task Details -')) {
      return (
        <Box display="flex" alignItems="center" gap={1}>
          <IconButton
            size="small"
            onClick={() => showTaskDetails(value, `${operationType} - Task Details`)}
            sx={{ p: 0.5 }}
          >
            <EyeOutlined style={{ color: '#1890ff', fontSize: '16px' }} />
          </IconButton>
          <Typography variant="caption" color="textSecondary">
            View Details
          </Typography>
        </Box>
      );
    }

    if (operationType === WORKFLOW_DATA_CONSTANTS.CHANGE_LOGS_TYPES.ADDED && isOldValue) {
      return <Typography variant="body2" color="textSecondary" fontStyle="italic">-</Typography>;
    }

    if (operationType === WORKFLOW_DATA_CONSTANTS.CHANGE_LOGS_TYPES.REMOVED && !isOldValue) {
      return <Typography variant="body2" color="textSecondary" fontStyle="italic">-</Typography>;
    }

    return (
      <Typography variant="body2">
        {value || <span style={{ color: '#999', fontStyle: 'italic' }}>-</span>}
      </Typography>
    );
  };

  const columns = [
    {
      field: 'operationType',
      headerName: 'Operation',
      width: 120,
      renderCell: (params) => {
        const colorConfig = getOperationTypeColor(params.value);
        return (
          <Tooltip title={params.value} arrow>
            <Chip
              label={params.value}
              size="small"
              sx={{
                backgroundColor: colorConfig.bgcolor,
                color: colorConfig.textColor,
                fontWeight: 500,
                fontSize: '0.75rem'
              }}
            />
          </Tooltip>
        );
      }
    },
    {
      field: 'taskName',
      headerName: 'Task Name',
      width: 150,
      renderCell: (params) => (
        <Tooltip title={params.value || "No task name"} arrow>
          <Typography variant="body2">{params.value || "-"}</Typography>
        </Tooltip>
      )
    },
    {
      field: 'fieldName',
      headerName: 'Field Name',
      width: 200,
      renderCell: (params) => (
        <Tooltip title={params.value} arrow>
          <Typography variant="body2">{params.value}</Typography>
        </Tooltip>
      )
    },
    {
      field: 'oldValue',
      headerName: 'Old Value',
      width: 150,
      renderCell: (params) => (
        <Tooltip title={params.value || "No previous value"} arrow>
          {renderValue(params, true)}
        </Tooltip>
      )
    },
    {
      field: 'newValue',
      headerName: 'New Value',
      width: 150,
      renderCell: (params) => (
        <Tooltip title={params.value || "No new value"} arrow>
          {renderValue(params, false)}
        </Tooltip>
      )
    },
    {
      field: 'changedBy',
      headerName: 'Changed By',
      width: 250,
      renderCell: (params) => (
        <Tooltip title={params.value} arrow>
          <Typography variant="body2">{params.value}</Typography>
        </Tooltip>
      )
    },
    {
      field: 'changedOn',
      headerName: 'Changed On',
      width: 180,
      renderCell: (params) => (
        <Tooltip title={formatDateTime(params.value,appSettings?.dateFormat)} arrow>
          <Typography variant="body2">{formatDateTime(params.value,appSettings?.dateFormat)}</Typography>
        </Tooltip>
      )
    },
  ];

  return (
    <>
      <Dialog
        open={open}
        onClose={onClose}
        maxWidth="lg"
        fullWidth
        PaperProps={{
          sx: { height: '90vh', maxHeight: '90vh' }
        }}
      >
        <DialogTitle sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Box sx={{ display: "flex", alignItems: "center", gap: "8px" }}>
            <ChangeCircleOutlinedIcon
              sx={{
                color: "black",
                fontSize: "20px",
                "&:hover": {
                  transform: "rotate(360deg)",
                  transition: "0.9s",
                },
                textAlign: "center",
                marginTop: "4px",
              }}
            />
            <Typography
              id="modal-modal-title"
              variant="subtitle1"
              fontSize={"16px"}
              fontWeight={"bold"}
              sx={{ color: "black", paddingTop: "3px" }}
            >
              Change Log
            </Typography>
          </Box>
          <IconButton onClick={onClose} size="small">
            <CloseOutlined />
          </IconButton>
        </DialogTitle>

        <DialogContent sx={{ p: 0, height: 'calc(100% - 64px)' }}>
          <Box sx={{ height: '100%', width: '100%', padding: "10px" }}>
            <ReusableDataTable
              title="Change Log History"
              rows={data || []}
              columns={columns}
              getRowIdValue="id" // Adjust based on your data structure
              pageSize={10}
              rowsPerPageOptions={[5, 10, 20]}
              disableSelectionOnClick={true}
              hideFooter={false}
              tempheight="calc(100vh - 160px)"
              width="100%"
              module="changelog"
              showRefresh={false}
              showSearch={false}
              showExport={data && data.length > 0} // Only show export when data exists
              showFilter={true}
              showColumns={true}
              checkboxSelection={false}
              isLoading={false}
              rowCount={data?.length || 0}
            />
          </Box>
        </DialogContent>
      </Dialog>

      {/* Task Details Dialog - Keep this unchanged */}
      <Dialog
        open={taskDetailsPopup.show}
        onClose={() => setTaskDetailsPopup({ show: false, content: '', title: '' })}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle sx={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          bgcolor: '#fafafa'
        }}>
          <Typography variant="h6" component="div">
            {taskDetailsPopup.title}
          </Typography>
          <IconButton
            onClick={() => setTaskDetailsPopup({ show: false, content: '', title: '' })}
            size="small"
          >
            <CloseOutlined />
          </IconButton>
        </DialogTitle>

        <DialogContent>
          <Box sx={{ mt: 2 }}>
            <Typography variant="subtitle2" sx={{ mb: 2, fontWeight: 600 }}>
              Task Details:
            </Typography>
            <Paper
              variant="outlined"
              sx={{
                p: 2,
                bgcolor: '#fafafa',
                fontFamily: 'monospace',
                fontSize: '0.875rem',
                whiteSpace: 'pre-wrap'
              }}
            >
              {taskDetailsPopup.content}
            </Paper>
          </Box>
        </DialogContent>

        <DialogActions>
          <Button
            onClick={() => setTaskDetailsPopup({ show: false, content: '', title: '' })}
            variant="outlined"
          >
            Close
          </Button>
        </DialogActions>
      </Dialog>
    </>
  );
};

export default ChangeLogWF;