import React from 'react';
import {
  Dialog,
  DialogContent,
  IconButton,
  FormControl,
  InputLabel,
  Select,
  MenuItem
} from '@mui/material';
import CloseIcon from '@mui/icons-material/Close';

import ExcelOperationsCard from "@components/Common/ExcelOperationsCard"
import { ArrowLeftOutlined } from "@ant-design/icons";

const ExcelOperationsDialogPopup = ({
  open,
  handleClose,
  setDialogState,
  selectedOption,
  handleChanged,
  MODULE_MAP,
  handleDownload,
  setEnableDocumentUpload,
  enableDocumentUpload,
  handleUpload
}) => {
  return (
    <Dialog
      open={open}
      maxWidth="md"
      fullWidth
      PaperProps={{
        sx: {
          borderRadius: '12px',
          padding: '4px',
          position: 'relative',
        },
      }}
    >
      {/* Left Arrow Button */}
      <IconButton
        onClick={() =>
          setDialogState({ createWorkflow: true, excelOperations: false })
        }
        sx={{
          position: 'absolute',
          left: 8,
          top: 8,
          color: (theme) => theme.palette.grey[500],
          zIndex: 1,
          '&:hover': {
            color: (theme) => theme.palette.primary.main,
            backgroundColor: (theme) => theme.palette.action.hover,
          },
        }}
      >
        <ArrowLeftOutlined />
      </IconButton>

      <DialogContent sx={{ pt: 6 }}>
        {/* Right Close Button */}
        <IconButton
          aria-label="close"
          onClick={handleClose}
          sx={{
            position: 'absolute',
            right: 8,
            top: 8,
            color: (theme) => theme.palette.grey[500],
            '&:hover': {
            color: (theme) => theme.palette.error.main,
            backgroundColor: (theme) => theme.palette.action.hover,
          },
          }}
        >
          <CloseIcon />
        </IconButton>

        <FormControl sx={{ mb: 2, mt: 2, minWidth: 200, maxWidth: 300 }}>
          <InputLabel id="dropdown-label">Module Type</InputLabel>
          <Select
            labelId="dropdown-label"
            value={selectedOption}
            label="Module Type"
            onChange={handleChanged}
            MenuProps={{ PaperProps: { sx: { maxHeight: 200 } } }}
          >
            
            <MenuItem value={MODULE_MAP.MAT}>MATERIAL</MenuItem>
            <MenuItem value={MODULE_MAP.ART}>ARTICLE</MenuItem>
            <MenuItem value={MODULE_MAP.PC}>PROFIT CENTER</MenuItem>
            <MenuItem value={MODULE_MAP.CC}>COST CENTER</MenuItem>
            <MenuItem value={MODULE_MAP.BK}>BANK KEY</MenuItem>
            <MenuItem value={MODULE_MAP.GL}>GENERAL LEDGER</MenuItem>
            <MenuItem value={MODULE_MAP.CCG}>COST CENTER GROUP</MenuItem>
            <MenuItem value={MODULE_MAP.PCG}>PROFIT CENTER GROUP</MenuItem>
            <MenuItem value={MODULE_MAP.CEG}>COST ELEMENT GROUP</MenuItem>
            <MenuItem value={MODULE_MAP.BOM}>BILL OF MATERIAL</MenuItem> 
            <MenuItem value={MODULE_MAP.IO}>INTERNAL ORDER</MenuItem>
            <MenuItem value={MODULE_MAP.SOURCE_LIST}>SOURCE LIST</MenuItem>

          </Select>
        </FormControl>

        <ExcelOperationsCard
          handleDownload={handleDownload}
          setEnableDocumentUpload={setEnableDocumentUpload}
          enableDocumentUpload={enableDocumentUpload}
          handleUploadMaterial={handleUpload}
        />
      </DialogContent>
    </Dialog>
  );
};

export default ExcelOperationsDialogPopup;

