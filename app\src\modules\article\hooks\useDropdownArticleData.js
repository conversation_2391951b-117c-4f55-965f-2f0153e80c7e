import useFetchDropdownAndDispatch from "./useMaterialFetchDropdownAndDispatch";
import { destination_ArticleMgmt } from "../../../destinationVariables";

const useDropdownArticleData = () => {
  const { fetchDataAndDispatch } = useFetchDropdownAndDispatch();
  const fetchAllDropdownMasterData = () => {
    const apiCalls = [
      // NOTE Not being used now but can be used later.
      // { url: `/${destination_ArticleMgmt}/data/getMatlGrp1`, keyName: "MatlGrp1" },
      // { url: `/${destination_ArticleMgmt}/data/getMatlGrp2`, keyName: "MatlGrp2" },
      // { url: `/${destination_ArticleMgmt}/data/getMatlGrp3`, keyName: "MatlGrp3" },
      // { url: `/${destination_ArticleMgmt}/data/getTaxInd`, keyName: "TaxInd" },
      // { url: `/${destination_ArticleMgmt}/data/getDsnOffice`, keyName: "DsnOffice" },
      // { url: `/${destination_ArticleMgmt}/data/getProdAllocation`, keyName: "ProductAllocation" }, //
      // { url: `/${destination_ArticleMgmt}/data/getMedium`, keyName: "Medium" },
      { url: `/${destination_ArticleMgmt}/data/getEanCat`, keyName: "CategoryOfInternationalArticleNumberEAN" },
      // { url: `/${destination_ArticleMgmt}/data/getSegStructure`, keyName: "SegmentationStructure" }, //
      // { url: `/${destination_ArticleMgmt}/data/getSegStrategy`, keyName: "SegmentationStrategy" }, //
      // { url: `/${destination_ArticleMgmt}/data/getANPCode`, keyName: "ANPCode" },
      // { url: `/${destination_ArticleMgmt}/data/getMRPGroup`, keyName: "MRPGroup" }, //
      // { url: `/${destination_ArticleMgmt}/data/getUnitGroup`, keyName: "UnitGroup" },
      // { url: `/${destination_ArticleMgmt}/data/getTaxCategory`, keyName: "TaxCategory" }, //
      // { url: `/${destination_ArticleMgmt}/data/getAbcId`, keyName: "AbcId" },
      // { url: `/${destination_ArticleMgmt}/data/getStandHUType`, keyName: "StandardHandlingUnitType" }, //
      // { url: `/${destination_ArticleMgmt}/data/getQualInspGrp`, keyName: "QualityInspectionGroup" }, //
      // { url: `/${destination_ArticleMgmt}/data/getBaseUom`, keyName: "QuarantinePeriod" }, //
      // { url: `/${destination_ArticleMgmt}/data/getDelyUnit`, keyName: "DelyUnit" },
      { url: `/${destination_ArticleMgmt}/data/getGenItemCatGroup`, keyName: "ItemCat" },
      // { url: `/${destination_ArticleMgmt}/data/getProductShape`, keyName: "ProductShape" }, //
      // { url: `/${destination_ArticleMgmt}/data/getLogHandlingInd`, keyName: "HandlingIndicator" }, //
      // { url: `/${destination_ArticleMgmt}/data/getWHMaterialGroup`, keyName: "WarehouseProductGroup" }, //
      // { url: `/${destination_ArticleMgmt}/data/getWhseStorCondition`, keyName: "WarehouseStorageCondition" },
      // { url: `/${destination_ArticleMgmt}/data/getCWProfile`, keyName: "CWProfileForCWQty" }, //
      // { url: `/${destination_ArticleMgmt}/data/getCatchWtTolGrp`, keyName: "CatchWTToleranceGroup" }, //
      // { url: `/${destination_ArticleMgmt}/data/getReferenceProduct`, keyName: "RefProductForPackagingBuilding" }, //
      // { url: `/${destination_ArticleMgmt}/data/getMatGrpPack`, keyName: "MaterialGroupPackagingMaterials" }, //
      // { url: `/${destination_ArticleMgmt}/data/getProductOrientationProfile`, keyName: "ProductOrientationProfile" }, //
      // { url: `/${destination_ArticleMgmt}/data/getClassBasedOnClassType`, keyName: "ClassType" }, //
      // { url: `/${destination_ArticleMgmt}/data/getSerializationLevel`, keyName: "SerializationLevel" },
      // { url: `/${destination_ArticleMgmt}/data/getPlRefMat`, keyName: "PlRefMat" },
      // { url: `/${destination_ArticleMgmt}/data/getCommGroup`, keyName: "CommGroup" },
      // { url: `/${destination_ArticleMgmt}/data/getPrRefMat`, keyName: "PrRefMat" },
      // { url: `/${destination_ArticleMgmt}/data/getRoundProf`, keyName: "RoundProf" },
      // { url: `/${destination_ArticleMgmt}/data/getTaxType1`, keyName: "TaxType1" },
      // { url: `/${destination_ArticleMgmt}/data/getTaxType2`, keyName: "TaxType2" },
      // { url: `/${destination_ArticleMgmt}/data/getTaxType3`, keyName: "TaxType3" },
      // { url: `/${destination_ArticleMgmt}/data/getTaxType4`, keyName: "TaxType4" },
      // { url: `/${destination_ArticleMgmt}/data/getTaxType5`, keyName: "TaxType5" },
      // { url: `/${destination_ArticleMgmt}/data/getTaxType6`, keyName: "TaxType6" },
      // { url: `/${destination_ArticleMgmt}/data/getTaxType7`, keyName: "TaxType7" },
      // { url: `/${destination_ArticleMgmt}/data/getTaxType8`, keyName: "TaxType8" },
      // { url: `/${destination_ArticleMgmt}/data/getTaxType9`, keyName: "TaxType9" },
      // { url: `/${destination_ArticleMgmt}/data/getTaxclass3`, keyName: "Taxclass3" },
      // { url: `/${destination_ArticleMgmt}/data/getTaxclass4`, keyName: "Taxclass4" },
      // { url: `/${destination_ArticleMgmt}/data/getTaxclass5`, keyName: "Taxclass5" },
      // { url: `/${destination_ArticleMgmt}/data/getTaxclass6`, keyName: "Taxclass6" },
      // { url: `/${destination_ArticleMgmt}/data/getTaxclass7`, keyName: "Taxclass7" },
      // { url: `/${destination_ArticleMgmt}/data/getTaxclass8`, keyName: "Taxclass8" },
      // { url: `/${destination_ArticleMgmt}/data/getTaxclass9`, keyName: "Taxclass9" },
      { url: `/${destination_ArticleMgmt}/data/getTaxInd`, keyName: "Json121" },
      { url: `/${destination_ArticleMgmt}/data/getMatlGrp4`, keyName: "MatlGrp4" },
      { url: `/${destination_ArticleMgmt}/data/getMatlGrp5`, keyName: "MatlGrp5" },
      { url: `/${destination_ArticleMgmt}/data/getMatfrgtgrp`, keyName: "Matfrgtgrp" },
      { url: `/${destination_ArticleMgmt}/data/getBaseUom`, keyName: "CompUom" },
      { url: `/${destination_ArticleMgmt}/data/getBaseUom`, keyName: "CompUom" },
      { url: `/${destination_ArticleMgmt}/data/getBaseUom`, keyName: "DelyUom" },
      { url: `/${destination_ArticleMgmt}/data/getBaseUom`, keyName: "AltUnit" },
      { url: `/${destination_ArticleMgmt}/data/getBaseUom`, keyName: "UnitDim" },
      { url: `/${destination_ArticleMgmt}/data/getBaseUom`, keyName: "CommCoUn" },
      { url: `/${destination_ArticleMgmt}/data/getBaseUom`, keyName: "LeqUnit1" },
      { url: `/${destination_ArticleMgmt}/data/getBaseUom`, keyName: "Json103" },
      { url: `/${destination_ArticleMgmt}/data/getBaseUom`, keyName: "Json75" },
      { url: `/${destination_ArticleMgmt}/data/getBaseUom`, keyName: "Json156" },
      { url: `/${destination_ArticleMgmt}/data/getUnitType`, keyName: "Unittype1" },
      { url: `/${destination_ArticleMgmt}/data/getSproctype`, keyName: "Sproctype" },
      { url: `/${destination_ArticleMgmt}/data/getVarOrdUn`, keyName: "VarOrdUn" },
      { url: `/${destination_ArticleMgmt}/data/getMatlGroup`, keyName: "MatlGroup" },
      { url: `/${destination_ArticleMgmt}/data/getMatlGroup`, keyName: "MatlGrp" },

      { url: `/${destination_ArticleMgmt}/data/getUnitOfWt`, keyName: "UnitOfWt" },
      { url: `/${destination_ArticleMgmt}/data/getVolumeUnit`, keyName: "Volumeunit" },
      { url: `/${destination_ArticleMgmt}/data/getHazMatProf`, keyName: "Hazmatprof" },

      { url: `/${destination_ArticleMgmt}/data/getItemCat`, keyName: "Json213" },
      { url: `/${destination_ArticleMgmt}/data/getSalesUnit`, keyName: "Json201" },
      { url: `/${destination_ArticleMgmt}/data/getPurValkey`, keyName: "PurValkey" },
      { url: `/${destination_ArticleMgmt}/data/getBasicMatl`, keyName: "BasicMatl" },

      { url: `/${destination_ArticleMgmt}/data/getXDistChainStatus`, keyName: "SalStatus" },

      { url: `/${destination_ArticleMgmt}/data/getEanCat`, keyName: "EanCat" },
      { url: `/${destination_ArticleMgmt}/data/getLangu`, keyName: "Langu" },
      { url: `/${destination_ArticleMgmt}/data/getSalesOrg`, keyName: "SalesOrg" },
      { url: `/${destination_ArticleMgmt}/data/getMatlStats`, keyName: "MatlStats" },
      { url: `/${destination_ArticleMgmt}/data/getMatPrGrp`, keyName: "MatPrGrp" },
      { url: `/${destination_ArticleMgmt}/data/getAcctAssgt`, keyName: "AcctAssgt" },
      { url: `/${destination_ArticleMgmt}/data/getDepcountry`, keyName: "Depcountry" },
      { url: `/${destination_ArticleMgmt}/data/getXPlant`, keyName: "PurStatus" },
      { url: `/${destination_ArticleMgmt}/data/getPlantSpMatlStatus`, keyName: "PurStstus" },
      { url: `/${destination_ArticleMgmt}/data/getPlantSpMatlStatus`, keyName: "PlantSpMatlStatus" },
      { url: `/${destination_ArticleMgmt}/data/getXPlant`, keyName: "CrossPlantMaterialStatus" }, //
      { url: `/${destination_ArticleMgmt}/data/getExtMatlGrp`, keyName: "Extmatgrp" },

      { url: `/${destination_ArticleMgmt}/data/getLoadingGroup`, keyName: "Loadinggrp" },
      { url: `/${destination_ArticleMgmt}/data/getAvailCheck`, keyName: "Availcheck" },
      { url: `/${destination_ArticleMgmt}/data/getCountryOfOrigin`, keyName: "Countryori" },
      { url: `/${destination_ArticleMgmt}/data/getRebateGrp`, keyName: "RebateGrp" },
      { url: `/${destination_ArticleMgmt}/data/getMRPType`, keyName: "MrpType" },
      { url: `/${destination_ArticleMgmt}/data/getMRPType`, keyName: "Json151" },
      { url: `/${destination_ArticleMgmt}/data/getLotSizingProcedure`, keyName: "Lotsizekey" },
      { url: `/${destination_ArticleMgmt}/data/getProcurementType`, keyName: "ProcType" },
      { url: `/${destination_ArticleMgmt}/data/getBackflush`, keyName: "Backflush" },
      { url: `/${destination_ArticleMgmt}/data/getPeriodInd`, keyName: "PeriodInd" },
      { url: `/${destination_ArticleMgmt}/data/getPlanningStrategyGroup`, keyName: "PlanStrgp" },
      { url: `/${destination_ArticleMgmt}/data/getConsumptionMode`, keyName: "Consummode" },
      { url: `/${destination_ArticleMgmt}/data/getConsumptionPeriodBkwd`, keyName: "BwdCons" },
      { url: `/${destination_ArticleMgmt}/data/getConsumptionPeriodFwd`, keyName: "FwdCons" },
      { url: `/${destination_ArticleMgmt}/data/getIndividualColl`, keyName: "DepReqId" },
      { url: `/${destination_ArticleMgmt}/data/getSaftyTimeIndicator`, keyName: "SaftyTId" },
      { url: `/${destination_ArticleMgmt}/data/getMixedMRP`, keyName: "MixedMrp" },
      { url: `/${destination_ArticleMgmt}/data/getRequirementGroup`, keyName: "GrpReqmts" },
      { url: `/${destination_ArticleMgmt}/data/getPriceUnit`, keyName: "PriceUnit" },
      { url: `/${destination_ArticleMgmt}/data/getMatlType`, keyName: "MatlType" },
      { url: `/${destination_ArticleMgmt}/data/getIndSector`, keyName: "IndSector" },
      { url: `/${destination_ArticleMgmt}/data/getTransGrp`, keyName: "TransGrp" },
      { url: `/${destination_ArticleMgmt}/data/getProfitCenter`, keyName: "ProfitCtr" },
      { url: `/${destination_ArticleMgmt}/data/getMatlGrp2`, keyName: "MatlGrp2" },
      { url: `/${destination_ArticleMgmt}/data/getProdAllocation`, keyName: "ProdAlloc" },
      { url: `/${destination_ArticleMgmt}/data/getVarianceKey`, keyName: "VarianceKey" },
      { url: `/${destination_ArticleMgmt}/data/getConsumptionPeriodFwd`, keyName: "ConsumptionPeriodFwd" },
      { url: `/${destination_ArticleMgmt}/data/getVendorDetails`, keyName: "Supplier" },
      { url: `/${destination_ArticleMgmt}/data/getVendorDetails`, keyName: "Json215" },
      { url: `/${destination_ArticleMgmt}/data/getBomUsage`, keyName: "BomUsage" },
      { url: `/${destination_ArticleMgmt}/data/getBomItemCategory`, keyName: "Category" },
      { url: `/${destination_ArticleMgmt}/data/getPlant`, keyName: "ProcurementPlant" },
      { url: `/${destination_ArticleMgmt}/data/getPurchaseOrg`, keyName: "PurchaseOrg" },
      { url: `/${destination_ArticleMgmt}/data/getTemperatureCondition`, keyName: "TempConds" },
      { url: `/${destination_ArticleMgmt}/data/getLabelType`, keyName: "LabelType" },
      { url: `/${destination_ArticleMgmt}/data/getLabelForm`, keyName: "LabelForm" },
      { url: `/${destination_ArticleMgmt}/data/getRoundingRuleSLED`, keyName: "RoundUpRuleExpirationDate" },
      { url: `/${destination_ArticleMgmt}/data/getExpirationDate`, keyName: "SledBbd" },
      { url: `/${destination_ArticleMgmt}/data/getSerialNumberLevel`, keyName: "SerializationLevel" },
      { url: `/${destination_ArticleMgmt}/data/getUnitOfIssue`, keyName: "IssueUnit" },
      { url: `/${destination_ArticleMgmt}/data/getTimeUnit`, keyName: "StgePdUn" },
      { url: `/${destination_ArticleMgmt}/data/getSerialNumberProfile`, keyName: "SernoProf" },
      { url: `/${destination_ArticleMgmt}/data/getDistributionProfile`, keyName: "Json125" },
      { url: `/${destination_ArticleMgmt}/data/getRndingProfile`, keyName: "Json209" },
      { url: `/${destination_ArticleMgmt}/data/getStockDeterminationGroup`, keyName: "DetermGrp" },
      { url: `/${destination_ArticleMgmt}/data/getIUIDType`, keyName: "IuidType" },
      { url: `/${destination_ArticleMgmt}/data/getClassType`, keyName: "Classtype" },

      //Request Header
      { url: `/${destination_ArticleMgmt}/data/getVendorDetails`, keyName: "Json301" },

      //Supplier Form
      { url: `/${destination_ArticleMgmt}/data/getPurGroup`, keyName: "Json13" },
      { url: `/${destination_ArticleMgmt}/data/getOrderUnit`, keyName: "Json45" },
      { url: `/${destination_ArticleMgmt}/data/getOrderUnit`, keyName: "Json252" },
      { url: `/${destination_ArticleMgmt}/data/getGeneric2`, keyName: "Json16" }, //Tax code
      { url: `/${destination_ArticleMgmt}/data/getGeneric3`, keyName: "Json26" }, //Confirmation Control
      { url: `/${destination_ArticleMgmt}/data/getGeneric4`, keyName: "Json2" }, //Brand
      { url: `/${destination_ArticleMgmt}/data/getCountryOfOrigin`, keyName: "Json4" },

      //Basic Data
      { url: `/${destination_ArticleMgmt}/data/getBaseUom`, keyName: "BaseUom" },
      { url: `/${destination_ArticleMgmt}/data/getProdHier`, keyName: "ProdHier" },
      { url: `/${destination_ArticleMgmt}/data/getExtMatlGrp`, keyName: "Extmatlgrp" },
      { url: `/${destination_ArticleMgmt}/data/getDivision`, keyName: "Division" },
      { url: `/${destination_ArticleMgmt}/data/getStorageCondition`, keyName: "StorConds" },
      { url: `/${destination_ArticleMgmt}/data/getContainerRequirements`, keyName: "Container" },
      { url: `/${destination_ArticleMgmt}/data/getHazMatNo`, keyName: "HazMatNo" },
      { url: `/${destination_ArticleMgmt}/data/getTransGrp`, keyName: "TransGrp" },
      { url: `/${destination_ArticleMgmt}/data/getMaterialGroupPack`, keyName: "MatGroupPackagingMat" },
      { url: `/${destination_ArticleMgmt}/data/getGenItemCatGroup`, keyName: "GItemCat" },
      { url: `/${destination_ArticleMgmt}/data/getXDistChainStatus`, keyName: "XSalStatus" },
      { url: `/${destination_ArticleMgmt}/data/getCSalStatus`, keyName: "CSalStatus" },
      { url: `/${destination_ArticleMgmt}/data/getPeriodIndicatorSLED`, keyName: "PeriodIndExpirationDate" },
      { url: `/${destination_ArticleMgmt}/data/getMatlGroup`, keyName: "MatlGroup" },
      { url: `/${destination_ArticleMgmt}/data/getLoadingGroup`, keyName: "Json302" },
      { url: `/${destination_ArticleMgmt}/data/getPurGroup`, keyName: "Json303" },
      { url: `/${destination_ArticleMgmt}/data/getCountryOfOrigin`, keyName: "Json304" },
      { url: `/${destination_ArticleMgmt}/data/getCountryOfOrigin`, keyName: "Json305" },
      { url: `/${destination_ArticleMgmt}/data/getGeneric1`, keyName: "Json306" }, //Temperature
      { url: `/${destination_ArticleMgmt}/data/getIndSector`, keyName: "Json307" },


      //Listing
      { url: `/${destination_ArticleMgmt}/data/getGeneric5`, keyName: "Json51" }, //Assortment Grade
      { url: `/${destination_ArticleMgmt}/data/getGeneric6`, keyName: "Json60" }, //Listing Procedure
      { url: `/${destination_ArticleMgmt}/data/getGeneric6`, keyName: "Json61" }, //Listing Procedure

      //POS
      { url: `/${destination_ArticleMgmt}/data/getBaseUom`, keyName: "Json75" },
      { url: `/${destination_ArticleMgmt}/data/getLangu`, keyName: "Json74" },
      { url: `/${destination_ArticleMgmt}/data/getDChainSpecStatus`, keyName: "Json71" },
      { url: `/${destination_ArticleMgmt}/data/getVendorDetails`, keyName: "Json301" },
      { url: `/${destination_ArticleMgmt}/data/getGeneric7`, keyName: "Json48" },
      // { url: `/${destination_ArticleMgmt}/data/getCharacteristicsByClass?className=TSHIRT`, keyName: "Json451" },
      { url: `/${destination_ArticleMgmt}/data/getBaseUom`, keyName: "Json45" },
      { url: `/${destination_ArticleMgmt}/data/getLoadingGroup`, keyName: "Json127" },
    ];
    apiCalls.forEach(({ url, keyName }) => {
      fetchDataAndDispatch(url, keyName);
    });
  }
  return { fetchAllDropdownMasterData };
};

export default useDropdownArticleData;
