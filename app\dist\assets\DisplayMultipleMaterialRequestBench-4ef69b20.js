import{r as c,n as y,s as H,b7 as z,bu as Q,j as e,aZ as S,c as o,d as r,b3 as Z,aa as P,a1 as J,a2 as U,a9 as X,F as O,O as i,g as Y,u as G,bv as q,b1 as ee,B,bs as te,Q as ae,a6 as le,a_ as oe,a$ as ne,b5 as re,b6 as ie,C as se,au as de,ah as ce,an as he,ag as pe}from"./index-f7d9b065.js";import{D as ue}from"./DatePicker-a8e9bd4a.js";import"./useMobilePicker-9b56b5b6.js";import"./CSSTransition-cd337b47.js";function ge(g,h){return Array.isArray(h)&&h.find(T=>T.code===g)||""}const me=({label:g,value:h,units:A,onSave:T,isEditMode:F,isExtendMode:j,options:R=[],type:w})=>{var m;const[p,M]=c.useState(h),[E,I]=c.useState(!1),x=y(a=>a.AllDropDown.dropDown),k=H(),V=ge(p,x);console.log("dropdownData",p),console.log("value e",h),console.log("label",g),console.log("units",A),console.log("transformedValue",V);const W=y(a=>a.edit.payload);let N=y(a=>a.payload.errorFields);console.log("editField",W),console.log("fieldData",{label:g,value:p,units:A,type:w});let d=g.replaceAll("(","").replaceAll(")","").replaceAll("/","").replaceAll("-","").replaceAll(".","").split(" ").join("");c.useEffect(()=>{M(h)},[h]);const D=a=>{console.log("checkonedit"),k(z({keyname:d.replaceAll("(","").replaceAll(")","").replaceAll("/","").replaceAll("-","").replaceAll(".","").split(" ").join(""),data:a}))};c.useEffect(()=>{console.log("lkey",d),console.log("data",h),k(z({keyname:d.replaceAll("(","").replaceAll(")","").replaceAll("/","").replaceAll("-","").replaceAll(".","").split(" ").join(""),data:h||""}))},[]);const s=Q();return console.log("editedValue[key] ",x[d]),console.log("editedValue[key] ",p),e(i,{item:!0,children:e(S,{children:F||j?o(O,{children:[e(r,{variant:"body2",color:"#777",children:g}),w==="Drop Down"?e(Z,{options:x[d]??[],value:p&&((m=x[d])==null?void 0:m.filter(a=>a.code===p))||"",onChange:(a,l)=>{D(l.code),console.log("newValue",l),M(l.code),I(!0),console.log("keys",d)},getOptionLabel:a=>{var l,C;return console.log("optionoptionoption",a),a===""?"":`${a&&((l=a[0])==null?void 0:l.code)} - ${a&&((C=a[0])==null?void 0:C.desc)}`},renderOption:(a,l)=>(console.log("option vakue",l),e("li",{...a,children:e(r,{style:{fontSize:12},children:`${l==null?void 0:l.code} - ${l==null?void 0:l.desc}`})})),renderInput:a=>e(P,{...a,variant:"outlined",size:"small",label:null,placeholder:`Select ${g}`})}):w==="Input"?e(P,{variant:"outlined",size:"small",value:p,onChange:a=>{const l=a.target.value;D(l),M(l)},placeholder:`Enter ${g}`}):w==="Calendar"?e(J,{dateAdapter:U,children:e(ue,{slotProps:{textField:{size:"small"}},value:x[d]?x[d]:"",defaultValue:s,placeholder:"Select Date Range",onChange:a=>k(z({keyname:d,data:"/Date("+Date.parse(a)+")/"})),onError:N.includes(d)})}):w==="Radio Button"?e(X,{sx:{padding:0},checked:p,onChange:(a,l)=>{D(l),M(l)}}):""]}):e(O,{children:o(O,{children:[e(r,{variant:"body2",color:"#777",children:g}),e(r,{variant:"body2",fontWeight:"bold",children:p})]})})})})},ve=()=>{var _;const g=Y(),h=H();c.useState({});const[A,T]=c.useState(0),[F,j]=c.useState([]),[R,w]=c.useState(!1);c.useState(!0);let[p,M]=c.useState([]);const[E,I]=c.useState([]),[x,k]=c.useState(0),[V,W]=c.useState(),N=y(t=>t.tabsData),$={basicData:"Basic Data"},d=G();y(t=>t.initialData.EditMultipleMaterial);const D=y(t=>t.initialData.MultipleMaterialRequestBench);y(t=>{var u;return(u=t==null?void 0:t.initialData)==null?void 0:u.IWMMyTask});let s=y(t=>t.userManagement.taskData);const m=d.state.rowData;console.log("rowData",m),console.log("task",s),d.state.requestNumber,y(t=>t.payload);for(let t=0;t<D.length;t++)if(D[t].Description===m.description){D[t];break}const a=(t,u)=>{k(u)},l=()=>{let t={};(s==null?void 0:s.processDesc)==="Mass Change"?t={massCreationId:"",massChangeId:s==null?void 0:s.subject,screenName:"Change"}:(s==null?void 0:s.processDesc)==="Mass Create"&&(t={massCreationId:s==null?void 0:s.subject,massChangeId:"",screenName:"Create"}),console.log(t);const u=n=>{j(n==null?void 0:n.body[0]);const v=n.body[0].viewData;W(n.body[0].IDs);const b=Object.keys(v);console.log("categorry",b),M(b);const K=b.map(L=>({category:L,data:v[L]}));I(K),console.log("materialDetails",E),h(q(n==null?void 0:n.body))},f=n=>{console.log(n)};se(`/${de}/data/displayMassMaterial`,"post",u,f,t)};console.log("factorsArray",p),c.useEffect(()=>{l(),h(q(F))},[]);const C=p.map(t=>{var n;const u=(n=Object.entries(N).filter(v=>{var b;return((b=$[v[0]])==null?void 0:b.split(" ")[0])==(t==null?void 0:t.split(" ")[0])})[0])==null?void 0:n[1],f=E.filter(v=>{var b;return((b=v.category)==null?void 0:b.split(" ")[0])==(t==null?void 0:t.split(" ")[0])});return f.length!==0?{category:t==null?void 0:t.split(" ")[0],data:f[0].data}:{category:t==null?void 0:t.split(" ")[0],data:u}}).map((t,u)=>(console.log("categorydata",t),(t==null?void 0:t.category)=="Basic"?[e(i,{container:!0,item:!0,md:12,sx:{backgroundColor:"white",maxHeight:"max-content",height:"max-content",mt:1,mb:1},children:Object.keys(t.data).map(f=>o(i,{item:!0,md:12,sx:{backgroundColor:"white",maxHeight:"max-content",height:"max-content",borderRadius:"8px",border:"1px solid #E0E0E0",mt:.25,boxShadow:"0px 2px 14px 0px rgba(48, 38, 185, 0.10)",...ee},children:[e(r,{sx:{fontSize:"12px",fontWeight:"700",margin:"0px !important"},children:f}),e(B,{sx:{width:"100%"},children:e(te,{sx:{padding:"0",paddingBottom:"0 !important",paddingTop:"10px !important"},children:e(i,{container:!0,style:{display:"grid",gridTemplateColumns:"repeat(6,1fr)",gap:"15px"},justifyContent:"space-between",alignItems:"flex-start",md:12,children:t.data[f].map(n=>e(me,{label:n.fieldName,value:n.value,onSave:v=>handleFieldSave(n.fieldName,v),type:n.fieldType,field:n}))})})})]},f))},t.category)]:null));return console.log(C,"lololol"),o("div",{children:[e(i,{container:!0,style:{...ae,backgroundColor:"#FAFCFF"},children:o(i,{sx:{width:"inherit"},children:[o(i,{item:!0,md:12,style:{padding:"16px",display:"flex"},children:[e(i,{item:!0,style:{display:"flex",justifyContent:"flex-end"},children:e(le,{color:"primary","aria-label":"upload picture",component:"label",sx:oe,children:e(ne,{style:{height:"1em",width:"1em",color:"#000000"},onClick:()=>{g("/RequestBench")}})})}),o(i,{md:8,children:[e(r,{variant:"h3",children:o("strong",{children:["Multiple Material : ",m.description," "]})}),e(r,{variant:"body2",color:"#777",children:"This view displays details of uploaded material"})]})]}),o(i,{container:!0,display:"flex",flexDirection:"row",flexWrap:"nowrap",children:[o(B,{width:"70%",sx:{marginLeft:"40px"},children:[e(i,{item:!0,sx:{paddingTop:"2px !important"},children:o(S,{flexDirection:"row",children:[e("div",{style:{width:"15%"},children:e(r,{variant:"body2",color:"#777",children:"Material"})}),o(r,{variant:"body2",fontWeight:"bold",justifyContent:"flex-start",children:[": ",m.material]})]})}),e(i,{item:!0,sx:{paddingTop:"2px !important"},children:o(S,{flexDirection:"row",children:[e("div",{style:{width:"15%"},children:e(r,{variant:"body2",color:"#777",children:"Material Type"})}),o(r,{variant:"body2",fontWeight:"bold",children:[": ",m.materialType]})]})}),e(i,{item:!0,sx:{paddingTop:"2px !important"},children:o(S,{flexDirection:"row",children:[e("div",{style:{width:"15%"},children:e(r,{variant:"body2",color:"#777",children:"Description"})}),o(r,{variant:"body2",fontWeight:"bold",children:[": ",m.description]})]})}),e(i,{item:!0,sx:{paddingTop:"2px !important"},children:o(S,{flexDirection:"row",children:[e("div",{style:{width:"15%"},children:e(r,{variant:"body2",color:"#777",children:"Industry Sector"})}),o(r,{variant:"body2",fontWeight:"bold",children:[": ",m.industrySector]})]})})]}),e(B,{width:"30%",sx:{marginLeft:"40px"},children:e(i,{item:!0,children:o(S,{flexDirection:"row",children:[e(r,{variant:"body2",color:"#777",style:{width:"30%"}}),e(r,{variant:"body2",fontWeight:"bold",sx:{width:"8%",textAlign:"center"}}),e(r,{variant:"body2",fontWeight:"bold",justifyContent:"flex-start"})]})})})]}),o(i,{container:!0,style:{padding:"16px"},children:[e(B,{sx:{borderBottom:1,borderColor:"divider"},children:e(re,{value:x,onChange:a,variant:"scrollable",sx:{background:"#FFF",borderBottom:"1px solid #BDBDBD",width:"100%"},"aria-label":"mui tabs example",children:p.map((t,u)=>e(ie,{sx:{fontSize:"12px",fontWeight:"700"},label:t},u))})}),e(i,{container:!0,item:!0,md:12,sx:{backgroundColor:"white",maxHeight:"max-content",height:"max-content",mt:1,mb:1},children:C&&((_=C[x])==null?void 0:_.map((t,u)=>e(B,{sx:{mb:2,width:"100%"},children:e(r,{variant:"body2",children:t})},u)))},C)]})]})}),R?e(pe,{sx:{position:"fixed",bottom:0,left:0,right:0},elevation:2,children:e(ce,{className:"container_BottomNav",showLabels:!0,sx:{display:"flex",justifyContent:"flex-end",gap:1},value:A,onChange:t=>{T(t)},children:e(he,{size:"small",variant:"contained",onClick:()=>{g("/masterDataCockpit/materialMaster/massMaterialTable")},children:"Save"})})}):""]})};export{ve as default};
